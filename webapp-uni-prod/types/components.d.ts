/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BaseButton: typeof import('./../src/components/BaseButton.vue')['default']
    BaseEmpty: typeof import('./../src/components/BaseEmpty.vue')['default']
    ConfirmDialog: typeof import('./../src/components/ConfirmDialog.vue')['default']
    CustomTabBar: typeof import('./../src/components/CustomTabBar.vue')['default']
    Dropdown: typeof import('./../src/components/Dropdown.vue')['default']
    Menu: typeof import('./../src/components/Menu.vue')['default']
    MusicPlayer: typeof import('./../src/components/MusicPlayer.vue')['default']
    NotFoundComponent: typeof import('./../src/components/NotFoundComponent.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Sidebar: typeof import('./../src/components/Sidebar.vue')['default']
    SlIpInput: typeof import('./../src/components/SlIpInput.vue')['default']
    SlRadio: typeof import('./../src/components/SlRadio.vue')['default']
    SlSVgIcon: typeof import('./../src/components/SlSVgIcon.vue')['default']
    UpdatePwd: typeof import('./../src/components/UpdatePwd.vue')['default']
  }
}
