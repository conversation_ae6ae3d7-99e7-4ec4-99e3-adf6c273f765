// src/types/amap-wx.d.ts
// 实际存在路径待定
declare module '@/hooks/use-wmap/amap-wx.130' {
  interface AMapWXOptions {
    key: string; // 高德地图API密钥
  }

  interface AMapWXCallbackResult<T> {
    errMsg: string;
    data: T;
  }

  interface LocationPoint {
    longitude: number;
    latitude: number;
  }

  interface PoiItem {
    id: string;
    name: string;
    type: string;
    address: string;
    location: LocationPoint;
    tel: string;
    distance: number;
    business_area: string;
  }

  interface RegeoResult {
    formatted_address: string;
    addressComponent: {
      city: string;
      district: string;
      province: string;
      street: string;
      streetNumber: string;
    };
    pois: PoiItem[];
  }

  interface RouteStep {
    instruction: string;
    distance: number;
    duration: number;
    polyline: string; // 格式: "lng1,lat1;lng2,lat2;..."
    action: string;
    assistant_action: string;
  }

  export interface MapRoutePath {
    /** 起点和终点的距离 单位米 */
    distance: number;
    /** 时间预计 单位秒 */
    duration: number;
    steps: RouteStep[];
    transit_count: number;
  }

  export interface TransitRouteResult {
    origin: string;
    destination: string;
    paths: MapRoutePath[];
    taxi_cost: number;
  }

  interface WeatherResult {
    province: string;
    city: string;
    weather: string;
    temperature: string;
    winddirection: string;
    windpower: string;
    humidity: string;
    reporttime: string;
  }
  /**
   * 输入提示查询参数
   */
  interface InputtipsOptions {
    keywords: string; // 搜索关键词
    location?: string; // 当前位置坐标，格式："longitude,latitude"
    city?: string; // 城市代码或名称
    citylimit?: boolean; // 是否限制在当前城市
    /** all：返回所有数据类型；poi：返回POI数据类型；bus：返回公交站点数据类型；busline：返回公交线路数据类型 */
    datatype?: string;
    sig?: string; // 签名
    success?: (res: AMapWXCallbackResult<InputtipsResult>) => void;
    fail?: (err: any) => void;
  }

  /**
   * 输入提示查询结果
   */
  interface InputtipsResult {
    tips: Array<{
      name: string; // 名称
      location: string; // 坐标，格式："longitude,latitude"
      address: string; // 地址
      adcode: string; // 行政区划代码
      district: string; // 行政区
      city: string; // 城市
      province: string; // 省份
      type: string; // 类型
      typecode: string; // 类型代码
    }>;
  }
  namespace AMapWXNamespace {
    class AMapWX {
      constructor(options: AMapWXOptions);

      /**
       * 获取当前位置
       */
      getLocation(options: { success?: (res: AMapWXCallbackResult<LocationPoint>) => void; fail?: (err: any) => void; type?: 'wgs84' | 'gcj02' }): void;

      /**
       * 获取逆地理编码（坐标转地址）
       */
      getRegeo(options: {
        success?: (res: AMapWXCallbackResult<RegeoResult>) => void;
        fail?: (err: any) => void;
        location?: string; // "longitude,latitude"
        poi_types?: string;
        sig?: string;
      }): void;

      /**
       * 获取POI周边搜索
       */
      getPoiAround(options: {
        success?: (res: AMapWXCallbackResult<PoiItem[]>) => void;
        fail?: (err: any) => void;
        keywords?: string;
        types?: string;
        location?: string; // "longitude,latitude"
        radius?: number;
        offset?: number;
        page?: number;
        sig?: string;
      }): void;

      /**
       * 获取公交路线规划
       */
      getTransitRoute(options: {
        success?: (res: AMapWXCallbackResult<TransitRouteResult>) => void;
        fail?: (err: any) => void;
        origin: string; // "longitude,latitude"
        destination: string; // "longitude,latitude"
        city?: string;
        cityd?: string;
        strategy?: number; // 0-速度优先；1-费用优先；2-距离优先；3-不走高速；4-高速优先；5-公交优先
        sig?: string;
      }): void;

      /**
       * 获取步行路线规划
       */
      getWalkingRoute(options: {
        success?: (res: TransitRouteResult) => void;
        fail?: (err: any) => void;
        origin: string; // "longitude,latitude"
        destination: string; // "longitude,latitude"
        sig?: string;
      }): void;

      /**
       * 获取骑行路线规划
       */
      getRidingRoute(options: {
        success?: (res: AMapWXCallbackResult<TransitRouteResult>) => void;
        fail?: (err: any) => void;
        origin: string; // "longitude,latitude"
        destination: string; // "longitude,latitude"
        sig?: string;
      }): void;

      /**
       * 获取驾车路线规划
       */
      getDrivingRoute(options: {
        success?: (res: TransitRouteResult) => void;
        // success?: (res: any) => void;
        fail?: (err: any) => void;
        origin: string; // "longitude,latitude"
        destination: string; // "longitude,latitude"
        waypoints?: string;
        strategy?: number; // 0-速度优先；1-费用优先；2-距离优先；3-不走高速
        sig?: string;
      }): void;

      /**
       * 获取天气情况
       */
      getWeather(options: { success?: (res: AMapWXCallbackResult<WeatherResult>) => void; fail?: (err: any) => void; city?: string; sig?: string }): void;
      /**
       * 获取输入提示
       */
      getInputtips(options: InputtipsOptions): Promise<InputtipsResult>;
    }
  }

  // 导出命名空间
  export import AMapWX = AMapWXNamespace.AMapWX;

  // 默认导出对象
  const amapModule: {
    AMapWX: typeof AMapWXNamespace.AMapWX;
  };
  export default amapModule;
}
