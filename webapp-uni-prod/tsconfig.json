{"compilerOptions": {"target": "esnext", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["@dcloudio/types", "@uni-helper/uni-app-types", "miniprogram-api-typings", "uview-plus/types"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "sourceMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "vueCompilerOptions": {"plugins": ["@uni-helper/uni-app-types/volar-plugin"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts"], "exclude": ["dist", "node_modules", "uni_modules"]}