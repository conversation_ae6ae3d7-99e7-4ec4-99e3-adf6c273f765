// const COOKIE_KEY = "SMART_MINI_CK";
// const setCookie = (values: string[]) => {
//   if (values.length === 0) return;
//   const cookieValue = values.join("; ");
//   uni.setStorageSync(COOKIE_KEY, cookieValue);
// };
const SESSION_ID_KEY = "SMART_LOGIX_MINI_SESSION_ID";
// // 获取 Cookie
// const getCookie = () => {
//   return uni.getStorageSync(COOKIE_KEY);
// };

/**
 * 本地存储服务
 * 提供简单的键值对存储和 JSON 存储功能
 */
const storage = {
  /**
   * 存储后端返回的Session-Id
   */
  setSessionId(sessionId: string | null) {
    if (sessionId !== null) uni.setStorageSync(SESSION_ID_KEY, sessionId);
  },

  getSessionId() {
    const sessionId = uni.getStorageSync(SESSION_ID_KEY);
    if (sessionId) return sessionId;
    // 如果没有存储的 Session-Id，返回 null
    return null;
  },

  clearSessionId() {
    uni.removeStorageSync(SESSION_ID_KEY);
  },

  set(key: string | null, value: string | null) {
    if (key !== null && value !== null) uni.setStorageSync(key, value);
  },
  get(key: string | null) {
    if (key === null) return null;

    return uni.getStorageSync(key);
  },
  setJSON(key: any, jsonValue: any) {
    if (jsonValue !== null) this.set(key, JSON.stringify(jsonValue));
  },
  getJSON(key: any) {
    const value = this.get(key);
    if (value) return JSON.parse(value);
  },
  remove(key: string) {
    uni.removeStorageSync(key);
  },
};

export default storage;
