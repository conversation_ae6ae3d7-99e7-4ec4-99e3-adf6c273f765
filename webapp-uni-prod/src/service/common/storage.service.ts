const SESSION_ID_KEY = "SMART_LOGIX_MINI_SESSION_ID";
const COOKIE_KEY = "SMART_LOGIX_MINI_COOKIE";

/**
 * 本地存储服务
 * 提供简单的键值对存储和 JSON 存储功能
 */
const storage = {
  /**
   * 存储后端返回的Session-Id
   * @param sessionId Session-Id
   * @deprecated 请使用 setCookie ,后端弃用sessionId
   */
  setSessionId(sessionId: string | null | undefined) {
    if (sessionId !== null && sessionId !== undefined)
      uni.setStorageSync(SESSION_ID_KEY, sessionId);
  },
  /**
   * 获取存储的Session-Id
   * @returns 存储的Session-Id或null
   * @deprecated 请使用 getCookie ,后端弃用sessionId
   */
  getSessionId() {
    const sessionId = uni.getStorageSync(SESSION_ID_KEY);
    if (sessionId) return sessionId;
    // 如果没有存储的 Session-Id，返回 null
    return null;
  },
  /**
   * 设置 Cookie
   * @param cookie Cookie 字符串
   */
  setCookie(cookie: string[]) {
    if (cookie && cookie.length > 0) {
      const cookieValue = cookie.join("; ");
      const [key, value] = cookieValue.split("=");
      if (key == "SESSION" && value) {
        uni.setStorageSync(COOKIE_KEY, `${key}=${value.trim()}`);
      }
    }
  },
  /**
   * 获取存储的 Cookie
   * @returns 存储的 Cookie 或 null
   */
  getCookie() {
    return uni.getStorageSync(COOKIE_KEY) || null;
  },
  clearSessionId() {
    uni.removeStorageSync(SESSION_ID_KEY);
  },

  set(key: string | null, value: string | null) {
    if (key !== null && value !== null) uni.setStorageSync(key, value);
  },
  get(key: string | null) {
    if (key === null) return null;

    return uni.getStorageSync(key);
  },
  setJSON(key: any, jsonValue: any) {
    if (jsonValue !== null) this.set(key, JSON.stringify(jsonValue));
  },
  getJSON(key: any) {
    const value = this.get(key);
    if (value) return JSON.parse(value);
    return null;
  },
  setMap(key: any, mapValue: any) {
    const mapObj = Object.fromEntries(mapValue);
    uni.setStorageSync(key, mapObj);
  },
  getMap(key: any) {
    const storedObj = uni.getStorageSync(key);
    const restoredMap = new Map(Object.entries(storedObj));
    return restoredMap;
  },
  remove(key: string) {
    uni.removeStorageSync(key);
  },

  clearSync() {
    // 获取所有存储键
    const storageKeys = uni.getStorageInfoSync().keys;
    // 定义需要保留的键（可扩展）
    const keepKeys = ["storage_weeklyMenu"];
    // 遍历删除非保留项
    storageKeys.forEach((key) => {
      if (!keepKeys.includes(key)) {
        uni.removeStorageSync(key);
      }
    });
  },
};

export default storage;
