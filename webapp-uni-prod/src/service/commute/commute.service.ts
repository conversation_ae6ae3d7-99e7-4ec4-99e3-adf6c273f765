import { CommuteVehicle, CommuteVehicleInfo } from '@/models/Commute';
import { get, post } from '../http';

/**
 * 职工获取上下班路线发车情况接口
 * @param routeType 0:上班路线 1:下班路线
 */
// const getList = (routeType: 0 | 1) => {
//   return post<CommuteRouteInfo[]>('/commuteroute/getList', { data: { routeType } });
// };

/**
 * 发车/结束行程接口
 * @param routeType 0:上班路线 1:下班路线
 */
const updateStatus = (data: CommuteVehicle) => {
  return post('/commutevehicle/updateVehicleStatus', { data });
};

/**
 * 获取通勤班车-路线详细
 * @param routeType 0:上班路线 1:下班路线
 */
const getVehicleDetail = (carId: string) => {
  return get<CommuteVehicleInfo>(`/commutevehicle/getVehicleDetail/${carId}`);
};

/**
 * 职工关注路线接口
 * @param followed 关注路线true，取消关注false
 * @param carId 班车id
 * @param userId 职工id
 */
const saveFollow = (data: { followed: boolean; carId: string; userId: string }) => {
  return post(`/commuteuserfollowvehicle/save`, { data });
};
/**
 * 职工获取上下班车列表接口
 * @param routeType 0:上班路线 1:下班路线
 */
const getList = (routeType: 0 | 1) => {
  return post<CommuteVehicleInfo[]>(`/commutevehicle/getList`, { data: { routeType } });
};
export default { getList, updateStatus, saveFollow, getVehicleDetail };
