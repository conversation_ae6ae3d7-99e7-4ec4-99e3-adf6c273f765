import { CommuteRouteInfo, CommuteRouteType, CommuteVehicle, CommuteVehicleInfo } from '@/models/Commute';
import { get, post } from '../http';
/**
 * 发车/结束行程接口
 * @param routeType 0:上班路线 1:下班路线
 */
const updateStatus = (data: CommuteVehicle) => {
  return post('/commutevehicle/updateVehicleStatus', { data: { ...data, simulation: import.meta.env.VITE_APP_ENV == 'development' ? true : false }, custom: { toast: false } });
};

/**
 * 获取通勤班车-路线详细
 * @param carId 班车ID
 */
const getVehicleDetail = (carId: string) => {
  return get<CommuteVehicleInfo>(`/commutevehicle/getVehicleDetail/${carId}`, { custom: { toast: false, loading: false } });
};

/**
 * 职工关注路线接口
 * @param followed 关注路线true，取消关注false
 * @param carId 班车id
 * @param userId 职工id
 */
const saveFollow = (data: { followed: boolean; carId: string; userId: string }) => {
  return post(`/commuteuserfollowvehicle/save`, {
    data,
    custom: {
      toast: false, // 提示
    },
  });
};
/**
 * 职工获取上下班车列表接口
 * @param userId 职工ID
 * @param driverId 司机ID
 * @param status 0：停止，1：行驶中；全查置空
 */
const getList = (data: { userId?: string; driverId?: string; status?: 0 | 1; routeType: CommuteRouteType }) => {
  return post<CommuteVehicleInfo[]>(`/commutevehicle/getList`, { data, custom: { toast: false, loading: false } });
};
/** 班车配置-save */
const saveCommuteVehicle = (data: CommuteVehicleInfo) => {
  return post('/commutevehicle/save', {
    data,
    custom: {
      successTip: '保存成功',
      errorTip: '保存失败，请重试~',
    },
  });
};
/** 班车配置 -del */
const deleteCommuteVehicle = (id: string) => {
  return get(`/commutevehicle/delete/${id}`, {
    custom: {
      successTip: '删除成功',
      errorTip: '删除失败，请重试~',
    },
  });
};
/** 班车配置 -info */
const getInfoCommuteVehicle = (id: string) => {
  return get<CommuteVehicleInfo>(`/commutevehicle/getInfo/${id}`);
};
/** 班车配置 -list */
const getListCommuteVehicle = () => {
  return get<CommuteVehicleInfo[]>('commutevehicle/getAll');
};

/** 路线配置-save */
const saveCommuteRoute = (data: CommuteRouteInfo) => {
  return post('/commuteroute/save', {
    data,
    custom: {
      successTip: '保存成功',
      errorTip: '保存失败，请重试~',
    },
  });
};
/** 路线配置-info */
const getInfoCommuteRoute = (id: string) => {
  return get<CommuteRouteInfo>(`/commuteroute/getInfo/${id}`);
};
/** 路线配置-del */
const deleteCommuteRoute = (id: string) => {
  return get(`/commuteroute/delete/${id}`, {
    custom: {
      successTip: '删除成功',
      errorTip: '删除失败，请重试~',
    },
  });
};
/** 路线配置-list */
const getListCommuteRoute = (routeType: CommuteRouteType) => {
  return get<CommuteRouteInfo[]>(`/commuteroute/getAll/${routeType}`);
};
/** 班车、路线配置完成发布 */
const publishCommuteConfig = (data: { routeIdList: string[]; carIdsList: string[] }) => {
  return post(`/commuteroute/publish`, { data, custom: { successTip: '配置发布成功', errorTip: '配置发布失败，请重试~' } });
};
export default {
  getList,
  updateStatus,
  saveFollow,
  getVehicleDetail,
  saveCommuteVehicle,
  deleteCommuteVehicle,
  saveCommuteRoute,
  /** 路线配置-info */
  getInfoCommuteRoute,
  deleteCommuteRoute,
  getListCommuteRoute,
  getListCommuteVehicle,
  getInfoCommuteVehicle,
  publishCommuteConfig,
};
