import { get, post } from '@/service/http';
import { Page } from '@/models/Page';
import { Params, Floor, InspectionParams, StationParams, Station, AddStationParams, AddStationAreaParams, Config, ConfigData, InspectionDetail } from '@/subpages/inspection/models/inspection';
//floor 获取楼层站点区域列表
export const getSiteAreaList = (params: Params) => {
    return post<Floor>('/Floor/getList', {
        data: params
    });
}
// floor 新增站点区域
export const addSiteArea = (params: AddStationAreaParams) => {
    return post<null>('/Floor/save', {
        data: params,
        custom: { successTip: '添加成功', errorTip: '添加失败' }
    })
}
// 获取巡检记录列表详情
export const getRecordList = (params: InspectionParams) => {
    return post<any>('/Inspectionrecord/getInspectionrecord', {
        data: params
    });
}
//获取站点列表
export const getSiteList = (params: StationParams) => {
    return post<Page<Station>>('/Site/getList', {
        data: params
    });
}
//删除站点
export const deleteSite = (id: string) => {
    return get<null>(`/Site/delete/${id}`, {
        custom: { successTip: '删除成功', errorTip: '删除失败' }
    });
}
//新增站点
export const addSite = (params: AddStationParams) => {
    return post<Page<Config>>('/Site/save', {
        data: params,
        custom: { successTip: '添加成功', errorTip: '添加失败' }
    });
}
//获取站点详情
export const getSiteDetail = (id: string) => {
    return get<Station>(`/Site/getInfo/${id}`);
}
//获取站点列表
export const getAllFloorName = () => {
    return get<any>('/Site/getAllByFloorName');
}

//sitconfig 获取站点配置列表
export const getSiteConfigList = (params: Params) => {
    return post<Page<Config>>('/Siteconfig/getList', {
        data: params
    });
}
//删除配置
export const deleteSiteConfig = (id: string) => {
    return get<null>(`/Siteconfig/delete/${id}`, {
        custom: { successTip: '删除成功', errorTip: '删除失败' }
    });
}
//获取配置详情
export const getSiteConfigDetail = (id: string) => {
    return get<Config>(`/Siteconfig/getInfo/${id}`);
}
//新增配置
export const addSiteConfig = (params: ConfigData) => {
    return post<null>('/Siteconfig/save', {
        data: params,
        custom: { successTip: '配置成功', errorTip: '配置失败' }
    });
}
//workgroup 获取班组
export const getWorkGroup = (data: { ifPage: boolean }) => {
    return post<any>('/Workgroup/getList', { data: data });
}
//获取巡检记录详情
export const getRecordDetail = (id: string) => {
    return get<InspectionDetail>(`/Inspectionrecord/getInfo/${id}`);
}
//下载巡检记录
export const downloadRecord = (data: { startTime: string, endTime: string }) => {
    return post<any>('/Inspectionrecord/downloadInspectionrecord', {
        data: data,
    });
}

/**巡检相关请求服务 */
export default {
    getSiteAreaList,
    getRecordList,
    getSiteList,
    deleteSite,
    addSiteArea,
    addSite,
    getSiteDetail,
    getSiteConfigList,
    deleteSiteConfig,
    addSiteConfig,
    getAllFloorName,
    getSiteConfigDetail,
    getWorkGroup,
    getRecordDetail,
    downloadRecord
};
