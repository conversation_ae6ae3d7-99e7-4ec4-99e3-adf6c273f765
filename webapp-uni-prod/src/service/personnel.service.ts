import { IPersonnelGroupItem, PersonnelSearch } from '@/models/Personnel';
import { post } from './http';

/** 职工列表 */
const getList = (data: PersonnelSearch) => {
  return post('/Personnel/getList', { data });
};
/** 工作组列表 */
const getListOrganization = (data: { ifPage: boolean; loadPersonnelList: boolean ,keyword?:string}) => {
  return post<IPersonnelGroupItem[]>('/Organization/getList', { data });
};
/** 部门列表 */
const getListWorkGroup = (data: { ifPage: boolean; loadPersonnelList: boolean ,keyword?:string}) => {
  return post<IPersonnelGroupItem[]>('/Workgroup/getList', { data });
};
export default { getList, getListWorkGroup, getListOrganization };
