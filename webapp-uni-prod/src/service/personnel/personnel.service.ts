import { IPermission, IPersonnelGroupItem, Personnel, PersonnelSearch } from '@/models/Personnel';
import { get, post } from '../http';
/** 职工列表 */
const getList = (data: PersonnelSearch) => {
  return post('/Personnel/getList', { data });
};
/** 部门列表 */
const getListOrganization = (data: { ifPage: boolean; loadPersonnelList: boolean; keyword?: string }) => {
  return post<IPersonnelGroupItem[]>('/Organization/getList', { data });
};
/** 工作组列表 */
const getListWorkGroup = (data: { ifPage: boolean; loadPersonnelList: boolean; keyword?: string }) => {
  return post<IPersonnelGroupItem[]>('/Workgroup/getList', { data });
};
const delete_organization = (id: string) => {
  return get(`/Organization/delete/${id}`);
};
const save_organization = (data: IPersonnelGroupItem) => {
  return post('/Organization/save', { data });
};
const get_organization = (id: string) => {
  return get(`/Organization/getInfo/${id}`);
};
const get_workgroup = (id: string) => {
  return get<IPersonnelGroupItem>(`/Workgroup/getInfo/${id}`);
};
const delete_workgroup = (id: string) => {
  return get(`/Workgroup/delete/${id}`);
};
const save_workgroup = (data: IPersonnelGroupItem) => {
  return post('/Workgroup/save', { data });
};
const delete_personnel = (id: string) => {
  return get(`/Personnel/delete/${id}`);
};
const save_personnel = (data: Personnel) => {
  return post('/Personnel/save', { data });
};
const get_personnel = (id: string) => {
  return get<Personnel>(`/Personnel/getInfo/${id}`);
};
/** 权限列表 */
const getPermissions = () => {
  return post<IPermission[]>(`/Permission/getList`, { data: { ifPage: false } });
};
/** 权限列表 */
const getPositions = (data?: { ifPage: boolean; currentPage?: number; pageRecord?: number; nameLike?: string }) => {
  return post<{ id: string; name: string }[]>(`/Position/getList`, { data });
};
const save_positions = (data: { id?: string; name?: string }) => {
  return post('/Position/save', { data });
};
/** 部门 */
const organization = {
  getList: getListOrganization,
  delete: delete_organization,
  save: save_organization,
  getInfo: get_organization,
};
/** 工作组 */
const workgroup = {
  getList: getListWorkGroup,
  delete: delete_workgroup,
  save: save_workgroup,
  getInfo: get_workgroup,
};
const personnel = {
  getList,
  delete: delete_personnel,
  save: save_personnel,
  getInfo: get_personnel,
};
/** 职务 */
const position = {
  getList: getPositions,
  // delete: delete_organization,
  save: save_positions,
  // getInfo: get_organization,
};
export default { organization, workgroup, personnel, getPermissions, position };
