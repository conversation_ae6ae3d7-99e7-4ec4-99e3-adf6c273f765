import { post, get } from "../http";
import type {  IHairConfigParams } from "@/subpages/haircut/pages/appointment/Haircut";
/**基础配置*/
function getHairbaseconfig() {
  return get<string>(
    "https://www.shenlaninfo.com:8047/api/Hairbaseconfig/getBaseConfig"
  );
}
function saveHairbaseconfig(data:any){
  return post<any>(
    "https://www.shenlaninfo.com:8047/api/Hairbaseconfig/save",
    data
  );
}
export default { getHairbaseconfig,saveHairbaseconfig };
