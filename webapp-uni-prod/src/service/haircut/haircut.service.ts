import { post, get } from "../http";
import type { Page } from "@/models/Page";
import type {
  IHairConfigParams,
  Datas,
} from "@/subpages/haircut/pages/appointment/Haircut";
import type { HairNotification } from "@/subpages/haircut/pages/appointment/components/haircut-inform/inform";
/**基础配置*/
function getHairbaseconfig() {
  return post<any>("/Hairbaseconfig/getBaseConfig", {});
}
/**配置保存*/
function saveHairbaseconfig(data: IHairConfigParams) {
  return post<Page<any>>("/Hairbaseconfig/save", { data });
}
/**理发管理*/
function getHaircutconfig(data: number) {
  return get<any>(`/Hairdateconfig/getNewConfigNotOpenByWeek/${data}`);
}
/**删除理发管理*/
function delHaircutconfig(id: string) {
  return get<any>(`/Hairdateconfig/delete/${id}`);
}
/**保存理发管理*/
function saveHaircutconfig(data: Datas) {
  return post<any>(`/Hairdateconfig/save`, { data });
}
/**营业时间*/
function getBusinessHours(data: number) {
  return get<any>(`/Hairdateconfig/getFreeDateByWeek/${data}`);
}
/**理发预约日期*/
function getAppointmentDate() {
  return get<any>(`/Holidaycalendar/getHaircutHolidaycalendarList`);
}
/**通知反馈*/
function getNoticeFeedback() {
  return post<any>(
    `/Hairreservation/getAdminReservationWithNotificationFilter`,
    {}
  );
}
/**删除反馈*/
function delNoticeFeedback(id: string) {
  return get(`/Hairnotification/deleteByAdminDisplayAndId/${id}`);
}
/**删除全部*/
function delAllNoticeFeedback() {
  return get(`/Hairnotification/deleteListByAdminDisplay`);
}
/**再次通知*/
function againNotice(data: HairNotification) {
  return post<any>(`/Hairnotification/save`, { data });
}
/**理发预约*/
function haircutReservation() {
  return post<any>(`/Hairreservation/getReservationByPersonnelId`, {});
}
/**预约日期*/
function getAppointmentDateRange(data: string) {
  return get<any>(`/Hairdateconfig/getHairdateconfigByBusinessDate/${data}`);
}
/**通知到店*/ 
function notifyCustomer(data:any){
  return post<any>(`/Hairnotification/save`, { data });
}
/**员工预约*/ 
function getStaffAppointment(data:any){
  return post<any>(`/Hairreservation/save`, {data});
}
/**员工取消预约*/
function getStaffCancelAppointment(data:any){
  return post<any>(`/Hairreservation/deleteReserveByUser`, {data});
}
/**管理员取消预约*/ 
function getAdminCancelAppointment(data:any){
  return post<any>(`/Hairreservation/deleteReservationByAdmin`, {data});
}
export default {
  getHairbaseconfig,
  saveHairbaseconfig,
  getHaircutconfig,
  getBusinessHours,
  saveHaircutconfig,
  delHaircutconfig,
  getAppointmentDate,
  getNoticeFeedback,
  delNoticeFeedback,
  delAllNoticeFeedback,
  againNotice,
  haircutReservation,
  getAppointmentDateRange,
  notifyCustomer,
  getStaffAppointment,
  getStaffCancelAppointment,
  getAdminCancelAppointment
};
