// 引入配置
import type {
  HttpRequestConfig,
  HttpResponse,
} from "uview-plus/libs/luch-request/index";
import type { IResponse } from "./types";
import Request from "uview-plus/libs/luch-request/index";
import { requestInterceptors, responseInterceptors } from "./interceptors";

const http = new Request();

// 引入拦截器配置
export function setupRequest() {
  http.setConfig((defaultConfig: HttpRequestConfig) => {
    /* defaultConfig 为默认全局配置 */
    defaultConfig.baseURL = import.meta.env.VITE_API_BASE_URL + import.meta.env.VITE_API_PREFIX;
    // #ifdef H5
    if (import.meta.env.VITE_APP_PROXY === "true") {
      defaultConfig.baseURL = import.meta.env.VITE_API_PREFIX;
    }
    // #endif
    console.log(' defaultConfig.baseURL', defaultConfig.baseURL)
    return defaultConfig;
  });
  requestInterceptors(http);
  responseInterceptors(http);
}

export function request<T = any>(config: HttpRequestConfig): Promise<T> {
  return new Promise((resolve, reject) => {
    http
      .request(config)
      .then((res: HttpResponse<IResponse<T>>) => {
        console.log("[ res ] >", res);
        const { rlt, info, datas } = res.data;
        resolve(datas as T);
        if (rlt === 0) {
          resolve(datas as T);
        } else {
          reject(new Error(info || "未知错误"));
        }
      })
      .catch((err: any) => {
        console.error("[ err ] >", err);
        reject(err);
      });
  });
}

export function get<T = any>(
  url: string,
  config?: HttpRequestConfig
): Promise<T> {
  return request({ ...config, url, method: "GET" });
}

export function post<T>(url: string, config?: HttpRequestConfig): Promise<T> {
  return request({ ...config, url, method: "POST" });
}

export function upload<T = any>(
  url: string,
  config?: HttpRequestConfig
): Promise<T> {
  return request({ ...config, url, method: "UPLOAD" });
}

export function download<T = any>(
  url: string,
  config?: HttpRequestConfig
): Promise<T> {
  return request({ ...config, url, method: "DOWNLOAD" });
}

export default setupRequest;
