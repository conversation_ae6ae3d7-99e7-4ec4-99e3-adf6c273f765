import { Content } from '@/models/Content';
import storage from './common/storage.service';
import { get, post } from './http';

interface UploadOptions {
	url: string; //完整上传URL
	filePath: string;
	name: string;
	formData?: any;
	headers?: any;
	onProgressUpdate?: (progress: any) => void;
}

/**仅上传文件 */
export const uploadFile = (options: UploadOptions) => {
	return new Promise<{ url: string } | Content>((resolve, reject) => {
		const cookie = storage.getCookie();
		const { url = '/upload', filePath, name = 'file', formData = { type: 'image' }, headers = {}, onProgressUpdate } = options;
		const requestHeaders = {
			'Content-Type': 'multipart/form-data',
			...(cookie ? { Cookie: cookie } : {}),
			...headers,
		};
		// 构建完整 URL（与拦截器中的 baseURL 逻辑一致）
		let fullUrl = url;
		// #ifdef H5
		if (import.meta.env.VITE_APP_PROXY === 'true') {
			fullUrl = import.meta.env.VITE_API_PREFIX + url;
		} else {
			fullUrl = import.meta.env.VITE_API_BASE_URL + import.meta.env.VITE_API_PREFIX + url;
		}
		// #endif
		// #ifndef H5
		fullUrl = import.meta.env.VITE_API_BASE_URL + import.meta.env.VITE_API_PREFIX + url;
		// #endif
		const task = uni.uploadFile({
			url: fullUrl,
			filePath,
			name,
			formData,
			header: requestHeaders,
			success: (uploadFileRes) => {
				if (uploadFileRes.statusCode >= 200 && uploadFileRes.statusCode < 300) {
					try {
						const res = JSON.parse(uploadFileRes.data);
						resolve(res.datas);
					} catch (e) {
						reject(new Error('响应数据解析失败'));
					}
				} else {
					reject(new Error(`上传失败: ${uploadFileRes.errMsg}`));
				}
			},
			fail: (err) => {
				reject(new Error(`请求失败: ${err.errMsg}`));
			},
		});
		// 进度监听
		task.onProgressUpdate((e) => {
			onProgressUpdate?.(e.progress);
		});
	});
};

/**保存文件到服务器 */
export const saveUploadFile = (data: Content[]) => {
	return post(`/Content/saveBatch`, { data });
};

/**删除菜品图片 */
export const deleteDishImage = (imageId: string) => {
	return get(`/dishimage/delete/${imageId}`);
};

/**批量上传菜品图片 */
export const batchUploadDishImage = (data: any) => {
	return post(`/dishimage/uploadBatch`, { data });
};

/**AI生成菜品图片 */
export const generateDishAIImage = (_data: any) => {
	const { dishName } = _data;
	let data = { dishName, dishId: '' };
	// 存储任务对象的引用
	let requestTask: any = null;
	const promise = post<any>(`/dishimage/generateAiImage`, {
		data,
		custom: { loading: false },
		// 通过 getTask 回调获取任务对象
		getTask: (task) => {
			requestTask = task;
		},
	});
	return {
		promise,
		abort: () => {
			if (requestTask && typeof requestTask.abort === 'function') {
				requestTask.abort();
				console.log('AI图片生成请求已中止');
			} else {
				console.warn('请求任务不可用或已结束');
			}
		},
	};
};
