import { DateType, IDate } from '@/models/Date';
import { post } from '../http';

const BASE_URL = '/Holidaycalendar/';
const getList = (data: { startDate: string; endDate: string; officialType?: DateType; mannualType?: DateType }) => {
  return post<IDate[]>(`${BASE_URL}getList`, { data: { ...data, ifPage: false } });
};

const save = (data: IDate) => {
  return post(`${BASE_URL}save`, { data });
};
/** 批量保存日期 */
const saveList = (datas: IDate[]) => {
  return post<IDate[]>(`${BASE_URL}saveList`, {
    data: datas,
    custom: {
      successTip: '保存成功',
      errorTip: '保存失败，请重试~',
    },
  });
};
export default { getList, save, saveList };
