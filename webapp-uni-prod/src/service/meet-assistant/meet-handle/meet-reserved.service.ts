import type { MeetReserved } from "@/models/MeetReserved1";

import { post, get } from "@/service/http";

const getListReserved = async (search: MeetReserved) => {
  return post<any>("/Meeting/getList", { data: search });
};
const getMeetingroom = async (search: MeetReserved) => {
  return post<any>("/Meetingroom/getList", { data: search });
};
const getCurrentPersonnel = async () => {
  return get("/Personnel/getCurrentPersonnel");
};
const batchDelete = async (search: Array<string>) => {
  return post<any>("/Meeting/batchDelete", { data: search });
};

export default {
  getListReserved,
  getMeetingroom,
  getCurrentPersonnel,
  batchDelete,
};
