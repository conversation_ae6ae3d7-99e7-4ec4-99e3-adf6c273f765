import amapFile, { AMapWX, TransitRouteResult } from '@/hooks/use-wmap/amap-wx.130';
import { onMounted, onUnmounted, ref, toValue } from 'vue';

interface Station {
  id: number | string;
  name: string;
  longitude: number;
  latitude: number;
  desc?: string;
  _init?: any;
}

interface LocationPoint {
  longitude: number;
  latitude: number;
}

interface BusMapOptions {
  busIconPath?: string;
  stationIconPath?: string;
  animationInterval?: number;
  routeColor?: string;
}

const DEFAULT_OPTIONS: BusMapOptions = {
  // busIconPath: '../static/bus.svg',
  // stationIconPath: '../static/point.svg',
  animationInterval: 2000,
  routeColor: '#03CE6A',
};

export function useWmap(options: BusMapOptions = {}) {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  const markers = ref<any[]>([]);
  const polyline = ref<any[]>([]);
  const isLoading = ref(false);
  const myAmapFun = ref<AMapWX | null>(null);
  // 初始化地图
  const initMap = () => {
    // TODO: key
    myAmapFun.value = new amapFile.AMapWX({ key: '' });
  };

  // 获取路线数据
  const getTransitRoute = (stations: any[]): Promise<LocationPoint[]> => {
    return new Promise((resolve, reject) => {
      if (toValue(myAmapFun) == null || stations.length < 2) {
        reject(new Error('地图未初始化或站点数量不足'));
        return;
      }
      const startPoi = `${stations[0].longitude},${stations[0].latitude}`;
      const endPoi = `${stations[stations.length - 1].longitude},${stations[stations.length - 1].latitude}`;
      const waypoints = stations
        .slice(1, stations.length - 1)
        .map(wp => `${wp.longitude},${wp.latitude}`)
        .join(';');
      toValue(myAmapFun)?.getDrivingRoute({
        origin: startPoi,
        destination: endPoi,
        waypoints,
        // success: mapData => {
        success: mapData => {
          // const mapData = res.data;
          let points: LocationPoint[] = [];
          if (mapData?.paths && mapData.paths[0] && mapData.paths[0].steps) {
            const steps = mapData.paths[0].steps;
            for (const step of steps) {
              const stepPoints = step.polyline.split(';').map((point: any) => {
                const [lng, lat] = point.split(',');
                return { longitude: parseFloat(lng), latitude: parseFloat(lat) };
              });
              points = points.concat(stepPoints);
            }
          }
          resolve(points);
        },
        fail: info => {
          console.error('获取路线失败:', info);
          reject(info);
        },
      });
    });
  };

  // 设置站点标记
  const setStationMarkers = (stations: Station[]) => {
    const markerArr =
      stations.map(station => ({
        id: station.id,
        longitude: station.longitude,
        latitude: station.latitude,
        width: 16,
        height: 16,
        iconPath: mergedOptions.stationIconPath,
        anchor: { x: 0.5, y: 0.8 },
      })) || [];
    markers.value = markerArr;
    return markerArr;
  };

  // 设置路线
  const setRouteLine = (points: LocationPoint[], lineColor?: string) => {
    const polylineArr = [
      {
        points,
        color: lineColor || mergedOptions.routeColor,
        width: 6,
        dottedLine: false,
        arrowLine: true,
      },
    ];
    polyline.value = polylineArr;
    return polylineArr;
  };

  // 绘制完整路线
  const drawRoute = async (stations: Station[], lineColor?: string) => {
    try {
      const markers = setStationMarkers(stations);
      const points = await getTransitRoute(stations);
      const line = setRouteLine(points);
      //   startBusAnimation();
      return { markers, line };
    } catch (error) {
      console.error('绘制路线失败:', error);
      return null;
    }
  };

  /** 获取输入提示词 */
  const getInputtips = (keyword: string) => {
    return new Promise((resolve, reject) => {
      if (!toValue(myAmapFun)) reject(null);
      toValue(myAmapFun)?.getInputtips({
        keywords: keyword,
        datatype: 'bus',
        success: function (data) {
          console.log('获取输入提示词：', data);
          resolve(data);
        },
        fail: function (info) {
          console.error(info);
          reject(null);
        },
      });
    });
  };
  const getLocation = (): Promise<UniApp.GetLocationSuccess | null> => {
    return new Promise((resolve, reject) => {
      if (!toValue(myAmapFun)) reject(null);
      uni.getLocation({
        type: 'wgs84',
        success: res => {
          resolve(res);
        },
        fail: _ => {
          reject(null);
        },
      });
    });
  };
  /** 获取步行路线 */
  const getWalkingRoute = (data: { origin: string; destination: string }): Promise<TransitRouteResult | null> => {
    return new Promise((resolve, reject) => {
      if (!toValue(myAmapFun)) resolve(null);
      toValue(myAmapFun)?.getWalkingRoute({
        origin: data.origin,
        destination: data.destination,
        success: mapData => {
          resolve(mapData || null);
        },
        fail: info => {
          console.error('获取步行路线失败:', info);
          reject(null);
        },
      });
    });
  };
  /** 根据当前定位的位置 + 所有站点 = 计算距离当前最近的站点信息 */
  const getNearestStation = async <T = Station>(stations: Station[]): Promise<T | null> => {
    if (!stations?.length) return null;
    const location = await getLocation();
    if (!location) return null;
    const { latitude, longitude } = location;
    let nearestStation = null;
    let minDistance = Infinity;
    for (const station of [stations[0]]) {
      try {
        const routeRes = await getWalkingRoute({
          origin: `${longitude},${latitude}`,
          destination: `${station.longitude},${station.latitude}`,
        });
        if (routeRes?.paths?.length) {
          const distance = routeRes.paths[0].distance;
          if (distance < minDistance) {
            minDistance = distance;
            if (station._init) {
              nearestStation = station._init;
            } else {
              nearestStation = station;
            }
          }
        }
      } catch (error) {}
    }
    console.log('🚀 ~ useBusMap.ts ~ getNearestStation ~ nearestStation:', nearestStation);
    return nearestStation;
  };
  /** 获取两个站点之间的驾驶规划信息 */
  const getDrivingMsg = ({ start, end }: { start: { lat: number; lng: number }; end: { lat: number; lng: number } }): Promise<TransitRouteResult | null> => {
    return new Promise((resolve, reject) => {
      if (!toValue(myAmapFun)) resolve(null);
      toValue(myAmapFun)?.getDrivingRoute({
        origin: `${start.lng},${start.lat}`,
        destination: `${end.lng},${end.lat}`,
        success: mapData => {
          console.log('🚀 ~ index.ts ~ toValue ~ mapData:', mapData);
          resolve(mapData || null);
        },
        fail: info => {
          console.error('获取驾驶路线失败:', info);
          reject(null);
        },
      });
    });
  };
  /** 获取两个站点之间的驾驶规划信息。只要预计时间跟距离 */
  const getDrivingMsgForTime = async (data: { start: { lat: number; lng: number }; end: { lat: number; lng: number } }): Promise<{ distance: string; duration: string } | null> => {
    const drivingMsg = await getDrivingMsg(data);
    console.log('🚀 ~ index.ts ~ getDrivingMsgForTime ~ drivingMsg:', drivingMsg);
    if (!drivingMsg) return null;
    const distance = drivingMsg.paths[0].distance;
    const duration = drivingMsg.paths[0].duration;
    return {
      distance: formatDistance(distance),
      duration: formatSecondsToMinutes(duration),
    };
  };
  function formatDistance(num: number): string {
    if (num == null) return '';
    if (typeof num != 'number') {
      num = Number(num);
    }
    if (num < 1000) {
      return `${num.toFixed(0)}米`;
    } else {
      const km = num / 1000;
      return `${km.toFixed(1)}千米`;
    }
  }
  function formatSecondsToMinutes(seconds: number): string {
    // 秒转分钟（1分钟=60秒）
    const minutes = seconds / 60;

    // 取整处理（四舍五入）
    const formattedMinutes = Math.round(minutes);

    return `${formattedMinutes}分钟`;
  }
  // 清理资源
  const cleanup = () => {
    // stopBusAnimation();
  };

  // 生命周期钩子
  onMounted(() => {
    initMap();
  });

  onUnmounted(() => {
    cleanup();
  });

  return {
    drawRoute,
    myAmapFun,
    getInputtips,
    getNearestStation,
    getDrivingMsg,
    getDrivingMsgForTime,
  };
}
