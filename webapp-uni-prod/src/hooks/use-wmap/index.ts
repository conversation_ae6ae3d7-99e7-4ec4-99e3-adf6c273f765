import amapFile, { AMapWX, TransitRouteResult } from '@/hooks/use-wmap/amap-wx.130';
import { ICommuteCar, ICommuteMapStation, ICommuteStopInfo } from '@/models/Commute';
import { WS_RES_CHANNEL, WsMessage } from '@/models/Websocket';
import { generateRandomNumber } from '@/utils/generaterNum';
import { MapMarker, MapPolyline } from '@uni-helper/uni-app-types';
import { onMounted, onUnmounted, ref, toValue } from 'vue';

interface LocationPoint {
  longitude: number;
  latitude: number;
}

interface BusMapOptions {
  busIconPath?: string;
  stationPastIconPath?: string;
  stationFutureIconPath?: string;
  startIconPath?: string;
  endIconPath?: string;
  animationInterval?: number;
  routeColor?: string;
}
const DEFAULT_OPTIONS: BusMapOptions = {
  // 绝对路径
  // busIconPath:'/src/static'
  busIconPath: '/static/main/commute/bus.png',
  stationPastIconPath: '/static/main/commute/10-6.png',
  stationFutureIconPath: '/static/main/commute/10-7.png',
  startIconPath: '/static/main/commute/start.png',
  endIconPath: '/static/main/commute/end.png',
  animationInterval: 2000,
  routeColor: '#03CE6A',
};

export function useWmap(options: BusMapOptions = {}) {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  const polyline = ref<MapPolyline[]>([]);
  const myAmapFun = ref<AMapWX | null>(null);
  const baseUrl = import.meta.env.VITE_API_BASE_URL as string;
  // 初始化地图
  const initMap = () => {
    // TODO: key
    myAmapFun.value = new amapFile.AMapWX({ key: import.meta.env.VITE_AMAP_KEY || '4327be16f354ad98efd2414bf4166102' });
  };

  // 获取路线数据
  const getTransitRoute = (stations: ICommuteMapStation[]): Promise<LocationPoint[]> => {
    return new Promise((resolve, reject) => {
      if (toValue(myAmapFun) == null || stations.length < 2) {
        reject(new Error('地图未初始化或站点数量不足'));
        return;
      }
      const startPoi = `${stations[0].longitude},${stations[0].latitude}`;
      const endPoi = `${stations[stations.length - 1].longitude},${stations[stations.length - 1].latitude}`;
      const waypoints = stations
        .slice(1, stations.length - 1)
        .map(wp => `${wp.longitude},${wp.latitude}`)
        .join(';');
      toValue(myAmapFun)?.getDrivingRoute({
        origin: startPoi,
        destination: endPoi,
        waypoints,
        // success: mapData => {
        success: mapData => {
          // const mapData = res.data;
          let points: LocationPoint[] = [];
          if (mapData?.paths && mapData.paths[0] && mapData.paths[0].steps) {
            const steps = mapData.paths[0].steps;
            for (const step of steps) {
              const stepPoints = step.polyline.split(';').map((point: any) => {
                const [lng, lat] = point.split(',');
                return { longitude: parseFloat(lng), latitude: parseFloat(lat) };
              });
              points = points.concat(stepPoints);
            }
          }
          resolve(points);
        },
        fail: info => {
          console.error('获取路线失败:', info);
          reject(null);
        },
      });
    });
  };

  // 设置站点标记
  const drawMarker = (stations: ICommuteMapStation[]) => {
    const { startIconPath, endIconPath, stationPastIconPath, stationFutureIconPath } = mergedOptions;
    const markers = stations.filter(e => !e.hideDrawer);
    const markerArr =
      markers.map((e, index) => {
        const defaultUrl = index === 0 ? startIconPath : index === markers.length - 1 ? endIconPath : '';
        const markerTypePath = e._markerType == 'future' ? stationFutureIconPath : e._markerType == 'past' ? stationPastIconPath : '';
        const iconPath = e.iconUrl || markerTypePath || defaultUrl || stationFutureIconPath!;
        const width = e.width || 16;
        const height = e.height || (defaultUrl ? 24 : 16);
        return <MapMarker & { _label: MapMarker['label'] } & ICommuteMapStation>{
          id: generateRandomNumber(),
          longitude: e.longitude,
          latitude: e.latitude,
          width,
          height,
          iconPath,
          anchor: { x: 0.5, y: 0.5 },
          label: { content: e.name, anchorX: -width, anchorY: height / 2, color: '#333', fontSize: 12, bgColor: '#fff', borderColor: '#fff', padding: 6, borderRadius: 3 },
          _label: { content: e.name, anchorX: -width, anchorY: height / 2, color: '#333', fontSize: 12, bgColor: '#fff', borderColor: '#fff', padding: 6, borderRadius: 3 },
          _type: e._type,
        };
      }) || [];
    return markerArr;
  };

  // 设置路线
  const setRouteLine = (points: LocationPoint[], lineColor?: string, borderColor?: string) => {
    const polylineArr: MapPolyline[] = [
      {
        points,
        color: lineColor || mergedOptions.routeColor,
        width: 6,
        dottedLine: false,
        arrowLine: true,
        borderColor,
      },
    ];
    polyline.value = polylineArr;
    return polylineArr;
  };

  /** 绘制完整路线（路线+图标） */
  const drawRoute = async (stations: ICommuteMapStation[], lineColor?: string, borderColor?: string) => {
    let markers = [] as MapMarker[];
    let line = [] as MapPolyline[];
    try {
      markers = drawMarker(stations);
      const points = await getTransitRoute(stations);
      line = setRouteLine(points, lineColor, borderColor);
      //   startBusAnimation();
      return { markers, line, points };
    } catch (error) {
      console.error('绘制路线失败:', error);
      return { markers, line };
      // return null;
    }
  };
  /** 绘制路线 */
  const drawRouteLine = async (stations: ICommuteMapStation[], lineColor?: string) => {
    let line = [] as MapPolyline[];
    try {
      const points = await getTransitRoute(stations);
      line = setRouteLine(points, lineColor);
      return line;
    } catch (error) {
      console.error('绘制路线失败:', error);
    }
    return line;
  };

  /** 获取输入提示词 */
  const getInputtips = (keyword: string) => {
    return new Promise((resolve, reject) => {
      if (!toValue(myAmapFun)) reject(null);
      toValue(myAmapFun)?.getInputtips({
        keywords: keyword,
        datatype: 'bus',
        success: function (data) {
          console.log('获取输入提示词：', data);
          resolve(data);
        },
        fail: function (info) {
          console.error(info);
          reject(null);
        },
      });
    });
  };
  let currentLocation: UniApp.GetLocationSuccess | null = null;
  const getLocation = (): Promise<UniApp.GetLocationSuccess | null> => {
    return new Promise((resolve, reject) => {
      if (currentLocation) return resolve(currentLocation);
      if (!toValue(myAmapFun)) reject(null);
      uni.getLocation({
        type: 'wgs84',
        success: res => {
          currentLocation = res;
          resolve(res);
        },
        fail: _ => {
          reject(null);
        },
      });
    });
  };
  /** 获取步行路线 */
  const getWalkingRoute = (data: { origin: string; destination: string }): Promise<TransitRouteResult | null> => {
    return new Promise((resolve, reject) => {
      if (!toValue(myAmapFun)) reject(null);
      toValue(myAmapFun)?.getWalkingRoute({
        origin: data.origin,
        destination: data.destination,
        success: mapData => {
          resolve(mapData || null);
        },
        fail: info => {
          console.error('获取步行路线失败:', info);
          reject(null);
        },
      });
    });
  };
  /** 根据当前定位的位置 + 所有站点 = 计算距离当前最近的站点信息 */
  const getNearestStation = async <T = ICommuteMapStation>(stations: ICommuteMapStation[]): Promise<T | null> => {
    if (!stations?.length) return null;
    const location = await getLocation();
    if (!location) return null;
    const { latitude, longitude } = location;
    let nearestStation = null;
    let minDistance = Infinity;
    for (const station of stations) {
      try {
        const routeRes = await getWalkingRoute({
          origin: `${longitude},${latitude}`,
          destination: `${station.longitude},${station.latitude}`,
        });
        if (routeRes?.paths?.length) {
          const distance = routeRes.paths[0].distance;
          if (distance < minDistance) {
            minDistance = distance;
            if (station._init) {
              nearestStation = station._init;
            } else {
              nearestStation = station;
            }
          }
        }
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {}
    }
    return nearestStation;
  };
  /** 获取站点之间的驾驶规划信息 */
  const getDrivingMsg = (datas: { lat: number; lng: number }[]): Promise<TransitRouteResult | null> => {
    return new Promise((resolve, reject) => {
      if (!toValue(myAmapFun)) resolve(null);
      if (datas.length < 2) return null;
      const start = datas[0];
      const end = datas[datas.length - 1];
      const waypoints = datas
        .slice(1, datas.length - 1)
        .map(wp => `${wp.lng},${wp.lat}`)
        .join(';');
      toValue(myAmapFun)?.getDrivingRoute({
        origin: `${start.lng},${start.lat}`,
        destination: `${end.lng},${end.lat}`,
        waypoints,
        success: mapData => {
          resolve(mapData || null);
        },
        fail: info => {
          console.error('获取驾驶路线失败:', info);
          reject(null);
        },
      });
    });
  };
  /** 获取两个站点之间的驾驶规划信息。只要预计时间跟距离 */
  const getDrivingMsgForTime = async (datas: { lat: number; lng: number }[]): Promise<{ distance: string; duration: string; durationMs: number } | null> => {
    const drivingMsg = await getDrivingMsg(datas);
    if (!drivingMsg) return null;
    const distance = drivingMsg.paths[0].distance;
    const duration = drivingMsg.paths[0].duration;
    return {
      distance: formatDistance(distance),
      duration: formatSecondsToMinutes(duration),
      durationMs: duration,
    };
  };

  /**
   *
   * 更新距离当前最近站点的数据
   * @param stopList 站点列表
   * @param currentStop 当前站点
   * */
  const updateNearestData = async (stopList: ICommuteStopInfo[], currentStop: ICommuteStopInfo) => {
    const nearestData: { distance?: string; duration?: string; durationMs?: number; nearestStop: ICommuteStopInfo | null } | null = { nearestStop: null };
    try {
      const newNearestStop = await getNearestStop(stopList);
      nearestData.nearestStop = newNearestStop;
      if (newNearestStop) {
        const msg = await getNearestTip(currentStop, newNearestStop);
        Object.assign(nearestData, msg || {});
      }
    } catch (err) {}
    return nearestData;
  };
  const getNearestTip = async (currentStop: ICommuteStopInfo, nearestStop: ICommuteStopInfo) => {
    if (!currentStop?.id || !nearestStop.id) return;
    const startPoint = { lat: currentStop.lat, lng: currentStop.lng };
    const endPoint = { lat: nearestStop.lat, lng: nearestStop.lng };
    let msg: { distance: string; duration: string; durationMs: number } | null = null;
    try {
      msg = await getDrivingMsgForTime([startPoint, endPoint]);
    } catch (_) {}
    return msg;
  };
  const getNearestStop = async (stopList: ICommuteStopInfo[]) => {
    try {
      const nearestStop = await getNearestStation(
        stopList.map(s => {
          return {
            id: s.id!,
            name: s.name,
            longitude: s.lng,
            latitude: s.lat,
            _init: s,
          };
        })
      );
      const newNearestStop = nearestStop as unknown as ICommuteStopInfo;
      return newNearestStop;
    } catch (error) {
      return null;
    }
  };

  function formatDistance(num: number): string {
    if (num == null) return '';
    if (typeof num != 'number') {
      num = Number(num);
    }
    if (num < 1000) {
      return `${num.toFixed(0)}米`;
    } else {
      const km = num / 1000;
      return `${km.toFixed(1)}千米`;
    }
  }
  function formatSecondsToMinutes(seconds: number): string {
    // 秒转分钟（1分钟=60秒）
    const minutes = seconds / 60;

    // 取整处理（四舍五入）
    const formattedMinutes = Math.round(minutes);

    return `${formattedMinutes}分钟`;
  }
  const socketTaskInstance = ref<UniApp.SocketTask | null>(null);
  /** 原始实时班车位置数据 */
  const newShuttleDatas = ref<ICommuteCar[]>([]);
  /** 原始实时班车marker */
  const newShuttleMarkers = computed(() => {
    return (
      toValue(newShuttleDatas).map(s => {
        return <MapMarker>{
          id: generateRandomNumber(),
          longitude: s.lng,
          latitude: s.lat,
          iconPath: mergedOptions.busIconPath,
          height: 28,
          width: 28,
          anchor: { x: 0.5, y: 0.5 },
        };
      }) || []
    );
  });
  /** 创建局部 websocket；获取实时公交位置 */
  const connectSocketBus = async (data?: { carId?: string; routeId?: string }) => {
    const wsReportUrl = baseUrl.replace(/^http(s)?:/, 'wss:'); // 小程序中必须是 wss:// 协议
    const url = `${wsReportUrl}/websocket-smartlogixmini`;
    closeSocketBus();
    // uni.connectSocket() 正常使用时是会返回 task 对象的，如果想获取 task ，则不要使用 Promise 化
    socketTaskInstance.value = uni.connectSocket({
      url,
      success() {
        console.log('实时公交位置 WebSocket 连接成功');
      },
      fail() {
        console.log('实时公交位置 WebSocket 连接失败');
      },
    });
    const { carId, routeId } = data || {};
    socketTaskInstance.value.onOpen(() => {
      socketTaskInstance.value?.onMessage(res => {
        const messageData = JSON.parse(res.data) as WsMessage<ICommuteCar[]>;
        if (messageData.type == WS_RES_CHANNEL.SHUTTLE) {
          let datas = messageData.data || [];
          // 优先剔除同路线
          if (routeId) {
            datas = datas.filter(s => s.routeId == routeId);
          }
          if (carId) {
            datas = datas.filter(s => s.id == carId);
          }
          newShuttleDatas.value = datas;
        }
      });
    });
  };
  const closeSocketBus = () => {
    toValue(socketTaskInstance)?.close({
      success: () => {
        socketTaskInstance.value = null;
      },
    });
  };
  // 清理资源
  const cleanup = () => {
    // stopBusAnimation();
    closeSocketBus();
  };

  // 生命周期钩子
  onMounted(() => {
    initMap();
  });

  onUnmounted(() => {
    cleanup();
  });
  return {
    drawRoute,
    drawRouteLine,
    drawMarker,
    myAmapFun,
    getInputtips,
    getNearestStation,
    // getDrivingMsg,
    getDrivingMsgForTime,
    updateNearestData,
    connectSocketBus,
    newShuttleMarkers,
    closeSocketBus,
    newShuttleDatas,
    setRouteLine,
  };
}
