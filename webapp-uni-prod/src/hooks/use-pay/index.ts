export function usePay() {
  async function requestPayment() {
    uni.requestPayment({
      provider: "wxpay",
      timeStamp: String(Date.now()),
      nonceStr: "A1B2C3D4E5",
      package: "prepay_id=wx20180101abcdefg",
      signType: "RSA",
      paySign: "",
      // 订单信息可以根据实际情况传入
      orderInfo: {},
      success: function (res) {
        console.log("success:" + JSON.stringify(res));
      },
      fail: function (err) {
        console.log("fail:" + JSON.stringify(err));
      },
    });
  }
  return {
    requestPayment,
  };
}
