import { ref } from 'vue';
export default function useDebounce<T extends (...args: any[]) => any>(fn: T, delay: number = 300, immediate: boolean = false) {
  const timer = ref<number | null>(null);
  const isWaiting = ref(false);

  function debouncedFn(...args: Parameters<T>) {
    if (timer.value) {
      clearTimeout(timer.value);
    }

    if (immediate && !isWaiting.value) {
      fn(...args);
      isWaiting.value = true;
    } else {
      timer.value = setTimeout(() => {
        fn(...args);
        timer.value = null;
        isWaiting.value = false;
      }, delay);
    }
  }

  return debouncedFn;
}
