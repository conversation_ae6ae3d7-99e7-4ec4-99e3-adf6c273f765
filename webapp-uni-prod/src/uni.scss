/* uni.scss */
@import "uview-plus/theme.scss";
/* 颜色变量 */
/* 行为相关颜色 */
// $uni-color-primary: #007aff;
// $uni-color-success: #4cd964;
// $uni-color-warning: #f0ad4e;
// $uni-color-error: #dd524d;

// /* 文字基本颜色 */
$uni-text-color: #333; // 基本色
$uni-text-color-primary: #3b6eeb; // 主要文字颜色，通常用于链接或强调的文字
$uni-text-color-secondary: #666; // 次要文字颜色
$uni-text-color-grey: #999; // 辅助灰色，如加载更多的提示信息
// $uni-text-color-inverse: #fff; // 反色
$uni-text-color-placeholder: #999999; // 输入框占位符颜色
$uni-text-color-disable: #c0c0c0; // 禁用状态文字颜色

// /* 背景颜色 */
$uni-bg-color: #fff;
$uni-bg-color-grey: #f4f6fa;
// $uni-bg-color-hover: #f1f1f1; // 点击状态颜色
// $uni-bg-color-mask: rgba(0, 0, 0, 0.4); // 遮罩颜色

/*按钮相关颜色*/
$uni-btn-primary-bg: #3b6eeb; // 主按钮背景色

// /* 边框颜色 */
// $uni-border-color: #c8c7cc;

// /* 尺寸变量 */

// /* 文字尺寸 */
$uni-font-size-xs: 10px;
$uni-font-size-sm: 12px;
$uni-font-size-base: 14px;
$uni-font-size-lg: 16px;
$uni-font-size-xl: 18px;

// /* 图片尺寸 */
// $uni-img-size-sm: 20px;
// $uni-img-size-base: 26px;
// $uni-img-size-lg: 40px;

// /* Border Radius */
// $uni-border-radius-sm: 2px;
// $uni-border-radius-base: 3px;
// $uni-border-radius-lg: 6px;
// $uni-border-radius-circle: 50%;

// /* 水平间距 */
// $uni-spacing-row-sm: 5px;
// $uni-spacing-row-base: 10px;
// $uni-spacing-row-lg: 15px;

// /* 垂直间距 */
// $uni-spacing-col-sm: 4px;
// $uni-spacing-col-base: 8px;
// $uni-spacing-col-lg: 12px;

// /* 透明度 */
// $uni-opacity-disabled: 0.3; // 组件禁用态的透明度

// /* 文章场景相关 */
// $uni-color-title: #2c405a; // 文章标题颜色
// $uni-font-size-title: 20px;
// $uni-color-subtitle: #555; // 二级标题颜色
// $uni-font-size-subtitle: 18px;
// $uni-color-paragraph: #3f536e; // 文章段落颜色
// $uni-font-size-paragraph: 15px;

// style lang="scss" scoped 会用上该样式
// div => view
view {
  box-sizing: border-box;
}
// body => page
page {
  font-weight: 400;
  font-size: 14px;
  height: 100%;
  width: 100%;
  color: $uni-text-color;
}

