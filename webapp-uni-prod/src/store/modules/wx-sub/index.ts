import { defineStore } from "pinia";
import { getMessagetemplate } from "@/service/sub-message.service";
interface TemplateIdItem {
  name: string;
  wechatTemplateId: string;
  [key: string]: any;
}
const useWxSubStore = defineStore("ws-sub", {
  state: () => ({
    templates: [] as TemplateIdItem[],
  }),
  getters: {
    /**获取所有模板id信息 */
    getTemplates(state) {
      return state.templates;
    }
  },
  actions: {
    async setTemplates() {
      this.templates = await getMessagetemplate();      
    }
  },
});

export default useWxSubStore;
