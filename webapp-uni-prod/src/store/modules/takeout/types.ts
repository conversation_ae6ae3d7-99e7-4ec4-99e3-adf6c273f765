import {
  TakeoutMenu,
  TakeoutMenuDish,
  TakeoutOrder,
} from "@/subpages/takeout/models";
export interface TakeoutState {
  /** 购物车添加的外卖菜品 */
  cart: TakeoutMenuDish[];
  /** 购物车总价 */
  totalPrice: number;
  /** 明天可订购外卖菜品菜单 */
  takeoutMenu: TakeoutMenu;
  /** 当前选购的订单 */
  takeoutOrder: TakeoutOrder;
  /** 所有的订单列表 */
  orderList: TakeoutOrder[];
}

export interface TakeoutActions {
  privateUpdateMenuDishCount(dishId: string, delta: number): void;
  /** 添加菜品到购物车 */
  addToCart(item: TakeoutMenuDish): void;
  /** 从购物车中移除菜品 */
  removeFromCart(item: TakeoutMenuDish): void;
  /** 清空购物车 */
  clearCart(): void;
  /** 设置明天可订购外卖菜品菜单 */
  setTakeoutMenu(menu: TakeoutMenu): void;
  /** 计算购物车总价 */
  calculateTotalPrice(): void;
  /** 获取购物车菜品数量 */
  getCartItemCount(): number;
  /** 设置当前选购的订单 */
  setCurrentOrder(order: TakeoutOrder): void;
  /** 设置订单列表 */
  setOrderList(orders: TakeoutOrder[]): void;
}

export interface TakeoutGetters {
  /** 获取购物车菜品列表 */
  getCart: () => TakeoutMenuDish[];
  /** 获取购物车总价 */
  getTotalPrice: () => number;
  /** 获取明天可订购外卖菜品菜单 */
  getTakeoutMenu: () => TakeoutMenu;
  /** 获取当前选购的订单 */
  getCurrentOrder: () => TakeoutOrder;
  /** 获取所有订单列表 */
  getAllOrders: () => TakeoutOrder[];
}
