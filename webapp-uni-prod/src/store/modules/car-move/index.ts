import type { Personnel } from "@/models/CarMove";
import { defineStore } from "pinia";
const useCarMoveStore = defineStore("carMoveStore", {
  state: () => ({
    carmoveKeyword: "" as string,
    personList: [] as Personnel[],
  }),
  actions: {
    setKeyword(kw: string) {
      this.carmoveKeyword = kw;
    },
    setPersonList(list: Personnel[]) {
      this.personList = list;
    },
    clear() {
      this.carmoveKeyword = "";
      this.personList = [];
    },
  },
});
export default useCarMoveStore;
