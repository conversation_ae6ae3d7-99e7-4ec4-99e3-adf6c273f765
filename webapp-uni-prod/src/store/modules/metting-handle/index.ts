import type { Meeting, Meetingpersonnel } from "@/models/MettingHandle";
import { defineStore } from "pinia";
import { computed, reactive, ref } from "vue";

const useMettingHandleStore = defineStore("mettingHandle", () => {
  const mettingHandle = ref<Meeting>({ roomSelectionType: 1 });
  const moreGroupData = reactive<{
    name: string;
    datas: (Meetingpersonnel & { _isChecked: boolean })[];
  }>({
    name: "",
    datas: [],
  });
  const reset = () => {
    mettingHandle.value = { roomSelectionType: 1 };
    timeChanged.value = false;
    contentChanged.value = false;
    moreGroupData.name = "";
    moreGroupData.datas = [];
  };
  const resetGroupData = () => {
    moreGroupData.name = "";
    moreGroupData.datas = [];
  };
  // 已参会人员id
  const getPersonnelIds = computed(() => {
    return (
      mettingHandle.value.meetingpersonnelList
        ?.map((item) => item.personnel?.id)
        .filter((e) => !!e) || []
    );
  });
  const timeChanged = ref(false);
  const contentChanged = ref(false);
  const personnelChanged = ref(false);
  return {
    reset,
    mettingHandle,
    getPersonnelIds,
    timeChanged,
    contentChanged,
    personnelChanged,
    moreGroupData,
    resetGroupData,
  };
});
export default useMettingHandleStore;
