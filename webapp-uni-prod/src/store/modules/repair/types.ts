import { Personnel } from "@/models/Personnel";

export interface RepairState {
  /**是否为职工端 */ ifStaff: boolean;
  /**选中的内部维修人员信息 */  fixManInfo: Personnel;
}

export interface RepairActions {
  /**设置是否为职工端 */setIfStaff(type: boolean): void;
  /**设置选中内部的维修人员 */setFixMan(info: Personnel): void;
}

export interface RepairGetters {
  // /**获取当前搜索参数 */searchParams: () => RepairSearch;
  [key: string]: ((state: RepairState) => any) | (() => any);
}