import { defineStore } from 'pinia';
import type { Account } from '@/models/Account';
import authService from '@/service/auth';
import storage from '@/service/common/storage.service';

const usePrincipalStore = defineStore('principal', {
	state: () => ({
		userIdentity: undefined as Account | undefined,
	}),
	getters: {
		// 获取用户身份信息
		account(state) {
			return state.userIdentity;
		},
		// 是否已登录
		isAuthenticated(state) {
			return state.userIdentity !== undefined && state.userIdentity.id !== undefined;
		},
		// TODO：是否是超级管理员,逻辑待定
		isAdmin(state) {
			return state.userIdentity?.permissionList.findIndex((item) => item.name === 'admin') !== -1;
		},
	},
	actions: {
		hasAnyAuthority(authorities: string[]) {
			if (!this.userIdentity) {
				return false;
			}
			return authorities.some((authority) => this.userIdentity?.permissionList.findIndex((item) => item.name === authority) !== -1);
		},
		async logout() {
			await authService.logout();
			this.userIdentity = undefined;
			storage.clearSync();
		},
		async identity(force?: boolean): Promise<Account | undefined> {
			if (force === true) {
				this.userIdentity = undefined;
			}
			if (this.userIdentity && this.userIdentity.id) {
				return Promise.resolve(this.userIdentity);
			}
			return authService
				.getAccount()
				.then((account) => {
					if (account && account.id) {
						this.userIdentity = account;
						return this.userIdentity;
					}
					console.error('获取用户身份信息失败，返回值为空或未定义');
					this.userIdentity = undefined;
					return this.userIdentity;
				})
				.catch((e: string) => {
					console.error(e);
					this.userIdentity = undefined;
					return this.userIdentity;
				});
		},
		clearIndentity() {
			this.userIdentity = undefined;
			storage.clearSync();
		},
	},
});

export default usePrincipalStore;
