import { defineStore } from "pinia";
import type { Account } from "@/models/Account";
import authService from "@/service/auth";

const usePrincipalStore = defineStore("principal", {
  state: () => ({
    userIdentity: undefined as Account | undefined,
  }),
  getters: {
    account(state) {
      return state.userIdentity;
    },
    isAuthenticated(state) {
      return state.userIdentity !== undefined;
    },
  },
  actions: {
    // 根据权限名称判断是否有权限
    hasAnyAuthority(authorities: string[]) {
      if (!this.userIdentity) {
        return false;
      }
      return authorities.some(
        (authority) =>
          this.userIdentity?.permissionList.findIndex(
            (item) => item.name === authority
          ) !== -1
      );
    },
    async logout() {
      await authService.logout();
      this.userIdentity = undefined;
      // 清除缓存
      uni.clearStorageSync();
    },
    async identity(force?: boolean) {
      if (force === true) {
        this.userIdentity = undefined;
      }
      if (this.userIdentity) {
        return Promise.resolve(this.userIdentity);
      }
      return authService
        .getAccount()
        .then((account) => {
          this.userIdentity = account;
          return this.userIdentity;
        })
        .catch((e: string) => {
          console.error(e);
          this.userIdentity = undefined;
          return this.userIdentity; // emit error
        });
    },
  },
});

export default usePrincipalStore;
