import type { AppState } from './types';
import { defineStore } from 'pinia';
const BASE_URL = import.meta.env.VITE_API_BASE_URL;
const ENV = import.meta.env.VITE_APP_ENV;
const APP_TITLE = import.meta.env.VITE_APP_TITLE;

const useAppStore = defineStore('app', {
	state: (): AppState => ({
		systemInfo: {} as UniApp.GetSystemInfoResult,
		accountInfo: {} as UniNamespace.AccountInfo,
		baseUrl: BASE_URL as string,
		appName: APP_TITLE as string,
		isDevMode: ENV === 'development', // 是否是开发模式下打包
		isBuildMode: ENV === 'production', // 是否是生产环境打包
		isPreviewMode: ENV === 'preview', // 是否是预览模式
		previewVersion: 'v1.1.6.RC11', // 体验版本号
	}),
	getters: {
		getBaseUrl(): string {
			// 如果是体验版环境，返回预览环境地址
			// if (this.isTrial) {
			//   return "https://www.shenlaninfo.com:9047";
			// }
			return this.baseUrl;
		},
		// 是否是体验版环境,线上体验版,shenlaninfo.com:9047
		isTrial(): boolean {
			return this.accountInfo.miniProgram?.envVersion === 'trial';
		},
		// 打包时使用production 环境，运行在微信开发者工具中使用develop 环境,此环境使用正式库,qwtservice.com
		isLocalBuild(): boolean {
			return this.isBuildMode && this.accountInfo.miniProgram?.envVersion === 'develop';
		},
		// 打包时使用preview 环境，运行在微信开发者工具中使用develop 环境,此环境使用预览库，shenlaninfo.com:9047
		isLocalPreview(): boolean {
			return this.isPreviewMode && this.accountInfo.miniProgram?.envVersion === 'develop';
		},
		getSystemInfo(): UniApp.GetSystemInfoResult {
			return this.systemInfo;
		},

		// 获取环境标识
		getEnvText(): string {
			if (this.isDevMode) return '开发版'; //  微信开发者工具，dev 环境
			if (this.isLocalPreview) return `体验预览版${this.previewVersion}`; //  微信开发者工具，preview 环境
			if (this.isLocalBuild) return '正式预览版'; //  微信开发者工具，build 环境
			if (this.isTrial) return `体验版${this.previewVersion}`; // 线上体验版 preview 环境
			return ''; // 线上正式版,不显示环境标识
		},
	},
	actions: {
		setSystemInfo(info: UniApp.GetSystemInfoResult) {
			this.systemInfo = info;
		},
		setAccountInfo(info: UniNamespace.AccountInfo) {
			this.accountInfo = info;
			console.log('Account Info:', info);
		},
		initSystemInfo() {
			uni.getSystemInfo({
				success: (res: UniApp.GetSystemInfoResult) => {
					this.setSystemInfo(res);
				},
				fail: (err: any) => {
					console.error(err);
				},
			});
		},
		initAccountInfo() {
			const info: UniNamespace.AccountInfo = uni.getAccountInfoSync();
			this.setAccountInfo(info);
		},
		checkUpdate() {
			const updateManager = uni.getUpdateManager();
			updateManager.onCheckForUpdate((res: UniApp.OnCheckForUpdateResult) => {
				// 请求完新版本信息的回调
				console.log('请求完新版本信息的回调', res.hasUpdate);
			});
			updateManager.onUpdateReady(() => {
				uni.showModal({
					title: '更新提示',
					content: '新版本已经准备好，是否重启应用?',
					success(res) {
						if (res.confirm) {
							// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
							updateManager.applyUpdate();
						}
					},
				});
			});
			updateManager.onUpdateFailed((res: any) => {
				console.error(res);
				// 新的版本下载失败
				uni.showToast({
					title: '更新失败',
					icon: 'error',
				});
			});
		},
	},
});

export default useAppStore;
