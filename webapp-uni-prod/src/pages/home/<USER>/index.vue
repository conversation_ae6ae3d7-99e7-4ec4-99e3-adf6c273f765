<template>
  <view>
    <!-- 首页服务 -->
    <view class="home-ser">
      <text class="home-ser-t">首页服务</text>
      <view class="home-ser-view home-ser-view1">
        <view v-for="(item, index) in mainServices" :key="index" @click="skipHtml(item)">
          <SlSvgIcon :name="item.icon" size="27" />
          <text>{{ item.label }}</text>
        </view>
      </view>
    </view>
    <!-- 服务卡片 -->
    <view class="home-ser-card">
      <view class="ser-card" v-for="(card, index) in serviceCards" :key="index">
        <text class="home-ser-t">{{ card.title }}</text>
        <view class="home-ser-view">
          <template v-for="(item, idx) in card.items" :key="idx">
            <view 
              class="card-item" 
              v-if="item.icon || item.label"
              @click="skipHtml(item)"
            >
              <SlSvgIcon :name="item.icon" size="27" />
              <text>{{ item.label }}</text>
            </view>
          </template>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import SlSvgIcon from '@/components/SlSVgIcon.vue'
import { mainServices, serviceCards } from '@/utils/navigation'

const skipHtml = (item: { url: string }) => {
  if (!item.url) return
  uni.navigateTo({ url: item.url })
}
</script>

<style scoped>
/* 保持原有样式不变 */
.home-ser {
  opacity: 1;
  border-radius: 0px;
  background: #fdfdfd;
  box-shadow: 0px 0px 4px 0px #92929233;
  width: 100%;
  height: 101px;
  padding: 12px 15px;
  box-sizing: border-box;
}

.home-ser .home-ser-t {
  color: #333333;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 14px;
  display: block;
}

.home-ser-view {
  height: 50px;
}

.home-ser .home-ser-view::-webkit-scrollbar {
  display: none;
}

.home-ser .home-ser-view view {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  min-width: 42px;
  height: 42px;
  margin-right: 15px;
  flex-shrink: 0;
}

.home-ser .home-ser-view text {
  font-size: 12px;
  white-space: nowrap;
}

.home-ser-card {
  width: 100%;
  padding: 10px 17px;
  box-sizing: border-box;
}

.home-ser-card .ser-card {
  padding: 12px 15px;
  height: 101px;
  opacity: 1;
  border-radius: 10px;
  background: linear-gradient(180deg, #e7f3ff 0%, #ffffff 100%);
  border: 1px solid #ffffff;
  box-shadow: 0px 0px 4px 0px #92929233;
  margin-bottom: 9px;
}

.home-ser-card .ser-card .home-ser-t {
  color: #333333;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 14px;
  display: block;
}

.home-ser-card .ser-card .card-item {
  margin-right: 5px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  width: 70px;
}

.home-ser-card .ser-card .card-item text {
  display: block;
  width: 100%;
  text-align: center;
  font-size: 12px;
}

.home-ser-view {
  width: 100%;
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  padding-bottom: 10px;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
}
</style>