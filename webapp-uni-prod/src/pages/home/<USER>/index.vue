<template>
    <view>
      <!-- 首页服务 -->
      <view class="home-ser">
        <text class="home-ser-t">首页服务</text>
        <view class="home-ser-view home-ser-view1">
          <view v-for="(item, index) in mainServices" :key="index" @click="skipHtml(item.label)">
            <SlSvgIcon :name="`${item.icon}`" size="27" />
            <text>{{ item.label }}</text>
          </view>
        </view>
      </view>
      <!-- 服务卡片 -->
      <view class="home-ser-card">
        <view class="ser-card" v-for="(card, index) in serviceCards" :key="index">
          <text class="home-ser-t">{{ card.title }}</text>
          <view class="home-ser-view">
            <view class="card-item" v-for="(item, idx) in card.items" :key="idx" @click="skipHtml(item.label)">
            <SlSvgIcon :name="`${item.icon}`" size="27" />
              <text>{{ item.label }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </template>
  <script setup lang="ts">
  // import { useRouter } from 'vue-router'
  import SlSvgIcon from '@/components/SlSVgIcon.vue'
  // const router = useRouter()
  
  const skipHtml = (value:string) => {
    if(value == '会议助手'){
      uni.navigateTo({ url:'/meetSchedule'}) 
    }else if(value == '挪车服务'){
      uni.navigateTo({ url:'/car-move'}) 
    }
  }
  
  // 首页服务数据
  const mainServices = [
    { icon: 'home-通勤服务', label: '通勤服务' },
    { icon: 'home-会议管理', label: '会议助手' },
    { icon: 'home-用餐服务', label: '用餐服务' },
    { icon: 'home-物业服务', label: '物业服务' },
    { icon: 'home-理发', label: '理发预约' },
    { icon: 'home-安全', label: '安全管理' },
    { icon: 'home-固定资产', label: '固定资产管理' }
  ]
  
  // 卡片服务数据
  const serviceCards = [
    {
      title: '办公服务',
      items: [
        { icon: 'home-会议管理', label: '会议助手' },
        { icon: 'home-固定资产', label: '固定资产管理' },
        { icon: 'home-人事管理', label: '人事管理' },
        { icon: '', label: '' }
      ]
    },
    {
      title: '出行服务',
      items: [
        { icon: 'home-通勤服务', label: '通勤服务' },
        { icon: 'home-派车', label: '派车管理' },
        { icon: 'home-挪车', label: '挪车服务' },
        { icon: '', label: '' }
      ]
    },
    {
      title: '生活服务',
      items: [
        { icon: 'home-用餐服务', label: '用餐服务' },
        { icon: 'home-外卖服务', label: '外卖服务' },
        { icon: 'home-理发', label: '理发预约' },
        { icon: 'home-日用品', label: '日用品库' }
      ]
    },
    {
      title: '更多服务',
      items: [
        { icon: 'home-物业服务', label: '物业服务' },
        { icon: 'home-安全', label: '安全管理' },
        { icon: 'home-消息推送1', label: '消息推送' },
        { icon: '', label: '' }
      ]
    }
  ]
  </script>
  
  <style scoped >
.home-ser {
  opacity: 1;
  border-radius: 0px;
  background: #fdfdfd;
  box-shadow: 0px 0px 4px 0px #92929233;
  width: 100%;
  height: 101px;
  padding: 12px 15px;
  box-sizing: border-box;
}

.home-ser .home-ser-t {
  color: #333333;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 14px;
  display: block;
}
.home-ser-view{
    height: 50px;
}
.home-ser .home-ser-view::-webkit-scrollbar {
  display: none;
}

.home-ser .home-ser-view view {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  /* justify-content: center; */
  min-width: 42px;
  height: 42px;
  margin-right: 15px;
  flex-shrink: 0;
}

.home-ser .home-ser-view text {
  font-size: 12px;
  white-space: nowrap;
}

.home-ser-card {
  width: 100%;
  /* height: calc(100vh - 239px); */
  padding: 10px 17px;
  box-sizing: border-box;
}

.home-ser-card .ser-card {
  padding: 12px 15px;
  height: 101px;
  opacity: 1;
  border-radius: 10px;
  background: linear-gradient(180deg, #e7f3ff 0%, #ffffff 100%);
  border: 1px solid #ffffff;
  box-shadow: 0px 0px 4px 0px #92929233;
  margin-bottom: 9px;
}

.home-ser-card .ser-card .home-ser-t {
  color: #333333;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 14px;
  display: block;
}

.home-ser-card .ser-card .card-item {
  margin-right: 5px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  width: 70px;
}

.home-ser-card .ser-card .card-item i {
  display: block;
}

.home-ser-card .ser-card .card-item text {
  display: block;
  width: 100%;
  text-align: center;
  font-size: 12px;
}

.home-ser-view {
  width: 100%;
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  padding-bottom: 10px;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
}
  </style>
  