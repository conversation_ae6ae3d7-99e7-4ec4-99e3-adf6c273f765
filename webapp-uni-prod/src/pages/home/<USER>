<template>
  <view class="home-vessel">
    <image class="bg" src="@/static/icons/home/<USER>" mode="aspectFill" />
    <!-- 管理模块 -->
    <view class="cut" @click="cutMod()">
      <SlSvgIcon :name="`home-cut`" size="52" />
    </view>

    <view class="home-function">
      <!-- 搜索栏 -->
      <view class="search">
        <image class="icon-16 search-icon" src="@/static/icons/meeting/14-3.svg" mode="aspectFill" />
        <input type="text" v-model="searchText" placeholder="一站式公务派车" />
        <text>搜索</text>
      </view>

      <!-- 主要功能区 -->
      <view class="main-function">
        <view class="function-row">
          <view v-for="item in mainFunctions" :key="item.label" @click="handleMainClick(item)">
            <SlSvgIcon :name="item.icon" size="27" />
            <text>{{ item.label }}</text>
          </view>
        </view>
      </view>

      <!-- 热门服务 / 我的常用 -->
      <view class="cut-function" :class="{ 'frequent-bg': currentTab === 'frequent' }" v-if="!isMinimalMode">
        <view class="cut-oneRow">
          <text class="tab-item" :class="{ active: currentTab === 'popular' }" @click="switchTab('popular')">
            热门服务
          </text>
          <text class="tab-item" :class="{ active: currentTab === 'frequent' }" @click="switchTab('frequent')">
            我的常用
          </text>
        </view>
        <view class="cut-twoRow">
          <template v-for="(row, rowIndex) in tabData[currentTab]" :key="rowIndex">
            <view :class="`cut-${rowIndex === 0 ? 'one' : 'two'}`">
              <view v-for="item in row" :key="item.label" @click="handleTabClick(item)">
                <SlSvgIcon :name="item.icon" size="20" />
                <text style="margin-left: 6px">{{ item.label }}</text>
              </view>
            </view>
          </template>
        </view>
      </view>
    </view>
  </view>
  <NoticeWarn />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SlSvgIcon from '@/components/SlSVgIcon.vue'
import {
  fullFunctions,
  minimalFunctions,
  tabData,
  navigateTo,ServiceItem
} from '@/utils/navigation'

const searchText = ref('一站式公务派车')
const currentTab = ref<'popular' | 'frequent'>('popular')
const isMinimalMode = ref(false)
const mainFunctions = ref([...fullFunctions])

const switchTab = (type: 'popular' | 'frequent') => {
  currentTab.value = type
}

const cutMod = () => {
  isMinimalMode.value = !isMinimalMode.value
  mainFunctions.value = isMinimalMode.value ? [...minimalFunctions] : [...fullFunctions]
}

const handleTabClick = (item: ServiceItem) => {
  navigateTo(item)
}

const handleMainClick = (item: ServiceItem) => {
  navigateTo(item)
}
</script>

<style lang="scss" scoped>
.home-vessel {
position: relative;
height: 100vh;
background: #f2f4f8;
}

.home-vessel .bg {
width: 100%;
height: 310px;
background-size: cover;
}
.cut{
position: absolute;
right: 0;
top: 81px;
}
.home-vessel .home-function {
position: absolute;
top: 143px;
padding: 0 15px;
width: 100%;
box-sizing: border-box;
}

.home-vessel .home-function .search {
display: flex;
position: relative;
}
.home-vessel .home-function .search .search-icon {
left: 13px;
top: 10px;
z-index: 10;
position: absolute;
}


.home-vessel .home-function .search input {
width: 100%;
height: 36px;
opacity: 1;
border-radius: 20px;
background: #2034497f;
border: 1px solid #ffffff;
backdrop-filter: blur(10px);
color: #ffffff;
outline: none;
padding: 0 34px;
margin-bottom: 8px;
font-size: 14px;
}

.home-vessel .home-function .search text {
position: absolute;
right: 8px;
width: 35px;
height: 17px;
line-height: 17px;
border-left: 2px solid #9c9687;
text-align: center;
color: #ffffff;
font-size: 12px;
font-weight: bold;
top: 10px;
}

.home-vessel .home-function .main-function {
padding: 15px;
//   width: 100%;
min-height: 146px;
opacity: 1;
border-radius: 10px;
background: #ffffff;
box-shadow: 0px 0px 10px 0px #92929219;
font-size: 12px;
color: #3b465d;
margin-bottom: 10px;
width: 100%;
  box-sizing: border-box;
}
.main-function text{
  width: 100%;
}
.home-vessel .home-function .main-function .one-row {
margin-bottom: 23px;
}

.home-vessel .cut-function {
//   width: 100%;
height: 227px;
//   height: 169px;
background-image: url("/static/icons/home/<USER>");
background-position: center;
background-repeat: no-repeat;
//   background-size: cover;
//   background-size: 110% 113%;
background-size: 113% 149%;
margin-bottom: 10px;
}

.home-vessel .cut-function.frequent-bg {
background-image: url("/static/icons/home/<USER>");
}

.home-vessel .cut-function .cut-oneRow {
width: 100%;
box-sizing: border-box;
//   height: 38px;
height: 60px;
padding: 10px 15px;
display: flex;
}

.home-vessel .cut-function .cut-oneRow .tab-item {
font-size: 14px;
color: #3b465dcc;
position: relative;
cursor: pointer;
}

.home-vessel .cut-function .cut-oneRow .tab-item:nth-child(1) {
//   width: 74px;
width: 34%;
text-align: center;
line-height: 55px;
height: 55px;
}

.home-vessel .cut-function .cut-oneRow .tab-item:nth-child(2) {
line-height: 55px;
height: 55px;
width: 185px;
text-align: left;
//   margin-left: 42px;
 padding-left: 42px;
}

.home-vessel .cut-function .cut-oneRow .tab-item.active {
font-weight: bold;
color: #3b465d;
}

.home-vessel .cut-function .cut-oneRow .tab-item.active::after {
content: "";
position: absolute;
bottom: 0;
left: 50%;
transform: translateX(-50%);
width: 20px;
height: 2px;
background-color: #005ac5;
bottom: 8px;
}
.home-vessel .cut-function .cut-oneRow .tab-item:nth-child(2).active::after {
content: "";
position: absolute;
bottom: 0;
left: 30%;
transform: translateX(-50%);
width: 20px;
height: 2px;
background-color: #005ac5;
bottom: 8px;
}

.home-vessel .cut-function .cut-twoRow {
//   width: 100%;
height: 123px;
margin-top: 4px;
padding: 0 15px;
}

.home-vessel .cut-function .cut-twoRow .cut-one,
.home-vessel .cut-function .cut-twoRow .cut-two {
//   width: 100%;
//   height: 40px;
display: flex;
justify-content: space-between;
margin-top: 15px;
}

.home-vessel .cut-function .cut-twoRow .cut-one view,
.home-vessel .cut-function .cut-twoRow .cut-two view {
width: 125px;
//   height: 100%;
opacity: 1;
border-radius: 8px;
background: #f6f8fd;
border: 1px solid #2e67e20c;
box-shadow: 0px 0px 5px 0px #e0e2e84c;
display: flex;
padding: 12px;
}

// .home-vessel .cut-function .cut-twoRow .cut-one view text,
// .home-vessel .cut-function .cut-twoRow .cut-two view text {
//   display: block;
//   width: 20px;
//   height: 20px;
//   background-size: cover;
//   background-repeat: no-repeat;
//   margin-right: 6px;
// }

.home-vessel .cut-function .cut-twoRow .cut-one view text,
.home-vessel .cut-function .cut-twoRow .cut-two view text {
color: #3b465d;
font-size: 14px;
line-height: 26px;
}

.home-vessel .home-footer {
width: 100%;
position: absolute;
bottom: 0;
height: 49px;
background: #ffffffef;
backdrop-filter: blur(20px);
border-radius: 10px 10px 0 0;
box-shadow: 0 -0.5px 6px 0 #5d5d5d33;
display: flex;
justify-content: space-around;
align-items: center;
}

.home-vessel .home-footer view {
width: 57px;
height: 41px;
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
position: relative;
}

.home-vessel .home-footer view .i-footer {
display: block;
width: 22px;
height: 22px;
position: absolute;
top: 0;
background-size: cover;
background-repeat: no-repeat;
background-position: center;
}

.home-vessel .home-footer view text {
color: #666;
font-size: 10px;
width: 100%;
text-align: center;
position: absolute;
bottom: 0;
}

.home-vessel .home-footer view.active text {
color: #005ac5;
}

.home-vessel .home-footer view.active .i-footer {
width: 48px;
height: 48px;
top: -17px;
}

/* 默认图标 */
.home-vessel .home-footer view:nth-child(1) .i-footer {
background-image: url("/static/icons/home/<USER>");
}

.home-vessel .home-footer view:nth-child(2) .i-footer {
background-image: url("/static/icons/home/<USER>");
}

.home-vessel .home-footer view:nth-child(3) .i-footer {
background-image: url("/static/icons/home/<USER>");
}

/* 激活状态图标 */
.home-vessel .home-footer view:nth-child(1).active .i-footer {
background-image: url("/static/icons/home/<USER>");
}

.home-vessel .home-footer view:nth-child(2).active .i-footer {
background-image: url("/static/icons/home/<USER>");
}

.home-vessel .home-footer view:nth-child(3).active .i-footer {
background-image: url("/static/icons/home/<USER>");
}
.icon-16 {
width: 16px;
height: 16px;
}
.home-vessel .home-function .main-function .function-row {
display: flex;
flex-wrap: wrap;
}

.home-vessel .home-function .main-function .function-row view {
width: 25%; /* 4 items per row */
// height: 47px;
margin-bottom: 23px;
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
}

.home-vessel .home-function .main-function .function-row view i {
display: block;
width: 27px;
height: 27px;
background-size: cover;
}

.home-vessel .home-function .main-function .function-row view text {
display: block;
font-size: 14px;
text-align: center;
margin-top: 4px;
}
</style>