<template>
    <div class="notice-list-container" v-show="showModal">
      <div class="notice-inner">
        <div class="notice-warn-container">
          <SlSVgIcon
            name="16-16-10"
            size="16"
            class="close-icon"
            @click="closeModal"
          />
          <div class="title">{{ noticeData.title }}</div>
          <div class="notice-content">
            <div class="notice-title">{{ userName }}您好:</div>
            <span class="notice-time" v-if="noticeData.showTime">{{ currentTime }}</span>
            <span class="notice-content-text">{{ noticeData.content }}</span>
            <!-- 理发通知操作 -->
            <div class="notice-operation" v-if="noticeData.type === 'haircut'">
              <span class="notice-operation-title">您的选择：</span>
              <div class="notification-options">
                <div
                  v-for="option in haircutOptions"
                  :key="option.value"
                  @click="setNotificationType(option.value)"
                  class="notification-option"
                >
                  <SlSVgIcon
                    :name="selectedNotification === option.value 
                      ? 'components-selected' 
                      : 'components-unselected'"
                    size="16"
                  />
                  {{ option.label }}
  
                  <!-- 时间选择器仅在选中“稍后到店”时显示 -->
                  <div
                    v-if="option.value === 'later' && selectedNotification === 'later'"
                    class="small-div"
                  >
                    <span class="small-text">时间：</span>
                    <SLSelect
                      class="select-item"
                      v-model="selectedTime"
                      :options="timeOptions"
                      placeholder="请选择时间"
                      icon-name="components-12-7"
                      icon-size="14"
                      :rotate-on-open="true"
                    />
                  </div>
                </div>
              </div>
            </div>
  
            <div class="footer-btn">
              <BaseButton
                btn-type="save"
                @click="handleButtonClick"
                :btnStyle="{ width: '100%' }"
              >
                {{ noticeData.buttonText }}
              </BaseButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  
  <script lang="ts" setup>
  interface UserInfo {
  id: string;
  name: string;
  phone: string;
}
  import { ref, computed,onMounted } from 'vue';
  import SlSVgIcon from "@/components/SlSVgIcon.vue";
  import homeService from "@/service/home.service";
  import { usePrincipalStore } from "@/store";
  const principalStore = usePrincipalStore();
  const userInfo = ref<UserInfo>({
  id: "",
  name: "",
  phone: "",
});
  // 通知类型定义
  type NoticeType = 'haircut' | 'cancel' | 'menu' | 'delivery';//理发通知 | 理发取消 | 菜谱 | 外卖
  type NotificationChoice = 'immediate' | 'later' | 'cancel';
  
  interface NoticeConfig {
    type: NoticeType;
    title: string;
    content: string;
    buttonText: string;
    showTime: boolean;
    action: 'submit' | 'navigate';
  }
  
  const props = defineProps({
    userName: {
      type: String,
      default: '小明'
    }
  });
  
  const showModal = ref(true);
  const selectedTime = ref<any>(null);
  const selectedNotification = ref<NotificationChoice>('immediate');
  const currentNoticeType = ref<NoticeType>('haircut');
  
  const currentTime = computed(() => {
    const now = new Date();
    return `${now.getFullYear()}.${(now.getMonth() + 1).toString().padStart(2, '0')}.${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
  });
  
  const timeOptions = ref([
    { label: "15 分钟", value: 15 },
    { label: "30 分钟", value: 30 },
    { label: "46 分钟", value: 46 },
    { label: "60 分钟", value: 60 },
  ]);
  
  const haircutOptions = ref([
    { value: 'immediate', label: '立即到店' },
    { value: 'later', label: '稍后到店' },
    { value: 'cancel', label: '取消理发' }
  ]);
  
  const noticeConfigs: Record<NoticeType, NoticeConfig> = {
    haircut: {
      type: 'haircut',
      title: '理发通知',
      content: '系统检测到您预约了今天的理发，当前理发师已空闲，邀请您尽快前往理发店理发，感谢您的配合!',
      buttonText: '确定',
      showTime: true,
      action: 'submit'
    },
    cancel: {
      type: 'cancel',
      title: '理发取消通知',
      content: '由于理发师临时有其他安排，您预约的2025/06/02 9:00~9:30的理发已被店家取消，您可以预约其他空闲时间，感谢您的理解。',
      buttonText: '好的，我知道了',
      showTime: false,
      action: 'navigate'
    },
    menu: {
      type: 'menu',
      title: '每周菜谱通知',
      content: '下周的「菜谱盲盒」已解锁，快来查收吧!',
      buttonText: '立即前往',
      showTime: false,
      action: 'navigate'
    },
    delivery: {
      type: 'delivery',
      title: '外卖订餐通知',
      content: '明天的外卖面食上新啦，快来订餐吧!',
      buttonText: '立即前往',
      showTime: false,
      action: 'navigate'
    }
  };
  
  const noticeData = computed(() => noticeConfigs[currentNoticeType.value]);
  
  function setNotificationType(type: any) {
    selectedNotification.value = type;
  }
  
  function closeModal() {
    showModal.value = false;
  }
  
  function handleButtonClick() {
    if (noticeData.value.action === 'submit') {
      submitHaircutChoice();
    } else {
      navigateToPage();
    }
  }
  
  function submitHaircutChoice() {
    const data = {
      choice: selectedNotification.value,
      time: selectedNotification.value === 'later' ? selectedTime.value : null,
    };
    console.log('提交理发选择数据:', data);
    closeModal();
  }
  
  function navigateToPage() {
    console.log('跳转页面类型:', currentNoticeType.value);
    // uni.navigateTo({ url: getPageUrl() });
    closeModal();
  }
  
  function getPageUrl(): string {
    switch (currentNoticeType.value) {
      case 'cancel': return '/pages/haircut/reschedule';
      case 'menu': return '/pages/menu/weekly';
      case 'delivery': return '/pages/delivery/new';
      default: return '/';
    }
  }

  onMounted(()=>{
    principalStore.identity().then((res: any) => {
    userInfo.value = res;
    console.log(res,'resresresresresresresresresres');
    innitData()
  });

  })
  // 初始化
  const innitData = async()=>{
    const data = {
      userId:userInfo.value.id,
      module:'menu'
    }
    try {
     const currentData:any = await homeService.getModuleNotice(data)
     if(currentData){
      notificationData.value = currentData[0]
     }
    //  console.log(current,'通知信息');
     
    } catch (error) {
      
    }
  }
  interface NotificationData {
  content: string;
  extra: string;
  id: string;
  module: string;
  sysCreated: string;
  sysDeleted: number;
  title: string;
  type:  string; 
}
const notificationData = ref<NotificationData>({
  content: '',
  extra: '',
  id: '',
  module: '',
  sysCreated: '',
  sysDeleted: 0,
  title: '',
  type: '',
});
  </script>
  
  
  <style scoped lang="scss">
  .notice-list-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 10;
  
    .notice-inner {
      position: absolute;
      width: 290px;
      height: 400px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
  
      .notice-warn-container {
        padding: 15px 15px 0 15px;
        width: 100%;
        height: 100%;
        border-radius: 10px;
        background: linear-gradient(180deg, #d8e2ff 0%, #fff 20%, #ffffff 100%);
        box-shadow: 0px 0px 10px 0px #9292924c;
        display: flex;
        flex-direction: column;
        position: relative;
  
        .title {
          color: #333333;
          font-weight: bold;
          font-size: 16px;
          text-align: center;
          margin-bottom: 2px;
        }
  
        .notice-content {
          font-size: 14px;
  
          .notice-title {
            color: #333333;
            font-weight: regular;
          }
  
          .notice-time {
            color: #999999;
            font-size: 12px;
            display: block;
            margin-top: 2px;
          }
  
          .notice-content-text {
            color: #666666;
            margin-top: 10px;
            display: block;
          }
  
          .notice-operation {
            margin-top: 13px;
            margin-bottom: 21px;
  
            .notice-operation-title {
              color: #333333;
              font-weight: bold;
            }
  
            .notification-options {
              margin-top: 15px;
              text-align: center;
  
              .notification-option {
                margin-bottom: 12px;
              }
            }
          }
        }
      }
    }
  
    .close-icon {
      position: absolute;
      top: 18px;
      right: 15px;
      cursor: pointer;
    }
  
    .footer-btn {
      width: 260px;
      height: 40px;
      position: absolute;
      bottom: 35px;
    }
  }
  
  .small-div {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 8px;
    font-size: 12px;
    // margin-left: 50px;
    .small-text {
      font-size: 12px;
    }
  
    .select-item {
      height: 26px;
      width: 120px;
    }
  }
  </style>
  