<template>
  <CustomTabBar currentPath="/pages/home/<USER>">
    <view class="home-vessel">
      <image class="bg" :src="appStore.getBaseUrl + '/api/Content/getCurrentSolarTermImage'" mode="aspectFill" />
      <!-- 管理模块 -->
      <view class="cut" @click="cutMod()" v-if="showPrivilege && finalPermissionList.length > 1">
        <SlSvgIcon name="52-52-1" size="52" />
      </view>
      <view class="home-function">
        <!-- 搜索栏 -->
        <view class="search">
          <SlSvgIcon name="14-14-3" size="16" class="search-icon" @click="searchClick" />
          <input type="text" v-model="searchText" placeholder="一站式公务派车" placeholder-style="color: #A3ABB0" />
          <text @click="searchClick">搜索</text>
        </view>
        <!-- 主要功能区 -->
        <view class="main-function">
          <view class="function-row">
            <view v-for="item in mainFunctions" :key="item.label" @click="handleTabClick(item)">
              <SlSvgIcon :name="item.icon" size="27" />
              <text>{{ item.label }}</text>
            </view>
          </view>
        </view>
        <!-- 热门服务 / 我的常用 -->
        <view class="serviceContainer" v-if="!isMinimalMode">
          <view class="serviceTitle" :class="{ 'frequent-bg': currentTab === 'frequent' }">
            <text class="tab-itemo tab" :class="{ active: currentTab === 'popular' }" @click="switchTab('popular')">
              热门服务
            </text>
            <text class="tab-itemt tab" :class="{ active: currentTab === 'frequent' }" @click="switchTab('frequent')">
              我的常用
            </text>
          </view>
          <!--  -->
          <view class="cut-twoRow">
            <template v-for="(row, rowIndex) in tabData[currentTab]" :key="rowIndex">
              <view :class="`cut-${rowIndex === 0 ? 'one' : 'two'}`">
                <view v-for="item in row" :key="item.label" @click="handleTabClick(item)">
                  <SlSvgIcon :name="item.icon" size="20" style="display: flex" />
                  <text style="margin-left: 6px">{{ item.label }}</text>
                </view>
              </view>
            </template>
          </view>
        </view>
      </view>
    </view>
    <NoticeWarn />
  </CustomTabBar>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import SlSvgIcon from "@/components/SlSVgIcon.vue";
import {
  fullFunctions,
  tabData,
  navigateTo,
  ServiceItem,
  MANAGE_SERVICE,
} from "@/utils/navigation";
import NoticeWarn from "./NoticeWarn.vue";
import CustomTabBar from "@/components/CustomTabBar.vue";
import { Account, Permission } from "@/models/Account";
import { usePrincipalStore, useWxSubStore } from "@/store";
import { useAppStore } from "@/store";
const searchText = ref("一站式公务派车");
const currentTab = ref<"popular" | "frequent">("popular");
const isMinimalMode = ref(false);
const mainFunctions = ref([...fullFunctions]);
const finalPermissionList = ref<Permission[]>([]);
const showPrivilege = ref<boolean>(false);
const appStore = useAppStore();
const switchTab = (type: "popular" | "frequent") => {
  currentTab.value = type;
};
const principalStore = usePrincipalStore();
const cutMod = () => {
  isMinimalMode.value = !isMinimalMode.value;
  mainFunctions.value = isMinimalMode.value
    ? updateFunctionModeByPermission()
    : [...fullFunctions];
};

const searchClick = () => {
  uni.showToast({
    title: "功能正在升级中...",
    icon: "none",
  });
};
const handleTabClick = (item: ServiceItem) => {
  navigateTo(item); //staff or admin
};

// 模拟接口数据
const updateFunctionModeByPermission = () => {
  // 提取权限名称
  const permissionNames = finalPermissionList.value.map((p: any) => p.name);
  const hasDriverPermission = permissionNames.includes("班车发车");
  // 从 MANAGE_SERVICE 中筛选拥有权限的项
  const filtered = Object.values(MANAGE_SERVICE).filter((item) => {
    if (item.label == '通勤班车' && permissionNames.includes(item.label)) {
      return !hasDriverPermission
    }
    return permissionNames.includes(item.label)
  });
  return filtered;
};
const userId = ref<string>("");
const userName = ref<string>("");
const wxSubStore = useWxSubStore();
// 初始化数据
const initData = async () => {
  const account: Account | undefined = await principalStore.identity();
  if (!account) {
    return;
  }
  finalPermissionList.value = account.finalPermissionList;
  // console.log(account, "userInfo.value");
  userId.value = account.id;
  userName.value = account.name;
  showPrivilege.value = finalPermissionList.value.some(
    (item) => item.name === "职工权限"
  ); //如果没有职工权限的话就显示对应的管理
  if (!showPrivilege.value) {
    cutMod();
  }
  /**存储订阅需要的模板id信息 */
  wxSubStore.setTemplates();

};
onMounted(async () => {
  initData();
});
</script>

<style lang="scss" scoped>
.home-vessel {
  position: relative;
  height: 100%;
  background: #f2f4f8;
}

.home-vessel .bg {
  width: 100%;
  height: 310px;
  // background-size: cover;
}

.cut {
  position: absolute;
  right: 0;
  top: 81px;
}

.home-vessel .home-function {
  position: absolute;
  top: 143px;
  padding: 0 15px;
  width: 100%;
  box-sizing: border-box;
}

.home-vessel .home-function .search {
  display: flex;
  position: relative;
}

.home-vessel .home-function .search .search-icon {
  left: 13px;
  top: 10px;
  z-index: 10;
  position: absolute;
}

.home-vessel .home-function .search input {
  width: 100%;
  height: 36px;
  opacity: 1;
  border-radius: 20px;
  background: #2034497f;
  border: 1px solid #ffffff;
  backdrop-filter: blur(10px);
  color: #ffffff;
  outline: none;
  padding: 0 34px;
  margin-bottom: 8px;
  font-size: 14px;
}

.home-vessel .home-function .search text {
  position: absolute;
  right: 8px;
  width: 35px;
  height: 17px;
  line-height: 17px;
  border-left: 2px solid #A3ABB0;
  text-align: center;
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  top: 10px;
}

.home-vessel .home-function .main-function {
  padding: 15px;
  //   width: 100%;
  min-height: 146px;
  opacity: 1;
  border-radius: 10px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px #92929219;
  font-size: 12px;
  color: #3b465d;
  // margin-bottom: 10px;
  width: 100%;
  box-sizing: border-box;
  padding-bottom: 0;
}

.main-function text {
  width: 100%;
}

.home-vessel .home-function .main-function .one-row {
  margin-bottom: 23px;
}

.serviceContainer {
  width: 100%;
  height: 162px;
  border-radius: 10px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px #92929219;
  margin-top: 15px;

  .serviceTitle {
    width: 100%;
    height: 38px;
    background-image: url("@/static/images/home/<USER>");
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    font-size: 14px;
    color: #3b465d;
    display: flex;

    .tab-itemo {
      width: 90px;
    }

    .tab-itemt {
      width: 200px;
      text-align: left !important;
      padding-left: 40px;
    }

    .tab {
      height: 38px;
      line-height: 38px;
      text-align: center;
      display: block;
      position: relative;

      &.active {
        font-weight: bold;
        color: #3b465d;
      }

      &.active::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 14px;
        height: 2px;
        background-color: #005ac5;
        bottom: 0px;
      }

      &:nth-child(2).active::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 30%;
        transform: translateX(-50%);
        width: 14px;
        height: 2px;
        background-color: #005ac5;
        bottom: 0px;
      }
    }
  }

  .frequent-bg {
    background-image: url("@/static/images/home/<USER>");
  }

  .cut-twoRow {
    height: 123px;
    padding: 0 15px;
  }

  .cut-twoRow .cut-one,
  .cut-twoRow .cut-two {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
  }

  .cut-twoRow .cut-one view,
  .cut-twoRow .cut-two view {
    width: 125px;
    height: 40px;
    opacity: 1;
    border-radius: 8px;
    background: #f6f8fd;
    border: 1px solid #2e67e20c;
    box-shadow: 0px 0px 5px 0px #e0e2e84c;
    display: flex;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .cut-one view text,
  .cut-two view text {
    color: #3b465d;
    font-size: 14px;
    line-height: 26px;
  }
}

.home-vessel .home-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  height: 49px;
  background: #ffffffef;
  backdrop-filter: blur(20px);
  border-radius: 10px 10px 0 0;
  box-shadow: 0 -0.5px 6px 0 #5d5d5d33;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.home-vessel .home-footer view {
  width: 57px;
  height: 41px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.home-vessel .home-footer view .i-footer {
  display: block;
  width: 22px;
  height: 22px;
  position: absolute;
  top: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.home-vessel .home-footer view text {
  color: #666;
  font-size: 10px;
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: 0;
}

.home-vessel .home-footer view.active text {
  color: #005ac5;
}

.home-vessel .home-footer view.active .i-footer {
  width: 48px;
  height: 48px;
  top: -17px;
}

.icon-16 {
  width: 16px;
  height: 16px;
}

.home-vessel .home-function .main-function .function-row {
  display: flex;
  flex-wrap: wrap;
}

.home-vessel .home-function .main-function .function-row view {
  width: 25%;
  /* 4 items per row */
  // height: 47px;
  margin-bottom: 23px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // &:nth-last-child(-n+4) {
  //   margin-bottom: 0;
  // }
}

.home-vessel .home-function .main-function .function-row view i {
  display: block;
  width: 27px;
  height: 27px;
  background-size: cover;
}

.home-vessel .home-function .main-function .function-row view text {
  display: block;
  font-size: 14px;
  text-align: center;
  margin-top: 4px;
}
</style>
