<template>
  <div class="notice-list-container" v-show="showModal">
    <div class="notice-inner">
      <div class="notice-warn-container">
        <SlSVgIcon name="16-16-10" size="16" class="close-icon" @click="closeModalPlus" />
        <div class="title">{{ noticeData.title }}</div>

        <div class="notice-content">
          <div class="notice-title">{{ userName}}您好:</div>

          <span v-if="noticeData.showTime && currentNotice?.haircut?.noticeTime" class="notice-time">
            {{ currentNotice.haircut.noticeTime }}
          </span>

          <span class="notice-content-text">{{ noticeData.content }}</span>

          <div v-if="noticeData.type === 'haircut'" class="notice-operation">
            <span class="notice-operation-title">您的选择：</span>
            <div class="notification-options">
              <div
                v-for="option in haircutOptions"
                :key="option.value"
                @click="setNotificationType(option.value)"
                class="notification-option"
              >
                <SlSVgIcon
                  :name="selectedNotification === option.value ? '16-16-9' : '16-16-8'"
                  size="16"
                />
                {{ option.label }}

                <div v-if="option.value === '稍后到店' && selectedNotification === '稍后到店'" class="small-div">
                  <span class="small-text">时间：</span>
                  <SLSelect
                    class="select-item"
                    v-model="selectedTime"
                    :options="timeOptions"
                    placeholder="请选择时间"
                    icon-name="12-12-7"
                    icon-size="14"
                    :rotate-on-open="true"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="footer-btn">
            <BaseButton btn-type="save" @click="handleButtonClick" :btnStyle="{ width: '100%' }">
              {{ noticeData.buttonText }}
            </BaseButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { onShow } from "@dcloudio/uni-app";
import SlSVgIcon from "@/components/SlSVgIcon.vue";
import SLSelect from "@/components/SLSelect.vue";
import BaseButton from "@/components/BaseButton.vue";
import homeService from "@/service/home.service";
import { usePrincipalStore } from "@/store";

interface UserInfo {
  id: string;
  name: string;
  phone: string;
}

// const props = defineProps<{ userName: string; userIds: string }>();

const principalStore = usePrincipalStore();
const userInfo = ref<any>();
const showModal = ref(false);
const selectedTime = ref<any>(null);
const selectedNotification = ref("立即到店");
const currentNoticeType = ref("haircut");
const currentNotice = ref<any>();
  const userName = computed(() => userInfo.value?.name || "");
const timeOptions = ref([
  { label: "15 分钟", value: 15 },
  { label: "30 分钟", value: 30 },
  { label: "46 分钟", value: 46 },
  { label: "60 分钟", value: 60 },
]);

const haircutOptions = ref([
  { value: "立即到店", label: "立即到店" },
  { value: "稍后到店", label: "稍后到店" },
  { value: "取消理发", label: "取消理发" },
]);

const noticeData = computed(() => {
  const type = currentNoticeType.value;
  const cancelContent = (() => {
    const cancel = currentNotice.value?.cancel;
    return cancel?.hairStartTime && cancel?.hairEndTime
      ? `由于理发师临时有其他安排，您预约${formatTimeRange(cancel.hairStartTime, cancel.hairEndTime)}的理发已被店家取消，您可以预约其他空闲时间，感谢您的理解。`
      : "您预约的理发已被取消，感谢您的理解。";
  })();

  return {
    haircut: {
      type,
      title: "理发通知",
      content: "系统检测到您预约了今天的理发，当前理发师已空闲，邀请您尽快前往理发店理发，感谢您的配合!",
      buttonText: "确定",
      showTime: true,
      action: "submit",
    },
    cancel: {
      type,
      title: "理发取消通知",
      content: cancelContent,
      buttonText: "好的，我知道了",
      showTime: false,
      action: "navigate",
    },
    menu: {
      type,
      title: "每周菜谱通知",
      content: "下周的「菜谱盲盒」已解锁，快来查收吧!",
      buttonText: "立即前往",
      showTime: false,
      action: "navigate",
    },
    takeout: {
      type,
      title: "外卖订餐通知",
      content: "明天的外卖面食上新啦，快来订餐吧!",
      buttonText: "立即前往",
      showTime: false,
      action: "navigate",
    },
  }[type] || {};
});

function setNotificationType(type: string) {
  selectedNotification.value = type;
}

function closeModal() {
  showModal.value = false;
  currentNotice.value = {};
  initData();
}

function closeModalPlus() {
  const { menu, takeout } = currentNotice.value;
  if (currentNoticeType.value === "cancel") return hairCancelConfirmH();
  if (["menu", "takeout"].includes(currentNoticeType.value)) {
    updateStatus({ messageId: currentNoticeType.value === "menu" ? menu : takeout, userId: userInfo.value.id });
  } else if (currentNoticeType.value === "haircut") {
    closeModal();
  }
}

function handleButtonClick() {
  const { menu, takeout } = currentNotice.value;
  if (noticeData.value.action === "submit") {
    submitHaircutChoice();
  } else if (["menu", "takeout"].includes(currentNoticeType.value)) {
    updateStatus({ messageId: currentNoticeType.value === "menu" ? menu : takeout, userId: userInfo.value.id }, true);
  } else if (currentNoticeType.value === "cancel") {
    hairCancelConfirmH();
  }
}

async function hairCancelConfirmH() {
  try {
    await homeService.hairCancelConfirm(currentNotice.value.cancel.id);
    closeModal();
  } catch (error) {}
}

async function submitHaircutChoice() {
  const { haircut } = currentNotice.value;
  if (selectedNotification.value === "稍后到店" && !selectedTime.value) {
    return uni.showToast({ title: "请选择到店时间", icon: "none" });
  }

  const data = {
    feedbackContent:
      selectedNotification.value === "稍后到店"
        ? `稍后${selectedTime.value}分钟内到店`
        : selectedNotification.value,
    id: haircut.id,
    reservationId: haircut.reservationId,
  };

  try {
    await homeService.hairConfirm(data);
    closeModal();
  } catch (error) {}
}

function navigateToPage(type: string) {
  const urls: Record<string, string> = {
    menu: "/subpages/cookbook/pages/weekly-menu/index",
    takeout: "/subpages/takeout/pages/staff/index",
  };
  const url = urls[type];
  url ? uni.navigateTo({ url }) : console.warn(`No matching page for type: ${type}`);
}

async function updateStatus(data: any, skip?: boolean) {
  try {
    await homeService.updateStatusMenu(data);
    if (skip) navigateToPage(currentNoticeType.value);
    closeModal();
  } catch (error) {}
}

async function initData() {
  try {
    const res = await homeService.noticeConfirm();
    const simplified:any = {
      haircut: {
        customerName: res.haircut?.customerName || "",
        noticeTime: res.haircut?.hairnotificationList?.[0]?.noticeTime || "",
        id: res.haircut?.hairnotificationList?.[0]?.id || "",
        reservationId: res.haircut?.hairnotificationList?.[0]?.reservationId || "",
      },
      menu: res.menu?.[0]?.id,
      takeout: res.takeout?.[0]?.id,
      cancel: res.haircutDelete,
    };
    currentNotice.value = simplified;

    const found = ["haircut", "menu", "takeout", "cancel"].find((t) => {
      const val = simplified[t];
      return typeof val === "object"
        ? Object.values(val).some((v) => v !== "" && v !== undefined && v !== null)
        : !!val;
    });
    console.log(found,'found');
    
    currentNoticeType.value = found || "haircut";
    showModal.value = !!found;
  } catch (error) {
    console.error("获取通知失败", error);
  }
}

function formatTimeRange(start?: string, end?: string): string {
  if (!start || !end) return "";
  const [date, startTime] = start.split(" ");
  const [, endTime] = end.split(" ");
  return `${date} ${startTime}-${endTime}`;
}

onMounted(async () => {
  // userInfo.value = await principalStore.identity();
});

onShow(async () => {
  userInfo.value = await principalStore.identity();
  if(!userInfo.value)return
  initData();
});
</script>

<style scoped lang="scss">
.notice-list-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10;

  .notice-inner {
    position: absolute;
    width: 290px;
    height: 400px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .notice-warn-container {
      padding: 15px 15px 0;
      width: 100%;
      height: 100%;
      border-radius: 10px;
      background: linear-gradient(180deg, #d8e2ff 0%, #fff 20%, #ffffff 100%);
      box-shadow: 0px 0px 10px 0px #9292924c;
      display: flex;
      flex-direction: column;
      position: relative;
      box-sizing: border-box;

      .title {
        color: #333;
        font-weight: bold;
        font-size: 16px;
        text-align: center;
        margin-bottom: 2px;
      }

      .notice-content {
        font-size: 14px;

        .notice-title {
          color: #333;
        }

        .notice-time {
          color: #999;
          font-size: 12px;
          display: block;
          margin-top: 2px;
        }

        .notice-content-text {
          color: #666;
          margin-top: 10px;
          display: block;
        }

        .notice-operation {
          margin-top: 13px;
          margin-bottom: 21px;

          .notice-operation-title {
            color: #333;
            font-weight: bold;
          }

          .notification-options {
            margin-top: 15px;
            text-align: center;

            .notification-option {
              margin-bottom: 12px;
            }
          }
        }
      }
    }
  }

  .close-icon {
    position: absolute;
    top: 18px;
    right: 15px;
    cursor: pointer;
  }

  .footer-btn {
    width: 260px;
    height: 40px;
    position: absolute;
    bottom: 35px;
  }
}

.small-div {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
  font-size: 12px;

  .small-text {
    font-size: 12px;
  }

  .select-item {
    height: 26px;
    width: 120px;
  }
}
</style>
