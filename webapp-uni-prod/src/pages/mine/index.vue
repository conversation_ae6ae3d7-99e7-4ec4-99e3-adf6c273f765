<template>
    <CustomTabBar currentPath="/pages/mine/index">
        <view class="mine-page">
        <!-- 用户信息卡片 -->
        <view class="card user-card" @click="goProfile">
            <view class="user-info">
                <view class="avatar">{{ initials }}</view>
                <view class="info">
                    <text class="name">{{ account?.name }}</text>
                    <text class="role">{{ userJob }}</text>
                </view>
            </view>
            <SlSVgIcon name="12-12-6" size="12" />
        </view>

        <!-- 退出登录按钮 -->
        <view class="card logout-card" @click="onLogout">
            <SlSVgIcon name="28-28-2" size="28" />
            <text class="logout-text">退出登录</text>
        </view>
    </view>
    </CustomTabBar>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePrincipalStore } from '@/store'
import { usePermission } from '@/hooks'
import { Account } from '@/models/Account'
import CustomTabBar from "@/components/CustomTabBar.vue";
const principalStore = usePrincipalStore()
const accountMock: Account = {
    id: '12345',
    name: '张三',
    position: '前端开发',
    organization: {
        id: '1',
        name: '研发部'
    }
} as Account
const account = computed(() => principalStore.account || accountMock)

onShow(async () => {
    const hasPermission = await usePermission();
    console.log(hasPermission ? '已登录或白名单' : '未登录，拦截跳转');

    setTimeout(() => {
        uni.setTabBarItem({
            index: 2,
            text: hasPermission ? '已登录' : '未登录',
        })
    }, 2 * 1000);

});

// 显示用户name的第一个字
const initials = computed(() => {
    if (!account.value) return ''
    const n = account.value.name
    // 截取第一个字
    return n.length > 0 ? n.charAt(0) : ''
})

// 显示用户部门+职位
const userJob = computed(() => {
    if (account.value) {
        const { organization, position } = account.value
        const orgName = organization?.name || ''
        return orgName + (position ? `、${position}` : '')
    }
    return ''
})


// 点击跳转到个人详情
const goProfile = () => {
    uni.navigateTo({
        url: '/subpages/profile/index'
    })
}

// 退出登录
const onLogout = async () => {
    await principalStore.logout()
    uni.reLaunch({ url: '/pages/login/index' })
}
</script>

<style lang="scss" scoped>
.mine-page {
    padding: 10px 15px;
    height: 100%;
    background-color: #F4F6FA;

    .card {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #ffffff;
        border-radius: 10px;

        padding: 0 15px;

        &.user-card {
            height: 66px;

            .user-info {
                display: flex;
                align-items: center;
                flex: 1;
                padding: 12px 0;
            }

            .avatar {
                width: 44px;
                height: 44px;
                line-height: 44px;
                text-align: center;
                border-radius: 50%;
                background-color: #4F7AF6;
                color: #ffffff;
                font-size: 26px;
                flex-shrink: 0;
            }

            .info {
                flex: 1;
                margin-left: 8px;
                display: flex;
                flex-direction: column;

                .name {
                    color: #333333;
                    font-size: 14px;
                    font-weight: bold;
                }

                .role {
                    margin-top: 4px;
                    font-size: 12px;
                    color: #999999;
                }
            }
        }

        &.logout-card {
            height: 50px;
            justify-content: flex-start;
            margin-top: 15px;

            .logout-text {
                margin-left: 10px;
                font-size: 14px;
            }
        }
    }
}
</style>
