<template>
    <view class="login-page">
        <!--env标识-->
        <view class="env" v-if="env">
            <text class="env-text">{{ env }}</text>
        </view>
        <!-- 背景图 -->
        <image class="bg" src="/static/images/login/login-bg.png" mode="aspectFill" />
        <!-- 顶部标题 -->
        <view class="title">欢迎来到蓝海懃务</view>
        <!--登录方式选择-->
        <up-popup :show="showLoginSelect" mode="bottom" round="18rpx">
            <view class="popup-content">
                <view class="popup-title border-bottom">请选择登录方式</view>
                <button class="popup-option button border-bottom" hover-class="popup-option-active" type="default"
                    open-type="getPhoneNumber" @getphonenumber="getWxPhoneNumber">手机号快捷登录</button>
                <view class="popup-option item" :class="{ 'border-bottom': index != loginOptions.length - 1 }"
                    hover-class="popup-option-active" v-for="(item, index) in loginOptions" :key="item.type"
                    @tap="selectLoginType(item)">{{
                        item.label }}
                </view>
                <view class="popup-option cancel " @tap="cancelLogin()">取消</view>
            </view>
        </up-popup>
        <!-- 底部 口号（包含 Logo 和文字）-->
        <view class="footer">
            <!-- <SlSVgIcon name="50-50-2" class="slogo" size="50"></SlSVgIcon> -->
            <view class="logo-container">
                <image class="slogo" src="/static/images/login/logo.jpg" mode="widthFix" />
            </view>
            <image class="slogan" src="/static/images/login/login-slogan.svg" mode="widthFix" />
        </view>
        <!--自定义toast: 提示用户无权访问小程序；场景：未录入系统的手机号使用快捷方式登录-->
        <up-overlay mask-click-able="false" :show="showUnRegisterTip" opacity="0.1">
            <view class="toast-warp">
                <view class="toast">
                    <SlSVgIcon class="toast-icon" name="30-30-2" size="30"></SlSVgIcon>
                    <text class="toast-tip">您无权登录，请联系</text>
                    <text class="toast-tip">后勤管理中心人员</text>
                </view>
            </view>
        </up-overlay>
        <!-- 隐私政策组件 -->
        <privacy-popup ref="privacyComponent" position="center" @privacy-agreed="privacyAgreedHandle" />
        <!-- 手机号登录入口 -->
        <!-- <view class="login-entry">
            <button class="phone-login button" open-type="getPhoneNumber"
                @getphonenumber="getPhoneNumber($event, redirectUrl)">手机号快捷登录</button>
            <text class="other-login" @tap="showLoginSelect = true; otherType = true">其他号码登录</text>
        </view> -->
    </view>
</template>

<script setup lang="ts">
import { onReady } from '@dcloudio/uni-app';
import { ref } from 'vue'
import { useLogin, LoginType } from "@/composables/useLogin";
import { useAppStore } from '@/store';
const appStore = useAppStore();
// 环境标识
const env = computed(() => appStore.getEnvText)
const { curLoginType, getPhoneNumber } = useLogin();
// 显示登录方式选择弹窗
const showLoginSelect = ref(false)
// 当前登录方式，默认为快速登录
const loginOptions: Array<{
    type: LoginType;
    label: string;
    url?: string; // 可选的跳转链接
}> = [
        { type: 'phone', label: '手机号输入登录', url: '/subpages/login/pages/phone-login/index' },
        { type: 'password', label: '账号密码输入', url: '/subpages/login/pages/pwd-login/index' }
    ];
const redirectUrl = ref<string | undefined>(undefined); // 重定向地址
// 显示未注册提示
const showUnRegisterTip = ref(false);
onLoad((options: any) => {
    // 如果有重定向地址，则设置当前登录方式为密码登录
    if (options.redirect) {
        redirectUrl.value = decodeURIComponent(options.redirect);
    }
});
// 快捷登录获取手机号
const getWxPhoneNumber = async (e: any) => {
    showUnRegisterTip.value = false; // 隐藏未注册提示
    showLoginSelect.value = false; // 隐藏登录方式选择
    curLoginType.value = 'phone'; // 设置当前登录方式为手机号登录
    await getPhoneNumber(e, redirectUrl.value, (error: any) => {
        if (error === '1') {
            // 用户未授权快捷登录方式
        } else {
            showUnRegisterTip.value = true; // 显示未注册提示
        }
        setTimeout(() => {
            showUnRegisterTip.value = false; // 3秒后隐藏提示
            curLoginType.value = undefined; // 清除当前登录方式
            showLoginSelect.value = true; //  重新显示登录方式选择
        }, 3000);
    });
};

const privacyAgreedHandle = () => {
    console.log('隐私政策已同意');
}

onReady(async () => {
    uni.showLoading({
        title: '加载中...',
        mask: true,
        success: () => {
            setTimeout(async () => {
                showLoginSelect.value = true; // 显示登录方式选择
                uni.hideLoading(); // 延时隐藏原生 Loading
            }, 1000);
        },
    });
});

/**
 * 选择登录方式
 * @param type 登录方式 
 */
const selectLoginType = (item: { type: LoginType; url?: string }) => {
    const type = item.type;
    if (!type) return;
    curLoginType.value = type; // 更新当前登录方式
    // 跳转到其他登录方式页面
    if (item.url) {
        uni.navigateTo({
            url: `${item.url}?redirect=${encodeURIComponent(redirectUrl.value || '')}`,
        });
        return;
    }
}
const cancelLogin = () => {
    curLoginType.value = undefined// 清除当前登录方式
    showLoginSelect.value = false

    setTimeout(() => {
        showLoginSelect.value = true; // 重新显示登录方式选择
    }, 1000 * 5);
}
</script>

<style scoped lang="scss">
.login-page {
    position: relative;
    width: 100%;
    height: 100vh;
}

/* 全屏背景 */
.bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 75%;
    /* 减去底部口号高度 */
}

/* 标题 */
.title {
    position: absolute;
    top: 84px;
    width: 100%;
    text-align: center;
    font-size: 30px;
    color: #ffffff;
    z-index: 2;
}

/* 底部口号 */
.footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2;

    .logo-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;

        .slogo {
            display: inline-flex;
            width: 50px;
            height: 50px;
        }
    }


}

.slogan {
    width: 180px;
    height: auto;
    margin-top: 16px;
}

.popup-content {
    background: #f7f7f7;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;

    .popup-title,
    .popup-option {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        font-size: 14px;
        background: #ffffff;



        &.button {
            padding: 0;
            margin: 0;
            background-color: #ffffff;
            color: #333333;
            border-radius: 0;

            &:after {
                border: none;
                border-radius: 0;
            }
        }

        &.popup-option-active {
            color: #2E67E2;
        }
    }

    .border-bottom {
        border-bottom: 1px solid rgba(153, 153, 153, 0.1);
    }

    .popup-title {
        font-weight: medium;
        font-weight: bold;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
    }

    .popup-option {
        &.cancel {
            margin-top: 10px;
        }
    }
}

.login-entry {
    width: 260px;
    box-sizing: border-box;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .phone-login,
    .other-login {
        width: 100%;
        border-radius: 20px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        cursor: pointer;

        &:focus {
            outline: none;
        }
    }

    .phone-login {
        background: linear-gradient(270deg, rgba(71, 95, 255, 1) 0%, rgba(11, 203, 255, 1) 100%);
    }

    .other-login {
        background: rgba(51, 51, 51, 0.6);
        margin-top: 25px;
    }
}

.toast-warp {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .toast {
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: 10px;
        background: rgba(0, 0, 0, 0.65);
        padding: 10px 8px;
        box-sizing: border-box;
        margin-top: -50px;

        .toast-icon {
            display: inline-flex;
        }

        .toast-tip {
            color: #ffffff;
            font-size: 14px;
            padding: 0 10px;
            margin-top: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}

.env {
    position: absolute;
    top: 40px;
    left: 60px;
    z-index: 10;
    background: rgba(231, 75, 75, 0.5);
    border-radius: 4px;
    padding: 2px 6px;
    color: #fff;
    font-size: 12px;
}
</style>
