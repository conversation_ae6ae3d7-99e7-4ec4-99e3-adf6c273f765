<template>
    <view class="login-page">
        <!-- 背景图 -->
        <image class="bg" src="/static/images/login/login-bg.png" mode="aspectFill" />

        <!-- 顶部标题 -->
        <view class="title">欢迎来到勤务通</view>

        <!-- 原生 Loading 蒙层 -->
        <!-- onReady 钩子里会自动 show/hide -->
        <!--登录方式选择-->
        <up-popup :show="show" mode="bottom" round="12rpx">
            <view class="popup-content">
                <view class="popup-title">请选择登录方式</view>
                <view class="popup-option" @tap="goTo('quick')">手机号一键登录</view>
                <view class="popup-option" @tap="goTo('phone')">手机号输入登录</view>
                <view class="popup-option" @tap="goTo('password')">账号密码输入</view>
                <view class="popup-option cancel" @tap="cancelLogin()">取消</view>
            </view>
        </up-popup>
        <!-- 底部 口号（包含 Logo 和文字）-->
        <view class="footer">
            <SlSVgIcon name="login-logo" size="50"></SlSVgIcon>
            <image class="slogan" src="/static/images/login/login-slogan.svg" mode="widthFix" />
        </view>
    </view>
</template>

<script setup lang="ts">
import { onReady } from '@dcloudio/uni-app';
import { ref } from 'vue'
import authService from '@/service/auth'
import { usePrincipalStore } from '@/store';
import { AuthLoginUser } from '@/models/Account';
const principalStore = usePrincipalStore();
onReady(async () => {
    // 页面加载时显示原生 Loading
    uni.showLoading({
        title: '开发时自动登录...',
        mask: true,
    });
    await login(); // 调用登录方法
});

const show = ref(false)
// TODO： 登录逻辑，测试阶段快速登录
const login = async () => {
    try {
        const user: AuthLoginUser = {
            j_username: '***********',
            j_password: 'demo123',
            code: 'demo123',
        }
        // 先校验
        await authService.checkLogin(user.j_username, user.code!)
        // 再认证
        await authService.authenticate(user)
        // 登录成功后获取身份信息
        principalStore.identity(true).then((identity) => {
            uni.hideLoading(); // 隐藏原生 Loading
            // 跳转到首页
            // uni.switchTab({
            //     url: '/pages/home/<USER>'
            // });
            uni.navigateTo({
                url: '/subpages/takeout/pages/admin/index'
            });
        }).catch((error) => {
            console.error('获取身份信息失败:', error);
            uni.hideLoading(); // 隐藏原生 Loading
            show.value = true; // 显示登录方式选择
        });

    } catch (error) {
        console.error('登录失败:', error);
    } finally {
        uni.hideLoading(); // 隐藏原生 Loading
        show.value = true; // 显示登录方式选择
    }
}
const goTo = (type: string) => {
    show.value = false
    uni.showLoading({
        title: '尝试登录...',
        mask: true,
    }).then(async () => {
        await login(); // 调用登录方法
    })

    // let url = ''
    // if (type === 'quick') url = '/pages/login/quick'
    // if (type === 'phone') url = '/pages/login/phone'
    // if (type === 'password') url = '/pages/login/password'
    // uni.navigateTo({ url })
}

const cancelLogin = () => {
    show.value = false
}

</script>

<style scoped lang="scss">
.login-page {
    position: relative;
    width: 100%;
    height: 100vh;
}

/* 全屏背景 */
.bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 75%;
    /* 减去底部口号高度 */
}

/* 标题 */
.title {
    position: absolute;
    top: 84px;
    width: 100%;
    text-align: center;
    font-size: 30px;
    color: #ffffff;
    z-index: 2;
}

/* 底部口号 */
.footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.slogan {
    width: 180px;
    height: auto;
    margin-top: 16px;
}

.popup-content {
    background: #f7f7f7;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;

    .popup-title,
    .popup-option {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        font-size: 14px;
        background: #ffffff;

        &:not(.cancel) {
            border-bottom: 1px solid #999999;
            //TODO: 边框设置不透明度
        }
    }

    .popup-title {
        font-weight: medium;
        font-weight: bold;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
    }

    .popup-option {

        &.cancel {
            margin-top: 10px;
        }
    }
}
</style>
