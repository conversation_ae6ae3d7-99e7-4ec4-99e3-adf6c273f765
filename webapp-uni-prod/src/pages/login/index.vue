<template>
    <view class="login-page">
        <!-- 背景图 -->
        <image class="bg" src="/static/images/login/login-bg.png" mode="aspectFill" />
        <!-- 顶部标题 -->
        <view class="title">欢迎来到勤务通</view>
        <!--登录方式选择-->
        <up-popup :show="showLoginSelect" mode="bottom" round="12rpx">
            <view class="popup-content">
                <view class="popup-title">请选择登录方式</view>
                <button class="popup-option button" v-if="isOtherLoginType" open-type="getPhoneNumber"
                    @getphonenumber="getPhoneNumber">手机号快捷登录</button>
                <view class="popup-option" v-for="item in loginOptions" :key="item.type" @tap="selectLoginType(item)">{{
                    item.label }}
                </view>
                <view class="popup-option cancel" @tap="cancelLogin()">取消</view>
            </view>
        </up-popup>
        <!-- 底部 口号（包含 Logo 和文字）-->
        <view class="footer">
            <SlSVgIcon name="50-50-2" class="slogo" size="50"></SlSVgIcon>
            <image class="slogan" src="/static/images/login/login-slogan.svg" mode="widthFix" />
        </view>
        <!-- 手机号登录入口 -->
        <view class="login-entry">
            <button class="phone-login button" open-type="getPhoneNumber"
                @getphonenumber="getPhoneNumber">手机号快捷登录</button>
            <text class="other-login" @tap="showLoginSelect = true">其他号码登录</text>
        </view>
    </view>
</template>

<script setup lang="ts">
import { onReady } from '@dcloudio/uni-app';
import { ref } from 'vue'
import { useLogin, LoginType } from "@/composables/useLogin";
const { curLoginType, getPhoneNumber } = useLogin();

const isOtherLoginType = computed(() => {
    // 判断当前登录方式是否为其他登录方式（非手机号快捷登录）
    return curLoginType.value === 'phone' || curLoginType.value === 'password';
});
// 显示登录方式选择弹窗
const showLoginSelect = ref(false)

// 当前登录方式，默认为快速登录
const loginOptions: Array<{
    type: LoginType;
    label: string;
    url?: string; // 可选的跳转链接
}> = [
        { type: 'phone', label: '手机号输入登录', url: '/subpages/login/pages/phone-login/index' },
        { type: 'password', label: '账号密码输入', url: '/subpages/login/pages/pwd-login/index' }
    ];

onReady(async () => {
    uni.showLoading({
        title: '加载中...',
        mask: true,
        success: () => {
            setTimeout(async () => {
                showLoginSelect.value = true; // 显示登录方式选择
                uni.hideLoading(); // 延时隐藏原生 Loading
                
                // 开发环境下使用其他方式登录
                if (import.meta.env.VITE_APP_ENV == 'development') {
                    // 设置当前登录方式为密码登录
                    uni.navigateTo({
                        url: '/subpages/login/pages/pwd-login/index'
                    });
                    // 设置当前登录方式为密码登录
                    // uni.navigateTo({
                    //     url: '/subpages/login/pages/phone-login/index'
                    // });
                }
            }, 1000);
        },
    });
});

/**
 * 选择登录方式
 * @param type 登录方式 
 */
const selectLoginType = (item: { type: LoginType; url?: string }) => {
    const type = item.type;
    if (!type) return;
    curLoginType.value = type; // 更新当前登录方式
    // 跳转到其他登录方式页面
    if (item.url) {
        uni.navigateTo({
            url: item.url
        });
        return;
    }
}
const cancelLogin = () => {
    curLoginType.value = undefined// 清除当前登录方式
    showLoginSelect.value = false
}
</script>

<style scoped lang="scss">
.login-page {
    position: relative;
    width: 100%;
    height: 100vh;
}

/* 全屏背景 */
.bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 75%;
    /* 减去底部口号高度 */
}

/* 标题 */
.title {
    position: absolute;
    top: 84px;
    width: 100%;
    text-align: center;
    font-size: 30px;
    color: #ffffff;
    z-index: 2;
}

/* 底部口号 */
.footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2;

    .slogo {
        display: inline-flex;
    }
}

.slogan {
    width: 180px;
    height: auto;
    margin-top: 16px;
}

.popup-content {
    background: #f7f7f7;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;

    .popup-title,
    .popup-option {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        font-size: 14px;
        background: #ffffff;

        &:not(.cancel) {
            border: 1px solid rgba(153, 153, 153, 0.1);
        }

        .button {
            padding: 0;
            margin: 0;
        }
    }

    .popup-title {
        font-weight: medium;
        font-weight: bold;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
    }

    .popup-option {
        &.cancel {
            margin-top: 10px;
        }
    }
}

.login-entry {
    width: 260px;
    box-sizing: border-box;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .phone-login,
    .other-login {
        width: 100%;
        border-radius: 20px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        cursor: pointer;

        &:focus {
            outline: none;
        }
    }

    .phone-login {
        background: linear-gradient(270deg, rgba(71, 95, 255, 1) 0%, rgba(11, 203, 255, 1) 100%);
    }

    .other-login {
        background: rgba(51, 51, 51, 0.6);
        margin-top: 25px;
    }
}
</style>
