<template>
    <div class="worker-handle">
        <div class="content">
            <div class="row">
                <div class="item">
                    <span class="label required">姓名</span>
                    <div class="control-right">
                        <up-input border="none" type="text" placeholder="请输入" v-model="personInfo.name" clearable
                            maxlength="10" :custom-style="{ textAlign: 'right' }" />
                    </div>
                </div>
                <div class="item">
                    <span class="label required">部门</span>
                    <div class="control-right">
                        <up-select v-model:current="personInfo.department" label="请选择部门" :options="departmentOptions" />
                    </div>
                </div>
                <div class="item">
                    <span class="label">工作组</span>
                    <div class="control-right">
                        <up-select v-model:current="personInfo.department" label="请选择所属工作组"
                            :options="workGruopOptions" />
                    </div>
                </div>
                <div class="item">
                    <span class="label">职务</span>
                    <div class="control-right">
                        <!-- <up-select v-model:current="personInfo.department" label="请选择职务" :options="positionOptions" /> -->
                        <PositionSelect :options="positionOptions" />
                    </div>
                </div>
                <div class="item">
                    <span class="label required">手机号码</span>
                    <div class="control-right">
                        <up-input type="text" placeholder="请输入手机号码" v-model="personInfo.phone" clearable maxlength="11"
                            :border="'none'" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="item" style="border-bottom: 1px solid rgba(153, 153, 153, 0.2);height: 40px;">
                    <span class="label required">配置权限</span>
                </div>
                <Permissions :value="personInfo.permissions" @update:selected-ids="personInfo.permissions = $event" />
            </div>
        </div>
        <div class="footer">
            <up-button text="删除" class="btn-cancel-del" @click="showConfirm = true"></up-button>
            <!-- <up-button text="取消" class="btn-cancel" v-else @click="cancel"></up-button> -->
            <up-button text="完成" type="primary" :class="['btn-save']" @click="save"></up-button>
        </div>
    </div>
    <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="del">您确定要删除该职工吗</ConfirmDialog>
</template>

<script setup lang="ts">
import useMessage from '@/hooks/use-message';
import { Personnel } from '@/models/Personnel';
import { isEmpty } from '@/utils';
import { onMounted, ref, toValue } from 'vue';
import Permissions from './Permissions.vue';
import PositionSelect from './position/PositionSelect.vue'
import { options } from 'node_modules/axios/index.cjs';
interface ISelect {
    name: string;
    id: string
}
interface PersonnelItem extends Personnel {
    permissions?: string[]
}
const props = defineProps<{ id?: string }>()
const personInfo = ref<PersonnelItem>({})
const showConfirm = ref(false)
/** 部门列表 */
const departmentOptions = ref<ISelect[]>([])
/** 工作组列表 */
const workGruopOptions = ref<ISelect[]>([])
/** 职务列表 */
const positionOptions = ref<{ name: string; value: string }[]>([])
onMounted(() => {
    console.log("🚀 ~ WorkerHandle.vue ~ onMounted ~ props:", props)
    departmentOptions.value = Array.from({ length: 5 }, (_, i) => ({ name: `部门${i}`, id: `${i}` }))
    if (props.id) {
        uni.setNavigationBarTitle({ title: '编辑职工' })
        getInfo()
    }
})
const getInfo = () => {

}
const message = useMessage()
/** 保存工作组 */
const save = () => {
    console.log("🚀 ~ PersonsDepartment.vue ~ save ~ departmentName.value:", personInfo.value, props.id)
    const validateRequired: Partial<Record<keyof PersonnelItem, string>> = {
        'name': '姓名',
        'department': '部门',
        'phone': '手机号码',
        'permissions': '配置权限'
    }
    let errorMsg = ''
    for (const key in validateRequired) {
        const valueIsEmpty = isEmpty(toValue(personInfo)[key as keyof PersonnelItem])
        if (valueIsEmpty) {
            if (key == 'permissions')
                errorMsg = '请配置权限'
            else
                errorMsg = '请填写' + validateRequired[key as keyof PersonnelItem]
            break;
        }
    }
    if (errorMsg) {
        message.error(errorMsg)
        return
    }
}
const cancel = () => {
    uni.navigateBack()
}
const del = () => {

}
</script>

<style scoped lang="scss">
.label {
    display: inline-block;
    padding-left: 10px;
    color: #333;
}

.required {
    position: relative;

    &::after {
        content: '*';
        color: red;
        position: absolute;
        left: 0;
        top: 0;
    }
}

.row {
    background-color: #fff;
    margin-bottom: 8px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.password-placeholder {
    font-size: 12px;
    color: #999999;
    margin-bottom: 8px;
}

.permissions-wrapper {
    display: flex;
    align-items: center;
    gap: 10px 15px;
    flex-wrap: wrap;
    padding: 10px 0;

    .permissions-item {
        display: flex;
        align-items: center;
        gap: 4px;
    }
}

.item {
    display: flex;
    align-items: center;

    // > :not(.label) {
    //     flex: 1;
    //     display: flex;
    //     justify-content: flex-end;
    // }
}

.footer {
    flex: 1;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    button {
        width: 140px;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        border-radius: 3px;
    }
}

.btn-cancel {
    background: #FFFFFF;
    border: 1px solid #0066DF;
    color: #0066DF;
}

.btn-cancel-del {
    background: #FFFFFF;
    border: 1px solid #999999B2;
    background: #FFFFFF;
    color: #666;

    &:hover {
        border: 1px solid #E50101;
        color: #E50101;
    }
}

.btn-save {
    background: #0066DF;
    border-color: #0066DF;
    color: #fff;

    &.disabled {
        background: #999999;
        border-color: #999;
        pointer-events: none;
    }
}

.control-right {
    flex: 1;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .u-select {
        width: 186px;
    }
}

.worker-handle {
    background: #fdfdfd;
    padding: 15px;
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    z-index: 1;
}

.content {
    flex-grow: 1;
    overflow-y: auto;
}
</style>