<template>
    <div class="persons-department">
        <div class="content">
            <span class="label required">
                部门名称
            </span>
            <!-- <input class="input" type="text" placeholder="请输入部门名称" v-model="departmentName" style="width: 100%;"> -->
            <div class="input-group">
                <up-input type="text" placeholder="请输入部门名称" v-model="departmentName" clearable maxlength="10"
                    :border="'none'" />
            </div>
        </div>
        <div class="footer">
            <up-button text="删除" class="btn-cancel-del" v-if="props.id" @click="del"></up-button>
            <up-button text="取消" class="btn-cancel" v-else @click="cancel"></up-button>
            <up-button text="完成" type="primary" :class="['btn-save', { disabled: !departmentName }]"
                @click="save"></up-button>
            <!-- <button type="primary" :class="props.id ? 'btn-cancel-del' : 'btn-cancel'" @click="cancel">{{
                props.id ? '删除' : '取消' }}</button>
            <button type="primary" :class="props.id ? 'btn-cancel-del' : 'btn-cancel'" @click="cancel">{{
                props.id ? '删除' : '取消' }}</button>
            <button type="primary" :class="['btn-save', { disabled: !departmentName }]" @click="save">完成</button> -->
        </div>
    </div>
</template>

<script setup lang="ts">
import useMessage from '@/hooks/use-message';
import { isEmpty } from '@/utils';
import { ref, watchEffect } from 'vue';
// import { useRouter } from 'vue-router';

const departmentName = ref('')
const props = defineProps<{ id?: string; name?: string }>()
watchEffect(() => {
    console.log(props)
    if (props.name) {
        departmentName.value = props.name
        uni.setNavigationBarTitle({
            title: '编辑部门'
        })
    }

})
const save = () => {
    if (isEmpty(departmentName.value)) {
        message.error('请输入部门名称！')
        return
    }
    console.log("🚀 ~ PersonsDepartment.vue ~ save ~ departmentName.value:", departmentName.value, props.id)
}
// const router = useRouter()
const message = useMessage()
const cancel = () => {
    uni.navigateBack()
    // router.back()
}
const del = () => {
}
</script>

<style scoped lang="scss">
.label {
    display: inline-block;
    padding-left: 10px;
    color: #333;
}

.required {
    position: relative;

    &::after {
        content: '*';
        color: red;
        position: absolute;
        left: 0;
        top: 0;
    }
}

.persons-department {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 15px;
    gap: 10px;
}

.footer {
    flex: 1;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    button {
        width: 140px;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        border-radius: 3px;
    }
}

.btn-cancel {
    background: #FFFFFF;
    border: 1px solid #0066DF;
    color: #0066DF;
}

.btn-cancel-del {
    background: #FFFFFF;
    border: 1px solid #999999B2;
    background: #FFFFFF;
    color: #666;

    &:hover {
        border: 1px solid #E50101;
        color: #E50101;
    }
}

.btn-save {
    background: #0066DF;
    color: #fff;

    &.disabled {
        background: #999999;
    }
}

.input-group {
    background-color: #F8F8F8;
    border-radius: 3px;
    margin-top: 10px;
    height: 41px;
    line-height: 41px;
}

.content {
    flex-grow: 1;
    overflow-y: auto;
}
</style>