<template>
    <div class="persons-wrapper">
        <div class="persons-header">
            <div class="header-item" :class="{ 'active': currentType == 0 }" @click="changeType(0)">
                <span class="label">部门</span>
            </div>
            <div class="header-item" :class="{ 'active': currentType == 1 }" @click="changeType(1)">
                <span class="label">工作组</span>
            </div>
            <div class="header-item" :class="{ 'active': currentType == 2 }" @click="changeType(2)">
                <span class="label">职工</span>
            </div>
        </div>
        <div class="persons-content">
            <!-- 职工 -->
            <template v-if="currentType == 2">
                <up-input type="text" placeholder="请输入姓名" v-model="searchName" clearable />
            </template>
            <div class="persons-content-inner">
                <template v-if="currentType != 2">
                    <div class="content-info-wrapper" v-if="!currentInfoItem?.id">
                        <div class="content-item" v-for="item of list" :key="item.uuid" @click="toInfo(item)">
                            <SlSVgIcon v-if="currentType == 0 || currentType == 1"
                                :name="'persons-34-' + (currentType == 0 ? '11' : '12')" size="34"></SlSVgIcon>
                            <span class="label">{{ item.label }}({{ item.childrens.length }})</span>
                            <SlSVgIcon name="persons-12-1" size="12"></SlSVgIcon>
                        </div>
                    </div>
                    <!-- 详细分组 -->
                    <div v-else class="group-wrapper">
                        <div class="group-title">
                            <div @click="currentInfoItem = null" class="icon-rotate">
                                <SlSVgIcon name="persons-12-6" size="12" />
                            </div>
                            <SlSVgIcon v-if="currentType == 0 || currentType == 1"
                                :name="'persons-34-' + (currentType == 0 ? '11' : '12')" size="34" />
                            <span class="label">{{ currentInfoItem?.label }}({{ currentInfoItem?.childrens.length
                                }})</span>
                        </div>
                        <div class="group-item" v-for="child of currentInfoItem?.childrens" :key="child.uuid">
                            <div class="round">
                                {{ child.label[0] }}</div>
                            <div class="group-item_name">
                                <div style="color: #999;">
                                    {{ child.label }}
                                </div>
                                <div style="color: #333;">
                                    {{ child.orgName || '员工' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <!-- 职工列表 -->
                <template v-else>
                    <div class="group-item" v-for="personnel of personnelList" :key="personnel.id"
                        style="padding-left: 0;" @click="toInfoPersonnel(personnel.id || '')">
                        <div class="round">
                            {{ personnel?.name ? personnel.name[0] : '' }}
                        </div>
                        <div class="group-item_name">
                            <div style="color: #999;">
                                {{ personnel.name }}
                            </div>
                            <div style="color: #333;">
                                {{ personnel.department || '员工' }}
                            </div>
                        </div>
                        <SlSVgIcon name="persons-12-6" size="12" />
                    </div>
                </template>
            </div>
        </div>
        <div class="persons-footer">
            <button class="add-btn" v-if="!currentInfoItem?.id" type="primary" @click="addGroup">新增{{ currentType === 0
                ?
                '部门' :
                currentType === 1
                    ? '工作组' : '职工'
            }}</button>
            <template v-else>
                <!-- 部门 -->
                <button class="add-btn" v-if="currentType == 0" type="primary" @click="editDepartment">编辑部门</button>
                <!-- 工作组 -->
                <template v-else-if="currentType == 1">
                    <div class="group-btn">
                        <div class="group-btn_item" @click="editGroup">
                            编辑工作组
                        </div>
                        <div class="group-btn_item" @click="addGroupPerson">
                            添加组员
                        </div>
                    </div>
                    <!-- <button type="primary">编辑工作组</button>
                    <button type="primary">添加组员</button> -->
                </template>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
/** 0:部门 1:工作组 2:职工 */
type PersonType = 0 | 1 | 2
import { Personnel, PersonnelSearch } from '@/models/Personnel';
import personnelService from '@/service/personnel.service';
import { uuidv4 } from '@/utils/uuid';
import { onMounted, ref, toValue } from 'vue';
// import { useRouter } from 'vue-router';
const currentType = ref<PersonType>(0)
const currentInfoItem = ref<RowItem | null>(null)
const searchName = ref('')
interface RowItem {
    id?: string
    label: string;
    orgName?: string;
    uuid: string;
    childrens: RowItem[]
}
const list = ref<RowItem[]>([])
const personnelList = ref<Personnel[]>([])
// const isInHandle = ref(false)
onMounted(() => {
    getList()
})
const changeType = (type: PersonType) => {
    if (currentType.value == type) return
    currentType.value = type
    // isInHandle.value = false
    getList()
}
const getList = async () => {
    // if (toValue(currentType) == 0) {
    //     await getListDepartment()
    // } else if (toValue(currentType) == 1) {
    //     await getListGroup()
    // } else {
    //     await getListPersons()
    // }
    // if (currentType)
    // personnelService.getList(search).then(res => {
    //     list.value = res?.map((ele) => {
    //         // const label = (currentType.value == 0 ? '部门' : currentType.value == 1 ? '工作组' : '职工') + index
    //         return {
    //             id: ele.id || uuidv4(),
    //             uuid: uuidv4(),
    //             label: ele.groupName,
    //             childrens: ele.members?.map((child) => {
    //                 return {
    //                     id: ele.id || uuidv4(),
    //                     uuid: uuidv4(),
    //                     label: child,
    //                     childrens: []
    //                 }
    //             })
    //         }
    //     }) || []
    // })
    list.value = Array.from({ length: 20 }, (_, index) => {
        const label = (currentType.value == 0 ? '部门' : currentType.value == 1 ? '工作组' : '职工') + index
        return {
            id: uuidv4(),
            uuid: uuidv4(),
            label,
            childrens: Array.from({ length: 10 }, (_, index) => {
                return {
                    uuid: uuidv4(),
                    label: label + '_00_' + index,
                    childrens: []
                }
            })
        }
    })
    personnelList.value = Array.from({ length: 20 }, (_, index) => {
        return {
            id: uuidv4(),
            uuid: uuidv4(),
            name: '00_' + index
        }
    })
}
// const router = useRouter()
const toInfo = (item: RowItem) => {
    currentInfoItem.value = item
    // isInHandle.value = true
    console.log("🚀 ~ index.vue ~ toInfo ~ item:", item)
    console.log("🚀 ~ index.vue ~ toInfo ~ item:", toValue(currentType))

}
const addGroup = () => {
    switch (toValue(currentType)) {
        case 0: // 部门
            // router.push((`/persons/department/handle`))
            break;
        case 1: // 工作组
            // router.push((`/persons/worker/handle`))
            break;
        case 2: // 职员
            // router.push((`/persons/worker/handle/:id?`))
            break;
        default:
            break;
    }
}
const editDepartment = () => {
    const { uuid, label } = toValue(currentInfoItem) || {}
    // router.push((`/persons/department/handle/${uuid}/${label}`))
    uni.navigateTo({ url: `/pages/persons/PersonsDepartment?id=${uuid}&name=${label}` })
}
/** 编辑工作组 */
const editGroup = () => {
    const id = currentInfoItem?.value?.id
    uni.navigateTo({ url: `/pages/persons/worker/WorkerHandle?id=${id}` })
}
/** 添加组员 选择已有的职工加入工作组 */
const addGroupPerson = () => {
    const id = currentInfoItem?.value?.id
    uni.navigateTo({ url: `/pages/persons/worker/WorkerAddPersons?id=${id}` })
}
/** 职工详情 */
const toInfoPersonnel = (id: string) => {
    uni.navigateTo({ url: `/pages/persons/PersonsHandle?id=${id}` })
}
/** 部门列表 */
const getListDepartment = async () => {
    try {
        const res = await personnelService.getListOrganization({ ifPage: false, loadPersonnelList: true })
        list.value = res?.map((ele) => {
            return {
                id: ele.id,
                uuid: uuidv4(),
                label: ele.name || '',
                childrens: ele.personnelList?.map(child => {
                    return {
                        id: child.id,
                        uuid: uuidv4(),
                        label: child.name || '',
                        childrens: []
                    }
                }) || []
            }
        }) || []
        console.log("🚀 ~ index.vue ~ getListDepartment ~ res:", res)
    } catch (error) {

    }
}
/** 工作组列表 */
const getListGroup = async () => {
    try {
        const res = await personnelService.getListWorkGroup({ ifPage: false, loadPersonnelList: true })
        console.log("🚀 ~ index.vue ~ getListDepartment ~ res:", res)
        list.value = res?.map((ele) => {
            return {
                id: ele.id,
                uuid: uuidv4(),
                label: ele.name || '',
                childrens: ele.personnelList?.map(child => {
                    return {
                        id: child.id,
                        uuid: uuidv4(),
                        label: child.name || '',
                        childrens: []
                    }
                }) || []
            }
        }) || []
    } catch (error) {

    }
}
/** 人员列表 */
const getListPersons = async () => {
    const searchPersons = new PersonnelSearch()
    searchPersons.ifGrouped = false
    searchPersons.ifPage = false
    searchPersons.keyword = searchName.value || ''
    try {
        const res = await personnelService.getList(searchPersons) as Personnel[]
        personnelList.value = res || []
    } catch (error) {
        personnelList.value = []
    }
}
</script>

<style scoped lang="scss">
.persons-wrapper {
    padding: 0 15px;
}

.persons-header {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    border-bottom: 1px solid rgba(153, 153, 153, 0.2);
    font-size: 14px;
    font-weight: bold;
    color: #666;

    .header-item {
        height: 40px;
        line-height: 40px;
        display: inline-block;
        width: 33%;
        text-align: center;
        cursor: pointer;
    }

    .header-item.active,
    .header-item:hover {
        color: #333;
    }

    .header-item.active {
        .label {
            position: relative;

            &::after {
                position: absolute;
                content: '';
                bottom: -35%;
                left: 50%;
                width: 50%;
                height: 2px;
                background-color: #4355FC;
                transform: translateX(-50%);
            }
        }
    }
}

.content-item {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    min-height: 50px;
    cursor: pointer;

    &:hover {
        background: rgba(79, 122, 246, 0.1);
    }

    .label {
        display: flex;
        flex: 1;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #333;
        padding: 0 8px;
    }
}

.group-title {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 8px;

    .label {
        display: flex;
        flex: 1;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #333;
    }
}

.group-item {
    padding-left: 50px;
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 50px;

    &:hover {
        background: rgba(79, 122, 246, 0.1);
    }

    &:not(:last-child) {
        margin-bottom: 8px;
    }

    &_name {
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 12px;
        border-bottom: 1px solid #999;
        min-height: 50px;
        flex: 1;
    }
}

.cust-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
}

.persons-footer {
    height: 70px;
    display: flex;
    align-items: center;
    padding: 0 15px;
}

.add-btn {
    width: 100%;
    height: 40px;
    line-height: 40px;
    color: #fff;
    border-radius: 3px;
    background: #0066DF;
}

.content-info-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.persons-content {
    flex: 1;
    height: calc(100vh - 40px - 70px);
    overflow: hidden;
}

.persons-content-inner {
    height: 100%;
    overflow-y: auto;
}

.icon-rotate {
    transform: rotate(180deg);
}

.round {
    width: 34px;
    height: 34px;
    background: #4f7af6;
    border-radius: 17px;
    color: #ffffff;
    font-size: 14px;
    text-align: center;
    line-height: 34px;
}

.group-wrapper {
    padding: 10px 0;
}

.group-btn {
    display: flex;
    align-items: center;
    height: 40px;
    width: 100%;

    &_item {
        width: 50%;
        text-align: center;
        color: #0066DF;

        &:first-child {
            border-right: 1px solid #999;
        }
    }
}
</style>