<template>
    <div class="persons-wrapper">
        <div class="header">
            <up-input type="text" placeholder="请输入姓名/首字母" v-model="keyword" clearable />
        </div>
        <div class="content">
            <div class="personnel-item" v-for="item of list" :key="item.uuid">
                <div class="personnel-item_parent content-item">
                    <div @click="onParentItem(item)" style="display: flex;align-items: center;">
                        <SlSVgIcon class="check-icon"
                            :name="'persons-20-' + (item?.childrens.every(c => checkedIds.has(c.id)) ? 'check' : 'uncheck')" />
                        <SlSVgIcon name="persons-34-11" size="34" />
                    </div>
                    <span class="label">{{ item.label }}({{ item.childrens.length }})</span>
                    <div @click="item.showChild = !item.showChild" :class="item.showChild ? 'icon-rotate' : ''">
                        <SlSVgIcon name="persons-12-6" size="12" />
                    </div>
                </div>
                <div v-show="item.showChild">
                    <div class="personnel-item_child" v-for="child of item?.childrens" :key="child.id"
                        @click="onChildItem(child)">
                        <SlSVgIcon class="check-icon"
                            :name="'persons-20-' + (checkedIds.has(child.id) ? 'check' : 'uncheck')" />
                        <div class="group-item">
                            <div class="round">
                                {{ child.label[0] }}</div>
                            <div class="group-item_name">
                                <div style="color: #999;">
                                    {{ child.label }}
                                </div>
                                <div style="color: #333;">
                                    {{ child.orgName || '员工' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer">
            <up-button text="取消" class="btn-cancel" @click="cancel"></up-button>
            <up-button text="完成" type="primary" :class="['btn-save', { disabled: !checkedIds.size }]"
                @click="save"></up-button>
        </div>
    </div>
</template>

<script setup lang="ts">
// import { Personnel } from '@/models/Personnel';
// import personnelService from '@/service/personnel.service';
import { uuidv4 } from '@/utils/uuid';

interface RowItem {
    id: string
    label: string;
    orgName?: string;
    uuid: string;
    childrens: RowItem[]
    showChild?: boolean
}
const props = defineProps(['id'])
const list = ref<RowItem[]>([])
const checkedIds = ref<Set<string>>(new Set())
const keyword = ref('')
const cancel = () => {
    uni.navigateBack()
 }
const save = () => { }
onMounted(() => {
    console.log(props, 'props')
    getList()
})
const getList = () => {
    // personnelService.getListOrganization({ ifPage: false, keyword: toValue(keyword), loadPersonnelList: true }).then(res => {
    //     list.value = res?.map((ele) => {
    //         return {
    //             id: ele.id || uuidv4(),
    //             uuid: uuidv4(),
    //             label: ele.name || '',
    //             childrens: ele.personnelList?.map(child => {
    //                 return {
    //                     id: child.id || uuidv4(),
    //                     uuid: uuidv4(),
    //                     label: child.name || '',
    //                     childrens: []
    //                 }
    //             }) || []
    //         }
    //     }) || []
    // })
    list.value = Array.from({ length: 20 }, (_, index) => {
        const label = '工作组' + index
        return {
            id: uuidv4(),
            uuid: uuidv4(),
            label,
            childrens: Array.from({ length: 10 }, (_, index) => {
                return {
                    id: uuidv4(),
                    uuid: uuidv4(),
                    label: label + '_00_' + index,
                    childrens: []
                }
            })
        }
    })
}
const onParentItem = (item: RowItem) => {
    const { childrens = [] } = item
    const childAllCheck = childrens.every(c => checkedIds.value.has(c.id))
    let event = !childAllCheck
    if (event) {
        childrens.forEach(c => checkedIds.value.add(c.id))
    } else {
        childrens.forEach(c => checkedIds.value.delete(c.id))
    }
}
const onChildItem = (item: RowItem) => {
    const { id } = item
    if (!id) return;
    checkedIds.value.has(id) ? checkedIds.value.delete(id) : checkedIds.value.add(id)
}
</script>

<style scoped lang="scss">
// @import url("../persons-base.scss");
.persons-wrapper {
    background: #fdfdfd;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.content {
    flex: 1;
    overflow-y: auto;
}

.footer {
    padding: 15px;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    button {
        width: 140px;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        border-radius: 3px;
    }
}

.btn-cancel {
    background: #ffffff;
    border: 1px solid #0066df;
    color: #0066df;
}

.btn-cancel-del {
    background: #ffffff;
    border: 1px solid #999;
    background: #ffffff;
    color: #666;

    &:hover {
        border: 1px solid #e50101;
        color: #e50101;
    }
}


.btn-save {
    background: #0066df;
    border-color: #0066df;
    color: #fff;

    &.disabled {
        background: #999999;
        border-color: #999;
        pointer-events: none;
    }
}


.header {
    height: 51px;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 15px;
    background-color: #0653AF;

    .u-input {
        height: 30px;
        line-height: 30px;
        background-color: #fff;
        text-align: right;
    }
}

.group-item {
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 50px;
    margin-bottom: 8px;
    flex: 1;


    &_name {
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 12px;
        border-bottom: 1px solid #999;
        min-height: 50px;
        flex: 1;
    }
}

.round {
    width: 34px;
    height: 34px;
    background: #4f7af6;
    border-radius: 17px;
    color: #ffffff;
    font-size: 14px;
    text-align: center;
    line-height: 34px;
}

.check-icon {
    margin-right: 15px;
}

.icon-rotate {
    transform: rotate(90deg);
}

.personnel-item {
    display: flex;
    flex-direction: column;
    padding: 0 15px;

    &_parent,
    &_child {
        height: 50px;
        display: flex;
        align-items: center;

        &:hover {
            background: rgba(79, 122, 246, 0.1);
        }
    }
}

// .content {
//     padding: 0;
// }

.content-item {
    display: flex;
    align-items: center;
    cursor: pointer;

    &:hover {
        background: rgba(79, 122, 246, 0.1);
    }

    .label {
        display: flex;
        flex: 1;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #333;
        padding: 0 8px;
    }
}
</style>