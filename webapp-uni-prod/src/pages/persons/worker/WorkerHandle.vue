<template>
    <div class="worker-handle">
        <div class="content">
            <div class="row">
                <div class="item">
                    <span class="label required">工作组名称</span>
                    <div class="control-right">
                        <up-input border="none" type="text" placeholder="请输入" v-model="departInfo.name" clearable
                            maxlength="10" :custom-style="{ textAlign: 'right' }" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="item">
                    <span class="label">设置账密</span>
                    <div class="control-right">
                        <up-switch v-model="departInfo.isSetPassword" />
                    </div>
                </div>
                <template v-if="departInfo.isSetPassword">
                    <div class="item">
                        <span class="label required">账号</span>
                        <div class="control-right">
                            <up-input border="none" type="text" placeholder="请输入" v-model="departInfo.account" clearable
                                maxlength="10" :custom-style="{ textAlign: 'right' }" />
                        </div>
                    </div>
                    <div class="item">
                        <span class="label required">密码</span>
                        <div class="control-right">
                            <up-input border="none" type="text" placeholder="请输入" v-model="departInfo.password"
                                clearable :custom-style="{ textAlign: 'right' }" />
                        </div>
                    </div>
                    <div class="password-placeholder">
                        密码为英文大写字母、小写字母、特殊字符、数字中的至少三种组合长度为8-20位
                    </div>
                </template>
            </div>
            <div class="row">
                <div class="item" style="border-bottom: 1px solid rgba(153, 153, 153, 0.2);height: 40px;">
                    <span class="label required">配置权限</span>
                </div>
                <Permissions :value="departInfo.permissions" @update:selected-ids="departInfo.permissions = $event" />
            </div>
        </div>
        <div class="footer">
            <up-button text="删除" class="btn-cancel-del" v-if="!props.id" @click="showConfirm = true"></up-button>
            <up-button text="取消" class="btn-cancel" v-else @click="cancel"></up-button>
            <up-button text="完成" type="primary" :class="['btn-save', { disabled: !isCanSave }]"
                @click="save"></up-button>
        </div>
    </div>
    <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="del">您确定要删除该工作组吗</ConfirmDialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, toValue } from 'vue';
import Permissions from '../Permissions.vue';
import { isEmpty } from '@/utils';
import useMessage from '@/hooks/use-message';
class WorkerHandle {
    id?: string
    name?: string
    account?: string
    password?: string
    /** 权限 */
    permissions?: string[]
    /** 是否设置账密 */
    isSetPassword?: boolean = true
}
const props = defineProps<{ id?: string }>()
const departInfo = ref<WorkerHandle>(new WorkerHandle())
const showConfirm = ref(false)
onMounted(() => {
    console.log("🚀 ~ WorkerHandle.vue ~ onMounted ~ props:", props)
    if (props.id) {
        getInfo()
    }
})
const getInfo = () => {

}
/** 密码够8位数时按钮可点击 */
const isCanSave = computed(() => {
    const { password = '' } = toValue(departInfo);
    return password && password?.length > 8
})
const message = useMessage()
/** 保存工作组 */
const save = () => {
    console.log("🚀 ~ PersonsDepartment.vue ~ save ~ departmentName.value:", departInfo.value, props.id)
    const { password = '', permissions } = toValue(departInfo) || {}
    const validateRequired: Partial<Record<keyof WorkerHandle, string>> = {
        'name': '部门名称',
        'account': '账号',
        'password': '密码',
        'permissions': '权限'
    }
    for (const key in validateRequired) {
        const valueIsEmpty = isEmpty(departInfo.value[key as keyof WorkerHandle])
        if (valueIsEmpty) {
            if (key == 'permissions') {
                message.error('请选择配置权限')
            } else
                message.error('请填写' + validateRequired[key as keyof WorkerHandle])
            break;
        }
    }

    // 校验密码格式
    if (password && (password.length < 8 || password.length > 20)) {
        message.error('密码格式错误')
        // message.error('密码格式错误，请重试~')
        return
    }
    const types = [
        /[A-Z]/.test(password),  // 大写字母
        /[a-z]/.test(password),  // 小写字母
        /\d/.test(password),     // 数字
        /[#?!@$%^&*_-]/.test(password)  // 特殊符号
    ];
    const len = types.filter(Boolean).length
    if (len < 3) {
        message.error('密码格式错误')
        // message.error('密码格式错误，请重试~')
        return
    }
    if (!permissions?.length) {
        return new Error('请配置权限')
    }
}
// const router = useRouter()
const cancel = () => {
    // router.back()
    uni.navigateBack()
}
const del = () => {

}
</script>

<style scoped lang="scss">
.label {
    display: inline-block;
    padding-left: 10px;
    color: #333;
}

.required {
    position: relative;

    &::after {
        content: '*';
        color: red;
        position: absolute;
        left: 0;
        top: 0;
    }
}

.row {
    background-color: #fff;
    margin-bottom: 8px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.password-placeholder {
    font-size: 12px;
    color: #999999;
    margin-bottom: 8px;
}

.permissions-wrapper {
    display: flex;
    align-items: center;
    gap: 10px 15px;
    flex-wrap: wrap;
    padding: 10px 0;

    .permissions-item {
        display: flex;
        align-items: center;
        gap: 4px;
    }
}

.item {
    display: flex;
    align-items: center;

    // > :not(.label) {
    //     flex: 1;
    //     display: flex;
    //     justify-content: flex-end;
    // }
}

.footer {
    flex: 1;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    button {
        width: 140px;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        border-radius: 3px;
    }
}

.btn-cancel {
    background: #FFFFFF;
    border: 1px solid #0066DF;
    color: #0066DF;
}

.btn-cancel-del {
    background: #FFFFFF;
    border: 1px solid #999999B2;
    background: #FFFFFF;
    color: #666;

    &:hover {
        border: 1px solid #E50101;
        color: #E50101;
    }
}

.btn-save {
    background: #0066DF;
    border-color: #0066DF;
    color: #fff;

    &.disabled {
        background: #999999;
        border-color: #999;
        pointer-events: none;
    }
}

.control-right {
    flex: 1;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.worker-handle {
    background: #fdfdfd;
    padding: 15px;
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.content {
    flex-grow: 1;
    overflow-y: auto;
}
</style>