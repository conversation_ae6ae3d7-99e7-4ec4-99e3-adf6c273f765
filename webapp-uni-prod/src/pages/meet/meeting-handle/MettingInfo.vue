<template>
    <div class="meeting-info-main">
        <div class="info-container"
            style="display: flex;flex-direction: column;background-color: #fff;padding-bottom: 0;">
            <div class="info-time">
                <svg class="icon-12">
                    <use xlink:href="#icon-meeting-12-7"></use>
                </svg>
                <span> {{ meetingInfo.timeRange }}</span>
                <span class="flex-end " :class="'meeting-status-' + meetingInfo.status">
                    {{ getMeetingStatusText }}
                </span>
            </div>
        </div>
        <div class="info-container" style="display: flex;flex-direction: column;background-color: #fff;">
            <div class="meeting-info_title">{{ meetingInfo.title }}</div>
            <div class="info-container-item"
                :style="{ 'align-items': meetingInfo.content?.split('\n').length && meetingInfo.content?.split('\n').length > 1 ? 'flex-start' : 'center' }">
                <span class="label" style="margin-top: 0;">内容</span>
                <div class="content" style="color: #999">
                    <p v-for="item of meetingInfo.content?.split('\n')" :key="item">
                        {{ item }}
                    </p>
                </div>
            </div>
            <div class="info-container-item">
                <span class="label">附件</span>
                <span class="content" style="flex:1;">
                    <div v-if="meetingInfo?.contentList?.length" class="content-file-box">
                        <div class="content-file-item" v-for="(item, index) in meetingInfo.contentList" :key="index"
                            @click="previewFile(item)">
                            <div>
                                <svg class="icon-22">
                                    <use :xlink:href="'#icon-meeting-22-' + item.fileType?.substring(1)"></use>
                                </svg>
                            </div>
                            <div>
                                <div class="file-name">{{ item.fullName }}</div>
                                <div class="file-size">{{ convertBytes(item.fileSize!) }}</div>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
        </div>
        <div class="meeting-info" style="margin-top: 0;">
            <span class="label">发起人</span>
            <div class="content">
                <div class="circle circle-blue">{{ meetingInfo.creatorName?.substring(0, 1) }}</div>
                <span style="color: #999;">
                    {{ meetingInfo.creatorName }}
                </span>
            </div>
        </div>
        <!-- 参会人员 -->
        <div class="meeting-info info-participant" style="margin-bottom: 0">
            <span class="label require">参会人员({{ getAttendeeCount }})</span>
            <span style="color: #999;" class="flex-end" @click="handleSeeAttendeeDetail">
                参与详情
                <svg class="icon-12">
                    <use xlink:href="#icon-meeting-12-1"></use>
                </svg>
            </span>
        </div>
        <div class="participant-box">
            <template v-for="(item, idx) of getAttendeeList" :key="item.id">
                <!-- 详情这里只显示已确定参会人员，超过十个不显示 -->
                <!-- <div class="participant-item" v-if="item.feedback == 1"> -->
                <div class="participant-item" v-if="(idx < 10)">
                    <div class="circle circle-large"
                        :class="{ 'circle-blue': item.personnel?.type == 0, 'circle-out-icon': item.personnel?.type == 1 }">
                        {{ item.personnel?.name?.substring(0, 1) }}</div>
                    <span class="participant-name"> {{ item.personnel?.name }}</span>
                </div>
            </template>
        </div>
        <!-- 会议室 -->
        <div class="meeting-info" style="margin-bottom: 0;">
            <span class="label require">会议室</span>
        </div>
        <div class="meeting-room-box">
            <div class="meeting-room-box-inner">
                <div class="meeting-room-head" style="z-index: 3; position: relative">
                    <span class="room-title">
                        <svg class="icon-12" v-show="selectedRoom.roomName">
                            <use xlink:href="#icon-meeting-12-4"></use>
                        </svg>
                        {{ selectedRoom.roomName }}
                    </span>
                </div>
                <div class="room-container">
                    <div class="meeting-room-content"
                        style="display: flex; justify-content: center; position: relative;pointer-events: none;">
                        <SlMeetingRoom5 v-if="selectedRoom.roomF == 5" :selectedId="selectedRoom.id" />
                        <SlMeetingRoom8 v-if="selectedRoom.roomF == 8" :selectedId="selectedRoom.id" />
                        <SlMeetingRoom9 v-if="selectedRoom.roomF == 9" :selectedId="selectedRoom.id" />
                        <SlMeetingRoom13 v-if="selectedRoom.roomF == 13" :selectedId="selectedRoom.id" />
                    </div>
                </div>
            </div>
        </div>
        <!-- 会议服务 -->
        <div class="meeting-info" style="margin-bottom: 0;">
            <span class="label">会议服务</span>
        </div>
        <div class="service-out" v-show="meetServiceInfo?.length">
            <div class="service-box">
                <div class="service-item" v-for="item of meetServiceInfo" :key="item.title">
                    <div style="margin-bottom: 3px;">{{ item.title }}</div>
                    <div class="service-item-content" v-for="child of item.children" :key="child.title">
                        <span>{{ child.title }}：</span>
                        <span class="content_value">{{ child.value }}</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- 会议提醒 -->
        <div class="meeting-info">
            <span class="label">会议提醒</span>
            <div class="flex-end">
                <span class="content-overflow">
                    {{ notifyMinutesBeforeText || '' }}
                </span>
            </div>
        </div>
        <!-- 通知方式 -->
        <div class="meeting-info">
            <span class="label">通知方式</span>
            <div class="flex-end">
                <span class="content-overflow">
                    <span v-if="!!!meetingInfo.notifyBySms && !!!meetingInfo.notifyByVoiceCall">''</span>
                    <span v-else>
                        {{ meetingInfo.notifyBySms == 0 ? '' : '短信通知' }}
                        {{ meetingInfo.notifyByVoiceCall == 0 ? '' : '机器人语音电话通知' }}
                    </span>
                </span>
            </div>
        </div>
        <!-- 是否参会 -->
        <!-- 未开始的会议才支持操作修改 -->
        <template v-if="meetingInfo.status == 0">
            <template v-if="currentUser.role == 0">
                <div v-if="showFeedback" class="feedback-info">
                    <span>是否参会</span>
                    <div class="radio-box" @click="currentUser.feedback = 1">
                         <svg class="icon-12">
                            <use xlink:href="#icon-meeting-12-3" v-if="currentUser.feedback == 1"></use>
                            <use xlink:href="#icon-meeting-12-2" v-else></use>
                        </svg>
                        参加
                    </div>
                    <div class="radio-box" @click="currentUser.feedback = 2">
                         <svg class="icon-12">
                            <use xlink:href="#icon-meeting-12-3" v-if="currentUser.feedback == 2"></use>
                            <use xlink:href="#icon-meeting-12-2" v-else></use>
                        </svg>
                        建议延迟
                    </div>
                    <!-- 建议延迟原因 -->
                    <template v-if="currentUser.feedback == 2">
                        <input type="text" v-model="currentUser.reason" />
                    </template>
                    <div class="radio-box" @click="currentUser.feedback = 3">
                         <svg class="icon-12">
                            <use xlink:href="#icon-meeting-12-3" v-if="currentUser.feedback == 3"></use>
                            <use xlink:href="#icon-meeting-12-2" v-else></use>
                        </svg>
                        不参加
                    </div>
                    <!-- 不参加原因 -->
                    <template v-if="currentUser.feedback == 3">
                        <input type="text" v-model="currentUser.reason" />
                    </template>
                </div>
            </template>
            <!-- 待开始的会议支持修改 -->
            <div class="btn-footer">
                <template v-if="currentUser.role == 1">
                    <!-- <n-button class="save" type="primary" @click="onSave">修改会议</n-button> -->
                    <!-- <n-button class="cancel" @click="onCancel">取消会议</n-button> -->
                </template>
                <!-- <n-button v-else class="save" type="primary" @click="onSave">提交</n-button> -->
            </div>
        </template>
    </div>
    <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="onConfirmCancel">您确定要取消当前已预约会议吗？
    </ConfirmDialog>
    <!-- <div id="tooltip" ref="meetngRoom222" style="position: absolute; display: none; background: #fff; padding: 5px; border: 1px solid #000"></div> -->
</template>

<script setup lang="ts">
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import SlMeetingRoom13 from '@/components/SlMeetingRoom13.vue';
import SlMeetingRoom5 from "@/components/SlMeetingRoom5.vue";
import SlMeetingRoom8 from "@/components/SlMeetingRoom8.vue";
import SlMeetingRoom9 from "@/components/SlMeetingRoom9.vue";
import type { Meeting, Meetingpersonnel } from '@/models/MettingHandle';
import meetHandleService from '@/service/meet-assistant/meet-handle/meet-handle.service';
import { cloneDeep } from '@/utils/clone';
import { convertBytes } from "@/utils/transformBytes";
// import { NButton, useMessage } from 'naive-ui';
import { computed, onMounted, ref } from 'vue';
// import { useRoute, useRouter } from 'vue-router';
import { generateSelectedMeetingRoom, MeetingServiceOptions, type SelectedMeetingRoom } from './meeting-handle-data';
import type { Content } from "@/models/Content";
const meetingInfo = ref<Meeting>({})
const showConfirm = ref(false)
// const route = useRoute()
// const router = useRouter()
const SERVICE_OPTIONS = cloneDeep(MeetingServiceOptions)
const showFeedback = ref(false)
const currentUser = ref<Meetingpersonnel>({ feedback: 1 })
/** 会议服务详情 */
const meetServiceInfo = ref<{ title: string, children: { title: string, value: string }[] }[]>([])
/** 参会人员数量统计 (已确定要参会的人员/总人员) */
const getAttendeeCount = computed(() => {
    const meetingpersonnelList = meetingInfo.value?.meetingpersonnelList || []
    if (!meetingpersonnelList?.length) return '';
    const attendeeCount = meetingpersonnelList.filter(item => item.feedback == 1)?.length
    return `${attendeeCount}/${meetingpersonnelList.length}`
})
const getAttendeeList = computed(() => {
    const meetingpersonnelList = meetingInfo.value?.meetingpersonnelList || []
    return meetingpersonnelList.filter(item => item.feedback == 1)
})
const notifyMinutesBeforeText = computed(() => {
    const { sendInstantNotification, notifyMinutesBefore } = meetingInfo.value
    if (sendInstantNotification == null && notifyMinutesBefore == null) return ''
    if (sendInstantNotification == 1) return '立即通知'
    else return notifyMinutesBefore == null ? '' : `开始前${notifyMinutesBefore}分钟`
})
/** 会议状态 */
const getMeetingStatusText = computed(() => {
    const statusData: Record<number, string> = {
        0: '待开始',
        1: '进行中',
        2: '已取消',
        3: '已结束',
    }
    return meetingInfo.value.status == null ? '' : statusData[meetingInfo.value.status]
})
const currentId = ref('')
onMounted(() => {
    // const id = route.params.id as string || ''
    // currentId.value = id
    // getInfo(id)
})
const selectedRoom = ref<SelectedMeetingRoom>({
    id: undefined,
    roomF: 5,
    roomName: '',
})
function getInfo(id: string) {
    if (!id) return;
    meetHandleService.getInfoMeeting(id).then(res => {
        meetingInfo.value = res
        showFeedback.value = res.status == 0
        console.log("🚀 ~ meetHandleService.getInfoMeeting ~ res:", res)
        meetServiceInfo.value = []
        // 会议服务
        res.meetingfacilityList?.forEach(facility => {
            const serviceItem = SERVICE_OPTIONS.find(ele => ele.value == facility.type)
            if (!serviceItem) return;
            const title = serviceItem.title
            const children: { title: string, value: string }[] = []
            serviceItem.children?.forEach(child => {
                let value = ''
                if (child.type == 'select') {
                    // 数量为选择
                    value = facility.quantity ? `${facility.quantity}${child.unit || ''}` : ''
                } else {
                    // 内容
                    value = `${facility.content}`
                }
                if (value)
                    children.push({
                        title: child.title,
                        value,
                    })
            })
            meetServiceInfo.value.push({
                title,
                children,
            })
        })
        // 已选中的会议室
        if (res.meetingroom?.id) {
            const selected = generateSelectedMeetingRoom(res.meetingroom)
            selectedRoom.value = selected
        }
        // res.id
        if (res.id) {
            meetHandleService.getCurrentMeetingPersonnel(res.id).then(currentPersonnel => {
                showFeedback.value = currentPersonnel.role == 0 && res.status == 0
                currentUser.value = currentPersonnel || { feedback: 1 } // 默认勾选参加
            })
        }
    }).catch(err => {
        // message.error(err || '系统异常')
    })
}
/** 查看会议参与详情 */
function handleSeeAttendeeDetail() {
    // router.push({ name: 'meeting-info-participation', params: { id: meetingInfo.value.id } })
}
/** 修改会议 */
function onSave() {
    if (currentUser.value.role == 1) {
        // 发起人才可以修改
        // router.push({ name: 'meeting-handle-edit', params: { id: meetingInfo.value.id } })
    } else {
        // 参会人保存意见
        meetHandleService.saveMeetingpersonnel(currentUser.value).then(_ => {
            // message.success('保存成功')
            // router.back()
        }).catch(err => {
            // message.error(err || '保存失败')
        })
    }
}
/** 取消会议 */
function onCancel() {
    showConfirm.value = true
}
// const message = useMessage();
/** 确定取消会议 */
function onConfirmCancel() {
    if (!currentId.value) return
    meetHandleService.cancelMeeting(currentId.value).then(_ => {
        // message.success('取消成功')
        // router.back();
    }).catch(_ => {
        // message.error('取消失败')
    })
}

// const previewFile = (_item: Content) => {
//     if(!_item.id) return
//   const a = document.createElement('a')
//   a.href = `/api/Content/download/${_item.id}`
//   a.download = ''
//   document.body.appendChild(a)
//   a.click()
//   document.body.removeChild(a)
// }
const previewFile = async (_item: Content) => {
    if (!_item.id) return
    try {
        const response = await fetch(`/api/Content/download/${_item.id}`)
        const blob = await response.blob()
        const blobUrl = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = blobUrl
        if (_item.fullName && _item.fileType) {
            a.download = _item.fullName + _item.fileType
        } else {
            a.download = 'download' // 设置默认文件名
        }
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(blobUrl)
    } catch (err) {
        console.error('文件下载失败', err)
    }
}
</script>

<style scoped>
.meeting-info-main {
    /* 视口高度 */
    /* height: calc(100vh - 64px); */
    width: 100vw;
    position: relative;
    /* padding: 0 0.9375rem ; */
    /* padding: 0 15px 10px; */
    /* background-color: #fff; */
}

.info-container {
    padding: 8px 15px;
    /* border-top: 1px dashed rgba(153, 153, 153, 0.2); */
    /* border-bottom: 1px dashed rgba(153, 153, 153, 0.2); */
    margin-top: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.info-container>:last-child {
    border-bottom: 1px dashed rgba(153, 153, 153, 0.2);
    padding-bottom: 5px;
}

.info-container-item {
    /* min-height: 41px; */
    display: flex;
    align-items: center;
}

.meeting-info {
    display: flex;
    align-items: center;
    margin: 10px 0;
    min-height: 40px;
    padding: 0 15px;
    background-color: #fff;
}

.feedback-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 10px 0;
    min-height: 40px;
    padding: 15px;
    background-color: #fff;
    font-size: 14px;
}

.info-time {
    height: 40px;
    margin-bottom: 0;
    margin-top: 0;
    display: flex;
    align-items: center;
    font-size: 12px;
}

.info-time .flex-end {
    font-size: 12px;
}

.info-time svg {
    margin-right: 4px;
}

.border-bottom {
    border-bottom: 1px dashed rgba(153, 153, 153, 0.2);
}

.meeting-info_title {
    font-size: 14px;
    color: #333;
    font-weight: bold;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.label {
    min-width: 54px;
    font-size: 14px;
}

.content {
    font-size: 14px;
}

.meeting-info .content {
    flex: 1;
}

.circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    /* text-align: center; */
    /* line-height: 24px; */
    margin-right: 5px;
    background-color: #999;
    color: #fff;
    font-size: 10px;
}

.circle-large {
    width: 34px;
    height: 34px;
    font-size: 14px;
    margin-right: 0;
}

.circle.circle-blue {
    background-color: #4f7af6;
}

.circle-out-icon {
    position: relative;
}

.circle-out-icon::after {
    position: absolute;
    content: '外';
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    line-height: 16px;
    border: 1px solid #3e75fe;
    border-radius: 50%;
    color: #3e75fe;
    text-align: center;
}

.flex-end {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 4px;
    color: #999;
    font-size: 14px;
}

.content-overflow {
    max-width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-wrap: nowrap;
}

.participant-box {
    display: flex;
    align-items: center;
    width: 100%;
    flex-wrap: wrap;
    gap: 10px 0;
    padding: 0 10px 10px;
    /* padding: 8px 12px; */
    min-height: 50px;
    background-color: #fff;
}

.participant-item {
    width: 20%;
    /* min-width: 34px; */
    min-height: 54px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.participant-name {
    color: #333;
    font-size: 10px;
    margin-top: 4px;
}

/* 会议室 */
.meeting-room-box {
    /* height: 200px; */
    padding: 0 15px 10px;
    position: relative;
    background-color: #fff;
}

/* 会议室 */
.meeting-room-box-inner {
    border-radius: 3px;
    border: 1px solid rgba(153, 153, 153, 0.2);
    position: relative;
    padding-left: 10px;
    background-color: #fff;
}

.meeting-room-head {
    height: 26px;
    display: flex;
    align-items: center;
    margin-top: 8px;
    justify-content: space-between;
}

.room-title {
    color: #666;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.room-select {
    width: 61px;
    height: 26px;
}

.meeting-room-rect {
    position: absolute;
    bottom: 6px;
    right: 10px;
    font-size: 8px;
    color: #666;
}

.rect {
    display: inline-block;
    border-radius: 1px;
    background: #ffffff;
    border: 1px solid rgba(153, 153, 153, 0.2);
    background-color: #fff;
    width: 8px;
    height: 8px;
}

/* 会议状态文字 */
/* 待开始 */
.meeting-status-0 {
    color: #FF7605;
}

/* 进行中 */
.meeting-status-1 {
    color: #FF3333;
}

/* 取消，结束 */
.meeting-status-2,
.meeting-status-3 {
    color: #666;
}

/* 会议服务 */
.service-out {
    padding: 0 12px 8px;
    background-color: #fff;
    margin-bottom: 8px;
}

.service-box {
    display: flex;
    flex-direction: column;
    border-radius: 3px;
    border: 1px solid rgba(153, 153, 153, 0.2);
    padding: 12px 10px;
    gap: 4px;
}

.service-item-content {
    min-height: 17px;
    display: flex;
    align-items: center;
    color: #999;
    font-size: 14px;
}

.content_value {
    flex: 1;
    display: inline-block;
}

.require {
    position: relative;
}

.require::before {
    content: '*';
    position: absolute;
    color: #e50101;
    top: 0;
    /* left: -0.5rem; */
    left: -8px;
}

.btn-footer {
    margin: 10px 0;
    padding-left: 15px;
    padding-right: 10px;
}

.btn-footer .n-button {
    width: 100%;
    margin: 5px 0;
    height: 40px;
    border-radius: 3px;
}

.btn-footer .n-button.save {
    background: #0066df;
}

.btn-footer .n-button.cancel {
    background: #fff;
    border-color: #0066DF;
}

.room-container {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
}

.meeting-room-content {
    width: 190px;
    height: 120px;
}

.content-file-box {
    display: flex;
    flex-direction: column;
    width: 80%;
}

.content-file-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.file-name {
    font-size: 14px;
    color: #333;
}

.file-size {
    font-size: 8px;
    color: #666;
}
</style>