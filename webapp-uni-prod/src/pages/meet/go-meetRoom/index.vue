<template>
  <div class="room-container">
    <div class="top-info">
      <svg class="icon-12" v-show="selectedRoom.roomName">
        <use xlink:href="#icon-meeting-12-4"></use>
      </svg>
      <span>{{ selectedRoom.roomName }}</span>
    </div>
    <!-- 主体内容区域 -->
    <div class="meeting-room-container">
      <div class="meeting-room-content" v-if="selectedRoom.roomF">
        <SlMeetingRoom5 v-if="selectedRoom.roomF == 5" :selectedId="selectedRoom.id" />
        <SlMeetingRoom8 v-if="selectedRoom.roomF == 8" :selectedId="selectedRoom.id" />
        <SlMeetingRoom9 v-if="selectedRoom.roomF == 9" :selectedId="selectedRoom.id" />
        <SlMeetingRoom13 v-if="selectedRoom.roomF == 13" :selectedId="selectedRoom.id" />
      </div>
      <div class="empty-container" v-else>
        <BaseEmpty>
          暂无图片
        </BaseEmpty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseEmpty from "@/components/BaseEmpty.vue";
import SlMeetingRoom13 from "@/components/SlMeetingRoom13.vue";
import SlMeetingRoom5 from "@/components/SlMeetingRoom5.vue";
import SlMeetingRoom8 from "@/components/SlMeetingRoom8.vue";
import SlMeetingRoom9 from "@/components/SlMeetingRoom9.vue";
import type { Meetingroom } from "@/models/MettingHandle";
import { onMounted, ref } from 'vue';
import { useRoute } from "vue-router";
import { generateSelectedMeetingRoom, SelectedMeetingRoom } from "../meeting-handle/meeting-handle-data";
const route = useRoute();
const selectedRoom = ref<SelectedMeetingRoom>({
  id: undefined,
  roomF: null,
  roomName: '',
})
onMounted(() => {
  const meetingroom = route.query as Meetingroom;
  // 已选中的会议室
  if (meetingroom?.id) {
    const selected = generateSelectedMeetingRoom(meetingroom)
    selectedRoom.value = selected
  }
})
</script>

<style scoped>
.room-container .top {
  text-align: center;
  color: white;
  background: linear-gradient(180deg, #0221e7 0%, #4c5cff 100%);
  width: 100%;
  height: 64px; /* 20vw → 64px */
  line-height: 64px; /* 20vw → 64px */
  opacity: 1;
  flex-shrink: 0;
  position: relative;
}

.room-container .top i {
  position: absolute;
  left: 14.5px; /* 4.53125vw → 14.5px */
  top: 50%;
  display: block;
  width: 9px; /* 2.8125vw → 9px */
  height: 16px; /* 5vw → 16px */
  background: url("@/assets/image/meeting/back.png") center no-repeat;
  background-size: cover;
}

.room-container .top-info {
  display: flex;
  height: 45px; /* 14.0625vw → 45px */
  background: #f4f6fa;
  align-items: center;
  justify-content: center;
}

.room-container .top-info i {
  display: block;
  width: 9px; /* 2.8125vw → 9px */
  height: 10px; /* 3.125vw → 10px */
  background-size: cover;
}

.room-container .top-info span {
  opacity: 1;
  color: #333333;
  font-size: 14px; /* 4.375vw → 14px */
  margin-left: 4px; /* 1.25vw → 4px */
}

.room-container .mainBody {
  height: calc(100vh - 89px); /* 34.0625vw → 109px */
  width: 100%;
  opacity: 1;
  border-radius: 0;
  background: #fdfdfd;
  box-shadow: 0 0 4px #92929233; /* 1.25vw → 4px */
}

.meeting-room-container {
  width: 100%;
  padding: 15px;
  background-color: #fff;
}

.meeting-room-content {
  width: 290px;
  height: 146px;
  display: flex;
  justify-content: center;
  position: relative;
  pointer-events: none;
}
</style>
