<template>
  <view class="upload-wrap">
    <view class="upload-content">
      <view class="image-content">
        <!-- 预览区域 -->
        <view class="image-wrap">
          <view
            :class="[
              'preview-item',
              props.size == 64 ? 'preview-item-normal' : 'preview-item-small',
            ]"
            v-for="(item, index) in tempFiles"
            :key="index"
          >
            <!-- 图片预览 -->
            <image
              v-if="item.fileType === 'image'"
              class="preview-img"
              :src="item.path"
              mode="aspectFill"
              @click="previewImage(index)"
            />
            <!-- 视频预览 -->
            <view
              v-else-if="item.fileType === 'video'"
              class="preview-video"
              @click="previewVideo(index)"
            >
              <video
                class="video-thumbnail"
                :src="item.path"
                :show-play-btn="false"
                :controls="false"
                muted
                object-fit="cover"
                :initial-time="0.1"
                :enable-progress-gesture="false"
                :show-fullscreen-btn="false"
                :show-center-play-btn="false"
                :auto-pause-if-navigate="true"
                :auto-pause-if-open-native="true"
              ></video>
              <SlSVgIcon
                v-if="item.status === 'done'"
                name="14-14-24"
                size="14"
                class="video-icon"
              />
            </view>
            <view v-if="item.status === 'uploading'" class="upload-overlay">
              <view
                :class="[
                  'progress-text',
                  props.size == 64
                    ? 'preview-text-normal'
                    : 'preview-text-small',
                ]"
                >{{ item.progress }}%</view
              >
              <view
                :class="[
                  'progress-text',
                  props.size == 64
                    ? 'preview-text-normal'
                    : 'preview-text-small',
                ]"
                >上传中...</view
              >
            </view>
            <view
              v-if="item.status === 'done' || item.status === 'error'"
              class="delete-wrap"
            >
              <view
                v-if="item.status === 'error'"
                class="btn reupload-btn"
                @click.stop="reUploadImage(index)"
                ><view>上传失败</view><view>重新上传</view>
              </view>
              <view class="btn delete-btn" @click.stop="removeImage(index)"
                >删除</view
              >
            </view>
          </view>
          <!-- 上传按钮 -->
          <view
            v-if="tempFiles.length < maxCount"
            class="upload-btn"
            @tap="showActionSheet"
          >
            <SlSVgIcon name="64-64-1" :size="size" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
/**图片/视频的上传 */
// import { useAuthorize } from "@/hooks";
import { Content } from "@/models/Content";
import { uploadFile } from "@/service/upload.service";
import { uuidv4 } from "@/utils/uuid";
import { ref, watch } from "vue";
// 类型定义
interface TempFile {
  id?: string;
  /**上传后的文件路径 */ path: string;
  /**文件*/ file?: File;
  /**上传进度*/ progress: number;
  /**上传状态*/ status?: "waiting" | "uploading" | "done" | "error";
  /**(压缩后)文件大小*/ size: number;
  /**原始文件大小*/ oSize?: number;
  /**文件信息*/ fileInfo?: any;
  /**文件类型*/ fileType: "image" | "video";
  /**视频时长*/ duration?: number;
  /**临时文件路径*/ tempFilePath?: string;
}
// 上传按钮尺寸 32-small 64-normal
type Size = 32 | 64;
const props = withDefaults(
  defineProps<{
    /**上传地址*/ url?: string;
    /**最大上传数量*/ maxCount?: number;
    /**最大文件大小 单位MB*/ maxSize?: number;
    /**上传内容列表*/ contentList?: Content[];
    /**上传按钮大小*/ size?: Size;
    /**上传类型*/ uploadType?: "image" | "video";
    /**是否全部上传完成*/ ifAllDone?: boolean;
  }>(),
  {
    url: "/Content/upload",
    maxCount: 4,
    maxSize: 10, // 小程序单文件最大10MB
    contentList: () => [],
    size: 64,
    uploadType: undefined, // 默认为undefined，允许选择图片和视频
  }
);
const emits = defineEmits(["update:contentList", "change", "update:ifAllDone"]);
const tempFiles = ref<TempFile[]>([]);

// 计算允许的媒体类型
const mediaType = computed(() => {
  if (props.uploadType === "image") {
    return ["image"];
  } else if (props.uploadType === "video") {
    return ["video"];
  }
  return ["image", "video"]; // 允许选择图片和视频
});
/**上传的内容是否全部完成 */
watch(
  () => tempFiles.value,
  (newFiles) => {
    const allDone = newFiles.every((file) => file.status === "done");
    emits("update:ifAllDone", allDone);
  },
  { immediate: true, deep: true }
);
// 显示选择器
const showActionSheet = () => {
  uni.showActionSheet({
    itemList: ["拍照", "从手机相册选择"],
    success: ({ tapIndex }) => {
      chooseMedia(tapIndex === 0 ? ["camera"] : ["album"]);
    },
  });
};

// 选择图片/视频
const chooseMedia = async (sourceType: any[]) => {
  try {
    /**隐私保护配置了"摄像头"+"收集你选中的照片或视频信息",调用uni.chooseMedia可以自发弹出授权隐私保护的弹窗，通过后即可直接使用拍照/视频功能。在其他地方同意后再调用也可直接使用。不需要显式请求 */
    /**1.显式请求相机权限 */
    // if (sourceType.includes("camera")) {
    //   const hasPerm = await useAuthorize("camera");
    //   if (!hasPerm) {
    //     return;
    //   }
    // }
    /**2. 选择媒体文件 */
    const res = await selectMediaFiles(sourceType);
    /**3. 处理所有选中的文件 */
    const filesToProcess: any = [];
    await Promise.all(
      res.tempFiles.map(async (file: TempFile) => {
        const processedFile = await processFile(file);
        // let thumbnailPath: string = "";
        // if (file.fileType === "video") {
        //   // 微信小程序chooseMedia API会返回视频封面
        //   // #ifdef MP-WEIXIN
        //   thumbnailPath = file.thumbTempFilePath || "";
        //   // #endif
        // }
        // if (thumbnailPath) processedFile.thumbnailPath = thumbnailPath;
        if (processedFile) {
          filesToProcess.push(processedFile);
        }
      })
    );
    /**4.添加到临时文件列表（显示上传状态） */
    tempFiles.value.push(...filesToProcess);
    /**5.并行上传所有文件 */
    const uploadResults = await Promise.all(
      filesToProcess.map(uploadSingleFile)
    );
    /**6.统一处理上传结果 */
    const successfulUploads = uploadResults.filter(Boolean);
    updateContentList(successfulUploads);
  } catch (error) {
    // console.error("选择文件失败", error);
  }
};
/**选择媒体文件 */
const selectMediaFiles = async (sourceType: any[]) => {
  return new Promise<any>((resolve, reject) => {
    uni.chooseMedia({
      count: props.maxCount - tempFiles.value.length,
      mediaType: mediaType.value as ["image" | "video"],
      sourceType,
      maxDuration: mediaType.value.includes("video") ? 60 : undefined,
      sizeType: ["compressed"],
      success: resolve,
      fail: (err) => {
        if (err.errMsg.includes("auth") || err.errMsg.includes("deny")) {
          uni.showToast({
            title:
              "您已拒绝隐私保护指引，将无法使用[拍照/相册]核心功能，请稍后再试",
            icon: "none",
            duration: 2000,
          });
        }
        reject(err);
      },
    });
  });
};
/**处理文件大小和压缩 */
const processFile = async (file: TempFile) => {
  /**检查文件类型是否符合要求 只有图片/视频一个选项时才需要检查 */
  // if (mediaType.value.length == 1 && !mediaType.value.includes(file.fileType)) {
  //   uni.showToast({
  //     title: `请选择${file.fileType === "image" ? "图片" : "视频"}`,
  //     icon: "none",
  //   });
  //   return null;
  // }
  /**检查文件大小并处理 */
  let processedFile = file;
  if (file.size > props.maxSize * 1024 * 1024) {
    /**尝试压缩图片 */
    if (file.fileType === "image") {
      try {
        const compressedFile = await compressImage(file.tempFilePath);
        processedFile = {
          ...file,
          ...compressedFile,
          oSize: file.size, // 保留原始大小用于比较
        };
      } catch (compressError) {
        uni.showToast({
          title: `图片过大且压缩失败 (${formatFileSize(file.size)})`,
          icon: "none",
          duration: 3000,
        });
        return null;
      }
    } else if (file.fileType === "video") {
      try {
        const compressedFile = await compressVideo(file.tempFilePath);
        processedFile = {
          ...file,
          ...compressedFile,
          oSize: file.size, // 保留原始大小用于比较
        };
      } catch (compressError) {
        uni.showToast({
          title: `视频过大且压缩失败 (${formatFileSize(file.size)})`,
          icon: "none",
          duration: 3000,
        });
        return null;
      }
    }
  }
  /**path是压缩后的路径,tempFilePath是原始路径,优先使用压缩后的路径 */
  return {
    id: uuidv4(),
    path: processedFile.path || processedFile.tempFilePath,
    file: processedFile,
    size: processedFile.size,
    oSize: processedFile.oSize,
    fileType: processedFile.fileType,
    duration: processedFile.duration,
    progress: 0,
    status: "waiting",
  };
};

/**上传单个文件 */
const uploadSingleFile = async (file: TempFile): Promise<any> => {
  const idx = tempFiles.value.findIndex((f) => f.id === file.id);
  if (idx === -1) return null;
  try {
    /**更新状态为上传中 */
    tempFiles.value[idx].status = "uploading";
    let res = Object.create({});
    const result = await uploadFile({
      url: props.url,
      filePath: file.path,
      name: "file",
      formData: { type: file.fileType },
      onProgressUpdate: (e) => {
        tempFiles.value[idx].progress = e;
      },
    });
    res = result;
    if (file.duration && file.fileType === "video") {
      res.duration = file.duration;
    }
    /**更新状态为完成 */
    tempFiles.value[idx].status = "done";
    tempFiles.value[idx].fileInfo = res;
    return res;
  } catch (err) {
    tempFiles.value[idx].status = "error";
    return null;
  }
};

/**更新内容列表 */
const updateContentList = (successfulUploads: TempFile[]) => {
  if (successfulUploads.length > 0) {
    const newContentList = [
      ...(Array.isArray(props.contentList) ? props.contentList : []),
      ...successfulUploads,
    ];
    emits("update:contentList", newContentList);
    emits("change", {
      contentList: newContentList,
      type: "add",
      res: successfulUploads,
    });
  }
};
// 压缩图片函数
const compressImage = (filePath: string = ""): Promise<any> => {
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src: filePath,
      quality: 80, // 压缩质量 (0-100)
      success: (compressedRes) => {
        // 获取压缩后的文件信息
        uni.getFileSystemManager().getFileInfo({
          filePath: compressedRes.tempFilePath,
          success: (infoRes) => {
            resolve({
              path: compressedRes.tempFilePath,
              size: infoRes.size,
            });
          },
          fail: reject,
        });
      },
      fail: reject,
    });
  });
};
/**压缩视频(本地调试时需要集成 ffmpeg) */
const compressVideo = (filePath: string = ""): Promise<any> => {
  return new Promise((resolve, reject) => {
    uni.compressVideo({
      src: filePath,
      quality: "high", // 压缩质量high medium low
      success: (compressedRes) => {
        // 获取压缩后的文件信息
        uni.getFileSystemManager().getFileInfo({
          filePath: compressedRes.tempFilePath,
          success: (infoRes) => {
            resolve({
              path: compressedRes.tempFilePath,
              size: infoRes.size,
            });
          },
          fail: reject,
        });
      },
      fail: reject,
    });
  });
};

// 文件大小格式化函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
};
/**上传失败时点击重新上传 */
const reUploadImage = async (index: number) => {
  const file = tempFiles.value[index];
  const uploadResults = await uploadSingleFile(file);
  if (!uploadResults) return;
  /**6.统一处理上传结果 */
  const successfulUploads = uploadResults.filter(Boolean);
  updateContentList(successfulUploads);
};
// 删除图片
const removeImage = (index: number) => {
  const removedFile = tempFiles.value[index];
  tempFiles.value.splice(index, 1);
  const curFile = removedFile.fileInfo;
  if (curFile) {    
    /**从contentList中找到对应的项并删除 */
    const contentIndex = props.contentList.findIndex(
      (item) => item.storeName === curFile.storeName
    );
    if (contentIndex !== -1) {
      const newContentList = [...props.contentList];
      newContentList.splice(contentIndex, 1);
      emits("update:contentList", newContentList);
      emits("change", {
        contentList: newContentList,
        type: "remove",
        fileInfo: curFile,
      });
    }
  }
};
// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    current: index,
    urls: tempFiles.value.map((item) => item.path),
  });
};
// 预览视频
const previewVideo = (index: number) => {
  const videoPath = tempFiles.value[index].path;
  // 小程序原生的视频播放-需要真机调试
  // #ifdef MP-WEIXIN
  uni.openVideoEditor({
    filePath: videoPath,
    success: function (res) {},
    fail: function (res) {},
  });
  // #endif
};
</script>
<style lang="scss">
.upload-wrap {
  .upload-content {
    .image-content {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .image-wrap {
        display: flex;
        // 上传按钮尺寸为64px
        .preview-item-normal {
          width: 64px;
          height: 64px;
        }
        .preview-item-small {
          height: 32px;
          width: 32px;
        }
        .preview-item {
          margin: 1px 10px 1px 0px;
          position: relative;
          border-radius: 4px;
          overflow: hidden;
          .preview-img,
          .preview-video {
            width: 100%;
            height: 100%;
            border-radius: 4px;
          }
          .preview-video {
            position: relative;
            .video-thumbnail {
              width: 100%;
              height: 100%;
              border-radius: 4px;
            }
            .video-icon {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              z-index: 4;
            }
          }
          .upload-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            z-index: 2;

            .progress-text {
              color: #fff;
              margin-bottom: 4px;
            }
            .preview-text-normal {
              font-size: 12px;
            }
            .preview-text-small {
              font-size: 6px;
            }
          }
          .delete-wrap {
            width: 100%;
            height: 100%;
            position: absolute;
            bottom: 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            z-index: 3;
            .btn {
              width: 100%;
              text-align: center;
              font-size: 12px;
              color: #fff;
              background: rgba(51, 51, 51, 0.5);
              pointer-events: auto;
            }
            .delete-btn {
              height: 22px;
              line-height: 22px;
            }
            .reupload-btn {
              height: 50%;
              flex-grow: 1;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              border-bottom: rgba(51, 51, 51, 0.2);
            }
          }
          view:last-child {
            margin-right: unset;
          }
        }
      }
    }
  }
}
</style>
