<template>
  <view class="upload-wrap">
    <view class="upload-content">
      <view class="image-content">
        <!-- 预览区域 -->
        <view class="image-wrap">
          <view
            :class="[
              'preview-item',
              props.size == 64 ? 'preview-item-normal' : 'preview-item-small',
            ]"
            v-for="(item, index) in tempFiles"
            :key="index"
          >
            <!-- 图片预览 -->
            <image
              v-if="item.fileType === 'image'"
              class="preview-img"
              :src="item.path"
              mode="aspectFill"
              @click="previewImage(index)"
            />
            <!-- 视频预览 -->
            <view
              v-else-if="item.fileType === 'video'"
              class="preview-video"
              @click="previewVideo(index)"
            >
              <video
                class="video-thumbnail"
                :src="item.path"
                :show-play-btn="false"
                :controls="false"
                muted
                object-fit="cover"
                :initial-time="0.1"
                :enable-progress-gesture="false"
                :show-fullscreen-btn="false"
                :show-center-play-btn="false"
                :auto-pause-if-navigate="true"
                :auto-pause-if-open-native="true"
              ></video>
              <SlSVgIcon
                v-if="item.status === 'done'"
                name="14-14-24"
                size="14"
                class="video-icon"
              />
            </view>
            <view v-if="item.status === 'uploading'" class="upload-overlay">
              <view
                :class="[
                  'progress-text',
                  props.size == 64
                    ? 'preview-text-normal'
                    : 'preview-text-small',
                ]"
                >{{ item.progress }}%</view
              >
              <view
                :class="[
                  'progress-text',
                  props.size == 64
                    ? 'preview-text-normal'
                    : 'preview-text-small',
                ]"
                >上传中...</view
              >
            </view>
            <view v-if="item.status === 'done'" class="delete-wrap">
              <view class="delete-btn" @click.stop="removeImage(index)"
                >删除</view
              ></view
            >
          </view>
        </view>
        <!-- 上传按钮 -->
        <view
          v-if="tempFiles.length < maxCount"
          class="upload-btn"
          @click="showActionSheet"
        >
          <SlSVgIcon name="64-64-1" :size="size" />
        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
/**图片/视频的上传 */
import { Content } from "@/models/Content";
import { uploadFile } from "@/service/upload.service";
import { ref, watch } from "vue";
// 类型定义
interface TempFile {
  path: string;
  file?: File;
  progress: number;
  status?: "uploading" | "done" | "error";
  size: number;
  fileInfo?: any; // 存储上传后的文件信息
  fileType: "image" | "video";
  duration?: number; // 视频时长
}
// 上传按钮尺寸 32-small 64-normal
type Size = 32 | 64;
const props = withDefaults(
  defineProps<{
    url?: string;
    maxCount?: number;
    maxSize?: number; // 单位MB
    contentList?: Content[];
    size?: Size; // 上传按钮大小
    uploadType?: "image" | "video"; // 上传类型
  }>(),
  {
    url: "/Content/upload",
    maxCount: 4,
    maxSize: 10, // 小程序单文件最大10MB
    contentList: () => [],
    size: 64,
    uploadType: undefined, // 默认为undefined，允许选择图片和视频
  }
);
const emits = defineEmits(["update:contentList", "change"]);
const tempFiles = ref<TempFile[]>([]);

// 计算允许的媒体类型
const mediaType = computed(() => {
  if (props.uploadType === "image") {
    return ["image"];
  } else if (props.uploadType === "video") {
    return ["video"];
  }
  return ["image", "video"]; // 允许选择图片和视频
});

// 显示选择器
const showActionSheet = () => {
  uni.showActionSheet({
    itemList: ["拍照", "从手机相册选择"],
    success: ({ tapIndex }) => {
      chooseMedia(tapIndex === 0 ? ["camera"] : ["album"]);
    },
  });
};

// 选择图片/视频
const chooseMedia = async (sourceType: any[]) => {
  try {
    const res = await new Promise<any>((resolve, reject) => {
      uni.chooseMedia({
        count: props.maxCount - tempFiles.value.length,
        mediaType: mediaType.value as ["image" | "video"], // 使用计算出的媒体类型
        sourceType,
        maxDuration: props.uploadType === "video" ? 60 : undefined, // 视频最长时长，单位秒
        sizeType: ["compressed"], // 优先尝试压缩
        success: resolve,
        fail: reject,
      });
    });

    // 并行处理所有选中的文件
    await Promise.all(
      res.tempFiles.map(async (file: any) => {
        // 检查文件类型是否符合要求
        if (props.uploadType && file.fileType !== props.uploadType) {
          uni.showToast({
            title: `请选择${props.uploadType === "image" ? "图片" : "视频"}`,
            icon: "none",
          });
          return;
        }

        // 检查文件大小
        if (file.size > props.maxSize * 1024 * 1024) {
          // 对于图片，尝试压缩
          if (file.fileType === "image") {
            try {
              const compressedFile = await compressImage(file.tempFilePath);
              file = {
                ...file,
                ...compressedFile,
                originalSize: file.size, // 保留原始大小用于比较
              };
            } catch (compressError) {
              uni.showToast({
                title: `图片过大且压缩失败 (${formatFileSize(file.size)})`,
                icon: "none",
                duration: 3000,
              });
              return;
            }
          } else if (file.fileType === "video") {
            try {
              const compressedFile = await compressVideo(file.tempFilePath);
              file = {
                ...file,
                ...compressedFile,
                originalSize: file.size, // 保留原始大小用于比较
              };
            } catch (compressError) {
              uni.showToast({
                title: `视频过大且压缩失败 (${formatFileSize(file.size)})`,
                icon: "none",
                duration: 3000,
              });
              return;
            }
          }
        }
        // 视频处理 - 使用chooseMedia返回的缩略图
        // let thumbnailPath = "";
        // if (file.fileType === "video") {
        //   // 微信小程序chooseMedia API会返回视频封面
        //   // #ifdef MP-WEIXIN
        //   thumbnailPath = file.thumbTempFilePath || "";
        //   // #endif
        // }
        tempFiles.value.push({
          path: file.path || file.tempFilePath,
          file,
          size: file.size,
          progress: 0,
          fileType: file.fileType,
          // 如果有缩略图，保存缩略图路径
          // ...(thumbnailPath ? { thumbnailPath } : {}),
        });
        // 立即开始上传这个文件
        startUpload(file.path || file.tempFilePath, file.duration);
      })
    );
  } catch (error) {
    // uni.showToast({ title: "选择文件失败", icon: "none" });
  }
};
// 压缩图片函数
const compressImage = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src: filePath,
      quality: 80, // 压缩质量 (0-100)
      success: (compressedRes) => {
        // 获取压缩后的文件信息
        uni.getFileInfo({
          filePath: compressedRes.tempFilePath,
          success: (infoRes) => {
            resolve({
              path: compressedRes.tempFilePath,
              size: infoRes.size,
            });
          },
          fail: reject,
        });
      },
      fail: reject,
    });
  });
};
/**压缩视频(本地调试时需要集成 ffmpeg) */
const compressVideo = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    uni.compressVideo({
      src: filePath,
      quality: "high", // 压缩质量high medium low
      success: (compressedRes) => {
        // 获取压缩后的文件信息
        uni.getFileInfo({
          filePath: compressedRes.tempFilePath,
          success: (infoRes) => {
            resolve({
              path: compressedRes.tempFilePath,
              size: infoRes.size,
            });
          },
          fail: reject,
        });
      },
      fail: reject,
    });
  });
};

// 文件大小格式化函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
};
// 上传图片
const startUpload = async (filePath: string, duration: number) => {
  const fileIndex = tempFiles.value.findIndex((f) => f.path === filePath);
  if (fileIndex === -1) return;

  try {
    tempFiles.value[fileIndex].status = "uploading";
    const res: any = await uploadFile({
      url: props.url,
      filePath,
      name: "file",
      formData: { type: tempFiles.value[fileIndex].fileType },
      onProgressUpdate: (e: any) => {
        tempFiles.value[fileIndex].progress = e;
      },
    });
    // 保存上传后的文件信息
    tempFiles.value[fileIndex].fileInfo = res;
    tempFiles.value[fileIndex].status = "done";
    // 将duration添加到res对象中
    if (duration && tempFiles.value[fileIndex].fileType === "video") {
      res.duration = duration;
    }
    // 更新contentList
    const newContentList = [...props.contentList, res];
    emits("update:contentList", newContentList);
    emits("change", { contentList: newContentList, type: "add", res });
  } catch (err) {
    tempFiles.value[fileIndex].status = "error";
    // 上传失败时，确保该项不会被添加到contentList中，如果已经添加到contentList中，则移除
    if (tempFiles.value[fileIndex].fileInfo) {
      const contentIndex = props.contentList.findIndex(
        (item) =>
          item.id === tempFiles.value[fileIndex].fileInfo.id ||
          (item.filePath === tempFiles.value[fileIndex].fileInfo.filePath &&
            item.storeName === tempFiles.value[fileIndex].fileInfo.storeName)
      );

      if (contentIndex !== -1) {
        const newContentList = [...props.contentList];
        newContentList.splice(contentIndex, 1);
        emits("update:contentList", newContentList);
        emits("change", {
          contentList: newContentList,
          type: "remove",
          fileInfo: tempFiles.value[fileIndex].fileInfo,
        });
      }
    }
  }
};

// 删除图片
const removeImage = (index: number) => {
  const removedFile = tempFiles.value[index];
  tempFiles.value.splice(index, 1);

  if (removedFile.fileInfo) {
    // 从contentList中找到对应的项并删除
    const contentIndex = props.contentList.findIndex(
      (item) =>
        item.id === removedFile.fileInfo.id ||
        (item.filePath === removedFile.fileInfo.filePath &&
          item.storeName === removedFile.fileInfo.storeName)
    );

    if (contentIndex !== -1) {
      const newContentList = [...props.contentList];
      newContentList.splice(contentIndex, 1);
      emits("update:contentList", newContentList);
      emits("change", {
        contentList: newContentList,
        type: "remove",
        fileInfo: removedFile.fileInfo,
      });
    }
  }
};
// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    current: index,
    urls: tempFiles.value.map((item) => item.path),
  });
};
// 预览视频
const previewVideo = (index: number) => {
  const videoPath = tempFiles.value[index].path;
  // 小程序原生的视频播放-需要真机调试
  // #ifdef MP-WEIXIN
  uni.openVideoEditor({
    filePath: videoPath,
    success: function (res) {},
    fail: function (res) {},
  });
  // #endif
};
</script>
<style lang="scss">
.upload-wrap {
  .upload-content {
    .image-content {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .image-wrap {
        display: flex;
        // 上传按钮尺寸为64px
        .preview-item-normal {
          width: 64px;
          height: 64px;
        }
        .preview-item-small {
          height: 32px;
          width: 32px;
        }
        .preview-item {
          margin: 1px 10px 1px 0px;
          position: relative;
          border-radius: 4px;
          overflow: hidden;
          .preview-img,
          .preview-video {
            width: 100%;
            height: 100%;
            border-radius: 4px;
          }
          .preview-video {
            position: relative;
            .video-thumbnail {
              width: 100%;
              height: 100%;
              border-radius: 4px;
            }
            .video-icon {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              z-index: 4;
            }
          }
          .upload-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            z-index: 2;

            .progress-text {
              color: #fff;
              margin-bottom: 4px;
            }
            .preview-text-normal {
              font-size: 12px;
            }
            .preview-text-small {
              font-size: 6px;
            }
          }
          .delete-wrap {
            width: 100%;
            height: 22px;
            background: rgba(51, 51, 51, 0.5);
            position: absolute;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            // pointer-events: none;
            z-index: 3;
            .delete-btn {
              width: 100%;
              line-height: 22px;
              text-align: center;
              font-size: 12px;
              color: #fff;
              pointer-events: auto;
            }
          }
          view:last-child {
            margin-right: unset;
          }
        }
      }
    }
  }
}
</style>
