<template>
  <view class="upload-wrap">
    <view class="upload-content">
      <view class="image-content">
        <!-- 预览区域 -->
        <view class="image-wrap">
          <view
            class="preview-item"
            v-for="(item, index) in tempFiles"
            :key="index"
          >
            <!-- 图片预览 -->
            <image
              v-if="item.fileType === 'image'"
              class="preview-img"
              :src="item.path"
              mode="aspectFill"
              @click="previewImage(index)"
            />
            <!-- 视频预览 -->
            <view
              v-else-if="item.fileType === 'video'"
              class="preview-video"
              @click="previewVideo(index)"
            >
              <image
                class="video-thumbnail"
                :src="item.path"
                mode="aspectFill"
              />
              <SlSVgIcon name="14-14-24" size="14" class="video-icon"/>
            </view>
            <view class="delete-btn" @click.stop="removeImage(index)"
              >删除</view
            >
          </view>
        </view>
        <!-- 上传按钮 -->
        <view
          v-if="tempFiles.length < maxCount"
          class="upload-btn"
          @click="showActionSheet"
        >
          <SlSVgIcon name="64-64-1" size="64" />
        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
/**图片/视频的上传 */
import { Content } from "@/models/Content";
import { uploadFile } from "@/service/upload.service";
import { ref } from "vue";
// 类型定义
interface TempFile {
  path: string;
  file?: File;
  progress: number;
  status?: "uploading" | "done" | "error";
  size: number;
  fileInfo?: any; // 存储上传后的文件信息
  fileType: "image" | "video";
}
const props = withDefaults(
  defineProps<{
    url?: string;
    maxCount?: number;
    maxSize?: number; // 单位MB
    contentList?: Content[];
  }>(),
  {
    url: "/Content/upload",
    maxCount: 4,
    maxSize: 10, //小程序单文件最大10MB
    contentList: () => [],
  }
);
const emits = defineEmits(["update:contentList", "change"]);
const uploading = ref(false);
const tempFiles = ref<TempFile[]>([]);

// 显示选择器
const showActionSheet = () => {
  uni.showActionSheet({
    itemList: ["拍照(拍摄照片或视频)", "从手机相册选择"],
    success: ({ tapIndex }) => {
      chooseMedia(tapIndex === 0 ? ["camera"] : ["album"]);
    },
  });
};

// 选择图片
const chooseMedia = async (sourceType: any[]) => {
  try {
    const res = await new Promise<any>((resolve, reject) => {
      uni.chooseMedia({
        count: props.maxCount - tempFiles.value.length,
        mediaType: ["image", "video"],
        sourceType,
        maxDuration: 60, // 视频最长时长，单位秒
        sizeType: ["compressed"], // 优先尝试压缩
        success: resolve,
        fail: reject,
      });
    });

    //并行处理所有选中的图片
    await Promise.all(
      res.tempFiles.map(async (file: any) => {
        // 检查文件大小
        if (file.size > props.maxSize * 1024 * 1024) {
          // 对于图片，尝试压缩
          if (file.fileType === "image") {
            try {
              const compressedFile = await compressImage(file.tempFilePath);
              file = {
                ...compressedFile,
                originalSize: file.size, // 保留原始大小用于比较
              };
            } catch (compressError) {
              uni.showToast({
                title: `图片过大且压缩失败 (${formatFileSize(file.size)})`,
                icon: "none",
                duration: 3000,
              });
              return;
            }
          } else {
            // 视频不压缩，直接提示
            uni.showToast({
              title: `视频过大 (${formatFileSize(file.size)})`,
              icon: "none",
              duration: 3000,
            });
            return;
          }
        }

        tempFiles.value.push({
          path: file.tempFilePath,
          file,
          size: file.size, // 记录文件大小
          progress: 0,
          fileType: file.fileType,
        });
        // 立即开始上传这个文件
        startUpload(file.tempFilePath);
      })
    );
  } catch (error) {
    console.error("选择媒体文件失败:", error);
    uni.showToast({ title: "选择文件失败", icon: "none" });
  }
};
// 压缩图片函数
const compressImage = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src: filePath,
      quality: 80, // 压缩质量 (0-100)
      success: (compressedRes) => {
        // 获取压缩后的文件信息
        uni.getFileInfo({
          filePath: compressedRes.tempFilePath,
          success: (infoRes) => {
            resolve({
              path: compressedRes.tempFilePath,
              size: infoRes.size,
            });
          },
          fail: reject,
        });
      },
      fail: reject,
    });
  });
};

// 文件大小格式化函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
};
// 上传图片
const startUpload = async (filePath: string) => {
  const fileIndex = tempFiles.value.findIndex((f) => f.path === filePath);
  if (fileIndex === -1) return;

  try {
    tempFiles.value[fileIndex].status = "uploading";
    const res: any = await uploadFile({
      url: props.url,
      filePath,
      name: "file",
      formData: { type: tempFiles.value[fileIndex].fileType },
      onProgressUpdate: (e: any) => {
        tempFiles.value[fileIndex].progress = e.progress;
      },
    });
    // 保存上传后的文件信息
    tempFiles.value[fileIndex].fileInfo = res;
    tempFiles.value[fileIndex].status = "done";
    // 更新contentList
    const newContentList = [...props.contentList, res];
    emits("update:contentList", newContentList);
    emits("change", { contentList: newContentList, type: "add", res });
  } catch (err) {
    tempFiles.value[fileIndex].status = "error";
  }
};

// 删除图片
const removeImage = (index: number) => {
  const removedFile = tempFiles.value[index];
  tempFiles.value.splice(index, 1);

  if (removedFile.fileInfo) {
    // 从contentList中找到对应的项并删除
    const contentIndex = props.contentList.findIndex(
      (item) =>
        item.id === removedFile.fileInfo.id ||
        (item.filePath === removedFile.fileInfo.filePath &&
          item.storeName === removedFile.fileInfo.storeName)
    );

    if (contentIndex !== -1) {
      const newContentList = [...props.contentList];
      newContentList.splice(contentIndex, 1);
      emits("update:contentList", newContentList);
      emits("change", {
        contentList: newContentList,
        type: "remove",
        fileInfo: removedFile.fileInfo,
      });
    }
  }
};
// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    current: index,
    urls: tempFiles.value.map((item) => item.path),
  });
};
// 预览视频
const previewVideo = (index: number) => {
  const videoPath = tempFiles.value[index].path;
  // 小程序原生的视频播放-需要真机调试
  // #ifdef MP-WEIXIN
  uni.openVideoEditor({
    filePath: videoPath,
    success: function (res) {
      // console.log("视频预览成功", res);
    },
    fail: function (res) {
      console.error("视频预览失败", res, videoPath);
    },
  });
  // #endif
};
</script>
<style lang="scss">
.upload-wrap {
  .upload-content {
    .image-content {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .image-wrap {
        display: flex;
        .preview-item {
          width: 64px;
          height: 64px;
          margin: 1px 10px 1px 0px;
          position: relative;
          .preview-img,
          .preview-video {
            width: 100%;
            height: 100%;
            border-radius: 4px;
          }
          .preview-video {
            position: relative;
            .video-thumbnail {
              width: 100%;
              height: 100%;
              border-radius: 4px;
            }
            .video-icon {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              text-shadow: 0 0 4px rgba(0, 0, 0, 0.8);
            }
          }
          .delete-btn {
            width: 100%;
            text-align: center;
            font-size: 12px;
            color: #fff;
            text-shadow: 0 0 4px rgba(0, 0, 0, 0.8),
              0 0 10px rgba(255, 255, 255, 0.3);
            position: absolute;
            bottom: 0;
          }
          view:last-child {
            margin-right: unset;
          }
        }
      }
    }
  }
}
</style>
