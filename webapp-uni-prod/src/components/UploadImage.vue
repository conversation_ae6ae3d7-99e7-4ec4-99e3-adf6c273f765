<template>
	<view
		v-if="modelValue.dishId"
		class="row img-box"
	>
		<text class="label">{{ imageTitle }}</text>
		<view class="img-info-wrap">
			<ImageComponent
				:src="modelValue?.imageUrl || imageUrl"
				:size="110"
			></ImageComponent>
		</view>
	</view>
	<view class="row handle-img">
		<view class="upload-wrap">
			<header class="header">
				<view class="title">{{ modelValue.dishId ? '更改图片' : '菜品图片' }}</view>
				<view class="opt-btns">
					<view
						class="opt-btn"
						v-for="(item, index) in optBtns"
						:key="index"
						@click="onOptClick(item)"
					>
						<SlSVgIcon
							v-if="item.active"
							name="12-12-3"
							size="12"
						/>
						<SlSVgIcon
							v-else
							name="12-12-4"
							size="12"
						/>
						<text class="name">{{ item.name }}</text>
					</view>
				</view>
			</header>
			<view
				class="upload-content"
				v-if="optBtns[0].active"
			>
				<view class="image-content">
					<template v-if="tempFiles.length > 0">
						<view class="image-wrap">
							<up-swiper
								:list="tempFileUrls"
								previousMargin="60"
								nextMargin="60"
								circular
								:current="currFileIndex"
								:autoplay="false"
								radius="3"
								bgColor="#ffffff"
								@click="onPreviewImage"
								@change="onChangeThumb"
							></up-swiper>
							<view
								:style="{ width: deleteBtnWidth }"
								class="delete-btn"
								@click="deleteImage"
								>删除</view
							>
						</view>
						<!-- 上传按钮 -->
						<view
							v-if="tempFiles.length < maxCount"
							class="upload-btn"
							@click="showActionSheet"
						>
							<SlSVgIcon
								name="34-34-10"
								size="34"
							/>
						</view>
					</template>
					<template v-else>
						<SlSVgIcon
							name="110-110-1"
							size="110"
							@click="showActionSheet"
						/>
					</template>
				</view>
				<BaseButton
					v-if="tempFiles.length > 0 && modelValue.dishId"
					class="comfirm"
					btnType="cancel"
					:btnStyle="btnStyle"
					@click="replaceImage()"
					>替换</BaseButton
				>
			</view>
			<view
				class="ai-content"
				v-else
			>
				<view class="img-wrap">
					<image
						class="ai-img"
						v-if="httpAiData.imageUrl"
						:src="httpAiData.imageUrl"
						mode="aspectFill"
					></image>
					<view
						class="ai-tip"
						v-else
					>
						<SlSVgIcon
							class="ai-tip-border"
							name="110-110-2"
							size="110"
						/>
						<SlSVgIcon
							class="ai-loading"
							v-if="isAiImgGenerating"
							name="60-60-1"
							size="60"
						></SlSVgIcon>
						<text v-else>默认以菜名生成</text>
					</view>
				</view>
				<view class="ai-btns">
					<BaseButton
						class="ai-btn"
						btnType="save"
						:btnStyle="btnStyle"
						@click="onGenerateAIImg()"
						>{{ generateBtnText }}</BaseButton
					>
					<BaseButton
						class="ai-btn replace"
						:btnType="!httpAiData.imageUrl ? 'replace' : 'replace2'"
						:btnStyle="btnStyle"
						@click="onReplaceAIImg()"
						>替换
					</BaseButton>
				</view>
			</view>
		</view>
	</view>
</template>
<script setup lang="ts">
	import ImageComponent from '@/components/ImageComponent.vue';
	import { deleteDishImage, generateDishAIImage, uploadFile } from '@/service/upload.service';
	import { toRpx } from '@/utils/toRpx';
	import { ref } from 'vue';
	// 类型定义
	interface TempFile {
		path: string;
		id?: string;
		file?: File;
		progress?: number;
		status?: 'uploading' | 'done' | 'error';
		size?: number;
	}

	interface Images {
		id?: string;
		dishId?: string;
		imageUrl?: string;
		imageType?: 'ai' | 'photo';
	}
	export interface ImageInfo {
		imageId?: string;
		imageUrl?: string;
		images?: Images[];
		dishName?: string;
		dishId?: string;
	}

	const btnStyle = computed(() => {
		return !!httpAiData.value.imageUrl
			? { width: '70px', height: '28px', lineHeight: '28px', 'font-size': '14px', padding: 0 }
			: { width: '60px', height: '28px', lineHeight: '28px', 'font-size': '14px', padding: 0 };
	});
	const optBtns = ref([
		{ name: '上传图片', active: true },
		{ name: 'AI生成', active: false },
	]);
	const generateBtnText = computed(() => {
		return isAiImgGenerating.value ? '暂停' : !!httpAiData.value.imageUrl ? '重新生成' : '生成';
	});
	const isAiImgGenerating = ref(false);
	const httpAiData = ref({ id: '', imageUrl: '' });
	// 存储请求控制器
	const aiImageRequest = ref<ReturnType<typeof generateDishAIImage> | null>(null);

	const props = withDefaults(
		defineProps<{
			modelValue?: ImageInfo;
			url?: string; //后端api
			imageTitle?: string;
			changeTitle?: string;
			maxCount?: number;
			maxSize?: number; //超过多少MB时压缩
			immediate?: boolean; //是否选择图片后立即上传
		}>(),
		{
			url: '/dishimage/upload',
			modelValue: () => ({}),
			imageTitle: '菜品图片',
			changeTitle: '更改图片',
			maxCount: 9,
			maxSize: 1,
			immediate: false,
		}
	);

	const emit = defineEmits(['update:modelValue']);
	const httpImageIds = ref<string[]>([]);
	const imageUrl = ref('');
	const currFileIndex = ref(0);
	const tempFiles = ref<TempFile[]>([]);
	const tempFileUrls = computed(() => {
		return tempFiles.value.map((item) => item.path);
	});
	const deleteBtnWidth = computed(() => {
		return toRpx(110);
	});

	watch(
		() => props.modelValue.images,
		(newVal) => {
			(tempFiles.value as any) =
				newVal &&
				newVal.map((item) => {
					return {
						...item,
						path: item.imageUrl,
						status: 'done',
					};
				});

			httpImageIds.value = newVal ? newVal.map((item) => item.id).filter((id): id is string => id != null) : [];
			const sortedTempFiles = [...tempFiles.value].sort((a) => (a.path === props.modelValue.imageUrl ? -1 : 0));
			tempFiles.value = sortedTempFiles;

			imageUrl.value = props.modelValue.imageUrl || '';
		},
		{ once: true }
	);

	function onChangeThumb(e: any) {
		currFileIndex.value = e.current;
	}

	// 预览图片
	const onPreviewImage = (e: any) => {
		uni.previewImage({
			current: currFileIndex.value,
			urls: tempFiles.value.map((item) => item.path),
		});
	};

	// 显示选择器
	const showActionSheet = () => {
		uni.showActionSheet({
			itemList: ['拍照', '从手机相册选择'],
			success: async ({ tapIndex }) => {
				(await tapIndex) === 0 ? chooseMedia(['camera']) : chooseImage(tapIndex === 0 ? ['camera'] : ['album']);
			},
		});
	};

	const chooseMedia = async (sourceType: any[]) => {
		try {
			const res = await new Promise<any>((resolve, reject) => {
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sourceType: sourceType,
					sizeType: ['compressed'], // 使用压缩选项
					camera: 'back', // 默认后置摄像头
					success: resolve,
					fail: reject,
				});
			});
			handleChooseImages(res, 'tempFilePath');
		} catch (error) {
			uni.showToast({ title: '选择图片失败', icon: 'none' });
		}
	};

	//并行处理所有选中的图片
	const handleChooseImages = async (datas: any, pathKey: 'path' | 'tempFilePath' = 'path') => {
		await Promise.all(
			datas.tempFiles.map(async (file: any) => {
				// 检查文件大小
				if (file.size > props.maxSize * 1024 * 1024) {
					// 先尝试压缩大文件
					try {
						const compressedFile = await compressImage(file[pathKey]);
						file = {
							...compressedFile,
							originalSize: file.size, // 保留原始大小用于比较
						};
						return;
					} catch (compressError) {
						uni.showToast({
							title: `图片过大且压缩失败 (${formatFileSize(file.size)})`,
							icon: 'none',
							duration: 3000,
						});
						return;
					}
				}

				tempFiles.value.push({
					path: file[pathKey],
					file,
					size: file.size, // 记录文件大小
					progress: 0,
				});
				uploadImages();
			})
		);
	};

	// 选择图片- 添加压缩功能
	const chooseImage = async (sourceType: any[]) => {
		try {
			const res = await new Promise<any>((resolve, reject) => {
				uni.chooseImage({
					count: props.maxCount - tempFiles.value.length,
					sizeType: ['compressed'], // 优先尝试压缩
					sourceType,
					success: resolve,
					fail: reject,
				});
			});

			handleChooseImages(res);
		} catch (error) {
			uni.showToast({ title: '选择图片失败', icon: 'none' });
		}
	};

	// 压缩图片函数
	const compressImage = (filePath: string): Promise<any> => {
		return new Promise((resolve, reject) => {
			uni.compressImage({
				src: filePath,
				quality: 80, // 压缩质量 (0-100)
				success: (compressedRes) => {
					// 获取压缩后的文件信息
					uni.getFileInfo({
						filePath: compressedRes.tempFilePath,
						success: (infoRes) => {
							resolve({
								path: compressedRes.tempFilePath,
								size: infoRes.size,
							});
						},
						fail: reject,
					});
				},
				fail: reject,
			});
		});
	};

	// 文件大小格式化函数
	const formatFileSize = (bytes: number): string => {
		if (bytes === 0) return '0B';
		const k = 1024;
		const sizes = ['B', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
	};

	// 上传图片 - 只上传中间项
	const startUpload = async (filePath: string) => {
		const fileIndex = tempFiles.value.findIndex((f) => f.path === filePath);
		if (fileIndex === -1) return;

		try {
			tempFiles.value[fileIndex].status = 'uploading';
			const res = await uploadFile({
				url: props.url,
				filePath,
				name: 'file',
				formData: { type: 'image' },
				onProgressUpdate: (e: any) => {
					const index = tempFiles.value.findIndex((f) => f.path === filePath);
					if (index !== -1) {
						tempFiles.value[index].progress = e.progress;
					}
				},
			});
			tempFiles.value[fileIndex].status = 'done';
			return res; // 返回上传成功的URL
		} catch (err) {
			tempFiles.value[fileIndex].status = 'error';
			throw err; // 抛出错误
		}
	};

	function onOptClick(item: any) {
		if (item.name === optBtns.value.find((btn) => btn.active)?.name) {
			return;
		}
		optBtns.value.forEach((btn) => {
			btn.active = !btn.active;
		});
		//处理
		cancelRequest();
		isAiImgGenerating.value = false;
	}

	// 删除图片
	const deleteImage = async () => {
		try {
			await deleteDishImage(httpImageIds.value[currFileIndex.value]);
			tempFiles.value.splice(currFileIndex.value, 1);
			httpImageIds.value.splice(currFileIndex.value, 1);
			currFileIndex.value -= 1;
			if (!tempFiles.value.map((item: any) => item.path).includes(imageUrl.value)) {
				imageUrl.value = tempFiles.value[currFileIndex.value].path;
			}
			emit('update:modelValue', {
				...props.modelValue,
				imageId: httpImageIds.value[currFileIndex.value],
				imageUrl: '',
				images: httpImageIds.value.map((id: any) => ({ id, imageUrl: '' })),
			});
		} catch (error) {
			uni.showToast({ title: '删除图片失败', icon: 'none' });
		}
	};

	function replaceImage() {
		if (imageUrl.value === tempFiles.value[currFileIndex.value].path) return;
		imageUrl.value = tempFiles.value[currFileIndex.value].path;
		emit('update:modelValue', {
			...props.modelValue,
			imageId: httpImageIds.value[currFileIndex.value],
			imageUrl: '',
			images: httpImageIds.value.map((id: any) => ({ id, imageUrl: '' })),
		});
		uni.showToast({
			title: '替换成功',
			icon: 'none',
			duration: 1500,
		});
	}

	async function uploadImages() {
		if (!tempFiles.value.length) {
			uni.showToast({ title: '请先选择图片', icon: 'none' });
			return;
		}

		try {
			uni.showLoading({ title: '上传中...', mask: true });

			// 并行上传所有未完成的图片
			const uploadPromises = tempFiles.value.filter((file) => file.status !== 'done').map((file) => startUpload(file.path));

			const urls: any = await Promise.all(uploadPromises);
			if (urls.length) {
				httpImageIds.value.push(...urls);
			}
			if (!props.modelValue.dishId) {
				emit('update:modelValue', {
					...props.modelValue,
					imageId: httpImageIds.value[0],
					imageUrl: '',
					images: httpImageIds.value.map((id: any) => ({ id, imageUrl: '' })),
				});
			} else {
				httpImageIds.value.forEach((id: any) => {
					if (!props.modelValue.images?.find((image: any) => image.id === id)) {
						props.modelValue.images?.push({ id, imageUrl: '' });
					}
				});
				emit('update:modelValue', {
					...props.modelValue,
				});
			}
		} catch (err) {
			console.error(err);
		} finally {
			uni.hideLoading();
		}
	}

	async function onGenerateAIImg() {
		// 取消可能存在的旧请求
		cancelRequest();
		httpAiData.value = { id: '', imageUrl: '' };
		if (isAiImgGenerating.value) {
			isAiImgGenerating.value = false;
			return;
		}
		//生成
		if (!props.modelValue?.dishName) {
			uni.showToast({
				title: '请先填写菜名',
				icon: 'none',
				duration: 1500,
			});
			return;
		} else {
			isAiImgGenerating.value = true;
			aiImageRequest.value = await generateDishAIImage(props.modelValue);
			aiImageRequest.value.promise
				.then((data) => {
					console.log('图片生成成功', data);
					aiImageRequest.value = null;
					httpAiData.value = data;
					isAiImgGenerating.value = false;
				})
				.catch((err) => {
					if (err.errMsg !== 'request:fail abort') {
						console.error('图片生成失败', err);
					}
					aiImageRequest.value = null;
					isAiImgGenerating.value = false;
				});
		}
	}
	// 取消请求
	const cancelRequest = () => {
		if (aiImageRequest.value) {
			aiImageRequest.value.abort();
			aiImageRequest.value = null;
		}
	};
	async function onReplaceAIImg() {
		if (!httpAiData.value.id) {
			return;
		}
		httpImageIds.value.push(httpAiData.value.id);
		tempFiles.value.push({
			path: httpAiData.value.imageUrl,
			status: 'done',
		});
		currFileIndex.value += 1;
		replaceImage();
		httpAiData.value = { id: '', imageUrl: '' };
	}

	onMounted(() => {
		cancelRequest();
	});
</script>
<style lang="scss">
	.row {
		background: #fff;
		display: flex;
		box-shadow: 0px 0px 4px 0px #92929233;
		margin-bottom: 6px;
		padding: 0 15px;
	}

	.img-box {
		padding-top: 17px;
		padding-bottom: 17px;

		.label {
			padding-right: 34px;
		}

		.img-info-wrap {
			width: 110px;
			height: 110px;
		}
	}

	.handle-img {
		display: block;
		padding: 0;
	}

	.upload-wrap {
		background: #fff;
		padding: 10px 15px;
		height: 257px;
		position: relative;

		.header {
			width: 100%;
			display: flex;
			justify-content: space-between;

			.opt-btns {
				display: flex;

				.opt-btn {
					margin-left: 18px;

					.name {
						font-size: 12px;
						padding-left: 2px;
					}
				}
			}
		}

		.upload-content {
			.image-content {
				padding: 30px 15px;
				display: flex;
				align-items: center;
				justify-content: center;

				.image-wrap {
					flex: 1;
					max-width: 210px;
					height: 110px;
					overflow: hidden;
					position: relative;

					.delete-btn {
						height: 30px;
						line-height: 30px;
						text-align: center;
						background: #3333337f;
						color: #fff;
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						border-radius: 0 0 3px 3px;
					}

					.preview-item {
						width: 100px;
						height: 100px;
						position: relative;

						.preview-img {
							width: 100px;
							height: 100px;
							border-radius: 3px;
							opacity: 0.6000000238418579;
						}

						&.middle-item {
							width: 110px;
							height: 110px;

							.preview-img {
								width: 110px;
								height: 110px;
								opacity: 1;
							}
						}
					}
				}

				.upload-btn {
					position: absolute;
					right: 15px;
				}

				.comfirm {
					margin-top: 34px;
					margin-bottom: 30px;
				}
			}
		}

		.ai-content {
			padding-top: 30px;

			.img-wrap {
				text-align: center;

				.ai-tip {
					width: 110px;
					height: 110px;
					font-size: 12px;
					text-align: center;
					color: #999999;
					display: inline-flex;
					justify-content: center;
					align-items: center;
					position: relative;
					.ai-tip-border {
						position: absolute;
						z-index: 1;
						left: 0;
						top: 0;
					}
					text,
					.ai-loading {
						z-index: 2;
					}
					.ai-loading {
						width: 60px;
						height: 60px;
						animation: rotate 3s linear infinite;
					}
				}

				.ai-img {
					width: 110px;
					height: 110px;
				}
			}

			.ai-btns {
				display: flex;
				justify-content: center;
				margin: 30px 0;
				position: relative;

				.ai-btn {
					&.replace {
						position: absolute;
						right: 0;
						top: 50%;
						transform: translateY(-50%);
					}
				}
			}
		}
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}
</style>
