<template>
    <view class="upload-wrap">
        <header class="header">
            <view class="title">{{title}}</view>
            <view class="opt-btns">
                <view class="opt-btn" v-for="item in optBtns" @click="onOptClick(item)">
                    <SlSVgIcon v-if="item.active" name="12-12-3" size="12" />
                    <SlSVgIcon v-else name="12-12-4" size="12" />
                    <text class="name">{{item.name}}</text>
                </view>
            </view>
        </header>
        <view class="upload-content"  v-if="optBtns[0].active">
            <view class="image-content">
                <template v-if="tempFiles.length > 0">
                <view class="image-wrap">
                    <up-swiper :list="tempFileUrls" previousMargin="60" nextMargin="60" circular :autoplay="false" radius="3" bgColor="#ffffff" @change="onChangeThumb"></up-swiper>
                      <view :style="{width: deleteBtnWidth}" class="delete-btn" @click="removeImage">删除</view>
                </view>
                <!-- 上传按钮 -->
                <view v-if="tempFiles.length < maxCount" class="upload-btn" @click="showActionSheet">
                  <SlSVgIcon name="34-34-10" size="34" />
                </view>
                </template>
                <template v-else>
                  <SlSVgIcon name="34-34-10" size="110" @click="showActionSheet" />
                </template>
            </view>
            <BaseButton v-if="tempFiles.length>0 &&!immediate" class="comfirm" btnType="cancel" :btnStyle="btnStyle" @click="onComfirm()">替换</BaseButton>
        </view>
        <view class="ai-content" v-else>
          <view class="img-wrap">
              <image class="ai-img" v-if="aiImg" :src="aiImg"></image>
              <view class="ai-tip" v-else>
                默认以菜名生成
              </view>
          </view>
          <view class="ai-btns">
              <BaseButton class="ai-btn" btnType="save" :btnStyle="btnStyle">生成</BaseButton>
              <BaseButton v-if="aiImg" class="ai-btn" btnType="delete" :btnStyle="btnStyle" @click="onComfirm()">替换</BaseButton>
          </view>
        </view>
    </view>
</template>
<script setup lang="ts">
import { uploadFile } from '@/service/upload.service';
import { toRpx } from '@/utils/toRpx';
import { ref } from 'vue';
    // 类型定义
    interface TempFile {
      path: string
      file?: File
      progress: number
      status?: 'uploading' | 'done' | 'error',
      size: number
    }
    const btnStyle = {width:'60px',height: '28px','line-height': '28px','font-size': '14px'}
    const optBtns = ref([{name: '上传图片',active: true},{name: 'AI生成',active: false}]);
    const aiImg = ref('');

    const props = withDefaults(defineProps<{
        url?: string
        title?: string
        maxCount?: number
        maxSize?: number //超过多少MB时压缩
        immediate?: boolean //是否选择图片后立即上传
        }>(), {
            url: '/dishimage/upload',
            title: '更改图片',
            maxCount: 9,
            maxSize: 1,
            immediate: false
    })
const uploading = ref(false)
const emits = defineEmits(['change'])

const currFileIndex = ref(0)
const tempFiles = ref<TempFile[]>([])
const tempFileUrls = computed(() => {
  return tempFiles.value.map(item => item.path)
})
const deleteBtnWidth = computed(() => {
  return toRpx(110)
})

function onChangeThumb(e: any) {
  currFileIndex.value = e.current
}

// 显示选择器
const showActionSheet = () => {
  uni.showActionSheet({
    itemList: ['拍照', '从手机相册选择'],
    success: async ({ tapIndex }) => {
      await chooseImage(tapIndex === 0 ? ['camera'] : ['album'])
      if(props.immediate) {
        onComfirm()
      }
    }
  })
}

    // 选择图片- 添加压缩功能
const chooseImage = async (sourceType: any[]) => {
  try {
    const res = await new Promise<any>((resolve, reject) => {
      uni.chooseImage({
        count: props.maxCount - tempFiles.value.length,
        sizeType: ['compressed'], // 优先尝试压缩
        sourceType,
        success: resolve,
        fail: reject
      })
    })

    //并行处理所有选中的图片
    await Promise.all(res.tempFiles.map(async (file: any) => {
      // 检查文件大小
      if (file.size > props.maxSize * 1024 * 1024) {
        // 先尝试压缩大文件
        try {
          const compressedFile = await compressImage(file.path)
          file = {
            ...compressedFile,
            originalSize: file.size // 保留原始大小用于比较
          }
        }catch (compressError) {
          uni.showToast({
            title: `图片过大且压缩失败 (${formatFileSize(file.size)})`,
            icon: 'none',
            duration: 3000
          })
          return
        }
      }

      tempFiles.value.push({
        path: file.path,
        file,
        size: file.size, // 记录文件大小
        progress: 0
      })
    }))
  }catch (error) {
    uni.showToast({ title: '选择图片失败', icon: 'none' })
  }
}

// 压缩图片函数
const compressImage = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src: filePath,
      quality: 80, // 压缩质量 (0-100)
      success: (compressedRes) => {
        // 获取压缩后的文件信息
        uni.getFileInfo({
          filePath: compressedRes.tempFilePath,
          success: (infoRes) => {
            resolve({
              path: compressedRes.tempFilePath,
              size: infoRes.size
            })
          },
          fail: reject
        })
      },
      fail: reject
    })
  })
}

// 文件大小格式化函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i]
}


 // 上传图片 - 只上传中间项
const startUpload = async (filePath: string) => {
  const fileIndex = tempFiles.value.findIndex(f => f.path === filePath)
  if (fileIndex === -1) return

  try {
    tempFiles.value[fileIndex].status = 'uploading'
    const res = await uploadFile({
      url: props.url,
      filePath,
      name: 'file',
      formData: { type: 'image' },
      onProgressUpdate: (e: any) => {
        tempFiles.value[fileIndex].progress = e.progress
      }
    })
    tempFiles.value[fileIndex].status = 'done'
    return res // 返回上传成功的URL
  } catch (err) {
    tempFiles.value[fileIndex].status = 'error'
    throw err // 抛出错误
  }
}

function onOptClick(item: any) {
  optBtns.value.forEach(btn => {
    btn.active = !btn.active
  })
}

// 删除图片
const removeImage = () => {
  tempFiles.value.splice(currFileIndex.value, 1)
}

// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    current: index,
    urls: tempFiles.value.map(item => item.path)
  })
}

async function onComfirm() {
  if (!tempFiles.value.length) {
    uni.showToast({ title: '请先选择图片', icon: 'none' })
    return
  }
  const item = tempFiles.value[currFileIndex.value]

  // 如果中间项已经上传过，直接使用
  if (item.status === 'done') {
    // 假设URL已经存在
    emits('change', { url: item.path,file:item })
    return
  }
  try {
    uploading.value = true
    uni.showLoading({ title: '上传中...', mask: true })

    // 上传中间项
    const url = await startUpload(item.path)

    // 上传成功后触发事件
    emits( 'change', { url,file:item } )
    uni.showToast({
            title: '替换成功',
            icon: 'none',
            duration: 1500
          })

  } catch (err) {
    uni.showToast({
            title: '替换失败',
            icon: 'none',
            duration: 1500
          })
  } finally {
    uploading.value = false
    uni.hideLoading()
  }
}
</script>
<style lang="scss">
.upload-wrap {
  background: #fff;
  padding: 23.4375rpx 35.15625rpx;
    .header {
      width: 100%;
      display: flex;
      height: 20px;
      justify-content: space-between;
      .opt-btns {
        display: flex;
        .opt-btn {
          margin-left: 18px;
          .name {
            font-size: 12px;
            padding-left: 2px;
          }
        }
      }
    }
    .upload-content {
      .image-content {
        padding: 30px 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        .image-wrap {
            flex: 1;
            max-width: 210px;
            overflow: hidden;
            position: relative;
            .delete-btn {
              height: 30px;
              line-height: 30px;
              text-align: center;
              background: #3333337F;
              color: #fff;
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              border-radius: 0 0 3px 3px;
            }
          .preview-item {
            width: 100px;
            height: 100px;
            position: relative;
            .preview-img {
              width: 100px;
              height: 100px;
              border-radius: 3px;
              opacity: 0.6000000238418579;
            }
            &.middle-item {
              width: 110px;
              height: 110px;
              .preview-img {
              width: 110px;
              height: 110px;
              opacity: 1;
            }
            }
          }
        }
        .upload-btn {
          margin-left: 10px;
        }
        .comfirm {
          margin-top: 34px;
          margin-bottom: 30px;
        }
      }
    }
    .ai-content {
      padding-top: 34px;
      .img-wrap {
        text-align: center;
        .ai-tip {
          width: 110px;
          height: 110px;
          line-height: 110px;
          font-size: 12px;
          text-align: center;
          color: #999999;
          border: 1px dashed #999999;
          display: inline-block;
        }
      }
      .ai-btns {
        display: flex;
        justify-content: flex-end;
        margin: 30px 0;
        .ai-btn {
          margin-left: 55px;
        }
      }
    }
}
</style>