<template>
	<view
		v-if="modelValue.dishId"
		class="row img-box"
	>
		<text class="label">{{ imageTitle }}</text>
		<view
			class="img-info-wrap"
			@click="onPreviewDishImage(modelValue?.imageUrl || imageUrl)"
		>
			<ImageComponent
				:src="modelValue?.imageUrl || imageUrl"
				:size="110"
				:isLazyLoad="false"
			></ImageComponent>
		</view>
	</view>
	<view class="row handle-img">
		<view class="upload-wrap">
			<header class="header">
				<view class="title">{{ modelValue.dishId ? '更改图片' : '菜品图片' }}</view>
				<view class="opt-btns">
					<view
						class="opt-btn"
						v-for="(item, index) in optBtns"
						:key="index"
						@click="onOptClick(item)"
					>
						<SlSVgIcon
							v-if="item.active"
							name="12-12-3"
							size="12"
						/>
						<SlSVgIcon
							v-else
							name="12-12-4"
							size="12"
						/>
						<text class="name">{{ item.name }}</text>
					</view>
				</view>
			</header>
			<view
				class="upload-content"
				v-if="optBtns[0].active"
			>
				<view class="image-content">
					<template v-if="tempFiles.length > 0">
						<view class="image-wrap">
							<Swiper
								:list="tempFileUrls"
								:current="currFileIndex"
								@change="onChangeThumb"
								@click="onPreviewImage"
							></Swiper>
							<view
								class="delete-btn"
								@click="deleteImage"
								>删除</view
							>
						</view>
						<!-- 上传按钮 -->
						<view
							class="upload-btn"
							@click="showActionSheet"
						>
							<SlSVgIcon
								name="34-34-10"
								size="34"
							/>
						</view>
					</template>
					<template v-else>
						<SlSVgIcon
							name="110-110-1"
							size="110"
							@click="showActionSheet"
						/>
					</template>
				</view>
				<BaseButton
					v-if="tempFiles.length > 0 && modelValue.dishId"
					class="comfirm"
					btnType="cancel"
					:btnStyle="btnStyle"
					@click="replaceImage()"
					>替换</BaseButton
				>
			</view>
			<view
				class="ai-content"
				v-else
			>
				<view class="img-wrap">
					<image
						class="ai-img"
						v-if="httpAiData.imageUrl"
						:src="httpAiData.imageUrl"
						mode="aspectFill"
						@click="onPreviewAIImage()"
					></image>
					<view
						class="ai-tip"
						@click="onPreviewAIImage()"
						v-else
					>
						<SlSVgIcon
							class="ai-tip-border"
							name="110-110-2"
							size="110"
						/>
						<SlSVgIcon
							class="ai-loading"
							v-if="isAiImgGenerating"
							name="60-60-1"
							size="60"
						></SlSVgIcon>
						<text v-else>默认以菜名生成</text>
					</view>
				</view>
				<view class="ai-btns">
					<BaseButton
						class="ai-btn"
						btnType="save"
						:btnStyle="btnStyle"
						@click="onGenerateAIImg()"
						>{{ generateBtnText }}</BaseButton
					>
					<BaseButton
						class="ai-btn replace"
						v-if="modelValue.dishId"
						:btnType="!httpAiData.imageUrl ? 'replace' : 'replace2'"
						:btnStyle="btnStyle"
						@click="onReplaceAIImg()"
						>替换
					</BaseButton>
				</view>
			</view>
		</view>
	</view>
</template>
<script setup lang="ts">
	import ImageComponent from '@/components/ImageComponent.vue';
	import useDevicePermission from '@/hooks/use-device-permission';
	import { batchUploadDishImage, generateDishAIImage, uploadFile } from '@/service/upload.service';
	import { toRpx } from '@/utils/toRpx';
	import { ref } from 'vue';
	const { checkPermission, requestPermission, checkReadAlbumPermission } = useDevicePermission();
	// 类型定义
	interface TempFile {
		path: string;
		id?: string;
		file?: File;
		progress?: number;
		status?: 'uploading' | 'done' | 'error';
		size?: number;
		fileInfo?: any;
	}

	interface Images {
		id?: string;
		dishId?: string;
		imageUrl?: string;
		imageType?: 'ai' | 'photo';
	}
	export interface ImageInfo {
		imageId?: string;
		imageUrl?: string;
		images?: Images[];
		dishName?: string;
		dishId?: string;
		notRefresh?: boolean;
	}

	const btnStyle = computed(() => {
		return !!httpAiData.value.imageUrl
			? { width: '70px', height: '28px', lineHeight: '28px', 'font-size': '14px', padding: 0 }
			: { width: '60px', height: '28px', lineHeight: '28px', 'font-size': '14px', padding: 0 };
	});
	const optBtns = ref([
		{ id: 'img', name: '上传图片', active: true },
		{ id: 'ai', name: 'AI生成', active: false },
	]);
	const generateBtnText = computed(() => {
		return isAiImgGenerating.value ? '暂停' : !!httpAiData.value.imageUrl ? '重新生成' : '生成';
	});
	const isAiImgGenerating = ref(false);
	const httpAiData = ref({ id: '', imageUrl: '' });
	// 存储请求控制器
	const aiImageRequest = ref<ReturnType<typeof generateDishAIImage> | null>(null);

	const props = withDefaults(
		defineProps<{
			modelValue?: ImageInfo;
			url?: string; //后端api
			url2?: string; //后端api
			imageTitle?: string;
			changeTitle?: string;
			maxCount?: number;
			maxSize?: number; //超过多少MB时压缩
		}>(),
		{
			url: '/dishimage/upload',
			url2: '/dishimage/upload',
			modelValue: () => ({}),
			imageTitle: '菜品图片',
			changeTitle: '更改图片',
			maxCount: 1,
			maxSize: 1,
		}
	);

	const emit = defineEmits(['update:modelValue']);
	const imageUrl = ref('');
	const currFileIndex = ref(0);
	const tempFiles = ref<TempFile[]>([]);
	const tempFileUrls = computed(() => {
		return tempFiles.value.map((item) => {
			if (item.path) return item.path;
		});
	});
	const httpImageIds = computed(() => {
		return tempFiles.value.map((item) => item.id || '');
	});
	const deleteBtnWidth = computed(() => {
		return toRpx(110);
	});

	function isNotEmpty(obj: any) {
		return !!obj && JSON.stringify(obj) !== '{}';
	}

	watch(
		() => props.modelValue,
		(newVal) => {
			if (!isNotEmpty(newVal)) {
				return;
			}
			console.log('newVal', newVal);

			// 如果已经初始化过，直接返回
			if (newVal.notRefresh) return;
			const _images = newVal?.images || [];
			let _tempFiles: any = [];
			// 只初始化未存在的文件
			_images.forEach((item) => {
				if (!tempFiles.value.some((f) => f.id === item.id)) {
					_tempFiles.push({
						...item,
						path: item.imageUrl,
						status: 'done',
					});
				}
			});

			tempFiles.value = [..._tempFiles].sort((a) => (a.path === props.modelValue.imageUrl ? -1 : 0)).filter((item) => !!item.id);
			imageUrl.value = props.modelValue.imageUrl || '';
			currFileIndex.value = 0;
		},
		{ immediate: true } // 确保组件创建时立即执行
	);

	function onChangeThumb(e: any) {
		currFileIndex.value = e.current;
	}
	//预览菜品图片
	const onPreviewDishImage = (imageUrl: string) => {
		uni.previewImage({
			current: 0,
			urls: [imageUrl],
		});
	};
	// 预览图片
	const onPreviewImage = (e: any) => {
		uni.previewImage({
			current: currFileIndex.value,
			urls: tempFiles.value.map((item) => item.path),
		});
	};
	const onPreviewAIImage = () => {
		uni.previewImage({
			current: 0,
			urls: [httpAiData.value.imageUrl],
		});
	};

	// 显示选择器
	const showActionSheet = () => {
		uni.showActionSheet({
			itemList: ['拍照', '从手机相册选择'],
			success: async ({ tapIndex }) => {
				await handlePermission(tapIndex === 0 ? 'camera' : 'album');
				tapIndex === 0 ? await chooseMedia(['camera']) : await chooseImage(['album']);
			},
		});
	};

	const chooseMedia = async (sourceType: any[]) => {
		try {
			const res = await new Promise<any>((resolve, reject) => {
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sourceType: sourceType,
					sizeType: ['compressed'], // 使用压缩选项
					camera: 'back', // 默认后置摄像头
					success: resolve,
					fail: reject,
				});
			});
			handleChooseImages(res, 'tempFilePath');
		} catch (error) {}
	};

	//并行处理所有选中的图片
	const handleChooseImages = async (datas: any, pathKey: 'path' | 'tempFilePath' = 'path') => {
		await Promise.all(
			datas.tempFiles.map(async (file: any) => {
				// 检查文件大小
				if (file.size > props.maxSize * 1024 * 1024) {
					// 先尝试压缩大文件
					try {
						const compressedFile = await compressImage(file[pathKey]);
						file = {
							...compressedFile,
							originalSize: file.size, // 保留原始大小用于比较
						};
						return;
					} catch (compressError) {
						uni.showToast({
							title: `图片过大且压缩失败 (${formatFileSize(file.size)})`,
							icon: 'none',
							duration: 3000,
						});
						return;
					}
				}

				tempFiles.value.push({
					path: file[pathKey],
					file,
					size: file.size, // 记录文件大小
					progress: 0,
				});
				uploadImages();
			})
		);
	};

	// 选择图片- 添加压缩功能
	const chooseImage = async (sourceType: any[]) => {
		try {
			const res = await new Promise<any>((resolve, reject) => {
				uni.chooseMedia({
					count: props.maxCount,
					sizeType: ['compressed'], // 优先尝试压缩
					sourceType,
					success: resolve,
					fail: reject,
				});
			});
			handleChooseImages(res, 'tempFilePath');
		} catch (error) {}
	};

	// 压缩图片函数
	const compressImage = (filePath: string): Promise<any> => {
		return new Promise((resolve, reject) => {
			uni.compressImage({
				src: filePath,
				quality: 80, // 压缩质量 (0-100)
				success: (compressedRes) => {
					// 获取压缩后的文件信息
					console.log('压缩后', compressedRes);
					uni.getFileInfo({
						filePath: compressedRes.tempFilePath,
						success: (infoRes) => {
							resolve({
								path: compressedRes.tempFilePath,
								size: infoRes.size,
							});
						},
						fail: reject,
					});
				},
				fail: reject,
			});
		});
	};

	// 文件大小格式化函数
	const formatFileSize = (bytes: number): string => {
		if (bytes === 0) return '0B';
		const k = 1024;
		const sizes = ['B', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
	};

	//批量上传
	const startBatchUpload = async (files: any) => {
		console.log(tempFiles.value);
		try {
			const res = await batchUploadDishImage(
				files.map((f: any) => {
					return { filePath: f.path, fileSize: f.size, fileType: 'image' };
				})
			);
			console.log('批量上传', res);
		} catch (error) {
			uni.showToast({ title: '上传图片失败', icon: 'none' });
		}
	};

	const startUpload = (filePath: string): Promise<any> => {
		return new Promise(async (resolve, reject) => {
			const fileIndex = tempFiles.value.findIndex((f) => f.path === filePath);
			const file = tempFiles.value[fileIndex];

			// 1. 处理无效文件
			if (!file) {
				reject(new Error(`File not found: ${filePath}`));
				return;
			}

			// 2. 跳过已完成的文件（但返回现有结果）
			if (file.status === 'done') {
				resolve({ id: file.id, imageUrl: file.path }); // 返回已有数据
				return;
			}

			// 3. 执行上传
			try {
				tempFiles.value[fileIndex].status = 'uploading';

				const res: any = await uploadFile({
					url: props.url2 + '/1',
					filePath,
					name: 'file',
					formData: { type: 'image' },
					onProgressUpdate: (e: any) => {
						const index = tempFiles.value.findIndex((f) => f.path === filePath);
						if (index !== -1) {
							tempFiles.value[index].progress = e.progress;
						}
					},
				});
				// 4. 更新成功状态
				tempFiles.value[fileIndex] = {
					...file,
					id: res.id,
					path: res.imageUrl,
					status: 'done',
					progress: 100,
				};

				resolve(res); // 返回上传结果
			} catch (err) {
				// 5. 更新失败状态
				tempFiles.value[fileIndex] = {
					...file,
					id: '',
					status: 'error',
					progress: 0,
				};
				reject(err); // 传递错误
			}
		});
	};

	function onOptClick(item: any) {
		const { id } = item;
		if (id === optBtns.value.find((btn) => btn.active)?.id) {
			return;
		}
		optBtns.value.forEach((btn) => {
			btn.active = !btn.active;
		});
		if (!props.modelValue.dishId) {
			emit('update:modelValue', {
				...props.modelValue,
				imageId: id === 'img' ? httpImageIds.value[currFileIndex.value] : httpAiData.value.id,
				imageUrl: '',
				notRefresh: true,
			});
		}
		//处理
		cancelRequest();
		isAiImgGenerating.value = false;
	}

	// 删除图片
	const deleteImage = async () => {
		if (!httpImageIds.value[currFileIndex.value]) return;
		tempFiles.value.splice(currFileIndex.value, 1);
		if (currFileIndex.value > 0) {
			currFileIndex.value -= 1;
		}
		if (!tempFiles.value.map((item: any) => item.path).includes(imageUrl.value)) {
			imageUrl.value = tempFiles.value.length ? tempFiles.value[currFileIndex.value].path : '';
		}
		emit('update:modelValue', {
			...props.modelValue,
			imageId: httpImageIds.value.length ? httpImageIds.value[currFileIndex.value] : '',
			imageUrl: '',
			images: httpImageIds.value.map((id: any) => ({ id, imageUrl: '' })),
			notRefresh: true,
		});
	};

	function replaceImage() {
		if (imageUrl.value === tempFiles.value[currFileIndex.value].path) return;
		imageUrl.value = tempFiles.value[currFileIndex.value].path;
		const currImageId = httpImageIds.value[currFileIndex.value];
		storageImage();
		emit('update:modelValue', {
			...props.modelValue,
			imageId: currImageId,
			imageUrl: '',
			images: httpImageIds.value.map((id: any) => ({ id, imageUrl: '' })),
			notRefresh: true,
		});

		uni.showToast({
			title: '替换成功',
			icon: 'none',
			duration: 1500,
		});
	}

	//替换时，若图库中无此图片，则进行图片入库
	function storageImage() {
		const orginImageId = props.modelValue.imageId;
		const orginImageUrl = props.modelValue.imageUrl;
		if (orginImageId && !httpImageIds.value.includes(orginImageId)) {
			tempFiles.value.push({ id: orginImageId, path: orginImageUrl || '', status: 'done' });
		}
	}

	let isUploading = false;
	async function uploadImages() {
		if (isUploading) return;
		isUploading = true;
		if (!tempFiles.value.length) {
			uni.showToast({ title: '请先选择图片', icon: 'none' });
			return;
		}

		try {
			uni.showLoading({ title: '上传中...', mask: true });

			// 创建文件快照避免响应式干扰
			const filesSnapshot = [...tempFiles.value];
			const pendingFiles = filesSnapshot.filter((f) => f.status !== 'done');

			pendingFiles.forEach((file) => {
				const index = tempFiles.value.findIndex((f) => f.path === file.path);
				if (index !== -1) tempFiles.value[index].status = 'uploading';
			});

			// 并行上传所有未完成的图片
			const uploadPromises = pendingFiles.map((file) => startUpload(file.path));

			const urls: any = await Promise.all(uploadPromises);
			console.log(urls, 'urls');
			console.log(tempFiles.value, 'tempFiles.value');
			if (!props.modelValue.dishId) {
				emit('update:modelValue', {
					...props.modelValue,
					imageId: httpImageIds.value[0],
					imageUrl: '',
					images: httpImageIds.value.map((id: any) => ({ id, imageUrl: '' })),
					notRefresh: true,
				});
			} else {
				httpImageIds.value.forEach((id: any) => {
					if (!props.modelValue.images?.find((image: any) => image.id === id)) {
						props.modelValue.images?.push({ id, imageUrl: '' });
					}
				});
				emit('update:modelValue', {
					...props.modelValue,
					notRefresh: true,
				});
			}
		} catch (err) {
			console.error(err);
		} finally {
			uni.hideLoading();
			isUploading = false;
		}
	}

	async function onGenerateAIImg() {
		// 取消可能存在的旧请求
		cancelRequest();
		httpAiData.value = { id: '', imageUrl: '' };
		if (isAiImgGenerating.value) {
			isAiImgGenerating.value = false;
			return;
		}
		//生成
		if (!props.modelValue?.dishName) {
			uni.showToast({
				title: '请先填写菜名',
				icon: 'none',
				duration: 1500,
			});
			return;
		} else {
			isAiImgGenerating.value = true;
			aiImageRequest.value = await generateDishAIImage(props.modelValue);
			aiImageRequest.value.promise
				.then((data) => {
					aiImageRequest.value = null;
					httpAiData.value = data;
					isAiImgGenerating.value = false;
					//新增
					if (!props.modelValue.dishId) {
						emit('update:modelValue', {
							...props.modelValue,
							imageId: httpAiData.value.id,
							imageUrl: httpAiData.value.imageUrl,
							images: httpImageIds.value.map((id: any) => ({ id, imageUrl: '' })),
							notRefresh: true,
						});
					}
				})
				.catch((err) => {
					if (err.errMsg !== 'request:fail abort') {
						console.error('图片生成失败', err);
					}
					aiImageRequest.value = null;
					isAiImgGenerating.value = false;
				});
		}
	}
	// 取消请求
	const cancelRequest = () => {
		if (aiImageRequest.value) {
			aiImageRequest.value.abort();
			aiImageRequest.value = null;
		}
	};
	async function onReplaceAIImg() {
		if (!httpAiData.value.id) {
			return;
		}
		const orginImageId = httpAiData.value.id;
		const orginImageUrl = httpAiData.value.imageUrl;
		if (orginImageId && !httpImageIds.value.includes(orginImageId)) {
			tempFiles.value.push({ id: orginImageId, path: orginImageUrl || '', status: 'done' });
		}
		imageUrl.value = httpAiData.value.imageUrl;
		emit('update:modelValue', {
			...props.modelValue,
			imageId: httpAiData.value.id,
			imageUrl: httpAiData.value.imageUrl,
			images: httpImageIds.value.map((id: any) => ({ id, imageUrl: '' })),
			notRefresh: true,
		});
		httpAiData.value = { id: '', imageUrl: '' };
		uni.showToast({
			title: '替换成功',
			icon: 'none',
			duration: 1500,
		});
	}

	async function handlePermission(type: 'album' | 'camera' = 'camera'): Promise<void> {
		return new Promise(async (resolve, reject) => {
			let code = type === 'album' ? 'scope.writePhotosAlbum' : 'scope.camera';

			try {
				// 检查权限
				const status = await checkPermission(code);

				// 处理权限状态
				if (status === 'granted') {
					resolve(); // 已有权限直接解析
				} else if (status === 'unknown') {
					const status = await requestPermission(code); // 请求权限

					if (status === 'granted') {
						resolve();
					} else {
						reject();
					}
				} else if (status === 'denied') {
					// 显示权限拒绝提示框
					const modalRes = await new Promise<UniApp.ShowModalRes>((modalResolve) => {
						uni.showModal({
							title: `无法使用${type === 'camera' ? '相机' : '相册'}功能`,
							content: `您已拒绝${type === 'camera' ? '相机' : '相册'}权限，请在「设置」中开启权限后重试。`,
							confirmText: '去设置',
							cancelText: '取消',
							success: modalResolve,
							fail: () => modalResolve({ confirm: false, cancel: true }),
						});
					});

					if (modalRes.confirm) {
						// 打开系统设置
						const settingRes: any = await new Promise((settingResolve) => {
							uni.openSetting({
								success: settingResolve,
								fail: () => settingResolve({ authSetting: {} }),
							});
						});

						// 根据设置结果返回
						if (settingRes.authSetting[code]) {
							uni.showToast({ title: '权限已开启', icon: 'none' });
							resolve();
						} else {
							uni.showToast({ title: '未开启权限', icon: 'none' });
							reject(new Error('PERMISSION_DENIED'));
						}
					} else {
						reject(new Error('USER_CANCELLED'));
					}
				}
			} catch (error) {
				reject(error);
			}
		});
	}

	onMounted(() => {
		cancelRequest();
	});
</script>
<style lang="scss">
	.row {
		background: #fff;
		display: flex;
		box-shadow: 0px 0px 4px 0px #92929233;
		margin-bottom: 6px;
		padding: 0 15px;
	}

	.img-box {
		padding-top: 17px;
		padding-bottom: 17px;

		.label {
			padding-right: 34px;
		}

		.img-info-wrap {
			width: 110px;
			height: 110px;
		}
	}

	.handle-img {
		display: block;
		padding: 0;
	}

	.upload-wrap {
		background: #fff;
		padding: 10px 15px;
		height: 257px;
		position: relative;

		.header {
			width: 100%;
			display: flex;
			justify-content: space-between;

			.opt-btns {
				display: flex;

				.opt-btn {
					margin-left: 18px;

					.name {
						font-size: 12px;
						padding-left: 2px;
					}
				}
			}
		}

		.upload-content {
			.image-content {
				padding: 30px 15px;
				display: flex;
				align-items: center;
				justify-content: center;

				.image-wrap {
					flex: 1;
					max-width: 210px;
					width: 210px;
					height: 110px;
					overflow: hidden;
					position: relative;

					.delete-btn {
						width: 108px;
						height: 30px;
						line-height: 30px;
						text-align: center;
						background: #3333337f;
						color: #fff;
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						border-radius: 0 0 5px 5px;
					}
				}

				.upload-btn {
					position: absolute;
					right: 15px;
				}

				.comfirm {
					margin-top: 34px;
					margin-bottom: 30px;
				}
			}
		}

		.ai-content {
			padding-top: 30px;

			.img-wrap {
				text-align: center;

				.ai-tip {
					width: 110px;
					height: 110px;
					font-size: 12px;
					text-align: center;
					color: #999999;
					display: inline-flex;
					justify-content: center;
					align-items: center;
					position: relative;
					.ai-tip-border {
						position: absolute;
						z-index: 1;
						left: 0;
						top: 0;
					}
					text,
					.ai-loading {
						z-index: 2;
					}
					.ai-loading {
						width: 60px;
						height: 60px;
						animation: rotate 3s linear infinite;
					}
				}

				.ai-img {
					width: 110px;
					height: 110px;
					border-radius: 6px;
				}
			}

			.ai-btns {
				display: flex;
				justify-content: center;
				margin: 30px 0;
				position: relative;

				.ai-btn {
					&.replace {
						position: absolute;
						right: 0;
						top: 50%;
						transform: translateY(-50%);
					}
				}
			}
		}
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}
</style>
