<template>
  <div class="custom-select-container" ref="selectRef">
    <!-- 显示当前选中项 -->
    <div
      class="select-display"
      @click="handleDisplayClick"
      :style="{ borderColor: isOpen ? '' : '#dcdfe6' }"
    >
      <span class="select-value" :class="{ 'placeholder': isPlaceholder }">
        {{ displayValue || placeholder }}
      </span>
      <!-- 可清除按钮 -->
      <SlSVgIcon
        v-if="displayValue && ifClear"
        class="clear"
        name="12-12-10"
        size="12"
        @click.stop="onClear"
      />
      <!-- 下拉图标（可旋转） -->
      <SlSVgIcon
        :name="iconName"
        :size="iconSize"
        :class="{ 'icon-rotate': shouldRotate && isOpen }"
        class="dropdown-icon"
      />
    </div>

    <!-- 下拉选项区域（根据方向上下弹出） -->
    <transition name="fade">
      <div
        v-if="isOpen && hasDirectionReady"
        class="dropdown-menu-wrapper"
        :class="dropdownDirection"
      >
        <div class="dropdown-menu">
          <div
            v-for="(item, index) in options"
            :key="index"
            class="dropdown-item"
            :class="{ selected: isSelected(item) }"
            @click="selectItem(item)"
          >
          <slot name="custom-label" :option="item">
            {{ item.label }}
          </slot>
          </div>
          <!-- 空数据占位 -->
          <BaseEmpty v-if="!options.length" />
        </div>
        <slot name="dropdown-footer"></slot>
      </div>
    </transition>

    <!-- 遮罩层：点击空白关闭下拉 -->
    <div v-show="isOpen" class="dropdown-mask" @click="closeDropdown"></div>
  </div>
</template>

<script setup lang="ts">
/**
 * 自定义下拉选择组件
 * 功能：
 *  - 支持单选/多选
 *  - 支持清除已选项
 *  - 支持图标旋转
 *  - 自动根据容器位置选择向上/下弹出下拉框
 */

import { ref, computed, onMounted, nextTick, watch,getCurrentInstance } from "vue";

// 定义选项类型
interface Option {
  label: string;
  value: string | number;
}

// 接收组件 props
const props = withDefaults(
  defineProps<{
    options: Option[]; // 可选项列表
    modelValue?: string | number | string[] | number[];
    placeholder?: string;
    iconName?: string; // 图标名称
    iconSize?: string; // 图标大小
    rotateOnOpen?: boolean; // 是否旋转图标
    multiple?: boolean; // 是否多选
    ifClear?: boolean; // 是否可清除
    emitOnInit?: boolean; //  新增：是否在初始化时触发 change 事件
  }>(),
  {
    placeholder: "请选择",
    iconName: "12-12-7",
    iconSize: "12",
    rotateOnOpen: false,
    emitOnInit: true, //  默认触发
  }
);

// 事件
const emit = defineEmits(["update:modelValue", "change"]);

// -------------------- 响应式变量 --------------------
const isOpen = ref(false); // 下拉是否展开
const isClearing = ref(false); // 是否处于清除状态，避免误触 toggle
const selectedOption = ref<Option | Option[] | null>(null); // 当前选中项
const dropdownDirection = ref<"down" | "up">("down"); // 弹出方向
const selectRef = ref();
const instance = getCurrentInstance();
const proxy = instance?.proxy;
const hasDirectionReady = ref(false);
// 显示值
const displayValue = computed(() => {
  if (props.multiple && Array.isArray(selectedOption.value)) {
    return selectedOption.value.map((item) => item.label).join(", ");
  }
  return (selectedOption.value as Option)?.label;
});

// 是否显示 placeholder
const isPlaceholder = computed(() => {
  return props.placeholder && !displayValue.value;
});

// -------------------- 生命周期初始化 --------------------
onMounted(() => {
  updateSelectedOption(); // 初始化选中值
});

// -------------------- 方法逻辑 --------------------

// 根据 modelValue 和 options 初始化 selectedOption
const updateSelectedOption = () => {
  if (props.modelValue) {
    if (props.multiple && Array.isArray(props.modelValue)) {
      const modelValue = props.modelValue as (string | number)[];
      const matched = props.options.filter((item) =>
        modelValue.includes(item.value)
      );
      selectedOption.value = matched;
      if (props.emitOnInit) {
        emit("change", matched);
      }
    } else {
      const found = props.options.find(
        (item) => item.value === props.modelValue
      );
      selectedOption.value = found || null;
      if (found && props.emitOnInit) {
        emit("change", found);
      }
    }
  } else {
    selectedOption.value = props.multiple ? [] : null;
  }
};

// 判断选项是否被选中
const isSelected = (item: Option) => {
  if (props.multiple && Array.isArray(selectedOption.value)) {
    return selectedOption.value.some((option) => option.value === item.value);
  }
  return (selectedOption.value as Option)?.value === item.value;
};

// 点击选择框展示下拉
const handleDisplayClick = () => {
  if (!isClearing.value) toggleDropdown();
  isClearing.value = false;
};

// 切换下拉展示状态，并根据空间调整方向
const toggleDropdown = () => {
  isOpen.value = !isOpen.value;

  if (isOpen.value) {
    hasDirectionReady.value = false;

    nextTick(() => {
      const query = uni.createSelectorQuery().in(proxy);
      query
        .select(".select-display")
        .boundingClientRect((rect: any) => {
          if (rect) {
            const screenHeight = uni.getSystemInfoSync().windowHeight;
            const bottomSpace = screenHeight - rect.bottom;
            dropdownDirection.value = bottomSpace < 280 ? "up" : "down";
            hasDirectionReady.value = true;
          }
        })
        .exec();
    });
  } else {
    hasDirectionReady.value = false;
  }
};

// 清除选中
const onClear = () => {
  isClearing.value = true;
  selectedOption.value = null;
  emit("update:modelValue", "");
  emit("change", "");
};

// 选择一个选项
const selectItem = (item: Option) => {
  if (props.multiple) {
    if (Array.isArray(selectedOption.value)) {
      const index = selectedOption.value.findIndex(
        (option) => option.value === item.value
      );
      if (index !== -1) {
        selectedOption.value.splice(index, 1);
      } else {
        selectedOption.value.push(item);
      }
    } else {
      selectedOption.value = [item];
    }
    emit(
      "update:modelValue",
      (selectedOption.value as Option[]).map((item) => item.value)
    );
    emit("change", item);
  } else {
    selectedOption.value = item;
    emit("update:modelValue", item.value);
    emit("change", item);
  }
  closeDropdown();
};

// 关闭下拉菜单
const closeDropdown = () => {
  isOpen.value = false;
};

// 控制图标是否旋转
const shouldRotate = computed(() => props.rotateOnOpen);

// 监听 options / modelValue 变化
watch(() => props.options, updateSelectedOption);
watch(() => props.modelValue, updateSelectedOption, { deep: true  });
</script>

<style scoped lang="scss">
.custom-select-container {
  position: relative;
  width: 100%;
  font-size: 14px;
  height: 100%;
  border-radius: 3px;
  background: #ffffff;

  .select-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 11px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    cursor: pointer;
    transition: border-color 0.3s;
  }

  .select-value {
    flex: 1;
    padding-right: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .clear {
    margin: 0 8px;
  }

  .dropdown-menu-wrapper {
    position: absolute;
    z-index: 1000;
    width: 100%;
    border-radius: 10px;
    background: #ffffff;
    box-shadow: 0px 0px 10px 0px #4747474c;
    overflow: hidden;

    &.down {
      top: 100%;
      margin-top: 4px;
    }

    &.up {
      bottom: 100%;
      margin-bottom: 4px;
    }
  }

  .dropdown-menu {
    width: 100%;
    max-height: 240px;
    overflow-y: auto;

    .dropdown-item {
      padding: 8px 12px;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }

      &.selected {
        color: #ffffff;
        background: #4f7af6;
      }
    }
  }

  .dropdown-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    z-index: 9;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s, transform 0.3s;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
    transform: translateY(-10px);
  }

  .icon-rotate {
    transform: rotate(180deg);
    transition: transform 0.3s;
  }

  .dropdown-icon {
    transition: transform 0.3s ease;
  }

  .placeholder {
    font-size: 14px;
    color: #999999;
  }
}
</style>
