<template>
    <div class="custom-select-container">
      <!-- 显示当前选中项 -->
      <div
        class="select-display"
        @click="toggleDropdown"
        :style="{ borderColor: isOpen ? '' : '#dcdfe6' }"
      >
        <span :class="{ placeholder: placeholder && !selectedOption?.label }">{{
          selectedOption?.label || placeholder
        }}</span>
        <SlSvgIcon
          :name="iconName"
          :size="iconSize"
          :class="{ 'icon-rotate': shouldRotate && isOpen }"
          class="dropdown-icon"
        />
      </div>
  
      <!-- 下拉选项列表 -->
      <transition name="fade">
        <div v-show="isOpen" class="dropdown-menu" :style="{ top: dropdownTop }">
          <div
            v-for="(item, index) in options"
            :key="index"
            class="dropdown-item"
            :class="{ selected: isSelected(item) }"
            @click="selectItem(item)"
          >
            {{ item.label }}
          </div>
        </div>
      </transition>
  
      <!-- 点击外部关闭下拉的遮罩 -->
      <div v-show="isOpen" class="dropdown-mask" @click="closeDropdown"></div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, onMounted, nextTick } from "vue";
  import SlSvgIcon from "@/components/SlSVgIcon.vue";
  
  interface Option {
    label: string;
    value: string | number;
  }
  
  const props = withDefaults(
    defineProps<{
      options: Option[];
      modelValue?: string | number;
      placeholder?: string;
      iconName?: string; // 图标名称prop
      iconSize?: string; // 图标尺寸prop
      rotateOnOpen?: boolean; // 控制图标旋转
    }>(),
    {
      placeholder: "请选择",
      iconName: "components-12-7", // 默认图标
      iconSize: "12", // 默认大小
      rotateOnOpen: false, // 默认不旋转
    }
  );
  
  const emit = defineEmits(["update:modelValue", "change"]);
  
  const isOpen = ref(false);
  const selectedOption = ref<Option | null>(null);
  const dropdownTop = ref("40px");
  
  // 初始化选中项
  onMounted(() => {
    if (props.modelValue) {
      const found = props.options.find((item) => item.value === props.modelValue);
      if (found) {
        selectedOption.value = found;
        emit("change", found); // 初始化时触发change事件
      }
    }
  });
  
  // 监听options变化
  watch(
    () => props.options,
    (newOptions) => {
      if (props.modelValue) {
        selectedOption.value =
          newOptions.find((item) => item.value === props.modelValue) || null;
      }
    }
  );
  
  // 监听modelValue变化
  watch(
    () => props.modelValue,
    (newVal) => {
      if (newVal) {
        selectedOption.value =
          props.options.find((item) => item.value === newVal) || null;
      }
    }
  );
  
  // 判断是否选中
  const isSelected = (item: Option) => {
    return selectedOption.value?.value === item.value;
  };
  
  // 切换下拉状态
  const toggleDropdown = () => {
    isOpen.value = !isOpen.value;
    if (isOpen.value) {
      nextTick(() => {
        const query = uni.createSelectorQuery();
        query
          .select(".custom-select-container .select-display")
          .boundingClientRect((rect: any) => {
            if (rect) dropdownTop.value = `${rect.bottom}px`;
          })
          .exec();
      });
    }
  };
  
  // 选择选项
  const selectItem = (item: Option) => {
    selectedOption.value = item;
    emit("update:modelValue", item.value);
    emit("change", item); // 新增change事件
    closeDropdown();
  };
  
  // 关闭下拉
  const closeDropdown = () => {
    isOpen.value = false;
  };
  // 计算属性控制是否应该旋转
  const shouldRotate = computed(() => props.rotateOnOpen);
  </script>
  
  <style scoped lang="scss">
  .custom-select-container {
    position: relative;
    width: 100%;
    font-size: 14px;
  //   z-index: 1;
    border-radius: 3px;
    background: #ffffff;
  
    .select-display {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background-color: #fff;
      cursor: pointer;
      transition: border-color 0.3s;
    }
  
    .dropdown-menu {
      position: absolute;
      width: 100%;
      max-height: 200px;
      overflow-y: auto;
      margin-top: 4px;
      z-index: 1000;
      border-radius: 10px;
      background: #ffffff;
      box-shadow: 0px 0px 10px 0px #4747474c;
  
      .dropdown-item {
        padding: 8px 12px;
        cursor: pointer;
        transition: background-color 0.3s;
  
        &:hover {
          background-color: #f5f7fa;
        }
  
        &.selected {
          color: #ffffff;
          background: #4f7af6;
        }
      }
    }
  
    .dropdown-mask {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: transparent;
      z-index: 9;
    }
  
    .fade-enter-active,
    .fade-leave-active {
      transition: opacity 0.3s, transform 0.3s;
    }
  
    .fade-enter-from,
    .fade-leave-to {
      opacity: 0;
      transform: translateY(-10px);
    }
  
    .icon-rotate {
      transform: rotate(180deg);
      transition: transform 0.3s;
    }
    .dropdown-icon {
      transition: transform 0.3s ease;
      /* 默认不设置旋转 */
    }
  
    .icon-rotate {
      transform: rotate(180deg); /* 只有开启旋转时才生效 */
    }
    .placeholder {
      font-size: 14px;
      color: #999999;
    }
  }
  </style>
  