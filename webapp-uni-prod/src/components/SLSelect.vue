<template>
    <div class="custom-select-container">
      <!-- 显示当前选中项 -->
      <div
        class="select-display"
        @click="handleDisplayClick"
        :style="{ borderColor: isOpen ? '' : '#dcdfe6' }"
      >
        <span class="select-value" :class="{ 'placeholder':isPlaceholder  }">{{
          displayValue || placeholder
        }}</span>
        <SlSVgIcon
          v-if="displayValue&&ifClear"
          class="clear"
          name="12-12-10"
          size="12"
          @click.stop="onClear"
        />
        <SlSVgIcon
          :name="iconName"
          :size="iconSize"
          :class="{ 'icon-rotate': shouldRotate && isOpen }"
          class="dropdown-icon"
        />
      </div>
  
      <!-- 下拉选项列表 -->
      <transition name="fade">
        <div v-show="isOpen" class="dropdown-menu-wrapper"  :style="{ top: dropdownTop }">
          <div class="dropdown-menu">
            <div
            v-for="(item, index) in options"
            :key="index"
            class="dropdown-item"
            :class="{ selected: isSelected(item) }"
            @click="selectItem(item)"
            >
            {{ item.label }}
          </div>
          <BaseEmpty v-if="!options.length" />
        </div>
        <slot name="dropdown-footer"></slot>
        </div>
      </transition>
  
      <!-- 点击外部关闭下拉的遮罩 -->
      <div v-show="isOpen" class="dropdown-mask" @click="closeDropdown"></div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, onMounted, nextTick } from "vue";
  
  interface Option {
    label: string;
    value: string | number;
  }
  
  const props = withDefaults(
    defineProps<{
      options: Option[];
      modelValue?: string | number | string[] | number[];
      placeholder?: string;
      iconName?: string; // 图标名称prop
      iconSize?: string; // 图标尺寸prop
      rotateOnOpen?: boolean; // 控制图标旋转
      /** 是否支持多选 */
      multiple?: boolean;
      /** 是否一键清空 */
      ifClear?: boolean;
    }>(),
    {
      placeholder: "请选择",
      iconName: "components-12-7", // 默认图标
      iconSize: "12", // 默认大小
      rotateOnOpen: false, // 默认不旋转
    }
  );
  
  const emit = defineEmits(["update:modelValue", "change"]);
  
  const isOpen = ref(false);
  const isClearing = ref(false);
  const selectedOption = ref<Option | Option[]|null>(null);
  const dropdownTop = ref("40px");
  const displayValue = computed(() => {
  if (props.multiple && Array.isArray(selectedOption.value)) {
    return selectedOption.value.map(item => item.label).join(', ');
  }
  return (selectedOption.value as Option)?.label;
});
const isPlaceholder = computed(() => {
  return props.placeholder && (!selectedOption || (Array.isArray(selectedOption) && selectedOption.length === 0));
});
  // 初始化选中项
  onMounted(() => {
    updateSelectedOption()
    // if (props.modelValue) {
    //   const found = props.options.find((item) => item.value === props.modelValue);
    //   if (found) {
    //     selectedOption.value = found;
    //     emit("change", found); // 初始化时触发change事件
    //   }
    // }
  });
  const updateSelectedOption = () => {
    if (props.modelValue) {
      if(props.multiple && Array.isArray(props.modelValue)){
        const modelValue = props.modelValue as string[]
        selectedOption.value = props.options.filter((item) => modelValue.some((e)=>e == item.value)) || [];
          emit("change", selectedOption); // 初始化时触发change事件
      }else{
        const found = props.options.find((item) => item.value === props.modelValue);
        if (found) {
          emit("change", found); // 初始化时触发change事件
        }
        selectedOption.value = found || null;
      }
    }
  }
  // 监听options变化
  watch(
    () => props.options,
    (_) => {
      updateSelectedOption()
      // if (props.modelValue) {
      //   selectedOption.value =
      //     newOptions.find((item) => item.value === props.modelValue) || null;
      // }
    }
  );
  
  // 监听modelValue变化
  watch(
    () => props.modelValue,
    (_) => {
      updateSelectedOption()
      // if (newVal) {
      //   selectedOption.value =
      //     props.options.find((item) => item.value === newVal) || null;
      // }
    },{deep:1}
  );
  
  // 判断是否选中
  const isSelected = (item: Option) => {
    if (props.multiple && Array.isArray(selectedOption.value)) {
      return selectedOption.value.some((option) => option.value === item.value);
    } 
    return (toValue(selectedOption)as Option)?.value === item.value;
  };
  const handleDisplayClick = () => {
    if (!isClearing.value) {
      toggleDropdown();
    }
    isClearing.value = false;
  };
  // 切换下拉状态
  const toggleDropdown = () => {
    isOpen.value = !isOpen.value;
    if (isOpen.value) {
      nextTick(() => {
        const query = uni.createSelectorQuery();
        query
          .select(".custom-select-container .select-display")
          .boundingClientRect((rect: any) => {
            if (rect) dropdownTop.value = `${rect.bottom}px`;
          })
          .exec();
      });
    }
  };
  const onClear = () => {
    isClearing.value = true;
    selectedOption.value = null
    emit("update:modelValue", "");
    emit("change", "");
  }
  // 选择选项
  const selectItem = (item: Option) => {
    if (props.multiple) {
      if (Array.isArray(selectedOption.value)) {
        const index = selectedOption.value.findIndex((option) => option.value === item.value);
        if (index !== -1) {
          selectedOption.value.splice(index, 1);
        } else {
          selectedOption.value.push(item);
        }
      } else {
        selectedOption.value = [item];
      }
      // update
      emit("update:modelValue", (selectedOption.value as Option[]).map((item) => item.value));
      emit("change", item); // 新增change事件
      console.log("🚀 ~ SLSelect.vue ~ selectItem ~ update:")
    }else{
      selectedOption.value = item
      emit("update:modelValue", item.value);
      emit("change", item); // 新增change事件
    }
    closeDropdown();
  };
  
  // 关闭下拉
  const closeDropdown = () => {
    isOpen.value = false;
  };
  // 计算属性控制是否应该旋转
  const shouldRotate = computed(() => props.rotateOnOpen);
  </script>
  
  <style scoped lang="scss">
  .custom-select-container {
    position: relative;
    width: 100%;
    font-size: 14px;
    height: 100%;
  //   z-index: 1;
    border-radius: 3px;
    background: #ffffff;
  
    .select-display {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 100%;
      // padding: 8px 12px;
      padding: 0 11px ;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background-color: #fff;
      cursor: pointer;
      transition: border-color 0.3s;
    }
    .select-value{
      flex: 1;
      padding-right: 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .clear{
      margin: 0px 8px;
    }
  .dropdown-menu-wrapper{
      position: absolute;
      margin-top: 4px;
      z-index: 1000;
      width: 100%;
      border-radius: 10px;
      background: #ffffff;
      box-shadow: 0px 0px 10px 0px #4747474c;
      overflow: hidden;
  }
    .dropdown-menu {
      // position: absolute;
      width: 100%;
      max-height: 240px; // 六条滚动
      overflow-y: auto;
      // margin-top: 4px;
      // z-index: 1000;
      // border-radius: 10px;
      // background: #ffffff;
      // box-shadow: 0px 0px 10px 0px #4747474c;
  
      .dropdown-item {
        padding: 8px 12px;
        cursor: pointer;
        transition: background-color 0.3s;
  
        &:hover {
          background-color: #f5f7fa;
        }
  
        &.selected {
          color: #ffffff;
          background: #4f7af6;
        }
      }
    }
  
    .dropdown-mask {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: transparent;
      z-index: 9;
    }
  
    .fade-enter-active,
    .fade-leave-active {
      transition: opacity 0.3s, transform 0.3s;
    }
  
    .fade-enter-from,
    .fade-leave-to {
      opacity: 0;
      transform: translateY(-10px);
    }
  
    .icon-rotate {
      transform: rotate(180deg);
      transition: transform 0.3s;
    }
    .dropdown-icon {
      transition: transform 0.3s ease;
      /* 默认不设置旋转 */
    }
  
    .icon-rotate {
      transform: rotate(180deg); /* 只有开启旋转时才生效 */
    }
    .placeholder {
      font-size: 14px;
      color: #999999;
    }
  }
  </style>
  