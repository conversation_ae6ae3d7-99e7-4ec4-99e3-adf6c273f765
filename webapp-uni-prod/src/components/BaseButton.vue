<template>
    <button type="primary" :style="btnStyle" :class="[size, 'btn-' + btnType, { 'btn-disabled': disabled }]"
        @click="$emit('click')">
        <slot>
            <span>{{ btnType == 'save' ? '完成' : btnType == 'delete' ? '删除' : btnType == 'cancel' ? '取消' : '' }}</span>
        </slot>
    </button>
</template>

<script setup lang="ts">
withDefaults(defineProps<{ btnType?: 'save' | 'delete' | 'cancel', disabled?: boolean, size?: 'small' | 'medium' | 'large', btnStyle?: any }>(), {
    btnType: 'save',
    size: 'medium',
    btnStyle: {}
});
// uniapp转码后无法透传，只能emit
const emits = defineEmits(['click']);
</script>

<style scoped lang="scss">
// button
.btn-cancel {
    background: #ffffff;
    border: 1px solid #0066df;
    color: #0066df;
}

.btn-delete {
    background: #ffffff;
    border: 1px solid #999999b2;
    background: #ffffff;
    color: #666;

    &:hover {
        border: 1px solid #e50101;
        color: #e50101;
    }
}

.btn-save {
    background: #0066df;
    color: #fff;
}

.btn-disabled {
    background: #999999;
    pointer-events: none;
}

button {
    height: 40px;
    line-height: 40px;
    border-radius: 3px;
}

button.large {
    width: 290px;
}

button.medium {
    width: 140px;
}

button.small {
    width: 125px;
}
</style>