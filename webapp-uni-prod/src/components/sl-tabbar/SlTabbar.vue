<template>
  <view class="custom-tabbar" :style="navStyle">
    <view
      v-for="(item, index) in props.list"
      :key="index"
      class="tabbar-item"
      :style="itemStyle"
      @click="tabbarChange(index)"
      :class="{ active: index === modelValue }"
    >
      <!-- 图标区域 -->
      <view class="item-icon">
        <view class="icon">
          <SlSVgIcon
            :name="index === modelValue ? item.selectedIcon : item.icon"
            size="20"
          />
        </view>
      </view>

      <!-- 文字区域 -->
      <text
        class="item-text"
        :style="{ color: index === modelValue ? activeColor : inactiveColor }"
      >
        {{ item.text }}
      </text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { TabBarItem } from "@/models/TabBarItem";
const props = defineProps({
  list: {
    type: Array as () => TabBarItem[],
    default: () => [],
  },
  modelValue: {
    type: Number,
    default: 0,
  },
  height: {
    type: Number,
    // 高度 (单位px，不带单位)
    height: 56,
    default: 56,
  },
    // 自定义主题色
    activeColor: {
    type: String,
    default: "#005AC5",
  },
    // 非激活状态颜色
    inactiveColor: {
    type: String,
    default: "#666666",
  },
  // 背景颜色
  backgroundColor: {
    type: String,
    default: "#ffffff",
  },
  // 底部边框颜色
  borderColor: {
    type: String,
    default: "rgba(93, 93, 93, 0.2)",
  },
});
onLoad(() => {
    nextTick(() => {
  })
})
const emit = defineEmits<{
  (e: "update:modelValue", val: number): void;
  (e: "tabIndexChange", idx: number): void;
}>();

const curTabIndex = computed<number>({
  get: () => props.modelValue,
  set: (val: number) => emit("update:modelValue", val),
});

// 监听底栏切换
const tabbarChange = (index: number) => {
  // 先更新 v-model 绑定的值
  curTabIndex.value = index;
  // 再导航
  const selectedTab = props.list[index];
  console.log(selectedTab, "selectedTab");
  let path = selectedTab.pagePath || "";
  console.log(path, "path")
  if (path) {
    if (!path.startsWith("/")) path = "/" + path;
    uni.navigateTo({
      url: path,
      fail: (err) => {
        console.error("Failed to switch tab:", err);
      },
      success: () => {
        // 通知父组件（如果需要做其他额外处理）
        emit("tabIndexChange", index);
      },
    });
  }
};
// 安全区域值
const safeArea = ref(0);

// 计算导航栏位置样式
const navStyle = computed(() => {
  return {
    height: `${props.height + safeArea.value}px`,
    paddingBottom: `${safeArea.value}px`,
    backgroundColor: props.backgroundColor,
    borderColor: props.borderColor,
  };
});

// 计算项目高度
const itemStyle = computed(() => ({
  height: `${props.height}px`,
}));
// 获取安全区域
const getSafeArea = () => {
  const systemInfo = uni.getSystemInfoSync();
  const safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0;
  // 转换成px（微信小程序rpx单位需要转换）
  safeArea.value = safeAreaBottom;
};

onMounted(() => {
  getSafeArea();
});
</script>

<style lang="scss">
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  display: flex;
  align-items: flex-start;
  border-top: 1px solid;
  transition: all 0.2s ease;
  border-radius: 10px 10px 0px 0px;
  box-shadow: 0px -0.5px 6px 0px rgba(93, 93, 93, 0.2);
  backdrop-filter: blur(20px);

  .tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;

    &:active {
      opacity: 0.8;
      transform: scale(0.95);
    }

    .item-icon {
      position: relative;
      padding-top: 4px;
      .icon {
        // padding-top: 5px;
      }
    }

    .item-text {
      font-size: 14px;
      transform-origin: center bottom;
      transition: color 0.3s ease, transform 0.3s ease;
    }
  }
}
</style>
