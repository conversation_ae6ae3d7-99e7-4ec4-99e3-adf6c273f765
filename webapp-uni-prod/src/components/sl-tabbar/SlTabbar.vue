<template>
  <view class="custom-tabbar">
    <view v-for="(item, index) in props.list" :key="index" class="tabbar-item" @click="tabbarChange(index)"
      :class="{ active: index === modelValue }">
      <!-- 图标区域 -->
      <view class="item-icon">
        <view class="icon">
          <SlSubSvgIcon :subpage="subpage" :name="index === modelValue ? item.selectedIcon : item.icon" size="20" />
        </view>
      </view>
      <!-- 文字区域 -->
      <text class="item-text" :style="{ color: index === modelValue ? activeColor : inactiveColor }">
        {{ item.text }}
      </text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { TabBarItem } from "@/models/TabBarItem";
const props = defineProps({
  subpage: {
    type: String,
    default: "",
  },
  list: {
    type: Array as () => TabBarItem[],
    default: () => [],
  },
  modelValue: {
    type: Number,
    default: 0,
  },
  // 自定义主题色
  activeColor: {
    type: String,
    default: "#005AC5",
  },
  // 非激活状态颜色
  inactiveColor: {
    type: String,
    default: "#666666",
  },
});
const emit = defineEmits<{
  (e: "update:modelValue", val: number): void;
  (e: "tabIndexChange", idx: number): void;
}>();

const curTabIndex = computed<number>({
  get: () => props.modelValue,
  set: (val: number) => emit("update:modelValue", val),
});

// 监听底栏切换
const tabbarChange = (index: number) => {
  // 先更新 v-model 绑定的值
  curTabIndex.value = index;
  // 再导航
  const selectedTab = props.list[index];
  console.log(selectedTab, "selectedTab");
  let path = selectedTab.pagePath || "";
  console.log(path, "path")
  if (path) {
    if (!path.startsWith("/")) path = "/" + path;
    uni.navigateTo({
      url: path,
      fail: (err) => {
        console.error("Failed to switch tab:", err);
      },
      success: () => {
        // 通知父组件（如果需要做其他额外处理）
        emit("tabIndexChange", index);
      },
    });
  }
};
</script>

<style lang="scss">
.custom-tabbar {
  height: 50px;
  display: flex;
  flex-direction: row;
  border-radius: 10px 10px 0px 0px;
  background: rgba(255, 255, 255, 0.94);
  backdrop-filter: blur(20px);
  box-shadow: 0px -0.5px 6px 0px rgba(93, 93, 93, 0.2);

  .tabbar-item {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: transform 0.3s ease;

    &:active {
      opacity: 0.8;
      transform: scale(0.95);
    }

    .item-icon {
      position: relative;
      padding-top: 4px;
    }

    .item-text {
      font-size: 14px;
      line-height: 14px;
      transform-origin: center bottom;
      transition: color 0.3s ease, transform 0.3s ease;
    }
  }
}
</style>
