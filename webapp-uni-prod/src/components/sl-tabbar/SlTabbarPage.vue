<template>
    <!-- 自定义 tabBar 页面 -->
    <view class="sl-tabbar-page">
        <view class="sl-tabbar-page__content" :style="{ height: contentHeight }">
            <slot />
        </view>
        <SlTabbar class="sl-tab-bar" v-model="curTabIndex" :list="tabBarList" :style="tabBarStyle" v-if="showTabBar"/>
    </view>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { SUB_TAB_BAR_LIST } from '@/subPageTabs'
import { currentRoute } from "@/router";
import { toRpx } from '@/utils/toRpx';
// 接收上层传来的根目录标识
const props = withDefaults(defineProps<{
    tabBarRoot?: string,
    showTabBar?: boolean  // 控制是否显示 tabBar  理发管理特殊情况需要使用！
    admin?: boolean  // 是否为管理员模式，决定使用不同的 tabBar 配置
}>(), {
    showTabBar: true  // 默认值为 true
})

// 向上派发 tabIndexChange
const emit = defineEmits<{
    (e: 'tabIndexChange', index: number): void
}>()



// 当前激活索引
const curTabIndex = ref(0)
// 计算出当前子包的 tabBar 配置
const tabBarConf = computed(() => {
    const item = SUB_TAB_BAR_LIST.find(item => item.root === (props.tabBarRoot || ''))
    if (!item) {
        console.warn(`未找到对应的 tabBar 配置: ${props.tabBarRoot}`)
        return null
    }
    const tabBar = props.admin ? item.adminTabBar : item.tabBar
    return {
        root: item.root,
        tabBar: {
            list: tabBar ? tabBar.list.map(tab => ({
                text: tab.text,
                icon: tab.icon,
                selectedIcon: tab.selectedIcon,
                pagePath: tab.pagePath.startsWith('/') ? tab.pagePath : '/' + tab.pagePath
            })) : []
        }
    }
})
const tabBarList = computed(() => tabBarConf.value?.tabBar.list || [])
// 当前 tabBar 的安全区域
const safeArea = computed(() => {
    const systemInfo = uni.getSystemInfoSync();
    return systemInfo.safeAreaInsets?.bottom || 0; // 获取底部安全区域高度
})
const contentHeight = computed(() => {
    // 计算内容区域高度
    // 100vh 减去底部安全区域高度 和 tabBar 高度
    return `calc(100vh - ${toRpx(safeArea.value + 50)})`;
})
const tabBarStyle = computed(() => {
    return {
        paddingBottom: toRpx(safeArea.value),
    }
})
// 监听 curTabIndex
watch(curTabIndex, idx => {
    emit('tabIndexChange', idx)
})

// 当页面显示时（包括首次加载和从其他页面返回），取当前路由，计算 curTabIndex
onShow(() => {
    // 获取页面路由信息
    let currentPath = currentRoute()
    if (currentPath) {
        currentPath = currentPath.split('?')[0] // 去掉 query 参数
    }
    const idx = tabBarList.value.findIndex(item => {
        // 去掉可能的前导斜杠再比较
        return item.pagePath === currentPath
    })
    console.log('当前匹配的 tabBar 索引:', idx)
    if (idx >= 0) {
        curTabIndex.value = idx
    }
})

</script>

<style lang="scss" scoped>
.sl-tabbar-page {
    height: 100%;
    position: relative;

    &__content {
        overflow: auto;
        position: relative;
    }

    .sl-tab-bar {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
    }
}
</style>