<template>
    <!-- 自定义 tabBar 页面 -->
    <view class="sl-tabbar-page">
        <view class="sl-tabbar-page__content">
            <slot />
        </view>
        <SlTabbar v-model="curTabIndex" :list="tabBarList"
            v-if="showTabBar" />
    </view>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { SUB_TAB_BAR_LIST } from '@/subPageTabs'
import { currentRoute } from "@/router";
// 接收上层传来的根目录标识
const props = withDefaults(defineProps<{
    tabBarRoot?: string,
    showTabBar?: boolean  // 控制是否显示 tabBar
    admin?: boolean  // 是否为管理员模式，决定使用不同的 tabBar 配置
}>(), {
    showTabBar: true  // 默认值为 true
})
// 向上派发 tabIndexChange
const emit = defineEmits<{
    (e: 'tabIndexChange', index: number): void
}>()
// 当前激活索引
const curTabIndex = ref(0)
// 计算出当前子包的 tabBar 配置
// 计算出当前子包的 tabBar 配置
const tabBarConf = computed(() => {
    const item = SUB_TAB_BAR_LIST.find(item => item.root === (props.tabBarRoot || ''))
    console.log(item, 'iitem')
    if (!item) {
        console.warn(`未找到对应的 tabBar 配置: ${props.tabBarRoot}`)
        return null
    }
    const tabBar = props.admin ? item.adminTabBar : item.tabBar
    return {
        root: item.root,
        tabBar: {
            list: tabBar ? tabBar.list.map(tab => ({
                text: tab.text,
                icon: tab.icon,
                selectedIcon: tab.selectedIcon,
                pagePath: tab.pagePath.startsWith('/') ? tab.pagePath : '/' + tab.pagePath
            })) : []
        }
    }
})
const tabBarList = computed(() => tabBarConf.value?.tabBar.list || [])

// 监听 curTabIndex
watch(curTabIndex, idx => {
    emit('tabIndexChange', idx)
})

// 当页面显示时（包括首次加载和从其他页面返回），取当前路由，计算 curTabIndex
onShow(() => {
    // 获取页面路由信息
    let currentPath = currentRoute()
    if (currentPath) {
        currentPath = currentPath.split('?')[0] // 去掉 query 参数
    }
    const idx = tabBarList.value.findIndex(item => {
        // 去掉可能的前导斜杠再比较
        return item.pagePath === currentPath
    })
    console.log('当前匹配的 tabBar 索引:', idx)
    if (idx >= 0) {
        curTabIndex.value = idx
    }
})

</script>

<style lang="scss" scoped>
.sl-tabbar-page {
    display: flex;
    flex-direction: column;
    height: 100%;

    &__content {
        flex: 1;
        overflow: auto;
    }
}
</style>