<template>
    <!-- 自定义 tabBar 页面 -->
    <view class="sl-tabbar-page">
        <SlNavbar v-if="customNavbar" @on-press-back="pressBackTo" :back-to="pressBackPath" :state="navState"
            :navigation-bar-title-text="navigationBarTitleText || customNavbar?.text" />
        <view class="sl-tabbar-page__content" :style="{ height: contentHeight }">
            <slot />
        </view>
        <SlTabbar v-if="hasCustomTabbar" class="sl-tab-bar" :subpage="subpage" v-model="curTabIndex"
            :list="tabBarList" />
    </view>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { SUB_TAB_BAR_LIST } from '@/subPageTabs'
import { currentRoute } from "@/router";
import { toRpx } from '@/utils/toRpx';
// 接收上层传来的根目录标识
const props = (defineProps<{
    navigationBarTitleText?: string; // 自定义导航栏标题
    pressBackPath?: string, // 可选的返回路径
    tabBarRoot?: string,
    admin?: boolean  // 是否为管理员模式，决定使用不同的 tabBar 配置

}>())
// 当前激活索引
const curTabIndex = ref(0)
// 当前路径
const curPath = ref<string>('')
// 计算当前是否为自定义导航栏
const customNavbar = ref<{ text: string; pagePath: string } | undefined>(undefined)
// 当前自定义导航栏高度
const customNavbarHeight = computed(() => {
    return customNavbar.value ? `${(navState.value.navBarHeight + navState.value.statusBarHeight)}px` : '0px'
})

// 判断是否有自定义 tabBar
const hasCustomTabbar = computed(() => {
    return tabBarList.value.some(ele => ele.pagePath.split('?')[0] === curPath.value)
})
// 自定义 tabBar 高度
const customTabBarHeight = computed(() => {
    return hasCustomTabbar.value ? `${toRpx(safeArea.value + 50)}` : '0px'
})
// 当前子包的名称
const subpage = computed(() => {
    return props.tabBarRoot ? props.tabBarRoot.replace(/^subpages\//, '') : ''
})
const emit = defineEmits<{
    (e: 'tabIndexChange', index: number): void
    (e: 'onPressBack'): void // 导航栏返回事件
}>()
const navState = ref({
    statusBarHeight: 0,
    navBarHeight: 0
})
// 计算出当前子包的 tabBar 配置
const tabBarConf = computed(() => {
    const item = SUB_TAB_BAR_LIST.find(item => item.root === (props.tabBarRoot || ''))
    if (!item) {
        console.warn(`未找到对应的 tabBar 配置: ${props.tabBarRoot}`)
        return null
    }
    console.log('item', item)
    const tabBar = props.admin ? item.adminTabBar : item.tabBar
    console.log('tabBar', tabBar)
    return {
        root: item.root,
        tabBar: {
            list: tabBar ? tabBar.list.map(tab => ({
                text: tab.text,
                icon: tab.icon,
                selectedIcon: tab.selectedIcon,
                pagePath: tab.pagePath.startsWith('/') ? tab.pagePath : '/' + tab.pagePath
            })) : []
        },
        navBar: item.navBar
    }
})
// 自定义tabbar
const tabBarList = computed(() => tabBarConf.value?.tabBar.list || [])
// 自定义导航栏
const navBarList = computed(() => tabBarConf.value?.navBar?.list || [])
// 当前 tabBar 的安全区域
const safeArea = computed(() => {
    const systemInfo = uni.getSystemInfoSync();
    return systemInfo.safeAreaInsets?.bottom || 0; // 获取底部安全区域高度
})
// 计算内容区域高度
const contentHeight = computed(() => {
    // 100vh 减去底部安全区域高度 和 tabBar 高度 、自定义导航栏高度
    return `calc(100vh - ${customTabBarHeight.value} - ${customNavbarHeight.value})`;
})
// 当前 tabBar 的样式
const tabBarStyle = computed(() => {
    return {
        paddingBottom: toRpx(safeArea.value),
    }
})
// 监听 curTabIndex
watch(curTabIndex, idx => {
    emit('tabIndexChange', idx)
})

onLoad(() => {
    curPath.value = currentRoute()
    if (curPath.value) {
        curPath.value = curPath.value.split('?')[0] // 去掉 query 参数
    }
    const idx = tabBarList.value.findIndex(item => {
        return item.pagePath === curPath.value
    })
    if (idx >= 0) {
        curTabIndex.value = idx
    }
    customNavbar.value = navBarList.value.find(item => {
        return item.pagePath === curPath.value
    })
    if (customNavbar.value) {
        calcNav()
    }
})

const pressBackTo = () => {
    emit('onPressBack')
}

const calcNav = () => {
    const sys = uni.getSystemInfoSync()
    const isAndroid = sys.platform === 'android'
    navState.value.statusBarHeight = sys.statusBarHeight! // 获取状态栏高度
    navState.value.navBarHeight = isAndroid ? 48 : 44 // 获取导航栏高度，安卓为48px，iOS为44px
}
</script>

<style lang="scss" scoped>
.sl-tabbar-page {
    height: 100%;
    position: relative;

    &__content {
        overflow: auto;
        position: relative;
    }

    .sl-tab-bar {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
    }
}
</style>