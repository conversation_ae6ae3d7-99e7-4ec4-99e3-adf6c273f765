<template>
    <image class="sl-svg-icon" :src="iconUrl" :style="{ width: sizeUnit, height: sizeHUnit }" :mode="mode" v-if="iconUrl"
        @click="$emit('click')" />
</template>

<script setup lang="ts">
import { computed, withDefaults, defineProps } from 'vue'
import { toRpx } from '@/utils/toRpx'
// 小程序 <image> 组件可选的 mode 类型
type ImageMode =
    | 'scaleToFill'
    | 'aspectFit'
    | 'aspectFill'
    | 'widthFix'
    | 'heightFix'
    | 'top'
    | 'bottom'
    | 'center'
    | 'left'
    | 'right'
    | 'top left'
    | 'top right'
    | 'bottom left'
    | 'bottom right'
/**
 * SvgIcon 组件：
 * @prop name: 图标文件名，不带扩展名，格式 “文件夹-完整文件名”
 *   例如：meeting-16-1-search => static/icons/meeting/16-1-search.svg
 * @prop size: 宽高，可传 Number（当 px 处理）或合法 CSS 大小字符串
 * @prop mode: <image> 渲染模式，默认 'aspectFit'
 */
const props = withDefaults(
    defineProps<{ name: string; size?: number | string; sizeH?: number | string; mode?: ImageMode }>(),
    { mode: 'aspectFit' as ImageMode }
)
const emits = defineEmits(["click"]);

// 计算宽高
// const sizePx = computed(() => {
//     const v = props.size ?? 24
//     return typeof v === 'number' ? `${v}px` : /^\d+$/.test(v) ? `${v}px` : v
// })
// 计算最终 rpx 值
const sizeUnit = computed(() => toRpx(props.size ?? 24))
const sizeHUnit = computed(() => toRpx(props.sizeH ?? props.size ?? 24))

// 生成图标路径，使用 static 目录
const iconUrl = computed(() => {
    // 支持 ui 导出格式： 52-52-6 这种格式
    const arr = props.name.split('-')
    if (arr[0] == arr[1]) {
        if (arr.length < 3) return ''
        const fileName = arr.slice(2).join('-')
        return `/static/icons/${arr[0]}-${arr[1]}/${fileName}.svg`
    }
    // 支持 folder-name
    const [folder, ...rest] = props.name.split('-')
    const fileName = rest.join('-')
    if (!folder || !fileName) return ''
    return `/static/icons/${folder}/${fileName}.svg`
})

const { mode } = props
</script>
<style lang="scss">
.sl-svg-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>