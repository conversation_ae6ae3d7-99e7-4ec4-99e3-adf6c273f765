<script setup lang="ts">
import { ref } from 'vue';
import type { MenuItem } from '../models/menu'; // 大小写保持一致。
// 组件 Props
defineProps<{
    menuItems: MenuItem[];
}>();
const isProd = import.meta.env.APP_NODE_ENV === "production";
const activeDropdowns = ref(new Set<string>());
// 监听路由变化
const toggleDropdown = (menuLabel: string) => {
    if (activeDropdowns.value.has(menuLabel)) {
        activeDropdowns.value.delete(menuLabel); // 关闭菜单
    } else {
        activeDropdowns.value.add(menuLabel); // 展开菜单
    }
};

</script>

<template>
    <ul class="menu">
        <template v-for="item in menuItems" :key="item.label">
            <!-- 没有子菜单的情况 -->
        <template v-if="item.isProdHide ? !isProd : true">
            <li v-if="!item.children" class="menu-item">
                <!-- <a :href="item.link" :class="{ active: activeItem === item.link }">
                    <i v-if="item.icon" :class="item.icon"></i>
                    <span class="text">{{ item.label }}</span>
                </a> -->
                <router-link :to="item.path || (item.name ? { name: item.name } : null) || '/'"
                    :class="{ active: $route.path === item.path || (item.name && $route.name === item.name) }">
                    <i v-show="item.icon" :class="item.icon"></i>
                    <span class="text">{{ item.label }}</span>
                </router-link>
            </li>

            <!-- 有子菜单的情况 -->
            <li v-else class="menu-item dropdown" :class="{ open: activeDropdowns.has(item.label) }">
                <a href="javascript:void(0)" class="dropdown-toggle" @click="toggleDropdown(item.label)">
                    <i v-show="item.icon" :class="item.icon"></i>
                    <span class="text">{{ item.label }}</span>
                    <span class="caret" :class="{ rotated: activeDropdowns.has(item.label) }"></span>
                </a>
                <ul class="dropdown-menu" v-show="activeDropdowns.has(item.label)">
                    <Menu :menuItems="item.children"></Menu>
                </ul>
            </li>
            </template>
        </template>
    </ul>
</template>
<style scoped>
.menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-item {
    position: relative;
}

/* 一级 & 二级菜单 */
.menu a {
    text-decoration: none;
    color: var(--mt-sidebar-text-color);
    padding: 12px 0 12px 3rem;
    display: flex;
    align-items: center;
    border-radius: 5px;
    transition: background 0.3s, color 0.3s;
    font-size: 1.25rem;
    font-weight: bold;
}

/* 选中状态高亮 */
.menu a.active {
    background: #4F7AF6;
    border-radius: 50px 0 0 50px;
}

/* hover 效果 */
.menu a:hover {
    background: #2C4178;
}

/* 子菜单 */
.dropdown-menu {
    list-style: none;
    padding-left: 0;
    margin: 0;
    background: #273761;
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transition: max-height 0.3s ease-out, opacity 0.3s ease-out;

}

/* 展开状态 */
.dropdown.open .dropdown-menu {
    max-height: 500px;
    opacity: 1;
}

/* 子菜单项 */
.dropdown-menu li a {
    padding: 12px 0 12px 4.25rem;
    display: block;
    transition: background 0.3s;
    font-size: 16px;
    font-weight: normal;
}

.dropdown-menu li a:hover {
    background: #2C4178;
    /* background: var(--mt-sidebar-bg); */
}

.dropdown-toggle {
    position: relative;
}

/* caret 箭头 */
/* 默认箭头方向（向下） */
.dropdown-toggle .caret {
    position: absolute;
    content: '';
    width: 0;
    height: 0;
    right: 18px;
    top: 50%;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #fff;
    transition: transform 0.3s ease-in-out;
    transform: translateY(-50%);
}

.caret {
    margin-left: auto;
    transition: transform 0.3s ease-in-out;
}

.caret.rotated {
    transform: rotate(180deg);
}
.text{
    margin-left: 13px;
}
</style>
