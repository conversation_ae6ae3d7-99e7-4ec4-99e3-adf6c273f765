<template>
  <view class="container">
    <!-- 内容插槽 -->
    <view class="content" :style="{ paddingBottom: contentBottom + 'px' }">
      <slot></slot>
    </view>
    
    <!-- 底部导航 -->
    <view class="home-footer" :style="{ bottom: safeArea + 'px' }">
      <view
        v-for="(item, index) in tabList"
        :key="index"
        :class="{ active: activeIndex === index }"
        @click="handleFooterClick(index, item.url)"
      >
        <SlSvgIcon 
          :name="activeIndex === index ? item.activeIcon : item.defaultIcon"
          :size="activeIndex === index ? '57' : '22'"
          class="i-footer"
        />
        <text>{{ item.text }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from "vue";
import SlSvgIcon from "@/components/SlSVgIcon.vue";

const safeArea = ref(0);
const contentBottom = ref(49); // 默认底部导航高度

onMounted(() => {
  getSafeArea();
});

const getSafeArea = () => {
  const systemInfo = uni.getSystemInfoSync();
  const safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0;
  safeArea.value = Number(safeAreaBottom);
  contentBottom.value = 49 + safeArea.value + 23; // 底部导航高度 + 安全区域
};

const props = defineProps({
  // 当前页面路径
  currentPath: {
    type: String,
    required: true
  }
});

const tabList = [
  {
    text: "工作台",
    defaultIcon: "22-22-1",
    activeIcon: "57-57-1",
    url: "/pages/home/<USER>"
  },
  {
    text: "demo",
    defaultIcon: "22-22-58",
    activeIcon: "57-57-2",
    url: "/pages/demo/index"
  },
  {
    text: "我的",
    defaultIcon: "22-22-3",
    activeIcon: "57-57-3",
    url: "/pages/mine/index"
  }
];

// 根据当前路径计算activeIndex
const activeIndex = computed(() => {
  return tabList.findIndex(item => item.url === props.currentPath);
});

function handleFooterClick(index: number, url: string) {
  if (url) {
    uni.switchTab({ url: url });
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  position: relative;
}

.content {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
  // background: #f2f4f8;
}

.home-footer {
  width: 100%;
  position: fixed;
  height: 49px;
  background: #ffffffef;
  backdrop-filter: blur(20px);
  border-radius: 10px 10px 0 0;
  box-shadow: 0 -0.5px 6px 0 #5d5d5d33;
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 999;
  /* bottom 属性现在通过 style 绑定动态设置 */
}

.home-footer view {
  width: 57px;
  height: 41px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.i-footer {
  display: block;
  position: absolute;
  top: 0;
}

.home-footer view text {
  color: #666;
  font-size: 10px;
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: 0;
  transition: color 0.2s;
}

.home-footer view.active text {
  color: #005ac5;
}

.home-footer view.active .i-footer {
  top: -23px;
}
</style>