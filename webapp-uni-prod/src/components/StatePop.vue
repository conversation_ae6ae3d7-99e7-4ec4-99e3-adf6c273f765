<template>
    <!-- 弹出框 -->
    <up-popup
      :show="show"
      :round="round"
      :mode="mode"
      @close="handleClose"
      :closeable="closeable"
      :close-on-click-overlay="closeOnClickOverlay"
      :custom-style="popupStyle"
    >
      <div class="popup-content" :class="[contentClass, `popup-${type}`]">
        <SlSvgIcon 
          v-if="icon" 
          :name="icon" 
          :size="iconSize" 
          class="popup-icon"
        />
        <div class="popup-text">
          <span v-if="title" class="popup-title">{{ title }}</span>
          <span v-if="content" class="popup-content-text">{{ content }}</span>
          <slot></slot>
        </div>
        <button 
          v-if="showConfirmButton" 
          class="popup-button"
          @click="handleConfirm"
        >
          {{ confirmText }}
        </button>
        <button 
          v-if="showCancelButton" 
          class="popup-button popup-button-cancel"
          @click="handleCancel"
        >
          {{ cancelText }}
        </button>
      </div>
    </up-popup>
  </template>
  
  <script setup lang="ts">
  import { ref, computed } from 'vue';
  import SlSvgIcon from '@/components/SlSVgIcon.vue';
  
  interface Props {
    show: boolean; // 是否显示弹窗
    title?: string; // 标题
    content?: string; // 内容文本
    type?: 'success' | 'warning' | 'error' | 'info'| string; // 弹窗类型
    icon?: string; // 图标名称
    iconSize?: string; // 图标大小
    mode?: 'top' | 'bottom' | 'center' | 'left' | 'right'; // 弹出位置
    round?: number; // 圆角大小
    closeable?: boolean; // 是否显示关闭按钮
    closeOnClickOverlay?: boolean; // 点击遮罩是否关闭
    showConfirmButton?: boolean; // 是否显示确认按钮
    confirmText?: string; // 确认按钮文本
    showCancelButton?: boolean; // 是否显示取消按钮
    cancelText?: string; // 取消按钮文本
    contentClass?: string; // 内容区域自定义class
    popupStyle?: Record<string, any>; // 弹窗自定义样式
  }
  
  const props = withDefaults(defineProps<Props>(), {
    show: false,
    type: 'info',
    iconSize: '40',
    mode: 'bottom',
    round: 10,
    closeable: true,
    closeOnClickOverlay: true,
    showConfirmButton: true,
    confirmText: '确认',
    showCancelButton: false,
    cancelText: '取消',
    contentClass: '',
    popupStyle: () => ({})
  });
  
  const emit = defineEmits(['close', 'confirm', 'cancel', 'update:show']);
  
  const handleClose = () => {
    emit('update:show', false);
    emit('close');
  };
  
  const handleConfirm = () => {
    emit('confirm');
    handleClose();
  };
  
  const handleCancel = () => {
    emit('cancel');
    handleClose();
  };
  
  // 根据类型自动设置图标  TODO
  const computedIcon = computed(() => {
    if (props.icon) return props.icon;
    switch (props.type) {
      case 'success': return 'components-success';
      case 'warning': return 'components-war';
      case 'error': return 'components-lose';
      case 'info': 
      default: return 'components-info';
    }
  });
  </script>
  
  <style scoped>
  .popup-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .popup-icon {
    margin-bottom: 12px;
  }
  
  .popup-text {
    margin-bottom: 20px;
  }
  
  .popup-title {
    color: #333333;
    font-size: 14px;
    font-weight: bold;
    display: block;
    /* margin-bottom: 8px; */
  }
  
  .popup-content-text {
    margin-top: 7px;
    font-size: 14px;
    color: #666;
    display: block;
  }
  
  .popup-button {
    width: 100%;
    /* padding: 10px 0; */
    background-color: #0066DF;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    margin-top: 10px;
  }
  
  .popup-button-cancel {
    background-color: #f5f5f5;
    color: #333;
    margin-top: 10px;
  }
  
  /* 不同类型样式 */
  /* .popup-success {
    color: #07c160;
  }
  
  .popup-warning {
    color: #ff976a;
  }
  
  .popup-error {
    color: #ee0a24;
  }
  
  .popup-info {
    color: #1989fa;
  } */
  </style>