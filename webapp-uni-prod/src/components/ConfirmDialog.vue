<template>
  <div class="dialog-overlay">
    <div class="dialog-box" :style="styleProps" >
      <header v-if="showHeader">
        <slot name="header">
          <div class="dialog-t">
            <SlSvgIcon style="display: inline-flex;" name="components-warn" size="16" />
            <span>提示</span>
          </div>
        </slot>
      </header>
      <!-- 插槽内容：自定义提醒内容 -->
      <main class="dialog-content">
        <slot>确定要执行此操作吗？</slot>
      </main>
      <footer>
        <slot name="footer">
          <div class="dialog-actions">
            <div @click="$emit('close')" style="color: #666666" class="dialog-actions-div">{{ cancelText }}</div>
            <div @click="$emit('confirm')" class="dialog-actions-div">{{ confirmText }}</div>
          </div>
        </slot>
      </footer>

    </div>
  </div>
</template>

<script setup lang="ts">
import SlSvgIcon from '@/components/SlSVgIcon.vue'
interface Props {
  background?: string
  width?: string
  /** 取消文字 */
  cancelText?: string
  /** 确认文字 */
  confirmText?: string
  showHeader?: boolean
  styleObj?: any
}
defineEmits(["close", "confirm"]);
const props = withDefaults(defineProps<Props>(), {
  background: '#fff',
  width: '320px',
  cancelText: '取消',
  confirmText: '确定',
  showHeader: true,
} )

const styleProps = computed(() => {
  return Object.assign({background: props.background, width: props.width}, props.styleObj)
})
</script>

<style scoped lang="scss">
.dialog-overlay {
  padding: 15px;
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-box {
  padding-top: 24px;
  border-radius: 10px;
  position: relative;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 12px;
  border: none;
  background: transparent;
  font-size: 20px;
  cursor: pointer;
}

.dialog-content {
  padding: 20px 10px;
  font-size: 16px;
  text-align: center;
}

.dialog-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  border-top: 1px solid #e1e1e1;
  width: 100%;
}

.dialog-actions .dialog-actions-div {
  width: 50%;
  height: 48px;
  text-align: center;
  line-height: 48px;
  font-size: 16px;
}

.dialog-actions .dialog-actions-div:nth-child(1) {
  border-right: 1px solid #e1e1e1;
}

.cancel-btn,
.confirm-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.cancel-btn {
  background-color: #ddd;
  color: #333;
}

.confirm-btn {
  background-color: #007bff;
  color: white;
}

.dialog-t {
  color: red;
  text-align: center;
  font-size: 18px;
  height: 24px;
  line-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-t span {
  margin-left: 8px;
}
</style>
