<template>
  <div class="dialog-overlay">
    <div class="dialog-box">
      <div class="dialog-t">
        <svg class="icon-20">
          <use :xlink:href="`#icon-meeting-12-11`" />
        </svg>
        <span>提示</span>
      </div>
      <!-- 插槽内容：自定义提醒内容 -->
      <div class="dialog-content">
        <slot>确定要执行此操作吗？</slot>
      </div>
      <div class="dialog-actions">
        <!-- <button class="cancel-btn" @click="$emit('close')">取消</button>
        <button class="confirm-btn" @click="$emit('confirm')">确定</button> -->
        <div @click="$emit('close')" style="color: #666666">关闭</div>
        <div @click="$emit('confirm')">确定</div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineEmits(["close", "confirm"]);
</script>

<style scoped>
.dialog-overlay {
  padding: 15px;
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-box {
  background: #fff;
  padding-top: 24px;
  width: 320px;
  border-radius: 10px;
  position: relative;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 12px;
  border: none;
  background: transparent;
  font-size: 20px;
  cursor: pointer;
}

.dialog-content {
  padding: 20px 10px;
  font-size: 16px;
  text-align: center;
}

.dialog-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  border-top: 1px solid #e1e1e1;
  width: 100%;
}
.dialog-actions div {
  width: 50%;
  height: 48px;
  text-align: center;
  line-height: 48px;
  font-size: 16px;
}
.dialog-actions div:nth-child(1) {
  border-right: 1px solid #e1e1e1;
}

.cancel-btn,
.confirm-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.cancel-btn {
  background-color: #ddd;
  color: #333;
}

.confirm-btn {
  background-color: #007bff;
  color: white;
}
.dialog-t {
  color: red;
  text-align: center;
  font-size: 18px;
  height: 24px;
  line-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dialog-t span {
  margin-left: 8px;
}
</style>
