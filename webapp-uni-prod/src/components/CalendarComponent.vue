<template>
  <scroll-view
    scroll-x
    class="demo-c"
    :scroll-left="scrollLeft"
    scroll-with-animation
    scroll-anchoring
    ref="containerRef"
  >
    <view
      v-for="(day, index) in dayList"
      :key="index"
      class="demo-div"
      :class="getDayClass(day)"
      @tap="selectDate(day.date)"
      :id="'day-' + index"
    >
      <text class="date-text">{{ day.displayDate }}</text>
      <text class="week-text">{{
        day.vacationInfo ? day.vacationInfo : day.weekday
      }}</text>
      <text class="holiday-text" v-if="day.holidayInfo">{{
        day.holidayInfo
      }}</text>
      <text class="workday-text" v-if="day.isWorkday">班</text>
    </view>
  </scroll-view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed,defineProps } from "vue";
import dayjs from "dayjs";
import chineseDays, {
  getDayDetail,
  getHolidaysInRange,
  getWorkdaysInRange,
  isInLieu,
  isWorkday,
  isHoliday,
} from "chinese-days";

// 定义组件props和事件
const props = defineProps({
  // 基准日期，默认为今天
  baseDate: {
    type: String,
    default: dayjs().format("YYYY-MM-DD")
  },
  // 向前显示的天数
  daysBefore: {
    type: Number,
    default: 30
  },
  // 向后显示的周数配置：1-本周，2-本周+下周
  weeksAfter: {
    type: Number,
    default: 1,
    validator: (value: number) => [1, 2].includes(value)
  }
});

const emit = defineEmits(["dateChange"]);

interface DayItem {
  date: string;
  displayDate: string;
  weekday: string;
  isHoliday?: boolean;
  isWorkday?: boolean;
  holidayInfo?: string;
  vacationInfo?: string;
}

/**节假日信息*/
interface VacationArray {
  date: string;
  work: boolean;
  name: string;
}

const dayList = ref<DayItem[]>([]);
const activeDate = ref("");
const scrollLeft = ref(0);

// 计算向后显示的天数（根据周数配置）
const daysAfter = computed(() => {
  const base = dayjs(props.baseDate);
  const endOfThisWeek = base.endOf('week'); // 本周日
  const daysThisWeek = endOfThisWeek.diff(base, 'day') + 1; // 本周剩余天数
  
  if (props.weeksAfter === 1) {
    return daysThisWeek;
  } else {
    const endOfNextWeek = endOfThisWeek.add(1, 'week').endOf('week');
    return endOfNextWeek.diff(base, 'day') + 1;
  }
});

// 生成日期范围（带节假日信息）
function generateDateRange() {
  const base = dayjs(props.baseDate);
  const startDate = base.subtract(props.daysBefore, 'day');
  const endDate = base.add(daysAfter.value, 'day');
  
  const list: DayItem[] = [];
  const dateRange = `${startDate.format('YYYY-MM-DD')},${endDate.format('YYYY-MM-DD')}`;
  
  // 获取范围内的节假日和调班信息
  const holidaysExcludingWeekends: any = getHolidaysInRange(
    startDate.format('YYYY-MM-DD'),
    endDate.format('YYYY-MM-DD'),
    false
  );
  
  const vacationArray: Array<VacationArray> = holidaysExcludingWeekends.map((d: any) => getDayDetail(d));
  const map = new Map<string, VacationArray>();
  for (const item of vacationArray) {
    if (!map.has(item.name)) {
      map.set(item.name, item);
    }
  }
  const deVacationArray: Array<VacationArray> = Array.from(map.values());

  let currentDate = startDate;
  while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
    const dateStr = currentDate.format("YYYY-MM-DD");
    const dayDetail: any = getDayDetail(dateStr);
    const dayItem: DayItem = {
      date: dateStr,
      displayDate: currentDate.format("MM/DD"),
      weekday: `周${"日一二三四五六".charAt(currentDate.day())}`,
    };
    
    // 添加节假日信息
    if (vacationArray.some((d) => d.date === dateStr)) {
      dayItem.isHoliday = true;
      dayItem.holidayInfo = dayDetail.holiday || "休";
      dayItem.vacationInfo = deVacationArray.find((d) => d.date === dateStr)?.name.split(",")[1] || "";
    }
    
    // 添加调班信息（周末上班）
    if (isWorkday(dateStr) && [0, 6].includes(currentDate.day())) {
      dayItem.isWorkday = true;
    }
    
    list.push(dayItem);
    currentDate = currentDate.add(1, 'day');
  }
  
  return list;
}

function isPast(dateStr: string) {
  return dayjs(dateStr).isBefore(dayjs(), "day");
}

function getDayClass(day: DayItem) {
  return {
    past: isPast(day.date),
    active: activeDate.value === day.date,
    holiday: day.isHoliday,
    workday: day.isWorkday,
  };
}

function scrollToBaseDate() {
  const baseDate = props.baseDate;
  activeDate.value = baseDate;
  const baseIndex = dayList.value.findIndex((d) => d.date === baseDate);
  if (baseIndex === -1) return;
  
  // 计算滚动位置（基准日期显示在第二个位置）
  scrollLeft.value = baseIndex * 66 - 66;
}

// 初始化数据
function initData() {
  dayList.value = generateDateRange();
  setTimeout(scrollToBaseDate, 100);
}

// 监听props变化
watch(() => [props.baseDate, props.daysBefore, props.weeksAfter], () => {
  initData();
}, { immediate: true });

/**值传递*/ 
function selectDate(date: string) {
  console.log("选中日期：", date);
  activeDate.value = date;
  emit("dateChange", date);
}
</script>

<style lang="scss">
.demo-c {
  width: 100%;
  white-space: nowrap;
  padding: 10px 0;

  .demo-div {
    width: 60px;
    height: 70px; /* 增加高度容纳节假日信息 */
    border: 1px solid #ccc;
    text-align: center;
    padding: 5px;
    box-sizing: border-box;
    margin-right: 6px;
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    border-radius: 8px;

    .date-text {
      font-size: 14px;
      margin-bottom: 2px;
    }

    .week-text {
      font-size: 12px;
      margin-bottom: 2px;
    }

    .holiday-text {
      font-size: 10px;
      color: #ff4d4f;
      background: #fff2f0;
      padding: 0 3px;
      border-radius: 2px;
      margin-top: 2px;
      position: absolute;
      top: 0px;
      right: 0px;
    }

    .workday-text {
      position: absolute;
      top: 2px;
      right: 2px;
      font-size: 10px;
      color: white;
      background: #fa8c16;
      width: 12px;
      height: 12px;
      line-height: 12px;
      border-radius: 50%;
    }

    &.past {
      color: #aaa;
      background-color: #f5f5f5;

      .holiday-text {
        background: #f0f0f0;
      }
    }

    &.active {
      background-color: #005cc8;
      color: white;
      border-color: #005cc8;

      .holiday-text {
        color: white;
        background: rgba(255, 255, 255, 0.2);
      }
    }

    &.holiday:not(.past) {
      background-color: #fff2f0;
      border-color: #ffccc7;
    }

    &.workday:not(.past) {
      background-color: #fff7e6;
      border-color: #ffe7ba;
    }
  }
}
</style>