<!-- 滚动日历 XJM -->
<template>
	<scroll-view
		scroll-x
		class="demo-c"
		:scroll-left="scrollLeft"
		scroll-with-animation
		scroll-anchoring
		ref="containerRef"
	>
		<view
			v-for="(day, index) in dayList"
			:key="index"
			class="demo-Top"
			@tap="selectDate(day.date)"
			:id="'day-' + index"
		>
			<div
				class="demo-div"
				:class="getDayClass(day)"
				id="demo-div"
			>
				<text
					class="date-text"
					:style="{
						color: day.type === 4 ? '#999999' : '#333333',
					}"
					>{{ day.displayDate }}</text
				>
				<text class="week-text">{{ day.dayOrHolidayName }}</text>
			</div>
			<div
				class="workday-text"
				v-if="day.type === 1 || day.type === 2"
			>
				<SlSVgIcon
					:name="`14-14-${day.type === 1 ? '11' : '10'}`"
					size="14"
				/>
			</div>
		</view>
	</scroll-view>
</template>

<script setup lang="ts">
	import { ref, onMounted, watch, computed, getCurrentInstance } from 'vue';
	import dayjs from 'dayjs';
	const instance = getCurrentInstance();
	const proxy = instance?.proxy;
	const itemWidth = ref(0); // 实际的 demo-Top 宽度
	let widthReady = false;

	// 计算实际使用的基准日期
	const actualBaseDate = computed(() => {
		return props.baseDate || dayjs().format('YYYY-MM-DD');
	});

	onMounted(() => {
		setTimeout(() => {
			getItemWidth();
		}, 500);
	});

	// 定义组件props和事件
	const props = defineProps({
		// 基准日期，默认为今天
		baseDate: {
			type: String,
			default: dayjs().format('YYYY-MM-DD'),
		},
		// 后端返回的日期数据
		dateData: {
			type: Array as () => Array<{
				date: string;
				type: number; // 3-工作日, 4-周末
				dayOrHolidayName: string;
			}>,
			required: true,
		},
		modelValue: String, // 新增
	});

	const emit = defineEmits(['dateChange', 'update:modelValue']);

	interface DayItem {
		date: string;
		displayDate: string;
		dayOrHolidayName: string;
		type: number; // 1-节假日,2-补班日,3-工作日,4-非工作日
	}

	const dayList = ref<DayItem[]>([]);
	const activeDate = ref('');
	const scrollLeft = ref(0);

	// 处理后端数据，转换为组件需要的格式
	function processDateData() {
		return props.dateData.map((item) => ({
			date: item.date,
			displayDate: dayjs(item.date).format('MM/DD'),
			dayOrHolidayName: item.dayOrHolidayName,
			type: item.type,
		}));
	}

	function isPast(dateStr: string) {
		return dayjs(dateStr).isBefore(dayjs(), 'day');
	}

	function getDayClass(day: DayItem) {
		return {
			past: isPast(day.date),
			active: activeDate.value === day.date,
			holiday: day.type === 4, // 周末/假日
			workday: day.type === 3, // 工作日
		};
	}

	function scrollToBaseDate() {
		const baseDate = actualBaseDate.value;
		emit('dateChange', baseDate);
		emit('update:modelValue', baseDate); // 初始化同步 v-model
		activeDate.value = baseDate;
		scrollToDate(baseDate);
	}
	function scrollToDate(targetDate: string) {
		const index = dayList.value.findIndex((d) => d.date === targetDate);
		if (index === -1) return;

		const marginRight = 8;
		const totalItemWidth = itemWidth.value + marginRight;

		// ✅ 目标显示在第 2 个位置
		const targetIndex = Math.max(index - 1, 0);
		scrollLeft.value = targetIndex * totalItemWidth;
	}
	/**值传递*/
	function selectDate(date: string) {
		activeDate.value = date;
		emit('dateChange', date);
		emit('update:modelValue', date); // 更新 v-model
	}

	// 初始化数据
	function initData() {
		dayList.value = processDateData();
	}

	// 监听props变化
	watch(
		() => [actualBaseDate.value, props.dateData],
		() => {
			initData();
		},
		{ immediate: true, deep: true }
	);
	watch(
		() => props.modelValue,
		(newVal) => {
			// if (newVal && newVal !== activeDate.value) {
			//   activeDate.value = newVal;
			//   scrollToDate(newVal); // 滚动到选中
			// }
			if (newVal) {
				activeDate.value = newVal;

				// 根据宽度准备状态决定执行方式
				widthReady ? scrollToDate(newVal) : getItemWidth();
			}
		}
	);
	// 动态获取日期寬度
	function getItemWidth() {
		const query = uni.createSelectorQuery().in(proxy);
		query
			.select('.demo-Top')
			.boundingClientRect((res: any) => {
				if (res) {
					itemWidth.value = res.width;
					// ✅ 延迟触发，确保事件可监听
					setTimeout(() => {
						scrollToBaseDate();
						widthReady = true;
					}, 0);
				}
			})
			.exec();
	}
</script>

<style lang="scss">
	.demo-c {
		width: 100%;
		white-space: nowrap;
		padding: 6px 0;

		.demo-Top {
			display: inline-flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			position: relative;
			margin-right: 8px;
			width: 52px;
			height: 60px;
		}

		.demo-div {
			background: #f6f8fa;
			width: 46px;
			height: 54px;
			// border: 1px solid #ccc;
			text-align: center;
			padding: 5px;
			box-sizing: border-box;
			border-radius: 5px;
			overflow: visible;
			display: inline-flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			position: relative;

			.date-text {
				font-size: 12px;
				margin-bottom: 2px;
			}

			.week-text {
				font-size: 10px;
				margin-bottom: 2px;
				color: #999999;
			}

			&.past {
				color: #aaa;
				// background-color: #f5f5f5;
				background: #e4e9ed;
			}

			&.active {
				background-color: #005cc8;
				color: white;
				border-color: #005cc8;
				.date-text,
				.week-text {
					color: #ffffff !important;
				}
			}
		}

		.workday-text {
			position: absolute;
			top: 0px;
			right: 0px;
		}
		.date-text {
			color: #333333;
		}
	}
</style>
