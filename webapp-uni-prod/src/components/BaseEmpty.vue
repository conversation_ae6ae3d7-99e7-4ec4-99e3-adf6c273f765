<template>
    <div class="empty-container">
        <SlSVgIcon name="40-40-1" size="30" />
        <!-- <svg class="icon-30">
            <use xlink:href="#icon-home-暂无"></use>
        </svg> -->
        <slot>暂无数据</slot>
    </div>
</template>
<script setup lang="ts">

</script>

<style lang="css" scoped>
.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #999999;
    font-size: 12px;
}

/* 
.empty-img {
    background: url('@/assets/img/empty.png') no-repeat center;
    width: 178px;
    height: 178px;
    background-size: 100%;
} */
</style>