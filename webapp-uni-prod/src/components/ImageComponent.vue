<template>
	<up-image
		v-if="src"
		:src="src"
		:radius="radius"
		:width="sizeUnit"
		:height="sizeUnit"
		:lazy-load="isLazyLoad"
	>
	</up-image>
	<SlSVgIcon
		v-else
		:name="emptyName"
		:size="size"
	/>
</template>
<script setup lang="ts">
	import { toRpx } from '@/utils/toRpx';

	const props = withDefaults(
		defineProps<{
			src?: string;
			size?: number;
			emptyName?: string;
			radius?: number;
			isLazyLoad?: boolean;
		}>(),
		{
			src: '',
			size: 70,
			emptyName: '70-70-1',
			radius: 6,
			isLazyLoad: true,
		}
	);

	const sizeUnit = computed(() => toRpx(props.size));
</script>
<style lang="scss"></style>
