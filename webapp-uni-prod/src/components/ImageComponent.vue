<template>
    <image v-if="src" :src="src" :style="{ width: sizeUnit, height: sizeUnit }" mode="aspectFill"></image>
    <SlSVgIcon v-else name="70-70-1" :size="size" />
</template>
<script setup lang="ts">
import { toRpx } from '@/utils/toRpx';


const props = withDefaults(defineProps<{
    src?: string,
    size?: number
}>(), {
    src: '',
    size: 70
} )

const sizeUnit = computed(() => toRpx(props.size))

</script>
<style lang="scss"></style>