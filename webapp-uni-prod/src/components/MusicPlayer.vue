<template>
    <div class="music-player">
        <!-- audio -->
        <audio id="bg-audio" ref="audioRef" src="/music/bg-music.mp3" autoplay loop preload="auto"></audio>
        <!-- 浮动图标 -->
        <div class="icon" :class="{ playing, isHomePage }" @click="togglePlay" ref="iconRef">
            <!-- 播放和暂停状态 -->
            <img v-if="!playing" src="@/assets/img/music-pause.svg" alt="播放音乐" />
            <img v-else src="@/assets/img/music-play.svg" alt="暂停音乐" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, computed } from 'vue'
const iconRef = ref<HTMLElement | null>(null)
const audioRef = ref<HTMLAudioElement | null>(null)
const playing = ref(false)
import { useRoute } from 'vue-router'
const route = useRoute()
// 是否是home 页面
const isHomePage = computed(() => route.name === 'home')

/**
 * 切换播放状态
 */
function togglePlay() {
    if (!audioRef.value) return

    if (playing.value) {
        audioRef.value.pause()
        playing.value = !playing.value
    } else {
        // 部分移动端需要用户交互才能播放
        audioRef.value.play().then(() => {
            playing.value = !playing.value
        }).catch(() => {
            // 播放失败时可以给用户一个提示
            console.error('请先允许媒体自动播放')
        })
    }

}

// 如果想页面加载完就尝试自动播放（考虑浏览器会被静音策略拦截）
// 在挂载后尝试“模拟”一次点击
onMounted(() => {
    nextTick(() => {
        console.log('nextTick', iconRef.value)
        if (!iconRef.value) return
        // iconRef.value.click()
        // 有些环境只需要一次 click，有些需要先 dispatch mousedown/up
        const types = ['mousedown', 'mouseup', 'click'] as const
        types.forEach(type => {
            const e = new MouseEvent(type, {
                bubbles: true,
                cancelable: true,
                view: window
            })
            iconRef.value!.dispatchEvent(e)
        })
    })


    // document.addEventListener(
    //     'touchstart',
    //     () => {
    //         const audio = document.getElementById('bg-audio') as HTMLAudioElement
    //         if (!audio) return
    //         // 解除静音
    //         audio.muted = false
    //         // 重新 play（有些浏览器即便 autoplay 也需要再次调用 play）
    //         audio.play().catch(() => {
    //             console.warn('再次调用 play 仍被拦截')
    //         })
    //     },
    //     { once: true }
    // )
})
</script>

<style lang="css" scoped>
.music-player .icon {
    position: fixed;
    top: 12px;
    /* rem 单位会被 postcss-pxtorem 转换 */
    right: 12px;
    width: 24px;
    height: 24px;
    z-index: 9999;
    align-items: center;
    justify-content: center;
    display: none;
}

.music-player .icon.isHomePage {
    display: flex;
}

.music-player .icon img {
    width: 100%;
    max-height: 100%;
}

/* 播放时给图标加个旋转动画（可选） */
.music-player .icon.playing {
    animation: spin 3s linear infinite;
}

/* 旋转动画 */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>