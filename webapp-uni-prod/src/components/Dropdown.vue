<template>
    <div class="dropdown-wrapper" ref="dropdownRef">
        <div class="dropdown-trigger" @click="toggleDropdown" @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave">
            <slot name="trigger"></slot>
        </div>

        <Teleport to="body">
            <Transition name="fade">
                <div v-if="isOpen" class="dropdown-menu"
                    :style="{ top: `${position.top}px`, left: `${position.left}px` }" ref="dropdownMenuRef"
                    @mouseenter="clearHideTimeout" @mouseleave="scheduleHideDropdown">
                    <slot></slot>
                </div>
            </Transition>
        </Teleport>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';

const isOpen = ref(false);
const dropdownRef = ref(null);
const dropdownMenuRef = ref(null);
const position = ref({ top: 0, left: 0 });
let hideTimeout = null;

const toggleDropdown = async () => {
    isOpen.value = !isOpen.value;
    if (isOpen.value) {
        await nextTick();
        updatePosition();
    }
};

const updatePosition = () => {
    const triggerEl = dropdownRef.value;
    const menuEl = dropdownMenuRef.value;
    if (!triggerEl || !menuEl) return;

    const rect = triggerEl.getBoundingClientRect();
    position.value = {
        top: rect.bottom + window.scrollY + 4, // 添加 4px 间距
        left: rect.left + window.scrollX,
    };
};

const handleClickOutside = (event) => {
    if (
        dropdownRef.value &&
        !dropdownRef.value.contains(event.target) &&
        dropdownMenuRef.value &&
        !dropdownMenuRef.value.contains(event.target)
    ) {
        isOpen.value = false;
    }
};

const handleMouseEnter = () => {
    clearTimeout(hideTimeout);
};

const handleMouseLeave = () => {
    scheduleHideDropdown();
};

const scheduleHideDropdown = () => {
    hideTimeout = setTimeout(() => {
        isOpen.value = false;
    }, 300); // 延迟隐藏，增加用户体验
};

const clearHideTimeout = () => {
    clearTimeout(hideTimeout);
};

onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.dropdown-wrapper {
    display: inline-block;
    position: relative;
}

.dropdown-menu {
    position: absolute;
    min-width: 160px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    z-index: 1000;
    font-size: 14px;
}

.dropdown-menu ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.dropdown-menu li {
    padding: 8px 16px;
    cursor: pointer;
}

.dropdown-menu li:hover {
    background: #f5f5f5;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.2s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>