<template>
  <div class="sl-textarea">
    <textarea
      class="textarea"
      :value="modelValue"
      @input="onInput"
      :placeholder="placeholder"
      :maxlength="maxlength"
      :disabled="disabled"
    ></textarea>
    <div v-if="showWordLimit && maxlength" class="word-limit">
      {{ valueLength }}/{{ maxlength }}
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue: string;
  placeholder?: string;
  maxlength?: number;
  disabled?: boolean;
  showWordLimit?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: "请输入",
  disabled: false,
  showWordLimit: false,
});

const emit = defineEmits(["update:modelValue", "change"]);

const valueLength = computed(() => props.modelValue?.length || 0);

const onInput = (event: any) => {
  const target = event.target as HTMLTextAreaElement;
  const value = target.value;
  emit("update:modelValue", value);
  emit("change", value);
};
</script>

<style scoped lang="scss">
.sl-textarea {
  width: 100%;
  position: relative;

  .textarea {
    width: auto;
    height: 56px;
    border-radius: 6px;
    background: #f8f8f8;
    padding: 6px 8px;
    line-height: 1.5;
  }

  .word-limit {
    position: absolute;
    bottom: 5px;
    right: 8px;
    font-size: 12px;
    color: #999;
  }
}
</style>
