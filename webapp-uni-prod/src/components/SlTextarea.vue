<template>
  <view class="sl-textarea-box">
    <textarea
      class="sl-textarea"
      :value="displayValue"
      @input="onInput"
      @blur="onBlur"
      :placeholder="placeholder"
      :placeholder-style="`color: #999999;font-size:${toRpx(14)}`"
      :maxlength="maxlength"
      :disabled="disabled"
    ></textarea>
    <view v-if="showWordLimit && maxlength" class="word-limit">
      {{ displayValue.length }}/{{ maxlength }}
    </view>
  </view>
</template>

<script setup lang="ts">
import { toRpx } from '@/utils/toRpx';
interface Props {
  modelValue?: string;
  placeholder?: string;
  maxlength?: number;
  disabled?: boolean;
  showWordLimit?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: "请输入",
  maxlength: 200,
  disabled: false,
  showWordLimit: false,
});

const emit = defineEmits(["update:modelValue", "change"]);
/**显示的文本（确保不超过最大长度） */
const displayValue = ref(props.modelValue || "");

const onInput = (event: any) => {
  let inputVal = event.detail.value;
  /**手动执行截断 */
  if (props.maxlength > 0 && inputVal.length > props.maxlength) {
    inputVal = inputVal.substring(0, props.maxlength);
  }
  displayValue.value = inputVal;
  emit("update:modelValue", inputVal);
  emit("change", inputVal);
};
/**处理边界情况：粘贴操作可能在blur时才完整触发 */
const onBlur = () => {
  if (displayValue.value !== props.modelValue) {
    emit("update:modelValue", displayValue.value);
    emit("change", displayValue.value);
  }
};
/**监听外部modelValue变化 */
watch(
  () => props.modelValue,
  (newVal = "") => {
    if (newVal !== displayValue.value) {
      displayValue.value = newVal || "";

      /**确保外部传入值也不超长 */
      if (props.maxlength > 0 && newVal.length > props.maxlength) {
        const trimmed = newVal.substring(0, props.maxlength);
        displayValue.value = trimmed;
        emit("update:modelValue", trimmed);
        emit("change", trimmed);
      }
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.sl-textarea-box {
  width: 100%;
  position: relative;

  .sl-textarea {
    width: auto;
    height: 56px;
    border-radius: 6px;
    background: #f8f8f8;
    padding: 6px 8px;
    line-height: 1.5;
  }

  .word-limit {
    position: absolute;
    bottom: 5px;
    right: 8px;
    font-size: 12px;
    color: #999;
  }
}
</style>
