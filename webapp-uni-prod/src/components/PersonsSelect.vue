<template>
    <div class="personnel-item" v-for="item of list" :key="item.uuid">
        <div class="personnel-item_parent content-item">
            <!-- 勾选图标 -->
            <div @click="onParentItem(item)" style="display: flex;align-items: center;">
                <!-- 显示子级：子级全选中即为选中 -->
                <SlSVgIcon class="check-icon" v-if="item.childrens?.length && showCheckbox"
                    :name="'persons-20-' + (item.childrens?.every(c => checkedIds.has(c.id)) ? 'check' : 'uncheck')" />
                <!-- 不显示子级：勾选自己即为选中 -->
                <SlSVgIcon class="check-icon" v-else-if="!item.childrens?.length && showCheckbox"
                    :name="'persons-20-' + (checkedIds.has(item.id) ? 'check' : 'uncheck')" />
                <SlSVgIcon v-if="item.iconName" :name="item.iconName"
                    :size="item.iconSize || item.iconName.split('-')[1]" />
            </div>
            <span class="label">
                {{ item.label }}
                {{ showChildrenLength ? item.childrens?.length ? `(${item.childrens.length})` : '' : '' }}
            </span> 
            <!-- 展开折叠图标 -->
            <div v-if="item.childrens?.length && showExpand" @click="item._showChild = !item._showChild"
                :class="item._showChild ? 'icon-rotate' : ''">
                <SlSVgIcon name="persons-12-6" size="12" />
            </div>
        </div>
        <div v-show="item._showChild">
            <div class="personnel-item_child" v-for="child of item?.childrens" :key="child.id"
                @click="onChildItem(child)">
                <SlSVgIcon v-if="showCheckbox" class="check-icon"
                    :name="'persons-20-' + (checkedIds.has(child.id) ? 'check' : 'uncheck')" />
                <div class="group-item">
                    <!-- 含机构名称 -->
                    <template v-if="showOrgName">
                        <div class="round">
                            {{ child.label[0] }}</div>
                        <div class="group-item_name">
                            <div style="color: #999;">
                                {{ child.label }}
                            </div>
                            <div style="color: #333;">
                                {{ child.orgName || '员工' }}
                            </div>
                        </div>
                    </template>
                    <!-- 普通名称 -->
                    <template v-else>
                        <SlSVgIcon v-if="child.iconName" :name="child.iconName"
                            :size="child.iconSize || child.iconName.split('-')[1]" />
                        <span class="label">{{ child.label }}</span>
                    </template>
                </div>
            </div>
        </div>
    </div>
    <BaseEmpty v-if="!list.length" />
</template>

<script setup lang="ts">
interface RowItem {
    id: string
    label: string;
    orgName?: string;
    uuid?: string;
    childrens?: RowItem[]
    _showChild?: boolean;
    iconName?: string 
    iconSize?: number
}
const props = withDefaults(defineProps<{
    options: RowItem[],
    selectedIds: string[],
    /** 显示机构名称 */
    showOrgName?: boolean,
    /** 是否多选 */ 
    multiple?: boolean,
    /** 展示子集数量 */
    showChildrenLength?: boolean
    /** 是否显示选择框 */
    showCheckbox?: boolean
    /** 是否显示展开/折叠图标 */ 
    showExpand?: boolean
}>(), {
    multiple: true,
    showOrgName: false,
    showChildrenLength: true,
    showCheckbox: true,
    showExpand: true
})
const checkedIds = ref<Set<string>>(new Set())
const list = ref<RowItem[]>([])
const emit = defineEmits(['update:selectedIds'])
watchPostEffect(() => {
    list.value = props.options || []
})
watchEffect(() => {
    emit('update:selectedIds', Array.from(checkedIds.value))
})
const onParentItem = (item: RowItem) => {
    if (props.multiple) {
        const { childrens = [] } = item
        const childAllCheck = childrens.every(c => checkedIds.value.has(c.id))
        let event = !childAllCheck
        if (event) {
            childrens.forEach(c => checkedIds.value.add(c.id))
        } else {
            childrens.forEach(c => checkedIds.value.delete(c.id))
        }
    } else {
        checkedIds.value.clear()
        checkedIds.value.add(item.id)
    }
}
const onChildItem = (item: RowItem) => {
    const { id } = item
    if (!id) return;
    if (props.multiple) {
        checkedIds.value.has(id) ? checkedIds.value.delete(id) : checkedIds.value.add(id)
    } else {
        checkedIds.value.clear()
        checkedIds.value.add(id)
    }
}
</script>

<style scoped lang="scss">
.personnel-item {
    display: flex;
    flex-direction: column;
    padding: 0 15px;

    &_parent,
    &_child {
        height: 50px;
        display: flex;
        align-items: center;

        &:hover {
            background: rgba(79, 122, 246, 0.1);
        }
    }
}

.content-item {
    display: flex;
    align-items: center;
    cursor: pointer;

    &:hover {
        background: rgba(79, 122, 246, 0.1);
    }

    .label {
        display: flex;
        flex: 1;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #333;
        padding: 0 8px;
    }
}

.group-item {
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 50px;
    margin-bottom: 8px;
    flex: 1;


    &_name {
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 12px;
        border-bottom: 1px solid #999;
        min-height: 50px;
        flex: 1;
    }
}

.round {
    width: 34px;
    height: 34px;
    background: #4f7af6;
    border-radius: 17px;
    color: #ffffff;
    font-size: 14px;
    text-align: center;
    line-height: 34px;
}

.check-icon {
    margin-right: 15px;
}

.icon-rotate {
    transform: rotate(90deg);
}
</style>