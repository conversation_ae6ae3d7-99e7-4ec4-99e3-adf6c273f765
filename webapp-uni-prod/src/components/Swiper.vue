<template>
	<view class="uni-swiper">
		<swiper
			class="swiper"
			circular
			:autoplay="autoplay"
			:current="current"
			:previous-margin="previousMargin"
			:next-margin="nexMargin"
			:active-class="'active'"
			@change="change"
		>
			<swiper-item
				v-for="(path, index) in list"
				:key="index"
				class="swiper-container"
			>
				<view
					class="slide-image"
					:class="{ active: index === current }"
					@click="click"
				>
					<view class="image-wrap">
						<image
							class="img"
							:src="path"
							mode="aspectFill"
							lazy-load
						/>
					</view>
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script setup lang="ts">
	const props = withDefaults(
		defineProps<{
			list?: string[] | any[];
			autoplay?: boolean;
			current?: number;
			previousMargin?: string;
			nexMargin?: string;
		}>(),
		{
			list: () => [],
			autoplay: false,
			current: 0,
			previousMargin: '60',
			nexMargin: '60',
		}
	);

	const emit = defineEmits(['change', 'click']);
	const change = (e: any) => {
		emit('change', e.detail);
	};

	const click = (e: any) => {
		emit('click', e);
	};
</script>

<style lang="scss" scoped>
	.uni-swiper {
	}
	.swiper {
		width: 100%;
		height: 110px;
		display: flex;
		align-items: center;

		.swiper-container {
			display: flex;
			align-items: center;

			.slide-image {
				height: 100px;
				width: 100px;
				z-index: 1;
				margin: 0 auto;
			}

			.image-wrap {
				height: 100px;
				width: 100px;
				margin: 0 auto;
				.img {
					width: 100%;
					height: 100%;
					border-radius: 5px;
					opacity: 0.6;
				}
			}

			.active {
				transform: scale(1.1);
				transition: all 0.2s ease-in 0s;
				z-index: 20;
				.img {
					border-radius: 5.5px;
					opacity: 1;
				}
			}
		}
	}
</style>
