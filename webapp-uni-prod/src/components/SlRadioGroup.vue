<template>
    <view class="radio-group-container" :style="customStyle">
        <view class="radio-item" :class="{ 'radio-item-disabled': disabled || item.disabled }" v-for="item of options"
            :key="item.value" @click="onClick(item)">
            <view class="radio-item_icon">
                <SlSVgIcon class="icon" v-if="checked === item.value" :name="'12-12-' + (disabled ? '23' : '3')"
                    size="12" />
                <SlSVgIcon class="icon" name="12-12-4" v-else size="12" />
            </view>
            <view class="radio-item_text">
                {{ item.label }}
            </view>
        </view>
    </view>
</template>


<script setup lang="ts">
import { StyleValue } from 'vue';
interface IOption {
    label: string,
    value: string | number,
    disabled?: boolean
}
interface IRadioGroupProps {
    options: Array<IOption>,
    modelValue: string | number,
    disabled?: boolean,
    /** 可取消 */
    cancelable?: boolean,
    customStyle?: StyleValue
}
const props = defineProps<IRadioGroupProps>();
const emit = defineEmits(['update:modelValue'])
/** 当前选中的值；单选*/
const checked = computed({
    get: () => props.modelValue,
    set: (val: string | number) => {
        if (props.cancelable && props.modelValue === val) val = ''
        emit('update:modelValue', val)
    }
})
const onClick = ({ value, disabled }: IOption) => {
    if (props.disabled || disabled) return;
    checked.value = value
}
</script>

<style scoped lang="scss">
.radio-group-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;

    .radio-item {
        display: flex;
        align-items: center;
        padding: 5px 0;
        gap: 5px;

        &_icon,
        .icon {
            display: inline-flex;
            align-items: center;
        }
    }

    .radio-item-disabled {
        color: #666;
    }
}
</style>