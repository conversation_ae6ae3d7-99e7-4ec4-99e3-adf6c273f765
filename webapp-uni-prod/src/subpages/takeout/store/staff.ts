import { defineStore } from "pinia";
import { TakeoutState } from "./types";
import {
  TakeoutOrderSearch,
  TakeoutOrderStatus,
  type TakeoutOrder,
  type TakeoutOrderItem,
} from "@/subpages/takeout/models";
import {
  takeoutMenuService,
  takeoutOrderService,
} from "@/subpages/takeout/service";
import usePrincipalStore from "@/store/modules/principal";
import dayjs from "dayjs";
import type { Page } from "@/models/Page";

const getTomorrowDate = (): dayjs.Dayjs => {
  return dayjs().add(1, "day");
};

// 员工端外卖点餐相关的状态管理
const useTakeoutStore = defineStore("takeoutStaff", {
  state: (): TakeoutState => ({
    pickupDate: getTomorrowDate().format("YYYY-MM-DD"), // 取餐日期
    backupPickupDate: getTomorrowDate().format("YYYYMMDD"), // 备份取餐日期，默认是明天
    cart: [], // 购物车添加的外卖菜品
    cartDate: "", // 购物车日期,超过此日期需要清空购物车
    totalPrice: 0, // 购物车总价
    totalCount: 0, // 购物车菜品总数
    // 明天可订购外卖菜品菜单
    takeoutMenu: {
      menuDate: "",
      takeoutMenuDishList: [],
      published: false, // 是否已发布
    },
    takeoutOrder: {
      userId: "",
      takeoutOrderItemList: [],
      totalPrice: 0,
    },
    orderList: [], // 所有的订单列表
  }),
  getters: {
    getPickupDate: (state) => state.pickupDate,
    getBackupPickupDate: (state) => state.backupPickupDate,
    getCart: (state) => state.cart,
    getTotalPrice: (state) => state.totalPrice,
    getTotalCount: (state) => state.totalCount,
    getTakeoutMenu: (state) => state.takeoutMenu,
    getCurrentOrder: (state) => state.takeoutOrder,
    getAllOrders: (state) => state.orderList,
  },
  actions: {
    /** 私有：给菜单列表里的某道菜更新 orderCount */
    privateUpdateMenuDishCount(dishId: string, delta: number) {
      const dish = this.takeoutMenu.takeoutMenuDishList.find(
        (d) => d.dishId === dishId
      );
      if (dish) dish.orderCount = Math.max((dish.orderCount ?? 0) + delta, 0);
    },

    addToCart(dishId: string) {
      // —— 更新 cart
      const idx = this.cart.findIndex((i) => i.dishId === dishId);
      if (idx > -1) {
        this.cart[idx].orderCount! += 1;
      } else {
        // 在 cart 里找原菜单数据，复制一份，orderCount 为 1
        const menuDish = this.takeoutMenu.takeoutMenuDishList.find(
          (d) => d.dishId === dishId
        )!;
        this.cart.push({ ...menuDish, orderCount: 1 });
      }
      this.calculateTotalPrice();

      // —— 同步更新菜单列表
      this.privateUpdateMenuDishCount(dishId, +1);
    },
    /** 移除一份购物车 */
    removeFromCart(dishId: string) {
      const idx = this.cart.findIndex((i) => i.dishId === dishId);
      if (idx === -1) return;

      if (this.cart[idx].orderCount! > 1) {
        this.cart[idx].orderCount!--;
      } else {
        this.cart.splice(idx, 1);
      }
      this.calculateTotalPrice();
      this.privateUpdateMenuDishCount(dishId, -1);
    },
    clearCart() {
      this.cart = [];
      this.totalPrice = 0;
      this.totalCount = 0;
      this.takeoutMenu.takeoutMenuDishList.forEach((d) => {
        d.orderCount = 0;
      });
    },
    async setTakeoutMenu() {
      const principalStore = usePrincipalStore();
      const account = await principalStore.identity();
      const menu = await takeoutMenuService.getTakeoutMenuByDate(
        this.getBackupPickupDate,
        account!.id
      );
      if (this.cartDate !== this.getBackupPickupDate) {
        // 如果购物车日期不等于备份日期，则清空购物车
        this.cart = [];
        this.totalPrice = 0;
        this.totalCount = 0;
        this.cartDate = this.getBackupPickupDate;
      }
      if (this.cart.length) {
        // 如果购物车中的菜品不在菜单中，则从购物车中移除
        this.cart = this.cart.filter((item) =>
          menu.takeoutMenuDishList.some((d) => d.dishId === item.dishId)
        );
        // 更新菜单列表的菜品数量
        menu.takeoutMenuDishList.forEach((ele) => {
          const cartItem = this.cart.find((i) => i.dishId === ele.dishId);
          ele.orderCount = cartItem ? cartItem.orderCount : 0;
        });
      }
      this.takeoutMenu = menu;
    },

    // 获取订单列表
    async getTakeoutOrderList(
      search: TakeoutOrderSearch
    ): Promise<Page<TakeoutOrder>> {
      const principalStore = usePrincipalStore();
      const account = await principalStore.identity();
      const _search: TakeoutOrderSearch = {
        ...search,
        userId: account!.id,
      };
      if (search.status === TakeoutOrderStatus.ALL) {
        delete _search.status; // 如果是 ALL，则不传 status
      }
      return await takeoutOrderService.getTakeoutOrderList(_search);
    },

    // 计算购物车总价
    calculateTotalPrice() {
      this.totalPrice = this.cart.reduce(
        (sum, i) => sum + i.price * (i.orderCount ?? 0),
        0
      );
      this.totalCount = this.cart.reduce(
        (count, item) => count + (item.orderCount ?? 0),
        0
      );
    },

    // 获取购物车中菜品数量
    getCartItemCount() {
      return this.cart.reduce(
        (count, item) => count + (item.orderCount ?? 0),
        0
      );
    },

    // 预下单：结算页面,组装页面所需要的订单数据
    async genCurOrder() {
      const takeoutOrderItemList: TakeoutOrderItem[] = this.cart.map(
        (item) =>
          ({
            takeoutMenuDishId: item.id,
            dishName: item.dishName,
            price: item.price,
            limitPerPerson: item.limitPerPerson, // 每人限点
            orderCount: item.orderCount, // 订单数量
            portionUnit: item.portionUnit, // 分量单位默认为1
            imageUrl: item.imageUrl, // 菜品图片
          } as TakeoutOrderItem)
      );
      const principalStore = usePrincipalStore();
      const account = principalStore.account!;
      const order: TakeoutOrder = {
        userId: account.id, // 这里需要设置用户ID，结算页面会传入
        userName: account.name, // 用户名
        phone: account.phone, // 用户手机号
        takeoutOrderItemList,
        totalPrice: this.totalPrice,
        pickupTime: `${this.pickupDate} 00:00:00`, // 取餐时间
        status: TakeoutOrderStatus.UNPAID, // 订单状态：待支付
        remainingTime: 15 * 60 * 1000, // 剩余支付时间，单位为秒
      };
      this.takeoutOrder = order;
    },

    // 正式下单：点击立即支付时
    async saveOrder(): Promise<string | undefined> {
      const takeoutOrderItemList: TakeoutOrderItem[] = this.cart.map(
        (item) =>
          ({
            takeoutMenuDishId: item.id,
            orderCount: item.orderCount,
          } as TakeoutOrderItem)
      );

      const principalStore = usePrincipalStore();
      const account = principalStore.account!;
      const order: TakeoutOrder = {
        userId: account.id!, // 这里需要设置用户ID
        takeoutOrderItemList,
        totalPrice: this.totalPrice,
      };
      const _order = await takeoutOrderService.saveTakeoutOrder(order);
      if (_order && _order.orderId) {
        // 清空购物车
        this.clearCart();
        return _order.orderId;
      }
      return undefined;
    },

    /** 根据购物车生成当前的订单 */
    async getCurOrder(orderId: string) {
      const takeoutOrder = await takeoutOrderService.getTakeoutOrderInfo(
        orderId
      );
      this.takeoutOrder = takeoutOrder;
    },
    setCurrentOrder(order: TakeoutOrder) {
      this.takeoutOrder = order;
    },
    setOrderList(orders: TakeoutOrder[]) {
      this.orderList = orders;
    },
  },
});

export default useTakeoutStore;
