/**
 * 订单相关
 */

import { post, get } from "@/service/http";
import {
  TakeoutOrderCabinetSearch,
  TakeoutOrder,
  TakeoutOrderStatus,
} from "@/subpages/takeout/models";

export default {
  // 职工下单
  saveTakeoutOrder: (data: TakeoutOrder) =>
    post("/takeoutorder/save", {
      data,
      custom: {
        successTip: "下单成功",
        errorTip: "下单失败，请重试~",
      },
    }),
  // 查询取餐柜列表
  getPickupCabinetList: (data: TakeoutOrderCabinetSearch) =>
    post("/pickupcabinet/getList", { data }),
  // 查询订单列表
  getTakeoutOrderList: (data: { userId: string; status: TakeoutOrderStatus }) =>
    post<TakeoutOrder[]>("/takeoutorder/getList", { data }),
  // 查询订单详情
  getTakeoutOrderInfo: (orderId: string) =>
    get<TakeoutOrder>(`/takeoutorder/getInfo/${orderId}`),
  // 更新订单状态
  updateTakeoutOrderStatus: (data: {
    orderId: string; // 订单ID
    status: TakeoutOrderStatus; // 订单状态 0 - 3
  }) => get(`/takeoutorder/updateStatus/${data.orderId}/${data.status}`),

  // 取消未支付订单,将订单状态改为已取消
  cancelUnpaidOrder: (orderId: string) =>
    get(
      `/takeoutorder/updateStatus/${orderId}/${TakeoutOrderStatus.CANCELLED}`,
      {
        custom: {
          successTip: "订单取消成功",
          errorTip: "订单取消失败，请重试~",
        },
      }
    ),

  // 删除已完成订单/已取消订单，
  deleteCompletedOrder: (orderId: string) =>
    get(`/takeoutorder/delete/${orderId}`, {
      custom: {
        successTip: "订单删除成功",
        errorTip: "订单删除失败，请重试~",
      },
    }),

  // 设置订单取餐柜
  updateTakeoutOrderCabinetId: (orderId: string, cabinetId: string) =>
    get(`/takeoutorder/updateCabinetId/${orderId}/${cabinetId}`),
};
