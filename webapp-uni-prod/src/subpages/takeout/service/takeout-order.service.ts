/**
 * 订单相关
 */

import { post, get } from "@/service/http";
import {
  TakeoutOrderCabinetSearch,
  TakeoutOrderParams,
  TakeoutOrderStatus,
} from "@/subpages/takeout/models";

export default {
  // 职工下单
  saveTakeoutOrder: (data: TakeoutOrderParams) =>
    post("/takeoutorder/save", { data }),
  // 查询取餐柜列表
  getPickupCabinetList: (data: TakeoutOrderCabinetSearch) =>
    post("/pickupcabinet/getList", { data }),
  // 查询订单列表
  getTakeoutOrderList: (data: { userId: string; status: TakeoutOrderStatus }) =>
    post("/takeoutorder/getList", { data }),
  // 更新订单状态
  updateTakeoutOrderStatus: (data: {
    orderId: string; // 订单ID
    status: TakeoutOrderStatus; // 订单状态 0 - 3
  }) => get(`/takeoutorder/updateStatus/${data.orderId}/${data.status}`),
  // 设置订单取餐柜
  updateTakeoutOrderCabinetId: (orderId: string, cabinetId: string) =>
    get(`/takeoutorder/updateCabinetId/${orderId}/${cabinetId}`),
};
