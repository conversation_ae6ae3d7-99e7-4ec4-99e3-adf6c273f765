import { post } from "@/service/http";
import type { WxPayReqParam } from "../models";

export default {
  /**
   * 微信预支付接口
   * @param data
   * @param data.outTradeNo 订单号
   * @param data.openid 微信openid
   * @returns
   */
  prepayWechatPay: (data: { outTradeNo: string; openid: string }) =>
    post<WxPayReqParam>("/wechatpay/prepay", {
      data,
      custom: {
        errorTip: "支付请求失败",
      },
    }),
};
