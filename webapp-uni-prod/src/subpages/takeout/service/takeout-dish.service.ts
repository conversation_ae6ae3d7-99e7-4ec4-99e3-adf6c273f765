import { get, post } from "@/service/http";
import { TakeoutDish, TakeoutDishSearch } from "../models";

export default {
  // 菜品图片上传接口
  //  uploadDishImage : (dishId: string | number, data: any) =>
  //     post(`/dishimage/upload/${dishId}`, data);
  // 新增菜品接口
  saveDish: (data: TakeoutDish) =>
    post("/dish/save", {
      data,
      custom: {
        toast: true, // 提示 , true 不用配置
        loading: true, // 加载中... true 不用配置
        successTip: "新增成功",
        errorTip: "新增失败，请重试~",
      },
    }),

  // 更新菜品接口
  updateDish: (data: TakeoutDish) => post("/dish/update", data),

  // 获取菜品列表
  getDishList: (data: TakeoutDishSearch) =>
    post<TakeoutDish[]>("/dish/getList", { data }),

  // 下架菜品接口
  removeDish: (dishId: string) => get<string>(`/dish/delete/${dishId}`),

  // 获取菜品详情
  getDishById: (dishId: string) => get<TakeoutDish>(`/dish/getInfo/${dishId}`),
};
