import { get, post } from "@/service/http";
import type { TakeoutDish, TakeoutDishSearch } from "../models";

export default {
  // 菜品图片上传接口
  //  uploadDishImage : (dishId: string | number, data: any) =>
  //     post(`/dishimage/upload/${dishId}`, data);
  // 新增菜品接口
  saveDish: (data: TakeoutDish) =>
    post("/takeoutdish/save", {
      data,
      custom: {
        toast: true, // 提示 , true 不用配置
        loading: true, // 加载中... true 不用配置
        successTip: "新增成功",
        errorTip: "新增失败，请重试~",
      },
    }),

  // 更新菜品接口
  updateDish: (data: TakeoutDish) =>
    post("/takeoutdish/save", {
      data,
      custom: {
        successTip: "修改成功",
        errorTip: "修改失败，请重试~",
      },
    }),

  // 获取菜品列表
  getDishList: (data: TakeoutDishSearch) =>
    post<TakeoutDish[]>("/takeoutdish/getList", { data }),

  // 下架菜品接口
  deleteDish: (dishId: string) =>
    get<string>(`/takeoutdish/delete/${dishId}`, {
      custom: {
        loading: true, // 加载中... true 不用配置
        successTip: "下架成功",
        errorTip: "下架失败，请重试~",
      },
    }),

  // 获取菜品详情
  getDishById: (dishId: string) =>
    get<TakeoutDish>(`/takeoutdish/getInfo/${dishId}`),
};
