import { get, post } from "@/service/http";
import { TakeoutMenu } from "@/subpages/takeout/models";

export default {
  // 按日期查询外卖菜单: menuDate 格式为 "yyyyMMdd"
  getTakeoutMenuByDate: (menuDate: string, userId: string) =>
    get<TakeoutMenu>(`/takeoutmenu/getTakeoutMenuByDate/${menuDate}/${userId}`),

  // 保存外卖菜单
  saveTakeoutMenu: (data: TakeoutMenu) =>
    post<TakeoutMenu>("/takeoutmenu/save", {
      data,
      custom: {
        successTip: "发布成功",
        errorTip: "发布失败，请重试~",
      },
    }),
};
