/**订单状态
 * 0：待支付 1：待取餐 2：已完成 3：已取消
 */
export enum TakeoutOrderStatus {
  UNPAID = "0",
  PENDING = "1",
  COMPLETED = "2",
  CANCELLED = "3",
  ALL = "all",
}
export type TakeoutOrderStatusType = keyof typeof TakeoutOrderStatus;
/**
 * 单个订单项
 */
export interface TakeoutOrderItem {
  dishId: string;
  dishName: string;
  imageUrl?: string; // 菜品图片
  price: number;
  orderCount: number;
  portionUnit: number; // 分量单位
  id?: string;
  orderId?: string;
}

/**
 * 职工下单请求参数（传入）
 */
export interface TakeoutOrder {
  id?: string; // 订单ID
  userId: string;
  userName?: string;
  takeoutOrderItemList: TakeoutOrderItem[];
  status?: TakeoutOrderStatus;
  statusCn?: string; // 订单状态中文
  /** 总价 */
  totalPrice: number;
  /**手机号 */
  phone?: string;
  /** 订单号 */
  orderId?: string;
  /** 下单时间 */
  orderDate?: string;
  /** 取餐时间 */
  pickupTime?: string;
  /** 取餐柜id */
  cabinetId?: string;
  /** 取餐号 */
  pickupCode?: string;
  /** 剩余支付时间 */
  remainingTime?: number; // 单位：秒
}

/**
 * 订单外卖柜查询参数
 */
export interface TakeoutOrderCabinetSearch {
  ifPage?: boolean; // 是否分页
  currentPage?: number; // 当前页
  pageRecord?: number; // 页容量
}
