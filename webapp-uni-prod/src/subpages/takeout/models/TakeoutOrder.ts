import type { TakeoutMenuDish } from "./TakeoutMenu";

/**订单状态
 * 0：待支付 1：待取餐 2：已完成 3：已取消
 */
export enum TakeoutOrderStatus {
  UNPAID = "0",
  PENDING = "1",
  COMPLETED = "2",
  CANCELLED = "3",
  ALL = "all",
}
export type TakeoutOrderStatusType = keyof typeof TakeoutOrderStatus;
/**
 * 单个订单项
 */
export interface TakeoutOrderItem extends TakeoutMenuDish {
  takeoutMenuDishId?: string;
  totalCount?: number; // 菜品已经下单总数量
}

/**
 * 职工下单请求参数（传入）
 */
export interface TakeoutOrder {
  id?: string; // 订单ID
  transactionId?: string; // 交易ID 微信提供的订单号
  displayId?: string; // 订单号（显示用）
  menuDate?: string; // 下单日期，格式为 yyyyMMdd
  userId: string;
  userName?: string;
  takeoutOrderItemList: TakeoutOrderItem[];
  status?: TakeoutOrderStatus;
  statusCn?: string; // 订单状态中文
  /** 总价 */
  totalPrice: number;
  /**手机号 */
  phone?: string;
  /** 下单时间 */
  orderDate?: string;
  /** 取餐时间 */
  pickupTime?: string;
  /** 取餐柜id */
  cabinetId?: string;
  /** 取餐号 */
  pickupCode?: string;
  /** 剩余支付时间 */
  remainingTime?: number; // 单位：秒
}

/** 订单打印数据 */
export interface PrintTakeoutOrder {
  orderId: string; // 订单ID
  transactionId: string; // 交易ID 微信提供的订单号
  displayId: string;
  userName: string; // 用户名
  phone: string; // 手机号
  orderDate: string; // 下单时间
  pickupTime: string; // 取餐时间
  totalPrice: number; // 总价
  takeoutOrderItemList: {
    dishName: string; // 菜品名称
    dishPrice: number; // 菜品价格
    dishCount: number; // 菜品数量
  }[]; // 订单项列表
  pickupCode: string; // 取餐柜+取餐号
}

/**
 * 订单外卖柜查询参数
 */
export interface TakeoutOrderCabinetSearch {
  ifPage?: boolean; // 是否分页
  currentPage?: number; // 当前页
  pageRecord?: number; // 页容量
}

export type OrderChangeType = "delete" | "cancel" | "refresh";
