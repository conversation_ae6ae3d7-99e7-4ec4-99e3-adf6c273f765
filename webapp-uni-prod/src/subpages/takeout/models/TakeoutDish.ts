/**
 * TakeoutDish  菜品项
 */
export interface TakeoutDish {
  /** 菜品id, 可选 */
  id: string;
  /** 名称, 可选 */
  name: string;
  /** 价格, 可选, 示例值: 2.0 */
  price: number;
  /** 分量, 可选, 示例值: 2 */
  portionUnit: number;
  /** 没人限购份数, 可选, 示例值: 2 */
  limitPerPerson: number;
  /** 图片id */
  imageId: string;
  /** 菜品图片地址, 可选 */
  imageUrl?: string;
  /** 默认字段：菜谱管理里的菜品需要区分上午下午，外卖又不需要  */
  timePeriod?: "morning";
}

export interface TakeoutDishSearch {
  ifPage?: boolean; // 是否分页
  currentPage?: number; // 当前页
  pageRecord?: number; // 每页记录数
}
