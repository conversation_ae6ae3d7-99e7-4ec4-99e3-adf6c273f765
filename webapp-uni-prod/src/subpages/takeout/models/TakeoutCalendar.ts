export type TakeoutCalendarType = 1 | 2 | 3 | 4;

export interface TakeoutCalendar {
  id?: string;
  /** 日期 yyyy-MM-dd */
  date: string;
  /** 手动类型(0-未知,1-节假日,2-补班日,3-工作日,4-非工作日) */
  manualType: number;
  /** 法定节假日 */
  officialName: string;
  /** 官方类型(0-未知,1-节假日,2-补班日) */
  officialType: number;
  /** 实际类型(1-节假日,2-补班日,3-工作日,4-非工作日) */
  type: TakeoutCalendarType;
  /** 节假日或星期 */
  dayOrHolidayName: string;
}
