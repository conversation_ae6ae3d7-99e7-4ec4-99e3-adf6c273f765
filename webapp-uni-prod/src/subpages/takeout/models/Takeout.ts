/**
 * 外卖模块相关类型定义
 * @module Takeout
 * @description 包含外卖商品和菜单的类型定义
 * <AUTHOR> Jing
 * @date 2026-06-20
 */

/**
 * 外卖商品
 * 包含菜品id、名称、价格和分量
 * @interface TakeoutProduct
 */
export interface TakeoutProduct {
  /** 菜品id */
  dishId: string;
  /** 菜品名称 */
  dishName: string;
  /** 价格 */
  price: number;
  /** 分量 */
  portionUnit: number;
}

/**
 * 外卖菜单项
 * 包含菜品信息、限购数量和图片id
 * @interface TakeoutMenuItem
 */
export interface TakeoutMenuItem extends TakeoutProduct {
  /** 限购 */
  limitPerPerson: number;
  /** 图片id */
  imageId: string;
}

/**
 * 外卖菜单
 * 包含菜单日期和菜品列表
 */
export interface TakeoutMenu {
  menuDate: string;
  takeoutMenuDishList: TakeoutProduct[];
}

/**
 * 外卖订单项
 * 包含菜品信息和数量
 */
export interface TakeoutOrderItem extends TakeoutProduct {
  /** 数量 */
  quantity: number;
}

/**
 * 职工外卖订单
 */
export interface TakeoutOrder {
  userId: string;
  takeoutOrderItemList: TakeoutOrderItem[];
}
