import type { TakeoutDish } from "./TakeoutDish";
export interface TakeoutMenuDish extends TakeoutDish {
  /** 所属菜单 ID */
  takeoutMenuId: string;
  /** 菜品 ID */
  dishId: string;
  /** 菜品名称 */
  dishName: string;
  /** 已下单数量 */
  orderCount: number;

  /** 总的下单数量 */
  totalCount?: number;
}
export interface TakeoutMenuNoticeMessage {
  /** 通知内容 */
  content: string;
  /** 额外信息 */
  extra: string;
  /** 通知主键 */
  id: string;
  /** 所属模块 */
  module: string;
  /** 通知标题 */
  title: string;
  /** 通知类型 */
  type: string;
}
/** 按日期查询返回的菜单主体 */
export interface TakeoutMenu {
  /** 菜单主键 */
  id?: string;
  /** 菜单日期，yyyyMMdd */
  menuDate: string;
  /** 本日期下可选菜品列表 */
  takeoutMenuDishList: TakeoutMenuDish[];
  /** 通知消息 */
  notifyMessage?: TakeoutMenuNoticeMessage;

  /** 菜单状态，false=未发布，true=已发布 */
  published?: boolean;
}
