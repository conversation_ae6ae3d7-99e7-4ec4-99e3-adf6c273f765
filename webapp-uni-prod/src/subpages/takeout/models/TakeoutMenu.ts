/** 单个菜品项 */
export interface TakeoutMenuDish {
  /** 菜单项主键 */
  id?: string;
  /** 所属菜单 ID */
  takeoutMenuId: string;
  /** 菜品 ID */
  dishId: string;
  /** 菜品名称 */
  dishName: string;
  /** 单价 */
  price: number;
  /** 份量单位 */
  portionUnit: number;
  /** 每人限购份数 */
  limitPerPerson: number;
  /** 图片资源 ID */
  imageId: string;
  /** 删除标志，0=未删，1=已删 */
  /** 已下单数量 */
  orderCount: number;
  /** 图片 URL（相对路径） */
  imageUrl?: string;
}

/** 按日期查询返回的菜单主体 */
export interface TakeoutMenu {
  /** 菜单主键 */
  id?: string;
  /** 菜单日期，yyyyMMdd */
  menuDate: string;
  /** 本日期下可选菜品列表 */
  takeoutMenuDishList: TakeoutMenuDish[];
}
