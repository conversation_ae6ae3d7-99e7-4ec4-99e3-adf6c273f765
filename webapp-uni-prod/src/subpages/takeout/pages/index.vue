<template>
    <view class="order-page">
        <!-- Banner Section -->
        <view class="banner">
            <image src="/static/images/takeout/banner.png" alt="Banner" class="banner-image" />
            <view class="banner-text">
                <text>超多面食，等你来订</text>
            </view>
        </view>
        <!-- Header Section -->
        <view class="header">
            <view class="header-time">
                <text>取餐时间: 2025-06-03</text>
                <text>截止时间: 今天17:00</text>
            </view>
        </view>
        <!-- Menu Items Section -->
        <view class="menu-list">
            <view class="menu-item" v-for="item in menuItems" :key="item.id">
                <image :src="'/static/images/takeout/'+item.image+'.svg'" class="menu-item-img" />
                <view class="menu-item-info">
                    <text>{{ item.name }}</text>
                    <text>{{ item.price }}元</text>
                    <text>{{ item.limit }}</text>
                </view>
                <button class="add-button" @click="addToCart(item)">+</button>
            </view>
        </view>

        <!-- Footer: Total and Checkout -->
        <view class="footer">
            <text class="footer-total">合计: ¥{{ totalPrice }}</text>
            <button class="checkout-button" @click="goToCheckout">去结算</button>
        </view>
    </view>
</template>
<script lang="ts" setup>
import { ref } from 'vue';

const menuItems = ref([
    {
        id: 1,
        name: '大干层馒头',
        price: 4,
        limit: '限3份/人',
        image: 'empty',
    },
    {
        id: 2,
        name: '玉米饼',
        price: 3,
        limit: '限3份/人',
        image: 'empty',
    },
    {
        id: 3,
        name: '长花卷',
        price: 3,
        limit: '数量为0时，减号收回',
        image: 'empty',
    }
]);

const totalPrice = ref(0);
const cart = ref([]);

const addToCart = (item: any) => {
    // cart.value.push(item);
    // totalPrice.value += item.price;
};

const goToCheckout = () => {
    // Navigate to checkout page
    console.log('Proceed to checkout with items: ', cart.value);
};
</script>
<style lang="scss" scoped>
.order-page {
    display: flex;
    flex-direction: column;
    padding: 16px;
}

.header {
    background-color: #f8f8f8;
    padding: 10px;
    text-align: center;
}

.header-title {
    font-size: 20px;
    font-weight: bold;
}

.header-time text {
    font-size: 14px;
    color: #888;
}

.banner {
    position: relative;
    margin: 10px 0;
    width: 100%;
    height: 119px;
}

.banner-image {
    width: 100%;
    height: 150px;
}

.banner-text {
    position: absolute;
    top: 50%;
    left: 20px;
    color: #fff;
    font-size: 18px;
}

.menu-list {
    margin-top: 15px;
}

.menu-item {
    display: flex;
    margin-bottom: 15px;
    align-items: center;
}

.menu-item-img {
    width: 70px;
    height: 70px;
    margin-right: 10px;
}

.menu-item-info {
    flex: 1;
}

.add-button {
    background-color: #f6a7c1;
    border: none;
    color: white;
    padding: 5px;
    cursor: pointer;
}

.footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}

.footer-total {
    font-size: 18px;
    color: #333;
}

.checkout-button {
    background-color: #f6a7c1;
    color: white;
    padding: 10px 20px;
    border: none;
    cursor: pointer;
}
</style>