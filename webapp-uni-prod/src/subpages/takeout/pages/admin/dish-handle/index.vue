<template>
    <view class="admin-page">
        <view class="dish-detail">
            <view class="item">
                <view class="label required">菜名</view>
                <view class="value">
                    <input v-model="takeoutDish.name" type="text" placeholder="请输入菜名" />
                </view>
            </view>
            <view class="item">
                <view class="label required">分量</view>
                <view class="value">
                    <template v-if="takeoutDish.portionUnit && takeoutDish.portionUnit > 0">
                        <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-24" size="20"
                            @click="min('portionUnit')" />
                        <text style="margin: 0 8px;">{{ takeoutDish.portionUnit || '' }}</text>
                    </template>
                    <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-25" size="20"
                        @click="add('portionUnit')" />
                    <text class="unit">个/份</text>
                </view>
            </view>
            <view class="item">
                <view class="label required">价格</view>
                <view class="value">
                    <template v-if="takeoutDish.price && takeoutDish.price > 0">
                        <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-24" size="20"
                            @click="min('price')" />
                        <text style="margin: 0 8px;">{{ takeoutDish.price || '' }}</text>
                    </template>
                    <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-25" size="20"
                        @click="add('price')" />
                    <text class="unit">元/份</text>
                </view>
            </view>
            <view class="item">
                <view class="label required">限购数量</view>
                <view class="value">
                    <template v-if="takeoutDish.limitPerPerson && takeoutDish.limitPerPerson > 0">
                        <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-24" size="20"
                            @click="min('limitPerPerson')" />
                        <text style="margin: 0 8px;">{{ takeoutDish.limitPerPerson || '' }}</text>
                    </template>
                    <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-25" size="20"
                        @click="add('limitPerPerson')" />
                    <text class="unit">份/人</text>
                </view>
            </view>
        </view>
        <view class="upload-area">
            <UploadImage />
        </view>
        <view class="actions flex-center">
            <BaseButton class="cancel" size="medium" btn-type="cancel" @click="onCancel">取消</BaseButton>
            <BaseButton class="confirm" size="medium" btn-type="save" @click="onSubmit">提交</BaseButton>
        </view>
    </view>
</template>
<script setup lang="ts">
import { TakeoutDish } from '@/subpages/takeout/models';
import { takeoutDishService } from '@/subpages/takeout/service';
import { ref } from 'vue';
const dishValidateRule = {
    name: { required: true, message: "请输入菜名", type: 'string' },
    portionUnit: { required: true, message: "请设置分量", type: 'number' },
    price: { required: true, message: "请设置价格", type: 'number' },
    limitPerPerson: { required: true, message: "请设置限购数量", type: 'number' },
};
const takeoutDish = ref<TakeoutDish>({
    id: '',
    name: '包子',
    price: 2,
    imageUrl: '',
    portionUnit: 5,
    limitPerPerson: 4,
    imageId: '',
    timePeriod: 'morning'
})

const add = (property: string) => {
    const val = takeoutDish.value[property as keyof TakeoutDish] as number;
    if (val && val >= 0) {
        (takeoutDish.value as any)[property] = val + 1;
    } else {
        (takeoutDish.value as any)[property] = 1; // 如果当前值为0或未定义，则设置为1
    }
}
const min = (property: string) => {
    const val = takeoutDish.value[property as keyof TakeoutDish] as number;
    if (val && val > 0) {
        (takeoutDish.value as any)[property] = val - 1;
    }
}

const validate = () => {
    const errors: string[] = [];
    for (const key in dishValidateRule) {
        const rule = dishValidateRule[key as keyof typeof dishValidateRule];
        const value = takeoutDish.value[key as keyof TakeoutDish];
        console.log(`Validating ${key}:`, value);
        if (rule.required) {
            if (rule.type == 'string') {
                if (!value || (value as string).trim() === '') {
                    errors.push(rule.message);
                }
            } else if (rule.type == 'number') {
                if (value === undefined || value === null || isNaN(value as number) || (value as number) <= 0) {
                    errors.push(rule.message);
                }
            }
        }
    }
    return errors;
}


const onSubmit = async () => {
    const errors = validate();
    if (errors.length > 0) {
        uni.showToast({
            title: errors[0],
            icon: 'none',
            duration: 2000
        });
        return;
    }
    // 提交逻辑
    console.log('提交菜品信息:', takeoutDish.value);
    await takeoutDishService.saveDish(takeoutDish.value)

}

const onCancel = () => {
    // 取消逻辑
    console.log('取消操作');
}

</script>

<style lang="scss" scoped>
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/flex.scss";

.admin-page {
    background: $takeout-bg;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.dish-detail {
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    flex: 0 0 auto;
}

.item {
    height: 40px;
    width: 100%;
    padding: 0 15px;
    border-bottom: 1px solid rgba(153, 153, 153, 0.2);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;

    .value {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        input {
            width: 185px;
            height: 32px;
            border: none;
            outline: none;
            font-size: 14px;
            text-align: right;
        }
    }

    .label {
        position: relative;
        flex: 0 0 105px;
        font-size: 14px;

        &.required::before {
            content: '*';
            color: #E50101;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: -6px;
        }
    }

    .unit {
        font-size: 12px;
        color: $uni-text-color-secondary;
        margin-left: 8px;
    }
}

.upload-area {
    flex: 1;
    width: 100%;
    margin-top: 6px;
    background-color: #fff;
    box-sizing: border-box;
}

.actions {
    flex: 0 0 auto;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 15px;
    box-sizing: border-box;
    margin-bottom: 34px;

    .cancel+.confirm {
        margin-left: 10px;
    }

}
</style>