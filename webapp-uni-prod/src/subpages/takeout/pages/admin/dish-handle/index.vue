<template>
	<view class="admin-page">
		<view class="dish-detail">
			<view class="item">
				<view class="label required">菜名</view>
				<view class="value">
					<view class="input-group">
						<input class="input-name" :maxlength="8" v-model="takeoutDish.name" @input="dishNameChange"
							type="text" placeholder="请输入菜名" />
						<SlSubSvgIcon class="inline-flex" v-if="!isNameEmpty" subpage="takeout" name="12-12-10"
							size="12" />
					</view>
				</view>
			</view>
			<view class="item">
				<view class="label required">份量</view>
				<view class="value">
					<template v-if="takeoutDish.portionUnit && takeoutDish.portionUnit > 0">
						<SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-24" size="20"
							@click="min('portionUnit')" />
						<text class="value-text">{{ takeoutDish.portionUnit || '' }}</text>
					</template>
					<SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-25" size="20"
						@click="add('portionUnit')" />
					<text class="unit">个/份</text>
				</view>
			</view>
			<view class="item">
				<view class="label required">价格</view>
				<view class="value">
					<template v-if="takeoutDish.price && takeoutDish.price > 0">
						<SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-24" size="20"
							@click="min('price')" />
						<text class="value-text">{{ takeoutDish.price || '' }}</text>
					</template>
					<SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-25" size="20"
						@click="add('price')" />
					<text class="unit">元/份</text>
				</view>
			</view>
			<view class="item">
				<view class="label required">限购数量</view>
				<view class="value">
					<template v-if="takeoutDish.limitPerPerson && takeoutDish.limitPerPerson > 0">
						<SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-24" size="20"
							@click="min('limitPerPerson')" />
						<text class="value-text">{{ takeoutDish.limitPerPerson || '' }}</text>
					</template>
					<SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-25" size="20"
						@click="add('limitPerPerson')" />
					<text class="unit">份/人</text>
				</view>
			</view>
		</view>
		<view class="upload-area">
			<!--没有id需要传入空串-->
			<UploadImage :url="`/dishimage/upload/${takeoutDish.id || 0}`" v-model="takeoutDishImage" />
		</view>
		<view class="actions flex-center">
			<BaseButton class="cancel" size="medium" btn-type="cancel" @click="onCancel">取消</BaseButton>
			<BaseButton class="confirm" size="medium" btn-type="save" @click="onSubmit">提交</BaseButton>
		</view>
	</view>
</template>
<script setup lang="ts">
import type { TakeoutDish } from '@/subpages/takeout/models';
import { takeoutDishService } from '@/subpages/takeout/service';
import { ref } from 'vue';
import useTakeoutAdminStore from '@/subpages/takeout/store/admin';
import { ImageInfo } from '@/components/UploadImage.vue';
const takeoutAdminStore = useTakeoutAdminStore();
const isEdit = ref(false); // 是否为编辑状态
const dishValidateRule = {
	name: { required: true, message: '请输入菜名', type: 'string' },
	portionUnit: { required: true, message: '请设置份量', type: 'number' },
	price: { required: true, message: '请设置价格', type: 'number' },
	limitPerPerson: { required: true, message: '请设置限购数量', type: 'number' },
};
const takeoutDishImage = ref<ImageInfo>({});
const takeoutDish = ref<TakeoutDish>({} as TakeoutDish); // 菜品信息
const isFromDishList = ref(false); // 是否从菜品列表页面跳转过来
onLoad(async (query: any) => {
	// 如果有菜品ID，编辑页面：获取菜品详情
	if (query && query.dishId) {
		isFromDishList.value = query.deletable == 1
		isEdit.value = true; // 有菜品ID，表示编辑页面
		const dishId = query.dishId as string;
		// 根据ID获取菜品详情
		takeoutDish.value = await takeoutDishService.getDishById(dishId);
		takeoutDishImage.value = {
			imageId: takeoutDish.value.imageId || '',
			imageUrl: takeoutDish.value.imageUrl || '',
			dishId: takeoutDish.value.id || '',
			dishName: takeoutDish.value.name || '',
			images: takeoutDish.value.images || [],
		};
		uni.setNavigationBarTitle({
			title: `编辑菜品`,
		});
	} else {
		isEdit.value = false; // 没有菜品ID，表示新增页面
		takeoutDish.value = {} as TakeoutDish;
	}
});
const isEmpty = (val: string) => {
	return val === null || val === undefined || val.trim() === ''
}
const isNameEmpty = computed(() => isEmpty(takeoutDish.value.name))
const dishNameChange = (event: any) => {
	// 处理菜名输入变化
	takeoutDishImage.value.dishName = takeoutDish.value.name || '';
};

const add = (property: string) => {
	const val = takeoutDish.value[property as keyof TakeoutDish] as number;
	if (val && val >= 0) {
		if (val < 99) {
			(takeoutDish.value as any)[property] = val + 1;
		} else {
			(takeoutDish.value as any)[property] = 99; // 最大为99
			uni.showToast({
				title: '最大值为99',
				icon: 'none',
			});
		}
	} else {
		(takeoutDish.value as any)[property] = 1; // 如果当前值为0或未定义，则设置为1
	}
};
const min = (property: string) => {
	const val = takeoutDish.value[property as keyof TakeoutDish] as number;
	if (val && val > 0) {
		(takeoutDish.value as any)[property] = val - 1;
	}
};

const validate = () => {
	const errors: string[] = [];
	for (const key in dishValidateRule) {
		const rule = dishValidateRule[key as keyof typeof dishValidateRule];
		const value = takeoutDish.value[key as keyof TakeoutDish];
		if (rule.required) {
			if (rule.type == 'string') {
				if (!value || (value as string).trim() === '') {
					errors.push(rule.message);
				}
			} else if (rule.type == 'number') {
				if (value === undefined || value === null || isNaN(value as number) || (value as number) <= 0) {
					errors.push(rule.message);
				}
			}
		}
	}
	return errors;
};

const isNameValid = (name: string) => {
	return name.length <= 8;
};

const onSubmit = async () => {
	const errors = validate();
	if (errors.length > 0) {
		uni.showToast({
			title: errors[0],
			icon: 'none',
		});
		return;
	}
	// 检查菜名是否符合要求
	if (!isNameValid(takeoutDish.value.name)) {
		uni.showToast({
			title: '菜名不能超过8个字符',
			icon: 'none',
		});
		return;
	}
	// 如果有图片，设置图片信息
	if (takeoutDishImage.value.imageId) {
		takeoutDish.value.imageId = takeoutDishImage.value.imageId;
		takeoutDish.value.imageUrl = takeoutDishImage.value.imageUrl;
		takeoutDish.value.images = takeoutDishImage.value.images;
	} else {
		// 如果没有图片，清空图片信息
		takeoutDish.value.imageId = '';
		takeoutDish.value.imageUrl = '';
	}

	if (isEdit.value) {
		// 2025-07-11 无需设置，已经改为下架后不可见（删除）
		// takeoutDish.value.sysDeleted = 0; // 如果是编辑状态设置为未下架状态
		await takeoutDishService.updateDish(takeoutDish.value);
		if (isFromDishList.value) {
			// 如果是从菜品列表页面跳转过来，更新当前菜品列表
			uni.navigateTo({
				url: '/subpages/takeout/pages/admin/dish-list/index',
			});
		} else {
			const updatedDish = await takeoutDishService.getDishById(takeoutDish.value.id || '');
			// 更新菜品数据
			takeoutAdminStore.updateCurTakeoutMenuDishList(updatedDish);
			// 提交成功后，回退到菜品列表页面
			uni.navigateBack({
				delta: 1, // 回退一层
			});
		}

	} else {
		// 跳转到列表
		await takeoutDishService.saveDish(takeoutDish.value);
		uni.navigateTo({
			url: '/subpages/takeout/pages/admin/dish-list/index',
		});
	}
};

const onCancel = () => {
	// 回退到菜品列表页面
	uni.navigateBack();
};
</script>

<style lang="scss" scoped>
@import '@/subpages/takeout/styles/variable.scss';
@import '@/subpages/takeout/styles/flex.scss';

.admin-page {
	background: $takeout-bg;
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
}

.dish-detail {
	width: 100%;
	display: flex;
	flex-direction: column;
	background-color: #fff;
	flex: 0 0 auto;
}

.item {
	height: 40px;
	width: 100%;
	padding: 0 15px;
	border-bottom: 1px solid rgba(153, 153, 153, 0.2);
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-sizing: border-box;

	.value {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: flex-end;

		.input-group {
			height: 32px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			position: relative;
			border-radius: 3px;
			width: 100%;
			padding: 0 8px;
			background: #F8F8F8;
		}

		.input-name {
			flex: 1 1 auto;
			width: 100%;
			height: 100%;
			font-size: 14px;
			text-align: right;
			background-color: #F8F8F8;
			margin-right: 6px;
		}

		.value-text {
			width: 24px;
			text-align: center;
		}
	}

	.label {
		position: relative;
		flex: 0 0 105px;
		font-size: 14px;

		&.required::before {
			content: '*';
			color: #e50101;
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			left: -6px;
		}
	}

	.unit {
		font-size: 12px;
		color: $uni-text-color-secondary;
		margin-left: 8px;
	}
}

.upload-area {
	flex: 1;
	width: 100%;
	margin-top: 6px;
	background-color: #fff;
	box-sizing: border-box;
}

.actions {
	flex: 0 0 auto;
	width: 100%;
	display: flex;
	align-items: center;
	padding: 0 15px;
	box-sizing: border-box;
	margin-bottom: 34px;

	.cancel+.confirm {
		margin-left: 10px;
	}
}
</style>
