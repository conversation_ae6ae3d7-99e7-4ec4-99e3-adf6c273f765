<template>
    <SlTabbarPage :tab-bar-root="'subpages/takeout'" admin>
        <view class="admin-page">
            <!-- 菜品列表 -->
            <view class="item-list">
                <uni-list v-if="takeoutMenuDishList.length" class="takeout-menu-list">
                    <TakeoutMenuListItem v-for="item of takeoutMenuDishList" :key="item.dishId" :item="item"
                        @modify="onModify" :selectable="false" deletable editable @remove="onRemove" />
                </uni-list>
                <EmptyList v-else show />
            </view>
            <!-- 浮动新增 -->
            <view class="fab flex-center" @click="onAdd">
                <SlSubSvgIcon class="inline-flex" subpage="takeout" name="52-52-4" size="52" />
            </view>
        </view>
    </SlTabbarPage>
</template>

<script setup lang="ts">

import { ref } from 'vue';
import TakeoutMenuListItem from '@/subpages/takeout/components/TakeoutMenuListItem.vue'
import EmptyList from '@/subpages/takeout/components/EmptyList.vue'
import { TakeoutMenuDish } from '@/subpages/takeout/models';
import { takeoutDishService } from '@/subpages/takeout/service';
// 数据与状态
const takeoutMenuDishList = ref<TakeoutMenuDish[]>([]);

onLoad(async () => {
    // 页面加载时获取菜品列表
    await getDishList();
});

const getDishList = async () => {
    // TODO: 需要分页获取菜品列表
    const list = await takeoutDishService.getDishList({ ifPage: false });
    takeoutMenuDishList.value = list.map(item => {
        return {
            dishId: item.id,
            dishName: item.name,
            portionUnit: item.portionUnit,
            limitPerPerson: item.limitPerPerson,
            price: item.price,
            imageId: item.imageId,
            imageUrl: item.imageUrl,
            orderCount: 0,
            takeoutMenuId: ''
        }
    });
    console.log('转换后的菜品列表', takeoutMenuDishList.value);
}

const onAdd = () => {
    // uni.navigateTo({
    //     url: '/subpages/takeout/pages/admin/dish-edit/index'
    // });
};

const onModify = (item: TakeoutMenuDish) => {
    // uni.navigateTo({
    //     url: `/subpages/takeout/pages/admin/dish-edit/index?id=${item.id}`
    // });
};

const onRemove = (dishId: string) => {
    // uni.showModal({
    //     title: '删除菜品',
    //     content: `确定删除菜品 ${item.name} 吗？`,
    //     success: (res) => {
    //         if (res.confirm) {
    //             // 调用删除接口
    //         }
    //     }
    // });
};

</script>

<style lang="scss" scoped>
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/flex.scss";

.admin-page {
    background: $takeout-bg;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.fab {
    position: fixed;
    right: 10px;
    bottom: 117px;
    width: 52px;
    height: 52px;
}

// 菜品列表
.item-list {
    padding: 0 15px;
    background-color: #ffffff;
    box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
    flex: 1 1 auto;
    overflow: hidden;
    overflow-y: auto;
    height: 100%;

    .takeout-menu-list {
        padding: 0;
        margin: 0;
        height: 100%;
    }
}
</style>