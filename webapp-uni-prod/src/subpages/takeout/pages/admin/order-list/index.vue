<template>
    <!--管理员端外卖订单列表-->
    <SlTabbarPage :tab-bar-root="'subpages/takeout'" admin>
        <view class="order-list">
            <view class="order-list-header">
                <text class="header-title">订单列表</text>
            </view>
            <view class="order-list-content">
                <!-- <OrderProductItem v-for="(item) in orderList" :item="item" :key="item.id" /> -->
            </view>
            <view class="takeout-bottom-actions">
                <button class="takeout-btn btn-primary btn-h-40" @click="goToDetailList">订单详情</button>
            </view>
        </view>
    </SlTabbarPage>
</template>
<script lang="ts" setup>
// import OrderProductItem from '@/subpages/takeout/components/admin-order/.vue';
import { TakeoutOrder } from '@/subpages/takeout/models';
// import { AdminOrderProductList } from '@/subpages/takeout/mocks/AdminOrderList.mock';
// const orderMockList = AdminOrderProductList.map(ele => {
//     return { ...ele }
// })
const orderList = ref<TakeoutOrder[]>([]);
const goToDetailList = () => {
    uni.navigateTo({
        url: '/subpages/takeout/pages/admin/order-detail-list/index'
    });
}
</script>

<style lang="scss" scoped>
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/float.scss";

.order-list {
    background-color: $takeout-bg;
    padding: 0 15px;
    padding-bottom: 55px;
}
</style>