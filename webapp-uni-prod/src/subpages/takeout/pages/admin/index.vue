<template>
    <view class="admin-page">
        <!-- 搜索栏 -->
        <view class="search-bar">
            <input class="search-input" v-model="keyword" placeholder="请输入菜名" />
            <SlSVgIcon name="16-16-6" size="16" @click="onSearch" />
        </view>

        <!-- 菜品列表 -->
        <view class="item-list">
            <uni-list>
                <TakeoutMenuListItem v-for="item in filteredItems" :key="item.dishId" :item="item"
                    :selected-ids="selectedIds" selectable @check="onCheck" @modify="onModify" />
            </uni-list>
        </view>
        <!-- 浮动发布 -->
        <view class="fab" @click="onPublish">
            <SlSVgIcon name="52-52-5" size="52" />
        </view>

        <!-- 底部已选菜品入口 -->
        <view class="footer" @click="onGoSelected">
            <view class="cart">
                <SlSVgIcon name="20-20-23" size="20" />
                <view v-if="selectedIds.length" class="badge">
                    {{ selectedIds.length }}
                </view>
            </view>
            <text class="footer-text">已选菜品</text>
        </view>
        <!-- 弹出层：已选菜品列表 -->
        <SelectedMenuPopup v-model:show="showPopup" :items="items" :selected-ids="selectedIds" @clear="onClear"
            @remove="onRemove" />
    </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import TakeoutMenuListItem from '../../components/TakeoutMenuListItem.vue'
import SelectedMenuPopup from '../../components/SelectedMenuPopup.vue'
import type { TakeoutMenuItem, TakeoutMenu, TakeoutProduct } from '../../models/Takeout'

// 数据与状态
const items = ref<TakeoutMenuItem[]>([{
    dishId: '1',
    dishName: '大千层馒头',
    price: 4,
    portionUnit: 2,
    limitPerPerson: 2,
    imageId: 'mantou',
},
{
    dishId: '2',
    dishName: '玉米饼',
    price: 4,
    portionUnit: 4,
    limitPerPerson: 3,
    imageId: 'yumibing',
},
{
    dishId: '3',
    dishName: '长花卷',
    price: 3,
    portionUnit: 4,
    limitPerPerson: 5,
    imageId: 'huajuan',
},
{
    dishId: '4',
    dishName: '大葱花卷',
    price: 3,
    portionUnit: 4,
    limitPerPerson: 5,
    imageId: 'dachonghuajuan',
},
{
    dishId: '5',
    dishName: '大葱花卷',
    price: 3,
    portionUnit: 4,
    limitPerPerson: 5,
    imageId: 'dachonghuajuan',
},
{
    dishId: '6',
    dishName: '大葱花卷',
    price: 3,
    portionUnit: 4,
    limitPerPerson: 5,
    imageId: 'dachonghuajuan',
},
{
    dishId: '7',
    dishName: '大肉夹馍',
    price: 6,
    portionUnit: 1,
    limitPerPerson: 2,
    imageId: 'roujiamo',
},
{
    dishId: '8',
    dishName: '大肉包子',
    price: 5,
    portionUnit: 2,
    limitPerPerson: 2,
    imageId: 'baozhi',
},
{
    dishId: '9',
    dishName: '大肉饼',
    price: 5,
    portionUnit: 1,
    limitPerPerson: 2,
    imageId: 'roubing',
},
{
    dishId: '10',
    dishName: '大肉包子',
    price: 5,
    portionUnit: 2,
    limitPerPerson: 2,
    imageId: 'baozhi',
}])
const keyword = ref<string>('')
const selectedIds = ref<string[]>([])
const showPopup = ref<boolean>(false)

// 过滤后的列表
const filteredItems = computed(() =>
    items.value.filter(i => !keyword.value || i.dishName.includes(keyword.value))
)

function onSearch() {
    console.log('搜索：', keyword.value)
}

function onCheck(data: { dishId: string, checked: boolean }) {
    const { dishId, checked } = data
    if (checked) {
        if (!selectedIds.value.includes(dishId)) selectedIds.value.push(dishId)
    } else {
        selectedIds.value = selectedIds.value.filter(id => id !== dishId)
    }
}

function onModify(item: TakeoutMenuItem) {
    uni.navigateTo({ url: `/pages/takeout-menu-edit/index?dishId=${item.dishId}` })
}

async function onPublish() {
    if (!selectedIds.value.length) {
        uni.showToast({ title: '请先勾选菜品', icon: 'none' })
        return
    }
    const menu: TakeoutMenu = {
        menuDate: new Date().toISOString().slice(0, 10),
        takeoutMenuDishList: items.value
            .filter(i => selectedIds.value.includes(i.dishId))
            .map<TakeoutProduct>(i => ({
                dishId: i.dishId,
                dishName: i.dishName,
                price: i.price,
                portionUnit: i.portionUnit
            }))
    }
    // await api.takeout.publishMenu(menu)
    uni.showToast({ title: '发布成功', icon: 'success' })
}

function onGoSelected() {
    if (!selectedIds.value.length) {
        uni.showToast({ title: '请先勾选菜品', icon: 'none' })
        return
    }
    showPopup.value = true
}

function onRemove(dishId: string) {
    selectedIds.value = selectedIds.value.filter(id => id !== dishId)
}

function onClear() {
    selectedIds.value = []
}
</script>

<style lang="scss" scoped>
.admin-page {
    background: #F4F6FA;
    display: flex;
    flex-direction: column;
    height: 100%;
}

// 搜索栏
.search-bar {
    display: flex;
    background: #fff;
    align-items: center;
    position: relative;
    padding: 10px 15px;
    flex: 0 0 auto;

    .search-input {
        height: 30px;
        line-height: 30px;
        border: 1px solid rgba(153, 153, 153, 0.5);
        border-radius: 3px;
        width: 100%;
        padding: 0 8px;
    }

    sl-s-vg-icon {
        position: absolute;
        right: 23px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
    }
}

// 菜品列表
.item-list {
    padding: 0 15px;
    background-color: #ffffff;
    box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
    flex: 1;
    padding-bottom: 50px; // 给底部留出空间
}

// 浮动发布按钮
.fab {
    position: fixed;
    right: 10px;
    bottom: 100px;
    width: 52px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
}

// 底部已选菜品
.footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    background: #FFFFFF;
    box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);

    .cart {
        width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .badge {
            position: absolute;
            top: -3px;
            right: -3px;
            min-width: 12px;
            height: 12px;
            line-height: 12px;
            font-size: 8px;
            color: #fff;
            background: $uni-text-color-price;
            border-radius: 50%;
            text-align: center;
        }
    }

    .footer-text {
        font-size: 16px;
        color: #333;
        margin-left: 6px;
    }


}
</style>
