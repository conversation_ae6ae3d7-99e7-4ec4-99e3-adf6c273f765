<template>
    <SlTabbarPage :tab-bar-root="'subpages/takeout'" admin>
        <view class="admin-page">
            <!-- 搜索栏 -->
            <view class="search-bar">
                <input class="search-input" v-model="keyword" placeholder="请输入菜名" />
                <SlSubSvgIcon subpage="takeout" class="search-icon" name="16-16-6" size="16" @click="onSearch" />
            </view>

            <!-- 菜品列表 -->
            <view class="item-list">
                <uni-list v-if="filteredItems.length" class="takeout-menu-list">
                    <TakeoutMenuListItem v-for="item in filteredItems" :key="item.dishId" :item="item" selectable
                        editable :selected-ids="selectedIds" @check="onCheck" @modify="onModify" />
                </uni-list>
                <EmptyList v-else show />
            </view>
            <!-- 浮动发布 -->
            <view class="fab flex-center" @click="onPublish">
                <SlSubSvgIcon class="inline-flex" subpage="takeout" name="52-52-5" size="52" />
            </view>

            <!-- 底部已选菜品入口 -->
            <view class="footer" @click="onGoSelected">
                <view class="cart">
                    <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-23" size="20" />
                    <view v-if="selectedDishList.length" class="badge">
                        {{ selectedDishList.length }}
                    </view>
                </view>
                <text class="footer-text">已选菜品</text>
            </view>
            <!-- 弹出层：已选菜品列表 -->
            <SelectedMenuPopup v-model:show="showSelectedMenu" :selectedItems="selectedDishList" @clear="onClear"
                @remove="onRemove" />
            <!--模态框：提示后天配置后天的菜谱-->
            <up-modal class="custom-modal" :show="!!twoDaysLaterTip" :showCancelButton="false"
                :showConfirmButton="false">
                <view class="modal-content">
                    <view class="tip-title flex-center">
                        <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-23" size="20" />
                    </view>
                    <view class="tip-content flex-col-center-x">
                        <text>
                            后天的日期已开放，可前往配置订餐
                        </text>
                        <text>菜谱啦~</text>
                    </view>
                    <view class="modal-footer flex-center-y">
                        <view @click="closeTipModal"
                            class="takeout-btn btn-border-unset btn-h-40 cancel takeout-secondary-text">关闭</view>
                        <view @click="selectTwoDaysLaterMenu"
                            class="takeout-btn  btn-border-unset btn-h-40 confirm takeout-primary-text takeout-blod-text">
                            立即前往
                        </view>
                    </view>
                </view>
            </up-modal>
        </view>
    </SlTabbarPage>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import TakeoutMenuListItem from '@/subpages/takeout/components/TakeoutMenuListItem.vue'
import SelectedMenuPopup from '@/subpages/takeout/components/SelectedMenuPopup.vue'
// import type { TakeoutMenuItem } from '@/subpages/takeout/models/Takeout'
import { takeoutDishService, takeoutMenuService } from '@/subpages/takeout/service'
import { formatDate } from '@/subpages/takeout/utils'
import type { TakeoutMenu, TakeoutMenuDish } from '@/subpages/takeout/models'
import EmptyList from '@/subpages/takeout/components/EmptyList.vue'
import { usePrincipalStore } from '@/store'
const principalStore = usePrincipalStore()
// 数据与状态
const todayMenu = ref<TakeoutMenu>({
    id: '',
    menuDate: formatDate(new Date()),
    takeoutMenuDishList: []
})
// 已选菜品
const selectedDishList = ref<TakeoutMenuDish[]>([])
// 搜索关键字
const keyword = ref<string>('')
// 后天配置外卖菜单提示
const twoDaysLaterTip = ref<string>('')
// 是否显示已选菜品弹层
const showSelectedMenu = ref<boolean>(false)
// 当前发布的日期
const curPublishDate = ref<string>('')
/** 
 * 是否是今天8:00-24:00之间
 * 1. 在今天8:00-24:00 提示后天的日期需要开放
 */
// const isWorkTime = computed(() => {
//     const today = new Date()
//     const hours = today.getHours()
//     // 如果是今天的8点到24点之间，提示后天的菜谱配置
//     return (hours >= 8 && hours < 24) && (today.getDay() === 1 || today.getDay() === 2)
// })
const selectedIds = computed(() => selectedDishList.value.map(i => i.dishId))

onLoad(async () => {
    const today = new Date()
    const todayStr = formatDate(today)
    // 获取当天菜单
    const todayMenu = await getMneuByDate(todayStr)
    console.log('今天的菜单:', todayMenu)
    // 说明提示后天菜单未配置
    if (todayMenu && todayMenu.notifyMessage) {
        twoDaysLaterTip.value = todayMenu.notifyMessage.content
    }
    // // 获取两天后的菜单
    // const twoDaysLaterMenu = await getTwoDaysLaterMenu()
    // console.log('两天后的菜单:', twoDaysLaterMenu)
    // if (!twoDaysLaterMenu || !twoDaysLaterMenu.takeoutMenuDishList || twoDaysLaterMenu.takeoutMenuDishList.length === 0) {
    //     // 如果没有菜品，提示配置
    //     twoDaysLaterHasMenu.value = false
    // }
})

const getMneuByDate = async (dateStr: string) => {
    const account = await principalStore.identity()
    //TODO：临时设置 95b71b8db24c4ed9af61a132cd8baa1d
    return await takeoutMenuService.getTakeoutMenuByDate(dateStr, '95b71b8db24c4ed9af61a132cd8baa1d')
}

const getTwoDaysLaterStr = () => {
    const today = new Date()
    const twoDaysLater = new Date(today)
    twoDaysLater.setDate(today.getDate() + 2)
    return formatDate(twoDaysLater)
}



// 过滤后的列表
const filteredItems = computed(() =>
    todayMenu.value.takeoutMenuDishList.filter(i => !keyword.value || i.dishName.includes(keyword.value))
)

const onSearch = () => {
    console.log('搜索：', keyword.value)
}

const onCheck = (data: { dish: TakeoutMenuDish, checked: boolean }) => {
    const { dish, checked } = data
    if (checked) {
        selectedDishList.value.push(dish)
    } else {
        selectedDishList.value = selectedDishList.value.filter(item => item.dishId !== dish.dishId)
    }
}

function onModify(item: any) {
    uni.navigateTo({ url: `/pages/takeout-menu-edit/index?dishId=${item.dishId}` })
}

const onPublish = async () => {
    if (!selectedDishList.value.length) {
        uni.showToast({ title: '请先勾选菜品' })
        return
    }
    const menu: TakeoutMenu = {
        menuDate: curPublishDate.value,
        takeoutMenuDishList: selectedDishList.value
            .map(i => ({
                dishId: i.dishId,
                dishName: i.dishName,
                price: i.price,
                portionUnit: i.portionUnit,
                limitPerPerson: i.limitPerPerson,
                imageId: i.imageId,
                orderCount: 0, // 发布时订单数为0
                takeoutMenuId: '' // 发布时菜单ID为空
            }))
    }
    await takeoutMenuService.saveTakeoutMenu(menu)
}

const onGoSelected = () => {
    if (!selectedIds.value.length) {
        uni.showToast({ title: '请先勾选菜品', icon: 'none' })
        return
    }
    showSelectedMenu.value = true
}
const selectTwoDaysLaterMenu = async () => {
    twoDaysLaterTip.value = ''
    const list = await takeoutDishService.getDishList({ ifPage: false })
    console.log('两天后的菜品列表:', list)
    curPublishDate.value = getTwoDaysLaterStr()// 两天后的日期
    todayMenu.value = {
        id: '',
        menuDate: curPublishDate.value,
        takeoutMenuDishList: list.map(dish => ({
            dishId: dish.id,
            dishName: dish.name,
            price: dish.price,
            portionUnit: dish.portionUnit,
            limitPerPerson: dish.limitPerPerson,
            imageId: dish.imageId,
            orderCount: 0,
            takeoutMenuId: ''
        }))
    }
    console.log('两天后的菜单:', todayMenu.value)
}

const closeTipModal = () => {
    twoDaysLaterTip.value = ''
}

const onRemove = (dishId: string) => {
    // selectedIds.value = selectedIds.value.filter(id => id !== dishId)
    selectedDishList.value = selectedDishList.value.filter(item => item.dishId !== dishId)
}

const onClear = () => {
    // selectedIds.value = []
    selectedDishList.value = []
}
</script>

<style lang="scss" scoped>
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/flex.scss";
@import "@/subpages/takeout/styles/text.scss";

.admin-page {
    background: $takeout-bg;
    display: flex;
    flex-direction: column;
    height: 100%;
}

// 搜索栏
.search-bar {
    display: flex;
    background: #fff;
    align-items: center;
    position: relative;
    padding: 10px 15px;
    flex: 0 0 auto;

    .search-input {
        height: 30px;
        line-height: 30px;
        border: 1px solid rgba(153, 153, 153, 0.5);
        border-radius: 3px;
        width: 100%;
        padding: 0 8px;
    }

    .search-icon {
        position: absolute;
        right: 23px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
    }
}

// 菜品列表
.item-list {
    padding: 0 15px;
    background-color: #ffffff;
    box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
    flex: 1 1 auto;
    height: calc(100% - 50px - 50px); // search-bar + footer height
    overflow: hidden;
    overflow-y: auto;

    .takeout-menu-list {
        padding: 0;
        margin: 0;
        height: 100%;
    }
}

// 浮动发布按钮
.fab {
    position: fixed;
    right: 10px;
    bottom: 117px;
    width: 52px;
    height: 52px;

}

// 底部已选菜品
.footer {
    // position: fixed;
    // left: 0;
    // right: 0;
    // bottom: 65px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    background: #FFFFFF;
    box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);

    .cart {
        width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .badge {
            position: absolute;
            top: -3px;
            right: -3px;
            min-width: 12px;
            height: 12px;
            line-height: 12px;
            font-size: 8px;
            color: #fff;
            background: $takeout-price-color;
            border-radius: 50%;
            text-align: center;
        }
    }

    .footer-text {
        font-size: 16px;
        color: #333;
        margin-left: 6px;
    }


}

.custom-modal {
    border-radius: 10px;

    ::v-deep .u-modal__content {
        padding: 0 !important;
    }
}

.modal-content {
    width: 100%;
    padding-top: 20px;

    .tip-content {
        margin-top: 12px;
    }
}

.modal-footer {
    border-top: 1px solid #D8D8D8;
    margin-top: 20px;

    .takeout-btn {
        width: 50%;

        &.cancel {
            border-right: 1px solid #D8D8D8;
        }
    }

}
</style>
