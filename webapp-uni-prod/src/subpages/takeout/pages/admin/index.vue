<template>
    <SlTabbarPage :tab-bar-root="'subpages/takeout'" admin>
        <view class="admin-page" :class="{ 'published': takeoutMenu.published }">
            <!--日期选择-->
            <view class="takeout-calendar" v-if="canlendarList.length">
                <CalendarComponent v-model="currDate" @dateChange="onDateChange" :date-data="canlendarList" />
            </view>
            <!-- 搜索栏 -->
            <view class="search-bar" v-if="takeoutMenu.published != true">
                <view class="search-input-group">
                    <input class="search-input" v-model="keyword" @input="onSearchChange" placeholder="请输入菜名" />
                    <view class="clear-icon" @tap.stop="onClearChange">
                        <SlSubSvgIcon class="inline-icon" v-if="!isKeywordEmpty" subpage="takeout" name="12-12-10"
                            size="12" />
                    </view>
                    <view class="search-icon" @tap.stop="onClearChange">
                        <SlSubSvgIcon subpage="takeout" class="inline-icon" name="16-16-6" size="16"
                            @tap="onSearchChange" />
                    </view>
                </view>
            </view>
            <!-- 菜品列表 -->
            <view class="item-list">
                <uni-list v-if="filteredItems.length" class="takeout-menu-list">
                    <!-- 当前日期已发布的菜品 -->
                    <template v-if="takeoutMenu.published">
                        <OrderListItemCard v-for="item in filteredItems" :key="item.id" :item="item" />
                    </template>
                    <!--选购后天的菜品-->
                    <template v-else>
                        <TakeoutMenuListItem v-for="item in filteredItems" :key="item.id" :item="item" selectable
                            editable />
                    </template>
                </uni-list>
                <TakeoutEmptyList v-else show />
            </view>
            <!-- 浮动发布 -->
            <view v-if="!takeoutMenu.published" class="fab flex-center" @click="onPublish">
                <SlSubSvgIcon class="inline-flex" subpage="takeout" name="52-52-5" size="52" />
            </view>
            <!-- 底部已选菜品入口 -->
            <view class="footer" @tap="onGoSelected" v-if="!takeoutMenu.published">
                <view class="cart">
                    <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-23" size="20" />
                    <view v-if="cartDishCount > 0" class="badge">
                        {{ cartDishCount }}
                    </view>
                </view>
                <text class="footer-text">已选菜品</text>
            </view>
            <!--已发布菜单订单详细按钮-->
            <view class="detail-btn-container flex-center" v-else>
                <button class="takeout-btn btn-primary btn-h-40 btn-r-3 btn-w-100" @tap="onGoDetailList">订单详情</button>
            </view>
            <!-- 弹出层：已选菜品列表 -->
            <SelectedMenuPopup v-model:show="showSelectedMenu" />
            <!--模态框：提示后天配置后天的菜谱-->
            <up-modal class="custom-modal" :show="!!twoDaysLaterTip && showModal" :showCancelButton="false"
                :showConfirmButton="false" negativeTop="70px">
                <view class="modal-content">
                    <view class="tip-title flex-center">
                        <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-23" size="20" />
                    </view>
                    <view class="tip-content flex-col-center-x">
                        <text>
                            后天的日期已开放，可前往配置订餐
                        </text>
                        <text>菜谱啦~</text>
                    </view>
                    <view class="modal-footer flex-center-y">
                        <view @tap="closeTipModal"
                            class="takeout-btn btn-border-unset btn-h-40 cancel takeout-secondary-text">关闭
                        </view>
                        <view @tap="selectTwoDaysLaterMenu"
                            class="takeout-btn  btn-border-unset btn-h-40 confirm takeout-primary-text takeout-blod-text">
                            立即前往
                        </view>
                    </view>
                </view>
            </up-modal>
        </view>
    </SlTabbarPage>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import TakeoutMenuListItem from '@/subpages/takeout/components/TakeoutMenuListItem.vue'
import OrderListItemCard from '@/subpages/takeout/components/admin-order/OrderListItemCard.vue'
import SelectedMenuPopup from '@/subpages/takeout/components/SelectedMenuPopup.vue'
import type { TakeoutCalendar } from '@/subpages/takeout/models'
import TakeoutEmptyList from '@/subpages/takeout/components/TakeoutEmptyList.vue'
import { takeoutCalendarService } from '@/subpages/takeout/service'
import { useTakeoutAdminStore } from '@/subpages/takeout/store'
const takeoutAdminStore = useTakeoutAdminStore()
// 当前的外卖菜单
const takeoutMenu = computed(() => takeoutAdminStore.getTakeoutMenu)
// 后天配置外卖菜单提示
const twoDaysLaterTip = computed(() => takeoutMenu.value.notifyMessage && takeoutMenu.value.notifyMessage.content)
// 当前日期
const currDate = computed(() => takeoutAdminStore.getCurDate)

// 购物车菜品数量
const cartDishCount = computed(() => takeoutAdminStore.getCartDishCount)
const showModal = ref<boolean>(true)
// 搜索关键字
const keyword = ref<string>('')

// 是否显示已选菜品弹层
const showSelectedMenu = ref<boolean>(false)
// 外卖订单日历表
const canlendarList = ref<TakeoutCalendar[]>([])
const isEmpty = (val: string) => {
    return val === null || val === undefined || val.trim() === ''
}
const isKeywordEmpty = computed(() => isEmpty(keyword.value))
onLoad(async () => {
    canlendarList.value = await takeoutCalendarService.getTakeoutHolidaycalendarList()
    // 获取当天菜单
    await takeoutAdminStore.getTodayMenu()
})
// 监听菜单发布状态变化
const onSearchChange = () => {
    takeoutAdminStore.searchDishByName(keyword.value)
}
const onClearChange = () => {
    keyword.value = ''
    onSearchChange()
}
// 监听日期变化 date: YYYY-MM-DD 格式
const onDateChange = async (date: string) => {
    if (currDate.value === date) {
        return
    }
    takeoutAdminStore.setCurDate(date)
    keyword.value = ''
    const backupDate = takeoutAdminStore.getBackCurDate
    await takeoutAdminStore.getTakeoutMenuByDate(backupDate)
}
// 过滤后的列表
const filteredItems = computed(() => takeoutAdminStore.getFilteredDishList)

// 立即前往配置后天的菜单： 获取所有的菜品
const selectTwoDaysLaterMenu = async () => {
    keyword.value = ''
    await takeoutAdminStore.genAfterTwoDaysMenu()
}
// 点击发布按钮
const onPublish = async () => {
    if (!cartDishCount.value) {
        uni.showToast({ title: '请先勾选菜品', icon: 'none' })
        return
    }
    await takeoutAdminStore.publishMenu()
}

const onGoSelected = () => {
    if (!cartDishCount.value) {
        uni.showToast({ title: '请先勾选菜品', icon: 'none' })
        return
    }
    showSelectedMenu.value = true
}

const onGoDetailList = () => {
    const menuDate = currDate.value.replace(/-/g, '')
    uni.navigateTo({
        url: `/subpages/takeout/pages/admin/order-detail-list/index?menuDate=${menuDate}`
    })
    // 临时测试
    // takeoutAdminStore.resetPublishedMenu()
}

const closeTipModal = () => {
    showModal.value = false
}

</script>

<style lang="scss" scoped>
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/flex.scss";
@import "@/subpages/takeout/styles/text.scss";

.admin-page {
    background: $takeout-bg;
    display: flex;
    flex-direction: column;
    height: 100%;
}

// 日期选择
.takeout-calendar {
    background: #fff;
    margin-bottom: 10px;
}

// 搜索栏50: heigth 30 padding 10*2
.search-bar {
    display: flex;
    background: #fff;
    align-items: center;
    position: relative;
    padding: 10px 15px;
    flex: 0 0 auto;

    .search-input-group {
        height: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        border: 1px solid rgba(153, 153, 153, 0.5);
        border-radius: 3px;
        width: 100%;
        padding: 0 8px;
    }

    .search-input {
        height: 30px;
        flex: 1 1 auto;
    }

    .clear-icon {
        margin-right: 6px;
    }

    .search-icon,
    .clear-icon {
        flex: 0 0 auto;
        display: inline-flex;
        cursor: pointer;
        width: 16px;
        height: 16px;
        align-items: center;
        justify-content: center;

        .inline-icon {
            display: inline-flex;
        }
    }
}

// 菜品列表
.item-list {
    background-color: #ffffff;
    box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
    flex: 1 1 auto;
    height: calc(100% - 50px - 50px); // search-bar + footer height
    overflow: hidden;
    overflow-y: auto;

    .takeout-menu-list {
        padding: 0;
        margin: 0;
        height: 100%;
    }
}

.admin-page.published {
    .takeout-calendar {
        margin-bottom: 1px;
    }

    .item-list {
        background-color: transparent;
        box-shadow: none;
        height: calc(100% - 50px); // search-bar + detail-btn-container height
        padding: 0 15px;
    }
}


// 浮动发布按钮
.fab {
    position: fixed;
    right: 10px;
    bottom: 117px;
    width: 52px;
    height: 52px;

}

// 底部订单详情
.detail-btn-container {
    padding: 0 15px;
    height: 70px;
    box-sizing: border-box;
    border-radius: 10px 10px 0px 0px;
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);
    width: 100%;
}

// 底部已选菜品
.footer {
    // position: fixed;
    // left: 0;
    // right: 0;
    // bottom: 65px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    background: #FFFFFF;
    box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);

    .cart {
        width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .badge {
            position: absolute;
            top: -3px;
            right: -3px;
            min-width: 12px;
            height: 12px;
            line-height: 12px;
            font-size: 8px;
            color: #fff;
            background: $takeout-price-color;
            border-radius: 50%;
            text-align: center;
        }
    }

    .footer-text {
        font-size: 16px;
        color: #333;
        margin-left: 6px;
    }


}

.custom-modal {
    border-radius: 10px;

    ::v-deep .u-modal__content {
        padding: 0 !important;
    }
}

.modal-content {
    width: 100%;
    padding-top: 20px;

    .tip-content {
        margin-top: 12px;
    }
}

.modal-footer {
    border-top: 1px solid #D8D8D8;
    margin-top: 20px;

    .takeout-btn {
        width: 50%;

        &.cancel {
            border-right: 1px solid #D8D8D8;
        }
    }

}
</style>
