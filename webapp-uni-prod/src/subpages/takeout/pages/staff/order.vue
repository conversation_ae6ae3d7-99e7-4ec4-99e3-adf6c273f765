<template>
    <view class="order-container">
        <!--取餐时间-->
        <view class="order-header">
            <text>取餐时间：{{ orderTime }}</text>
        </view>
        <!-- 订单商品展示 -->
        <view class="order-items">
            <view class="order-item-list">
                <!-- <scroll-view class="order-item-list" @scroll="scroll" :scroll-left="120" :scroll-x="true"> -->
                <view class="order-item" v-for="(item) in orderItems" :key="item.name">
                    <view class="image-container">
                        <!-- 商品图片 -->
                        <image :src="item.imgUrl" class="item-image" />
                        <text class="product-count" v-if="item.count > 1">x{{ item.count }}</text>
                    </view>
                    <text class="product-name">{{ item.name }}</text>
                </view>
            </view>
            <!-- </scroll-view> -->
            <!--商品计数-->
            <view class="products-count">
                <text>共{{ productsCount }}件</text>
            </view>
        </view>

        <!-- 订单信息 -->
        <view class="order-info">
            <view class="order-total">
                <text class="total-label">合计：</text>
                <text class="total-price"> ¥{{ totalPrice }}元</text>
            </view>
            <view class="order-details">
                <view class="order-detail-item">
                    <text>订餐人：</text>
                    <text>{{ orderUser }}</text>
                </view>
                <view class="order-detail-item">
                    <text>手机号码：</text>
                    <text>{{ orderPhone }}</text>
                </view>
            </view>
        </view>
        <!-- 订单信息结束 -->
        <view class="order-pay">
            <view class="order-total">
                <text class="total-label">合计：</text>
                <text class="total-price"> ¥{{ totalPrice }}元</text>
            </view>
            <!-- 支付按钮 -->
            <button @click="goToPayment" class="payment-btn">立即支付</button>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const orderItems = ref([
    { imgUrl: '/static/icons/50-50/1.svg', count: 1, name: '大千层馒头' },
    { imgUrl: '/static/icons/50-50/1.svg', count: 4, name: '玉米饼' },
    { imgUrl: '/static/icons/50-50/1.svg', count: 1, name: '手擀面' },
    { imgUrl: '/static/icons/50-50/1.svg', count: 2, name: '刀削面' },
    { imgUrl: '/static/icons/50-50/1.svg', count: 3, name: '拉面' },
    { imgUrl: '/static/icons/50-50/1.svg', count: 1, name: '饺子' },
    { imgUrl: '/static/icons/50-50/1.svg', count: 2, name: '包子' },
]);

const totalPrice = ref(21); // 总价
const orderUser = ref('陈晓晓'); // 订单人
const orderPhone = ref('13528328856'); // 订单人手机
const orderTime = ref('2025-06-03'); // 取餐时间

const productsCount = ref(orderItems.value.reduce((total, item) => total + item.count, 0)); // 商品总数


function goToPayment() {
    // 执行支付相关逻辑
    console.log('跳转到支付界面');
}
</script>

<style scoped lang="scss">
.order-container {
    background-color: #F4F6FA;
}

.order-header {
    font-size: 12px;
    background-color: #fff;
    padding: 0 15px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.order-items {
    background-color: #fff;
    padding: 10px 15px;
    display: flex;
}

.order-item-list {
    white-space: nowrap;
    flex: 1;
    display: flex;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; // iOS 弹性滚动
    // 以下两行可以隐藏滚动条（可选）
    scrollbar-width: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .order-item {
        width: 50px;
        flex: 0 0 auto; // 固定宽度

        &+.order-item {
            margin-left: 11px; //商品之间的间距
        }

        .image-container {
            position: relative;
            width: 50px;
            height: 50px;
            border-radius: 5px;
            background: #FFFFFF;

            .item-image {
                width: 100%;
                height: 100%;
            }

            .product-count {
                display: inline-block;
                text-align: center;
                min-width: 17px;
                height: 12px;
                position: absolute;
                bottom: 0;
                left: 0;
                color: rgba(255, 255, 255, 0.8);
                border-radius: 18px;
                background: rgba(51, 51, 51, 0.5);
                font-size: 8px;
                backdrop-filter: blur(10px);
            }
        }

        .product-name {
            width: 100%;
            display: inline-block;
            text-align: center;
            font-size: 12px;
            color: #666666;
            margin-top: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

    }
}

.products-count {
    width: 91px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 12px;
    color: #666;
}

.order-total {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.order-info {
    border-top: 1px solid rgba(153, 153, 153, 0.2);
    background: #fff;
    height: 88px;
    width: 100%;
    padding: 0 15px;

    .order-total {
        margin: 9px 0;
    }
}

.total-price {
    color: $uni-text-color;
    font-weight: 700;
}

.order-details {
    display: flex;
    flex-direction: column;
    width: 100%;

    .order-detail-item {
        display: flex;
        flex-direction: row;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        text {
            display: block;
            font-size: $uni-font-size-sm;
            color: $uni-text-color-secondary;
        }
    }
}

.order-pay {
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 0 15px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 50px;
    background: #fff;
    border-radius: 10px 10px 0px 0px;
    box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);

    .order-total {
        flex: 1;
        padding-right: 34px;
    }
}

.payment-btn {
    background-color: $uni-btn-primary-bg;
    color: #ffffff;
    border-radius: 45px;
    width: 96px;
    height: 36px;
    line-height: 36px;
    text-align: center;
}
</style>
