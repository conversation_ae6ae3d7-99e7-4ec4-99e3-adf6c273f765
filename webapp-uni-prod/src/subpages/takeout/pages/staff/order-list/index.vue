<template>
    <SlTabbarPage :tab-bar-root="'subpages/takeout'">
        <!--订单列表-->
        <view class="order-container">
            <!--头部tabs-->
            <view class="order-tabs">
                <SlTabs :list="tabList" v-model="curTab" @click="tabChange"></SlTabs>
            </view>
            <!-- 订单商品展示 -->
            <view class="order-items">
                <OrderDetailCard v-for="(item) in orderList" :key="item.id" :item="item" />
            </view>
        </view>
    </SlTabbarPage>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { TakeoutOrderItem } from '../../../models/Takeout';
import OrderDetailCard from '../../../components/order/OrderDetailCard.vue';
import { TakeoutOrderListMock } from '../../../mocks/OrderList.mock';
import { TakeoutOrderStatusList } from '@/subpages/takeout/models/Takeout.const';
import { getStatusType, getStatusText } from '@/subpages/takeout/utils/order-status';
const curTab = ref('all'); // 当前选中的tab
// 订单状态列表
const tabList = TakeoutOrderStatusList.map(ele => {
    return {
        name: ele.text,
        value: ele.value
    }
})
// 订单列表数据，实际应用中应从后端获取
const mockList: TakeoutOrderItem[] = TakeoutOrderListMock.map(item => {
    return {
        ...item,
        // 模拟数据中添加一个订单状态
        statusType: getStatusType(item.status),
        statusText: getStatusText(item.status)
    }
})
const orderList = ref<TakeoutOrderItem[]>(mockList);

const tabChange = (tab: { name: string; value: string }) => {
    console.log('当前选中的tab:', tab);
}
</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";

.order-container {
    background-color: $takeout-bg;
    width: 100%;
}

.order-tabs {
    height: 40px;
    width: 100%;
}

.order-items {
    padding: 10px 15px;
    width: 100%;
}
</style>
