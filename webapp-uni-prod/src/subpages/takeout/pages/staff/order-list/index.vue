<template>
    <!--订单列表-->
    <SlTabbarPage :tab-bar-root="'subpages/takeout'">
        <view class="order-container">
            <!--头部tabs-->
            <view class="order-tabs">
                <SlTabs :list="tabList" v-model="curTab" @click="tabChange"></SlTabs>
            </view>
            <!-- 订单商品展示 -->
            <view class="order-items" :class="{ 'flex-center': orderList.length === 0 }">
                <uni-list v-if="orderList.length > 0">
                    <OrderDetailCard v-for="(item) in orderList" :key="item.id" :item="item"
                        @update:type="onTypeChange($event, item)" />
                </uni-list>
                <TakeoutEmptyList v-else show />
            </view>
            <!--弹窗-->
            <up-modal class="custom-tip-modal" :show="true" v-if="showModal" :showCancelButton="false"
                :showConfirmButton="false">
                <view class="modal-content">
                    <view class="tip-title flex-center">
                        <SlSubSvgIcon class="inline-flex" subpage="takeout" name="16-16-7" size="16" />
                        <text class="takeout-danger-text">提示</text>
                    </view>
                    <view class="tip-content flex-col-center-x">
                        {{ curHandleType === 'delete' ? '删除订单后无法恢复，请慎重考虑' : '当前订单未支付，您确定要取消该订单吗？' }}
                    </view>
                    <view class="modal-footer flex-center-y">
                        <view @tap="curHandleType == 'cancel' ? handleConfirm() : cancel()"
                            class="takeout-btn btn-border-unset btn-h-40 cancel takeout-secondary-text">
                            {{ curHandleType == 'cancel' ? '确定' : '取消' }}
                        </view>
                        <view @tap="curHandleType == 'cancel' ? cancel() : handleConfirm()"
                            class="takeout-btn btn-border-unset btn-h-40 confirm takeout-primary-text takeout-blod-text">
                            {{ curHandleType == 'cancel' ? '再想想' : '确定' }}
                        </view>
                    </view>
                </view>
            </up-modal>
        </view>
    </SlTabbarPage>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import OrderDetailCard from '@/subpages/takeout/components/order/OrderDetailCard.vue';
import TakeoutEmptyList from '@/subpages/takeout/components/TakeoutEmptyList.vue';
import { TakeoutOrderStatusList } from '@/subpages/takeout/models/Takeout.const';
import { takeoutOrderService } from '@/subpages/takeout/service'
import { usePrincipalStore } from '@/store';
import { OrderChangeType, TakeoutOrder, TakeoutOrderStatus } from '@/subpages/takeout/models';
const principalStore = usePrincipalStore();
const curHandleType = ref<OrderChangeType | undefined>(undefined); // 当前处理类型 delete or cancel
const curHandleOrder = ref<TakeoutOrder | null>(null); // 当前处理的订单
// 订单状态列表
const tabList = TakeoutOrderStatusList.map(ele => {
    return {
        name: ele.text,
        value: ele.value
    }
})
const curTab = ref('all'); // 当前选中的tab

const orderList = ref<TakeoutOrder[]>([]);

const showModal = computed(() => {
    return curHandleType.value === 'cancel' || curHandleType.value === 'delete'
})

const tabChange = async (tab: { name: string; value: string }) => {
    curTab.value = tab.value;
    // 切换tab时重新获取订单列表
    await getOrderList();
}

onLoad(async () => {
    // 页面加载时获取订单列表
    await getOrderList();
});

onPullDownRefresh(async () => {
    try {
        await getOrderList();
    } finally {
        uni.stopPullDownRefresh();
    }
});

/**
 * 获取订单列表
 */
const getOrderList = async () => {
    const account = await principalStore.identity();
    if (account) {
        const status = curTab.value === 'all' ? '' : curTab.value
        orderList.value = await takeoutOrderService.getTakeoutOrderList({
            userId: account.id,
            status: status as TakeoutOrderStatus
        })
        console.log('获取到的订单列表:', orderList.value);
    }
}

const onTypeChange = async (type: OrderChangeType, item: TakeoutOrder) => {
    curHandleType.value = type;
    if (type == 'refresh') {
        // 刷新订单列表
        await getOrderList();
        return;
    }
    curHandleOrder.value = item;
}

const cancel = () => {
    curHandleType.value = undefined; // 重置当前处理类型
    curHandleOrder.value = null; // 重置当前处理订单
}

const handleConfirm = async () => {
    if (!curHandleType.value || !curHandleOrder.value) {
        console.warn('当前没有处理类型或订单');
        return;
    }
    const orderId = curHandleOrder.value.id;
    if (curHandleType.value === 'delete') {
        await takeoutOrderService.deleteCompletedOrder(orderId!);
    } else if (curHandleType.value === 'cancel') {
        await takeoutOrderService.cancelUnpaidOrder(orderId!);
    }
    curHandleType.value = undefined; // 重置当前处理类型
    curHandleOrder.value = null; // 重置当前处理订单
    setTimeout(async () => {
        await getOrderList(); // 刷新订单列表
    }, 500); // 延时处理，确保数据更新后再获取订单列表
}

</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/flex.scss";
@import "@/subpages/takeout/styles/modal.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/text.scss";

.order-container {
    background-color: $takeout-bg;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.order-tabs {
    height: 40px;
    width: 100%;
    box-sizing: border-box;
    flex: 0 0 auto;
}

.order-items {
    flex: 1 1 auto;
    padding: 10px 15px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: calc(100% - 40px);
    overflow: hidden;
    overflow-y: auto;
}
</style>
