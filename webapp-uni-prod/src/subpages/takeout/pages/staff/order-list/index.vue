<template>
    <!--订单列表-->
    <SlTabbarPage :tab-bar-root="'subpages/takeout'">
        <view class="order-container">
            <!--头部tabs-->
            <view class="order-tabs">
                <SlTabs :list="tabList" v-model="curTab" @click="tabChange"></SlTabs>
            </view>
            <!-- 订单商品展示 -->
            <view class="order-items" :class="{ 'flex-center': orderList.length === 0 }">
                <scroll-view class="scroll-container" v-if="orderList.length > 0" scroll-y @scrolltolower="loadMore"
                    @refresherrefresh="resetList" refresher-enabled :refresher-triggered="refreshing">
                    <uni-list>
                        <OrderDetailCard v-for="(item) in orderList" :key="item.id" :item="item"
                            @update:type="onTypeChange($event, item)" />
                    </uni-list>
                    <u-loadmore :line="true" v-if="orderList.length > 2" :status="loadmoreStatus"
                        @loadmore="loadMore" />
                </scroll-view>
                <TakeoutEmptyList v-else show />
            </view>
            <!--弹窗-->
            <up-modal :class="'custom-tip-modal'" :show="showModal" :showCancelButton="false"
                :showConfirmButton="false">
                <TakeoutModal style="width: 100%;" :type="modalType" @cancel="cancel" @handleConfirm="handleConfirm" />
            </up-modal>
        </view>
    </SlTabbarPage>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import OrderDetailCard from '@/subpages/takeout/components/order/OrderDetailCard.vue';
import TakeoutEmptyList from '@/subpages/takeout/components/TakeoutEmptyList.vue';
import TakeoutModal from '@/subpages/takeout/components/modal/TakeoutModal.vue';
import { TakeoutOrderStatusList } from '@/subpages/takeout/models/Takeout.const';
import { TakeoutModalType } from '@/subpages/takeout/models/TakeoutModal';
import { takeoutOrderService } from '@/subpages/takeout/service'
import { useScrollRefresh } from '@/subpages/takeout/composables/useScrollRefresh'
import useTakeoutStore from '@/subpages/takeout/store/staff'
import { OrderChangeType, type TakeoutOrder, type TakeoutOrderSearch, TakeoutOrderStatus } from '@/subpages/takeout/models';
const takeoutStore = useTakeoutStore();
const curHandleType = ref<OrderChangeType | undefined>(undefined); // 当前处理类型 delete or cancel
const curHandleOrder = ref<TakeoutOrder | null>(null); // 当前处理的订单
// 订单状态列表
const tabList = TakeoutOrderStatusList.map(ele => {
    return {
        name: ele.text,
        value: ele.value
    }
})
const curTab = ref('all'); // 当前选中的tab

const modalType = computed(() => {
    if (curHandleType.value === 'cancel') {
        return TakeoutModalType.CANCEL_ORDER;
    } else if (curHandleType.value === 'delete') {
        return TakeoutModalType.DELETE_ORDER;
    }
    return undefined;
})

const showModal = computed(() => {
    return curHandleType.value === 'cancel' || curHandleType.value === 'delete'
})
const fetchOrders = async (search: TakeoutOrderSearch) => {
    return takeoutStore.getTakeoutOrderList(search);
}
const {
    list: orderList,
    search,
    refreshing,
    loadMore,
    loadmoreStatus,
    resetList, } = useScrollRefresh<TakeoutOrder, TakeoutOrderSearch>(
        fetchOrders
    );
const tabChange = async (tab: { name: string; value: string }) => {
    curTab.value = tab.value;
    search.value.status = tab.value as TakeoutOrderStatus; // 更新搜索条件
    await resetList(); // 切换tab时刷新列表
}
const onTypeChange = async (type: OrderChangeType, item: TakeoutOrder) => {
    curHandleType.value = type;
    if (type == 'refresh') {
        // 刷新订单列表
        await resetList();
        return;
    }
    curHandleOrder.value = item;
}
const cancel = () => {
    curHandleType.value = undefined; // 重置当前处理类型
    curHandleOrder.value = null; // 重置当前处理订单
}
const handleConfirm = async () => {
    if (!curHandleType.value || !curHandleOrder.value) {
        console.warn('当前没有处理类型或订单');
        return;
    }
    const orderId = curHandleOrder.value.id;
    if (curHandleType.value === 'delete') {
        await takeoutOrderService.deleteCompletedOrder(orderId!);
    } else if (curHandleType.value === 'cancel') {
        await takeoutOrderService.cancelUnpaidOrder(orderId!);
    }
    curHandleType.value = undefined; // 重置当前处理类型
    curHandleOrder.value = null; // 重置当前处理订单
    await resetList(); // 重置订单列表
}

</script>

<style lang="scss" scoped>
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/modal.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/flex.scss";
@import "@/subpages/takeout/styles/text.scss";

.order-container {
    background-color: $takeout-bg;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.order-tabs {
    height: 40px;
    width: 100%;
    box-sizing: border-box;
    flex: 0 0 auto;
}

.order-items {
    flex: 1 1 auto;
    padding: 10px 15px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: calc(100% - 40px);
    overflow: hidden;

    .scroll-container {
        width: 100%;
        height: 100%;
    }
}
</style>
