<template>
    <!--订单列表-->
    <SlTabbarPage :tab-bar-root="'subpages/takeout'">
        <view class="order-container">
            <!--头部tabs-->
            <view class="order-tabs">
                <SlTabs :list="tabList" v-model="curTab" @click="tabChange"></SlTabs>
            </view>
            <!-- 订单商品展示 -->
            <view class="order-items" :class="{ 'flex-center': orderList.length === 0 }">
                <uni-list v-if="orderList.length > 0">
                    <OrderDetailCard v-for="(item) in orderList" :key="item.id" :item="item" />
                </uni-list>
                <EmptyList v-else show />
            </view>
        </view>
    </SlTabbarPage>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import OrderDetailCard from '@/subpages/takeout/components/order/OrderDetailCard.vue';
import EmptyList from '@/subpages/takeout/components/EmptyList.vue';
import { TakeoutOrderStatusList } from '@/subpages/takeout/models/Takeout.const';
import { takeoutOrderService } from '@/subpages/takeout/service'
import { usePrincipalStore } from '@/store';
import { TakeoutOrder, TakeoutOrderStatus } from '@/subpages/takeout/models';
const principalStore = usePrincipalStore();
// 订单状态列表
const tabList = TakeoutOrderStatusList.map(ele => {
    return {
        name: ele.text,
        value: ele.value
    }
})
const curTab = ref('all'); // 当前选中的tab

const orderList = ref<TakeoutOrder[]>([]);

const tabChange = async (tab: { name: string; value: string }) => {
    console.log('当前选中的tab:', tab);
    curTab.value = tab.value;
    // 切换tab时重新获取订单列表
    await getOrderList();
}

onLoad(async () => {
    // 页面加载时获取订单列表
    await getOrderList();
});

/**
 * 获取订单列表
 */
const getOrderList = async () => {
    const account = await principalStore.identity();
    if (account) {
        const status = curTab.value === 'all' ? '' : curTab.value
        orderList.value = await takeoutOrderService.getTakeoutOrderList({
            userId: account.id,
            status: status as TakeoutOrderStatus
        })
        console.log('获取到的订单列表:', orderList.value);
    }
}

</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/flex.scss";

.order-container {
    background-color: $takeout-bg;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.order-tabs {
    height: 40px;
    width: 100%;
    box-sizing: border-box;
    flex: 0 0 auto;
}

.order-items {
    flex: 1 1 auto;
    padding: 10px 15px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: calc(100% - 40px);
    overflow: hidden;
    overflow-y: auto;
}
</style>
