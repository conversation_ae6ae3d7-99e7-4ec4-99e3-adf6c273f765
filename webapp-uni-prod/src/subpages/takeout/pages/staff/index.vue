<template>
    <view class="order-page">
        <!-- Banner Section -->
        <view class="banner">
            <image src="/static/images/takeout/banner.png" alt="Banner" class="banner-image" />
        </view>
        <!-- Header Section -->
        <view class="header-time">
            <text>取餐时间: 2025-06-03</text>
            <text>截止时间: 今天17:00</text>
        </view>
        <view class="menu-list">
            <uni-list>
                <uni-list-item v-for="item in menuItems" :key="item.id">
                    <view class="menu-item">
                        <image :src="'/static/images/takeout/' + item.image + '.svg'" class="menu-item-img" />
                        <view class="menu-item-info">
                            <text>{{ item.name }}</text>
                            <text class="price">¥{{ item.price }}</text>
                            <text class="limit">限制{{ item.limit }}份/人</text>
                        </view>
                        <view class="menu-item-actions">
                            <!--减少商品-->
                            <SlSVgIcon name="20-20-24" size="20" @click="removeFromCart(item)" />
                            <text style="margin: 0 8px;">{{ item.count || 0 }}</text>
                            <!--增加商品-->
                            <SlSVgIcon :name="computedAddDisabled(item) ? '20-20-26' : '20-20-25'" size="20"
                                @click="addToCart(item)" />
                        </view>
                    </view>
                </uni-list-item>
            </uni-list>
        </view>
        <!-- Footer:结算 -->
        <view class="footer">
            <SlSVgIcon name="20-20-28" size="20" />
            <text class="footer-total">合计:
                <text class="price">
                    ¥{{ totalPrice }}
                </text>
            </text>
            <button :class="{
                'checkout-button': true,
                'disabled': !canCheckout()
            }" @click="goToCheckout">去结算</button>
        </view>
    </view>
</template>
<script lang="ts" setup>
interface ProductItem {
    id: number;
    name: string;
    price: number;
    limit: number;
    image: string;
    count?: number; // 可选属性，用于记录购物车中的数量
}
import { ref } from 'vue';
const menuItems = ref<ProductItem[]>([
    {
        id: 1,
        name: '大干层馒头',
        price: 4,
        limit: 3,
        image: 'empty',
    },
    {
        id: 2,
        name: '玉米饼',
        price: 3,
        limit: 3,
        image: 'empty',
    },
    {
        id: 3,
        name: '长花卷',
        price: 3,
        limit: 3,
        image: 'empty',
    },
    {
        id: 4,
        name: '大葱花卷',
        price: 3,
        limit: 3,
        image: 'empty',
    },
    {
        id: 5,
        name: '大肉夹馍',
        price: 6,
        limit: 2,
        image: 'empty',
    },
    {
        id: 6,
        name: '大肉包子',
        price: 5,
        limit: 2,
        image: 'empty',
    },
    {
        id: 7,
        name: '大肉饼',
        price: 5,
        limit: 2,
        image: 'empty',
    },
    {
        id: 8,
        name: '大肉馅饼',
        price: 5,
        limit: 2,
        image: 'empty',
    },
    {
        id: 9,
        name: '大肉包',
        price: 5,
        limit: 2,
        image: 'empty',
    },
    {
        id: 10,
        name: '大肉馅饼',
        price: 5,
        limit: 2,
        image: 'empty',
    }
]);

const totalPrice = ref(0);
const cart = ref<ProductItem[]>([]);
// 从购物车减少商品数量
const removeFromCart = (item: ProductItem) => {
    if (!item.count || item.count <= 0) return;
    const index = cart.value.findIndex(cartItem => cartItem.id === item.id);
    if (index > -1) {
        item.count = item.count - 1;
        cart.value.splice(index, 1);
        totalPrice.value -= item.price;
    }
};

// 计算添加按钮是否禁用
const computedAddDisabled = (item: ProductItem) => {
    return item.count && item.count >= item.limit;
};

// 计算是否可以去结算
const canCheckout = () => {
    return cart.value.length > 0;
};


// 添加商品到购物车
const addToCart = (item: ProductItem) => {
    if (item.count && item.count >= item.limit) {
        // uni.showToast({
        //     title: `最多只能选择 ${item.limit} 份`,
        //     icon: 'none'
        // });
        return;
    } else {
        item.count = (item.count || 0) + 1;
    }
    cart.value.push(item);
    totalPrice.value += item.price;
};

const goToCheckout = () => {
    // 如果可以结算，跳转到结算页面
    if (canCheckout()) {
        uni.navigateTo({
            url: '/subpages/takeout/pages/staff/order'
        });
    }
};
</script>
<style lang="scss" scoped>
.order-page {
    display: flex;
    flex-direction: column;
    background: #F4F6FA;
}

.banner {
    position: relative;
    width: 100%;
}

.banner-image {
    width: 100%;
    height: 119px;
    object-fit: cover;
}

.header-time {
    text-align: center;
    height: 40px;
    padding: 0 19px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    color: #666666;
}

.menu-list {
    overflow: hidden;
    overflow-y: auto;
    background: #ffffff;
    max-width: 100%;
    height: calc(100vh - 119px - 50px - 40px);
    padding: 10px 15px;
    display: flex;
    flex-direction: column;
}

.menu-item {
    display: flex;
    margin-bottom: 15px;
    align-items: center;
    width: 100%;
}

.menu-item-img {
    width: 70px;
    height: 70px;
    margin-right: 10px;
}

.menu-item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.limit {
    color: #999999;
    font-size: 12px;
}

.price {
    color: #EE391C;
    font-weight: bold;
}

.menu-item-actions {
    display: flex;
    align-items: center;
}

.footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    height: 50px;
    background: #ffffff;
    z-index: 1;
    padding: 0 15px 0 16px;
    align-items: center;
}

sl-s-vg-icon {
    display: inline-flex;
}

.footer-total {
    flex: 1;
    color: #333;
    margin-left: 12px;
}

.checkout-button {
    width: 96px;
    height: 36px;
    color: #ffffff;
    border-radius: 45px;
    background-color: #3B6EEB;
    box-shadow: 0px 4px 10px 0px #3B6EEB4C;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    &.disabled {
        background-color: #999999;
    }
}
</style>