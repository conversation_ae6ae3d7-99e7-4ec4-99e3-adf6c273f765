<template>
    <SlTabbarPage :tab-bar-root="'subpages/takeout'">
        <!--外卖点餐首页-->
        <view class="order-page">
            <!-- Banner Section -->
            <view class="banner">
                <image src="../../static/banner.png" alt="Banner" class="banner-image" />
            </view>
            <!-- Header Section -->
            <view class="header-time">
                <text>取餐时间: 2025-06-03</text>
                <text>截止时间: 今天17:00</text>
            </view>
            <view class="menu-list">
                <uni-list>
                    <TakeoutProductListItem v-for="item in takeoutMenu.takeoutMenuDishList" :key="item.dishId"
                        :item="item" @add="addToCart" @remove="removeFromCart" />
                </uni-list>
            </view>
            <!-- Footer:订单结算 -->
            <TakeoutOrderCheck fixed :canCheckout="canCheckout" :totalPrice="totalPrice" @open="openCart" />
            <!-- 弹出层：已选购菜品列表 -->
            <SelectedOrderPopup v-model:show="showPopup" :canCheckout="canCheckout" :totalPrice="totalPrice"
                :cart="cart" :selectedIds="cart.map(item => item.dishId)" @clear="onClear" @add="addToCart"
                @remove="removeFromCart" />
        </view>
    </SlTabbarPage>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
import TakeoutProductListItem from '../../components/TakeoutProductListItem.vue'
import SelectedOrderPopup from '../../components/SelectedOrderPopup.vue';
import TakeoutOrderCheck from '../../components/TakeoutOrderCheck.vue';
import type { TakeoutProduct } from '../../models/Takeout'
import { takeoutMenuService } from '@/subpages/takeout/service'
// import { formatDate } from '@/subpages/takeout/utils';
import { TakeoutMenu } from '@/subpages/takeout/models';
const takeoutMenu = ref<TakeoutMenu>({ id: '', menuDate: '', takeoutMenuDishList: [] });
onLoad(async () => {
    // const curDate = formatDate()
    await getMenuListByDate('20250620');
})
const totalPrice = ref(0);
const showPopup = ref<boolean>(false)
// 购物车列表
const cart = ref<TakeoutProduct[]>([]);
// 获取指定日期的菜单列表
const getMenuListByDate = async (dateStr: string) => {
    takeoutMenu.value = await takeoutMenuService.getTakeoutMenuByDate(dateStr);
    console.log('takeoutMenu', takeoutMenu);
};
// 计算是否可以去结算
const canCheckout = computed(() => {
    // 如果购物车中有商品，则可以结算
    return cart.value.length > 0;
});
// 添加商品到购物车,子组件已经判断limitPerPerson
const addToCart = (item: TakeoutProduct) => {
    const index = getIndex(item);
    if (index == -1) {
        // 如果商品已存在于购物车中，增加数量
        cart.value.push(item);
    }
    // 更新总价
    totalPrice.value += item.price;
};

// 从购物车减少商品数量,子组件已经判断 quantity 会大于0
const removeFromCart = (item: TakeoutProduct) => {
    if (item.quantity == 0) {
        const index = getIndex(item);
        if (index !== -1) {
            // 如果商品数量为0，直接从购物车中移除
            cart.value.splice(index, 1);
        }
    }
};

const getIndex = (item: TakeoutProduct): number => {
    return cart.value.findIndex(cartItem => cartItem.dishId === item.dishId);
};

const openCart = () => {
    // 打开购物车
    showPopup.value = true;
};

// 清空购物车
const onClear = () => {
    cart.value = [];
    totalPrice.value = 0;
}

</script>
<style lang="scss" scoped>
.order-page {
    display: flex;
    flex-direction: column;
    background: #F4F6FA;
}

.banner {
    position: relative;
    width: 100%;
}

.banner-image {
    width: 100%;
    height: 119px;
    object-fit: cover;
}

.header-time {
    text-align: center;
    height: 40px;
    padding: 0 19px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    color: #666666;
}

.menu-list {
    overflow: hidden;
    overflow-y: auto;
    background: #ffffff;
    max-width: 100%;
    height: calc(100vh - 119px - 50px - 40px);
    padding: 10px 0;
    display: flex;
    flex-direction: column;
}
</style>