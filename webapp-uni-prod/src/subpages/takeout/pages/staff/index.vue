<template>
    <SlTabbarPage :tab-bar-root="'subpages/takeout'">
        <!--外卖点餐首页-->
        <view class="order-page">
            <!-- Banner Section -->
            <view class="banner">
                <image :src="appStore.getBaseUrl + '/takeout_banner.jpg'" alt="Banner" class="banner-image" />
            </view>
            <!-- Header Section -->
            <view class="header-time">
                <text>取餐时间: {{ takeoutDate }}</text>
                <text>截止时间: 今天 17:00</text>
            </view>
            <view class="menu-list" :class="{ 'flex-center': takeoutMenu.takeoutMenuDishList.length === 0 }">
                <uni-list v-if="takeoutMenu.takeoutMenuDishList.length > 0">
                    <TakeoutProductListItem v-for="item in takeoutMenu.takeoutMenuDishList" :key="item.dishId"
                        :item="item" />
                    <up-loadmore line status="nomore" v-if="takeoutMenu.takeoutMenuDishList.length > 3" />
                </uni-list>
                <TakeoutEmptyList :text="isTodayMenuEmpty ? '今日休息' : '暂无数据'" v-else show />
            </view>
            <!-- Footer:订单结算 -->
            <TakeoutOrderCheck @open="openCart" />
            <!-- 弹出层：已选购菜品列表 -->
            <SelectedOrderPopup v-model:show="showPopup" />
        </view>
    </SlTabbarPage>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
import SelectedOrderPopup from '@/subpages/takeout/components/SelectedOrderPopup.vue';
import TakeoutOrderCheck from '@/subpages/takeout/components/TakeoutOrderCheck.vue';
import TakeoutProductListItem from '@/subpages/takeout/components/TakeoutProductListItem.vue';
import TakeoutEmptyList from '@/subpages/takeout/components/TakeoutEmptyList.vue'
import { useTakeoutStore } from '@/subpages/takeout/store'
import { useAppStore } from "@/store";
const appStore = useAppStore();
const takeoutStore = useTakeoutStore()
const takeoutMenu = computed(() => {
    return takeoutStore.takeoutMenu;
});
// 取餐时间
const takeoutDate = computed(() => takeoutStore.getPickupDate)
// 
const isTodayMenuEmpty = computed(() => {
    return takeoutMenu.value.published && (!takeoutMenu.value.takeoutMenuDishList || takeoutMenu.value.takeoutMenuDishList.length === 0)
});

onLoad(async () => {
    await getMenuListByDate();
})
onPullDownRefresh(async () => {
    try {
        await getMenuListByDate();
    } finally {
        uni.stopPullDownRefresh();
    }
});
const showPopup = ref<boolean>(false)

// 获取指定日期的菜单列表
const getMenuListByDate = async () => {
    await takeoutStore.setTakeoutMenu();
};

const openCart = () => {
    // 打开购物车
    showPopup.value = true;
};

</script>
<style lang="scss" scoped>
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/flex.scss";
@import "@/subpages/takeout/styles/text.scss";

.order-page {
    background: $takeout-bg;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.banner {
    position: relative;
    width: 100%;
}

.banner-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.header-time {
    height: 30px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    color: #666666;
    padding: 0 15px;
}

.menu-list {
    overflow: hidden;
    overflow-y: auto;
    background: #ffffff;
    max-width: 100%;
    height: calc(100% - 120px - 50px - 40px); // banner + 取餐时间40 + 结算50
    padding: 10px 0;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}
</style>