<template>
    <view class="order-detail-container">
        <OrderDetailPage :item="takeoutOrder" />
    </view>
</template>
<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import OrderDetailPage from '@/subpages/takeout/components/order/OrderDetailPage.vue';
import { TakeoutOrderItem } from '@/subpages/takeout/models/Takeout';
import { TakeoutOrderListMock } from '@/subpages/takeout/mocks/OrderList.mock';
import { getStatusText, getStatusType } from '@/subpages/takeout/utils/order-status';
onLoad((options) => {
    // 页面加载时可以进行一些初始化操作
    console.log('Order Detail Page Loaded', options);
    const orderId = (<{ orderId: string }>options).orderId; // 获取传入的订单ID
    takeoutOrder.value = mockList.find(item => item.id === orderId) || mockList[0]; // 根据订单ID查找订单
    console.log('Selected Order:', takeoutOrder.value);
});
// 订单列表数据，实际应用中应从后端获取
const mockList: TakeoutOrderItem[] = TakeoutOrderListMock.map(item => {
    return {
        ...item,
        // 模拟数据中添加一个订单状态
        statusType: getStatusType(item.status),
        statusText: getStatusText(item.status)
    }
})
const takeoutOrder = ref<TakeoutOrderItem>(mockList[0]); // 选中的订单
</script>

<style scoped lang="scss"></style>