<template>
    <view class="order-detail-container">
        <OrderDetailPage :item="takeoutOrder" v-if="takeoutOrder" />
    </view>
</template>
<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import OrderDetailPage from '@/subpages/takeout/components/order/OrderDetailPage.vue';
import { takeoutOrderService } from "@/subpages/takeout/service"
import { TakeoutOrder } from '@/subpages/takeout/models';
onLoad(async (options) => {
    const orderId = (<{ orderId: string }>options).orderId; // 获取传入的订单ID
    if (orderId) {
        await getInfo(orderId);
    } else {
        console.error('No order ID provided');
    }
});

const takeoutOrder = ref<TakeoutOrder | undefined>(undefined); // 选中的订单

const getInfo = async (id: string) => {
    takeoutOrder.value = await takeoutOrderService.getTakeoutOrderInfo(id)
    console.log('订单详情', takeoutOrder.value);
}

</script>

<style scoped lang="scss"></style>