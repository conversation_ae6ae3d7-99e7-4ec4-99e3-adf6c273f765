<template>
    <!--自定义返回：订单详情页面返回到订单列表页面-->
    <SlTabbarPage :tab-bar-root="'subpages/takeout'" press-back-path="/subpages/takeout/pages/staff/order-list/index">
        <view class="order-detail-container">
            <OrderDetailPage :item="takeoutOrder" v-if="takeoutOrder" @update:type="onTypeChange" />
            <up-modal class="custom-tip-modal" :show="true" v-if="showModal" :showCancelButton="false"
                :showConfirmButton="false">
                <view class="modal-content">
                    <view class="tip-title flex-center">
                        <SlSubSvgIcon class="inline-flex" subpage="takeout" name="16-16-7" size="16" />
                        <text class="takeout-danger-text">提示</text>
                    </view>
                    <view class="tip-content flex-col-center-x">
                        {{ curHandleType === 'delete' ? '删除订单后无法恢复，请慎重考虑' : '当前订单未支付，您确定要取消该订单吗？' }}
                    </view>
                    <view class="modal-footer flex-center-y">
                        <view @tap="curHandleType == 'cancel' ? handleConfirm() : cancel()"
                            class="takeout-btn btn-border-unset btn-h-40 cancel takeout-secondary-text">
                            {{ curHandleType == 'cancel' ? '确定' : '取消' }}
                        </view>
                        <view @tap="curHandleType == 'cancel' ? cancel() : handleConfirm()"
                            class="takeout-btn btn-border-unset btn-h-40 confirm takeout-primary-text takeout-blod-text">
                            {{ curHandleType == 'cancel' ? '再想想' : '确定' }}
                        </view>
                    </view>
                </view>
            </up-modal>
        </view>
    </SlTabbarPage>
</template>
<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import OrderDetailPage from '@/subpages/takeout/components/order/OrderDetailPage.vue';
import { takeoutOrderService } from "@/subpages/takeout/service"
import { OrderChangeType, TakeoutOrder } from '@/subpages/takeout/models';
const curHandleType = ref<OrderChangeType | undefined>(undefined); // 当前处理类型 delete or cancel
onLoad(async (options) => {
    const orderId = (<{ orderId: string }>options).orderId; // 获取传入的订单ID
    if (orderId) {
        await getInfo(orderId);
    }
});

const takeoutOrder = ref<TakeoutOrder | undefined>(undefined); // 选中的订单

const showModal = computed(() => {
    return curHandleType.value === 'cancel' || curHandleType.value === 'delete'
})

const getInfo = async (id: string) => {
    takeoutOrder.value = await takeoutOrderService.getTakeoutOrderInfo(id)
}

const cancel = () => {
    curHandleType.value = undefined; // 重置当前处理类型
}
const onTypeChange = (type: OrderChangeType) => {
    curHandleType.value = type;
}
const handleConfirm = async () => {
    if (!curHandleType.value || !takeoutOrder.value) {
        console.warn('当前没有处理类型或订单');
        return;
    }
    const orderId = takeoutOrder.value.id;
    if (curHandleType.value === 'delete') {
        await takeoutOrderService.deleteCompletedOrder(orderId!);
        cancel();
        setTimeout(() => {
            goToList();
        }, 500); // 延时500毫秒后返回上一页
    } else if (curHandleType.value === 'cancel') {
        await takeoutOrderService.cancelUnpaidOrder(orderId!);
        cancel();
        setTimeout(async () => {
            await getInfo(orderId!); // 刷新订单详情
        }, 500); // 延时500毫秒后返回上一页
    }
}

const goToList = () => {
    uni.navigateTo({
        url: `/subpages/takeout/pages/staff/order-list/index`
    });
}

</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/flex.scss";
@import "@/subpages/takeout/styles/modal.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/text.scss";
</style>