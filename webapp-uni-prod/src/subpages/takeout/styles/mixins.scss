@mixin image-wrapper($size, $radius) {
  width: $size;
  height: $size;
  border-radius: $radius;
  .image-item {
    width: 100%;
    height: 100%;
    border-radius: $radius;
    overflow: hidden;
  }
}

.product-image-wrapper {
  @include image-wrapper($takeout-product-img-size, $takeout-product-img-radis);
}

.order-image-wrapper {
  @include image-wrapper($takeout-order-img-width, $takeout-product-img-radis);
}

.img-round-5 {
  border-radius: 5px;
}

.mg-r-20 {
  margin-right: 20px;
}


.mg-r-10 {
  margin-right: 10px;
}
