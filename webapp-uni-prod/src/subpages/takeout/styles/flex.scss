.inline-flex {
  display: inline-flex;
}
/* 水平居中（主轴居中） */
.flex-center-x {
  display: flex;
  justify-content: center;
}

/* 垂直居中（交叉轴居中） */
.flex-center-y {
  display: flex;
  align-items: center;
}

/* 水平+垂直居中 */
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 水平两端对齐且垂直居中 */
.flex-between-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 垂直方向排列并水平居中 */
.flex-col-center-x {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 垂直方向排列并垂直居中 */
.flex-col-center-y {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 垂直方向排列并水平+垂直居中 */
.flex-col-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* 垂直方向排列且两端对齐 */
.flex-col-between {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
