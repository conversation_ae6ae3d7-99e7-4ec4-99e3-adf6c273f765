import type { WxPayReqParam } from "../models";
import { takeoutPayService } from "../service";
import { usePrincipalStore } from "@/store";
export function usePay() {
  const principalStore = usePrincipalStore();
  async function prepayWechatPay(orderId: string) {
    // 检查用户是否登录
    const account = await principalStore.identity();
    if (!account) {
      uni.showToast({
        title: "请先登录",
        icon: "none",
      });
    }
    const openid = account?.wechatOpenId;
    // 检查传入的参数
    if (!orderId || !openid) {
      uni.showToast({
        title: "参数错误",
        icon: "none",
      });
      console.error(`参数错误: account=`, account);
      console.error(`参数错误: openid=${openid}, orderId=${orderId}`);
      throw new Error("参数错误: openid 和 orderId 必须提供");
    }
    const wxPayParams = await takeoutPayService.prepayWechatPay({
      openid,
      outTradeNo: orderId,
    });
    // 处理微信支付参数
    // 注意：uni.requestPayment 需要在微信小程序环境中调用
    // 此处res 返回的具体数据请查看微信文档
    const res: any = await requestPayment(wxPayParams);
    if (res.errMsg === "requestPayment:ok") {
      return res;
    } else {
      uni.showToast({
        title: `支付失败`,
        icon: "none",
      });
      // 支付失败
      throw new Error(`支付失败: ${res.errMsg}`);
    }
  }
  async function requestPayment(params: WxPayReqParam) {
    // 微信支付参数
    return new Promise((resolve, reject) => {
      const { appId, nonceStr, packageStr, paySign, signType, timeStamp } =
        params;
      uni.requestPayment({
        provider: "wxpay",
        timeStamp,
        nonceStr,
        package: packageStr,
        signType,
        paySign,
        appId,
        // 订单信息可以根据实际情况传入
        orderInfo: {},
        success: (res) => {
          resolve(res);
        },
        fail: (err) => {
          console.error("fail:" + JSON.stringify(err));
          reject(err);
        },
      });
    });
  }
  return {
    wechatPay: prepayWechatPay,
  };
}
