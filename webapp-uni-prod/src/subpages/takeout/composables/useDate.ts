import dayjs from "dayjs";
/**
 * 提供日期格式化相关方法，基于 dayjs
 */
export function useDate() {
  // 当前日期，格式为 'YYYY-MM-DD' ，日历组件格式
  const curDate = dayjs().format("YYYY-MM-DD");
  // 后端需要的日期格式 'YYYYMMDD'
  const backCurDate = dayjs().format("YYYYMMDD");

  /**
   * 格式化取餐时间：后端返回 'yyyy-MM-dd HH:mm:ss'
   * @param time - 完整时间字符串
   * @returns 'YYYY-MM-DD'
   */
  function formatPickupTime(time: string): string {
    return time ? time.split(" ")[0] : "";
  }
  return {
    formatPickupTime,
    curDate,
    backCurDate,
  };
}
