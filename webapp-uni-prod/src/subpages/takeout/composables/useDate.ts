// src/composables/useDate.ts
import dayjs from "dayjs";
/**
 * 提供日期格式化相关方法，基于 dayjs
 */
export function useDateUtils() {
  // 当前日期，格式为 'YYYY-MM-DD' ，日历组件格式
  const curDate = dayjs().format("YYYY-MM-DD");
  // 后端需要的日期格式 'YYYYMMDD'
  const backCurDate = dayjs().format("YYYYMMDD");
  /**
   * 格式化日期
   * @param date - Date 对象或可解析的日期字符串，默认当前时间
   * @param format  - 可选的格式化字符串,默认返回 'YYYYMMDD'
   * @returns 对应格式的字符串，如 '20250626' 或 '2025-06-26'
   */
  function formatDate(
    date: Date | string = new Date(),
    format?: string
  ): string {
    const d = dayjs(date);
    // 如果用户传了类似 'yyyy-MM-dd' 就转换成 'YYYY-MM-DD'
    if (format) {
      return d.format(format);
    }
    // 默认输出 'YYYYMMDD' 接口需要
    return d.format("YYYYMMDD");
  }

  /**
   * 格式化取餐时间：后端返回 'yyyy-MM-dd HH:mm:ss'
   * @param time - 完整时间字符串
   * @returns 'YYYY-MM-DD'
   */
  function formatPickupTime(time: string): string {
    return time ? time.split(" ")[0] : "";
  }

  return {
    formatDate,
    formatPickupTime,
    curDate,
    backCurDate,
  };
}
