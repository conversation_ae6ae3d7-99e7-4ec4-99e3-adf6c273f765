// /src/composables/useCountDown.ts
import { ref, computed, onUnmounted } from "vue";

/**
 * 用于倒计时的 composable
 * @param initialSeconds 初始剩余秒数
 */
export function useCountDown(initialSeconds: number) {
  // 当前剩余秒数
  const remaining = ref(initialSeconds);
  // 格式化后的字符串 "MM:SS"
  const display = computed(() => {
    const m = Math.floor(remaining.value / 60);
    const s = remaining.value % 60;
    return `${String(m).padStart(2, "0")}:${String(s).padStart(2, "0")}`;
  });

  let timer: ReturnType<typeof setInterval> | null = null;

  // 启动倒计时
  function start(seconds = initialSeconds) {
    // 如果传入新的 seconds，就重置
    if (seconds !== remaining.value) {
      remaining.value = seconds;
    }
    stop();
    timer = setInterval(() => {
      if (remaining.value > 0) {
        remaining.value--;
      } else {
        stop();
      }
    }, 1000);
  }

  // 停止倒计时
  function stop() {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  }

  // 组件卸载时清理定时器
  onUnmounted(() => {
    stop();
  });

  return { remaining, display, start, stop };
}
