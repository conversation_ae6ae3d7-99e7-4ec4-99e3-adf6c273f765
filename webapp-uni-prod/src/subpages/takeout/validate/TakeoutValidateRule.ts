export interface TakeoutValidateRule {
  // 是否必填
  required?: boolean;
  // 错误提示信息
  message?: string;
  // 字符串类型
  type?: string;
  pattern?: RegExp;
  // 最小长度
  min?: number;
  // 最大值
  max?: number;
  // 自定义验证函数
  validator?: (value: any) => boolean | Promise<boolean>;
}

export interface TakeoutValidateRuleMap {
  [key: string]: TakeoutValidateRule;
}

/**
 * takeout 菜品验证规则
 */
const dishValidateRule: TakeoutValidateRuleMap = {
  dishName: { required: true, message: "菜名不能为空" },
  portionUnit: { required: true, message: "分量不能为空" },
  price: { required: true, message: "价格不能为空" },
  limitPerPerson: { required: true, message: "限购数量不能为空" },
};

export default {
  dishValidateRule,
};
