import { TakeoutOrderStatus, TakeoutOrderStatusType } from "../models";
import { TakeoutOrderStatusList } from "../models/Takeout.const";

export const getStatusType = (
  status: TakeoutOrderStatus
): TakeoutOrderStatusType => {
  const statusItem = TakeoutOrderStatusList.find(
    (item) => item.value === status
  );
  return statusItem ? statusItem.key : "ALL";
};

export const getStatusText = (status: TakeoutOrderStatus): string => {
  const statusItem = TakeoutOrderStatusList.find(
    (item) => item.value === status
  );
  return statusItem ? statusItem.text : "未知状态";
};
