import { TakeoutValidateRule } from "../validate/TakeoutValidateRule";

export const validateRequired = (value: any, message: string): boolean => {
  if (value === undefined || value === null || value === "") {
    throw new Error(message);
  }
  return true;
};

export const validateType = (
  value: any,
  type: string,
  message: string
): boolean => {
  if (typeof value !== type) {
    throw new Error(message);
  }
  return true;
};

export const validatePattern = (
  value: any,
  pattern: RegExp,
  message: string
): boolean => {
  if (!pattern.test(value)) {
    throw new Error(message);
  }
  return true;
};

export const validateMin = (
  value: any,
  min: number,
  message: string
): boolean => {
  if (value < min) {
    throw new Error(message);
  }
  return true;
};

export const validateMax = (
  value: any,
  max: number,
  message: string
): boolean => {
  if (value > max) {
    throw new Error(message);
  }
  return true;
};

export const validateCustom = (
  value: any,
  validator: (value: any) => boolean | Promise<boolean>,
  message: string
): Promise<void> => {
  return new Promise((resolve, reject) => {
    const result = validator(value);
    if (result instanceof Promise) {
      result.then(() => resolve()).catch(() => reject(new Error(message)));
    } else if (!result) {
      reject(new Error(message));
    } else {
      resolve();
    }
  });
};

/**
 * 校验函数
 * @param value
 * @param rules
 * @returns
 */
export const validatorFn = (
  value: any,
  rules: TakeoutValidateRule
): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      if (rules.required) {
        validateRequired(value, rules.message || "This field is required.");
      }
      if (rules.type) {
        validateType(value, rules.type, rules.message || `Invalid type.`);
      }
      if (rules.pattern) {
        validatePattern(
          value,
          rules.pattern,
          rules.message || "Invalid format."
        );
      }
      if (rules.min !== undefined) {
        validateMin(
          value,
          rules.min,
          rules.message || `Value must be at least ${rules.min}.`
        );
      }
      if (rules.max !== undefined) {
        validateMax(
          value,
          rules.max,
          rules.message || `Value must not exceed ${rules.max}.`
        );
      }
      if (rules.validator) {
        validateCustom(
          value,
          rules.validator,
          rules.message || "Validation failed."
        );
      }
      resolve();
    } catch (error) {
      reject(error);
    }
  });
};
