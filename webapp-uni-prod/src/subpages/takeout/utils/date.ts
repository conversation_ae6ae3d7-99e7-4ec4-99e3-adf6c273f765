/**
 * 格式化日期
 * @param date - 日期对象或日期字符串
 * @return 格式化后的日期字符串
 * @param format - 可选的格式化字符串，默认  yyyyMMdd ,支持 yyyy-MM-dd 格式
 */
export const formatDate = (date?: Date | string, format?: string): string => {
  date = date || new Date();
  const d = typeof date === "string" ? new Date(date) : date;
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  if (format && format.includes("yyyy-MM-dd")) {
    return `${year}-${month}-${day}`;
  }

  return `${year}${month}${day}`;
};

/**
 * 格式化取餐时间：后端返回的格式 为 yyyy-MM-dd HH:mm:ss
 * @description 格式化取餐时间为 yyyy-MM-dd 格式
 * @param time
 * @returns
 */
export const formatPickupTime = (time: string): string => {
  return time ? time.split(" ")[0] : "";
};
