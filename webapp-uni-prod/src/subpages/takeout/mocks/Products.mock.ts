import { TakeoutProduct } from "../models/Takeout";

export const ProductsMock: TakeoutProduct[] = [
  {
    dishId: "1",
    dishName: "大千层馒头",
    quantity: 1,
    price: 3,
    portionUnit: 2,
    limitPerPerson: 1,
  },
  {
    dishId: "2",
    dishName: "玉米饼",
    quantity: 2,
    price: 4,
    portionUnit: 2,
    limitPerPerson: 3,
  },
  {
    dishId: "3",
    dishName: "手擀面",
    quantity: 1,
    price: 5,
    portionUnit: 2,
    limitPerPerson: 1,
  },
  {
    dishId: "4",
    dishName: "刀削面",
    quantity: 2,
    price: 6,
    portionUnit: 2,
    limitPerPerson: 2,
  },
  {
    dishId: "5",
    dishName: "拉面",
    quantity: 3,
    price: 7,
    portionUnit: 2,
    limitPerPerson: 3,
  },
  {
    dishId: "6",
    dishName: "饺子",
    quantity: 1,
    price: 8,
    portionUnit: 2,
    limitPerPerson: 1,
  },
  {
    dishId: "7",
    dishName: "包子",
    quantity: 2,
    price: 9,
    portionUnit: 2,
    limitPerPerson: 2,
  },
];
