// import { TakeoutOrderItem, TakeoutOrderStatus } from "../models/Takeout";

// /**
//  * status: TakeoutOrderStatus;
//   orderDate: string;
//   userId: string;
//   productList: TakeoutProduct[];
//   takeoutNumber?: string;
//   id: string;
//  */
// export const TakeoutOrderListMock: Array<TakeoutOrderItem> = [
//   {
//     id: "order1",
//     username: "张三",
//     phoneNumber: "1345678901",
//     status: TakeoutOrderStatus.UNPAID,
//     orderDate: "2023-10-01",
//     userId: "user123",
//     orderNumber: "202310010001",
//     productList: [
//       {
//         dishId: "dish1",
//         dishName: "花卷",
//         price: 2,
//         portionUnit: 1,
//         limitPerPerson: 2,
//         imageId: "image1",
//         quantity: 1,
//       },
//       {
//         dishId: "dish2",
//         dishName: "肉夹馍",
//         price: 5,
//         portionUnit: 1,
//         limitPerPerson: 1,
//         imageId: "image2",
//         quantity: 2,
//       },
//       {
//         dishId: "dish5",
//         dishName: "刀削面",
//         price: 8,
//         portionUnit: 1,
//         limitPerPerson: 1,
//         imageId: "image5",
//         quantity: 1,
//       },
//       {
//         dishId: "dish6",
//         dishName: "凉皮",
//         price: 2,
//         portionUnit: 1,
//         limitPerPerson: 3,
//         imageId: "image6",
//         quantity: 2,
//       },
//       {
//         dishId: "dish10",
//         dishName: "包子",
//         price: 10,
//         portionUnit: 1,
//         limitPerPerson: 1,
//         imageId: "image10",
//         quantity: 1,
//       },
//     ],
//     takeoutNumber: "",
//     takeoutTime: "2026-06-01",
//   },
//   {
//     id: "order2",
//     username: "张三",
//     phoneNumber: "1345678901",
//     status: TakeoutOrderStatus.PENDING,
//     orderNumber: "202310020002",
//     orderDate: "2023-10-02",
//     userId: "user456",
//     takeoutNumber: "待放置-001",
//     takeoutTime: "2026-06-03",
//     productList: [
//       {
//         dishId: "dish3",
//         dishName: "包子",
//         price: 4,
//         portionUnit: 1,
//         limitPerPerson: 2,
//         imageId: "image3",
//         quantity: 1,
//       },
//       {
//         dishId: "dish4",
//         dishName: "饺子",
//         price: 6,
//         portionUnit: 1,
//         limitPerPerson: 2,
//         imageId: "image4",
//         quantity: 3,
//       },
//     ],
//   },
//   {
//     id: "order3",
//     username: "张三",
//     phoneNumber: "1345678901",
//     status: TakeoutOrderStatus.COMPLETED,
//     orderNumber: "202310030003",
//     orderDate: "2023-10-03",
//     userId: "user789",
//     takeoutNumber: "L1-002",
//     takeoutTime: "2026-06-01",
//     productList: [
//       {
//         dishId: "dish7",
//         dishName: "大千层馒头",
//         price: 6,
//         portionUnit: 1,
//         limitPerPerson: 1,
//         imageId: "image7",
//         quantity: 1,
//       },
//       {
//         dishId: "dish8",
//         dishName: "馒头",
//         price: 2,
//         portionUnit: 1,
//         limitPerPerson: 1,
//         imageId: "image8",
//         quantity: 2,
//       },
//     ],
//   },
//   {
//     id: "order33",
//     username: "张三2",
//     phoneNumber: "1345678902",
//     status: TakeoutOrderStatus.COMPLETED,
//     orderNumber: "202310030003",
//     orderDate: "2023-10-03",
//     userId: "user789",
//     takeoutNumber: "L1-003",
//     takeoutTime: "2026-06-01",
//     productList: [
//       {
//         dishId: "dish7",
//         dishName: "大千层馒头",
//         price: 6,
//         portionUnit: 1,
//         limitPerPerson: 1,
//         imageId: "image7",
//         quantity: 1,
//       },
//     ],
//   },
//   {
//     id: "order",
//     username: "张三",
//     phoneNumber: "1345678901",
//     status: TakeoutOrderStatus.CANCELLED,
//     orderNumber: "202310040004",
//     orderDate: "2023-10-04",
//     userId: "user101112",
//     takeoutNumber: "",
//     takeoutTime: "2026-06-01",
//     productList: [
//       {
//         dishId: "dish9",
//         dishName: "包子",
//         price: 5,
//         portionUnit: 1,
//         limitPerPerson: 2,
//         imageId: "image9",
//         quantity: 1,
//       },
//     ],
//   },
// ];
