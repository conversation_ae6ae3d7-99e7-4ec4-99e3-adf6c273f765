// /**外卖管理端订单列表 */

// import {
//   TakeoutAdminOrderItem,
//   TakeoutAdminOrderProduct,
//   TakeoutOrderStatus,
// } from "../models/Takeout";

// export const AdminOrderProductList: Array<TakeoutAdminOrderProduct> = [
//   {
//     id: "1",
//     dishId: "1",
//     dishName: "大千层馒头",
//     quantity: 10,
//     price: 3,
//     portionUnit: 2,
//     limitPerPerson: 1,
//   },
//   {
//     id: "2",
//     dishId: "2",
//     dishName: "玉米饼",
//     quantity: 29,
//     price: 4,
//     portionUnit: 2,
//     limitPerPerson: 3,
//   },
//   {
//     id: "3",
//     dishId: "3",
//     dishName: "手擀面",
//     quantity: 81,
//     price: 5,
//     portionUnit: 2,
//     limitPerPerson: 1,
//   },
//   {
//     id: "4",
//     dishId: "4",
//     dishName: "刀削面",
//     quantity: 28,
//     price: 6,
//     portionUnit: 2,
//     limitPerPerson: 2,
//   },
//   {
//     id: "5",
//     dishId: "5",
//     dishName: "拉面",
//     quantity: 83,
//     price: 7,
//     portionUnit: 2,
//     limitPerPerson: 3,
//   },
//   {
//     id: "6",
//     dishId: "6",
//     dishName: "饺子",
//     quantity: 18,
//     price: 8,
//     portionUnit: 2,
//     limitPerPerson: 1,
//   },
//   {
//     id: "7",
//     dishId: "7",
//     dishName: "包子",
//     quantity: 82,
//     price: 9,
//     portionUnit: 2,
//     limitPerPerson: 2,
//   },
// ];

// export const AdminOrderList: Array<TakeoutAdminOrderItem> = [
//   {
//     id: "order1",
//     username: "张三",
//     phoneNumber: "1345678901",
//     status: TakeoutOrderStatus.PENDING,
//     orderDate: "2023-10-01",
//     userId: "user123",
//     takeoutNumber: "L1-003",
//     takeoutTime: "2026-06-01",
//     orderNumber: "202310010001",
//     productList: [
//       {
//         dishId: "dish1",
//         dishName: "花卷",
//         price: 2,
//         portionUnit: 1,
//         limitPerPerson: 2,
//         imageId: "image1",
//         quantity: 1,
//       },
//       {
//         dishId: "dish2",
//         dishName: "肉夹馍",
//         price: 5,
//         portionUnit: 1,
//         limitPerPerson: 1,
//         imageId: "image2",
//         quantity: 2,
//       },
//       {
//         dishId: "dish5",
//         dishName: "刀削面",
//         price: 8,
//         portionUnit: 1,
//         limitPerPerson: 1,
//         imageId: "image5",
//         quantity: 1,
//       },
//       {
//         dishId: "dish6",
//         dishName: "凉皮",
//         price: 2,
//         portionUnit: 1,
//         limitPerPerson: 3,
//         imageId: "image6",
//         quantity: 2,
//       },
//     ],
//   },
//   {
//     id: "order2",
//     username: "赵四",
//     phoneNumber: "1345678901",
//     status: TakeoutOrderStatus.PENDING,
//     orderNumber: "202310020002",
//     orderDate: "2023-10-02",
//     userId: "user456",
//     takeoutNumber: "L1-001",
//     takeoutTime: "2026-06-03",
//     productList: [
//       {
//         dishId: "dish3",
//         dishName: "包子",
//         price: 4,
//         portionUnit: 1,
//         limitPerPerson: 2,
//         imageId: "image3",
//         quantity: 1,
//       },
//       {
//         dishId: "dish4",
//         dishName: "饺子",
//         price: 6,
//         portionUnit: 1,
//         limitPerPerson: 2,
//         imageId: "image4",
//         quantity: 3,
//       },
//     ],
//   },
//   {
//     id: "order3",
//     username: "王五",
//     phoneNumber: "1345678901",
//     status: TakeoutOrderStatus.PENDING,
//     orderNumber: "202310030003",
//     orderDate: "2023-10-03",
//     userId: "user789",
//     takeoutNumber: "L1-002",
//     takeoutTime: "2026-06-01",
//     productList: [
//       {
//         dishId: "dish7",
//         dishName: "大千层馒头",
//         price: 6,
//         portionUnit: 1,
//         limitPerPerson: 1,
//         imageId: "image7",
//         quantity: 1,
//       },
//       {
//         dishId: "dish8",
//         dishName: "馒头",
//         price: 2,
//         portionUnit: 1,
//         limitPerPerson: 1,
//         imageId: "image8",
//         quantity: 2,
//       },
//     ],
//   },
//   {
//     id: "order4",
//     username: "李四",
//     phoneNumber: "1345678901",
//     status: TakeoutOrderStatus.PENDING,
//     orderNumber: "202310040004",
//     orderDate: "2023-10-04",
//     userId: "user101112",
//     takeoutNumber: "L5-001",
//     takeoutTime: "2026-06-01",
//     productList: [
//       {
//         dishId: "dish9",
//         dishName: "包子",
//         price: 5,
//         portionUnit: 1,
//         limitPerPerson: 2,
//         imageId: "image9",
//         quantity: 1,
//       },
//     ],
//   },
// ];
