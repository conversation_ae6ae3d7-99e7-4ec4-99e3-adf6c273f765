<template>
    <!-- 底部结算 -->
    <view class="takeout-order-check" :class="{ 'fixed': props.fixed }">
        <SlSubSvgIcon class="inline-flex" subpage="takeout" :name="canCheckout ? '20-20-27' : '20-20-28'" size="20" @click="openCart" />
        <view class="footer-total">
            <text>合计:</text>
            <text class="price">
                ¥{{ totalPrice }}
            </text>
        </view>
        <button :class="{
            'checkout-button': true,
            'disabled': !canCheckout
        }" @click="goToCheckout">去结算</button>
    </view>
</template>

<script lang="ts" setup>
const props = defineProps<{
    canCheckout: boolean;
    totalPrice: number;
    fixed?: boolean; // 是否固定在底部
}>();

const emit = defineEmits<{
    (e: 'open'): void;
}>();

const goToCheckout = () => {
    // 如果可以结算，跳转到结算页面
    if (props.canCheckout) {
        uni.navigateTo({
            url: '/subpages/takeout/pages/staff/checkout/index',
        });
    }
};
const openCart = () => {
    // 打开购物车
    emit('open');
};
</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
// 底部已选菜品
.takeout-order-check {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    height: 50px;
    background: #ffffff;
    z-index: 1;
    padding: 0 15px;
    align-items: center;
    border-radius: 10px 10px 0px 0px;
    background: #FFFFFF;
    box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);

    &.fixed {
        position: fixed;
        bottom: 0;
    }
}

.footer-total {
    flex: 1;
    margin-left: 15px;
    display: inline-flex;
    .price {
        color: $takeout-price-color;
        font-weight: 700;
    }
}

.checkout-button {
    width: 96px;
    height: 36px;
    flex: 0 0 auto;
    color: #ffffff;
    border-radius: 45px;
    background-color: #3B6EEB;
    box-shadow: 0px 4px 10px 0px rgba(59, 110, 235, 0.3);
    display: inline-flex;
    align-items: center;
    justify-content: center;

    &.disabled {
        background-color: #999999;
    }
}
</style>
