<template>
    <!-- 底部结算 -->
    <view class="takeout-order-check" :class="{ 'fixed': props.fixed }">
        <SlSubSvgIcon class="inline-flex" subpage="takeout" :name="canCheckout ? '20-20-27' : '20-20-28'" size="20"
            @click="openCart" />
        <view class="footer-total">
            <text>合计:</text>
            <text class="price">
                ¥{{ totalPrice }}
            </text>
        </view>
        <button :class="{
            'checkout-button': true,
            'disabled': !canCheckout
        }" @tap="goToCheckout">去结算</button>
    </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useTakeoutStore } from '@/store';
const takeoutStore = useTakeoutStore();

const props = defineProps<{
    fixed?: boolean; // 是否固定在底部
}>();

const emit = defineEmits<{
    (e: 'open'): void;
}>();

const totalPrice = computed(() => {
    return takeoutStore.totalPrice;
})

// 计算是否可以结算
const canCheckout = computed(() => {
    // 如果购物车有菜品，则可以结算
    return takeoutStore.cart.length > 0 && isBefore17.value;
});

// 计算是否在17点之前
const isBefore17 = computed(() => {
    return new Date().getHours() <= 17;
});

const goToCheckout = async () => {
    // 如果可以结算，跳转到结算页面
    if (canCheckout.value) {
        //下单， 生成当前订单
        await takeoutStore.genCurrentOrder()
        // 下单成功跳转结算页面
        uni.navigateTo({
            url: '/subpages/takeout/pages/staff/checkout/index',
        });
    } else {
        if (!isBefore17.value) {
            uni.showToast({
                title: '17:00 点后无法下单',
                icon: 'none',
                duration: 2000
            });
        }
    }
};
const openCart = () => {
    // 打开购物车
    emit('open');
};
</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/flex.scss";

// 底部已选菜品
.takeout-order-check {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    height: 50px;
    background: #ffffff;
    z-index: 1;
    padding: 0 15px;
    align-items: center;
    border-radius: 10px 10px 0px 0px;
    background: #FFFFFF;
    box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);

    &.fixed {
        position: fixed;
        bottom: 0;
    }
}

.footer-total {
    flex: 1;
    margin-left: 15px;
    display: inline-flex;

    .price {
        color: $takeout-price-color;
        font-weight: 700;
    }
}

.checkout-button {
    width: 96px;
    height: 36px;
    flex: 0 0 auto;
    color: #ffffff;
    border-radius: 45px;
    background-color: #3B6EEB;
    box-shadow: 0px 4px 10px 0px rgba(59, 110, 235, 0.3);
    display: inline-flex;
    align-items: center;
    justify-content: center;

    &.disabled {
        background-color: #999999;
    }
}
</style>
