<template>
    <!--订单详情item-->
    <view class="order-detail-card takeout-card">
        <view class="order-header flex-between-center">
            <view class="user-info flex-center-y">
                <SlSubSvgIcon class="inline-flex" subpage="takeout" name="16-16-29" size="16" />
                <text class="username takeout-blod-text">{{ item.username }}</text>
            </view>
            <view class="takeout-number takeout-blod-text">
                <text class="label">取餐号：</text>
                <text class="value takeout-primary-text">
                    {{ item.takeoutNumber }}
                </text>
            </view>
        </view>
        <OrderProductList :orderProductItems="item.productList" isOrderList />
        <view class="order-details">
            <view class="item flex-between-center">
                <text class="label">订餐人：</text>
                <text class="value">{{ item.username }}</text>
            </view>
            <view class="item flex-between-center">
                <text class="label">手机号码：</text>
                <text class="value">{{ item.phoneNumber }}</text>
            </view>
            <view class="item flex-between-center">
                <text class="label">订单号：</text>
                <text class="value">{{ item.orderNumber }}</text>
            </view>
            <view class="item flex-between-center">
                <text class="label">订单时间：</text>
                <text class="value">{{ item.orderDate }}</text>
            </view>
        </view>
        <view class="order-actions flex-between-center">
            <view class="label">
                <text class="takeout-required-text">*</text>
                <text class="takeout-sm-text">取餐柜</text>
            </view>
            <up-select v-model:current="curabinetId" :label="curabinetLabel" :options="cabinetList"
                @select="selectCabinet"></up-select>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { TakeoutAdminOrderItem } from '../../models/Takeout';
import OrderProductList from '../order/OrderProductList.vue';

defineProps<{
    item: TakeoutAdminOrderItem;
}>();
const curabinetId = ref<string>(''); // 默认补选中
const curabinetLabel = ref<string>('请选择'); // 默认补选中
const cabinetList = ref([
    { name: 'L1', id: '1' },
    { name: 'L2', id: '2' },
    { name: 'L3', id: '3' },
    { name: 'L4', id: '4' },
    { name: 'L5', id: '5' },
]);
const selectCabinet = (value: { name: string; id: string }) => {
    curabinetId.value = value.id;
    curabinetLabel.value = value.name;
    // 这里可以添加逻辑处理选中柜子的操作
    console.log('Selected cabinet:', value);
};

</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/flex.scss";
@import "@/subpages/takeout/styles/card.scss";
@import "@/subpages/takeout/styles/text.scss";

.order-detail-card {
    width: 100%;
    padding: 10px 0;
    position: relative;

    .order-header {
        padding: 0 15px;

        .user-info>.username {
            margin-left: 8px;
        }
    }

    .order-details {
        display: flex;
        flex-direction: column;
        width: 100%;

        .item {
            padding: 0 15px;

            .label,
            .value {
                font-size: $uni-font-size-sm;
                color: $uni-text-color-secondary;
            }
        }
    }

    .order-actions {
        height: $takeout-h-40;
        padding: 0 15px;
    }
}
</style>