<template>
    <!--外卖订单card item-->
    <view class="product-card takeout-card">
        <view class="product-info flex-center-y">
            <view class="image-container">
                <!-- 商品图片 -->
                <image :src="item.imageUrl" class="item-image" />
            </view>
            <view class="info-container flex-col-between">
                <view class="product-name text-ellipsis">
                    {{ item.dishName }}
                    {{ item.portionUnit > 0 ? '(' + item.portionUnit + '个/份)' : '' }}
                </view>
                <view class="product-price flex-between-center">
                    <text class="price">
                        ￥ {{ item.price }}
                    </text>
                    <text class="limit">
                        限购{{ item.limitPerPerson }}份/人
                    </text>
                </view>
            </view>
        </view>
        <view class="product-count flex-center-y">
            <div class="labele">已订购数量</div>
            <view class="count">
                20
            </view>
        </view>
    </view>
</template>
<script lang="ts" setup>
import { TakeoutMenuDish } from '@/subpages/takeout/models';
defineProps<{
    item: TakeoutMenuDish;
}>();
</script>
<style lang="scss" scoped>
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/flex.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/card.scss";

.product-card {


    .product-info {
        height: 90px;
        padding: 0 15px;

        .image-container {
            width: $takeout-product-img-size;
            height: $takeout-product-img-size;
            border-radius: $takeout-product-img-radis;
            flex: 0 0 auto;

            .item-image {
                width: 100%;
                height: 100%;
            }
        }

        .info-container {
            height: $takeout-product-img-size;
            flex: 1;
            width: 0;
            padding-left: 20px;

            .product-price {
                .price {
                    font-size: $uni-font-size-sm;
                    font-weight: bold;
                    color: $takeout-price-color;
                }

                .limit {
                    font-size: $uni-font-size-sm;
                    color: $uni-text-color-secondary;
                }
            }
        }
    }

    .product-count {
        height: 40px;
        border-top: $takeout-border-style;
        padding: 0 15px;

        .labele {
            color: $uni-text-color-secondary;
        }

        .count {
            color: $takeout-price-color;
            margin-left: 20px;
        }
    }
}
</style>