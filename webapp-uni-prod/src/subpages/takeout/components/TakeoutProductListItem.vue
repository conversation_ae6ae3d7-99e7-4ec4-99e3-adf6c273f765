<template>
    <!--菜品item-->
    <uni-list-item class="item">
        <image :src="getServerImgUrl(item.imageUrl!)" class="item-img" />
        <view class="item-info">
            <text>{{ item.dishName }}</text>
            <text class="price">¥{{ item.price }}</text>
            <text class="limit">限制{{ item.limitPerPerson }}份/人</text>
        </view>
        <view class="item-actions">
            <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-24" size="20" @click="removeFromCart(item)" />
            <text style="margin: 0 8px;">{{ item.orderCount || 0 }}</text>
            <SlSubSvgIcon class="inline-flex" subpage="takeout" :name="computedAddDisabled(item) ? '20-20-26' : '20-20-25'" size="20"
                @click="addToCart(item)" />
        </view>
    </uni-list-item>
</template>

<script lang="ts" setup>
import { TakeoutMenuDish } from '../models';
import { getServerImgUrl } from '@/utils/image-url';
defineProps<{
    item: TakeoutMenuDish,
}>()
const emit = defineEmits<{
    (e: 'add', item: TakeoutMenuDish): void
    (e: 'remove', item: TakeoutMenuDish): void
}>()

// 计算添加按钮是否禁用
const computedAddDisabled = (item: TakeoutMenuDish) => {
    return item.orderCount && item.orderCount >= item.limitPerPerson;
};

const addToCart = (item: TakeoutMenuDish) => {
    if (item.orderCount && item.orderCount >= item.limitPerPerson) {
        return;
    }
    item.orderCount = (item.orderCount || 0) + 1; // 增加数量
    emit('add', item);
};

const removeFromCart = (item: TakeoutMenuDish) => {
    if (!item.orderCount || item.orderCount <= 0) return;
    item.orderCount -= 1; // 减少数量
    emit('remove', item);
};


</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";

.item {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 8px;
    height: 70px;
    flex: 0 0 auto;
    padding: 0 15px 15px 15px;
}

.item-img {
    width: 70px;
    height: 70px;
    margin-right: 10px;
}

.item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.limit {
    color: #999999;
    font-size: 12px;
}

.price {
    color: $takeout-price-color;
    font-weight: bold;
}

.item-actions {
    display: flex;
    align-items: center;
}
</style>