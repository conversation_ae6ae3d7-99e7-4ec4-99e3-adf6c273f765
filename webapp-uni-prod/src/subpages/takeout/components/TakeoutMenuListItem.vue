<template>
    <uni-list-item class=".item" :name="item.dishId">
        <!-- 可选复选框 -->
        <view class="checkbox">
            <up-checkbox v-if="selectable" class="item-checkbox" :customStyle="{ margin: '0' }" :name="item.dishId"
                usedAlone :checked="isSelected" @change="onCheck($event)" />
        </view>
        <!-- 主体内容 -->
        <view class="item-body" :class="{ 'selectable': selectable }">
            <!-- 商品图片 -->
            <TakeoutDishImage :item="item" />
            <view class="info">
                <view class="name-info">
                    <text class="name">
                        {{ item.dishName }}（{{ item.portionUnit }}个/份）
                    </text>
                    <text class="limit">
                        限购{{ item.limitPerPerson }}份/人
                    </text>
                </view>
                <text class="takeout-price">
                    <text class="price-symbol">¥ </text>
                    <text class="price-value">{{ item.price }}</text>
                </text>
            </view>
            <div class="actions" v-if="editable || deletable">
                <view v-if="editable" class="modify-btn" @tap.stop="onModify">
                    修改
                </view>
                <view v-if="deletable" class="delete-btn" :disabled="item.sysDeleted == 1" @tap.stop="onDelete">
                    下架
                </view>
            </div>
            <!-- 支持移除按钮 -->
            <view v-if="removable" class="remove-btn" @tap.stop="onRemove">
                <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-24" size="20" />
            </view>
        </view>
    </uni-list-item>
</template>

<script setup lang="ts">
import type { TakeoutMenuDish } from '../models'
import TakeoutDishImage from './TakeoutDishImage.vue'
import { useTakeoutAdminStore } from '@/subpages/takeout/store'
const takeoutAdminStore = useTakeoutAdminStore()
// 接收单个菜品项、已选ID列表和是否可选的标
const props = defineProps<{
    item: TakeoutMenuDish
    // 是否可选
    selectable?: boolean
    // 是否可编辑
    editable?: boolean
    // 是否可移除选中商品
    removable?: boolean
    // 是否可下架商品
    deletable?: boolean
}>()

const emit = defineEmits<{
    (e: 'delete', item: TakeoutMenuDish): void
}>()

const isSelected = computed(() => {
    return takeoutAdminStore.isDishInCart(props.item.dishId)
})

/**
 * 处理复选框选中状态变化
 */
const onCheck = (checked: boolean) => {
    if (checked) {
        takeoutAdminStore.addToCart(props.item.dishId)
    } else {
        takeoutAdminStore.removeFromCart(props.item.dishId)
    }
}

const onModify = () => {
    // 跳转到编辑页面, 从列表跳转1 ，从菜品发布页面跳转0
    uni.navigateTo({
        url: `/subpages/takeout/pages/admin/dish-handle/index?dishId=${props.item.dishId}&deletable=${props.deletable ? 1 : 0}`
    })
}

const onRemove = () => {
    takeoutAdminStore.removeFromCart(props.item.dishId)
}

const onDelete = () => {
    if (props.item.sysDeleted === 1) {
        return
    }
    emit('delete', props.item)
}

</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/mixins.scss";
@import "@/subpages/takeout/styles/flex.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/card.scss";
@import "@/subpages/takeout/styles/text.scss";
.limit {
    font-size: $uni-font-size-sm;
    color: $uni-text-color-secondary;
}

.item {
    display: flex;
    align-items: flex-start;
    background: #fff;
    height: 90px;
    flex: 0 0 auto;
    box-sizing: border-box;
    padding: 0 15px;

    .checkbox {
        display: flex;
        align-items: center;
        flex: 0 0 auto;
        height: 70px;
        margin-right: 10px;

        .item-checkbox {
            margin-right: 13px;
        }

    }


    .item-body {
        display: flex;
        align-items: center;
        flex: 1;
        position: relative;
        box-sizing: border-box;
        height: 70px;

        .info {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
            box-sizing: border-box;
            justify-content: space-between;

            .name-info {
                display: flex;
                flex-direction: column;
                flex: 1;

                .limit {
                    font-size: 12px;
                    color: #999999;
                    margin-top: 8px;
                }
            }
        }

        .actions {
            position: absolute;
            right: 0px;
            bottom: 0;
            flex: 0 0 auto;
            display: flex;
            flex-direction: row;
            align-items: center;

            .modify-btn,
            .delete-btn {
                width: 50px;
                height: 24px;
                padding: 0;
                margin: 0;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                font-size: $uni-font-size-base;
                border-radius: 3px;
                border: none;
            }

            .delete-btn {
                color: $uni-text-color-secondary;
                background: rgba(193, 194, 209, 0.2);

                &:active {
                    color: rgba(255, 11, 11, 1);
                    background: rgba(255, 11, 11, 0.2);
                }
            }

            .modify-btn+.delete-btn {
                margin-left: 10px;
            }

            .modify-btn {
                color: $uni-text-color-primary;
                background: rgba(79, 122, 246, 0.2);
            }
        }

        .remove-btn {
            display: inline-flex;
        }
    }
}
</style>
