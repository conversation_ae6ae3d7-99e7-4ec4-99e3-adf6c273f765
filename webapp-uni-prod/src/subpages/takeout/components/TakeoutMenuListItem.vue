<template>
    <uni-list-item class="item">
        <!-- 可选复选框 -->
        <up-checkbox v-if="selectable" class="item-checkbox" :customStyle="{ marginRight: '10px' }" :name="item.dishId"
            usedAlone :checked="selectedIds && selectedIds.includes(item.dishId)" @change="onCheck($event)" />
        <!-- 主体内容 -->
        <view :class="{ 'item-body': true, 'selectable': selectable }">
            <image class="item-img" :src="getImageUrl(item.imageId!)" mode="aspectFill" />
            <view class="info">
                <text class="name">
                    {{ item.dishName }}（{{ item.portionUnit }}份）
                </text>
                <text class="limit">
                    限购{{ item.limitPerPerson }}份/人
                </text>
                <text class="price">¥{{ item.price }}</text>
            </view>
            <div class="actions">
                <button v-if="editable" class="modify-btn" @click.stop="onModify">
                    修改
                </button>
                <button v-if="deletable" class="delete-btn" @click.stop="onDelete">
                    下架
                </button>
                <!-- 如果在弹层里支持移除按钮 -->
                <view v-if="removable" class="remove-btn" @click.stop="onRemove">
                    <SlSubSvgIcon class="inline-flex" subpage="takeout" name="20-20-24" size="20" />
                </view>
            </div>
        </view>
    </uni-list-item>
</template>

<script setup lang="ts">
import type { TakeoutMenuDish } from '../models'
import { getImageUrl } from '../utils/image-url';

// 组件属性定义
// 接收单个菜品项、已选ID列表和是否可选的标
const props = defineProps<{
    item: TakeoutMenuDish
    selectedIds?: string[]
    selectable?: boolean
    // 是否可编辑
    editable?: boolean
    // 是否可移除选中商品
    removable?: boolean
    // 是否可下架商品
    deletable?: boolean

}>()
const emit = defineEmits<{
    (e: 'check', payload: { dish: TakeoutMenuDish, checked: boolean }): void
    (e: 'modify', item: TakeoutMenuDish): void
    (e: 'remove', dishId: string): void
    (e: 'delete', dishId: string): void
}>()

const onCheck = (checked: boolean) => {
    emit('check', { dish: props.item, checked })
}

const onModify = () => {
    emit('modify', props.item)
}

const onRemove = () => {
    emit('remove', props.item.dishId)
}

const onDelete = () => {
    emit('delete', props.item.dishId)
}

</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";

.item {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 8px;
    height: 70px;
    flex: 0 0 auto;
    padding-bottom: 15px;

    .item-checkbox {
        margin-right: 13px;
    }

    .item-body {
        display: flex;
        align-items: center;
        flex: 1;
        position: relative;
        padding: 0 15px;

        &.selectable {
            padding-left: 0;
        }

        .item-img {
            width: 70px;
            height: 70px;
            border-radius: 5px;
            margin-right: 20px;
        }

        .info {
            flex: 1;
            display: flex;
            flex-direction: column;

            .name {
                margin-bottom: 8px;
            }

            .limit {
                font-size: $uni-font-size-sm;
                color: $uni-text-color-secondary;
                margin-bottom: 14px;
            }

            .price {
                color: $takeout-price-color;
                font-weight: bold;
            }
        }

        .actions {
            position: absolute;
            right: 0;
            bottom: 0;
            flex: 0 0 auto;
            display: flex;
            flex-direction: row;
            align-items: center;

            .modify-btn,
            .delete-btn {
                width: 50px;
                height: 24px;
                padding: 0;
                margin: 0;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                font-size: $uni-font-size-base;
                border-radius: 3px;
            }

            .delete-btn {
                color: rgba(255, 11, 11, 1);
                background: rgba(255, 11, 11, 0.2);
            }

            .modify-btn+.delete-btn {
                margin-left: 10px;
            }

            .modify-btn {
                color: $uni-text-color-primary;
                background: rgba(79, 122, 246, 0.2);
            }

            .remove-btn {
                display: inline-flex;
            }
        }

    }
}
</style>
