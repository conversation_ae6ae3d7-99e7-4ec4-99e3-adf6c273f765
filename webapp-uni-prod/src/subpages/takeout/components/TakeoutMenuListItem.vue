<template>
    <uni-list-item class="item">
        <!-- 可选复选框 -->
        <up-checkbox v-if="selectable" class="item-checkbox" :customStyle="{ marginRight: '10px' }" :name="item.dishId"
            usedAlone :checked="selectedIds.includes(item.dishId)" @change="onCheck($event)" />
        <!-- 主体内容 -->
        <view :class="{ 'item-body': true, 'selectable': selectable }">
            <image class="item-img" :src="getImageUrl(item.imageId)" mode="aspectFill" />
            <view class="info">
                <text class="name">
                    {{ item.dishName }}（{{ item.portionUnit }}份）
                </text>
                <text class="limit">
                    限购{{ item.limitPerPerson }}份/人
                </text>
                <text class="price">¥{{ item.price }}</text>
            </view>
            <button v-if="selectable" class="modify-btn" @click.stop="onModify">
                修改
            </button>
            <!-- 如果在弹层里支持移除按钮 -->
            <view v-else class="remove-btn" @click.stop="onRemove">
                <SlSVgIcon name="20-20-24" size="20" />
            </view>
        </view>
    </uni-list-item>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import type { TakeoutMenuItem } from '../models/Takeout'

// 组件属性定义
// 接收单个菜品项、已选ID列表和是否可选的标
const props = defineProps<{
    item: TakeoutMenuItem
    selectedIds: string[]
    selectable: boolean
}>()
const emit = defineEmits<{
    (e: 'check', payload: { dishId: string; checked: boolean }): void
    (e: 'modify', item: TakeoutMenuItem): void
    (e: 'remove', dishId: string): void
}>()

function getImageUrl(id: string): string {
    // TODO: 根据实际项目调整图片路径
    return `/static/images/takeout/empty.svg`;
}

function onCheck(checked: boolean) {
    emit('check', { dishId: props.item.dishId, checked })
}

function onModify() {
    emit('modify', props.item)
}

function onRemove() {
    emit('remove', props.item.dishId)
}
</script>

<style scoped lang="scss">
.item {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 8px;
    height: 70px;
    flex: 0 0 auto;
    padding-bottom: 15px;

    .item-checkbox {
        margin-right: 13px;
    }

    .item-body {
        display: flex;
        align-items: center;
        flex: 1;
        position: relative;
        padding: 0 15px;

        &.selectable {
            padding-left: 0;
        }

        .item-img {
            width: 70px;
            height: 70px;
            border-radius: 5px;
            margin-right: 20px;
        }

        .info {
            flex: 1;
            display: flex;
            flex-direction: column;

            .name {
                margin-bottom: 8px;
            }

            .limit {
                font-size: $uni-font-size-sm;
                color: $uni-text-color-secondary;
                margin-bottom: 14px;
            }

            .price {
                color: $uni-text-color-price;
                font-weight: bold;
            }
        }

        .modify-btn {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 50px;
            height: 24px;
            color: $uni-text-color-primary;
            border-radius: 3px;
            background: rgba(79, 122, 246, 0.2);
            font-size: $uni-font-size-base;
            padding: 0;
            margin: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .remove-btn {
            display: inline-flex;
        }
    }
}
</style>
