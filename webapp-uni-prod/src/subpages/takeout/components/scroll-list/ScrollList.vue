<template>
    <!--自定义列表刷新-->
    <scroll-view scroll-y :refresher-enabled="true" :refresher-triggered="refreshing"
        @refresherrefresh="$emit('refresh')" @scrolltolower="$emit('loadmore')" class="scroll-container">
        <slot name="item" :items="list"></slot>
        <u-loadmore v-if="showLoadmore" :status="loadmoreStatus" @loadmore="$emit('loadmore')" />
    </scroll-view>
</template>

<script setup lang="ts">
defineProps<{
    list: any[]
    refreshing: boolean
    loadmoreStatus: string
    showLoadmore: boolean
}>()
const emit = defineEmits(['refresh', 'loadmore'])
</script>

<style scoped lang="scss">
.scroll-container {
    height: 100%;
}
</style>