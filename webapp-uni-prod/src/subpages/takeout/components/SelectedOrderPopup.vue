<template>
    <up-popup :show="show" :round="10" mode="bottom" @close="closePopup">
        <view class="popup-container">
            <!-- 头部：标题 + 清空 -->
            <view class="popup-header">
                <text class="title">已加购菜品</text>
                <view class="clear-btn" @click="onClear">
                    <SlSubSvgIcon class="inline-flex" subpage="takeout" name="16-16-16" size="16" />
                    <text class="clear-text">清空</text>
                </view>
            </view>
            <!-- 列表：可滚动 -->
            <view class="popup-list">
                <scroll-view class="scroll-view-list" :scroll-y="true">
                    <uni-list>
                        <TakeoutProductListItem v-for="item in cart" :key="item.dishId" :item="item" />
                    </uni-list>
                </scroll-view>
            </view>
            <TakeoutOrderCheck />
        </view>
    </up-popup>
</template>

<script setup lang="ts">
import TakeoutProductListItem from './TakeoutProductListItem.vue'
import TakeoutOrderCheck from './TakeoutOrderCheck.vue'
import { useTakeoutStore } from '@/store'
const takeoutStore = useTakeoutStore()
defineProps<{
    show: boolean;
}>()
const emit = defineEmits<{
    (e: 'update:show', val: boolean): void
}>()

const cart = computed(() => takeoutStore.cart) // 从store获取购物车列表

// 关闭弹层
const closePopup = () => {
    emit('update:show', false)
}

// 清空所有已选
const onClear = () => {
    takeoutStore.clearCart(); // 清空购物车
}

</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/flex.scss";

.popup-container {
    width: 100%;
    background-color: #fff;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    padding: 0 15px;

    .title {
        font-weight: bold;
    }

    .clear-btn {
        background: transparent;
        color: $uni-text-color-secondary;
        display: inline-flex;
        align-items: center;

        .clear-text {
            margin-left: 5px;
        }
    }
}

.popup-list {
    height: 261px;
    border-top: 1px solid rgba(153, 153, 153, 0.2);
    padding: 10px 0;
    flex: 1;

    .scroll-view-list {
        height: 240px;
    }
}
</style>