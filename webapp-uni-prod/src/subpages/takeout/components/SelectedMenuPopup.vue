<template>
    <up-popup :show="show" :round="10" mode="bottom" @close="closePopup">
        <view class="popup-container">
            <!-- 头部：标题 + 清空 -->
            <view class="popup-header">
                <text class="title">已选菜品</text>
                <view class="clear-btn" @click="onClear">
                    <SlSVgIcon name="16-16-16" size="16" />
                    <text>清空</text>
                </view>
            </view>
            <!-- 列表：可滚动 -->
            <view class="popup-list">
                <scroll-view class="scroll-view-list" :scroll-y="true">
                    <uni-list>
                        <TakeoutMenuListItem v-for="item in selectedItems" :key="item.dishId" :item="item"
                            :selected-ids="selectedIds" :selectable="false" @remove="onRemove" />
                    </uni-list>
                </scroll-view>
            </view>

            <view class="footer">
                <view class="cart">
                    <SlSVgIcon name="20-20-23" size="20" />
                    <view v-if="selectedIds.length" class="badge">
                        {{ selectedIds.length }}
                    </view>
                </view>
                <text class="footer-text">已选菜品</text>
            </view>
        </view>
    </up-popup>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import TakeoutMenuListItem from './TakeoutMenuListItem.vue';
import type { TakeoutMenuItem } from '../models/Takeout'

const props = defineProps<{
    show: boolean
    items: TakeoutMenuItem[]
    selectedIds: string[]
}>()
const emit = defineEmits<{
    (e: 'update:show', val: boolean): void
    (e: 'clear'): void
    (e: 'remove', dishId: string): void
}>()

// 计算出已选的完整项
const selectedItems = computed(() =>
    props.items.filter(i => props.selectedIds.includes(i.dishId))
)

// 关闭弹层
function closePopup() {
    emit('update:show', false)
}

// 清空所有已选
function onClear() {
    emit('clear')
}

// 移除单项
function onRemove(dishId: string) {
    emit('remove', dishId)
}

</script>

<style scoped lang="scss">
.popup-container {
    width: 100%;
    background-color: #fff;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    padding: 0 15px;

    .title {
        font-weight: bold;
    }

    .clear-btn {
        background: transparent;
        color: $uni-text-color-secondary;
        display: inline-flex;
        align-items: center;

        text {
            margin-left: 4px;
        }
    }
}

.popup-list {
    height: 261px;
    border-top: 1px solid rgba(153, 153, 153, 0.2);
    padding: 10px 0;
    flex: 1;

    .scroll-view-list {
        height: 240px;
    }
}

// 底部已选菜品
.footer {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    background: #FFFFFF;
    box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);

    .cart {
        width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .badge {
            position: absolute;
            top: -3px;
            right: -3px;
            min-width: 12px;
            height: 12px;
            line-height: 12px;
            font-size: 8px;
            color: #fff;
            background: $uni-text-color-price;
            border-radius: 50%;
            text-align: center;
        }
    }

    .footer-text {
        font-size: 16px;
        color: #333;
        margin-left: 6px;
    }


}
</style>
