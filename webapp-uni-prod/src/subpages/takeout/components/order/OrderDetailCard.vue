<template>
    <!--订单详情item-->
    <view class="takeout-card order-detail-card" :class="item.statusType" @click="goToDetail">
        <view class="order-header">
            <SlSubSvgIcon class="inline-flex" subpage="takeout" name="16-16-29" size="16" />
            <view class="unpaid-info" v-if="item.status === TakeoutOrderStatus.UNPAID">
                <view class="unpaid-tip">
                    <text class="status-text">请在</text>
                    <text class="remaining-time">{{ remainingMinTime }}</text>
                    <text class="status-text">内支付</text>
                </view>
                <text class="unpaid-status">
                    待支付
                </text>
            </view>
            <view class="other-info" v-else>
                <view class="other-tip">
                    <tempate v-if="item.takeoutNumber">
                        <text class="status-text">取餐号：</text>
                        <text class="takeout-number"
                            :class="{ 'primary': item.status === TakeoutOrderStatus.PENDING }">{{
                                item.takeoutNumber }}</text>
                    </tempate>
                </view>
                <text class="other-status" :class="{ 'primary': item.status === TakeoutOrderStatus.PENDING }">
                    {{ item.statusText }}
                </text>
            </view>
        </view>
        <OrderProductList :orderProductItems="item.productList" isOrderList :takeoutTime="item.takeoutTime" />
        <view class="order-take-info" v-if="!onlyOneProduct">
            <text class="take-info-time">取餐时间：{{ item.takeoutTime }}</text>
        </view>
        <view class="order-actions" v-if="item.status !== TakeoutOrderStatus.PENDING">
            <template v-if="item.status === TakeoutOrderStatus.UNPAID">
                <button class="takeout-btn btn-default btn-h-28" @click.stop="cancelOrder(item)">
                    取消订单
                </button>
                <button class="takeout-btn btn-primary btn-h-28" @click.stop="goToPayment(item)">
                    立即支付
                </button>
            </template>
            <template
                v-if="item.status === TakeoutOrderStatus.COMPLETED || item.status === TakeoutOrderStatus.CANCELLED">
                <button class="takeout-btn btn-default btn-h-28" @click.stop="deleteOrder(item)">
                    删除订单
                </button>
            </template>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { TakeoutOrderItem, TakeoutOrderStatus } from '../../models/Takeout';
import OrderProductList from './OrderProductList.vue';

const props = defineProps<{
    item: TakeoutOrderItem;
}>();

const emit = defineEmits<{
    (e: 'goToPayment', item: TakeoutOrderItem): void;
    (e: 'cancelOrder', item: TakeoutOrderItem): void;
    (e: 'deleteOrder', item: TakeoutOrderItem): void;
    (e: 'goToDetail', item: TakeoutOrderItem): void;
}>();

// 仅购买一种商品
const onlyOneProduct = computed(() => {
    return props.item.productList.length === 1;
});

// 剩余支付时间，格式 mm:ss ,如 14：33
const remainingMinTime = ref<string>('15:00');

const getRemainingTime = () => {
    // 这里可以添加逻辑来计算剩余时间
    // 例如，假设剩余时间为15分钟
    let minutes = 15;
    let seconds = 0;
    remainingMinTime.value = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    setInterval(() => {
        if (seconds > 0) {
            seconds--;
        } else if (minutes > 0) {
            minutes--;
            seconds = 59;
        }
        remainingMinTime.value = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    }, 1000);
};

onMounted(() => {
    if (props.item.status === TakeoutOrderStatus.UNPAID) {
        getRemainingTime();
    }
});

const goToPayment = (item: TakeoutOrderItem) => {
    emit('goToPayment', item);
}
const cancelOrder = (item: TakeoutOrderItem) => {
    emit('cancelOrder', item);
}
const deleteOrder = (item: TakeoutOrderItem) => {
    emit('deleteOrder', item);
}
const goToDetail = () => {
    console.log('跳转到订单详情', props.item.id);
    uni.navigateTo({
        url: `/subpages/takeout/pages/staff/order-detail/index?orderId=${props.item.id}`
    });
}

</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/card.scss";

.order-detail-card {
    width: 100%;
    padding: 10px 0;
    // height: 194px;
    position: relative;
    display: flex;
    flex-direction: column;

    // &.PENDING {
    //     height: 148px;
    // }
    .order-header {
        display: flex;
        align-items: center;
        padding: 0 15px;
        flex: 0 0 auto;
    }

    .unpaid-info,
    .other-info {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 4px;
        flex: 1;

        .unpaid-tip,
        .other-tip {
            flex: 1;
            display: flex;
            font-weight: bold;

            .takeout-number {
                color: $uni-text-color-secondary;

                &.primary {
                    color: $uni-text-color-primary;
                }
            }

            .remaining-time {
                color: #D5302C;
            }
        }

        .unpaid-status {
            color: #EE391C;
            flex: 0 0 auto;
        }

        .other-status {
            color: $uni-text-color-secondary;

            &.primary {
                color: $uni-text-color-primary;
            }
        }
    }

    .order-take-info {
        padding: 0 15px;

        .take-info-time {
            color: $uni-text-color-secondary;
            font-size: $uni-font-size-sm;
        }
    }

    .order-actions {
        display: flex;
        width: 100%;
        flex: 0 0 auto;
        padding: 0 15px;
        justify-content: flex-end;
        align-items: center;
        margin-top: 20px;

        .takeout-btn {
            width: 78px;
            border-radius: 3px;
            flex: 0 0 auto;
            margin: 0;
        }

        .takeout-btn+.takeout-btn {
            margin-left: 15px;
        }
    }
}
</style>