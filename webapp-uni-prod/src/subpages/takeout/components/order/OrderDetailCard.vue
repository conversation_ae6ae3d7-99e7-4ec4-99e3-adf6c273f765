<template>
    <!--订单详情item-->
    <uni-list-item>
        <view class="takeout-card order-detail-card" :class="statusType" @tap="goToDetail">
            <view class="order-header">
                <SlSubSvgIcon class="inline-flex" subpage="takeout" name="16-16-29" size="16" />
                <view class="unpaid-info" v-if="item.status === TakeoutOrderStatus.UNPAID && remaining > 0">
                    <view class="unpaid-tip">
                        <text class="status-text">请在</text>
                        <text class="remaining-time">{{ remainingMinTime }}</text>
                        <text class="status-text">内支付</text>
                    </view>
                    <text class="unpaid-status">
                        待支付
                    </text>
                </view>
                <view class="other-info" v-else>
                    <view class="other-tip">
                        <tempate v-if="item.pickupCode">
                            <text class="status-text">取餐号：</text>
                            <text class="takeout-number"
                                :class="{ 'primary': item.status === TakeoutOrderStatus.PENDING }">{{
                                    item.cabinetId || '待放置' }}-{{
                                    item.pickupCode }}</text>
                        </tempate>
                    </view>
                    <text class="other-status" :class="{ 'primary': item.status === TakeoutOrderStatus.PENDING }">
                        {{ item.statusCn }}
                    </text>
                </view>
            </view>
            <OrderProductList :takeoutOrder="item" isOrderList />
            <view class="order-take-info" v-if="!onlyOneProduct">
                <text class="take-info-time">取餐时间：{{ pickupTime }}</text>
            </view>
            <view class="order-actions" v-if="item.status != TakeoutOrderStatus.PENDING">
                <template v-if="item.status === TakeoutOrderStatus.UNPAID && remaining > 0">
                    <view class="takeout-btn btn-default btn-h-28" @tap.stop="cancelOrder">
                        取消订单
                    </view>
                    <view class="takeout-btn btn-primary btn-h-28" @tap.stop="goToPayment">
                        立即支付
                    </view>
                </template>
                <template
                    v-if="item.status === TakeoutOrderStatus.COMPLETED || item.status === TakeoutOrderStatus.CANCELLED">
                    <view class="takeout-btn btn-default btn-h-28" @tap.stop="deleteOrder">
                        删除订单
                    </view>
                </template>
            </view>
        </view>
    </uni-list-item>
</template>

<script lang="ts" setup>
import { OrderChangeType, TakeoutOrder, TakeoutOrderStatus } from '@/subpages/takeout/models';
import { getStatusType } from '@/subpages/takeout/utils';
import OrderProductList from './OrderProductList.vue';
import { useCountDown } from '@/subpages/takeout/composables/useCountDown';
import { useDate } from '@/subpages/takeout/composables/useDate';
const { formatPickupTime } = useDate();

const props = defineProps<{
    item: TakeoutOrder;
}>();

const emit = defineEmits<{
    (e: 'update:type', val: OrderChangeType): void;
}>();

// 剩余时间倒计时
const { display: remainingMinTime, remaining, start } = useCountDown(props.item.remainingTime || 0);
const pickupTime = computed(() => formatPickupTime(props.item.pickupTime!))

watch(remaining, (newRemaining) => {
    // 如果剩余时间为0 
    if (newRemaining <= 0) {
        props.item.status = TakeoutOrderStatus.CANCELLED; // 设置订单状态为已取消
        props.item.remainingTime = 0; // 重置剩余时间
        props.item.statusCn = '已取消'; // 更新状态文本
    }
});

const statusType = computed(() => {
    return getStatusType(props.item.status!);
})

// 仅购买一种商品
const onlyOneProduct = computed(() => {
    return props.item.takeoutOrderItemList.length === 1;
});

onMounted(() => {
    // 待支付订单，需要开启倒计时
    if (props.item.status === TakeoutOrderStatus.UNPAID) {
        start(props.item.remainingTime || 0);
    }
});

/**
 * 订单列表点击“立即支付”跳转到订单确认页面
 */
const goToPayment = async () => {
    const orderId = props.item.id!;
    uni.navigateTo({
        url: `/subpages/takeout/pages/staff/checkout/index?orderId=${orderId}`,
    });
}
// 取消订单
const cancelOrder = async () => {
    if (props.item && props.item.id) {
        emit('update:type', 'cancel');
    }
}
// 删除订单
const deleteOrder = async () => {
    if (props.item && props.item.id) {
        emit('update:type', 'delete');
    }
}

// 跳转到订单详情
const goToDetail = () => {
    uni.navigateTo({
        url: `/subpages/takeout/pages/staff/order-detail/index?orderId=${props.item.id}`
    });
}

</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/flex.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/card.scss";

.order-detail-card {
    width: 100%;
    padding: 10px 0;
    // height: 194px;
    position: relative;
    display: flex;
    flex-direction: column;

    // &.PENDING {
    //     height: 148px;
    // }
    .order-header {
        display: flex;
        align-items: center;
        padding: 0 15px;
        flex: 0 0 auto;
    }

    .unpaid-info,
    .other-info {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 4px;
        flex: 1;

        .unpaid-tip,
        .other-tip {
            flex: 1;
            display: flex;
            font-weight: bold;

            .takeout-number {
                color: $uni-text-color-secondary;

                &.primary {
                    color: $uni-text-color-primary;
                }
            }

            .remaining-time {
                color: #D5302C;
            }
        }

        .unpaid-status {
            color: #EE391C;
            flex: 0 0 auto;
        }

        .other-status {
            color: $uni-text-color-secondary;

            &.primary {
                color: $uni-text-color-primary;
            }
        }
    }

    .order-take-info {
        padding: 0 15px;

        .take-info-time {
            color: $uni-text-color-secondary;
            font-size: $uni-font-size-sm;
        }
    }

    .order-actions {
        display: flex;
        width: 100%;
        flex: 0 0 auto;
        padding: 0 15px;
        justify-content: flex-end;
        align-items: center;
        margin-top: 20px;

        .takeout-btn {
            width: 78px;
            border-radius: 3px;
            flex: 0 0 auto;
            margin: 0;
        }

        .takeout-btn+.takeout-btn {
            margin-left: 15px;
        }
    }
}
</style>