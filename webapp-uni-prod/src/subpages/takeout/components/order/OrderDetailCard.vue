<template>
    <!--订单详情item-->
    <uni-list-item>
        <view class="takeout-card order-detail-card" :class="statusType" @tap="goToDetail">
            <view class="order-header">
                <SlSubSvgIcon class="inline-flex" subpage="takeout" name="16-16-29" size="16" />
                <view class="unpaid-info" v-if="item.status === TakeoutOrderStatus.UNPAID">
                    <view class="unpaid-tip">
                        <text class="status-text">请在</text>
                        <text class="remaining-time">{{ remainingMinTime }}</text>
                        <text class="status-text">内支付</text>
                    </view>
                    <text class="unpaid-status">
                        待支付
                    </text>
                </view>
                <view class="other-info" v-else>
                    <view class="other-tip">
                        <tempate v-if="item.pickupCode">
                            <text class="status-text">取餐号：</text>
                            <text class="takeout-number"
                                :class="{ 'primary': item.status === TakeoutOrderStatus.PENDING }">{{
                                    item.pickupCode }}</text>
                        </tempate>
                    </view>
                    <text class="other-status" :class="{ 'primary': item.status === TakeoutOrderStatus.PENDING }">
                        {{ item.statusCn }}
                    </text>
                </view>
            </view>
            <OrderProductList :takeoutOrder="item" isOrderList />
            <view class="order-take-info" v-if="!onlyOneProduct">
                <text class="take-info-time">取餐时间：{{ pickupTime }}</text>
            </view>
            <view class="order-actions" v-if="item.status != TakeoutOrderStatus.PENDING">
                <template v-if="item.status === TakeoutOrderStatus.UNPAID">
                    <button class="takeout-btn btn-default btn-h-28" @tap.stop="cancelOrder">
                        取消订单
                    </button>
                    <button class="takeout-btn btn-primary btn-h-28" @tap.stop="goToPayment">
                        立即支付
                    </button>
                </template>
                <template
                    v-if="item.status === TakeoutOrderStatus.COMPLETED || item.status === TakeoutOrderStatus.CANCELLED">
                    <button class="takeout-btn btn-default btn-h-28" @tap.stop="deleteOrder">
                        删除订单
                    </button>
                </template>
            </view>
        </view>
    </uni-list-item>
</template>

<script lang="ts" setup>
import { TakeoutOrder, TakeoutOrderStatus } from '@/subpages/takeout/models';
import { formatPickupTime, getStatusType } from '@/subpages/takeout/utils';
import OrderProductList from './OrderProductList.vue';
import { takeoutOrderService } from '@/subpages/takeout/service';
import { useCountDown } from '@/subpages/takeout/composables/useCountDown';

const props = defineProps<{
    item: TakeoutOrder;
}>();
// 剩余时间倒计时
const { display: remainingMinTime, start } = useCountDown(props.item.remainingTime || 0);
const pickupTime = computed(() => formatPickupTime(props.item.pickupTime!))

const statusType = computed(() => {
    return getStatusType(props.item.status);
})

// 仅购买一种商品
const onlyOneProduct = computed(() => {
    return props.item.takeoutOrderItemList.length === 1;
});


onMounted(() => {
    // 待支付订单，需要开启倒计时
    if (props.item.status === TakeoutOrderStatus.UNPAID) {
        start(props.item.remainingTime || 0);
    }
});

// TODO：调用微信支付
const goToPayment = () => {
    // 提示功能未完善
    uni.showToast({
        title: '支付功能未完善',
        icon: 'none'
    });
}
// 取消订单
const cancelOrder = async () => {
    // TODO: 强制弹窗提醒用户
    if (props.item && props.item.id) {
        await takeoutOrderService.cancelUnpaidOrder(props.item.id);
        console.log('取消订单', props.item.id);
    }
}
// 删除订单
const deleteOrder = async () => {
    // TODO: 强制弹窗提醒用户
    if (props.item && props.item.id) {
        await takeoutOrderService.deleteCompletedOrder(props.item.id);
    }
}

// 跳转到订单详情
const goToDetail = () => {
    uni.navigateTo({
        url: `/subpages/takeout/pages/staff/order-detail/index?orderId=${props.item.id}`
    });
}

</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/flex.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/card.scss";

.order-detail-card {
    width: 100%;
    padding: 10px 0;
    // height: 194px;
    position: relative;
    display: flex;
    flex-direction: column;

    // &.PENDING {
    //     height: 148px;
    // }
    .order-header {
        display: flex;
        align-items: center;
        padding: 0 15px;
        flex: 0 0 auto;
    }

    .unpaid-info,
    .other-info {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 4px;
        flex: 1;

        .unpaid-tip,
        .other-tip {
            flex: 1;
            display: flex;
            font-weight: bold;

            .takeout-number {
                color: $uni-text-color-secondary;

                &.primary {
                    color: $uni-text-color-primary;
                }
            }

            .remaining-time {
                color: #D5302C;
            }
        }

        .unpaid-status {
            color: #EE391C;
            flex: 0 0 auto;
        }

        .other-status {
            color: $uni-text-color-secondary;

            &.primary {
                color: $uni-text-color-primary;
            }
        }
    }

    .order-take-info {
        padding: 0 15px;

        .take-info-time {
            color: $uni-text-color-secondary;
            font-size: $uni-font-size-sm;
        }
    }

    .order-actions {
        display: flex;
        width: 100%;
        flex: 0 0 auto;
        padding: 0 15px;
        justify-content: flex-end;
        align-items: center;
        margin-top: 20px;

        .takeout-btn {
            width: 78px;
            border-radius: 3px;
            flex: 0 0 auto;
            margin: 0;
        }

        .takeout-btn+.takeout-btn {
            margin-left: 15px;
        }
    }
}
</style>