<template>
    <!--订单商品详细item-->
    <view class="order-item" :class="{ 'only-one-product': onlyOneProduct }" @click="$emit('goToDetail', item)">
        <view class="image-container">
            <!-- 商品图片 -->
            <!-- <image v-if="item.imageUrl" :src="item.imageUrl" class="item-image img-round-5" /> -->
            <up-image v-if="item.imageUrl" :lazy-load="true" :radius="5" :show-loading="true" :width="size"
                :height="size" mode="aspectFill" :src="item.imageUrl"></up-image>
            <SlSubSvgIcon v-else class="inline-flex img-round-5" subpage="takeout" name="70-70-1" size="50" />
            <text class="product-count" v-if="item.orderCount! > 1">x{{ item.orderCount }}</text>
        </view>
        <text v-if="!onlyOneProduct" class="product-name">{{ item.dishName }}</text>
    </view>
</template>

<script lang="ts" setup>
import { TakeoutOrderItem } from '@/subpages/takeout/models';
import { toRpx } from '@/utils/toRpx';
defineProps<{
    item: TakeoutOrderItem,
    onlyOneProduct?: boolean // 是否仅有一个商品
}>()
const size = toRpx(50)
</script>
<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/mixins.scss";

.order-item {
    width: 50px;
    flex: 0 0 auto; // 固定宽度

    &.only-one-product {
        height: 50px;
    }

    .image-container {
        position: relative;
        width: 50px;
        height: 50px;
        border-radius: 5px;
        background: #FFFFFF;

        .item-image {
            width: 100%;
            height: 100%;
        }

        .product-count {
            display: inline-block;
            text-align: center;
            min-width: 17px;
            height: 12px;
            position: absolute;
            bottom: 0;
            left: 0;
            color: rgba(255, 255, 255, 0.8);
            border-radius: 18px;
            background: rgba(51, 51, 51, 0.5);
            font-size: 8px;
            backdrop-filter: blur(10px);
        }
    }

    .product-name {
        width: 100%;
        display: inline-block;
        text-align: center;
        font-size: 10px;
        color: #666666;
        margin-top: 6px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

}
</style>