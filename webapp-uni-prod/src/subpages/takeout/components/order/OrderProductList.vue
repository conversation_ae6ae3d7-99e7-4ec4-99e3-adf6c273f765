<template>
    <view class="order-products">
        <view class="order-item-list">
            <OrderProductItem class="order-product-item" v-for="(item) in takeoutOrder.takeoutOrderItemList"
                :key="item.dishId" :onlyOneProduct="onlyOneProduct" :item="item" />
            <view class="takeout-time flex-col-center-y" v-if="onlyOneProduct && isOrderList">
                <text class="product-info">
                    {{ takeoutOrder.takeoutOrderItemList[0].dishName + '' +
                        (takeoutOrder.takeoutOrderItemList[0].portionUnit) }}
                </text>
                <text class="takeout-time-text">取餐时间：{{ pickupTime }}</text>
            </view>
        </view>
        <!--商品金额计数-->
        <view class="products-count" :class="{ 'is-order-list': props.isOrderList }">
            <text class="price" v-if="props.isOrderList">
                ¥ {{ takeoutOrder.totalPrice }}
            </text>
            <text class="count">共{{ totalCount }}件</text>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { TakeoutOrder } from '@/subpages/takeout/models';
import OrderProductItem from '@/subpages/takeout/components/order/OrderProductItem.vue';
import { formatPickupTime } from '@/subpages/takeout/utils';

const props = defineProps<{
    takeoutOrder: TakeoutOrder,
    isOrderList?: boolean; // 是否订单列表 , 列表订单会展示金额
}>();

const totalCount = computed((): number => {
    if (!props.takeoutOrder || !props.takeoutOrder.takeoutOrderItemList) {
        return 0;
    }
    return props.takeoutOrder.takeoutOrderItemList.reduce(
        (count, item) => count + item.orderCount,
        0
    )
});

const pickupTime = computed(() => formatPickupTime(props.takeoutOrder.pickupTime!))

const onlyOneProduct = computed(() => {
    return totalCount.value === 1
});

</script>

<style scoped lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/flex.scss";

.order-products {
    background-color: #fff;
    padding: 10px 15px;
    width: 100%;
    display: flex;
}

.order-product-item+.order-product-item {
    margin-left: 11px; //商品之间的间距
}

.order-item-list {
    white-space: nowrap;
    flex: 1;
    display: flex;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; // iOS 弹性滚动
    // 以下两行可以隐藏滚动条（可选）
    scrollbar-width: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .takeout-time {
        font-size: $uni-font-size-sm;
        color: $uni-text-color-secondary;
        display: flex;
        flex-direction: column;
        margin-left: 5px;
        height: 50px;

        .takeout-time-text {
            margin-top: 3px;
        }
    }

    .order-item {
        width: 50px;
        flex: 0 0 auto; // 固定宽度

        &+.order-item {
            margin-left: 11px; //商品之间的间距
        }

        .image-container {
            position: relative;
            width: 50px;
            height: 50px;
            border-radius: 5px;
            background: #FFFFFF;

            .item-image {
                width: 100%;
                height: 100%;
            }

            .product-count {
                display: inline-block;
                text-align: center;
                min-width: 17px;
                height: 12px;
                position: absolute;
                bottom: 0;
                left: 0;
                color: rgba(255, 255, 255, 0.8);
                border-radius: 18px;
                background: rgba(51, 51, 51, 0.5);
                font-size: 8px;
                backdrop-filter: blur(10px);
            }
        }

        .product-name {
            width: 100%;
            display: inline-block;
            text-align: center;
            font-size: 12px;
            color: #666666;
            margin-top: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

    }
}

.products-count {
    min-width: 65px;
    padding-left: 5px;
    flex: 0 0 auto;
    // height: 60px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .count {
        font-size: $uni-font-size-sm;
        color: $uni-text-color-secondary;
    }

    &.is-order-list {
        min-width: 55px;
        flex-direction: column;
        align-items: flex-end;
        justify-content: center;

        .price {
            font-weight: bold;
            color: $uni-text-color;
        }

        .count {
            font-size: $uni-font-size-xs;
        }
    }
}
</style>