<template>
    <up-modal class="custom-tip-modal" :show="show" :showCancelButton="false" :showConfirmButton="false">
        <view class="modal-content">
            <view class="tip-title flex-center">
                <SlSubSvgIcon class="inline-flex" subpage="takeout" name="16-16-7" size="16" />
                <text class="takeout-danger-text">提示</text>
            </view>
            <view class="tip-content flex-col-center-x">
                <slot></slot>
            </view>
            <view class="modal-footer flex-center-y">
                <view @tap="cancel" class="takeout-btn btn-border-unset btn-h-40 cancel takeout-secondary-text">
                    {{ cancelText }}
                </view>
                <view @tap="confirm"
                    class="takeout-btn btn-border-unset btn-h-40 confirm takeout-primary-text takeout-blod-text">
                    {{ confirmText }}
                </view>
            </view>
        </view>
    </up-modal>
</template>

<script setup lang="ts">
withDefaults(defineProps<{
    customIcon?: string       // 图标名称
    customSubpage?: string    // 子页面名，默认 takeout
    customIconSize?: number   // 图标大小
    confirmText?: string
    cancelText?: string
    show: boolean
}>(), {
    customSubpage: 'takeout',
    customIconSize: 20,
    confirmText: '确定',
    cancelText: '取消',
    show: false
})

const emit = defineEmits<{
    (e: 'update:show', val: boolean): void
    (e: 'confirm'): void
    (e: 'cancel'): void
}>()

const cancel = () => {
    emit('update:show', false)
    emit('cancel')
}
const confirm = () => {
    emit('update:show', false)
    emit('confirm')
}
</script>

<style lang="scss">
@import "@/subpages/takeout/styles/variable.scss";
@import "@/subpages/takeout/styles/button.scss";
@import "@/subpages/takeout/styles/flex.scss";
@import "@/subpages/takeout/styles/text.scss";

::v-deep .u-modal__content {
    padding: 0 !important;
}

.custom-tip-modal {
    border-radius: 10px;


}

.modal-content {
    width: 100%;
    padding-top: 20px;

    .takeout-danger-text {
        font-size: 16px;
        margin-left: 6px;
    }

    .tip-content {
        margin-top: 12px;
    }
}

.modal-footer {
    border-top: 1px solid #D8D8D8;
    margin-top: 20px;

    .takeout-btn {
        width: 50%;

        &.cancel {
            border-right: 1px solid #D8D8D8;
        }
    }
}
</style>
