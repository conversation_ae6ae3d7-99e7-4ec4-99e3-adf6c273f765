<template>
    <view class="empty-container" v-if="show">
        <SlSubSvgIcon class="inline-flex" subpage="takeout" name="40-40-1" size="40" />
        <text class="takeout-secondary-text">{{ text || '暂无数据' }}</text>
    </view>
</template>
<script lang="ts" setup>

const props = defineProps<{
    show?: boolean
    text?: string
}>();

const show = computed(() => {
    return props.show ?? true;
});

</script>

<style lang="scss" scoped>
@import "@/subpages/takeout/styles/flex.scss";

.empty-container {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .takeout-secondary-text {
        display: inline-flex;
        justify-content: center;
        font-size: 12px;
        color: #999999;
    }
}
</style>
