<template>
    <view class="empty-container" v-if="show">
        <SlSubSvgIcon class="inline-flex" subpage="takeout" name="40-40-1" size="40" />
    </view>
</template>
<script lang="ts" setup>

const props = defineProps<{
    show?: boolean
}>();

const show = computed(() => {
    return props.show ?? true;
});

</script>

<style lang="scss" scoped>
.empty-container {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
