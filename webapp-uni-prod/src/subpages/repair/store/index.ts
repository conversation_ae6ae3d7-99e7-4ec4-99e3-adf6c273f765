import { defineStore } from 'pinia';
import { RepairState, RepairActions, RepairGetters } from './types';
import { Personnel } from '@/models/Personnel';

/**物业服务状态管理 */
const useRepairStore = defineStore<
  'repair',
  RepairState,
  RepairGetters,
  RepairActions
>('repair', {
  state: () => ({
    /**选中的内部维修人员信息 */
    fixManInfo: { id: "", name: "", phone: "", position: "" },
    /**是否需要刷新报修列表 */
    needRefresh: true,
  }),

  actions: {
    /**设置选中的内部维修人员 */
    setFixMan(info: Personnel) {
      this.fixManInfo = info;
    },
    /**设置是否需要刷新报修列表 */
    setNeedRefresh(need: boolean) {
      this.needRefresh = need;
    },
  },
});

export default useRepairStore;