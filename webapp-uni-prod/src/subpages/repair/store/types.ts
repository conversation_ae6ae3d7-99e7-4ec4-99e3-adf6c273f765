import { Personnel } from "@/models/Account";

export interface RepairState {
  /**选中的内部维修人员信息 */  fixManInfo: Partial<Personnel>;
  /**是否需要刷新报修列表 */
  needRefresh: boolean;
}

export interface RepairActions {
  /**设置选中内部的维修人员 */setFixMan(info: Partial<Personnel>): void;
  /**设置是否需要刷新报修列表 */
  setNeedRefresh(need: boolean): void;
}

export interface RepairGetters {
  // /**获取当前搜索参数 */searchParams: () => RepairSearch;
  [key: string]: ((state: RepairState) => any) | (() => any);
}