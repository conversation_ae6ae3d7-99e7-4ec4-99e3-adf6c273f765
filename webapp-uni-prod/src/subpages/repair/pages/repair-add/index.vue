<template>
  <view class="container">
    <u-form
      labelPosition="left"
      :model="data"
      :rules="rules"
      ref="formRef"
      labelWidth="120"
    >
      <view class="form-card">
        <u-form-item label="报修人员" prop="repairMan">
          <view class="info">{{ data.repairMan }}</view>
        </u-form-item>
        <u-form-item label="手机号码" prop="repairTel">
          <view class="info">{{ data.repairTel }}</view>
        </u-form-item>
        <u-form-item label="详细位置" prop="position" required>
          <input
            type="text"
            v-model="data.position"
            class="input"
            placeholder="请输入详细位置"
            :maxlength="10"
            @input="onValidate('position')"
          />
        </u-form-item>
        <u-form-item label="紧急程度" prop="urgency" required>
          <SLSelect
            class="select"
            v-model="data.urgency"
            :options="urgencyOpt"
            @change="onValidate('urgency')"
          />
        </u-form-item>
        <u-form-item
          label="报修内容"
          class="form-textarea"
          prop="repairContent"
        >
          <SlTextarea
            class="textarea"
            v-model="data.repairContent"
            :showWordLimit="true"
            :maxlength="30"
          />
        </u-form-item>
      </view>
      <view class="form-card upload-card">
        <view class="upload-title">图片/视频</view>
        <!-- todo上传图片视频 -->
        <!-- <u-upload
          :fileList="fileList"
          @afterRead="afterRead"
          @delete="deletePic"
          name="file"
          multiple
          :maxCount="9"
          :previewFullImage="true"
        ></u-upload> -->
        <UploadImage2
          v-model:contentList="data.contentList"
          @change="handleContentChange"
        ></UploadImage2>
      </view>
    </u-form>
    <view class="save-btn">
      <BaseButton btn-type="save" size="large" @click="onSave">提交</BaseButton>
    </view>
  </view>

  <StatePop
    v-model:show="showPopup"
    :title="statePopTitle"
    :type="statePopType"
    :confirmText="confirmText"
    :icon="statePopIcon"
    @confirm="onConfirm"
    @cancel="onConfirm"
  >
  </StatePop>
</template>

<script setup lang="ts">
import { UrgencyLevel, Repair } from "@/subpages/repair/models/Repair";
import repairService from "@/service/repair/repair.service";
import { usePrincipalStore } from "@/store";
import { uploadFile } from "@/service/upload.service";

/**获取用户信息 */
const principalStore = usePrincipalStore();
/**表单引用 */
const formRef = ref();
/**todo引入字典 */
const urgencyOpt = [
  { label: "高", value: UrgencyLevel.HIGH },
  { label: "中", value: UrgencyLevel.MEDIUM },
  { label: "低", value: UrgencyLevel.LOW },
];
/**表单数据 */
const data = reactive<Repair>({
  repairMan: "",
  repairTel: "",
  position: "",
  urgency: <UrgencyLevel>"",
  repairContent: "",
  contentList: [],
});
/**文件列表 */
const fileList = ref<any>([]);
/**弹窗相关状态 */
const showPopup = ref(false);
const statePopTitle = ref("");
const statePopType = ref("");
const confirmText = ref("");
const statePopIcon = ref("");
/**表单验证规则 */
const rules = {
  position: [
    { required: true, message: "请输入详细位置", trigger: ["blur", "change"] },
  ],
  urgency: [{ required: true, message: "请选择紧急程度", trigger: "change" }],
};
onMounted(async () => {
  getAccount();
});
const handleContentChange = (event:any) => {
  console.log("上传成功handleContentChange", event.content, data.contentList);
  // 可以在这里做一些额外处理
};
/**实时校验字段 */
const onValidate = (field: string) => {
  if (formRef.value) {
    formRef.value.validateField(field);
  }
};
const onSave = () => {
  formRef.value
    .validate()
    .then(async (valid: any) => {
      if (valid) {
        try {
          // console.log("提交报修数据:", toRaw(data));
          await repairService.save(data);
          statePopTitle.value = "报修成功";
          statePopType.value = "success";
          confirmText.value = "查看报修记录";
          statePopIcon.value = "components-succeed";
          showPopup.value = true;
        } catch (error) {
          statePopTitle.value = "报修失败";
          statePopType.value = "error";
          confirmText.value = "返回";
          statePopIcon.value = "components-lose";
          showPopup.value = true;
        }
      }
    })
    .catch((errors: any) => {
      console.log("表单验证失败:", errors);
    });
};
const onConfirm = () => {
  showPopup.value = false;
  uni.navigateTo({ url: "/subpages/repair/pages/index" });
};
// const onCancel = () => {
//   showPopup.value = false;
// };
/**获取当前登录用户信息 */
const getAccount = () => {
  let account = principalStore.userIdentity;
  if (account) {
    data.repairManId = account.id;
    data.repairMan = account.name;
    data.repairTel = account.phone;
  }
};
/**上传后回调 */
const afterRead = async (event: any) => {
  // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
  let lists: Array<{ url: string; [key: string]: any }> = [].concat(event.file);
  let fileListLen = fileList.value.length;
  lists.map((item: any) => {
    fileList.value.push({ ...item, status: "uploading", message: "上传中" });
  });
  for (let i = 0; i < lists.length; i++) {
    const res: any = await uploadFile({
      url: "/Content/upload",
      filePath: lists[i].url,
      name: "file",
      formData: { type: "image" },
      onProgressUpdate: (e: any) => {
        // tempFiles.value[fileIndex].progress = e.progress
        let item = fileList.value[fileListLen];
        fileList.value.splice(fileListLen, 1, {
          ...item,
          status: "success",
          message: "",
          url: res.url,
        });
        fileListLen++;
      },
    });
    // const result: any = await uploadFilePromise(lists[i].url);
    // let item = fileList.value[fileListLen];
    // fileList.value.splice(fileListLen, 1, {
    //   ...item,
    //   status: "success",
    //   message: "",
    //   url: result.url,
    // });
    // fileListLen++;
  }
};
const uploadFilePromise = (url: string) => {
  return new Promise((resolve, reject) => {
    let a = uni.uploadFile({
      // url: "/api/Content/upload", // 仅为示例，非真实的接口地址
      url: "https://www.shenlaninfo.com:8047/api/Content/upload",
      filePath: url,
      name: "file",
      formData: {
        user: "test",
      },
      success: (res: any) => {
        console.log("uploadFilePromise", res, res.data, res.header);
        setTimeout(() => {
          // resolve(res.data.data);
          resolve(res.data);
        }, 1000);
      },
      fail: (err) => {
        console.log("请求失败", err.errMsg);

        reject(new Error(`请求失败: ${err.errMsg}`));
      },
    });
  });
};
/**删除图片 */
const deletePic = (event: any) => {
  fileList.value.splice(event.index, 1);
};
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  display: flex;
  flex-direction: column;
}

.form-card {
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  padding: 12px 16px;
  margin-bottom: 10px;

  .info {
    width: 100%;
    text-align: right;
    padding: 0px 8px;
  }

  .select {
    width: 100%;
  }

  :deep(.custom-select-container) {
    .select-display {
      height: 28px !important;
      justify-content: flex-end !important;

      label {
        padding-right: 8px;
      }
    }

    .dropdown-item {
      text-align: center;
    }
  }

  :deep(.u-form-item__body) {
    padding: 5px 0;
  }

  .input {
    width: 100%;
    height: 24px;
    border-radius: 3px;
    background: #f8f8f8;
    text-align: right;
    padding: 6px 8px;
  }

  .form-textarea {
    :deep(.u-form-item__body) {
      display: flex;
      flex-direction: column !important;
    }

    :deep(.u-form-item__body__left) {
      padding-bottom: 5px;
    }

    .textarea {
      width: 100%;
      height: 56px;
    }
  }
}

.upload-card {
  .upload-title {
    margin-bottom: 10px;
    color: #666666;
  }

  .upload-desc {
    font-size: 12px;
    color: #999;
    margin-bottom: 15px;
  }
}

.save-btn {
  width: 100%;
  position: absolute;
  bottom: 20px;
}
</style>
