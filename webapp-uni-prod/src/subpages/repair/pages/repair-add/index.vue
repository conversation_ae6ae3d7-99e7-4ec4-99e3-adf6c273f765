<template>
  <view class="container">
    <u-form
      labelPosition="left"
      :model="data"
      :rules="rules"
      ref="formRef"
      labelWidth="120"
      errorType="toast"
    >
      <view class="form-card">
        <u-form-item label="报修人员" prop="repairMan">
          <view class="info">{{ data.repairMan }}</view>
        </u-form-item>
        <u-form-item label="手机号码" prop="repairTel">
          <view class="info">{{ data.repairTel || "--" }}</view>
        </u-form-item>
        <u-form-item label="详细位置" prop="position" required>
          <input
            type="text"
            v-model="data.position"
            class="input"
            placeholder="请输入详细位置"
            :placeholder-style="`color: #999999;font-size:${toRpx(14)}`"
            :maxlength="10"
          />
        </u-form-item>
        <u-form-item label="紧急程度" prop="urgency" required>
          <SLSelect
            class="select"
            v-model="data.urgency"
            :options="urgencyOpt"
          />
        </u-form-item>
        <u-form-item
          label="报修内容"
          class="form-textarea"
          prop="repairContent"
          required
        >
          <SlTextarea
            class="textarea"
            v-model="data.repairContent"
            :showWordLimit="true"
            :maxlength="30"
          />
        </u-form-item>
      </view>
      <view class="form-card upload-card">
        <view class="upload-title">图片/视频</view>
        <UploadImage2
          v-model:contentList="data.contentList"
          v-model:ifAllDone="ifAllDone"
        ></UploadImage2>
      </view>
    </u-form>
    <view class="save-btn">
      <BaseButton
        btn-type="save"
        size="large"
        @click="onSave"
        :disabled="ifDisBtn"
        >提交</BaseButton
      >
    </view>
  </view>

  <StatePop
    v-model:show="showPopup"
    :title="statePopTitle"
    :type="statePopType"
    :confirmText="confirmText"
    :icon="statePopIcon"
    @confirm="onConfirm"
    @close="onConfirm"
  >
  </StatePop>
</template>

<script setup lang="ts">
import { UrgencyLevel, Repair } from "@/subpages/repair/models/Repair";
import repairService from "@/subpages/repair/service";
import { usePrincipalStore } from "@/store";
import useRepairStore from "@/subpages/repair/store";
import { useWxSubscribe } from "@/hooks";
import { saveUploadFile } from "@/service/upload.service";
import { toRpx } from "@/utils/toRpx";
/**获取用户信息 */
const principalStore = usePrincipalStore();
const repairStore = useRepairStore();
/**表单引用 */
const formRef = ref();
/**todo引入字典 */
const urgencyOpt = [
  { label: "高", value: UrgencyLevel.HIGH },
  { label: "中", value: UrgencyLevel.MEDIUM },
  { label: "低", value: UrgencyLevel.LOW },
];
/**表单数据 */
const data = reactive<Repair>({
  repairMan: "",
  repairTel: "",
  position: "",
  urgency: <UrgencyLevel>"",
  repairContent: "",
  contentList: [],
});
/**是否禁用提交按钮-避免重复提交 */
const ifDisBtn = ref<boolean>(false);
/**图片/视频是否完全上传完成-避免上传一半 */
const ifAllDone = ref<boolean>(false);
/**弹窗相关状态 */
const showPopup = ref(false);
const statePopTitle = ref("");
const statePopType = ref("");
const confirmText = ref("");
const statePopIcon = ref("");
/**表单验证规则 */
const rules = {
  position: [
    { required: true, message: "请输入详细位置", trigger: ["blur", "change"] },
  ],
  urgency: [{ required: true, message: "请选择紧急程度", trigger: "change" }],
  repairContent: [
    { required: true, message: "请输入报修内容", trigger: ["blur", "change"] },
  ],
};
onMounted(async () => {
  getAccount();
});
const onSave = () => {
  formRef.value
    .validate()
    .then(async (valid: any) => {
      if (valid) {
        try {
          if (!ifAllDone.value) {
            uni.showToast({ title: "图片/视频上传中，请稍等", icon: "none" });
            return;
          }
          ifDisBtn.value = true;
          /**保存之前先保存文件信息 */
          if (data.contentList && data.contentList.length) {
            await saveUploadFile(data.contentList);
          }
          await repairService.save(data);
          useWxSubscribe(["维修取消通知", "维修进度通知", "维修完成通知"], {
            forceSubscribe: true,
          }).finally(() => {
            statePopTitle.value = "报修成功";
            statePopType.value = "success";
            confirmText.value = "查看报修记录";
            statePopIcon.value = "40-40-3";
            showPopup.value = true;
            ifDisBtn.value = false;
          });
        } catch (error) {
          statePopTitle.value = "报修失败";
          statePopType.value = "error";
          confirmText.value = "返回";
          statePopIcon.value = "40-40-4";
          showPopup.value = true;
          ifDisBtn.value = false;
        }
      }
    })
    .catch((errors: any) => {
      // console.log("表单验证失败:", errors);
    });
};
const onConfirm = () => {
  showPopup.value = false;
  if (statePopType.value == "success") {
    repairStore.setNeedRefresh(true);
    uni.navigateBack();
  }
};
/**获取当前登录用户信息 */
const getAccount = () => {
  let account = principalStore.userIdentity;
  if (account) {
    data.repairManId = account.id;
    data.repairMan = account.name;
    data.repairTel = account.phone;
  }
};
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  display: flex;
  flex-direction: column;
}

.form-card {
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  padding: 12px 16px;
  margin-bottom: 10px;

  .info {
    width: 100%;
    text-align: right;
    padding: 0px 8px;
  }

  .select {
    width: 100%;
  }

  :deep(.custom-select-container) {
    .select-display {
      height: 32px !important;

      label {
        padding-right: 8px;
        text-align: right;
      }
    }

    .dropdown-item {
      text-align: center;
    }
  }

  :deep(.u-form-item__body) {
    padding: 5px 0;
  }
  :deep(.u-form-item__body__left__content__required) {
    color: #e50101;
    font-size: 14px;
    left: -9px;
    top: -4px;
  }
  :deep(.u-form-item__body__left__content__label) {
    color: #666666;
    font-size: 14px;
  }
  :deep(.u-form-item__body__right) {
    font-size: 14px;
  }

  .input {
    width: 100%;
    height: 24px;
    border-radius: 3px;
    background: #f8f8f8;
    text-align: right;
    padding: 6px 8px;
  }

  .form-textarea {
    :deep(.u-form-item__body) {
      display: flex;
      flex-direction: column !important;
    }

    :deep(.u-form-item__body__left) {
      padding-bottom: 5px;
    }

    .textarea {
      width: 100%;
      height: 56px;
    }
  }
}

.upload-card {
  .upload-title {
    margin-bottom: 10px;
    color: #666666;
  }

  .upload-desc {
    font-size: 12px;
    color: #999;
    margin-bottom: 15px;
  }
}

.save-btn {
  width: 100%;
  position: absolute;
  bottom: 20px;
}
</style>
