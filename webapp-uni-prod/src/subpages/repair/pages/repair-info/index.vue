<template>
  <view class="container" v-if="info && info.id">
    <!-- 信息面板 -->
    <view class="info-panel">
      <!-- 基本信息 -->
      <view class="info-section basic-info">
        <view class="info-list">
          <view
            class="info-item"
            v-for="item in basicInfoItems"
            :key="item.label"
          >
            <view class="info-label">{{ item.label }}</view>
            <view class="info-value" :class="item.class">
              {{ item.value }}
              <SlSubSvgIcon
                subpage="repair"
                v-if="!ifStaff && item.label === '手机号码'"
                class="phone-icon"
                :name="phoneIconClicked ? '24-24-3' : '24-24-2'"
                size="24"
                @click="onCall(item.value)"
              />
            </view>
          </view>
        </view>
      </view>
      <!-- 催办信息 -->
      <view class="info-section urge-info" v-if="info?.urgentContent">
        <view class="info-list">
          <view class="info-item">
            <view class="info-label">催办内容</view>
            <view class="info-value ellipsis">{{ info.urgentContent }}</view>
          </view>
          <view class="info-item" v-if="info.urgentTime">
            <view class="info-label">催办时间</view>
            <view class="info-value">{{ info.urgentTime }}</view>
          </view>
        </view>
      </view>
      <!-- 图片视频 -->
      <view class="info-section">
        <view class="info-list">
          <view class="info-item image-item">
            <view class="info-label">图片/视频</view>
            <view class="info-value">
              <RepairImages :contentList="info.contentList" />
            </view>
          </view>
        </view>
      </view>
      <!-- 处理/维修信息 -->
      <view class="info-section handle-info" v-if="handleInfoItems.length > 0">
        <image
          v-if="ifCompletedOrCancelled"
          class="image"
          :src="'/subpages/repair/static/' + info.repairStatus + '.svg'"
        />
        <view class="section-title">
          <view>{{ ifCompletedOrCancelled ? "维修信息" : "处理信息" }}</view>
          <button
            class="select-btn"
            @click="onGoFixMan"
            v-if="!ifStaff && !ifCompletedOrCancelled"
          >
            选择内部人员
          </button>
        </view>
        <view class="info-list">
          <!-- 表单指派人员 -->
          <template v-if="isAdminAndPending || isAdminAndProcessing">
            <u-form
              labelPosition="left"
              :model="formData"
              :rules="rules"
              ref="formRef"
              labelWidth="120"
            >
              <u-form-item label="报修人员" prop="fixMan" required>
                <input
                  type="text"
                  v-model="formData.fixMan"
                  class="input"
                  placeholder="请输入报修人员"
                  :maxlength="10"
                  @input="onValidate('fixMan')"
                />
              </u-form-item>
              <u-form-item label="手机号码" prop="fixManTel" required>
                <input
                  type="text"
                  v-model="formData.fixManTel"
                  class="input"
                  placeholder="请输入手机号码"
                  :minlength="11"
                  :maxlength="11"
                  @input="onValidate('fixManTel')"
                />
                <SlSubSvgIcon
                  subpage="repair"
                  class="phone-icon"
                  :name="phoneIconClicked ? '24-24-3' : '24-24-2'"
                  size="24"
                  @click="onCall(formData.fixManTel)"
                />
              </u-form-item>
            </u-form>
          </template>
          <template v-else>
            <view
              class="info-item"
              v-for="item in handleInfoItems"
              :key="item.label"
            >
              <view class="info-label">{{ item.label }}</view>
              <view
                class="info-value"
                :class="{ 'phone-value': item.isPhone, [item.class]: true }"
              >
                {{ item.value || "--" }}
                <SlSubSvgIcon
                  subpage="repair"
                  v-if="item.isPhone"
                  class="phone-icon"
                  :name="phoneIconClicked ? '24-24-3' : '24-24-2'"
                  size="24"
                  @click="onCall(item.value)"
                />
              </view>
            </view>
          </template>
        </view>
      </view>
      <!-- 按钮组 -->
      <view class="btn-box" v-if="actionButtons.length">
        <BaseButton
          v-for="btn in actionButtons"
          :key="btn.text"
          :btnType="btn.type"
          size="medium"
          @click="onBtn(btn.action)"
          :disabled="btn.action == 'urge' && ifDisUrge"
        >
          {{ btn.text }}
        </BaseButton>
      </view>
    </view>
  </view>
  <RepairConfirmDialog
    v-if="dialogData.id"
    @close="onConfirm(0)"
    @confirm="onConfirm(1)"
    :type="dialogData.type"
    :content="dialogData"
    class="confirm-dialog"
  />
</template>

<script setup lang="ts">
/**故障报修-报修详细 */
import { onLoad, onShow } from "@dcloudio/uni-app";
import {
  DialogType,
  getAssignStatus,
  Repair,
  RepairStatus,
  UrgencyLevel,
} from "@/subpages/repair/models/Repair";
import repairService from "@/service/repair";
import useRepairStore from "@/store/modules/repair";
import RepairConfirmDialog from "../../components/RepairConfirmDialog.vue";
import RepairImages from "../../components/RepairImages.vue";

const repairStore = useRepairStore();
const repairId = ref("");
const info = ref<Repair | null>(null);
const phoneIconClicked = ref(false);
/**表单引用 */
const formRef = ref();
const formData = reactive({ fixMan: "", fixManTel: "" });
const rules = {
  fixMan: [
    { required: true, message: "请输入维修人员", trigger: ["blur", "change"] },
  ],
  fixManTel: [
    { required: true, message: "请输入手机号码", trigger: ["blur", "change"] },
  ],
};
const dialogData = reactive({
  id: "",
  type: "" as DialogType,
  completeReason: "",
  content: "",
  loginUserRole: repairStore.ifStaff ? "workers" : "administrator",
});
const ifStaff = computed(() => repairStore.ifStaff);
/**状态计算属性 */
const ifPending = computed(
  () => info.value?.repairStatus === RepairStatus.PENDING
);
const ifProcessing = computed(
  () => info.value?.repairStatus === RepairStatus.PROCESSING
);
const ifCompleted = computed(
  () => info.value?.repairStatus === RepairStatus.COMPLETED
);
const ifCancelled = computed(
  () => info.value?.repairStatus === RepairStatus.CANCELLED
);
const ifCompletedOrCancelled = computed(
  () => ifCompleted.value || ifCancelled.value
);

/**角色和状态组合计算属性 */
const isStaffAndPending = computed(() => ifStaff.value && ifPending.value);
const isStaffAndProcessing = computed(
  () => ifStaff.value && ifProcessing.value
);
const isAdminAndPending = computed(() => !ifStaff.value && ifPending.value);
const isAdminAndProcessing = computed(
  () => !ifStaff.value && ifProcessing.value
);
/**是否禁用催办按钮：职员+待处理/处理中+催办时间 */
const ifDisUrge = computed(
  () =>
    !!(
      ifStaff.value &&
      info.value?.urgentTime &&
      (ifPending.value || ifProcessing.value)
    )
);
/**基本信息项 */
const basicInfoItems = computed(() => {
  const items = [];

  /**只有管理员端显示工单编号 */
  if (!ifStaff.value && info.value?.repairNumber) {
    items.push({ label: "工单编号", value: info.value.repairNumber });
  }

  if (info.value) {
    items.push(
      { label: "报修人员", value: info.value.repairMan },
      { label: "手机号码", value: info.value.repairTel },
      { label: "详细位置", value: info.value.position },
      {
        label: "紧急程度",
        value: getUrgencyLevelText(info.value.urgency),
        class: info.value.urgency,
      },
      {
        label: "报修状态",
        value: getStatusText(info.value.repairStatus),
        class: info.value.repairStatus,
      },
      { label: "报修时间", value: info.value.repairTime || "" },
      { label: "报修内容", value: info.value.repairContent, class: "ellipsis" }
    );
  }

  return items;
});

/**处理/维修信息项 */
const handleInfoItems = computed(() => {
  const items: any = [];

  if (!info.value) return items;

  /**职员待处理和处理中状态 */
  if (ifStaff.value && (ifPending.value || ifProcessing.value)) {
    items.push({
      label: "当前状态",
      value: getAssignStatus(info.value.assignStatus),
      class: "assign-status",
    });
    if (ifProcessing.value) {
      items.push(
        { label: "维修人员", value: info.value.fixMan },
        {
          label: "手机号码",
          value: info.value.fixManTel || "",
          isPhone: true,
        }
      );
    }
  }
  /**管理员端待处理和处理中状态 */
  if (!ifStaff.value && (ifPending.value || ifProcessing.value)) {
    items.push(
      { label: "维修人员", value: info.value.fixMan, place: "请输入维修人员" },
      {
        label: "手机号码",
        value: info.value.fixManTel || "",
        isPhone: true,
        place: "请输入手机号码",
      }
    );
  }

  /**已完结状态 */
  if (ifCompleted.value) {
    items.push(
      { label: "维修人员", value: info.value.fixMan || "" },
      {
        label: "手机号码",
        value: info.value.fixManTel || "",
        isPhone: !!info.value.fixManTel,
      },
      { label: "完结时间", value: info.value.completeTime || "" },
      { label: "结束原因", value: info.value.completeReason || "" },
      { label: "备注", value: info.value.remarks || "", class: "ellipsis" }
    );
  }

  /**已取消状态 */
  if (ifCancelled.value) {
    items.push(
      { label: "取消人员", value: info.value.cancelMan || "" },
      { label: "取消时间", value: info.value.cancelTime || "" },
      { label: "取消原因", value: info.value.cancelReason || "" }
    );
  }

  return items;
});

/**按钮组 */
const actionButtons: any = computed(() => {
  const buttons = [];

  /**职工端按钮 */
  if (ifStaff.value) {
    if (ifPending.value || ifProcessing.value) {
      buttons.push(
        { text: "取消报修", type: "cancel", action: "cancel" },
        { text: "催办", type: "save", action: "urge" }
      );
    }
  } else {
    /**管理员端按钮 */
    if (ifPending.value) {
      buttons.push(
        { text: "取消报修", type: "cancel", action: "cancel" },
        { text: "指派", type: "save", action: "assign" }
      );
    } else if (ifProcessing.value) {
      buttons.push(
        { text: "重新指派", type: "save", action: "reassign" },
        { text: "结束任务", type: "complete", action: "complete" }
      );
    }
  }

  return buttons;
});
onLoad((options: any) => {
  if (options.id) {
    repairId.value = options.id;
    getInfo(repairId.value);
  }
});
onShow(() => {
  const { name, phone } = repairStore.fixManInfo;
  if (name && phone) {
    formData.fixMan = name;
    formData.fixManTel = phone;
    if (info.value) {
      info.value.fixMan = name;
      info.value.fixManTel = phone;
    }
    /**清空 store 中的信息，避免下次进入页面时仍然使用旧数据 */
    repairStore.setFixMan({ id: "", name: "", phone: "", position: "" });
  }
});
/** 0关闭 1确定 */
const onConfirm = async (type: 0 | 1) => {
  if (type) {
    if (dialogData.type === "urge") {
      await urgeRepair();
    } else if (dialogData.type === "cancel") {
      await cancelRepair();
    } else if (dialogData.type === "complete") {
      await completeRepair();
    }
  }
  resetDialog();
};
const urgeRepair = async () => {
  let { id, content } = dialogData;
  let data: Repair = { id, urgentContent: content };
  try {
    uni.showToast({ title: "催办成功", icon: "none" });
    await repairService.urgeRepair(data);
    goToList();
    resetDialog();
  } catch (error) {
    uni.showToast({ title: "操作失败，请重试", icon: "none" });
  }
};
const cancelRepair = async () => {
  let { id, content, loginUserRole } = dialogData;
  let data: Repair = { id, cancelReason: content, loginUserRole };
  try {
    uni.showToast({ title: "取消成功", icon: "none" });
    await repairService.cancelRepair(data);
    goToList();
    resetDialog();
  } catch (error) {
    uni.showToast({ title: "操作失败，请重试", icon: "none" });
  }
};
const completeRepair = async () => {
  let { id, content, completeReason } = dialogData;
  let data: Repair = { id, completeReason, remarks: content };
  try {
    uni.showToast({ title: "结束成功", icon: "none" });
    await repairService.completeRepair(data);
    goToList();
    resetDialog();
  } catch (error) {
    uni.showToast({ title: "操作失败，请重试", icon: "none" });
  }
};
const resetDialog = () => {
  dialogData.id = "";
  dialogData.type = "cancel";
  dialogData.content = "";
  dialogData.completeReason = "";
};
/**报修详情 */
const getInfo = async (id: string) => {
  try {
    const result = await repairService.getInfo(id);
    info.value = result;
    formData.fixMan = info.value.fixMan || "";
    formData.fixManTel = info.value.fixManTel || "";
  } catch (error) {
    console.error("获取报修详情失败", error);
    uni.showToast({
      title: "加载失败，请重试",
      icon: "none",
    });
  }
};
/**点击按钮 */
const onBtn = (action: string) => {
  switch (action) {
    case "cancel":
      onCancelOrStop("cancel");
      break;
    case "urge":
      onUrge();
      break;
    case "assign":
      onAssign("assigned");
      break;
    case "reassign":
      onAssign("reassigned");
      break;
    case "complete":
      onCancelOrStop("complete");
      break;
    default:
      break;
  }
};
/**拨打电话 */
const onCall = (phone: string) => {
  if (!phone) return;
  phoneIconClicked.value = true;
  uni.makePhoneCall({ phoneNumber: phone });
  setTimeout(() => {
    phoneIconClicked.value = false;
  }, 300);
};
/**打开维修人员列表 */
const onGoFixMan = () => {
  uni.navigateTo({ url: "/subpages/repair/pages/fixman-list/index" });
};
/**实时校验字段 */
const onValidate = (field: string) => {
  if (formRef.value) {
    formRef.value.validateField(field);
  }
};
/**取消报修|结束任务 */
const onCancelOrStop = async (type: DialogType) => {
  let { id = "" } = info.value || {};
  dialogData.id = id;
  dialogData.type = type;
};
/**催办 */
const onUrge = async () => {
  let { id = "", urgentTime } = info.value || {};
  if (urgentTime) return;
  /**打开内容弹窗填写催办内容 */
  dialogData.id = id;
  dialogData.type = "urge";
};
/**指派/重新指派 */
const onAssign = async (status: string) => {
  formRef.value
    .validate()
    .then(async (valid: any) => {
      if (valid && info.value) {
        try {
          info.value.assignStatus = status;
          info.value.fixMan = formData.fixMan;
          info.value.fixManTel = formData.fixManTel;
          const { id = "", fixMan, fixManTel, assignStatus } = info.value;
          let data = { id, fixMan, fixManTel, assignStatus };
          await repairService.assignRepair(data);
          uni.showToast({ title: "指派成功", icon: "none" });
          goToList();
          /**刷新当前页-点击返回列表时要列表位刷新 */
          // getInfo(id);
        } catch (error) {
          uni.showToast({ title: "指派失败，请重试", icon: "none" });
        }
      }
    })
    .catch((errors: any) => {
      console.log("表单验证失败:", errors);
    });
};
/**跳转列表页 */
const goToList = () => {
  uni.navigateTo({ url: "/subpages/repair/pages/index" });
};
/**获取紧急程度文本 */
const getUrgencyLevelText = (level?: UrgencyLevel) => {
  switch (level) {
    case UrgencyLevel.LOW:
      return "低";
    case UrgencyLevel.MEDIUM:
      return "中";
    case UrgencyLevel.HIGH:
      return "高";
    default:
      return "";
  }
};
/**获取状态文本 */
const getStatusText = (repairStatus?: RepairStatus) => {
  switch (repairStatus) {
    case RepairStatus.PENDING:
      return "待处理";
    case RepairStatus.PROCESSING:
      return "处理中";
    case RepairStatus.COMPLETED:
      return "已完结";
    case RepairStatus.CANCELLED:
      return "已取消";
    default:
      return "";
  }
};
</script>

<style lang="scss" scoped>
$pending: #ff7605;
$processing: #005cc8;
$completed: #5b851b;
$default: #333333;

.container {
  background-color: #f4f6fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.info-panel {
  flex: 1;
}

.info-section {
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  padding: 8px 14px;
  margin-bottom: 8px;

  &.handle-info {
    position: relative;

    .image {
      width: 48px;
      height: 42px;
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 2;
    }
  }
}

.section-title {
  height: 30px;
  line-height: 30px;
  font-weight: 500;
  color: #333;
  padding-left: 6px;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 11px;
    background: #1a58b2;
    border-radius: 2px;
  }

  .select-btn {
    height: 28px;
    line-height: 28px;
    padding: 0 12px;
    border-radius: 3px;
    background: #ffffff;
    border: 1px solid #4f7af6;
    margin: unset;
    color: #4f7af6;
  }
}

.info-list {
  display: flex;
  flex-direction: column;

  :deep(.u-form-item__body) {
    padding: 5px 0;
  }

  .input {
    width: 100%;
    height: 28px;
    border-radius: 3px;
    background: #f8f8f8;
    border: none;
    text-align: right;
    padding: 0px 15px;
    margin-left: 20px;
  }
}

.info-item {
  min-height: 28px;
  margin: 3px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:last-child {
    border-bottom: none;
  }

  &.image-item {
    flex-direction: column;
    align-items: unset;
    .info-value {
      justify-content: unset;
    }
    :deep(.repair-images) {
      margin-top: 10px;
      margin-bottom: 0px;
    }
  }
}

.info-label {
  width: 80px;
  color: #666;
}

.info-value {
  flex: 1;
  color: #333;
  word-break: break-all;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  &.ellipsis {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: right;
  }

  &.assign-status{
    color: #0B825E;
  }

  &.low,
  &.medium,
  &.high {
    flex: unset;
    padding: 0px 6px;
    border-radius: 2px;
  }

  &.low {
    background: #fff7d4;
    color: #ad8d00;
  }

  &.medium {
    background: #fde6ca;
    color: #e27600;
  }

  &.high {
    background: #ffdfde;
    color: #c93535;
  }

  &.pending {
    color: $pending;
  }

  &.processing {
    color: $processing;
  }

  &.completed {
    color: $completed;
  }

  &.cancelled {
    color: $default;
  }
}

.phone-value {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.phone-icon {
  width: 24px;
  height: 24px;
  margin-left: 10px;
}

.btn-box {
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: space-around;
  margin: 15px 0;
  // position: sticky;
  // bottom: 28px;
  // left: 0;
  // right: 0;
  :deep(.btn-complete) {
    background: #2191b3;
  }
}
.confirm-dialog {
  :deep(.sl-textarea) {
    height: 74px !important;
  }
  :deep(.dialog-box) {
    padding-top: 10px;
  }
  :deep(.dialog-actions) {
    margin-top: unset;
  }
  :deep(.select-display) {
    height: 32px !important;
  }
}
</style>
