<template>
  <SlTabbarPage
    :tab-bar-root="'subpages/repair'"
    press-back-path="/"
    :navigationBarTitleText="navigationBarTitleText"
  >
    <view class="container">
      <!-- 搜索区域 -->
      <view class="search">
        <view class="search-box" :class="{ admin: !ifStaff }">
          <up-input
            placeholder="请输入关键词"
            :placeholder-style="`font-size:${toRpx(12)}`"
            border="surround"
            v-model="searchParams.keyword"
            @change="getList(true)"
          ></up-input>
          <SlSubSvgIcon
            subpage="repair"
            v-if="searchParams.keyword"
            class="clear"
            name="12-12-10"
            size="12"
            @click="onClear"
          />
          <SlSubSvgIcon
            subpage="repair"
            name="16-16-6"
            size="16"
            @click="getList(true)"
          />
        </view>
        <SLSelect
          v-if="!ifStaff"
          class="select"
          :placeholder="'报修人员'"
          :shouldRotate="true"
          v-model="searchParams.repairManId"
          :options="repairManOpt"
          :ifClear="true"
          @change="getList(true)"
        />
      </view>

      <!-- 标签页 -->
      <view class="tabs">
        <SlTabs :list="tabs" v-model="curTab" @click="OnTab"></SlTabs>
      </view>

      <!-- 报修列表 -->
      <scroll-view
        class="repair-list-container"
        scroll-y
        @scrolltolower="onMore"
        @refresherrefresh="getList(true)"
        refresher-enabled
        :refresher-triggered="refreshing"
      >
        <view
          v-if="repairList.length > 0"
          class="repair-list"
          :class="{ staff: ifStaff }"
        >
          <view
            v-for="item in repairList"
            :key="item.id"
            class="repair-item"
            @click="onItem(item.id)"
          >
            <!-- 报修项头部 -->
            <view class="repair-header">
              <view
                class="header-status"
                :class="'status-' + item.repairStatus"
              >
                {{ getStatusText(item.repairStatus) }}
              </view>
              <view class="header-time">{{ getTime(item) }}</view>
            </view>
            <!-- 报修内容 -->
            <view class="repair-content">
              <view class="repair-info">
                <view class="info-item" v-if="!ifStaff">
                  <text class="info-label">工单编号</text>
                  <text class="info-value">{{ item.repairNumber }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">报修人员</text>
                  <text class="info-value">{{ item.repairMan }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">紧急程度</text>
                  <text
                    class="urgency-level"
                    :class="`urgency-${item.urgency}`"
                  >
                    {{ getUrgencyText(item.urgency) }}
                  </text>
                </view>
                <view class="info-item">
                  <text class="info-label">报修位置</text>
                  <text class="info-value">{{ item.position }}</text>
                </view>
                <view class="info-item" style="align-items: flex-start">
                  <text class="info-label">报修内容</text>
                  <text class="info-value ellipsis">{{
                    item.repairContent || "--"
                  }}</text>
                </view>
              </view>

              <!-- 报修图片 -->
              <SlMediaViewer :contentList="item.contentList" />
            </view>

            <!-- 报修底部操作区 -->
            <view
              class="repair-footer"
              v-if="
                !(
                  !ifStaff &&
                  (item.repairStatus === 'completed' ||
                    item.repairStatus === 'cancelled') &&
                  !item.urgentTime
                )
              "
            >
              <view
                class="urge-content"
                :class="{
                  'urge-content2':
                    item.repairStatus === 'pending' ||
                    item.repairStatus === 'processing',
                }"
                v-if="item.urgentTime && !ifStaff"
              >
                <view>催办内容</view>
                <view class="ellipsis">{{ item.urgentContent || "--" }}</view>
              </view>
              <view class="desc-btns">
                <view style="width: 100%">
                  <template v-if="ifStaff">
                    <view
                      class="info-item desc"
                      v-if="item.repairStatus === 'processing'"
                    >
                      <view class="info-label">已指派给</view>
                      <view class="info-value">
                        <view
                          ><text style="margin-left: unset">{{
                            item.fixMan
                          }}</text>
                          <text>{{ item.fixManTel }}</text></view
                        >
                        <view @click.stop>
                          <SlSubSvgIcon
                            subpage="repair"
                            class="phone-icon"
                            :name="curId === item.id ? '24-24-3' : '24-24-2'"
                            size="24"
                            @click="onCall(item)"
                          />
                        </view>
                      </view>
                    </view>
                    <view
                      class="info-item desc"
                      v-else-if="item.repairStatus === 'completed'"
                    >
                      <view class="info-label">结束原因</view>
                      <view class="info-value">{{
                        item.completeReason || "--"
                      }}</view>
                    </view>
                    <view
                      class="info-item desc"
                      v-else-if="item.repairStatus === 'cancelled'"
                    >
                      <view class="info-label">取消原因</view>
                      <view class="info-value">{{
                        item.cancelReason || "--"
                      }}</view>
                    </view>
                  </template>
                </view>
                <view class="repair-btns">
                  <!-- 待处理状态的操作按钮 -->
                  <template v-if="item.repairStatus === 'pending'">
                    <button
                      class="cancel"
                      @click.stop="onBtn(item.id, 'cancel')"
                    >
                      取消报修
                    </button>
                    <button
                      v-if="ifStaff"
                      class="urge"
                      :class="{ enabled: item.urgentTime }"
                      @click.stop="onUrge(item)"
                    >
                      催办
                    </button>
                    <button v-else class="urge" @click.stop="onAssign(item.id)">
                      指派
                    </button>
                  </template>
                  <!-- 处理中状态的操作按钮 -->
                  <template v-else-if="item.repairStatus === 'processing'">
                    <!-- 职工端显示 电话图标 点击跳转微信拨打页面 -->
                    <template v-if="ifStaff"></template>
                    <!-- 管理员端 -->
                    <template v-else>
                      <button class="urge" @click.stop="onReassign(item.id)">
                        重新指派
                      </button>
                      <button
                        class="stop"
                        @click.stop="onBtn(item.id, 'complete')"
                      >
                        结束任务
                      </button>
                    </template>
                  </template>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <uni-load-more
          v-if="repairList.length > 0"
          :status="loading ? 'loading' : hasMore ? 'loadmore' : 'nomore'"
          :icon-size="16"
          :content-text="{
            contentdown: '上拉加载更多',
            contentrefresh: '加载中...',
            contentnomore: '没有更多数据了',
          }"
        />

        <!-- 空列表提示 -->
        <view v-if="!loading && repairList.length === 0" class="empty-list">
          <BaseEmpty />
        </view>
      </scroll-view>

      <!-- 底部按钮 -->
      <view class="add-btn" v-if="ifStaff">
        <BaseButton btn-type="save" size="large" @click="onAdd"
          >我要报修</BaseButton
        >
      </view>
    </view>
    <RepairConfirmDialog
      v-if="dialogData.id"
      @close="onConfirm(0)"
      @confirm="onConfirm(1)"
      :type="dialogData.type"
      :content="dialogData"
      class="confirm-dialog"
    />
  </SlTabbarPage>
</template>

<script setup lang="ts">
/**报修-列表 */
import repairService from "@/subpages/repair/service";
import {
  DialogType,
  Repair,
  RepairStatus,
  UrgencyLevel,
} from "@/subpages/repair/models/Repair";
import RepairConfirmDialog from "../components/RepairConfirmDialog.vue";
import { useDebounce, usePermission } from "@/hooks";
import useRepairStore from "@/subpages/repair/store";
import { useWxSubscribe } from "@/hooks";
import { useWxSubStore } from "@/store";
import { addSub } from "@/service/sub-message.service";
import { toRpx } from "@/utils/toRpx";
const repairStore = useRepairStore();
/**是否为职工端 */
const ifStaff = ref(true);
const curTab = ref("pending");
let curId = ref<string>("");
const dialogData = reactive({
  id: "",
  type: "" as DialogType,
  completeReason: "",
  content: "",
  loginUserRole: ifStaff.value ? "workers" : "administrator",
});
const tabs = reactive([
  { value: RepairStatus.PENDING, name: "待处理" },
  { value: RepairStatus.PROCESSING, name: "处理中" },
  { value: RepairStatus.COMPLETED, name: "已完结" },
  { value: RepairStatus.CANCELLED, name: "已取消" },
]);
/**分页信息 */
let pagination = reactive({ currentPage: 1, pageRecord: 10, recordCount: 0 });
/**加载状态 */
let loading = ref(false);
/**是否正在刷新 */
let refreshing = ref(false);
/**是否还有更多数据 */
let hasMore = ref(false);
let repairList = reactive([] as Repair[]);
interface Option {
  label: string;
  value: string | number;
}
let repairManOpt = ref([] as Option[]);
const wxSubStore = useWxSubStore();
let templates = wxSubStore.templates;
/**需要保持状态的字段 */
const searchParams = reactive({ keyword: "", repairManId: "" });
/**loginUserRole 职工-workers 管理员-administrator*/
const search = computed(() => ({
  loginUserRole: ifStaff.value ? "workers" : "administrator",
  repairStatus: curTab.value,
  keyword: searchParams.keyword,
  repairManId: searchParams.repairManId,
  currentPage: pagination.currentPage,
  pageRecord: pagination.pageRecord,
  ifPage: true,
}));
const navigationBarTitleText = ref<string>();
onLoad((query: AnyObject | undefined) => {
  initPage(query);
});
onShow(() => {
  if (repairStore.needRefresh) {
    getList(true);
    repairStore.setNeedRefresh(false);
  }
});
onUnload(() => {
  repairStore.setNeedRefresh(true);
});
const initPage = (query: AnyObject | undefined) => {
  ifStaff.value = query?.type !== "admin";
  curTab.value = query?.status || "pending";
  /**动态配置navigationBarTitleText */
  // uni.setNavigationBarTitle({
  //   title: ifStaff.value ? "故障报修" : "报修管理",
  // });
  navigationBarTitleText.value = ifStaff.value ? "故障报修" : "报修管理";
  if (!ifStaff.value) {
    getRepairMan();
    useWxSubscribe(["报修派单通知", "报修催办提醒", "维修取消通知"])
      .then((res) => {
        for (const e of res) {
          let item = templates.find((item) => item.wechatTemplateId === e);
          if (item && item.id) {
            addSub(item.id);
          }
        }
      })
      .finally(() => {
        getSubRemind();
      });
  }
};
const onClear = () => {
  searchParams.keyword = "";
  getList(true);
};
const OnTab = (tab: any) => {
  getList(true);
};
/**新增报修 */
const onAdd = () => {
  uni.navigateTo({ url: "/subpages/repair/pages/repair-add/index" });
};
/** 0关闭 1确定 */
const onConfirm = async (type: 0 | 1) => {
  if (type) {
    if (dialogData.type === "urge") {
      await urgeRepair();
    } else if (dialogData.type === "cancel") {
      await cancelRepair();
    } else if (dialogData.type === "complete") {
      await completeRepair();
    }
  }
  resetDialog();
};
/**取消报修|结束任务 */
const onBtn = async (id: string = "", type: DialogType) => {
  dialogData.id = id;
  dialogData.type = type;
  dialogData.loginUserRole = ifStaff.value ? "workers" : "administrator";
};
/**催办 */
const onUrge = async (data: Repair) => {
  let { id = "", urgentTime } = data;
  if (urgentTime) return;
  /**打开内容弹窗填写催办内容 */
  dialogData.id = id;
  dialogData.type = "urge";
};
/**指派处理人 */
const onAssign = (id: string = "") => {
  onItem(id);
};
/**重新指派 */
const onReassign = (id: string = "") => {
  onItem(id);
};
/**加载更多 */
const onMore = () => {
  if (loading.value || !hasMore.value) return;
  loadNextPage();
  getList();
};
/**拨打电话 */
const onCall = (item: Repair) => {
  const { fixManTel: phone } = item;
  if (!phone) return;
  curId.value = item.id || "";
  uni.makePhoneCall({ phoneNumber: phone });
  repairStore.setNeedRefresh(false);
  setTimeout(() => {
    curId.value = "";
  }, 300);
};
/**跳转详情 */
const onItem = (id: string = "") => {
  uni.navigateTo({
    url: `/subpages/repair/pages/repair-info/index?type=${
      ifStaff.value ? "staff" : "admin"
    }&id=${id}`,
  });
};
const getRepairMan = async () => {
  const res = (await repairService.getRepairManList()) as {
    repairManId: string;
    repairMan: string;
  }[];
  repairManOpt.value =
    res.map((item) => ({
      label: item.repairMan,
      value: item.repairManId,
    })) || [];
};
const getSubRemind = async () => {
  let res = await repairService.getRepairTemplate();
  let reMind = res.map((item: any) => item.name).join("、");
  if (!reMind.length) return;
  uni.showModal({
    title: "订阅提醒",
    content: `您的[${reMind}]订阅提醒次数即将用完，请前往增加提醒次数`,
    confirmText: "前往",
    success: (res) => {
      if (res.confirm) {
        uni.navigateTo({ url: "/subpages/sub-message/pages/index" });
      }
    },
  });
};
/**获取报修列表 */
const getList = useDebounce(async (refresh = false) => {
  if (refresh) {
    setRefreshing(true);
    pagination.currentPage = 1;
    search.value.currentPage = 1;
    hasMore.value = true;
    repairList = [];
  } else {
    setLoading(true);
  }

  try {
    const res = await repairService.getList(search.value);
    setRepairList(res.result || [], refresh);
    setPagination({
      recordCount: res.recordCount,
      currentPage: res.currentPage,
    });
    res;
  } finally {
    setLoading(false);
    setRefreshing(false);
    /**停止下拉刷新 */
    uni.stopPullDownRefresh();
  }
}, 500);
/**设置加载状态 */
const setLoading = (ifload: boolean) => {
  loading.value = ifload;
};
/**设置刷新状态 */
const setRefreshing = (ifRefreshing: boolean) => {
  refreshing.value = ifRefreshing;
};
/**设置报修列表数据 */
const setRepairList = (list: Repair[], replace = false) => {
  if (replace) {
    repairList = list;
  } else {
    repairList = [...repairList, ...list];
  }
};
/**设置分页信息 */
const setPagination = (pagination: {
  currentPage?: number;
  recordCount?: number;
}) => {
  if (pagination.currentPage !== undefined) {
    pagination.currentPage = pagination.currentPage;
    search.value.currentPage = pagination.currentPage;
  }
  if (pagination.recordCount !== undefined) {
    pagination.recordCount = pagination.recordCount;
    hasMore.value = repairList.length < pagination.recordCount;
  }
};
/**加载下一页 */
const loadNextPage = () => {
  if (!hasMore.value || loading.value) return;
  pagination.currentPage += 1;
  search.value.currentPage = pagination.currentPage;
};
const urgeRepair = async () => {
  let { id, content } = dialogData;
  let data: Repair = { id, urgentContent: content };
  await repairService.urgeRepair(data);
  getList(true);
  resetDialog();
};
const cancelRepair = async () => {
  let { id, content, loginUserRole } = dialogData;
  let data: Repair = { id, cancelReason: content, loginUserRole };
  await repairService.cancelRepair(data);
  getList(true);
  resetDialog();
};
const completeRepair = async () => {
  let { id, content, completeReason } = dialogData;
  let data: Repair = { id, completeReason, remarks: content };
  await repairService.completeRepair(data);
  getList(true);
  resetDialog();
};
const resetDialog = () => {
  dialogData.id = "";
  dialogData.type = "cancel";
  dialogData.content = "";
  dialogData.completeReason = "";
};
/**获取状态文本和样式 */
const getStatusText = (status: RepairStatus = RepairStatus.PENDING) => {
  const statusMap = {
    [RepairStatus.PENDING]: "待处理",
    [RepairStatus.PROCESSING]: "处理中",
    [RepairStatus.COMPLETED]: "已完结",
    [RepairStatus.CANCELLED]: "已取消",
  };
  return statusMap[status] || "未知";
};
/**获取紧急程度文本 */
const getUrgencyText = (level: UrgencyLevel = UrgencyLevel.LOW) => {
  const textMap = {
    [UrgencyLevel.LOW]: "低",
    [UrgencyLevel.MEDIUM]: "中",
    [UrgencyLevel.HIGH]: "高",
  };
  return textMap[level] || "未知";
};
/**根据状态获取显示的时间 */
const getTime = (item: Repair) => {
  const status = item.repairStatus as RepairStatus;
  const timeMap: Record<RepairStatus, keyof Repair> = {
    [RepairStatus.PENDING]: "repairTime",
    [RepairStatus.PROCESSING]: "assignTime",
    [RepairStatus.COMPLETED]: "completeTime",
    [RepairStatus.CANCELLED]: "cancelTime",
  };
  const key = timeMap[status];
  return key && item[key] ? (item[key] as string) : "";
};
</script>

<style lang="scss" scoped>
$pending: #ff7605;
$processing: #196ad0;
$completed: #21843b;
$default: #333333;
$label: #666666;
$primary: #0066df;

.ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.container {
  height: 100%;
  background-color: #f5f5f5;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 顶部搜索区域 */
.search {
  height: 48px;
  padding: 10px 15px;
  background: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .search-box {
    width: 290px;
    height: 30px;
    padding-right: 12px;
    border-radius: 3px;
    font-size: 12px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid rgba(153, 153, 153, 0.2);
    display: flex;
    align-items: center;
    .clear {
      margin: 0px 8px;
    }
    :deep(.u-input) {
      border: none;
    }
    :deep(.u-input__content__field-wrapper__field) {
      font-size: 12px !important;
    }
    &.admin {
      width: 170px;
    }
  }
  :deep(.select-display) {
    width: 110px;
    height: 30px !important;
    font-size: 12px;
    border-radius: 3px !important;
    border: 1px solid rgba(153, 153, 153, 0.2) !important;
  }
  :deep(.custom-select-container .placeholder) {
    font-size: 12px;
  }
}

/* tabs区域 */
.tabs {
  height: 38px;
}

/* 列表区域 */
.repair-list-container {
  height: 50%;
  flex-grow: 1;
  padding-bottom: 20px;
  overflow: hidden;

  .empty-list {
    height: 100%;
    padding: 40px 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .repair-list {
    padding: 10px 15px 0px;

    &.staff {
      padding: 10px 15px 50px;
    }
  }

  .repair-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    .repair-header {
      padding: 10px 15px 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-status {
        padding: 2px 8px;
        border-radius: 2px;
      }

      .status-pending {
        background: #fff0e4;
        border: 1px solid #ffe0c5;
        color: $pending;
      }

      .status-processing {
        background: #e4f7ff;
        border: 1px solid #c5e6ff;
        color: $processing;
      }

      .status-completed {
        background: #eeffee;
        border: 1px solid #c2e9aa;
        color: $completed;
      }

      .status-cancelled {
        background: #f1f1f1;
        border: 1px solid #c5c5c5;
        color: $default;
      }

      .header-time {
        font-size: 12px;
        color: $label;
      }
    }

    .repair-content {
      padding: 0px 15px;
      display: flex;
      flex-direction: column;
    }

    .repair-info {
      margin: 5px 0px;
      color: $default;
      font-size: 12px;
    }

    .info-item {
      width: 100%;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      .info-label {
        width: 70px;
        color: $label;
      }

      .info-value {
        width: 50%;
        flex-grow: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .urgency-level {
      padding: 2px 6px;
      border-radius: 2px;
      font-size: 12px;

      &.urgency-low {
        background: #fff7d4;
        color: #ad8d00;
      }

      &.urgency-medium {
        background: #fde6ca;
        color: #e27600;
      }

      &.urgency-high {
        background: #ffdfde;
        color: #c93535;
      }
    }

    .repair-footer {
      border-top: 1px dashed #f5f5f5;
      background: #f8fbff;
      padding: 10px 15px;

      .urge-content {
        display: flex;
        font-size: 12px;
        &.urge-content2 {
          margin-bottom: 8px;
        }

        view:first-child {
          width: 70px;
          color: $label;
        }

        view:last-child {
          width: 50%;
          flex-grow: 1;
        }
      }

      .desc-btns {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .desc {
        color: $label;
        font-size: 12px;
        align-items: center;
        margin-bottom: unset;

        .phone-icon {
          margin-left: 10px;
        }
      }

      .desc text {
        margin-left: 5px;
        color: #333333;
      }

      .repair-btns {
        display: flex;
        justify-content: flex-end;

        button {
          height: 28px;
          line-height: 28px;
          border-radius: 3px;
          text-align: center;
          margin-left: 15px;
          white-space: nowrap;
          font-size: 14px;
          /**去掉微信按钮自带的伪元素边框 */
          &:after {
            border: none;
          }
        }

        .urge {
          background: $primary;
          color: #ffffff;
          &:active {
            background: #4186fd;
          }
          &.enabled {
            background: rgba(153, 153, 153, 0.8);
            &:active {
              background: rgba(153, 153, 153, 0.8);
            }
          }
        }

        .cancel {
          color: $label;
          background: #ffffff;
          border: 1px solid #999999;
        }

        .stop {
          background: #2191b3;
          color: #ffffff;
          &:active {
            color: #19a3cd;
          }
        }
      }
    }
  }
}

/* 底部按钮 */
.add-btn {
  width: 100%;
  height: 70px;
  border-radius: 10px 10px 0px 0px;
  background: rgba(255, 255, 255, 0.7);
  box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);
  position: absolute;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-dialog {
  :deep(.sl-textarea) {
    height: 74px !important;
  }
  :deep(.dialog-box) {
    padding-top: 10px;
  }
  :deep(.dialog-content) {
    padding: 20px 12px;
  }
  :deep(.dialog-actions) {
    margin-top: unset;
  }
  :deep(.select-display) {
    height: 32px !important;
  }
}
</style>
