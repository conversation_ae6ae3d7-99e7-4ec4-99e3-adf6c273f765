<template>
  <view class="video-player-page">
    <video
      id="fullscreenVideo"
      :src="videoUrl"
      class="full-video"
      autoplay
      controls
      object-fit="contain"
      @fullscreenchange="fullscreenchange"
    ></video>
    <view class="back-btn" @click.stop="goBack">×</view>
  </view>
</template>

<script setup lang="ts">
import { onLoad, onReady } from "@dcloudio/uni-app";
import { ref } from "vue";

const videoUrl = ref("");
let videoContext: any = null;
onLoad((options = {}) => {
  if (options.url) {
    videoUrl.value = decodeURIComponent(options.url);
  }
});

onReady(() => {
  /**自动进入全屏 */
  videoContext = uni.createVideoContext("fullscreenVideo", this);
  requestFullScreen();
});

/**全屏*/
const requestFullScreen = () => {
  try {
    videoContext.requestFullScreen({ direction: 0 });
  } catch (e) {
    console.error("全屏请求失败", e);
  }
};
/**监听全屏状态变化 */
const fullscreenchange = (e: any) => {
  /**如果退出全屏，返回上一页 */
  if (!e.detail.fullScreen) {
    goBack();
  }
};

const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.video-player-page {
  width: 100vw;
  height: 100vh;
  background-color: #000;
  position: relative;
}

.full-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.back-btn {
  width: 40px;
  height: 40px;
  line-height: 40px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  position: absolute;
  top: 30px;
  right: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  z-index: 10000;
}
</style>
