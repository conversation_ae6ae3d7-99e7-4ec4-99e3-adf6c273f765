/**物业服务相关数据模型 */
import { Content } from "@/models/Content";
import { PageSearch } from "@/models/Page";


/**报修状态 */
export enum RepairStatus {
  /**待处理 */
  PENDING = 'pending',
  /**处理中 */
  PROCESSING = 'processing',
  /**已完结 */
  COMPLETED = 'completed',
  /**已取消 */
  CANCELLED = 'cancelled'
}

/**紧急程度 */
export enum UrgencyLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

/**报修项目 */
export interface Repair {
  /**报修ID */
  id?: string;
  /**报修单编号 */
  repairNumber?: string;
  /**报修人员姓名 */
  repairMan?: string;
  /**报修人电话 */
  repairTel?: string;
  /**报修位置 */
  position?: string;
  /**报修状态 */
  repairStatus?: RepairStatus;
  /**紧急程度 */
  urgency?: UrgencyLevel;
  /**报修内容 */
  repairContent?: string;
  /**报修时间 */
  repairTime?: string;
  /**已提交时长（处理中状态显示） */
  duration?: string;
  /**报修图片/视频 */
  images?: string[];
  /**处理人ID */
  handlerId?: string;
  /**指派时间 ！！！！！！！*/
  assignTime?: string;
  /**指派状态(待指派-assigning、已指派-assigned、已重新指派-reassigned) */
  assignStatus?: string;
  /**备注 */
  remarks?: string;
  /**催办状态 催办-urging、已催办-urged*/
  urgentStatus?: string;
  /**催办内容 */
  urgentContent?: string;
  /**催办时间 */
  urgentTime?: string;
  /**维修人员 */
  fixMan?: string;
  /**维修人员id */
  repairManId?: string;
  /**维修人员手机号码 */
  fixManTel?: string;
  /**完结时间 */
  completeTime?: string;
  /**完结原因(已解决、无法处理、耗材不够，需要备件、其他) */
  completeReason?: string;
  /**取消人员 */
  cancelMan?: string;
  /**取消时间 */
  cancelTime?: string;
  /**取消原因 */
  cancelReason?: string;
  /**登录用户角色 职员workers 管理员administrator */
  loginUserRole?: string;
  /**图片/视频 */
  contentList?: Content[];
}

/**报修查询参数 */
export class RepairSearch extends PageSearch {
  /**报修人 */
  repairMan?: string;

  constructor() {
    super();
    this.pageRecord = 10; // 每页10条
  }
}

export type DialogType = "urge" | "cancel" | "complete";

export const getAssignStatus = (assignStatus: string = "") => {
  switch (assignStatus) {
    case 'assigning':
      return '待指派';
    case 'assigned':
      return '已指派';
    case 'reassigned':
      return '已重新指派';
    default:
      return assignStatus;
  }
}