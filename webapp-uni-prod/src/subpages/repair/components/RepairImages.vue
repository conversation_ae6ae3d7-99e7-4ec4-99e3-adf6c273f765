<template>
  <view v-if="contentList && contentList.length > 0" class="repair-images">
    <view class="preview-item" v-for="(it, index) in contentList" :key="index">
      <!-- 图片预览 -->
      <image
        v-if="it.fileType === '.jpg' || it.fileType === '.png'"
        class="preview-img"
        :src="baseUrl + '/upload/' + it.filePath + '/' + it.storeName"
        mode="aspectFill"
        @click.stop="previewImage(contentList, index)"
      />
      <!-- 视频预览 -->
      <view
        v-else-if="it.fileType === '.mp4'"
        class="preview-video"
        @click.stop="previewVideo(contentList, index)"
      >
        <video
          class="video-thumbnail"
          :src="baseUrl + '/upload/' + it.filePath + '/' + it.storeName"
          :show-play-btn="false"
          :controls="false"
          muted
          object-fit="cover"
          :initial-time="0.1"
          :enable-progress-gesture="false"
          :show-fullscreen-btn="false"
          :show-center-play-btn="false"
          :auto-pause-if-navigate="true"
          :auto-pause-if-open-native="true"
        ></video>
        <SlSubSvgIcon
          subpage="repair"
          name="14-14-24"
          size="14"
          class="video-icon"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { Content } from "@/models/Content";

defineProps({
  contentList: {
    type: Array as () => Content[],
    default: () => [],
  },
});

const baseUrl = import.meta.env.VITE_API_BASE_URL;

// 预览图片
const previewImage = (contentList: Content[], index: number) => {
  const { filePath, storeName } = contentList[index];
  const url = `${baseUrl}/upload/${filePath}/${storeName}`;
  uni.previewImage({
    current: url,
    urls: [url],
  });
};

// 预览视频
const previewVideo = (contentList: Content[], index: number) => {
  const { filePath, storeName } = contentList[index];
  const url = `${baseUrl}/upload/${filePath}/${storeName}`;
  /**需要真机调试 */
  uni.navigateTo({
    url: `/subpages/repair/pages/video-player/index?url=${encodeURIComponent(url)}`
  });
};
</script>

<style lang="scss" scoped>
.repair-images {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  .preview-item {
    width: 48px;
    height: 48px;
    margin: 1px 7px 1px 0px;
    position: relative;
    .preview-img,
    .preview-video {
      width: 100%;
      height: 100%;
      border-radius: 4px;
    }
    .preview-video {
      position: relative;
      .video-thumbnail {
        width: 100%;
        height: 100%;
        border-radius: 4px;
      }
      .video-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-shadow: 0 0 4px rgba(0, 0, 0, 0.8);
      }
    }
    view:last-child {
      margin-right: unset;
    }
  }
}
</style>
