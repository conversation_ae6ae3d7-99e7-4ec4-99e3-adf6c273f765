<template>
  <view v-if="contentList && contentList.length > 0" class="repair-images">
    <view class="preview-item" v-for="(it, index) in contentList" :key="index">
      <!-- 图片预览 -->
      <image
        v-if="it.fileType === '.jpg' || it.fileType === '.png'"
        class="preview-img"
        :src="baseUrl + '/upload/' + it.filePath + '/' + it.storeName"
        mode="aspectFill"
        @click.stop="previewImage(contentList, index)"
      />
      <!-- 视频预览 -->
      <view
        v-else-if="it.fileType === '.mp4'"
        class="preview-video"
        @click.stop="previewVideo(contentList, index)"
      >
        <image
          class="video-thumbnail"
          :src="baseUrl + '/upload/' + it.filePath + '/' + it.storeName"
          mode="aspectFill"
        />
        <SlSubSvgIcon
          subpage="repair"
          name="14-14-24"
          size="14"
          class="video-icon"
        />
      </view>
    </view>
  </view>

  <view v-if="showVideo" class="video-player-container" @click="closeVideo">
    <view class="video-player-wrapper">
      <video
      :src="currentVideoUrl"
        class="video-player"
        autoplay
        controls
        object-fit="contain"
      ></video>
    </view>
  </view>
</template>

<script setup lang="ts">
import { Content } from "@/models/Content";

defineProps({
  contentList: {
    type: Array as () => Content[],
    default: () => [],
  },
});

const baseUrl = import.meta.env.VITE_API_BASE_URL;

// 预览图片
const previewImage = (contentList: Content[], index: number) => {
  const { filePath, storeName } = contentList[index];
  const url = `${baseUrl}/upload/${filePath}/${storeName}`;
  uni.previewImage({
    current: url,
    urls: [url],
  });
};

const showVideo = ref(false);
const currentVideoUrl = ref("");

// 预览视频
const previewVideo = (contentList: Content[], index: number) => {
  const { filePath, storeName } = contentList[index];
  const url = `${baseUrl}/upload/${filePath}/${storeName}`;
  currentVideoUrl.value = url;
  showVideo.value = true;
  // 添加延时，等待视频加载后自动进入全屏
  setTimeout(() => {
    const videoContext = uni.createVideoContext('videoPlayer');
    videoContext.requestFullScreen();
  }, 300);
};

const closeVideo = () => {
  showVideo.value = false;
  currentVideoUrl.value = "";
};
</script>

<style lang="scss" scoped>
.repair-images {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  .preview-item {
    width: 48px;
    height: 48px;
    margin: 1px 7px 1px 0px;
    position: relative;
    .preview-img,
    .preview-video {
      width: 100%;
      height: 100%;
      border-radius: 4px;
    }
    .preview-video {
      position: relative;
      .video-thumbnail {
        width: 100%;
        height: 100%;
        border-radius: 4px;
      }
      .video-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-shadow: 0 0 4px rgba(0, 0, 0, 0.8);
      }
    }
    view:last-child {
      margin-right: unset;
    }
  }
}
.video-player-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  .video-player-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .video-player {
    width: 100%;
    height: 100vh;
  }
}

</style>
