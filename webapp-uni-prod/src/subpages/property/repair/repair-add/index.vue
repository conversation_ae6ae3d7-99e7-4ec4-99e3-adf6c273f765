<template>
  <view class="container">
    <u-form labelPosition="left" :model="data" :rules="rules" ref="formRef" labelWidth="120">
      <view class="form-card">
        <u-form-item label="报修人员" prop="reporter">
          <view class="info">{{ data.reporter }}</view>
        </u-form-item>
        <u-form-item label="手机号码" prop="phone">
          <view class="info">{{ data.phone }}</view>
        </u-form-item>
        <u-form-item label="详细位置" prop="location" required>
          <input type="text" v-model="data.location" class="input" placeholder="请输入详细位置" :maxlength="10"
            @input="onValidate('location')" />
        </u-form-item>
        <u-form-item label="紧急程度" prop="urgencyLevel" required>
          <SLSelect class="select" v-model="data.urgencyLevel" :options="urgencyOpt"
            @change="onValidate('urgencyLevel')" />
        </u-form-item>
        <u-form-item label="报修内容" class="form-textarea" prop="content">
          <SlTextarea class="textarea" v-model="data.content" :showWordLimit="true" :maxlength="30" />
        </u-form-item>
      </view>
      <view class="form-card upload-card">
        <view class="upload-title">图片/视频</view>
        <!-- todo上传图片视频 -->
        <!-- <u-upload :fileList="fileList" @afterRead="afterRead" @delete="deletePic" name="file" multiple :maxCount="9"
          :previewFullImage="true"></u-upload> -->
      </view>
    </u-form>
    <view class="save-btn">
      <BaseButton btn-type="save" size="large" @click="onSave">提交</BaseButton>
    </view>
  </view>

  <StatePop v-model:show="showPopup" :title="statePopTitle" :type="statePopType" :confirmText="confirmText"
    :icon="statePopIcon" @confirm="onConfirm" @cancel="onCancel">
  </StatePop>
</template>

<script setup lang="ts">
import { UrgencyLevel, Repair } from "@/models/Repair";
// import { uploadFile } from "@/service/common";
// import repairService from "@/service/repair/repair.service";
// import { usePrincipalStore } from "@/store";

/**获取用户信息 */
// const principalStore = usePrincipalStore();
/**表单引用 */
const formRef = ref();
/**todo引入字典 */
const urgencyOpt = [
  { label: "高", value: UrgencyLevel.HIGH },
  { label: "中", value: UrgencyLevel.MEDIUM },
  { label: "低", value: UrgencyLevel.LOW },
];
/**表单数据 */
const data = reactive<Repair>({ reporter: "", phone: "", location: "", urgencyLevel: <UrgencyLevel>"", content: "", });
/**文件列表 */
const fileList = ref([]);
/**弹窗相关状态 */
const showPopup = ref(false);
const statePopTitle = ref("");
const statePopType = ref("");
const confirmText = ref("");
const statePopIcon = ref("");
/**表单验证规则 */
const rules = {
  location: [
    { required: true, message: "请输入详细位置", trigger: ["blur", "change"] },
  ],
  urgencyLevel: [
    { required: true, message: "请选择紧急程度", trigger: "change" },
  ],
};
onMounted(async () => {
  getAccount();
});
/**实时校验字段 */
const onValidate = (field: string) => {
  if (formRef.value) {
    formRef.value.validateField(field);
  }
};
const onSave = () => {
  formRef.value
    .validate()
    .then(async (valid: any) => {
      if (valid) {
        try {
          // await repairService.save(data);
          // console.log("提交报修数据:", toRaw(data));
          statePopTitle.value = "报修成功";
          statePopType.value = "success";
          confirmText.value = "查看报修记录";
          statePopIcon.value = "components-succeed";
          showPopup.value = true;
        } catch (error) {
          statePopTitle.value = "报修失败";
          statePopType.value = "error";
          confirmText.value = "返回";
          statePopIcon.value = "components-lose";
          showPopup.value = true;
        }
      }
    })
    .catch((errors: any) => {
      console.log("表单验证失败:", errors);
    });
};
const onConfirm = () => {
  showPopup.value = false;
  uni.navigateTo({ url: "/subpages/property/repair/index" });
};
const onCancel = () => {
  showPopup.value = false;
};
/**获取当前登录用户信息 */
const getAccount = () => {
  // let account = principalStore.userIdentity;
  let account = {
    name: "张三",
    phone: "***********",
  };
  if (account) {
    data.reporter = account.name;
    data.phone = account.phone;
  }
  console.log("当前登录用户信息:", account, data);
};
/**上传后回调 */
const afterRead = async (event: any) => {
  // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
  // let lists = [].concat(event.file);
  // let fileListLen = fileList.value.length;
  // lists.map((item: any) => {
  //   fileList.value.push({ ...item, status: "uploading", message: "上传中" });
  // });
  // for (let i = 0; i < lists.length; i++) {
  //   const result = await uploadFile(lists[i].url);
  //   let item = fileList.value[fileListLen];
  //   fileList.value.splice(fileListLen, 1, {
  //     ...item,
  //     status: "success",
  //     message: "",
  //     url: result.url,
  //   });
  //   fileListLen++;
  // }
};
/**删除图片 */
const deletePic = (event: any) => {
  fileList.value.splice(event.index, 1);
};
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  display: flex;
  flex-direction: column;
}

.form-card {
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  padding: 12px 16px;
  margin-bottom: 10px;

  .info {
    width: 100%;
    text-align: right;
    padding: 0px 8px;
  }

  .select {
    width: 100%;
  }

  :deep(.custom-select-container) {
    .select-display {
      justify-content: flex-end !important;

      label {
        padding-right: 8px;
      }
    }

    .dropdown-item {
      text-align: center;
    }
  }

  :deep(.u-form-item__body) {
    padding: 5px 0;
  }

  .input {
    width: 100%;
    height: 24px;
    border-radius: 3px;
    background: #f8f8f8;
    text-align: right;
    padding: 6px 8px;
  }

  .form-textarea {
    :deep(.u-form-item__body) {
      display: flex;
      flex-direction: column !important;
    }

    :deep(.u-form-item__body__left) {
      padding-bottom: 5px;
    }

    .textarea {
      width: 100%;
      height: 56px;
    }
  }
}

.upload-card {
  .upload-title {
    margin-bottom: 5px;
    color: #666666;
  }

  .upload-desc {
    font-size: 12px;
    color: #999;
    margin-bottom: 15px;
  }
}

.save-btn {
  width: 100%;
  position: absolute;
  bottom: 20px;
}
</style>