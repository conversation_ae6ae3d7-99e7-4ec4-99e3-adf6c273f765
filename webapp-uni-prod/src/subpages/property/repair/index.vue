<template>
  <view class="container">
    <!-- 搜索区域 -->
    <view class="search">
      <view class="search-box">
        <input type="text" v-model="searchKeyword" placeholder="请输入关键词" confirm-type="search"
          @confirm="getList(true)" />
        <!-- <sl-input v-model="searchKeyword" :placeholder="'请输入关键词'" clearable @input="getList(true)" /> -->
      </view>
      <!-- <view class="filter-btn" @click="showFilterPopup = true">
        <uni-icons type="paperplane" size="18" color="#007AFF" />
        <text>筛选</text>
      </view> -->
    </view>

    <!-- 标签页 -->
    <view class="tabs">
      <SlTabs :list="tabs" v-model="curTab" @click="OnTab"></SlTabs>
    </view>

    <!-- 报修列表 -->
    <scroll-view class="repair-list-container" scroll-y @scrolltolower="onMore" @refresherrefresh="getList(true)"
      refresher-enabled :refresher-triggered="refreshing">
      <view v-if="repairList.length > 0" class="repair-list" :class="{ staff: ifStaff }">
        <view v-for="item in repairList" :key="item.id" class="repair-item">
          <!-- 报修项头部 -->
          <view class="repair-header">
            <view class="header-status" :class="'status-' + item.status">
              {{ getStatusText(item.status) }}
            </view>
            <view class="header-time">{{ item.createTime }}</view>
          </view>
          <!-- 报修内容 -->
          <view class="repair-content">
            <view class="repair-info">
              <view class="info-item">
                <text class="info-label">报修人员</text>
                <text class="info-value">{{ item.reporter }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">紧急程度</text>
                <text class="urgency-level" :class="`urgency-${item.urgencyLevel}`">
                  {{ getUrgencyText(item.urgencyLevel) }}
                </text>
              </view>
              <view class="info-item">
                <text class="info-label">报修位置</text>
                <text class="info-value">{{ item.location }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">报修内容</text>
                <text class="info-value ellipsis">{{ item.content }}</text>
              </view>
            </view>

            <!-- 报修图片 -->
            <view v-if="item.images && item.images.length > 0" class="repair-images">
              <image v-for="(img, imgIndex) in item.images" :key="imgIndex" :src="img" mode="aspectFill"
                @click="onOpenImg(item.images, imgIndex)" />
            </view>
          </view>

          <!-- 报修底部操作区 -->
          <view class="repair-footer">
            <view class="urge-content" v-if="item.urge && !ifStaff">
              <view>催办内容</view>
              <view class="ellipsis">{{ item.urge }}</view>
            </view>
            <view class="desc-btns">
              <view>
                <template v-if="ifStaff">
                  <view class="desc" v-if="item.status === 'pending'">已提交时长 <text>{{ item.duration }}</text></view>
                  <view class="desc" v-else-if="item.status === 'processing'">已指派给 <text>{{ item.duration }}</text>
                  </view>
                  <view class="desc" v-else-if="item.status === 'completed'">结束原因 <text>{{ item.duration }}</text>
                  </view>
                  <view class="desc" v-else>取消原因 <text>{{ item.duration }}</text></view>
                </template>
              </view>
              <view class="repair-btns">
                <!-- 待处理状态的操作按钮 -->
                <template v-if="item.status === 'pending'">
                  <button class="cancel" @click="onCancel(item.id)"> 取消 </button>
                  <button v-if="ifStaff" class="urge" @click="onUrge(item.id)"> 催办 </button>
                  <button v-else class="urge" @click="onAssign(item.id)"> 指派 </button>
                </template>
                <!-- 处理中状态的操作按钮 -->
                <template v-else-if="item.status === 'processing'">
                  <!-- 职工端显示 电话图标 点击跳转微信拨打页面 -->
                  <template v-if="ifStaff"></template>
                  <!-- 管理员端 -->
                  <template v-else>
                    <button class="urge" @click="onReassign(item.id)"> 重新指派 </button>
                    <button class="stop" @click="onReassign(item.id)"> 结束任务 </button>
                  </template>
                </template>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <uni-load-more v-if="repairList.length > 0" :status="loading ? 'loading' : hasMore ? 'loadmore' : 'nomore'"
        :icon-size="16" :content-text="{
          contentdown: '上拉加载更多',
          contentrefresh: '加载中...', contentnomore: '没有更多数据了',
        }" />

      <!-- 空列表提示 -->
      <view v-if="!loading && repairList.length === 0" class="empty-list">
        <!-- <image src="/static/images/empty.png" mode="aspectFit" /> -->
        <text>暂无数据</text>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="add-btn" v-if="ifStaff">
      <BaseButton btn-type="save" size="large" @click="onAdd">我要报修</BaseButton>
    </view>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom" @change="(e: any) => (showFilterPopup = e.show)">
      <view v-if="showFilterPopup" class="filter-popup">
        <view class="filter-title">筛选</view>

        <!-- 紧急程度筛选 -->
        <view class="filter-item">
          <view class="filter-label">紧急程度</view>
          <view class="filter-options">
            <view class="filter-option" :class="{ active: !filter.urgencyLevel }"
              @click="store.setFilter({ urgencyLevel: undefined })">
              全部
            </view>
            <view v-for="option in urgencyOpt" :key="option.value" class="filter-option"
              :class="{ active: filter.urgencyLevel === option.value }"
              @click="store.setFilter({ urgencyLevel: option.value })">
              {{ option.label }}
            </view>
          </view>
        </view>

        <!-- 日期筛选 -->
        <view class="filter-item">
          <view class="filter-label">日期范围</view>
          <view class="date-selection">
            <picker mode="date" :value="filter.startDate" @change="confirmStartDate">
              <view class="date-picker">
                <text>{{ filter.startDate || "开始日期" }}</text>
                <uni-icons type="calendar" size="16" color="#666" />
              </view>
            </picker>
            <text class="date-separator">至</text>
            <picker mode="date" :value="filter.endDate" @change="confirmEndDate">
              <view class="date-picker">
                <text>{{ filter.endDate || "结束日期" }}</text>
                <uni-icons type="calendar" size="16" color="#666" />
              </view>
            </picker>
          </view>
        </view>

        <!-- 筛选按钮 -->
        <view class="filter-buttons">
          <button class="reset-button" @click="onSearch(0)">重置</button>
          <button class="apply-button" @click="onSearch(1)">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
/**报修-列表 */
import { useRepairStore } from "@/store";
// import repairService from "@/service/repair";
import { RepairStatus, UrgencyLevel } from "@/models/Repair";
const store = useRepairStore();
const curTab = ref('pending');
/**是否为职工端 */
const ifStaff = computed(() => store.ifStaff);
/**搜索关键词 */
const searchKeyword = computed({
  get: () => store.searchKeyword,
  set: (value) => store.setSearchKeyword(value),
});
/**筛选 */
const showFilterPopup = computed({
  get: () => store.showFilterPopup,
  set: (value) => store.toggleFilterPopup(value),
});
/**紧急程度选项 */
const urgencyOpt = [
  { value: UrgencyLevel.LOW, label: "低" },
  { value: UrgencyLevel.MEDIUM, label: "中" },
  { value: UrgencyLevel.HIGH, label: "高" },
];
/**筛选条件 */
const filter = computed(() => store.filter);
/**日期选择确认 */
const confirmStartDate = (e: any) => {
  store.setFilter({ startDate: e.detail.value });
};
const confirmEndDate = (e: any) => {
  store.setFilter({ endDate: e.detail.value });
};
/**筛选 0重置 1搜索 */
const onSearch = (flag: 0 | 1) => {
  if (flag) {
    store.toggleFilterPopup(false);
    getList();
  } else {
    store.resetFilter();
  }
}
const tabs = reactive([
  { value: RepairStatus.PENDING, name: "待处理" },
  { value: RepairStatus.PROCESSING, name: "处理中" },
  { value: RepairStatus.COMPLETED, name: "已完结" },
  { value: RepairStatus.CANCELLED, name: "已取消" },
]);
/**报修列表数据 */
const repairList = computed(() => store.repairList);
/**加载状态 */
const loading = computed(() => store.loading);
const refreshing = computed(() => store.refreshing);
const hasMore = computed(() => store.hasMore);
onMounted(() => {
  //todo 模拟数据
  mockData();
  // store.setIfStaff(false);
  // console.log("onMounted 挂载故障列表");
  
});
const OnTab = (tab: any) => {
  // curTab 已通过 v-model 自动更新，不需要手动设置
  // console.log('tab',tab.value,curTab);  
  getList();
}
/**新增报修 */
const onAdd = () => {
  uni.navigateTo({ url: "/subpages/property/repair/repair-add/index" });
};
/**催办 */
const onUrge = async (id: string) => {
  // try {
  //   await repairService.urgeRepair(id);
  //   uni.showToast({
  //     title: "已催促处理",
  //     icon: "success",
  //   });
  // } catch (error) {
  //   console.error("催促处理失败", error);
  //   uni.showToast({
  //     title: "操作失败，请重试",
  //     icon: "none",
  //   });
  // }
};
/**取消报修 */
const onCancel = async (id: string) => {
  uni.showModal({
    title: "提示",
    content: "确定要取消该报修吗？",
    success: async (res) => {
      // if (res.confirm) {
      //   try {
      //     await repairService.cancelRepair(id);
      //     uni.showToast({
      //       title: "已取消报修",
      //       icon: "success",
      //     });
      //     getList(true);
      //   } catch (error) {
      //     console.error("取消报修失败", error);
      //     uni.showToast({
      //       title: "操作失败，请重试",
      //       icon: "none",
      //     });
      //   }
      // }
    },
  });
};
/**指派处理人 */
const onAssign = (id: string) => {
};
/**重新指派 */
const onReassign = (id: string) => {
};
/**加载更多 */
const onMore = () => {
  if (loading.value || !hasMore.value) return;
  store.loadNextPage();
  // getList();
};
/**放大图片/视频 */
const onOpenImg = (images: string[], index: number) => {
  const urls = images.map((id) => `${id}`);
  uni.previewImage({
    urls,
    current: urls[index],
  });
};
// 加载数据
const getList = async (refresh = false) => {
  if (refresh) {
    store.setRefreshing(true);
    store.pagination.currentPage = 1;
    store.hasMore = true;
    store.repairList = [];
  } else {
    store.setLoading(true);
  }

  try {
    // 模拟数据
    mockData();
    // API调用
    // const result = await repairService.getList(store.searchParams);
    // store.setRepairList(result.result || [], refresh);
    // store.setPagination({
    //   total: result.total,
    //   currentPage: result.currentPage,
    // });
  } catch (error) {
    console.error("加载报修列表失败", error);
    uni.showToast({
      title: "加载失败，请重试",
      icon: "none",
    });
  } finally {
    store.setLoading(false);
    store.setRefreshing(false);
    // 停止下拉刷新
    uni.stopPullDownRefresh();
  }
};
// 获取状态文本和样式
const getStatusText = (status: RepairStatus) => {
  const statusMap = {
    [RepairStatus.PENDING]: "待处理",
    [RepairStatus.PROCESSING]: "处理中",
    [RepairStatus.COMPLETED]: "已完结",
    [RepairStatus.CANCELLED]: "已取消",
  };
  return statusMap[status] || "未知";
};
// 获取紧急程度文本
const getUrgencyText = (level: UrgencyLevel) => {
  const textMap = {
    [UrgencyLevel.LOW]: "低",
    [UrgencyLevel.MEDIUM]: "中",
    [UrgencyLevel.HIGH]: "高",
  };
  return textMap[level] || "未知";
};
/**模拟数据 */
const mockData = () => {
  const mockData = [
    {
      id: '1001',
      duration: '01:23:45',
      location: '一栋二楼',
      status: RepairStatus.PENDING,
      // status: RepairStatus.PROCESSING,
      // status: RepairStatus.COMPLETED,
      // status: RepairStatus.CANCELLED,
      reporter: '张三',
      urgencyLevel: UrgencyLevel.HIGH,
      content: '厨房水管漏水，地面已积水,厨房水管漏水，地面已积水',
      createTime: '2023-12-01 09:30:00',
      urge: '快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧',
      images: ['https://picsum.photos/200/300?random=1', 'https://picsum.photos/200/300?random=2']
    },
    {
      id: '1002',
      duration: '01:23:45',
      location: '一栋十一楼',
      // status: RepairStatus.PENDING,
      status: RepairStatus.PROCESSING,
      reporter: '李四',
      urgencyLevel: UrgencyLevel.MEDIUM,
      content: '卧室灯不亮，可能是电路问题,卧室灯不亮，可能是电路问题，卧室灯不亮，可能是电路问题，卧室灯不亮，可能是电路问题',
      createTime: '2023-12-02 14:20:00',
      urge: '快点吧快点吧快点吧快点吧快点吧快点吧快点吧快点吧',
      images: ['https://picsum.photos/200/300?random=3']
    },
    {
      id: '1003',
      duration: '01:23:45',
      location: '一栋七楼',
      // status: RepairStatus.PENDING,
      status: RepairStatus.COMPLETED,
      reporter: '王五',
      urgencyLevel: UrgencyLevel.LOW,
      content: '客厅墙面有裂缝，需要修补',
      createTime: '2023-12-03 11:15:00',
      urge: '快点吧',
    },
    {
      id: '1004',
      duration: '01:23:45',
      location: '一栋十二楼',
      // status: RepairStatus.PENDING,
      status: RepairStatus.CANCELLED,
      reporter: '赵六',
      urgencyLevel: UrgencyLevel.HIGH,
      content: '卫生间下水道堵塞，无法正常排水',
      createTime: '2023-12-03 16:45:00',
      // urge: '快点吧',
      images: ['https://picsum.photos/200/300?random=4', 'https://picsum.photos/200/300?random=5']
    },
    // {
    //   id: '1005',
    //   duration: '01:23:45',
    //   location: '一栋二十楼',
    //   status: RepairStatus.PENDING,
    //   reporter: '钱七',
    //   urgencyLevel: UrgencyLevel.MEDIUM,
    //   content: '阳台门把手损坏，无法正常开关',
    //   createTime: '2023-12-04 10:05:00',
    //   urge: '快点吧',
    // }
  ];

  // 设置模拟数据到store
  store.setRepairList(mockData, true);
  store.setPagination({
    total: mockData.length,
    currentPage: 1
  });
  store.setLoading(false);
  store.setRefreshing(false);
};
</script>

<style lang="scss" scoped>
$pending: #FF7605;
$processing: #005CC8;
$completed: #5B851B;
$default: #333333;
$label: #666666;
$primary: #0066DF;

.ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.container {
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 顶部搜索区域 */
.search {
  height: 48px;
  padding: 10px 15px;
  background: #F9F9F9;
  display: flex;
  align-items: center;

  .search-box {
    flex: 1;
    height: 30px;
    padding: 0 12px;
    border-radius: 3px;
    font-size: 12px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid rgba(153, 153, 153, 0.2);
    display: flex;
    align-items: center;

    input:placeholder {
      color: #999;
    }
  }

  .filter-btn {
    margin-left: 10px;
    display: flex;
    align-items: center;
    padding: 0 5px;
  }
}

/* tabs区域 */
.tabs {
  height: 38px;
}

/* 列表区域 */
.repair-list-container {
  height: 50%;
  flex-grow: 1;
  padding-bottom: 20px;
  overflow: hidden;

  .empty-list {
    padding: 40px 0;
  }

  .repair-list {
    padding: 10px 15px 0px;

    &.staff {
      padding: 10px 15px 50px;
    }
  }

  .repair-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    .repair-header {
      padding: 10px 15px 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-status {
        padding: 2px 8px;
        border-radius: 2px;
      }

      .status-pending {
        background: #FFF0E4;
        border: 1px solid #FFE0C5;
        color: $pending;
      }

      .status-processing {
        background: #E4F7FF;
        border: 1px solid #C5E6FF;
        color: $processing;
      }

      .status-completed {
        background: #EEFFEE;
        border: 1px solid #C2E9AA;
        color: $completed;
      }

      .status-cancelled {
        background: #F1F1F1;
        border: 1px solid #C5C5C5;
        color: $default;
      }

      .header-time {
        font-size: 12px;
        color: $label;
      }
    }

    .repair-content {
      padding: 0px 15px;
      display: flex;
      flex-direction: column;
    }

    .repair-info {
      margin: 5px 0px;
      color: $default;
      font-size: 12px;

      .info-item {
        width: 100%;
        margin-bottom: 8px;
        display: flex;
      }

      .info-label {
        width: 70px;
        color: $label;
      }

      .info-value {
        width: 50%;
        flex-grow: 1;
      }
    }

    .urgency-level {
      padding: 2px 6px;
      border-radius: 2px;
      font-size: 12px;

      &.urgency-low {
        background: #FFF7D4;
        color: #AD8D00;
      }

      &.urgency-medium {
        background: #FDE6CA;
        color: #E27600;
      }

      &.urgency-high {
        background: #FFDFDE;
        color: #C93535;
      }
    }

    .repair-images {
      display: flex;
      flex-wrap: wrap;
    }

    .repair-images image {
      width: 48px;
      height: 48px;
      margin-right: 8px;
      margin-bottom: 8px;
      border-radius: 3px;
    }

    .repair-footer {
      border-top: 1px dashed #f5f5f5;
      background: #F8FBFF;
      padding: 10px 15px;

      .urge-content {
        display: flex;
        font-size: 12px;
        margin-bottom: 8px;

        view:first-child {
          width: 70px;
          color: $label;
        }

        view:last-child {
          width: 50%;
          flex-grow: 1;
        }
      }

      .desc-btns {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .desc {
        color: $label;
        font-size: 12px;
      }

      .desc text {
        margin-left: 5px;
        color: #E50101;
      }

      .repair-btns {
        display: flex;
        justify-content: flex-end;

        button {
          min-width: 58px;
          height: 28px;
          line-height: 28px;
          border-radius: 20px;
          text-align: center;
          margin-left: 15px;
        }

        .urge {
          background: $primary;
          color: #FFFFFF;
        }

        .cancel {
          color: $label;
          background: #FFFFFF;
          border: 1px solid #C4C4C4;
        }

        .stop {
          background: #2191B3;
          color: #FFFFFF;
        }
      }

    }

  }
}

/* 底部按钮 */
.add-btn {
  width: 100%;
  height: 70px;
  border-radius: 10px 10px 0px 0px;
  background: rgba(255, 255, 255, 0.7);
  box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);
  position: absolute;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 筛选弹窗 */
// .filter-popup {
//   background-color: #fff;
//   padding: 15px;

//   .filter-item {
//     margin-bottom: 15px;
//   }

//   .filter-title {
//     font-size: 16px;
//     font-weight: bold;
//     margin-bottom: 10px;
//   }

//   .filter-options {
//     display: flex;
//     flex-wrap: wrap;
//   }

//   .filter-option {
//     padding: 6px 12px;
//     background-color: #f5f5f5;
//     border-radius: 4px;
//     margin-right: 10px;
//     margin-bottom: 10px;
//   }

//   .filter-option.active {
//     background-color: #4f7af6;
//     color: #fff;
//   }

//   .filter-date {
//     display: flex;
//     align-items: center;
//   }

//   .date-trigger {
//     flex: 1;
//     height: 36px;
//     background-color: #f5f5f5;
//     border-radius: 4px;
//     display: flex;
//     align-items: center;
//     justify-content: space-between;
//     padding: 0 10px;
//   }

//   .date-separator {
//     margin: 0 10px;
//   }

//   .filter-buttons {
//     display: flex;
//     justify-content: space-between;
//     margin-top: 20px;
//   }

//   .filter-buttons :deep(.u-button) {
//     width: 48%;
//   }
// }</style>