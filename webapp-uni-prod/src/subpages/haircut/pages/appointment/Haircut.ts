
/**
 * 理发店基础配置接口类型
 */
export interface IHairBaseConfig {
    /** 配置ID */
    id: string;
    /** 系统创建时间 */
    sysCreated?: string;
    /** 营业日，格式："周一,周三,周五" */
    weekDays: string;
    /** 每日开始营业时间，格式："08:30" */
    dailyOpenTime: string;
    /** 每日结束营业时间，格式："17:00" */
    dailyCloseTime: string;
    /** 午休开始时间，格式："11:30" */
    lunchStartTime: string;
    /** 午休结束时间，格式："13:00" */
    lunchEndTime: string;
    /** 顾客间隔时间（分钟） */
    customerInterval: number;
  }
  
  /**
   * 模拟的后端接口数据
   */
  export const mockHairConfig: IHairBaseConfig = {
    id: "f0f40fcf7baa4a84ac62166952414dd9",
    sysCreated: "2025-06-19 15:47",
    weekDays: "周一,周三,周五",
    dailyOpenTime: "08:30",
    dailyCloseTime: "17:00",
    lunchStartTime: "11:30",
    lunchEndTime: "13:00",
    customerInterval: 30,
  };
  
  /**
   * 请求参数类型
   */
  export interface IHairConfigParams {
    weekDays: string;
    dailyOpenTime: string;
    dailyCloseTime: string;
    lunchStartTime: string;
    lunchEndTime: string;
    customerInterval: number;
    id: string;
  }
  
  /**
   * 响应数据类型
   */
//   export interface IHairConfigResponse {
//     code: number;
//     message: string;
//     data: IHairBaseConfig;
//     timestamp: number;
//   }