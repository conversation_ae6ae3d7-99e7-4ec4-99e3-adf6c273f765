/**
 * 理发店基础配置接口类型
 */
export interface IHairBaseConfig {
  /** 配置ID */
  id: string;
  /** 系统创建时间 */
  sysCreated?: string;
  /** 营业日，格式："周一,周三,周五" */
  weekDays: string;
  /** 每日开始营业时间，格式："08:30" */
  dailyOpenTime: string;
  /** 每日结束营业时间，格式："17:00" */
  dailyCloseTime: string;
  /** 午休开始时间，格式："11:30" */
  lunchStartTime: string;
  /** 午休结束时间，格式："13:00" */
  lunchEndTime: string;
  /** 顾客间隔时间（分钟） */
  customerInterval: number;
}

/**
 * 模拟的后端接口数据
 */
export const mockHairConfig: IHairBaseConfig = {
  id: "f0f40fcf7baa4a84ac62166952414dd9",
  sysCreated: "2025-06-19 15:47",
  weekDays: "周一,周三,周五",
  dailyOpenTime: "08:30",
  dailyCloseTime: "17:00",
  lunchStartTime: "11:30",
  lunchEndTime: "13:00",
  customerInterval: 30,
};
export const mockBackendSlots = [
  {
    id: "028c447b986d46528801fd5e24ac7173",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "09:00",
    endTime: "09:30",
    status: 0,
  },
  {
    id: "06577f88759e44e58a4522325ff989df",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "11:00",
    endTime: "11:30",
    status: 0,
  },
  {
    id: "14e62f0caefd4b72bf45eb6ddb8096e1",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "16:00",
    endTime: "16:30",
    status: 0,
  },
  {
    id: "23032858bb1a41e8bc3d9598d6f4956f",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "16:30",
    endTime: "17:00",
    status: 0,
  },
  {
    id: "2ac8a1ece01c49fab4635968ee4251af",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "12:30",
    endTime: "13:00",
    status: 2,
  },
  {
    id: "3cc15a38f70f4cbbb67258a053aedd69",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "13:30",
    endTime: "14:00",
    status: 2,
  },
  {
    id: "3e74eb547974420ba164b4d1d68ff8f0",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "10:00",
    endTime: "10:30",
    status: 0,
  },
  {
    id: "49d2c3d83ae5459a92e4a3fc3c42137c",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "15:00",
    endTime: "15:30",
    status: 2,
  },
  {
    id: "7460ab2f3f884b738b5663413d51ea08",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "12:00",
    endTime: "12:30",
    status: 2,
  },
  {
    id: "79150db04dc44cb59ee55cf5356876ff",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "13:00",
    endTime: "13:30",
    status: 2,
  },
  {
    id: "b16d2c516cb14c00b53814baa78eac29",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "09:30",
    endTime: "10:00",
    status: 0,
  },
  {
    id: "b38cde52131c4a759fdcbdc222221bcf",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "08:30",
    endTime: "09:00",
    status: 0,
  },
  {
    id: "bfd23d5235aa447983b07146f2305d5c",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "10:30",
    endTime: "11:00",
    status: 0,
  },
  {
    id: "c9bc3bbf53564db79c2469f3c2183e1b",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "11:30",
    endTime: "12:00",
    status: 2,
  },
  {
    id: "d826998dc0274777b68b07f8af5989f6",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "14:30",
    endTime: "15:00",
    status: 0,
  },
  {
    id: "dc69a71fe5e94c56be917680046f5424",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "15:30",
    endTime: "16:00",
    status: 2,
  },
  {
    id: "e8c19fc07f904d54a7555701513b3275",
    sysCreated: "2025-06-21 09:45",
    configId: "1b98ee22c8bc494593b629ab62443c3e",
    startTime: "14:00",
    endTime: "14:30",
    status: 4,
  },
];

/**理发配置模拟数据*/
export const mockHaircutConfig: Array<Datas> = [
  {
    id: "83a68231b46f4bc7abdf0daa9b733f9d",
    sysCreated: "2025-06-21 14:16",
    businessDate: "2025/07/14",
    openTime: "08:30",
    closeTime: "17:00",
    lunchStartTime: "11:30",
    lunchEndTime: "13:30",
    intervalMinutes: 50,
    issue: 2,
    ifopen: 0,
    specialTimeList: [
      {
        id: "5881ac8807824a0aa7a7baac5f24a497",
        sysCreated: "2025-06-21 14:16",
        configId: "83a68231b46f4bc7abdf0daa9b733f9d",
        specialStartTime: "15:00",
        specialEndTime: "16:00",
      },
      {
        id: "5fab0f19f91d42039790634a4c53a1f0",
        sysCreated: "2025-06-21 14:16",
        configId: "83a68231b46f4bc7abdf0daa9b733f9d",
        specialStartTime: "12:00",
        specialEndTime: "14:00",
      },
    ],
    hairtimeslot: [],
    weekDay: "周一",
  },
  {
    id: "83a68231b46f4bc7abdf0daa9b15877",
    sysCreated: "2025-06-21 14:16",
    businessDate: "2025/07/14",
    openTime: "08:30",
    closeTime: "17:00",
    lunchStartTime: "11:30",
    lunchEndTime: "13:30",
    intervalMinutes: 50,
    issue: 2,
    ifopen: 0,
    specialTimeList: [
      {
        id: "5881ac8807824a0aa7a7baac5f24a497",
        sysCreated: "2025-06-21 14:16",
        configId: "83a68231b46f4bc7abdf0daa9b733f9d",
        specialStartTime: "15:00",
        specialEndTime: "16:00",
      },
      {
        id: "5fab0f19f91d42039790634a4c53a1f0",
        sysCreated: "2025-06-21 14:16",
        configId: "83a68231b46f4bc7abdf0daa9b733f9d",
        specialStartTime: "12:00",
        specialEndTime: "14:00",
      },
    ],
    hairtimeslot: [],
    weekDay: "周二",
  },
];

/**
 * 请求参数类型
 */
export interface IHairConfigParams {
  weekDays: string;
  dailyOpenTime: string;
  dailyCloseTime: string;
  lunchStartTime: string;
  lunchEndTime: string;
  customerInterval: number;
  id: string;
}
export interface Root {
  rlt: number;
  info: string;
  datas: Datas;
}
/** 理发预约列表*/
export interface HaircutAppointment {
  id: string;
  personnelId: string;
  timeslotId: string;
  customerName: string;
  customerPhone: string;
  baberPhone: string;
  appointmentTime?:string;
  hairStartTime: string; // 示例："2025/06/26 09:30"
  hairEndTime: string;   // 示例："2025/06/26 10:00"
  status: number;        // 示例：0（未完成？）
  sysCreated: string;    // 示例："2025-06-23 09:39"
}
// 预约总列表
export interface Datas {
  id?: string;
  sysCreated?: string;
  businessDate?: string;
  openTime?: string;
  closeTime?: string;
  lunchStartTime?: string;
  lunchEndTime?: string;
  intervalMinutes?: number;
  issue?: number;
  ifopen?: number;
  specialTimeList?: SpecialTimeList[];
  hairtimeslot?: Hairtimeslot[];
  weekDay?: string;
}
/** 特殊时间列表"*/
export interface SpecialTimeList {
  id?: string;
  sysCreated?: string;
  configId?: string;
  specialStartTime?: string;
  specialEndTime?: string;
}
export type TimeSlotState = 0 | 1 | 2 | 3 | 4; // 0=可预约, 1=已约满, 2=不可约, 3=未预约, 4=用户名

/**发型预约时段*/
export interface Hairtimeslot {
  id: string;
  sysCreated: string;
  configId: string;
  startTime: string;
  endTime: string;
  status: TimeSlotState | any;
  time?: string;
  hairreservation?: Hairreservation
}
export interface Hairreservation {
  id: string
  sysCreated: string
  timeslotId: string
  personnelId: string
  customerName: string
  customerPhone: string
  baberPhone: string
  status: number
  hairStartTime: string
  hairEndTime: string
  hairnotificationList: HairnotificationList[]
}

export interface HairnotificationList {
  id: string
  sysCreated: string
  reservationId: string
  noticeMethod: string
  noticeTime: string
  feedbackContent: string
  adminDisplay: number
  userDisplay: number
}






/**假期列表*/ 
export interface HolidaycalendarList {
  id:string;
  date: string;
  officialType: number;
  manualType: number;
  officialName: string;
  type: number;
  dayOrHolidayName: string;
}
/**后端模拟数据*/ 
 export const HolidaycalendarListDatas :Array<HolidaycalendarList>= [
  {
      "id": "",
      "date": "2025-06-07",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 4,
      "dayOrHolidayName": "周六"
  },
  {
      "id": "",
      "date": "2025-06-08",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 4,
      "dayOrHolidayName": "周日"
  },
  {
      "id": "",
      "date": "2025-06-09",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周一"
  },
  {
      "id": "",
      "date": "2025-06-10",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周二"
  },
  {
      "id": "",
      "date": "2025-06-11",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周三"
  },
  {
      "id": "",
      "date": "2025-06-12",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周四"
  },
  {
      "id": "",
      "date": "2025-06-13",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周五"
  },
  {
      "id": "",
      "date": "2025-06-14",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 4,
      "dayOrHolidayName": "周六"
  },
  {
      "id": "",
      "date": "2025-06-15",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 4,
      "dayOrHolidayName": "周日"
  },
  {
      "id": "",
      "date": "2025-06-16",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周一"
  },
  {
      "id": "",
      "date": "2025-06-17",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周二"
  },
  {
      "id": "",
      "date": "2025-06-18",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周三"
  },
  {
      "id": "",
      "date": "2025-06-19",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周四"
  },
  {
      "id": "",
      "date": "2025-06-20",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周五"
  },
  {
      "id": "",
      "date": "2025-06-21",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 4,
      "dayOrHolidayName": "周六"
  },
  {
      "id": "",
      "date": "2025-06-22",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 4,
      "dayOrHolidayName": "周日"
  },
  {
      "id": "",
      "date": "2025-06-23",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周一"
  },
  {
      "id": "",
      "date": "2025-06-24",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周二"
  },
  {
      "id": "",
      "date": "2025-06-25",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周三"
  },
  {
      "id": "",
      "date": "2025-06-26",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周四"
  },
  {
      "id": "",
      "date": "2025-06-27",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周五"
  },
  {
      "id": "",
      "date": "2025-06-28",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 4,
      "dayOrHolidayName": "周六"
  },
  {
      "id": "",
      "date": "2025-06-29",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 4,
      "dayOrHolidayName": "周日"
  },
  {
      "id": "",
      "date": "2025-06-30",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周一"
  },
  {
      "id": "",
      "date": "2025-07-01",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周二"
  },
  {
      "id": "",
      "date": "2025-07-02",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周三"
  },
  {
      "id": "",
      "date": "2025-07-03",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周四"
  },
  {
      "id": "",
      "date": "2025-07-04",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 3,
      "dayOrHolidayName": "周五"
  },
  {
      "id": "",
      "date": "2025-07-05",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 2,
      "dayOrHolidayName": "周六"
  },
  {
      "id": "",
      "date": "2025-07-06",
      "officialType": 0,
      "manualType": 0,
      "officialName": "",
      "type": 1,
      "dayOrHolidayName": "国庆节"
  }
]