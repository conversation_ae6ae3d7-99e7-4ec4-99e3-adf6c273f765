<template>
  <div class="ha-main">
    <!-- 日历组件 -->
    <CalendarComponent
      @dateChange="handleDateChange"
      v-model="selectedDate"
      :date-data="backendDateData"
    ></CalendarComponent>
    <!-- 时间段选择区域 -->
    <div class="timeContainer">
      <!-- 管理员模式下显示今日理发人数 -->
      <div v-if="useRoot" class="today-count">
        <span>今日需理发<span class="highlight">{{ currentDate?.totalCustomer }}</span>人</span>
      </div>

      <!-- 时间段展示 -->
      <template v-if="timeRows.length > 0">
        <div class="row" v-for="(row, rowIndex) in timeRows" :key="rowIndex">
          <div
            v-for="(slot, index) in row"
            :key="index"
            class="time-slot"
            :class="{
              unavailable: slot.Status === 1 || slot.Status === 2,
              userstyle: slot.Status === 4,
              selected: selectedSlot && selectedSlot.time === slot.time,
            }"
            @click="selectTimeSlot(slot)"
          >
            <div class="time-text">{{ formatTimeDisplay(slot.time!) }}</div>
            <div class="time-state">
              {{ getStateText(slot.Status, slot.hairreservation) }}
            </div>
          </div>
        </div>
      </template>

      <!-- 无数据时显示 -->
      <div class="noData" v-else>
        <BaseEmpty>今日休息</BaseEmpty>
      </div>
    </div>
  </div>

  <!-- 预约信息弹窗 -->
  <up-popup
    :show="showPopup"
    :round="10"
    mode="bottom"
    @close="closePopup"
    :closeable="true"
  >
    <div>
      <div class="popup-header">预约信息</div>

      <!-- 普通用户视图 -->
      <div class="popup-content" v-if="!useRoot">
        <div class="info-section">
          <div class="info-row">
            <span class="info-label">预约时间：</span>
            <span class="info-value"
              >{{ selectedDate }} {{ selectedTime }}</span
            >
          </div>
          <div class="info-row">
            <span class="info-label">预约人：</span>
            <span class="info-value">{{ userInfo.name }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">手机号码：</span>
            <span class="info-value">{{ userInfo.phone }}</span>
          </div>
        </div>
        <BaseButton btn-type="save" @click="submitReservation" size="large"
          >提交预约</BaseButton
        >
      </div>

      <!-- 管理员视图 -->
      <div v-else>
        <div class="info-section">
          <div class="info-row admin-row">
            <span class="info-label admin-label">预约人：</span>
            <span>{{ selectedSlot?.hairreservation!.customerName }}</span>
          </div>
          <div class="info-row admin-row">
            <span class="info-label admin-label">手机号码：</span>
            <span>{{ selectedSlot?.hairreservation!.customerPhone }}</span>
            <div
              class="phone-icon"
              @click="
                makePhoneCall(selectedSlot?.hairreservation!.customerPhone!)
              "
            >
              <SlSubSvgIcon subpage="haircut" name="24-24-2" size="24" />
            </div>
          </div>
          <div class="info-row admin-row">
            <span class="info-label admin-label">预约时间：</span>
            <span>{{ selectedDate }} {{ selectedTime }}</span>
          </div>
          <div class="info-row notification-row">
            <span class="info-label admin-label">通知方式：</span>
            <div class="notification-options">
              <div
                v-for="option in notificationOptions"
                :key="option.value"
                @click="setNotificationType(option.value)"
                class="notification-option"
              >
                <SlSubSvgIcon
                  subpage="haircut"
                  :name="
                    selectedNotification.includes(option.value)
                      ? '16-16-9'
                      : '16-16-8'
                  "
                  size="16"
                />
                {{ option.label }}
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <BaseButton
              btn-type="cancel"
              @click="cancelReservation"
              size="small"
              >取消预约</BaseButton
            >
            <BaseButton btn-type="save" @click="notifyCustomer" size="small"
              >通知到店</BaseButton
            >
          </div>
        </div>
      </div>
    </div>
  </up-popup>

  <!-- 确认对话框 -->
  <ConfirmDialog
    v-if="showConfirmDialog"
    @close="closeConfirmDialog"
    @confirm="handleConfirmAction"
  >
    您确定要取消当前客人的理发预约吗?
  </ConfirmDialog>
</template>

<script setup lang="ts">
// ==================== 类型定义 ====================
/**
 * 用户信息接口
 */
interface UserInfo {
  id: string;
  name: string;
  phone: string;
}

/**
 * 通知类型定义
 */
type NotificationType = "微信推送" | "短信通知" | "机器人推送";

/**
 * 通知选项接口
 */
interface NotificationOption {
  value: NotificationType;
  label: string;
}

// ==================== 导入部分 ====================
import { ref, computed, onMounted } from "vue";
import CalendarComponent from "@/components/CalendarComponent.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import {
  Hairtimeslot,
  TimeSlotState,
  Datas,
} from "./Haircut";
import getAppointmentDate from "@/service/haircut/haircut.service";
import dayjs from "dayjs";
import { usePrincipalStore } from "@/store";
import { onShow } from "@dcloudio/uni-app";
// ==================== 状态管理 ====================
const principalStore = usePrincipalStore();

// ==================== 响应式数据 ====================
const userInfo = ref<UserInfo>({
  id: "",
  name: "",
  phone: "",
});

const selectedDate = ref(dayjs().format("YYYY-MM-DD")); // 当前选择的日期
const backendDateData = ref([]); // 后端返回的日期数据
const selectedTime = ref(""); // 选择的时间段
const selectedSlot = ref<Hairtimeslot | null>(null); // 选择的时段对象
const showPopup = ref(false); // 是否显示弹窗
const showConfirmDialog = ref(false); // 是否显示确认对话框
const selectedNotification = ref<NotificationType[]>([]); // 选择的通知方式
const currentDate = ref<Datas>(); // 当前日期数据
const backendTimeSlots = ref<any[]>([]); // 后端返回的时间段数据

// ==================== 计算属性 ====================
/**
 * 判断是否是管理员模式
 */
const useRoot = computed(() => props.role);

/**
 * 将时间段分成每行3个
 */
const timeRows = computed(() => {
  const rows = [];
  for (let i = 0; i < backendTimeSlots.value.length; i += 3) {
    rows.push(backendTimeSlots.value.slice(i, i + 3));
  }
  return rows;
});

// ==================== 常量定义 ====================
/**
 * 通知方式选项
 */
const notificationOptions: NotificationOption[] = [
  { value: "微信推送", label: "微信通知" },
  { value: "短信通知", label: "短信通知" },
  { value: "机器人推送", label: "机器人语音电话通知" },
];

// ==================== 组件Props ====================
const props = defineProps({
  // 角色标识，true为管理员，false为普通用户
  role: {
    type: Boolean,
    default: false,
  },
});

// ==================== 事件发射器 ====================
const emit = defineEmits(["timeSelected", "timeSelectedPOP"]);

// ==================== 方法定义 ====================

/**
 * 处理并排序时间段数据
 * @param backendSlots 后端返回的时间段数据
 * @returns 处理后的时间段数组
 */
function processTimeSlots(backendSlots: Hairtimeslot[]): any[] {
  return backendSlots
    .sort((a, b) => a.startTime.localeCompare(b.startTime))
    .map((slot) => {
      // 状态转换逻辑
      let displayStatus = slot.status;
      if (useRoot.value) {
        displayStatus =
          slot.status === 0 ? 3 : slot.status === 1 ? 4 : slot.status;
      }

      return {
        ...slot,
        Status: displayStatus, // 转换后的状态
        time: formatTimeRange(slot.startTime, slot.endTime),
      };
    });
}

/**
 * 处理日期变化
 * @param date 选择的日期
 */
function handleDateChange(date: string) {
  console.log(date,'选择的日期');
  getHairdateconfigByBusinessDate(date);
}

/**
 * 选择时间段
 * @param slot 选择的时间段对象
 */
function selectTimeSlot(slot: Hairtimeslot) {
  console.log(selectedDate.value,'selectedDate.value');
  if (slot.Status === 0 && !useRoot.value) {
    // 可预约 用户端
    selectedSlot.value = slot;
    selectedTime.value = formatTimeDisplay(slot.time!);
    showPopup.value = true;
  } else if (slot.Status === 4) {
    // 管理员端 用户信息
    selectedSlot.value = slot;
    selectedTime.value = formatTimeDisplay(slot.time!);
    showPopup.value = true;
    const methodsStr =
      slot.hairreservation?.hairnotificationList?.[0]?.noticeMethod || "";
    selectedNotification.value = methodsStr.split(",") as NotificationType[];
  }
}

/**
 * 关闭弹窗
 */
function closePopup() {
  showPopup.value = false;
}

/**
 * 提交预约
 */
async function submitReservation() {
  try {
    await getAppointmentDate.getStaffAppointment({
      timeslotId: selectedSlot.value!.id,
    });
    emit("timeSelectedPOP", {
      showPopup: true,
      statePopTitle: "预约成功",
      statePopType: "success",
      statePopIcon: "40-40-3",
      confirmText: "查看预约记录",

    });
    getHairdateconfigByBusinessDate(selectedDate.value);
  } catch (error) {
    // 错误处理
    emit("timeSelectedPOP", {
      showPopup: true,
      statePopTitle: "预约失败",
      statePopType: "warning",
      statePopIcon: "40-40-5",
      confirmText: "返回",
    });
  } finally {
    closePopup();
  }
}

/**
 * 格式化时间显示
 * @param timeStr 时间字符串
 * @returns 格式化后的时间
 */
function formatTimeDisplay(timeStr: string): string {
  return timeStr.replace("-", "~");
}

/**
 * 格式化时间范围显示为 "09:30~10:00"
 * @param start 开始时间
 * @param end 结束时间
 * @returns 格式化后的时间范围字符串
 */
function formatTimeRange(start: string, end: string): string {
  const startTime = start.split(" ")[1]; // 提取 "09:30"
  const endTime = end.split(" ")[1]; // 提取 "10:00"
  return `${startTime}~${endTime}`;
}

/**
 * 获取时间段状态文本
 * @param state 状态码
 * @param reservation 预约信息
 * @returns 对应的状态文本
 */
function getStateText(state: TimeSlotState, reservation?: any): string {
  const stateTexts = [
    "可预约", // 0
    "已约满", // 1
    "不可约", // 2
    "未预约", // 3
    reservation?.customerName || "用户预约", // 4
  ];

  return stateTexts[state] || "";
}

/**
 * 设置通知方式
 * @param type 通知类型
 */
function setNotificationType(type: NotificationType) {
  const index = selectedNotification.value.indexOf(type);
  if (index > -1) {
    selectedNotification.value.splice(index, 1); // 取消选中
  } else {
    selectedNotification.value.push(type); // 添加选中
  }
}

/**
 * 取消预约
 */
function cancelReservation() {
  showPopup.value = false;
  showConfirmDialog.value = true;
}

/**
 * 通知客户到店
 */
async function notifyCustomer() {
  if (selectedNotification.value.length === 0) {
    uni.showToast({ title: "请选择通知方式", icon: "none" });
    return;
  }
  
  const seleData = selectedSlot.value?.hairreservation?.hairnotificationList?.[0];
  const data: {
    noticeMethod: string;
    id?: string; 
    reservationId?: string | number; 
  } = {
    noticeMethod: selectedNotification.value
      .filter(item => item.trim() !== "")  // 过滤空值
      .join(","),
  };
  if (seleData?.id) {
    data.id = seleData.id;
  }
  
  if (selectedSlot.value?.hairreservation?.id) {
    data.reservationId = selectedSlot.value.hairreservation.id;
  }
  
  try {
    await getAppointmentDate.notifyCustomer(data);
    getHairdateconfigByBusinessDate(selectedDate.value);
    uni.showToast({ title: "通知成功", icon: "none" });
  } catch (error) {
    uni.showToast({ title: "通知失败，请重试~", icon: "none" });
  } finally {
    showPopup.value = false;
  }
}

/**
 * 拨打电话
 * @param phoneNumber 电话号码
 */
function makePhoneCall(phoneNumber: string) {
  uni.makePhoneCall({
    phoneNumber,
    success: () => console.log("拨打成功"),
    fail: (err) => console.error("拨打失败", err),
  });
}

/**
 * 关闭确认对话框
 */
function closeConfirmDialog() {
  showConfirmDialog.value = false;
}

/**
 * 处理确认对话框的确认操作
 */
async function handleConfirmAction() {
  const { id, timeslotId } = selectedSlot.value?.hairreservation!;
  const data = { id, timeslotId };
  
  try {
    await getAppointmentDate.getAdminCancelAppointment(data);
    showConfirmDialog.value = false;
    uni.showToast({ title: "取消成功", icon: "none" });
    getHairdateconfigByBusinessDate(selectedDate.value);
  } catch (error) {
    uni.showToast({ title: "取消失败", icon: "none" });
  }
}

/**
 * 获取指定日期的理发配置数据
 */
const getHairdateconfigByBusinessDate = async (date:string) => {
  try {
    const data: Datas = await getAppointmentDate.getAppointmentDateRange(date);//TODO日期需要变动
    currentDate.value = data;
    // 将 API 返回的 hairTimeSlot 处理后赋值给 backendTimeSlots
    if (data.hairtimeslot && data.hairtimeslot.length > 0) {
      backendTimeSlots.value = processTimeSlots(data.hairtimeslot);
    } else {
      backendTimeSlots.value = []; // 如果没有数据，设为空数组
    }

    console.log("预约时间段:", backendTimeSlots.value);
  } catch (error) {
    console.error("获取时间段数据失败:", error);
    backendTimeSlots.value = []; // 出错时设为空数组
  }
};

// ==================== 生命周期钩子 ====================
onMounted(() => {
  // 初始化加载数据
  getAppointmentDate.getAppointmentDate().then((res) => {
    backendDateData.value = res;
  });
  
  // 获取用户身份信息
  principalStore.identity().then((res: any) => {
    userInfo.value = res;
  });
});
onShow(()=>{})
</script>

<style scoped lang="scss">
/* ==================== 主容器样式 ==================== */
.ha-main {
  padding: 0 15px;
}

/* ==================== 时间段容器样式 ==================== */
.timeContainer {
  border-top: 1px solid rgba(153, 153, 153, 0.2);
  padding-top: 7px;

  /* 今日理发人数 */
  .today-count {
    font-size: 14px;
    font-weight: bolder;
    margin-bottom: 10px;

    .highlight {
      color: red;
    }
  }

  /* 时间段行样式 */
  .row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 10px;
    
    /* 单个时间段样式 */
    .time-slot {
      border: 1px solid #efefef;
      border-radius: 5px;
      // width: 90px;
      padding: 9px;
      text-align: center;
      background-color: #fff;
      cursor: pointer;

      .time-text {
        font-size: 12px;
        color: #333333;
      }

      .time-state {
        font-size: 12px;
        color: #0da444;
      }

      /* 不可用状态 */
      &.unavailable {
        background-color: #efefef;
        cursor: not-allowed;

        .time-text,
        .time-state {
          color: #999999;
        }
      }

      /* 用户预约状态 */
      &.userstyle {
        .time-text,
        .time-state {
          color: #4f7af6;
        }
      }

      /* 选中状态 */
      &.selected {
        background-color: #f3fff9;
        border-color: #1db968;
      }
    }
  }

  /* 无数据样式 */
  .noData {
    text-align: center;
    padding: 20px 0;
  }
}

/* ==================== 弹窗样式 ==================== */
.popup-header {
  height: 42px;
  line-height: 42px;
  border-bottom: 1px solid rgba(153, 153, 153, 0.2);
  padding: 0 15px;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
}

/* ==================== 信息区域样式 ==================== */
.info-section {
  padding: 10px 15px;

  .info-row {
    margin-bottom: 10px;
    font-size: 14px;

    .info-label {
      color: #666666;
    }

    .info-value {
      float: right;
    }

    /* 管理员行样式 */
    &.admin-row {
      display: flex;
      position: relative;

      .admin-label {
        width: 82px;
        display: block;
      }
    }

    /* 通知选项行样式 */
    &.notification-row {
      display: flex;
      .admin-label {
        width: 82px;
        display: block;
      }
    }
  }

  /* 电话图标样式 */
  .phone-icon {
    position: absolute;
    right: 0px;
  }

  /* 通知选项容器 */
  .notification-options {
    flex: 1;

    .notification-option {
      // display: flex;
      align-items: center;
      margin-bottom: 10px;
      cursor: pointer;

      .SlSvgIcon {
        margin-right: 12px;
      }
    }
  }
}

/* ==================== 操作按钮容器 ==================== */
.action-buttons {
  display: flex;
  margin-top: 20px;
  justify-content: space-between;
  
  button {
    flex: 1;

    &:first-child {
      margin-right: 10px;
    }
  }
}

</style>