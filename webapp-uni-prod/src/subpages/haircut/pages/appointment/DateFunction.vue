<template>
  <div class="ha-main">
    <!-- 时间 -->
    <CalendarComponent
      @dateChange="handleDateChange"
      :weeks-after="2"
    ></CalendarComponent>
    <!-- 预约时间段 -->
    <div class="timeContainer">
      <template v-if="timeRows.length > 0">
        <div class="row" v-for="(row, rowIndex) in timeRows" :key="rowIndex">
          <div
            v-for="(slot, index) in row"
            :key="index"
            class="row-div"
            :class="{
              unavailable: slot.state === 1 || slot.state === 2,
              selected: selectedSlot && selectedSlot.time === slot.time,
            }"
            @click="clickTime(slot)"
          >
            <div class="r-font">{{ formatTimeDisplay(slot.time) }}</div>
            <div class="r-state">
              {{
                slot.state === 0
                  ? "可预约"
                  : slot.state === 1
                  ? "已约满"
                  : "不可约"
              }}
            </div>
          </div>
        </div>
      </template>
      <div class="noData" v-else>
        <BaseEmpty> 今日休息 </BaseEmpty>
      </div>
    </div>
  </div>
  <!-- 弹出框 -->
  <up-popup
    :show="show"
    :round="10"
    mode="bottom"
    @close="close"
    :closeable="true"
  >
    <div>
      <div class="mod-top">预约信息</div>
      <div class="mod-content">
        <div class="mod-row">
          <span class="mod-tl">预约时间：</span>
          <span class="mod-t">{{ selectedDate }} {{ selectedTimeDate }}</span>
        </div>
        <div class="mod-row">
          <span class="mod-tl">预约人：</span>
          <span class="mod-t">小明</span>
        </div>
        <div class="mod-row">
          <span class="mod-tl">手机号码：</span>
          <span class="mod-t">158158158158</span>
        </div>
      </div>
      <button @click="submit()">提交预约</button>
    </div>
  </up-popup>
</template>
<script setup lang="ts">
type PopupType = "error" | "info" | "success" | "warning";
import { ref, computed } from "vue";
import CalendarComponent from "@/components/CalendarComponent.vue";
const selectedDate = ref(""); //日期选中
const selectedTimeDate = ref(""); //时间段选中
const selectedSlot = ref<any>(null);
const show = ref(false);
// 定义要触发的事件
const emit = defineEmits(["timeSelected", "timeSelectedPOP"]);
/**日期选中*/
function handleDateChange(date: string) {
  selectedDate.value = date.replace(/-/g, "/");
  // 这里可以添加获取该日期对应时间段的API调用
  // fetchTimeSlotsForDate(selectedDate.value);
}
// 分行展示
const timeRows = computed(() => {
  const rows = [];
  for (let i = 0; i < backendTimeSlots.value.length; i += 3) {
    rows.push(backendTimeSlots.value.slice(i, i + 3));
  }
  return rows;
});
/**时间段选中 */
const clickTime = (item: any) => {
  if (item.state === 0) {
    // 只有可预约的时间段才能被选中
    selectedSlot.value = item;
    selectedTimeDate.value = formatTimeDisplay(item.time);
    show.value = true;
  }
};
/**弹窗关闭逻辑*/
function close() {
  show.value = false;
}
/**提交预约*/
function submit() {
  // selectedTimeDate.value
  // selectedDate.value
  // 预约成功
  close();
  setTimeout(() => {
    emit("timeSelectedPOP", {
      showPopup: true,
      statePopTitle: "预约成功",
      statePopType: "success",
      statePopIcon: "components-succeed",
      confirmText: "查看预约记录",
      // contentText.value = "当前时间段可能已被预约，请刷新重试"
    });
  }, 500);
}

// 格式化时间显示
const formatTimeDisplay = (timeStr: string) => {
  return timeStr.replace("-", "~");
};
// 模拟API调用函数
async function fetchTimeSlotsForDate(date: string) {
  try {
    // const response = await yourApiService.getTimeSlots(date);
    // backendTimeSlots.value = response.data;
  } catch (error) {
    console.error("获取时间段失败:", error);
  }
}
// 模拟后端返回的数据
const backendTimeSlots = ref([
  { time: "08:30-09:00", state: 0 },
  { time: "09:00-09:30", state: 0 },
  { time: "09:30-10:00", state: 1 },
  { time: "10:00-10:30", state: 0 },
  { time: "10:30-11:00", state: 2 },
  { time: "11:00-11:30", state: 0 },
  { time: "13:30-14:00", state: 0 },
  { time: "14:00-14:30", state: 0 },
  { time: "14:30-15:00", state: 1 },
  { time: "15:00-15:30", state: 0 },
  { time: "15:30-16:00", state: 0 },
  { time: "16:00-16:30", state: 2 },
  { time: "16:30-17:00", state: 0 },
]);
</script>
<style scoped lang="scss">
.ha-main {
  padding: 0 15px;
}
.timeContainer {
  border-top: 1px solid #99999933;
  padding-top: 7px;
  .row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    background-color: #ffffff;
    .row-div {
      border: 1px solid #efefef;
      border-radius: 5px;
      width: 90px;
      padding: 9px;
      text-align: center;
      background-color: #fff;
      cursor: pointer;

      .r-font {
        font-size: 12px;
        color: #333333;
      }

      .r-state {
        font-size: 12px;
        color: #0da444;
      }

      &.unavailable {
        background-color: #efefef;
        cursor: not-allowed;

        .r-font,
        .r-state {
          color: #999999;
        }
      }

      &.booked {
        background-color: #fff9e6;
        border-color: #ffd700;

        .r-state {
          color: #ffa500;
        }
      }

      &.selected {
        background-color: #f3fff9;
        border-color: #1db968;
      }
    }
  }
}

.mod-top {
  height: 42px;
  line-height: 42px;
  border-bottom: 1px solid #99999933;
  padding: 0 15px;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
}
.mod-content {
  padding: 10px 15px;
  .mod-row {
    margin-bottom: 10px;
    font-size: 14px;
    .mod-tl {
      color: #666666;
    }
    .mod-t {
      float: right;
    }
  }
}
button {
  width: 100%;
  height: 40px;
}
</style>
