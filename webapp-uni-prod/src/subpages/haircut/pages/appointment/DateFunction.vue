<template>
  <div class="ha-main">
    <!-- 日历组件 -->
    <CalendarComponent
      @dateChange="handleDateChange"
      :weeks-after="2"
    ></CalendarComponent>
    
    <!-- 时间段选择区域 -->
    <div class="timeContainer">
      <!-- 管理员模式下显示今日理发人数 -->
      <div v-if="useRoot" class="today-count">
        <span>今日需理发<span class="highlight">1</span>人</span>
      </div>
      
      <!-- 时间段展示 -->
      <template v-if="timeRows.length > 0">
        <div class="row" v-for="(row, rowIndex) in timeRows" :key="rowIndex">
          <div
            v-for="(slot, index) in row"
            :key="index"
            class="time-slot"
            :class="{
              unavailable: slot.state === 1 || slot.state === 2,
              selected: selectedSlot && selectedSlot.time === slot.time,
            }"
            @click="selectTimeSlot(slot)"
          >
            <div class="time-text">{{ formatTimeDisplay(slot.time) }}</div>
            <div class="time-state">
              {{ getStateText(slot.state) }}
            </div>
          </div>
        </div>
      </template>
      
      <!-- 无数据时显示 -->
      <div class="noData" v-else>
        <BaseEmpty>今日休息</BaseEmpty>
      </div>
    </div>
  </div>
  
  <!-- 预约信息弹窗 -->
  <up-popup
    :show="showPopup"
    :round="10"
    mode="bottom"
    @close="closePopup"
    :closeable="true"
  >
    <div>
      <div class="popup-header">预约信息</div>
      
      <!-- 普通用户视图 -->
      <div class="popup-content" v-if="!useRoot">
        <div class="info-section">
          <div class="info-row">
            <span class="info-label">预约时间：</span>
            <span class="info-value">{{ selectedDate }} {{ selectedTime }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">预约人：</span>
            <span class="info-value">小明</span>
          </div>
          <div class="info-row">
            <span class="info-label">手机号码：</span>
            <span class="info-value">158158158158</span>
          </div>
        </div>
        <button @click="submitReservation" class="submit-btn">提交预约</button>
      </div>
      
      <!-- 管理员视图 -->
      <div v-else>
        <div class="info-section">
          <div class="info-row admin-row">
            <span class="info-label admin-label">预约人：</span>
            <span>小明</span>
          </div>
          <div class="info-row admin-row">
            <span class="info-label admin-label">手机号码：</span>
            <span>158158158158</span>
            <div class="phone-icon" @click="makePhoneCall('10086')">
              <SlSvgIcon name="components-2" size="24" />
            </div>
          </div>
          <div class="info-row admin-row">
            <span class="info-label admin-label">预约时间：</span>
            <span>{{ selectedDate }} {{ selectedTime }}</span>
          </div>
          <div class="info-row notification-row">
            <span class="info-label admin-label">通知方式：</span>
            <div class="notification-options">
              <div v-for="option in notificationOptions" 
                   :key="option.value"
                   @click="setNotificationType(option.value)"
                   class="notification-option">
                <SlSvgIcon
                  :name="selectedNotification === option.value 
                    ? 'components-selected' 
                    : 'components-unselected'"
                  size="16"
                />
                {{ option.label }}
              </div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <button @click="cancelReservation" class="cancel-btn">取消预约</button>
            <button @click="notifyCustomer" class="notify-btn">通知到店</button>
          </div>
        </div>
      </div>
    </div>
  </up-popup>
  
  <!-- 确认对话框 -->
  <ConfirmDialog
    v-if="showConfirmDialog"
    @close="closeConfirmDialog"
    @confirm="handleConfirmAction"
  >
    您确定要取消当前客人的理发预约吗?
  </ConfirmDialog>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import SlSvgIcon from "@/components/SlSVgIcon.vue";
import CalendarComponent from "@/components/CalendarComponent.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";

// 类型定义
type TimeSlotState = 0 | 1 | 2; // 0=可预约, 1=已约满, 2=不可约
type NotificationType = "wechat" | "sms" | "robot";
type TimeSlot = {
  time: string;
  state: TimeSlotState;
};

interface NotificationOption {
  value: NotificationType;
  label: string;
}

// 响应式数据
const selectedDate = ref(""); // 选择的日期
const selectedTime = ref(""); // 选择的时间段
const selectedSlot = ref<TimeSlot | null>(null); // 选择的时段对象
const showPopup = ref(false); // 是否显示弹窗
const useRoot = ref(false); // 是否是管理员模式
const showConfirmDialog = ref(false); // 是否显示确认对话框
const selectedNotification = ref<NotificationType>("wechat"); // 选中的通知方式

// 通知方式选项
const notificationOptions: NotificationOption[] = [
  { value: "wechat", label: "微信通知" },
  { value: "sms", label: "短信通知" },
  { value: "robot", label: "机器人语音电话通知" }
];

// 模拟后端返回的时间段数据
const backendTimeSlots = ref<TimeSlot[]>([
  { time: "08:30-09:00", state: 0 },
  { time: "09:00-09:30", state: 0 },
  { time: "09:30-10:00", state: 1 },
  { time: "10:00-10:30", state: 0 },
  { time: "10:30-11:00", state: 2 },
  { time: "11:00-11:30", state: 0 },
  { time: "13:30-14:00", state: 0 },
  { time: "14:00-14:30", state: 0 },
  { time: "14:30-15:00", state: 1 },
  { time: "15:00-15:30", state: 0 },
  { time: "15:30-16:00", state: 0 },
  { time: "16:00-16:30", state: 2 },
  { time: "16:30-17:00", state: 0 },
]);

// 计算属性：将时间段分成每行3个
const timeRows = computed(() => {
  const rows = [];
  for (let i = 0; i < backendTimeSlots.value.length; i += 3) {
    rows.push(backendTimeSlots.value.slice(i, i + 3));
  }
  return rows;
});

// 事件发射器
const emit = defineEmits(["timeSelected", "timeSelectedPOP"]);

/**
 * 处理日期变化
 * @param date 选择的日期
 */
function handleDateChange(date: string) {
  selectedDate.value = date.replace(/-/g, "/");
  // 这里可以添加获取该日期对应时间段的API调用
  // fetchTimeSlotsForDate(selectedDate.value);
}

/**
 * 选择时间段
 * @param slot 选择的时间段对象
 */
function selectTimeSlot(slot: TimeSlot) {
  // if (slot.state === 0) { // 只有可预约的时间段才能被选中
  //   selectedSlot.value = slot;
  //   selectedTime.value = formatTimeDisplay(slot.time);
  //   showPopup.value = true;
  // }
  uni.navigateTo({ url: '/subpages/haircut/pages/appointment/components/haircut-config/index' })
  // uni.navigateTo({ url: '/subpages/haircut/pages/appointment/components/haircut-handle/index' })
}

/**
 * 关闭弹窗
 */
function closePopup() {
  showPopup.value = false;
}

/**
 * 提交预约
 */
function submitReservation() {
  closePopup();
  setTimeout(() => {
    emit("timeSelectedPOP", {
      showPopup: true,
      statePopTitle: "预约成功",
      statePopType: "success",
      statePopIcon: "components-succeed",
      confirmText: "查看预约记录"
    });
  }, 500);
}

/**
 * 格式化时间显示
 * @param timeStr 时间字符串
 * @returns 格式化后的时间
 */
function formatTimeDisplay(timeStr: string): string {
  return timeStr.replace("-", "~");
}

/**
 * 获取时间段状态文本
 * @param state 状态码
 * @returns 对应的状态文本
 */
function getStateText(state: TimeSlotState): string {
  const stateTexts = ["可预约", "已约满", "不可约"];
  return stateTexts[state] || "";
}

/**
 * 设置通知方式
 * @param type 通知类型
 */
function setNotificationType(type: NotificationType) {
  selectedNotification.value = type;
}

/**
 * 取消预约
 */
function cancelReservation() {
  showPopup.value = false;
  showConfirmDialog.value = true;
}

/**
 * 通知客户到店
 */
function notifyCustomer() {
  showPopup.value = false;
  console.log("通知方式:", selectedNotification.value);
}

/**
 * 拨打电话
 * @param phoneNumber 电话号码
 */
function makePhoneCall(phoneNumber: string) {
  uni.makePhoneCall({
    phoneNumber,
    success: () => console.log("拨打成功"),
    fail: (err) => console.error("拨打失败", err),
  });
}

/**
 * 关闭确认对话框
 */
function closeConfirmDialog() {
  showConfirmDialog.value = false;
}

/**
 * 处理确认对话框的确认操作
 */
function handleConfirmAction() {
  showConfirmDialog.value = false;
  console.log("执行取消预约操作");
}
</script>

<style scoped lang="scss">
/* 主容器样式 */
.ha-main {
  padding: 0 15px;
}

/* 时间段容器样式 */
.timeContainer {
  border-top: 1px solid rgba(153, 153, 153, 0.2);
  padding-top: 7px;
  
  .today-count {
    font-size: 14px;
    font-weight: bolder;
    margin-bottom: 10px;
    
    .highlight {
      color: red;
    }
  }
  
  /* 时间段行样式 */
  .row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    background-color: #ffffff;
    
    /* 单个时间段样式 */
    .time-slot {
      border: 1px solid #efefef;
      border-radius: 5px;
      width: 90px;
      padding: 9px;
      text-align: center;
      background-color: #fff;
      cursor: pointer;
      
      .time-text {
        font-size: 12px;
        color: #333333;
      }
      
      .time-state {
        font-size: 12px;
        color: #0da444;
      }
      
      /* 不可用状态 */
      &.unavailable {
        background-color: #efefef;
        cursor: not-allowed;
        
        .time-text,
        .time-state {
          color: #999999;
        }
      }
      
      /* 选中状态 */
      &.selected {
        background-color: #f3fff9;
        border-color: #1db968;
      }
    }
  }
  
  /* 无数据样式 */
  .noData {
    text-align: center;
    padding: 20px 0;
  }
}

/* 弹窗头部样式 */
.popup-header {
  height: 42px;
  line-height: 42px;
  border-bottom: 1px solid rgba(153, 153, 153, 0.2);
  padding: 0 15px;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
}

/* 信息区域样式 */
.info-section {
  padding: 10px 15px;
  
  .info-row {
    margin-bottom: 10px;
    font-size: 14px;
    
    .info-label {
      color: #666666;
    }
    
    .info-value {
      float: right;
    }
    
    /* 管理员行样式 */
    &.admin-row {
      display: flex;
      position: relative;
      
      .admin-label {
        width: 82px;
        display: block;
      }
    }
    
    /* 通知选项行样式 */
    &.notification-row {
      display: flex;
    }
  }
  
  /* 电话图标样式 */
  .phone-icon {
    position: absolute;
    right: 0px;
  }
  
  /* 通知选项容器 */
  .notification-options {
    flex: 1;
    
    .notification-option {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      cursor: pointer;
      
      .SlSvgIcon {
        margin-right: 12px;
      }
    }
  }
}

/* 按钮基础样式 */
button {
  width: 100%;
  height: 40px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  
  &.submit-btn {
    background-color: #1db968;
    color: white;
  }
  
  &.cancel-btn {
    background-color: #f5f5f5;
    color: #333;
    margin-right: 10px;
  }
  
  &.notify-btn {
    background-color: #1db968;
    color: white;
  }
}

/* 操作按钮容器 */
.action-buttons {
  display: flex;
  margin-top: 20px;
  
  button {
    flex: 1;
    
    &:first-child {
      margin-right: 10px;
    }
  }
}
</style>