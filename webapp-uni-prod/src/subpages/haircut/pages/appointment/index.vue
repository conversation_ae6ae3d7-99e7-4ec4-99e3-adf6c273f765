<template>
  <div class="ha-vessel">
    <div class="ha-img"></div>
    <CalendarComponent
      @dateChange="handleDateChange"
      :weeks-after="2"
    ></CalendarComponent>
    <div class="timeContainer">
      <div class="row" v-for="(row, rowIndex) in timeRows" :key="rowIndex">
        <div
          v-for="(slot, index) in row"
          :key="index"
          class="row-div"   
          :class="{
            unavailable:
              slot.status === 'unavailable' || slot.status === 'full',
            selected:
              selectedSlot &&
              selectedSlot.start === slot.start &&
              selectedSlot.end === slot.end,
          }"
          @click="clickTime(slot)"
        >
          <div class="r-font">{{ slot.start }}~{{ slot.end }}</div>
          <div class="r-state">
            {{
              slot.status === "available"
                ? "可预约"
                : slot.status === "full"
                ? "已约满"
                : "不可约"
            }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import CalendarComponent from "@/components/CalendarComponent.vue";
const selectedDate = ref("");

const startTime = "08:30";
const endTime = "17:00";
const intervalMinutes = 30;

// 中午休息时间段
const restStart = "11:30";
const restEnd = "13:30";

// 时间格式化
function formatTime(h: any, m: any) {
  return `${String(h).padStart(2, "0")}:${String(m).padStart(2, "0")}`;
}

// 判断时间是否在某个时间段内
function isInRestPeriod(start: any, end: any, restStart: any, restEnd: any) {
  return !(end <= restStart || start >= restEnd);
}

// 生成时间段 - 模拟后端数据
function generateTimeSlots(start: any, end: any, interval: any) {
  const [startH, startM] = start.split(":").map(Number);
  const [endH, endM] = end.split(":").map(Number);
  const [restStartH, restStartM] = restStart.split(":").map(Number);
  const [restEndH, restEndM] = restEnd.split(":").map(Number);

  let hour = startH;
  let minute = startM;
  const slots = [];

  while (hour < endH || (hour === endH && minute < endM)) {
    const slotStart = formatTime(hour, minute);
    let nextMinute = minute + interval;
    let nextHour = hour;
    if (nextMinute >= 60) {
      nextHour += Math.floor(nextMinute / 60);
      nextMinute = nextMinute % 60;
    }
    const slotEnd = formatTime(nextHour, nextMinute);

    // 跳过中午休息时间段
    if (!isInRestPeriod(slotStart, slotEnd, restStart, restEnd)) {
      // 模拟不同的状态
      let status = "available";
      const random = Math.random();
      if (random < 0.3) {
        status = "unavailable";
      } else if (random < 0.9) {
        status = "full";
      }

      slots.push({
        start: slotStart,
        end: slotEnd,
        status: status,
      });
    }

    hour = nextHour;
    minute = nextMinute;
  }

  return slots;
}

// 分行展示
const timeRows = computed(() => {
  const slots = generateTimeSlots(startTime, endTime, intervalMinutes);
  const rows = [];
  for (let i = 0; i < slots.length; i += 3) {
    rows.push(slots.slice(i, i + 3));
  }
  return rows;
});

const selectedSlot: any = ref(null);
const clickTime = (item: any) => {
  // 只有可预约的时间段才能被选中
  if (item.status === "available") {
    selectedSlot.value = item;
    console.log(item, "item");
  }
};

function handleDateChange(date: string) {
  selectedDate.value = date;
  // 其他逻辑...
}
</script>
<style scoped lang="scss">
.ha-vessel {
  height: 100%;
  width: 100%;
  background-color: #f8f8f8;
  .ha-img {
    height: 120px;
    width: 100%;
    border: 1px solid #000;
  }
  .timeContainer {
    .row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;

      .row-div {
        border: 1px solid #efefef;
        border-radius: 5px;
        width: 90px;
        padding: 9px;
        text-align: center;
        background-color: #fff;
        cursor: pointer;

        .r-font {
          font-size: 12px;
          color: #333333;
        }

        .r-state {
          font-size: 12px;
          color: #0da444;
        }

        &.unavailable {
          background-color: #efefef;
          cursor: not-allowed;

          .r-font,
          .r-state {
            color: #999999;
          }
        }

        &.booked {
          background-color: #fff9e6;
          border-color: #ffd700;

          .r-state {
            color: #ffa500;
          }
        }

        &.selected {
          background-color: #f3fff9;
          border-color: #1db968;
        }
      }
    }
  }
}
</style>
