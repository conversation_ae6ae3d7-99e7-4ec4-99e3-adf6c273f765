<template>
  <div class="ha-vessel">
    <div class="ha-img">
      <image src="@/static/images/haircut/理发banner.png" mode="aspectFill" />
    </div>
    <!-- 预约时间模块 -->
    <DateFunction
      v-if="!myInfo"
      @timeSelectedPOP="handleTimeSelected"
    ></DateFunction>
    <!-- 预约详情 -->
    <div class="ha-detail" v-else>
      <div class="ha-my">我的预约</div>
      <div class="ha-detail-c">
        <div class="ha-detail-div">
          <span class="ha-detail-span">预约人：</span>
          <span>小明</span>
        </div>
        <div class="ha-detail-div">
          <span class="ha-detail-span">手机号码：</span>
          <span>158158158158</span>
        </div>
        <div class="ha-detail-div">
          <span class="ha-detail-span">预约时间：</span>
          <span>2025/0602 10:30~11:00</span>
        </div>
        <div class="footer">
          <button @click="callBarber()">联系理发师</button>
          <button @click="cancelAppointment()">取消预约</button>
        </div>
      </div>
    </div>
  </div>
  <StatePop
    v-model:show="showPopup"
    :title="statePopTitle"
    :type="statePopType"
    :confirmText="confirmText"
    :content="contentText"
    :icon="statePopIcon"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
  </StatePop>

  <ConfirmDialog
    v-if="showConfirm"
    @close="showConfirm = false"
    @confirm="handleConfirmDialog"
    >您确定要取消当前理发预约吗？</ConfirmDialog
  >
</template>

<script setup lang="ts">
type PopupType = "error" | "info" | "success" | "warning";
import { ref, computed } from "vue";
import DateFunction from "./DateFunction.vue";
import StatePop from "@/components/StatePop.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
const selectedDate = ref(""); //日期选中
const selectedTimeDate = ref(""); //时间段选中
const show = ref(false);
const myInfo = ref(false);
// 弹窗变量
const showPopup = ref(false);
const statePopTitle = ref("");
const statePopType = ref<PopupType>("info"); // 设置默认值
const confirmText = ref("");
const statePopIcon = ref("");
const contentText = ref("");

const showConfirm = ref(false);
/**时间模块的回调*/ 
const handleTimeSelected = (data: any) => {
  showPopup.value = data.showPopup;
  statePopTitle.value = data.statePopTitle;
  statePopType.value = data.statePopType;
  statePopIcon.value = data.statePopIcon;
  confirmText.value = data.confirmText;
};
/**弹窗关闭逻辑*/
function close() {
  show.value = false;
}
/**提交预约*/
function submit() {
  // selectedTimeDate.value
  // selectedDate.value
  // 预约成功
  close();
  setTimeout(() => {
    showPopup.value = true;
    statePopTitle.value = "预约成功";
    statePopType.value = "success";
    statePopIcon.value = "components-succeed";
    confirmText.value = "查看预约记录";
    // contentText.value = "当前时间段可能已被预约，请刷新重试"
  }, 500);
}

//弹窗确认
const handleConfirm = () => {
  console.log("确认按钮点击");
  myInfo.value = true;
};
//弹窗取消
const handleCancel = () => {
  console.log("取消按钮点击");
};

// 详情
function callBarber() {
  //电话
  uni.makePhoneCall({
    phoneNumber: "12345678900", // 替换为要拨打的号码
    success: () => {
      console.log("拨打成功");
    },
    fail: (err) => {
      console.error("拨打失败", err);
    },
  });
}
// 取消预约
function cancelAppointment() {
  showConfirm.value = true;
}
// 确认弹窗的确认按钮
function handleConfirmDialog() {
  console.log("点了什么");
  // showPopup.value = true
  // statePopTitle.value = '取消成功'
  // statePopType.value = "success"
  // statePopIcon.value = "components-succeed"
  // confirmText.value = "返回"
}
</script>

<style scoped lang="scss">
/* 样式保持不变 */
.ha-vessel {
  height: 100%;
  width: 100%;
  .ha-img {
    height: 120px;
    width: 100%;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
button {
  width: 100%;
  height: 40px;
}
.noData {
  position: absolute;
  top: 50%;
  left: 42%;
}

.ha-detail {
  padding: 18px 15px;
  .ha-my {
    font-weight: bold;
    font-size: 14px;
  }
  .ha-detail-c {
    margin-top: 12px;
    min-height: 172px;
    width: 100%;
    opacity: 1;
    border-radius: 10px;
    background: #ffffff;
    box-shadow: 0px 0px 6px 0px #92929233;
    padding: 10px 15px;
    box-sizing: border-box;
    .ha-detail-div {
      margin-bottom: 10px;
      display: flex;
      .ha-detail-span {
        width: 70px;
        font-size: 14px;
        color: #666666;
        width: 110px;
        display: block;
      }
    }
    .footer {
      border-top: 1px solid #99999933;
      padding: 14px 0px;
      display: flex;
      button {
        width: 120px;
      }
    }
  }
}
</style>
