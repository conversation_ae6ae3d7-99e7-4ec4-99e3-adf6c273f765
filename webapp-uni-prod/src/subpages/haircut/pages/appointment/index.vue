<template>
  <SlTabbarPage :tab-bar-root="'subpages/haircut'" :admin="isAdminRole" >
    <div class="haircut-container">
      <!-- 顶部banner图片 -->
      <div class="banner-container">
        <image :src="appStore.getBaseUrl + '/haircut_banner.jpg'" mode="aspectFill" />
      </div>

      <!-- 根据是否有预约信息显示不同内容 -->
      <template v-if="!hasAppointment">
        <!-- 预约时间选择模块 -->
        <DateFunction ref="dateFunctionRef" @timeSelectedPOP="handleTimeSelectedPopup" :role="isAdminRole" />
      </template>
      <template v-else>
        <!-- 我的预约详情 -->
        <div class="appointment-detail">
          <div class="detail-title">我的预约</div>
          <div class="detail-content">
            <div class="detail-row">
              <span class="detail-label">预约人：</span>
              <span>{{ myAppointment?.customerName }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">手机号码：</span>
              <span>{{ myAppointment?.customerPhone }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">预约时间：</span>
              <span class="time-text">{{ formattedAppointmentTime }}</span>
            </div>
            <div class="action-buttons">
              <BaseButton btn-type="cancel" @click="callBarber" size="small" :disabled="isAppointmentCompleted">
                <div class="contact-item">
                  <SlSubSvgIcon subpage="haircut" :name="`20-20-${isAppointmentCompleted ? '35' : '22'}`" size="20"
                    class="inline-icon" />
                  <span class="contact">联系理发师</span>
                </div>
              </BaseButton>
              <BaseButton btn-type="save" @click="showCancelConfirmation" size="small"
                :disabled="isAppointmentCompleted">
                取消预约
              </BaseButton>
            </div>
          </div>
          <div class="tips" v-if="isAppointmentCompleted">
            温馨提示:请等待下一期开放
          </div>
        </div>
      </template>

      <!-- 状态提示弹窗 -->
      <StatePop v-model:show="showStatusPopup" :title="statusPopupTitle" :type="statusPopupType"
        :confirmText="confirmButtonText" :content="popupContent" :icon="statusPopupIcon" @confirm="handlePopupConfirm"
        @close="handlePopupCancel" />

      <!-- 取消预约确认对话框 -->
      <ConfirmDialog v-if="showCancelDialog" @close="closeCancelDialog" @confirm="confirmCancelAppointment">
        您确定要取消当前理发预约吗？
      </ConfirmDialog>
    </div>
  </SlTabbarPage>
</template>

<script setup lang="ts">
// ==================== 类型定义 ====================
/**
 * 状态弹窗类型
 */
type PopupType = "error" | "info" | "success" | "warning";

/**
 * 时间选择弹窗事件数据
 */
interface TimeSelectedEvent {
  showPopup: boolean;
  statePopTitle: string;
  statePopType: PopupType;
  statePopIcon: string;
  confirmText: string;
  popupContent: string;
}

// ==================== 导入部分 ====================
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import DateFunction from "./DateFunction.vue";
import StatePop from "@/components/StatePop.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import hairBaseConfig from "@/subpages/haircut/service/haircut.service";
import { HaircutAppointment } from "@/subpages/haircut/models";
import { useDebounce } from "@/hooks";
import { useAppStore } from "@/store";
const appStore = useAppStore();
// ==================== 响应式数据 ====================
const isAdminRole = ref(false); // 是否为管理员角色
const hasAppointment = ref(false); // 是否有预约信息
const myAppointment = ref<HaircutAppointment | null>(null); // 我的预约信息
const dateFunctionRef = ref();

// 状态弹窗相关
const showStatusPopup = ref(false);
const statusPopupTitle = ref("");
const statusPopupType = ref<PopupType>("info");
const confirmButtonText = ref("");
const statusPopupIcon = ref("");
const popupContent = ref("");

// 取消预约确认对话框
const showCancelDialog = ref(false);

// ==================== 计算属性 ====================
/**
 * 格式化后的预约时间
 */
const formattedAppointmentTime = computed(() => {
  if (!myAppointment.value) return "";
  return formatTimeRange(
    myAppointment.value.hairStartTime,
    myAppointment.value.hairEndTime
  );
});

/**
 * 预约是否已完成
 */
const isAppointmentCompleted = computed(() => {
  return myAppointment.value?.status === 2;
});

// ==================== 方法定义 ====================
/**
 * 格式化时间范围显示
 * @param start 开始时间
 * @param end 结束时间
 * @returns 格式化后的时间字符串 (YYYY-MM-DD HH:mm~HH:mm)
 */
function formatTimeRange(start: string, end: string): string {
  const [date] = start.split(" ");
  const [, startTime] = start.split(" ");
  const [, endTime] = end.split(" ");
  return `${date} ${startTime}~${endTime}`;
}

/**
 * 处理时间选择弹窗事件
 * @param data 弹窗事件数据
 */
function handleTimeSelectedPopup(data: TimeSelectedEvent) {
  showStatusPopup.value = data.showPopup;
  statusPopupTitle.value = data.statePopTitle;
  statusPopupType.value = data.statePopType;
  statusPopupIcon.value = data.statePopIcon;
  confirmButtonText.value = data.confirmText;
  popupContent.value = data.popupContent;
}

/**
 * 弹窗确认按钮处理
 */
function handlePopupConfirm() {
  // 刷新预约信息
  fetchAppointmentInfo();
}

/**
 * 弹窗取消按钮处理
 */
function handlePopupCancel() {
  // hasAppointment.value = !hasAppointment.value;
  const successOrFailureStatuses = ["预约成功", "预约失败", "取消成功"];
  if (successOrFailureStatuses.includes(statusPopupTitle.value)) {
    fetchAppointmentInfo();
  }
}

/**
 * 联系理发师
 */
function callBarber() {
  if (!myAppointment.value?.baberPhone) return;

  uni.makePhoneCall({
    phoneNumber: myAppointment.value.baberPhone,
    success: () => console.log("拨打成功"),
    fail: (err) => console.error("拨打失败", err),
  });
}

/**
 * 显示取消预约确认对话框
 */
function showCancelConfirmation() {
  showCancelDialog.value = true;
}

/**
 * 关闭取消预约对话框
 */
function closeCancelDialog() {
  showCancelDialog.value = false;
}

/**
 * 确认取消预约
 */
async function confirmCancelAppointment() {
  if (!myAppointment.value) return;

  const { id, timeslotId } = myAppointment.value;
  const data = { id, timeslotId };

  try {
    await hairBaseConfig.getStaffCancelAppointment(data);
    // 显示取消成功提示
    showStatusPopup.value = true;
    statusPopupTitle.value = "取消成功";
    statusPopupType.value = "success";
    statusPopupIcon.value = "40-40-3";
    confirmButtonText.value = "确定";

    // 刷新预约信息
    // fetchAppointmentInfo();
  } catch (error) {
    showStatusPopup.value = true;
    statusPopupTitle.value = "取消失败";
    statusPopupType.value = "error";
    statusPopupIcon.value = "40-40-5";
    confirmButtonText.value = "确定";
    console.error("取消预约失败:", error);
  } finally {
    showCancelDialog.value = false;
  }
}

/**
 * 获取预约信息
 */
async function fetchAppointmentInfo() {
  try {
    const data = await hairBaseConfig.haircutReservation();

    if (!data) {
      // 没有预约信息
      hasAppointment.value = false;
      myAppointment.value = null;
    } else {
      // 有预约信息
      hasAppointment.value = true;
      myAppointment.value = data;
    }
  } catch (error) {
    console.error("获取预约信息失败:", error);
    hasAppointment.value = false;
    myAppointment.value = null;
  } finally {
    showStatusPopup.value = false;
  }
}

const routeOptions = ref<any>({}); // 保存 onLoad 传入的参数

// ==================== 生命周期钩子 ====================
onLoad((options: any) => {
  routeOptions.value = options; // 缓存参数
  // 根据传入参数设置角色
  isAdminRole.value = options.type === "admin";
  if (!isAdminRole.value) {
    // 获取预约信息
    fetchAppointmentInfo();
  } else {
    uni.setNavigationBarTitle({
      title: "理发管理",
    });
  }
  console.log(
    isAdminRole.value ? "管理端进入" : "职工端进入",
    isAdminRole.value
  );
});
const debouncedRefresh = useDebounce(async () => {
  try {
    const isAdmin = routeOptions.value.type === "admin";
    isAdminRole.value = isAdmin;
    dateFunctionRef.value?.fetchDateFunctionData?.();
    if (!isAdmin) {
      await fetchAppointmentInfo();
    }
  } finally {
    uni.stopPullDownRefresh();
  }
}, 500);
onPullDownRefresh(debouncedRefresh);
</script>

<style scoped lang="scss">
/* ==================== 主容器样式 ==================== */
.haircut-container {
  // height: 100vh;
  width: 100%;

  .banner-container {
    height: 120px;
    width: 100%;

    image {
      width: 100%;
      height: 100%;
    }
  }
}

/* ==================== 预约详情样式 ==================== */
.appointment-detail {
  padding: 18px 15px;

  .detail-title {
    font-weight: bold;
    font-size: 14px;
  }

  .detail-content {
    margin-top: 12px;
    // min-height: 172px;
    width: 100%;
    opacity: 1;
    border-radius: 10px;
    background: #ffffff;
    box-shadow: 0px 0px 6px 0px #92929233;
    // padding: 10px 15px;
    padding: 10px 15px 0px 10px;
    box-sizing: border-box;

    .detail-row {
      margin-bottom: 10px;
      display: flex;

      .detail-label {
        font-size: 14px;
        color: #666666;
        width: 100px;
        display: block;
      }

      .time-text {
        white-space: nowrap;
      }
    }

    .action-buttons {
      border-top: 1px solid rgba(153, 153, 153, 0.2);
      padding: 14px 0;
      display: flex;
      justify-content: space-between;

      .inline-icon {
        display: inline-flex;
        margin-right: 5px;
      }

      button {
        width: 100%;
        height: 40px;
      }

      .contact-item {
        display: inline-flex;
        align-items: center;

        .contact {
          white-space: nowrap;
        }
      }
    }
  }

  .tips {
    color: #999999;
    font-size: 12px;
    margin: 10px 0px 0px 0px;
  }
}

/* ==================== 无数据样式 ==================== */
.noData {
  position: absolute;
  top: 50%;
  left: 42%;
}
</style>
