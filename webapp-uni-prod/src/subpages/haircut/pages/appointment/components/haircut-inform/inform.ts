export interface NoticeFeedback {
    /** 关联通知反馈信息的唯一标识（新增时可不传，后端会生成） */
    id?: string;
    /** 关联车主 ID */
    personnelId?: string;
    /** 车主姓名（仅列表查询时返回） */
    name?: string;
    /** 车主职位（仅列表查询时返回） */
    position?: string;
    /** 车主联系电话（仅列表查询时返回） */
    phone?: string;
    /** 关联车辆 ID */
    vehicleInfoId?: string;
    /** 车牌号（仅列表查询时返回） */
    licensePlateNumber?: string;
    /** 通知方式 */
    noticeMethod?: string;
    /** 通知时间，如：2023-10-03 09:15:00 */
    noticeTime?: string;
    /** 车主反馈内容 */
    feedbackContent?: string;
  }