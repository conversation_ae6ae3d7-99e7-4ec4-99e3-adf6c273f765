<template>
  <div class="notice-list-container">
    <div class="item" v-for="item of list" :key="item.id">
      <div class="title">
        <view style="display: inline-flex; align-items: center">
          <SlSVgIcon name="16-16-17" size="16" />
          <text class="label" style="margin-left: 8px">通知反馈</text>
        </view>
        <view
          style="display: inline-flex; align-items: center"
          @click="deleteItem(item)"
        >
          <SlSVgIcon name="16-16-16" size="16" />
        </view>
      </div>
      <div class="content">
        <view class="list">
          <view class="list-item">
            <text class="label">姓名</text>
            <text class="val">{{ item.name }}</text>
          </view>
          <view class="list-item">
            <text class="label">职务</text>
            <text class="val">{{ item.position }}</text>
          </view>
          <view class="list-item">
            <text class="label">手机号码</text>
            <text class="val">{{ item.phone }}</text>
          </view>
          <view class="list-item">
            <text class="label">车牌号</text>
            <text class="val">{{ item.licensePlateNumber }}</text>
          </view>
          <view class="list-item">
            <text class="label">通知方式</text>
            <text class="val">{{ item.noticeMethod }}</text>
          </view>
          <view class="list-item">
            <text class="label">通知时间</text>
            <text class="val">{{ item.noticeTime }}</text>
          </view>
          <view class="list-item">
            <text class="label">反馈</text>
            <text class="val danger-text">{{ item.feedbackContent }}</text>
          </view>
        </view>
      </div>
      <div class="actions">
        <button class="actions-btn" @click="infoItem(item)">再次通知</button>
      </div>
    </div>
    <div v-if="!list.length" class="noData">
      <BaseEmpty>
        <text style="color: #999; font-size: 14px"> 暂无反馈数据 </text>
      </BaseEmpty>
    </div>
    <!-- 全部清除 -->
    <div class="cancelAll" v-if="list.length">
      <button class="cancelAll-btn" @click="cancelAll">全部清除</button>
    </div>
  </div>

  <!-- 统一的确认对话框 -->
  <ConfirmDialog
    v-if="showConfirm"
    @close="showConfirm = false"
    @confirm="handleConfirm"
    >{{ confirmMessage }}</ConfirmDialog
  >
</template>

<script lang="ts" setup>
import BaseEmpty from "@/components/BaseEmpty.vue";
import type { NoticeFeedback } from "./inform";
import SlSVgIcon from "@/components/SlSVgIcon.vue";
import { onMounted, ref } from "vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import BaseButton from "@/components/BaseButton.vue";
const showConfirm = ref(false);
const currentId = ref("");
const confirmType = ref<"delete" | "clear">();
const confirmMessage = ref("");
const list = ref(<NoticeFeedback[]>[
  {
    personnelId: "1",
    name: "xiaoming",
    position: "处长",
    phone: "158",
    vehicleInfoId: "2",
    noticeMethod: "短信",
    noticeTime: "2023-08-08 10:00:00",
    feedbackContent: "好的",
  },
  {
    personnelId: "1",
    name: "xiaoming",
    position: "处长",
    phone: "158",
    vehicleInfoId: "2",
    noticeMethod: "短信",
    noticeTime: "2023-08-08 10:00:00",
    feedbackContent: "好的",
  },
]);

onMounted(async () => {
  await getList();
});

const getList = async () => {
  try {
    // list.value = await getListAll({ ifPage: false });
  } catch (e) {}
};

const deleteItem = async (item: NoticeFeedback) => {
  showConfirmDialog("delete", "您确定要删除当前通知反馈记录吗?", item.id);
};

const showConfirmDialog = (
  type: "delete" | "clear",
  message: string,
  id?: string
) => {
  confirmType.value = type;
  confirmMessage.value = message;
  if (id) currentId.value = id;
  showConfirm.value = true;
};

const handleConfirm = async () => {
  if (!showConfirm.value) return;
};
const infoItem = async (item: NoticeFeedback) => {
  uni.navigateTo({ url: '/subpages/haircut/pages/appointment/components/haircut-noticeWay/index' })
};
const cancelAll = () => {
  showConfirmDialog("delete", "您确定要删除当前通知反馈记录吗?", '1');
};
</script>

<style scoped lang="scss">
.notice-list-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 15px;
  // position: relative;
  padding-bottom: 90px; /* 为 cancelAll 预留空间，防止遮挡 */
}

.item {
  width: 100%;
  height: 326px;
  border-radius: 10px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(29, 24, 24, 0.3);
  display: flex;
  flex-direction: column;
  padding-bottom: 25px;
  margin-bottom: 10px;
}

.title {
  height: 40px;
  width: 100%;
  padding: 0 15px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(153, 153, 153, 0.2);
  font-size: 14px;
  justify-content: space-between;
}

.label {
  color: #333; /* 替换 var(--text-primary) */
}

.content {
  margin-top: 10px;
  padding: 0 15px;
  flex: 1;
}

.list {
  display: flex;
  flex-direction: column;
}

.list-item {
  display: flex;
  height: 27px;
  align-items: center;
}

.list-item .label,
.list-item .val {
  font-size: 12px;
}

.list-item .label,
.list-item .val:not(.danger-text) {
  color: #666666;
}

.list-item .label {
  flex: 0 70px;
}

.list-item .val {
  flex: 1;
}

.danger-text {
  color: red; /* 如果需要特殊样式 */
}

.actions {
  padding: 0 15px;
  width: 100%;
}
.actions-btn {
  border-radius: 3px;
  background: #ffffff;
  border: 1.2px solid #4f7af6;
  font-size: 14px;
  color: #4f7af6;
}

.noData {
  width: 100%;
  height: calc(100vh - 74px);
  display: flex;
  justify-content: center;
  align-items: center;
}
.cancelAll {
  position: fixed; /* 改为 fixed 定位 */
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 10px 10px 0 0;
  background: rgba(255, 255, 255, 0.7); /* 调整透明度 */
  box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);
  width: 100%;
  height: 70px;
  padding: 15px;
  backdrop-filter: blur(5px); /* 可选：毛玻璃效果 */
  z-index: 999; /* 确保在最上层 */
  .cancelAll-btn {
    border-radius: 3px;
    background: #0066df;
    color: #ffffff;
    width: 100%;
  }
}
</style>
