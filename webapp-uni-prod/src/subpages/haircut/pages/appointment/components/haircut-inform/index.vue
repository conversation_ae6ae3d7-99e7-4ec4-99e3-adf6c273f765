<template>
  <SlTabbarPage :tab-bar-root="'subpages/haircut'">
    <div class="notice-list-container">
      <div class="item" v-for="item of currentList" :key="item.id">
        <div class="title">
          <view style="display: inline-flex; align-items: center">
            <!-- <SlSVgIcon name="16-16-17" size="16" /> -->
            <SlSubSvgIcon subpage="haircut" name="16-16-17" size="16" />
            <text class="label" style="margin-left: 8px">通知反馈</text>
          </view>
          <view
            style="display: inline-flex; align-items: center"
            @click="deleteItem(item)"
          >
            <!-- <SlSVgIcon name="16-16-16" size="16" /> -->
            <SlSubSvgIcon subpage="haircut" name="16-16-16" size="16" />
          </view>
        </div>
        <div class="content">
          <view class="list">
            <view class="list-item">
              <text class="label">姓名</text>
              <text class="val">{{ item.customerName }}</text>
            </view>
            <view class="list-item">
              <text class="label">手机号码</text>
              <text class="val">{{ item.baberPhone }}</text>
            </view>
            <view class="list-item">
              <text class="label">通知时间</text>
              <text class="val">{{ item.appointmentTime }}</text>
            </view>
            <view class="list-item">
              <text class="label">通知方式</text>
              <text class="val">{{
                item.hairnotification[0].noticeMethod
              }}</text>
            </view>
            <view class="list-item">
              <text class="label">反馈时间</text>
              <text class="val">{{ item.hairnotification[0].noticeTime }}</text>
            </view>
            <view class="list-item">
              <text class="label">反馈</text>
              <text class="val danger-text">{{
                item.hairnotification[0].feedbackContent
              }}</text>
            </view>
          </view>
        </div>
        <div class="actions">
          <button class="actions-btn" @click="infoItem(item)">再次通知</button>
        </div>
      </div>
      <div v-if="!currentList.length" class="noData">
        <BaseEmpty>
          <text style="color: #999; font-size: 14px"> 暂无反馈数据 </text>
        </BaseEmpty>
      </div>
      <!-- 全部清除 -->
      <div class="cancelAll" v-if="currentList.length">
        <button class="cancelAll-btn" @click="cancelAll">全部清除</button>
      </div>
    </div>

    <!-- 统一的确认对话框 -->
    <ConfirmDialog
      v-if="showConfirm"
      @close="showConfirm = false"
      @confirm="handleConfirm"
      >{{ confirmMessage }}</ConfirmDialog
    >
  </SlTabbarPage>
</template>

<script lang="ts" setup>
import BaseEmpty from "@/components/BaseEmpty.vue";
import type { ReservationData, HairNotification } from "./inform";
import { onMounted, ref } from "vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import hairBaseConfig1 from "@/service/haircut/haircut.service";
import { useAppointmentStore } from "../haircutStore";
import { onShow } from "@dcloudio/uni-app";
const store = useAppointmentStore();
const showConfirm = ref(false);
const currentId = ref("");
const confirmType = ref<"delete" | "clear">();
const confirmMessage = ref("");
const currentList = ref<ReservationData[]>([]);
onMounted(() => {});
onShow(() => {
  getList();
});
const getList = async () => {
  try {
    const rawData = await hairBaseConfig1.getNoticeFeedback();
    const data = rawData.map((item: any) => ({
      ...item,
      appointmentTime: formatTimeRange(item.hairStartTime, item.hairEndTime),
      hairnotification: item.hairnotificationList?.slice(0, 1) || [],
    }));
    currentList.value = data;
    console.log(data, "处理后的 data");
  } catch (e) {
    console.error("获取数据失败", e);
  }
};

const deleteItem = async (item: any) => {
  showConfirmDialog(
    "delete",
    "您确定要删除当前通知反馈记录吗?",
    item.hairnotification[0].id
  );
};

const showConfirmDialog = (
  type: "delete" | "clear",
  message: string,
  id?: string
) => {
  confirmType.value = type;
  confirmMessage.value = message;
  if (id) currentId.value = id;
  showConfirm.value = true;
};

const handleConfirm = async () => {
  if (!showConfirm.value) return;
  try {
    if (currentId.value) {
      await hairBaseConfig1.delNoticeFeedback(currentId.value);
      uni.showToast({ title: "删除成功", icon: "none" });
    } else {
      await hairBaseConfig1.delAllNoticeFeedback();
      uni.showToast({ title: "全部删除成功", icon: "none" });
    }
    getList();
  } catch (error) {
    uni.showToast({ title: "删除失败，请重试~", icon: "none" });
    console.error("删除失败：", error);
  } finally {
    showConfirm.value = false;
  }
};

const infoItem = async (item: any) => {
  console.log(item, "再次通知");
  store.setBusinessData("haircut-noticeWay", item.hairnotification);
  uni.navigateTo({
    url: "/subpages/haircut/pages/appointment/components/haircut-noticeWay/index",
  });
};
const cancelAll = () => {
  showConfirmDialog("delete", "您确定要删除当前所有通知反馈记录吗?");
};

// 处理时间格式的函数
const formatTimeRange = (startTime: string, endTime: string): string => {
  // 提取日期部分（假设格式一致）
  const datePart = startTime.split(" ")[0];

  // 提取开始时间的小时和分钟
  const startHourMin = startTime.split(" ")[1];

  // 提取结束时间的分钟（只取后两位）
  const endMin = endTime.split(" ")[1].substring(3);

  return `${datePart} ${startHourMin}~${endMin}`;
};
</script>

<style scoped lang="scss">
.notice-list-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 15px;
  // position: relative;
  padding-bottom: 90px; /* 为 cancelAll 预留空间，防止遮挡 */
  box-sizing: border-box
}

.item {
  width: 100%;
  height: 326px;
  border-radius: 10px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(29, 24, 24, 0.3);
  display: flex;
  flex-direction: column;
  padding-bottom: 25px;
  margin-bottom: 10px;
  box-sizing: border-box
}

.title {
  height: 40px;
  width: 100%;
  padding: 0 15px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(153, 153, 153, 0.2);
  font-size: 14px;
  justify-content: space-between;
  box-sizing: border-box
}

.label {
  color: #333; /* 替换 var(--text-primary) */
}

.content {
  margin-top: 10px;
  padding: 0 15px;
  flex: 1;
}

.list {
  display: flex;
  flex-direction: column;
}

.list-item {
  display: flex;
  height: 27px;
  align-items: center;
}

.list-item .label,
.list-item .val {
  font-size: 12px;
}

.list-item .label,
.list-item .val:not(.danger-text) {
  color: #666666;
}

.list-item .label {
  flex: 0 70px;
}

.list-item .val {
  flex: 1;
}

.danger-text {
  color: red; /* 如果需要特殊样式 */
}

.actions {
  padding: 0 15px;
  width: 100%;
  box-sizing: border-box
}
.actions-btn {
  border-radius: 3px;
  background: #ffffff;
  border: 1.2px solid #4f7af6;
  font-size: 14px;
  color: #4f7af6;
}

.noData {
  width: 100%;
  height: calc(100vh - 74px);
  display: flex;
  justify-content: center;
  align-items: center;
}
.cancelAll {
  position: fixed; /* 改为 fixed 定位 */
  bottom: 78px;
  left: 0;
  right: 0;
  border-radius: 10px 10px 0 0;
  background: rgba(255, 255, 255, 0.7); /* 调整透明度 */
  box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);
  width: 100%;
  height: 70px;
  padding: 15px;
  backdrop-filter: blur(5px); /* 可选：毛玻璃效果 */
  z-index: 999; /* 确保在最上层 */
  box-sizing: border-box;
  .cancelAll-btn {
    border-radius: 3px;
    background: #0066df;
    color: #ffffff;
    width: 100%;
  }
}
</style>
