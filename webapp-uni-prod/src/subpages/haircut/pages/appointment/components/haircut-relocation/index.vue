<template>
  <div class="main">
    <!-- 基本营业信息配置 -->
    <div class="deploy-top">
      <!-- 营业日期 -->
      <div class="deploy-item">
        <span class="label">营业日期</span>
        <div class="deploy-select">
          <SLSelect
          class="select-item"
            v-model="businessDate"
            :options="dateOptions"
            placeholder="请选择营业日期"
            icon-name="14-14-4"
            icon-size="14"
            @change="handleDateChange"
          />
        </div>
      </div>

      <!-- 开始时间 -->
      <div class="deploy-item">
        <span class="label">开始时间</span>
        <div class="deploy-select">
          <SLSelect
          class="select-item"
            v-model="startTime"
            :options="timeOptions"
            placeholder="请选择开始时间"
            icon-name="12-12-7"
            icon-size="14"
            :rotate-on-open="true"
            @change="handleTimeChange('start')"
          />
        </div>
      </div>

      <!-- 结束时间 -->
      <div class="deploy-item">
        <span class="label">结束时间</span>
        <div class="deploy-select">
          <SLSelect
          class="select-item"
            v-model="endTime"
            :options="timeOptions"
            placeholder="请选择结束时间"
            icon-name="12-12-7"
            icon-size="14"
            :rotate-on-open="true"
            @change="handleTimeChange('end')"
          />
          <div v-if="timeError" class="error-tip">{{ timeError }}</div>
        </div>
      </div>

      <span class="remind">{{`默认${remindData.intervalMinutes}分钟一位，午休时间${remindData.timeQuantum}`}}</span>
    </div>

    <!-- 特殊不营业时间段配置 -->
    <div class="deploy-special">
      <div class="special-label">
        <span class="special-title">特殊不营业时间段</span>
        <SlSubSvgIcon subpage="haircut" :name="`36-36-${showDeploy ? '2' : '3'}`" size="36" @click="closeSpecial"/>
      </div>

      <template v-if="showDeploy">
        <div
          v-for="(item, index) in specialItems"
          :key="index"
          :class="{ 'has-multiple': specialItems.length > 1 }"
          class="special-item"
        >
          <!-- 特殊开始时间 -->
          <div class="deploy-item">
            <span class="label">开始时间</span>
            <div class="deploy-select">
              <SLSelect
              class="select-item"
                v-model="item.startTime"
                :options="specialTimeOptions"
                placeholder="请选择开始时间"
                icon-name="12-12-7"
                icon-size="14"
                :rotate-on-open="true"
                @change="handleSpecialTimeChange('start', index)"
              />
            </div>
          </div>

          <!-- 特殊结束时间 -->
          <div class="deploy-item">
            <span class="label">结束时间</span>
            <div class="deploy-select">
              <SLSelect
              class="select-item"
                v-model="item.endTime"
                :options="specialTimeOptions"
                placeholder="请选择结束时间"
                icon-name="12-12-7"
                icon-size="14"
                :rotate-on-open="true"
                @change="handleSpecialTimeChange('end', index)"
              />
            </div>
          </div>
        </div>

        <!-- 新增特殊时间按钮 -->
        <div class="footer-btn" v-if="specialItems.length < 3">
          <button @click="addItem">新增</button>
        </div>
      </template>
    </div>

    <!-- 底部操作按钮 -->
    <div class="cancelAll">
      <BaseButton btn-type="cancel" @click="cancel" size="small" />
      <BaseButton btn-type="save" @click="save" size="small" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { onShow } from "@dcloudio/uni-app";
import SLSelect from "@/components/SLSelect.vue";
import SlSvgIcon from "@/components/SlSVgIcon.vue";
import { useAppointmentStore } from "../haircutStore";
import getBusinessHours from "@/service/haircut/haircut.service";
import {Datas} from '../../Haircut'
const showDeploy = ref(false);
const businessDate = ref("");
const startTime = ref("");
const endTime = ref("");
const timeError = ref("");
const specialItems = ref([{ startTime: "", endTime: "" }]);
const currentYear = new Date().getFullYear(); // 动态获取当前年份
const store = useAppointmentStore();
const currentData = ref(null);
const dateOptions = ref<{ label: string; value: string }[]>([]);
const remindData = ref<any>({
  intervalMinutes: 30,
  timeQuantum: '11:30-13:30'
})
// 日期选项（示例数据）
const editId =ref('')
// 时间段选项（每半小时一个）
const timeOptions = ref(generateTimeOptions());
const specialTimeOptions = ref(generateTimeOptions());
onMounted(() => {
  // fetchDateOptionsFromApi();
});
function generateTimeOptions() {
  return Array.from({ length: 24 * 2 }, (_, i) => {
    const hours = String(Math.floor(i / 2)).padStart(2, "0");
    const minutes = i % 2 === 0 ? "00" : "30";
    const time = `${hours}:${minutes}`;
    return { label: time, value: time };
  });
}
const weekNumber = ref(1);
// 页面打开后接收回填数据
onShow(() => {
  const data = store.getBusinessData("haircut-relocation-edit");
  if (!data) return;
  // 设置周数并初始化数据
  const { weekNumber: week, type, rawData } = data;
  weekNumber.value = week;
  initData(week);
  const info = {
      intervalMinutes: rawData.intervalMinutes,
      timeQuantum: `${rawData.lunchStartTime}-${rawData.lunchEndTime}`
    };
    remindData.value = info
  // 如果不是新增操作，进入编辑流程
  if (type !== "add" && rawData) {
    currentData.value = data;
    editId.value = rawData.id;
    edit(data);
  }
});


// 编辑数据回填逻辑
function edit(data: any) {
  businessDate.value = data.date + " " + data.week;
  if (data.timeRange?.includes("~")) {
    const [start, end] = data.timeRange.split("~");
    startTime.value = start;
    endTime.value = end;
  }

  if (Array.isArray(data.overTimeRange)) {
    specialItems.value = data.overTimeRange.map((item: string) => {
      const [start, end] = item.split("~");
      return { startTime: start, endTime: end };
    });
    showDeploy.value = true;
  } else {
    specialItems.value = [{ startTime: "", endTime: "" }];
    showDeploy.value = false;
  }
}

// 校验营业时间
const validateTimes = () => {
  if (startTime.value && endTime.value && startTime.value >= endTime.value) {
    timeError.value = "结束时间必须晚于开始时间";
    return false;
  }
  timeError.value = "";
  return true;
};

watch([startTime, endTime], validateTimes);

// 事件处理函数
const handleDateChange = (item: any) => console.log("选择日期:", item);
const handleTimeChange = (type: "start" | "end") => {
  validateTimes();
};

const handleSpecialTimeChange = (type: "start" | "end", index: number) => {
  const item = specialItems.value[index];
  if (item.startTime && item.endTime && item.startTime >= item.endTime) {
    uni.showToast({ title: "结束时间必须晚于开始时间", icon: "none" });
  }
};

// 添加特殊时间段
const addItem = () => {
  if (specialItems.value.length < 3) {
    specialItems.value.push({ startTime: "", endTime: "" });
  } else {
    uni.showToast({ title: "最多只能添加3个特殊时间段", icon: "none" });
  }
};

// 获取营业时间汇总
const businessHours = computed(() => ({
  date: businessDate.value,
  start: startTime.value,
  end: endTime.value,
}));

// 表单校验
const validateForm = () => {
  if (!businessDate.value) return "请选择营业日期";
  if (!startTime.value) return "请选择开始时间";
  if (!endTime.value) return "请选择结束时间";
  if (!validateTimes()) return timeError.value;
  return true;
};

// 获取全部数据
const getData = () => ({
  businessHours: businessHours.value,
  specialHours: specialItems.value.filter(
    (item) => item.startTime && item.endTime
  ),
});

// 提交数据
const save = async() => {
  const valid = validateForm();
  if (valid !== true) {
    return uni.showToast({ title: valid, icon: "none" });
  }

  for (const item of specialItems.value) {
    if (!item.startTime || !item.endTime) continue;
    if (item.startTime >= item.endTime) {
      return uni.showToast({
        title: "特殊时间段的结束时间必须晚于开始时间",
        icon: "none",
      });
    }
    if (item.startTime < startTime.value || item.endTime > endTime.value) {
      return uni.showToast({
        title: "特殊时间段必须在营业时间范围内",
        icon: "none",
      });
    }
  }
  const dataTime = `${currentYear}/${businessDate.value.split(" ")[0]}`;
  const formData:Datas = {
    openTime: startTime.value,
    closeTime: endTime.value,
    businessDate: dataTime,
    specialTimeList: specialItems.value
      .filter((item) => item.startTime && item.endTime)
      .map((item) => ({
        specialStartTime: item.startTime,
        specialEndTime: item.endTime,
      })),
  };
  if(!showDeploy.value){
    delete formData.specialTimeList
  }
  if(editId.value){
    formData.id = editId.value
  }
  try {
     await getBusinessHours.saveHaircutconfig(formData);
    uni.showToast({ title: "保存成功", icon: "none" });
  } catch (error) {
    uni.showToast({ title: "保存失败", icon: "none" });
  }finally{
    uni.navigateBack();
  }
  store.setBusinessData("haircut-relocation", formData);
};

// 关闭/展开特殊配置项
const closeSpecial = () => {
  showDeploy.value = !showDeploy.value;
};

// 返回上一页
const cancel = () => {
  uni.navigateBack();
};
// 模拟后端返回日期数据
const fetchDateOptionsFromApi = async (res: Array<string>) => {
  dateOptions.value = res.map((item) => {
    const [datePart, weekPart] = item.split(" ");
    const [year, month, day] = datePart.split("-");
    const formattedDate = `${month}/${day}`;
    return {
      label: `${formattedDate} ${weekPart}`,
      value: `${formattedDate} ${weekPart}`,
    };
  });
};
// 获取星期数据
const initData = async (type: number) => {
  try {
    const res = await getBusinessHours.getBusinessHours(type);
    fetchDateOptionsFromApi(res)
  } catch (error) {
    
  }
};
// 暴露方法供父组件调用
defineExpose({ getData, validateForm });
interface FormData {
  specialTimeList?: {
    specialStartTime: string;
    specialEndTime: string;
  }[];
}
</script>

<style scoped lang="scss">
.main {
  position: relative;
  height: 100vh;
  padding-bottom: 70px;
  overflow-y: auto;

  .deploy-top {
    padding: 15px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .remind {
    font-size: 12px;
    color: #999;
  }

  .deploy-special {
    margin-top: 10px;
    padding: 10px 15px;
    box-shadow: 0 0 4px rgba(146, 146, 146, 0.2);

    .special-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      color: #333;

      .special-title {
        line-height: 36px;
      }
    }

    .special-item {
      // padding-bottom: 15px;
    }

    .has-multiple {
      border-bottom: 1px dashed #9993;
      margin-top: 15px;
    }
  }

  .deploy-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    position: relative;
    height: 32px;
    .label {
      font-size: 14px;
      color: #333;
      min-width: 70px;
    }

    .deploy-select {
      width: 220px;
      height: 32px;
      .select-item{
        height: 32px;
      }
    }

    .error-tip {
      position: absolute;
      right: 0;
      bottom: -18px;
      font-size: 12px;
      color: #f56c6c;
    }
  }

  .footer-btn {
    text-align: center;
    margin-top: 20px;

    button {
      width: 60px;
      height: 28px;
      line-height: 28px;
      background: #4f7af6;
      color: #fff;
      border-radius: 3px;
      font-size: 14px;
    }
  }
}

.cancelAll {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: space-between;
  height: 70px;
  box-shadow: 0 0 5px rgba(146, 146, 146, 0.2);
  z-index: 999;
}
</style>
