<template>
  <div class="main">
    <div class="deploy-top">
      <div class="deploy-item">
        <span class="label">营业日期</span>
        <div class="deploy-select">
          <SLSelect
            v-model="businessDate"
            :options="dateOptions"
            placeholder="请选择营业日期"
            icon-name="components-14-4"
            icon-size="14"
            @change="handleDateChange"
          />
        </div>
      </div>
      <div class="deploy-item">
        <span class="label">开始时间</span>
        <div class="deploy-select">
          <SLSelect
            v-model="startTime"
            :options="timeOptions"
            placeholder="请选择开始时间"
            icon-name="components-12-7"
            icon-size="14"
            :rotate-on-open="true"
            @change="handleTimeChange('start')"
          />
          <div v-if="timeError" class="error-tip">{{ timeError }}</div>
        </div>
      </div>
      <div class="deploy-item">
        <span class="label">结束时间</span>
        <div class="deploy-select">
          <SLSelect
            v-model="endTime"
            :options="timeOptions"
            placeholder="请选择结束时间"
            icon-name="components-12-7"
            icon-size="14"
            :rotate-on-open="true"
            @change="handleTimeChange('end')"
          />
        </div>
      </div>
      <span class="remind">默认30分钟一位，午休时间11:30-13:30</span>
    </div>
    <!--  -->
    <!-- 动态item -->
    <div class="deploy-special">
      <div class="special-label">
        <span class="special-title">特殊不营业时间段</span>
        <SlSVgIcon
          :name="`36-36-${showDeploy ? '2' : '3'}`"
          size="36"
          @click="closeSpecial"
        />
      </div>
      <!-- 动态item  -->
      <template v-if="showDeploy">
        <div
          v-for="(item, index) in specialItems"
          :key="index"
          :class="{ 'has-multiple': specialItems.length > 1 }"
          class="special-item"
        >
          <div class="deploy-item">
            <span class="label">开始时间</span>
            <div class="deploy-select">
              <SLSelect
                v-model="item.startTime"
                :options="specialTimeOptions"
                placeholder="请选择开始时间"
                icon-name="components-12-7"
                icon-size="14"
                :rotate-on-open="true"
                @change="handleSpecialTimeChange('start', index)"
              />
            </div>
          </div>
          <div class="deploy-item">
            <span class="label">结束时间</span>
            <div class="deploy-select">
              <SLSelect
                v-model="item.endTime"
                :options="specialTimeOptions"
                placeholder="请选择结束时间"
                icon-name="components-12-7"
                icon-size="14"
                :rotate-on-open="true"
                @change="handleSpecialTimeChange('end', index)"
              />
            </div>
          </div>
        </div>
        <!-- 新增按钮 - 只在数量小于3时显示 -->
        <div class="footer-btn">
          <button @click="addItem">新增</button>
        </div>
      </template>
    </div>
    <div
      class="bottom-btn"
      :style="{
        position: specialItems.length == 3 ? 'static' : 'absolute',
      }"
    >
      <BaseButton btn-type="cancel" @click="cancel" />
      <BaseButton btn-type="save" @click="save" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import SLSelect from "@/components/SLSelect.vue";
import SlSvgIcon from "@/components/SlSVgIcon.vue";
const showDeploy = ref(false);
// 日期选项（示例数据，实际应从后端获取）
const dateOptions = ref([
  { label: "周一至周五", value: "weekday" },
  { label: "周六周日", value: "weekend" },
  { label: "每天", value: "daily" },
]);

// 时间选项（每半小时一个间隔）
const timeOptions = ref(
  Array.from({ length: 24 * 2 }, (_, i) => {
    const hours = Math.floor(i / 2);
    const minutes = i % 2 === 0 ? "00" : "30";
    const timeValue = `${hours.toString().padStart(2, "0")}:${minutes}`;
    return {
      label: timeValue,
      value: timeValue,
    };
  })
);
const specialTimeOptions = ref(
  Array.from({ length: 24 * 2 }, (_, i) => {
    const hours = Math.floor(i / 2);
    const minutes = i % 2 === 0 ? "00" : "30";
    const timeValue = `${hours.toString().padStart(2, "0")}:${minutes}`;
    return {
      label: timeValue,
      value: timeValue,
    };
  })
);

// 表单数据
const businessDate = ref("");
const startTime = ref("");
const specialStartTime = ref("");
const specialEndTime = ref("");
const endTime = ref("");
const timeError = ref("");
// 特殊时间段数据
const specialItems = ref([{ startTime: "", endTime: "" }]);

// 验证时间逻辑
const validateTimes = () => {
  if (startTime.value && endTime.value) {
    if (startTime.value >= endTime.value) {
      timeError.value = "结束时间必须晚于开始时间";
      return false;
    }
  }
  timeError.value = "";
  return true;
};

// 处理日期变更
const handleDateChange = (item: any) => {
  console.log("选择日期:", item);
  // 这里可以添加日期变更后的逻辑
};

// 处理时间变更
const handleTimeChange = (type: any) => {
  validateTimes();
  console.log(
    `选择${type === "start" ? "开始" : "结束"}时间:`,
    type === "start" ? startTime.value : endTime.value
  );
};

// 处理特殊时间变更 - 修改为带索引的版本
const handleSpecialTimeChange = (type: "start" | "end", index: number) => {
  const item = specialItems.value[index];
  if (item.startTime && item.endTime && item.startTime >= item.endTime) {
    uni.showToast({
      title: "结束时间必须晚于开始时间",
      icon: "none",
    });
  }
  console.log(
    `选择${type === "start" ? "开始" : "结束"}时间:`,
    type === "start" ? item.startTime : item.endTime
  );
};

// 监听时间变化
watch([startTime, endTime], () => {
  validateTimes();
});

// 获取完整营业时间（计算属性）
const businessHours = computed(() => {
  return {
    date:
      dateOptions.value.find((item) => item.value === businessDate.value)
        ?.label || "",
    start: startTime.value,
    end: endTime.value,
  };
});

// 表单验证
const validateForm = () => {
  if (!businessDate.value) return "请选择营业日期";
  if (!startTime.value) return "请选择开始时间";
  if (!endTime.value) return "请选择结束时间";
  if (!validateTimes()) return timeError.value;
  return true;
};

// 提交方法示例
const submitForm = () => {
  const valid = validateForm();
  if (valid === true) {
    console.log("提交数据:", businessHours.value);
    // 这里调用API提交数据
  } else {
    uni.showToast({
      title: valid,
      icon: "none",
    });
  }
};
const addItem = () => {
  if (specialItems.value.length < 3) {
    specialItems.value.push({ startTime: "", endTime: "" });
  } else {
    uni.showToast({
      title: "最多只能添加3个特殊时间段",
      icon: "none",
    });
  }
};
// 修改getData方法以包含特殊时间段
const getData = () => ({
  businessHours: businessHours.value,
  specialHours: specialItems.value.filter(
    (item) => item.startTime && item.endTime
  ),
});
const cancel = () => {
  uni.navigateBack();
};
const closeSpecial = () => {
  showDeploy.value = !showDeploy.value;
};
const save = () => {};
// 暴露方法给父组件
defineExpose({
  submitForm,
  validateForm,
  getData,
});
</script>

<style scoped lang="scss">
.main {
  position: relative;
  height: 100vh;
  .deploy-top {
    width: 100%;
    padding: 15px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  .remind {
    font-size: 12px;
    color: #999999;
  }
  .deploy-special {
    margin-top: 10px;
    min-height: 40px;
    padding: 10px 15px;
    box-shadow: 0px 0px 4px 0px #92929233;
    .special-label {
      font-size: 14px;
      color: #333333;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .special-title {
        line-height: 36px;
      }
    }
    .special-item {
      padding-bottom: 15px;
    }
    .has-multiple {
      border-bottom: 1px dashed #99999933;
      margin-top: 15px;
    }
  }
  .deploy-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    position: relative;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 14px;
      color: #333;
      min-width: 70px;
    }

    .deploy-select {
      width: 220px;
      position: relative;
    }

    .error-tip {
      position: absolute;
      right: 0;
      bottom: -18px;
      color: #f56c6c;
      font-size: 12px;
    }
  }
  .footer-btn {
    width: 100%;
    text-align: center;
    margin-top: 20px;
    button {
      width: 60px;
      height: 28px;
      line-height: 28px;
      border-radius: 3px;
      background: #4f7af6;
      color: #ffffff;
      font-size: 14px;
    }
  }
}
.bottom-btn {
  // position: absolute;
  bottom: 0;
  padding: 0 15px;
  margin-bottom: 9px;
  display: flex;
  width: 100%;
  justify-content: space-between;
}
</style>
