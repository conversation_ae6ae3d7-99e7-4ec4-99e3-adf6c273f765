<template>
  <div class="main">
    <!-- 基本营业信息配置 -->
    <div class="deploy-top">
      <!-- 营业日期 -->
      <div class="deploy-item">
        <span class="label">营业日期</span>
        <div class="deploy-select">
          <SLSelect
            class="select-item"
            v-model="businessDate"
            :options="dateOptions"
            placeholder="请选择营业日期"
            icon-name="14-14-4"
            icon-size="14"
            @change="handleDateChange"
          />
        </div>
      </div>

      <!-- 开始时间 -->
      <div class="deploy-item">
        <span class="label">开始时间</span>
        <div class="deploy-select">
          <SLSelect
            class="select-item"
            v-model="startTime"
            :options="timeOptions"
            placeholder="请选择开始时间"
            icon-name="12-12-7"
            icon-size="14"
            :rotate-on-open="true"
            @change="handleTimeChange('start')"
          />
        </div>
      </div>

      <!-- 结束时间 -->
      <div class="deploy-item">
        <span class="label">结束时间</span>
        <div class="deploy-select">
          <SLSelect
            class="select-item"
            v-model="endTime"
            :options="timeOptions"
            placeholder="请选择结束时间"
            icon-name="12-12-7"
            icon-size="14"
            :rotate-on-open="true"
            @change="handleTimeChange('end')"
          />
          <div v-if="timeError" class="error-tip">{{ timeError }}</div>
        </div>
      </div>

      <span class="remind">{{
        `默认${remindData.intervalMinutes}分钟一位，午休时间${remindData.timeQuantum}`
      }}</span>
    </div>

    <!-- 特殊不营业时间段配置 -->
    <div  :class="['deploy-special', { 'deploy-special1': showDeploy }]">
      <div class="special-label">
        <span class="special-title">特殊不营业时间段</span>
        <SlSubSvgIcon
          style="display: inline-flex;"
          subpage="haircut"
          :name="`36-36-${showDeploy ? '2' : '3'}`"
          size="36"
          @click="closeSpecial"
        />
      </div>

      <template v-if="showDeploy">
        <div
          v-for="(item, index) in specialItems"
          :key="index"
          :class="[
            'special-item',
            {
              'has-multiple': specialItems.length > 1 && index !== 0,
              'has-border': specialItems.length > 1 && index === 0,
            },
          ]"
        >
          <!-- 删除按钮 -->
          <div class="del-icon" @click="delBtn(item, index)">
            <SlSubSvgIcon subpage="haircut" name="20-20-21" size="20" />
          </div>
          <!-- 特殊开始时间 -->
          <div class="deploy-item">
            <span class="label">开始时间</span>
            <div class="deploy-select">
              <SLSelect
                class="select-item"
                v-model="item.startTime"
                :options="specialTimeOptions"
                placeholder="请选择开始时间"
                icon-name="12-12-7"
                icon-size="14"
                :rotate-on-open="true"
                @change="handleSpecialTimeChange('start', index)"
              />
            </div>
          </div>

          <!-- 特殊结束时间 -->
          <div class="deploy-item">
            <span class="label">结束时间</span>
            <div class="deploy-select">
              <SLSelect
                class="select-item"
                v-model="item.endTime"
                :options="specialTimeOptions"
                placeholder="请选择结束时间"
                icon-name="12-12-7"
                icon-size="14"
                :rotate-on-open="true"
                @change="handleSpecialTimeChange('end', index)"
              />
            </div>
          </div>
        </div>

        <!-- 新增特殊时间按钮 -->
        <div class="footer-btn" v-if="specialItems.length < 3">
          <button @click="addItem">新增</button>
        </div>
      </template>
    </div>

    <!-- 底部操作按钮 -->
    <div class="cancelAll">
      <BaseButton btn-type="cancel" @click="cancel" size="small" />
      <BaseButton btn-type="save" @click="save" size="small" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { onShow } from "@dcloudio/uni-app";
import SLSelect from "@/components/SLSelect.vue";
import SlSvgIcon from "@/components/SlSVgIcon.vue";
import { useAppointmentStore } from "../haircutStore";
import getBusinessHours from "@/subpages/haircut/service/haircut.service";
import { Datas, IHairBaseConfig } from "@/subpages/haircut/models";
const showDeploy = ref(false);
const businessDate = ref("");
const startTime = ref("");
const endTime = ref("");
const timeError = ref("");
const specialItems = ref([{ startTime: "", endTime: "" }]);
const currentYear = new Date().getFullYear(); // 动态获取当前年份
const store = useAppointmentStore();
const currentData = ref(null);
const dateOptions = ref<{ label: string; value: string }[]>([]);
const remindData = ref<any>({
  intervalMinutes: 30,
  timeQuantum: "11:30-13:30",
});
// 日期选项（示例数据）
const editId = ref("");
// 时间段选项（每半小时一个）
const timeOptions = ref(generateTimeOptions());
const specialTimeOptions = ref(generateTimeOptions());
onMounted(() => {
  // fetchDateOptionsFromApi();
});
function generateTimeOptions(minTime = "00:00", maxTime = "23:30") {
  const parseTime = (time: string) => {
    const [h, m] = time.split(":").map(Number);
    return h * 60 + m;
  };

  const startMinutes = parseTime(minTime);
  const endMinutes = parseTime(maxTime);

  return Array.from({ length: 24 * 2 }, (_, i) => {
    const minutes = i * 30;
    const hours = String(Math.floor(minutes / 60)).padStart(2, "0");
    const mins = minutes % 60 === 0 ? "00" : "30";
    const time = `${hours}:${mins}`;
    return {
      label: time,
      value: time,
    };
  }).filter((item) => {
    const total = parseTime(item.value);
    return total >= startMinutes && total <= endMinutes;
  });
}
const weekNumber = ref(1);
// 页面打开后接收回填数据
onShow(async () => {
  confinementTime();
  const data = store.getBusinessData("haircut-relocation-edit");
  if (!data) return;
  const { weekNumber: week, type, rawData } = data;
  weekNumber.value = week;
  // 设置提醒数据（新增和编辑共用）
  if (rawData) {
    remindData.value = {
      intervalMinutes: rawData.intervalMinutes,
      timeQuantum: `${rawData.lunchStartTime}-${rawData.lunchEndTime}`,
    };
  }

  // 初始化数据
  await initData(week, type !== "add" ? rawData?.id : undefined);

  // 编辑流程
  if (type !== "add" && rawData) {
    currentData.value = data;
    editId.value = rawData.id;
    edit(data);
  }
});

// 编辑数据回填逻辑
function edit(data: any) {
  businessDate.value = data.date + " " + data.week;
  if (data.timeRange?.includes("~")) {
    const [start, end] = data.timeRange.split("~");
    startTime.value = start;
    endTime.value = end;
  }

  if (Array.isArray(data.overTimeRange)) {
    specialItems.value = data.overTimeRange.map((item: string) => {
      const [start, end] = item.split("~");
      return { startTime: start, endTime: end };
    });
    showDeploy.value = data.overTimeRange.length > 0 ? true : false;
  } else {
    specialItems.value = [{ startTime: "", endTime: "" }];
    showDeploy.value = false;
  }
}

// 校验营业时间
const validateTimes = () => {
  if (startTime.value && endTime.value && startTime.value >= endTime.value) {
    timeError.value = "结束时间必须晚于开始时间";
    return false;
  }
  timeError.value = "";
  return true;
};

watch([startTime, endTime], validateTimes);

// 事件处理函数
const handleDateChange = (item: any) => console.log("选择日期:", item);
const handleTimeChange = (type: "start" | "end") => {
  validateTimes();
};

const handleSpecialTimeChange = (type: "start" | "end", index: number) => {
  const item = specialItems.value[index];
  if (item.startTime && item.endTime && item.startTime >= item.endTime) {
    uni.showToast({ title: "结束时间必须晚于开始时间", icon: "none" });
  }
};

// 添加特殊时间段
const addItem = () => {
  const items = specialItems.value;
  const lastItem = items[items.length - 1];

  // 如果没有任何项，直接添加第一个
  if (!lastItem) {
    items.push({ startTime: "", endTime: "" });
    return;
  }

  // 有最后一项，检查是否填写完整
  if (!lastItem.startTime || !lastItem.endTime) {
    return uni.showToast({
      title: "请先填写当前特殊时间段",
      icon: "none",
    });
  }

  if (items.length < 3) {
    items.push({ startTime: "", endTime: "" });
  } else {
    uni.showToast({
      title: "最多只能添加3个特殊时间段",
      icon: "none",
    });
  }
};

// 获取营业时间汇总
const businessHours = computed(() => ({
  date: businessDate.value,
  start: startTime.value,
  end: endTime.value,
}));

// 表单校验
const validateForm = () => {
  if (!businessDate.value) return "请选择营业日期";
  if (!startTime.value) return "请选择开始时间";
  if (!endTime.value) return "请选择结束时间";
  if (!validateTimes()) return timeError.value;
  return true;
};

// 获取全部数据
const getData = () => ({
  businessHours: businessHours.value,
  specialHours: specialItems.value.filter(
    (item) => item.startTime && item.endTime
  ),
});

// 提交数据
const save = async () => {
  const valid = validateForm();
  if (valid !== true) {
    return uni.showToast({ title: valid, icon: "none" });
  }
  // 校验特殊时间段
  const specialTimes = specialItems.value.filter(
    (item) => item.startTime && item.endTime
  );

  // 1. 检查每个特殊时间段是否有效
  for (const item of specialItems.value) {
    if (!item.startTime || !item.endTime) continue;
    if (item.startTime >= item.endTime) {
      return uni.showToast({
        title: "特殊时间段的结束时间必须晚于开始时间",
        icon: "none",
      });
    }
    if (item.startTime < startTime.value || item.endTime > endTime.value) {
      return uni.showToast({
        title: "特殊时间段必须在营业时间范围内",
        icon: "none",
      });
    }
  }
  // 2. 检查特殊时间段之间是否有重叠
  if (hasOverlappingSpecialTimes(specialTimes)) {
    return uni.showToast({
      title: "存在重复的特殊时间段",
      icon: "none",
    });
  }

  // 3. 检查特殊时间段是否与午休时间重叠
  //   if (remindData.value.timeQuantum) {
  //   const [lunchStart, lunchEnd] = remindData.value.timeQuantum.split('-');
  //   if (isOverlappingWithLunch(specialTimes, lunchStart, lunchEnd)) {
  //     return uni.showToast({
  //       title: "午休时间不属于特殊时间段",
  //       icon: "none",
  //     });
  //   }
  // }
  const dataTime = `${currentYear}/${businessDate.value.split(" ")[0]}`;
  const formData: Datas = {
    openTime: startTime.value,
    closeTime: endTime.value,
    businessDate: dataTime,
    specialTimeList: specialItems.value
      .filter((item) => item.startTime && item.endTime)
      .map((item) => ({
        specialStartTime: item.startTime,
        specialEndTime: item.endTime,
      })),
  };
  if (!showDeploy.value) {
    delete formData.specialTimeList;
  }
  if (editId.value) {
    formData.id = editId.value;
  }

  try {
    await getBusinessHours.saveHaircutconfig(formData);
    uni.showToast({ title: "保存成功", icon: "none" });
  } catch (error) {
    uni.showToast({ title: "保存失败", icon: "none" });
  } finally {
    setTimeout(() => {
      uni.navigateBack();
      store.setBusinessData("haircut-relocation", formData);
    }, 500);
  }
};
// 检查特殊时间段之间是否有重叠
function hasOverlappingSpecialTimes(
  times: { startTime: string; endTime: string }[]
) {
  // 将时间转换为分钟数方便比较
  const timeToMinutes = (time: string) => {
    const [h, m] = time.split(":").map(Number);
    return h * 60 + m;
  };

  // 检查所有时间段两两之间是否有重叠
  for (let i = 0; i < times.length; i++) {
    for (let j = i + 1; j < times.length; j++) {
      const aStart = timeToMinutes(times[i].startTime);
      const aEnd = timeToMinutes(times[i].endTime);
      const bStart = timeToMinutes(times[j].startTime);
      const bEnd = timeToMinutes(times[j].endTime);

      // 时间段重叠的条件
      if (aStart < bEnd && aEnd > bStart) {
        return true;
      }
    }
  }
  return false;
}
// 检查特殊时间段是否与午休时间重叠
function isOverlappingWithLunch(
  specialTimes: { startTime: string; endTime: string }[],
  lunchStart: string,
  lunchEnd: string
) {
  const timeToMinutes = (time: string) => {
    const [h, m] = time.split(":").map(Number);
    return h * 60 + m;
  };

  const lunchStartMin = timeToMinutes(lunchStart);
  const lunchEndMin = timeToMinutes(lunchEnd);

  for (const item of specialTimes) {
    const itemStart = timeToMinutes(item.startTime);
    const itemEnd = timeToMinutes(item.endTime);

    // 检查是否与午休时间重叠
    if (itemStart < lunchEndMin && itemEnd > lunchStartMin) {
      return true;
    }
  }
  return false;
}
// 关闭/展开特殊配置项
const closeSpecial = () => {
  showDeploy.value = !showDeploy.value;
};
const delBtn = (
  item: { startTime: string; endTime: string },
  index: number
) => {
  specialItems.value.splice(index, 1); // 删除该项
};

// 返回上一页
const cancel = () => {
  uni.navigateBack();
};
// 模拟后端返回日期数据
const fetchDateOptionsFromApi = async (res: Array<string>) => {
  dateOptions.value = res.map((item) => {
    const [datePart, weekPart] = item.split(" ");
    const [year, month, day] = datePart.split("-");
    const formattedDate = `${month}/${day}`;
    return {
      label: `${formattedDate} ${weekPart}`,
      value: `${formattedDate} ${weekPart}`,
    };
  });
};
// 营业时间段起始段
const confinementTime = async () => {
  try {
    const data: IHairBaseConfig = await getBusinessHours.getHairbaseconfig();
    const { dailyOpenTime, dailyCloseTime } = data;
    if (dailyOpenTime && dailyCloseTime) {
      timeOptions.value = generateTimeOptions(dailyOpenTime, dailyCloseTime);
      specialTimeOptions.value = generateTimeOptions(
        dailyOpenTime,
        dailyCloseTime
      );
    }
  } catch (error) {
    console.error("获取营业配置失败", error);
  }
};
// 初始化数据函数：根据是否有 id 自动调用不同接口  获取星期数据
const initData = async (week: number, id?: string) => {
  try {
    const res = id
      ? await getBusinessHours.getBusinessDateEdit(week, id)
      : await getBusinessHours.getBusinessHours(week);

    fetchDateOptionsFromApi(res);
  } catch (error) {
    console.error("初始化数据失败：", error);
  }
};
// 暴露方法供父组件调用
defineExpose({ getData, validateForm });
interface FormData {
  specialTimeList?: {
    specialStartTime: string;
    specialEndTime: string;
  }[];
}
</script>

<style scoped lang="scss">
.main {
  position: relative;
  height: 100vh;
  padding-bottom: 70px;
  overflow-y: auto;

  .deploy-top {
    padding: 15px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .remind {
    font-size: 12px;
    color: #999;
  }

  .deploy-special {
    margin-top: 10px;
    // padding: 10px 15px;
    box-shadow: 0 0 4px rgba(146, 146, 146, 0.2);
    padding: 0px 15px;
    .special-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      color: #333;

      .special-title {
        line-height: 36px;
      }
    }

    .special-item {
      border-radius: 5px;
      border: 1px dashed #d5d5d5;
      padding: 10px 15px;
      position: relative;
      margin-top: 8px;
      &.has-border {
        // border-bottom: 1px dashed #9993;
      }

      &.has-multiple {
        border-bottom: 1px dashed #9993;
        margin-top: 19px;
      }
      .del-icon {
        position: absolute;
        top: -10px;
        right: -10px;
      }
    }
  }
  .deploy-special1{
    padding-bottom: 10px;
  }
  .deploy-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    position: relative;
    height: 32px;
    &:last-child {
      margin-bottom: 0;
    }
    .label {
      font-size: 14px;
      color: #333;
      min-width: 70px;
    }

    .deploy-select {
      width: 220px;
      height: 32px;
      .select-item {
        height: 32px;
      }
    }

    .error-tip {
      position: absolute;
      right: 0;
      bottom: -18px;
      font-size: 12px;
      color: #f56c6c;
    }
  }

  .footer-btn {
    text-align: center;
    margin-top: 20px;
    button {
      width: 60px;
      height: 28px;
      line-height: 28px;
      background: #4f7af6;
      color: #fff;
      border-radius: 3px;
      font-size: 14px;
    }
  }
}

.cancelAll {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: space-between;
  height: 70px;
  box-shadow: 0 0 5px rgba(146, 146, 146, 0.2);
  z-index: 999;
}
</style>
