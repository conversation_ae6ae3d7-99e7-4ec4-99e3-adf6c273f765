<template>
    <div class="notice-way-container">
      <div class="notice-way-title">通知方式</div>
      <div
        v-for="(item, index) in options"
        :key="index"
        class="notice-item"
        @click="toggleSelect(index)"
      >
        <SlSVgIcon :name="selected[index] ? '12-12-14' : '12-12-13'" size="12" />
        <span class="notice-item-span">{{ item.label }}</span>
      </div>
      <div class="bottom-btn">
        <BaseButton btn-type="cancel" @click="cancel" />
        <BaseButton btn-type="save" @click="save" />
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from "vue";
  import SlSVgIcon from "@/components/SlSVgIcon.vue";
  
  // 通知方式选项
  const options = ref([
    { label: "微信通知" },
    { label: "短信通知" },
    { label: "机器人语音电话通知" },
  ]);
  
  // 选中状态数组
  const selected = ref(Array(options.value.length).fill(false));
  
  // 切换选中状态
  const toggleSelect = (index: number) => {
    selected.value[index] = !selected.value[index];
    console.log(selected.value[index], index);
  };
  
  // 返回上一页
  const cancel = () => {
    uni.navigateBack();
  };
  
  // 保存选中的选项
  const save = () => {
    // 1. 获取选中的选项
    const selectedOptions = options.value.filter((_, index) => selected.value[index]);
  
    // 2. 提取选中的通知方式（仅 label）
    const selectedLabels = selectedOptions.map((item) => item.label);
  
    // 3. 打印或提交选中的数据
    console.log("选中的通知方式:", selectedLabels);
  
    // 4. 提交到后端（示例）
    // submitToBackend(selectedLabels);
  
    // 5. 返回上一页（可选）
    uni.navigateBack();
  };
  </script>
  
  <style scoped lang="scss">
  .notice-way-container {
    width: 100%;
    padding: 10px 15px;
    font-size: 14px;
    padding-bottom: 100px; /* 防止按钮遮挡内容 */
  
    .notice-way-title {
      color: #333333;
      font-weight: bold;
      margin-bottom: 10px;
    }
  
    .notice-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      cursor: pointer;
  
      .notice-item-span {
        margin-left: 4px;
      }
    }
  }
  
  .bottom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }
  </style>