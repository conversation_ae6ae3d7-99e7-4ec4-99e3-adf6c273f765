<template>
  <div class="notice-way-container">
    <div class="notice-way-title">通知方式</div>
    <div
      v-for="(item, index) in options"
      :key="index"
      class="notice-item"
      @click="toggleSelect(item.label)"
    >
      <SlSubSvgIcon 
        subpage="haircut" 
        :name="isSelected(item.label) ? '12-12-14' : '12-12-13'" 
        size="12" 
      />
      <span class="notice-item-span">{{ item.label }}</span>
    </div>
    <div class="bottom-btn">
      <BaseButton btn-type="cancel" @click="cancel" />
      <BaseButton btn-type="save" @click="save" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { onShow } from "@dcloudio/uni-app";
import { useAppointmentStore } from "../haircutStore";
import hairBaseConfig from "@/service/haircut/haircut.service";
const store = useAppointmentStore();
const currentItem = ref<any>({});
const selectedMethods = ref<string[]>([]); // 存储选中的方法名称

// 通知方式选项
const options = ref([
  { label: "微信推送" },  // 注意标签需要与noticeMethod中的名称完全匹配
  { label: "短信通知" },
  { label: "机器人推送" },
]);

onShow(() => {
  const data = store.getBusinessData("haircut-noticeWay");
  if (data && data[0]?.noticeMethod) {
    currentItem.value = { ...data[0] };
    initSelected(data[0].noticeMethod);
  }
});

// 初始化选中状态
const initSelected = (noticeMethods: string) => {
  if (!noticeMethods) return;
  selectedMethods.value = noticeMethods.split(',').map(method => method.trim());
  console.log("初始化选中的方法:", selectedMethods.value);
};

// 检查是否选中
const isSelected = (method: string) => {
  return selectedMethods.value.includes(method);
};

// 切换选中状态
const toggleSelect = (method: string) => {
  if (isSelected(method)) {
    // 如果已选中，则移除
    selectedMethods.value = selectedMethods.value.filter(m => m !== method);
  } else {
    // 如果未选中，则添加
    selectedMethods.value = [...selectedMethods.value, method];
  }
  console.log("当前选中的方法:", selectedMethods.value);
};

// 返回上一页
const cancel = () => {
  uni.navigateBack();
};

// 保存选中的选项
const save = async() => {
  if (selectedMethods.value.length === 0) {
    uni.showToast({
      title: '请至少选择一种通知方式',
      icon: 'none'
    });
    return;
  }
  const noticeMethodString = selectedMethods.value.join(',');
  // 更新 currentItem 中的 noticeMethod
  currentItem.value.noticeMethod = noticeMethodString;
  delete currentItem.value.sysCreated
  delete currentItem.value.noticeTime
  delete currentItem.value.adminDisplay
  delete currentItem.value.feedbackContent
  delete currentItem.value.userDisplay
  try {
    await hairBaseConfig.againNotice(currentItem.value)
    uni.showToast({
      title: '保存成功',
      icon: 'none'
    });
  uni.navigateBack();
  } catch (error) {
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    });
  }
  console.log("选中的通知方式:", noticeMethodString, currentItem.value);
};
</script>

<style scoped lang="scss">
.notice-way-container {
  width: 100%;
  padding: 10px 15px;
  font-size: 14px;
  padding-bottom: 100px; /* 防止按钮遮挡内容 */

  .notice-way-title {
    color: #333333;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .notice-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    cursor: pointer;

    .notice-item-span {
      margin-left: 4px;
    }
  }
}

.bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}
</style>