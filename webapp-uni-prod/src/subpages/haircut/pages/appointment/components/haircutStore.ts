import { defineStore } from 'pinia'
interface BusinessData {
  [key: string]: any // 可以是任意类型的值
}
export const useAppointmentStore = defineStore('appointment', {
  state: () => ({
    configData: null as null | Record<string, any>, // 其他配置数据
    _businessData: null as null | BusinessData,    // 动态业务数据存储
  }),

  actions: {
    /**
     * 设置业务数据（键值对形式）
     * @param key - 存储的字段名
     * @param value - 存储的值（任意类型）
     */
    setBusinessData(key: string, value: any) {
      if (!this._businessData) {
        this._businessData = {}; // 初始化空对象
      }
      this._businessData[key] = value; // 动态存储
    },

    /**
     * 获取业务数据（并自动清空）
     * @param key - 可选，如果传 key 就返回指定字段，否则返回整个对象
     */
    getBusinessData(key?: string): any | null {
      if (!this._businessData) return null;

      if (key) {
        const value = this._businessData[key];
        delete this._businessData[key]; // 只删除指定字段
        return value;
      } else {
        const data = this._businessData;
        this._businessData = null; // 清空整个对象
        return data;
      }
    },

    /** 手动清空数据 */
    clearBusinessData() {
      this._businessData = null;
    }
  }
})