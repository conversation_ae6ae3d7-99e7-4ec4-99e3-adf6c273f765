<template>
  <div class="main">
    <div class="hair-top">
      <span class="hair-top-title">配置新一期预约</span>
      <div class="nav">
        <span
          v-for="item in navItems"
          :key="item.type"
          :class="['nav-item', { active: activeType === item.type }]"
          :data-type="item.type"
          @click="handleNavClick(item.type)"
          :style="{ marginLeft: item.type === 'all' ? '0' : '' }"
        >
          {{ item.label }}
        </span>
      </div>
    </div>
    <div class="hair-subject">
      <!-- 动态渲染预约项 -->
      <div
        v-for="(schedule, index) in scheduleList"
        :key="index"
        class="hair-subject-item"
      >
        <div class="item-icon" @click="delBtn(index)">
          <SlSvgIcon name="components-21" size="20" />
        </div>
        <div class="item-top">{{ schedule.date }} {{ schedule.day }}</div>
        <div class="item">
          <div class="item-time">
            <SlSvgIcon name="meeting-12-7" size="12" />
            {{ schedule.timeRange }}
          </div>
          <div class="item-btn" @click="reconfig(index)">重新配置</div>
        </div>
      </div>

      <!-- 按钮新增 -->
      <div class="footer-btn">
        <button @click="addItem">新增</button>
      </div>
    </div>
    <span>默认上期第二周周五17点开放新一期预约</span>
    <div class="bottom-btn">
      <BaseButton btn-type="save" size="large" @click="confirm"
        >确定</BaseButton
      >
    </div>
  </div>
  <ConfirmDialog
    v-if="showConfirm"
    @close="showConfirm = false"
    @confirm="handleConfirmDialog"
    >您确定要删除当前配置日期吗?</ConfirmDialog
  >
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import SlSvgIcon from "@/components/SlSVgIcon.vue";
import BaseButton from "@/components/BaseButton.vue";
import useMessage from '@/hooks/use-message';
const showConfirm = ref(false);
const activeType = ref("下周");
const message = useMessage()
// 导航项
const navItems = [
  { label: "下周", type: "下周" },
  { label: "下下周", type: "下下周" },
];

// 模拟后端数据
const mockScheduleData: any = {
  下周: [
    { date: "06/10", day: "周一", timeRange: "08:30~17:00" },
    { date: "06/11", day: "周二", timeRange: "09:00~18:00" },
    { date: "06/12", day: "周三", timeRange: "08:00~16:30" },
  ],
  下下周: [
    { date: "06/17", day: "周一", timeRange: "08:30~17:00" },
    { date: "06/18", day: "周二", timeRange: "10:00~19:00" },
  ],
};

// 计算属性：根据当前选中的类型返回对应的数据
const scheduleList = computed(() => {
  return mockScheduleData[activeType.value] || [];
});

// 处理导航点击
const handleNavClick = (type: string) => {
  activeType.value = type;
};

// 删除项
const delBtn = (index: number) => {
  showConfirm.value = true
  console.log("删除项:", index);
  // mockScheduleData[activeType.value].splice(index, 1)
};

// 重新配置
const reconfig = (index: number) => {
  console.log("重新配置项:", index);
  uni.navigateTo({ url: '/subpages/haircut/pages/appointment/components/haircut-relocation/index' })
  // 这里可以打开配置弹窗等操作
};

// 新增项
const addItem = () => {
  console.log("新增项");
  // mockScheduleData[activeType.value].push(newItem)
};
// 确定保存项
const confirm = () => {};

// 确认弹窗的确认按钮
function handleConfirmDialog() {
  showConfirm.value = false
  message.error('删除失败，请重试~')

}
</script>
<style scoped lang="scss">
.main {
  width: 100%;
  height: 100vh;
  background-color: #f4f6fa;
}
.hair-top {
  background: #ffffff;
  height: 78px;
  width: 100%;
  padding: 0px 15px;
  padding-top: 14px;
  border-bottom: 1px solid #99999933;
  .nav {
    width: 100%;
    margin-top: 20px;
    display: flex;
    align-items: center;

    .nav-item {
      margin-right: 20px;
      color: #333333;
      font-size: 14px;
      cursor: pointer;
      position: relative;
      padding-bottom: 4px;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background-color: transparent;
        transition: width 0.3s ease, background-color 0.3s ease;
      }

      &.active {
        // color: #455bff;
        font-size: 16px;
        font-weight: bold;

        &::after {
          width: 39%;
          background-color: #4355fc;
          margin-left: 10px;
        }
      }
    }
  }
}
.hair-subject {
  width: 100%;
  padding: 18px 15px;
  background: #ffffff;
  .hair-subject-item {
    width: 100%;
    height: 63px;
    border-radius: 5px;
    background: #ffffff;
    border: 1px solid #9999997f;
    padding: 12px 6px 7px 16px;
    position: relative;
    margin-bottom: 17px;
    .item-icon {
      position: absolute;
      right: -9px;
      top: -10px;
    }
    .item {
      display: flex;
      justify-content: space-between;
      margin-top: 3px;
      .item-time {
        color: #666666;
        font-size: 14px;
        line-height: 24px;
      }
      .item-btn {
        width: 68px;
        height: 24px;
        line-height: 24px;
        border-radius: 3px;
        text-align: center;
        background: #dce4fd;
        color: #3968ee;
      }
    }
  }
  .footer-btn {
    width: 100%;
    text-align: center;
    button {
      width: 60px;
      height: 28px;
      line-height: 28px;
      border-radius: 3px;
      background: #4f7af6;
      color: #ffffff;
      font-size: 14px;
    }
  }
}
.bottom-btn {
  position: absolute;
  bottom: 0;
  padding: 0 15px;
  margin-bottom: 9px;
}
</style>
