<template>
  <SlTabbarPage :tab-bar-root="'subpages/haircut'">
    <div class="main">
      <!-- 顶部标题与导航 -->
      <div class="hair-top">
        <span class="hair-top-title">配置新一期预约</span>
        <div class="nav">
          <span
            v-for="item in navItems"
            :key="item.type"
            :class="['nav-item', { active: activeType === item.type }]"
            :data-type="item.type"
            @click="handleNavClick(item.type)"
            :style="{ marginLeft: item.type === 'all' ? '0' : '' }"
          >
            {{ item.label }}
          </span>
        </div>
      </div>

      <!-- 预约项展示区 -->
      <div class="hair-subject">
        <div
          v-for="(schedule, index) in scheduleList"
          :key="index"
          class="hair-subject-item"
        >
          <!-- 删除按钮 -->
          <div class="item-icon" @click="delBtn(schedule, index)">
            <!-- <SlSvgIcon name="components-21" size="20" /> -->
            <SlSubSvgIcon subpage="haircut" name="20-20-21" size="20" />
          </div>

          <!-- 日期与星期 -->
          <div class="item-top">{{ schedule.date }} {{ schedule.week }}</div>

          <!-- 时间信息与按钮 -->
          <div class="item">
            <div>
              <div class="item-time">
                <!-- <SlSVgIcon name="16-16-20" size="16" /> -->
                <SlSubSvgIcon subpage="haircut" name="16-16-20" size="16" />
                {{ schedule.timeRange }}
              </div>
              <!-- 特殊不营业时间 -->
              <div
                v-for="(overTime, i) in schedule.overTimeRange"
                :key="i"
                class="item-time"
              >
                <!-- <SlSVgIcon name="16-16-21" size="16" /> -->
                <SlSubSvgIcon subpage="haircut" name="16-16-21" size="16" />
                {{ overTime }}
              </div>
            </div>

            <!-- 重新配置按钮 -->
            <div class="item-btn" @click="reconfig(index)">重新配置</div>
          </div>
        </div>

        <!-- 新增按钮 -->
        <div class="footer-btn">
          <button @click="addItem">新增</button>
        </div>
      </div>

      <!-- 提示文案 -->
      <div class="tips">默认上期第二周周五17点开放新一期预约</div>

      <!-- 底部确认按钮 -->
      <!-- <div class="bottom-btn">
      <BaseButton btn-type="save" size="large" @click="confirm">确定</BaseButton>
    </div> -->
    </div>

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      v-if="showConfirm"
      @close="showConfirm = false"
      @confirm="handleConfirmDialog"
    >
      您确定要删除当前配置日期吗?
    </ConfirmDialog>
  </SlTabbarPage>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, reactive } from "vue";
import { onShow } from "@dcloudio/uni-app";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import SlSvgIcon from "@/components/SlSVgIcon.vue";
import BaseButton from "@/components/BaseButton.vue";
import useMessage from "@/hooks/use-message";
import { useAppointmentStore } from "../haircutStore";
import getHaircutManage from "@/service/haircut/haircut.service";
import { mockHaircutConfig } from "../../Haircut";

// 状态管理
const store = useAppointmentStore();
const showConfirm = ref(false);
const activeType = ref("第一周");
const message = useMessage();
const editIndex = ref<number | null>(null); // 当前编辑索引
const currentId = ref("");
// 导航栏数据
const navItems = [
  { label: "第一周", type: "第一周" },
  { label: "第二周", type: "第二周" },
];

// 预约数据（每周最多7条）
const scheduleData: any = reactive<Record<string, any[]>>({
  第一周: [],
  第二周: [],
});

// 转换后端数据为前端需要的格式
const transformBackendData = (backendData: any[]) => {
  return backendData.map((item) => {
    // 格式化日期为 MM/DD
    const dateParts = item.businessDate.split("/");
    const formattedDate = `${dateParts[1]}/${dateParts[2]}`;

    // 处理特殊时间段
    const overTimeRange =
      item.specialTimeList?.map(
        (special: any) =>
          `${special.specialStartTime}~${special.specialEndTime}`
      ) || [];

    return {
      date: formattedDate,
      week: item.weekDay,
      timeRange: `${item.openTime}~${item.closeTime}`,
      overTimeRange,
      // 保留原始数据用于编辑
      rawData: item,
    };
  });
};

// 获取理发配置数据
const getHaircutConfig = async (type: number) => {
  const weekType = type === 1 ? "第一周" : "第二周";
  // scheduleData[weekType] = transformBackendData(mockHaircutConfig);
  try {
    const res = await getHaircutManage.getHaircutconfig(type);
    console.log(res, "resssssss");
    if (res) {
      const weekType = type === 1 ? "第一周" : "第二周";
      scheduleData[weekType] = transformBackendData(res);
    }
  } catch (error) {
    console.error("获取理发配置失败:", error);
    message.error("获取配置失败，请重试");
  }
};

// 页面显示时处理数据（页面跳转返回的新增配置）
onShow(() => {
  const data = store.getBusinessData("haircut-relocation");
  console.log(123456);
  if (data) {
    console.log("接收到业务数据:", data);
    weekNumber.value;
    getHaircutConfig(weekNumber.value);
    // 转换并处理返回的数据
    // const transformedData =data;
    // // 如果是重新配置，则更新对应索引的数据
    // if (editIndex.value !== null) {
    //   scheduleData[activeType.value][editIndex.value] = transformedData;
    //   editIndex.value = null; // 使用完后重置
    // } else {
    //   // 新增模式
    //   if (scheduleData[activeType.value].length >= 7) {
    //     message.error("每周最多只能配置7个预约时间段");
    //     return;
    //   }
    //   scheduleData[activeType.value].push(transformedData);
    // }
  }
});

// 计算属性：当前选择周的数据
const scheduleList = computed(() => {
  return scheduleData[activeType.value] || [];
});
const weekNumber = ref(1);
// 切换导航周次
const handleNavClick = (type: string) => {
  console.log(type);

  activeType.value = type;
  // 根据类型获取数据（第一周对应1，第二周对应2）
  weekNumber.value = type === "第一周" ? 1 : 2;
  getHaircutConfig(weekNumber.value);
};

// 删除项（确认弹窗控制）
const delBtn = (data: any, index: number) => {
  showConfirm.value = true;
  editIndex.value = index; // 保存当前要删除的索引
  console.log("删除项:", index, data.rawData.id, data);
  currentId.value = data.rawData.id;
};

// 重新配置（跳转编辑页并传值）
const reconfig = (index: number) => {
  let schedule = scheduleList.value[index];
  schedule.weekNumber = weekNumber.value;
  schedule.type = "edit";
  editIndex.value = index; // 设置为编辑模式
  // 传递原始数据用于编辑
  store.setBusinessData("haircut-relocation-edit", schedule);
  console.log(schedule, "schedule", index, editIndex.value);

  uni.navigateTo({
    url: "/subpages/haircut/pages/appointment/components/haircut-relocation/index",
  });
};

// 新增预约项
const addItem = () => {
  if (scheduleData[activeType.value]?.length >= 7) {
    message.error("每周最多只能配置7个预约时间段");
    return;
  }
  store.setBusinessData("haircut-relocation-edit", {
    weekNumber: weekNumber.value,
    type: "add",
  });
  uni.navigateTo({
    url: "/subpages/haircut/pages/appointment/components/haircut-relocation/index",
  });
};

// 点击确认按钮的逻辑
const confirm = () => {
  console.log("保存数据：", scheduleData);
};

// 弹窗确认删除项
const handleConfirmDialog = async () => {
  try {
    if (currentId.value === null) return;
    // 调用删除API
    await getHaircutManage.delHaircutconfig(currentId.value);
    uni.showToast({ title: "删除成功", icon: "none" });
    weekNumber.value;
    getHaircutConfig(weekNumber.value);
  } catch (error) {
    console.error("删除失败:", error);
    uni.showToast({ title: "删除失败，请重试~", icon: "none" });
  } finally {
    showConfirm.value = false;
    editIndex.value = null;
  }
};

// 初始化加载第一周数据
onMounted(() => {
  // getHaircutConfig(1);
  handleNavClick("第一周");
});
</script>
<style scoped lang="scss">
.main {
  width: 100%;
  height: 100vh;
  background-color: #f4f6fa;
}

.hair-top {
  background: #ffffff;
  height: 78px;
  padding: 14px 15px 0;
  border-bottom: 1px solid #99999933;

  .hair-top-title {
    font-weight: bold;
  }

  .nav {
    margin-top: 20px;
    display: flex;
    align-items: center;

    .nav-item {
      margin-right: 20px;
      color: #333;
      font-size: 14px;
      cursor: pointer;
      position: relative;
      padding-bottom: 4px;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background-color: transparent;
        transition: width 0.3s ease, background-color 0.3s ease;
      }

      &.active {
        font-size: 16px;
        font-weight: bold;

        &::after {
          width: 39%;
          background-color: #4355fc;
          margin-left: 12px;
        }
      }
    }
  }
}

.hair-subject {
  padding: 18px 15px;
  background: #fff;

  .hair-subject-item {
    min-height: 63px;
    border-radius: 5px;
    background: #fff;
    border: 1px solid #9999997f;
    padding: 12px 6px 7px 16px;
    position: relative;
    margin-bottom: 17px;

    .item-icon {
      position: absolute;
      right: -9px;
      top: -10px;
    }

    .item {
      display: flex;
      justify-content: space-between;
      margin-top: 3px;

      .item-time {
        color: #666;
        font-size: 14px;
        line-height: 24px;
      }

      .item-btn {
        width: 68px;
        height: 24px;
        line-height: 24px;
        border-radius: 3px;
        text-align: center;
        background: #dce4fd;
        color: #3968ee;
      }
    }
  }

  .footer-btn {
    text-align: center;

    button {
      width: 60px;
      height: 28px;
      line-height: 28px;
      border-radius: 3px;
      background: #4f7af6;
      color: #fff;
      font-size: 14px;
    }
  }
}

.bottom-btn {
  // position: absolute;
  // bottom: 0;
  // padding: 0 15px;
  // margin-bottom: 9px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: space-between;
  height: 70px;
  box-shadow: 0 0 5px rgba(146, 146, 146, 0.2);
  z-index: 999;
}
</style>
