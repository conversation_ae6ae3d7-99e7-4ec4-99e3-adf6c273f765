<template>
  <div class="config-container">
    <div class="config-top">
      <span class="config-top-title">每周营业时间(默认)</span>
      <div class="config-item">
        <div
          v-for="(item, index) in options"
          :key="index"
          class="notice-item"
          @click="toggleSelect(index)"
        >
          <SlSVgIcon
            :name="businessConfig.weekDays[index] ? '12-12-14' : '12-12-13'"
            size="12"
          />
          <span class="notice-item-span">{{ item.label }}</span>
        </div>
      </div>
    </div>
    <!-- 营业时间 -->
    <div class="config-time-default">
      <div class="config-time-default-item">
        <span class="config-top-title">每天营业时间(默认)</span>
        <!-- 开始时间 -->
        <div class="deploy-item">
          <span class="label">开始时间</span>
          <div class="deploy-select">
            <SLSelect
              v-model="startTime"
              :options="timeOptions"
              placeholder="请选择开始时间"
              icon-name="components-12-7"
              icon-size="14"
              :rotate-on-open="true"
              @change="handleTimeChange('start')"
            />
          </div>
        </div>
        <!-- 结束时间 -->
        <div class="deploy-item">
          <span class="label">结束时间</span>
          <div class="deploy-select">
            <SLSelect
              v-model="endTime"
              :options="timeOptions"
              placeholder="请选择结束时间"
              icon-name="components-12-7"
              icon-size="14"
              :rotate-on-open="true"
              @change="handleTimeChange('end')"
            />
            <div v-if="timeError" class="error-tip">{{ timeError }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 午休时间 -->
    <div class="config-time-default">
      <div class="config-time-default-item">
        <span class="config-top-title">每天午休时间(默认)</span>
        <!-- 开始时间 -->
        <div class="deploy-item">
          <span class="label">开始时间</span>
          <div class="deploy-select">
            <SLSelect
              v-model="breakStartTime"
              :options="timeOptions"
              placeholder="请选择开始时间"
              icon-name="components-12-7"
              icon-size="14"
              :rotate-on-open="true"
              @change="handleBreakTimeChange('start')"
            />
          </div>
        </div>
        <!-- 结束时间 -->
        <div class="deploy-item">
          <span class="label">结束时间</span>
          <div class="deploy-select">
            <SLSelect
              v-model="breakEndTime"
              :options="timeOptions"
              placeholder="请选择结束时间"
              icon-name="components-12-7"
              icon-size="14"
              :rotate-on-open="true"
              @change="handleBreakTimeChange('end')"
            />
            <div v-if="breakTimeError" class="error-tip">
              {{ breakTimeError }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 间隔 -->
    <div class="config-time-default">
      <div class="config-time-default-item interval-c">
        <span class="config-top-title">每位间隔时间(默认)</span>
        <div class="interval">
          <span class="interval-title">间隔时间</span>
          <div class="interval-icon">
            <SlSVgIcon
              name="20-20-24"
              size="20"
              @click="subtract"
              v-if="intervalTime !== 0"
            />
            <span class="interval-num">{{ intervalTime }}</span>
            <SlSVgIcon
              :name="`20-20-${intervalTime < 60 ? '25' : '26'}`"
              size="20"
              @click="add"
            />
            <span class="interval-unit">分钟/位</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 按钮 -->
    <div class="bottom-btn">
      <BaseButton btn-type="cancel" @click="cancel" />
      <BaseButton btn-type="save" @click="save" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import hairBaseConfig from "@/service/haircut/haircut.service";
import { IHairBaseConfig,mockHairConfig} from "../../Haircut";
// 营业时间配置
const businessConfig = ref<{
  weekDays: boolean[];
  dailyHours: { start: string; end: string };
  breakHours: { start: string; end: string };
  interval: number;
}>({
  weekDays: [true, false, true, false, true, false, false],
  dailyHours: { start: "08:30", end: "17:00" },
  breakHours: { start: "11:30", end: "13:30" },
  interval: 30
});
// 初始化默认值
onMounted(() => {
  getHairbaseconfig();
});
const startTime = ref("");
const endTime = ref("");
const breakStartTime = ref("");
const breakEndTime = ref("");
const timeError = ref("");
const breakTimeError = ref("");
const intervalTime = ref(30);

// 通知方式选项
const options = ref([
  { label: "周一", value: 0 },
  { label: "周二", value: 1 },
  { label: "周三", value: 2 },
  { label: "周四", value: 3 },
  { label: "周五", value: 4 },
  { label: "周六", value: 5 },
  { label: "周日", value: 6 },
]);
// 将星期数组转换为字符串格式 "周一,周三,周五"
const getWeekDaysString = () => {
  const days = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];
  return businessConfig.value.weekDays
    .map((selected, index) => (selected ? days[index] : null))
    .filter((day) => day !== null)
    .join(",");
};
// 时间选项（每半小时一个间隔）
const timeOptions = ref(
  Array.from({ length: 24 * 2 }, (_, i) => {
    const hours = Math.floor(i / 2);
    const minutes = i % 2 === 0 ? "00" : "30";
    const timeValue = `${hours.toString().padStart(2, "0")}:${minutes}`;
    return {
      label: timeValue,
      value: timeValue,
    };
  })
);

// 切换选中状态
const toggleSelect = (index: number) => {
  businessConfig.value.weekDays[index] = !businessConfig.value.weekDays[index];
};

// 处理营业时间变更
const handleTimeChange = (type: "start" | "end") => {
  if (type === "start") {
    businessConfig.value.dailyHours.start = startTime.value;
  } else {
    businessConfig.value.dailyHours.end = endTime.value;
  }
  validateTimes();
};

// 处理午休时间变更
const handleBreakTimeChange = (type: "start" | "end") => {
  if (type === "start") {
    businessConfig.value.breakHours.start = breakStartTime.value;
  } else {
    businessConfig.value.breakHours.end = breakEndTime.value;
  }
  validateBreakTimes();
};

// 验证营业时间逻辑
const validateTimes = () => {
  if (startTime.value && endTime.value) {
    if (startTime.value >= endTime.value) {
      timeError.value = "结束时间必须晚于开始时间";
      return false;
    }
  }
  timeError.value = "";
  return true;
};

// 验证午休时间逻辑
const validateBreakTimes = () => {
  if (breakStartTime.value && breakEndTime.value) {
    if (breakStartTime.value >= breakEndTime.value) {
      breakTimeError.value = "午休结束时间必须晚于开始时间";
      return false;
    }
    // 检查午休时间是否在营业时间内
    if (
      businessConfig.value.dailyHours.start &&
      businessConfig.value.dailyHours.end
    ) {
      if (
        breakStartTime.value < businessConfig.value.dailyHours.start ||
        breakEndTime.value > businessConfig.value.dailyHours.end
      ) {
        breakTimeError.value = "午休时间必须在营业时间内";
        return false;
      }
    }
  }
  breakTimeError.value = "";
  return true;
};

const subtract = () => {
  if (intervalTime.value > 0) {
    intervalTime.value -= 10;
    businessConfig.value.interval = intervalTime.value;
  }
};

const add = () => {
  if (intervalTime.value < 60) {
    intervalTime.value += 10;
    businessConfig.value.interval = intervalTime.value;
  }
};

// 返回上一页
const cancel = () => {
  uni.navigateBack();
};

// 保存配置
const save = () => {
  // 验证数据
  if (!validateTimes() || !validateBreakTimes()) {
    return;
  }
  // 确保至少选择了一个营业日
  const hasSelectedDay = businessConfig.value.weekDays.some((day) => day);
  if (!hasSelectedDay) {
    uni.showToast({
      title: "请至少选择一个营业日",
      icon: "none",
    });
    return;
  }

  // 确保营业时间已选择
  if (
    !businessConfig.value.dailyHours.start ||
    !businessConfig.value.dailyHours.end
  ) {
    uni.showToast({
      title: "请设置营业时间",
      icon: "none",
    });
    return;
  }
  // 准备符合后端接口的数据结构
  const requestData = {
    weekDays: getWeekDaysString(),
    dailyOpenTime: businessConfig.value.dailyHours.start,
    dailyCloseTime: businessConfig.value.dailyHours.end,
    lunchStartTime: businessConfig.value.breakHours.start,
    lunchEndTime: businessConfig.value.breakHours.end,
    customerInterval: businessConfig.value.interval,
  };
  console.log("提交的配置数据:", requestData);
  // submitToBackend(configData);
  uni.showToast({
    title: "保存成功",
    icon: "success",
  });

  // 返回上一页
  setTimeout(() => {
    uni.navigateBack();
  }, 1500);
};
//   ==============================接口======================
const getHairbaseconfig = () => {
  // hairBaseConfig.getHairbaseconfig().then(res=>{
  //     console.log(res);
  // })
  const data:IHairBaseConfig = mockHairConfig
    // 将字符串格式的星期转换为数组
    const daysMap:any = {
        '周一': 0, '周二': 1, '周三': 2, 
        '周四': 3, '周五': 4, '周六': 5, '周日': 6
      };
  const selectedDays = data.weekDays.split(',');
      const weekDays = Array(7).fill(false);
      selectedDays.forEach(day => {
        const index = daysMap[day];
        if (index !== undefined) {
          weekDays[index] = true;
        }
      });
        // 更新业务配置
        businessConfig.value = {
        weekDays,
        dailyHours: {
          start: data.dailyOpenTime,
          end: data.dailyCloseTime
        },
        breakHours: {
          start: data.lunchStartTime,
          end: data.lunchEndTime
        },
        interval: data.customerInterval
      };
      startTime.value = data.dailyOpenTime;
      endTime.value = data.dailyCloseTime;
      breakStartTime.value = data.lunchStartTime;
      breakEndTime.value = data.lunchEndTime;
      intervalTime.value = data.customerInterval;
};
</script>

<style scoped lang="scss">
.config-container {
  width: 100%;
  background: #f2f3f7;
  font-size: 14px;
  .config-top {
    height: auto;
    padding: 14px 15px;
    background: #ffffff;
    margin-bottom: 10px;
    .config-top-title {
      color: #333333;
    }
    .config-item {
      margin-top: 10px;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 10px;
    }
    .notice-item {
      display: flex;
      align-items: center;
      gap: 5px;
      cursor: pointer;
    }
    /* 单独控制每列对齐方式 */
    .notice-item:nth-child(3n + 1) {
      justify-content: flex-start;
    }
    .notice-item:nth-child(3n + 2) {
      justify-content: center;
    }
    .notice-item:nth-child(3n + 3) {
      justify-content: flex-end;
    }
  }
  .config-time-default {
    padding: 14px 15px;
    background: #ffffff;
    margin-bottom: 10px;
    .config-time-default-item {
      height: 132px;
      .interval {
        margin-top: 14px;
        display: flex;
        justify-content: space-between;
        .interval-icon {
          display: flex;
          align-items: center;
          .interval-num {
            width: 30px;
            text-align: center;
          }
          .interval-unit {
            color: #666666;
            margin-left: 4px;
          }
        }
      }
    }
    .interval-c {
      height: 78px;
    }
  }
  .deploy-item {
    margin-top: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    position: relative;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 14px;
      color: #333;
      min-width: 70px;
    }

    .deploy-select {
      width: 220px;
      position: relative;
    }

    .error-tip {
      position: absolute;
      right: 0;
      bottom: -18px;
      color: #f56c6c;
      font-size: 12px;
    }
  }
}
.bottom-btn {
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  padding-bottom: 26px;
}
</style>
