<template>
  <div class="config-container">
    <div class="config-top">
      <span class="config-top-title">每周营业时间(默认)</span>
      <div class="config-item">
        <div
          v-for="(item, index) in options"
          :key="index"
          class="notice-item"
          @click="toggleSelect(index)"
        >
          <SlSubSvgIcon
            :name="businessConfig.weekDays[index] ? '12-12-14' : '12-12-13'"
            size="12"
            subpage="haircut"
          />
          <span class="notice-item-span">{{ item.label }}</span>
        </div>
      </div>
    </div>
    <!-- 营业时间 -->
    <div class="config-time-default">
      <div class="config-time-default-item">
        <span class="config-top-title">每天营业时间(默认)</span>
        <!-- 开始时间 -->
        <div class="deploy-item">
          <span class="label">开始时间</span>
          <div class="deploy-select">
            <SLSelect
              class="select-item"
              v-model="startTime"
              :options="timeOptions"
              placeholder="请选择开始时间"
              icon-name="12-12-7"
              icon-size="14"
              :rotate-on-open="true"
              @change="handleTimeChange('start')"
            />
          </div>
        </div>
        <!-- 结束时间 -->
        <div class="deploy-item">
          <span class="label">结束时间</span>
          <div class="deploy-select">
            <SLSelect
            class="select-item"
              v-model="endTime"
              :options="timeOptions"
              placeholder="请选择结束时间"
              icon-name="12-12-7"
              icon-size="14"
              :rotate-on-open="true"
              @change="handleTimeChange('end')"
            />
            <div v-if="timeError" class="error-tip">{{ timeError }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 午休时间 -->
    <div class="config-time-default">
      <div class="config-time-default-item">
        <span class="config-top-title">每天午休时间(默认)</span>
        <!-- 开始时间 -->
        <div class="deploy-item">
          <span class="label">开始时间</span>
          <div class="deploy-select">
            <SLSelect
            class="select-item"
              v-model="breakStartTime"
              :options="timeOptions"
              placeholder="请选择开始时间"
              icon-name="12-12-7"
              icon-size="14"
              :rotate-on-open="true"
              @change="handleBreakTimeChange('start')"
            />
          </div>
        </div>
        <!-- 结束时间 -->
        <div class="deploy-item">
          <span class="label">结束时间</span>
          <div class="deploy-select">
            <SLSelect
            class="select-item"
              v-model="breakEndTime"
              :options="timeOptions"
              placeholder="请选择结束时间"
              icon-name="12-12-7"
              icon-size="14"
              :rotate-on-open="true"
              @change="handleBreakTimeChange('end')"
            />
            <div v-if="breakTimeError" class="error-tip">
              {{ breakTimeError }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 间隔 -->
    <div class="config-time-default">
      <div class="config-time-default-item interval-c">
        <span class="config-top-title">每位间隔时间(默认)</span>
        <div class="interval">
          <span class="interval-title">间隔时间</span>
          <div class="interval-icon">
            <SlSubSvgIcon
            name="20-20-24"
            size="20"
            @click="subtract"
            subpage="haircut"
            v-if="intervalTime !== 0"
          />
            <span class="interval-num">{{ intervalTime }}</span>
            <SlSubSvgIcon
            :name="`20-20-${intervalTime < 60 ? '25' : '26'}`"
            size="20"
            subpage="haircut"
            @click="add"
          />
            <span class="interval-unit">分钟/位</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 按钮 -->
  </div>
  <div class="cancelAll">
      <BaseButton btn-type="cancel" @click="cancel" >恢复默认配置</BaseButton>
      <BaseButton btn-type="save" @click="save" />
  </div>
    <!-- 确认对话框 -->
    <ConfirmDialog
    v-if="showConfirmDialog"
    @close="closeConfirmDialog"
    @confirm="handleConfirmAction"
  >
  您确定要恢复默认配置吗?
  </ConfirmDialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import hairBaseConfig1 from "@/service/haircut/haircut.service";
import { IHairBaseConfig,mockHairConfig} from "../../Haircut";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
const showConfirmDialog = ref(false); // 是否显示确认对话框
const currentId = ref('')
// 营业时间配置
const businessConfig = ref<{
  weekDays: boolean[];
  dailyHours: { start: string; end: string };
  breakHours: { start: string; end: string };
  interval: number;
}>({
  weekDays: [true, false, true, false, true, false, false],
  dailyHours: { start: "08:30", end: "17:00" },
  breakHours: { start: "11:30", end: "13:30" },
  interval: 30
});
// 初始化默认值
onMounted(() => {
  getHairbaseconfig();
});
const startTime = ref("");
const endTime = ref("");
const breakStartTime = ref("");
const breakEndTime = ref("");
const timeError = ref("");
const breakTimeError = ref("");
const intervalTime = ref(30);

// 通知方式选项
const options = ref([
  { label: "周一", value: 0 },
  { label: "周二", value: 1 },
  { label: "周三", value: 2 },
  { label: "周四", value: 3 },
  { label: "周五", value: 4 },
  { label: "周六", value: 5 },
  { label: "周日", value: 6 },
]);
// 将星期数组转换为字符串格式 "周一,周三,周五"
const getWeekDaysString = () => {
  const days = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];
  return businessConfig.value.weekDays
    .map((selected, index) => (selected ? days[index] : null))
    .filter((day) => day !== null)
    .join(",");
};
// 时间选项（每半小时一个间隔）
const timeOptions = ref(
  Array.from({ length: 24 * 2 }, (_, i) => {
    const hours = Math.floor(i / 2);
    const minutes = i % 2 === 0 ? "00" : "30";
    const timeValue = `${hours.toString().padStart(2, "0")}:${minutes}`;
    return {
      label: timeValue,
      value: timeValue,
    };
  })
);

// 切换选中状态
const toggleSelect = (index: number) => {
  businessConfig.value.weekDays[index] = !businessConfig.value.weekDays[index];
};

// 处理营业时间变更
const handleTimeChange = (type: "start" | "end") => {
  if (type === "start") {
    businessConfig.value.dailyHours.start = startTime.value;
  } else {
    businessConfig.value.dailyHours.end = endTime.value;
  }
  validateTimes();
};

// 处理午休时间变更
const handleBreakTimeChange = (type: "start" | "end") => {
  if (type === "start") {
    businessConfig.value.breakHours.start = breakStartTime.value;
  } else {
    businessConfig.value.breakHours.end = breakEndTime.value;
  }
  validateBreakTimes();
};

// 验证营业时间逻辑
const validateTimes = () => {
  if (startTime.value && endTime.value) {
    if (startTime.value >= endTime.value) {
      timeError.value = "结束时间必须晚于开始时间";
      return false;
    }
  }
  timeError.value = "";
  return true;
};

// 验证午休时间逻辑
const validateBreakTimes = () => {
  if (breakStartTime.value && breakEndTime.value) {
    if (breakStartTime.value >= breakEndTime.value) {
      breakTimeError.value = "午休结束时间必须晚于开始时间";
      return false;
    }
    // 检查午休时间是否在营业时间内
    if (
      businessConfig.value.dailyHours.start &&
      businessConfig.value.dailyHours.end
    ) {
      if (
        breakStartTime.value < businessConfig.value.dailyHours.start ||
        breakEndTime.value > businessConfig.value.dailyHours.end
      ) {
        breakTimeError.value = "午休时间必须在营业时间内";
        return false;
      }
    }
  }
  breakTimeError.value = "";
  return true;
};

const subtract = () => {
  if (intervalTime.value > 10) {
    intervalTime.value -= 10;
    businessConfig.value.interval = intervalTime.value;
  }else{
    uni.showToast({
      title: "间隔时间不能少于10分钟",
      icon: "none",
    });
  }
};

const add = () => {
  if (intervalTime.value < 60) {
    intervalTime.value += 10;
    businessConfig.value.interval = intervalTime.value;
  }
};
/**
 * 关闭确认对话框
 */
 function closeConfirmDialog() {
  showConfirmDialog.value = false;
}
/**
 * 处理确认对话框的确认操作
 */
 async function handleConfirmAction() {
    const defaultWeekDays = [true, false, true, false, true, false, false];
    businessConfig.value = {
      weekDays: defaultWeekDays,
      dailyHours: {
        start: mockHairConfig.dailyOpenTime,
        end: mockHairConfig.dailyCloseTime
      },
      breakHours: {
        start: mockHairConfig.lunchStartTime,
        end: mockHairConfig.lunchEndTime
      },
      interval: mockHairConfig.customerInterval
    };
    startTime.value = mockHairConfig.dailyOpenTime;
    endTime.value = mockHairConfig.dailyCloseTime;
    breakStartTime.value = mockHairConfig.lunchStartTime;
    breakEndTime.value = mockHairConfig.lunchEndTime;
    intervalTime.value = mockHairConfig.customerInterval;
    showConfirmDialog.value = false;
    save(true)
}




// 返回上一页
const cancel = () => {
  // uni.navigateBack();
  showConfirmDialog.value = true
};

// 保存配置
const save = async(defaultConfig?:boolean) => {
  // 验证数据
  if (!validateTimes() || !validateBreakTimes()) {
    return;
  }
  // 确保至少选择了一个营业日
  const hasSelectedDay = businessConfig.value.weekDays.some((day) => day);
  if (!hasSelectedDay) {
    uni.showToast({
      title: "请至少选择一个营业日",
      icon: "none",
    });
    return;
  }

  // 确保营业时间已选择
  if (
    !businessConfig.value.dailyHours.start ||
    !businessConfig.value.dailyHours.end
  ) {
    uni.showToast({
      title: "请设置营业时间",
      icon: "none",
    });
    return;
  }
  // 准备符合后端接口的数据结构
  const requestData = {
    weekDays:getWeekDaysString(),
    dailyOpenTime:businessConfig.value.dailyHours.start,
    dailyCloseTime:businessConfig.value.dailyHours.end,
    lunchStartTime:businessConfig.value.breakHours.start,
    lunchEndTime:businessConfig.value.breakHours.end,
    customerInterval:businessConfig.value.interval,
    id:currentId.value
  };
  try {
    await hairBaseConfig1.saveHairbaseconfig(requestData)
    console.log("提交的配置数据:", requestData,requestData.id);
    uni.showToast({ title: defaultConfig?'恢复配置成功':`保存成功`, icon: "none" });
  } catch (error) {
    uni.showToast({ title:defaultConfig?'恢复配置失败，请重试~':"保存失败，请重试~", icon: "none" });
  }finally{
    setTimeout(() => {
      uni.navigateBack();
    }, 500);
  }
};
//   ==============================接口======================
const getHairbaseconfig = () => {
  hairBaseConfig1.getHairbaseconfig().then((res:IHairBaseConfig)=>{
      const data:IHairBaseConfig = res
      currentId.value = res.id
    // 将字符串格式的星期转换为数组
    const daysMap:any = {
        '周一': 0, '周二': 1, '周三': 2, 
        '周四': 3, '周五': 4, '周六': 5, '周日': 6
      };
  const selectedDays = data.weekDays.split(',');
      const weekDays = Array(7).fill(false);
      selectedDays.forEach(day => {
        const index = daysMap[day];
        if (index !== undefined) {
          weekDays[index] = true;
        }
      });
        // 更新业务配置
        businessConfig.value = {
        weekDays,
        dailyHours: {
          start: data.dailyOpenTime,
          end: data.dailyCloseTime
        },
        breakHours: {
          start: data.lunchStartTime,
          end: data.lunchEndTime
        },
        interval: data.customerInterval
      };
      startTime.value = data.dailyOpenTime;
      endTime.value = data.dailyCloseTime;
      breakStartTime.value = data.lunchStartTime;
      breakEndTime.value = data.lunchEndTime;
      intervalTime.value = data.customerInterval;
  }).catch(err => {
    console.error('请求出错:', err); // 捕获可能的错误
});
};
</script>

<style scoped lang="scss">
.config-container {
  width: 100%;
  background: #f2f3f7;
  font-size: 14px;
  overflow: auto;
  height: calc(100% - 78px);
  overflow: auto;
  .config-top {
    height: auto;
    padding: 14px 15px;
    background: #ffffff;
    margin-bottom: 10px;
    .config-top-title {
      color: #333333;
    }
    .config-item {
      margin-top: 10px;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 10px;
    }
    .notice-item {
      display: flex;
      align-items: center;
      gap: 5px;
      cursor: pointer;
    }
    /* 单独控制每列对齐方式 */
    .notice-item:nth-child(3n + 1) {
      justify-content: flex-start;
    }
    .notice-item:nth-child(3n + 2) {
      justify-content: center;
    }
    .notice-item:nth-child(3n + 3) {
      justify-content: flex-end;
    }
  }
  .config-time-default {
    padding: 14px 15px;
    background: #ffffff;
    margin-bottom: 10px;
    .config-time-default-item {
      height: 132px;
      .interval {
        margin-top: 14px;
        display: flex;
        justify-content: space-between;
        .interval-icon {
          display: flex;
          align-items: center;
          .interval-num {
            width: 30px;
            text-align: center;
          }
          .interval-unit {
            color: #666666;
            margin-left: 4px;
          }
        }
      }
    }
    .interval-c {
      height: 78px;
    }
  }
  .deploy-item {
    margin-top: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    position: relative;
    height: 32px;
    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 14px;
      color: #333;
      min-width: 70px;
    }

    .deploy-select {
      width: 220px;
      position: relative;
      height: 32px;
      .select-item{
        height: 32px;
      }
    }

    .error-tip {
      position: absolute;
      right: 0;
      bottom: -18px;
      color: #f56c6c;
      font-size: 12px;
    }
  }
}
.bottom-btn {
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  padding-bottom: 26px;
}
.cancelAll {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: space-between;
  height: 70px;
  box-shadow: 0 0 5px rgba(146, 146, 146, 0.2);
  z-index: 999;
  .cancelAll-btn {
    border-radius: 3px;
    background: #0066df;
    color: #ffffff;
    width: 100%;
  }
}
</style>
