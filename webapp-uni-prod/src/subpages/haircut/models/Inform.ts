/**
 * 美发预约通知信息接口
 * 用于描述发送给客户或理发师的预约通知信息
 */
export interface HairNotification {
  /** 通知记录的唯一标识ID */
  id: string;
  
  /** 系统创建时间 (格式: YYYY-MM-DD HH:mm) */
  sysCreated: string;
  
  /** 关联的预约记录ID */
  reservationId: string;
  
  /** 通知方式，多种方式用逗号分隔 (如: "短信通知,微信推送") */
  noticeMethod: string;
  
  /** 实际通知发送时间 (格式: YYYY/MM/DD HH:mm) */
  noticeTime: string;
  
  /** 用户反馈内容 (如无反馈则为空字符串) */
  feedbackContent: string;
  
  /** 后台管理界面是否显示 (0-不显示 1-显示) */
  adminDisplay: number;
  
  /** 用户端是否显示 (0-不显示 1-显示) */
  userDisplay: number;
  
  /** 反馈时间 (格式: YYYY-MM-DD HH:mm) */
  feedbackTime?: string;
}

/**
 * 美发预约数据接口
 * 描述客户预约理发服务的完整信息
 */
export interface ReservationData {
  /** 预约记录的唯一标识ID */
  id: string;
  
  /** 系统创建时间 (格式: YYYY-MM-DD HH:mm) */
  sysCreated: string;
  
  /** 关联的时间段ID */
  timeslotId: string;
  
  /** 关联的理发师/员工ID */
  personnelId: string;
  
  /** 客户姓名 */
  customerName: string;
  
  /** 客户联系电话 */
  customerPhone: string;
  
  /** 理发师联系电话 */
  baberPhone: string;

  /**理发预约时间*/ 
  appointmentTime:string;
  
  /** 
   * 预约状态 
   * 0-待确认 1-已确认 2-已完成 3-已取消
   * (可根据实际业务扩展)
   */
  status: number;
  
  /** 美发服务开始时间 (格式: YYYY/MM/DD HH:mm) */
  hairStartTime: string;
  
  /** 美发服务结束时间 (格式: YYYY/MM/DD HH:mm) */
  hairEndTime: string;
  
  /** 关联的通知记录数组 */
  hairnotification: HairNotification[];
}