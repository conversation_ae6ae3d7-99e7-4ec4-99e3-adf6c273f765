/**
 * 理发店基础配置接口类型
 */
export interface IHairBaseConfig {
  /** 配置ID */
  id?: string;
  /** 系统创建时间 */
  sysCreated?: string;
  /** 营业日，格式："周一,周三,周五" */
  weekDays: string;
  /** 每日开始营业时间，格式："08:30" */
  dailyOpenTime: string;
  /** 每日结束营业时间，格式："17:00" */
  dailyCloseTime: string;
  /** 午休开始时间，格式："11:30" */
  lunchStartTime: string;
  /** 午休结束时间，格式："13:00" */
  lunchEndTime: string;
  /** 顾客间隔时间（分钟） */
  customerInterval: number;
}

/**
 * 模拟的后端接口数据
 */
export const mockHairConfig: IHairBaseConfig = {
  weekDays: "周一,周三,周五",
  dailyOpenTime: "08:30",
  dailyCloseTime: "17:00",
  lunchStartTime: "11:30",
  lunchEndTime: "13:00",
  customerInterval: 30,
};
export const businessConfigMock = {
  weekDays: [true, false, true, false, true, false, false],
  dailyHours: {
    start: mockHairConfig.dailyOpenTime,
    end: mockHairConfig.dailyCloseTime,
  },
  breakHours: {
    start: mockHairConfig.lunchStartTime,
    end: mockHairConfig.lunchEndTime,
  },
  interval: mockHairConfig.customerInterval,
};
/**
 * 请求参数类型
 */
export interface IHairConfigParams {
  weekDays: string;
  dailyOpenTime: string;
  dailyCloseTime: string;
  lunchStartTime: string;
  lunchEndTime: string;
  customerInterval: number;
  id: string;
}
export interface Root {
  rlt: number;
  info: string;
  datas: Datas;
}
/** 理发预约列表*/
export interface HaircutAppointment {
  id: string;
  personnelId: string;
  timeslotId: string;
  customerName: string;
  customerPhone: string;
  baberPhone: string;
  appointmentTime?: string;
  hairStartTime: string; // 示例："2025/06/26 09:30"
  hairEndTime: string; // 示例："2025/06/26 10:00"
  status: number; // 示例：0   2表示预约过期
  sysCreated: string; // 示例："2025-06-23 09:39"
}
// 预约总列表
export interface Datas {
  id?: string;
  sysCreated?: string;
  businessDate?: string;
  openTime?: string;
  closeTime?: string;
  lunchStartTime?: string;
  lunchEndTime?: string;
  intervalMinutes?: number;
  issue?: number;
  ifopen?: number;
  specialTimeList?: SpecialTimeList[];
  hairtimeslot?: Hairtimeslot[];
  weekDay?: string;
  totalCustomer?: number; //总人数
  readyCustomer?: number; //已预约人数
}
/** 特殊时间列表"*/
export interface SpecialTimeList {
  id?: string;
  sysCreated?: string;
  configId?: string;
  specialStartTime?: string;
  specialEndTime?: string;
}
export type TimeSlotState = 0 | 1 | 2 | 3 | 4; // 0=可预约, 1=已约满, 2=不可约, 3=未预约, 4=用户名

/**发型预约时段*/
export interface Hairtimeslot {
  id: string;
  sysCreated: string;
  configId: string;
  startTime: string;
  Status: number;
  endTime: string;
  status: TimeSlotState | any;
  time?: string;
  hairreservation?: Hairreservation;
}
export interface Hairreservation {
  id: string;
  sysCreated: string;
  timeslotId: string;
  personnelId: string;
  customerName: string;
  customerPhone: string;
  baberPhone: string;
  status: number;
  hairStartTime: string;
  hairEndTime: string;
  hairnotificationList: HairnotificationList[];
}

export interface HairnotificationList {
  id: string;
  sysCreated: string;
  reservationId: string;
  noticeMethod: string;
  noticeTime: string;
  feedbackContent: string;
  adminDisplay: number;
  userDisplay: number;
}

/**假期列表*/
export interface HolidaycalendarList {
  id: string;
  date: string;
  officialType: number;
  manualType: number;
  officialName: string;
  type: number;
  dayOrHolidayName: string;
}
