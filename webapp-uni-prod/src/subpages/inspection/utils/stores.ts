import { defineStore } from 'pinia';
import { Station } from '../models/inspection';

export const useInspectionStore = defineStore('inspection', {
    state: () => ({
        //站点
        siteName: '',
        siteId: '',
        //班组
        inspectionTeamName: '',
        inspectionTeamId: '',
        //巡检记录数据 使用Station类型-巡检详情需要
        inspectionRecord: {} as Station,
        //存储巡检记录数据-打卡
        inspectionRecordList: [] as Station[],
    }),
})