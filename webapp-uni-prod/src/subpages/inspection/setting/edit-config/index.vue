<template>
  <view class="container">
    <view class="card basic-info">
      <view class="info-item">
        <view class="info-label">任务名称</view>
        <view class="info-value">
          <input
            placeholder="请输入任务名称"
            placeholderStyle="color: #999999"
            :border="'none'"
            type="text"
            :maxlength="10"
            v-model="configData.taskName"
            class="item-input"
          />
        </view>
      </view>
      <view class="info-item">
        <view class="info-label">班组</view>
        <view class="info-value select" @click="slectTeam()">
          <view
            :class="[
              'select-text',
              store.inspectionTeamName ? 'selected-text' : '',
            ]"
            >{{
              store.inspectionTeamName ? store.inspectionTeamName : "请选择班组"
            }}</view
          >
          <view>
            <SlSubSvgIcon subpage="inspection" name="14-14-7" size="10" />
          </view>
        </view>
      </view>
      <view class="info-item">
        <view class="info-label">站点</view>
        <view class="info-value select" @click="slectState()">
          <view
            :class="['select-text', store.siteName ? 'selected-text' : '']"
            >{{ store.siteName ? store.siteName : "请选择站点" }}</view
          >
          <view>
            <SlSubSvgIcon subpage="inspection" name="14-14-7" size="10" />
          </view>
        </view>
      </view>
      <view class="remark">
        <view class="label">备注</view>
        <view>
          <SlTextarea
            class="textarea"
            v-model="configData.remarks"
            placeholder="请输入"
            :maxlength="20"
            :showWordLimit="true"
          />
        </view>
      </view>
    </view>
    <view class="card work">
      <view class="work-title"> 每周打卡日期(默认) </view>
      <view class="work-time">
        <up-checkbox-group
          class="check-group"
          v-model="checkboxValue1"
          placement="row"
          @change="checkboxChange"
        >
          <up-checkbox
            v-for="(item, index) in checkboxList1"
            :key="index"
            :label="item.name"
            :name="item.id"
          >
          </up-checkbox>
        </up-checkbox-group>
      </view>
    </view>
    <view class="card clock">
      <view class="clock-title">打卡时间</view>
      <view class="clock-startTime">
        <view>开始时间</view>
        <view class="time">
          <TimeSelect v-model:modelValue="configData.startTime"></TimeSelect>
        </view>
      </view>
      <view class="clock-endTime">
        <view>结束时间</view>

        <view class="time">
          <TimeSelect
            v-model:modelValue="configData.endTime"
            :showNextDay="
              configData.endTime <= configData.startTime ? true : false
            "
          ></TimeSelect>
        </view>
      </view>
    </view>
    <view class="card limit">
      <view class="limit-times">
        <view>打卡次数</view>
        <!-- 打卡次数为0 -->
        <view class="limit-times-value">
          <SlSubSvgIcon
            subpage="inspection"
            name="20-20-24"
            size="20"
            @click="configData.inspectionFrequency -= 1"
            v-if="configData.inspectionFrequency != 0"
          />
          <view v-if="configData.inspectionFrequency != 0" class="count">{{
            configData.inspectionFrequency
          }}</view>
          <SlSubSvgIcon
            subpage="inspection"
            name="20-20-25"
            size="20"
            @click="configData.inspectionFrequency += 1"
          />
          <view class="unit unit-times">次</view>
        </view>
      </view>
      <view class="limit-interval">
        <view>每次至少间隔</view>
        <!-- 间隔为0 -->
        <view class="limit-times-value">
          <SlSubSvgIcon
            subpage="inspection"
            name="20-20-24"
            size="20"
            @click="configData.inspectionInterval -= 1"
            v-if="configData.inspectionInterval != 0"
          />
          <view v-if="configData.inspectionInterval != 0" class="count">{{
            configData.inspectionInterval
          }}</view>
          <SlSubSvgIcon
            subpage="inspection"
            name="20-20-25"
            size="20"
            @click="configData.inspectionInterval += 1"
          />
          <view class="unit">小时</view>
        </view>
      </view>
    </view>
    <view class="btn">
      <BaseButton
        btnType="delete"
        style="margin-right: 10px"
        @click="onDel()"
        v-if="isEdit"
        >删除</BaseButton
      >
      <BaseButton
        btnType="cancel"
        style="margin-right: 10px"
        @click="onBack()"
        v-else
        >取消</BaseButton
      >
      <BaseButton btnType="save" @click="onSave()">完成</BaseButton>
    </view>
  </view>
  <!-- 删除确认对话框 -->
  <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="del">
    您确定要删除此配置吗?
  </ConfirmDialog>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import TimeSelect from "@/subpages/inspection/components/time-select.vue";
import inspectionService from "@/subpages/inspection/api/inspection.service";
import { ConfigData } from "@/subpages/inspection/models/inspection";
import { useInspectionStore } from "@/subpages/inspection/utils/stores";

const store = useInspectionStore();
//选中的复选框值
const checkboxValue1 = ref<number[]>([1, 2, 3, 4, 5]);
const isEdit = ref<boolean>(false);
const showConfirm = ref<boolean>(false);
const delId = ref<string>("");
const configData = ref<ConfigData>({
  remarks: "",
  taskName: "",
  siteName: "",
  siteId: "",
  startTime: "",
  endTime: "",
  inspectionFrequency: 0,
  inspectionInterval: 0,
  inspectionTeamName: "",
  inspectionTeamId: "",
  orgId: "",
  inspectionDay: "",
});
//复选框的值
const checkboxList1 = reactive([
  {
    id: 1,
    name: "周一",
  },
  {
    id: 2,
    name: "周二",
  },
  {
    id: 3,
    name: "周三",
  },
  {
    id: 4,
    name: "周四",
  },
  {
    id: 5,
    name: "周五",
  },
  {
    id: 6,
    name: "周六",
  },
  {
    id: 7,
    name: "周日",
  },
]);
const configValidateRule = {
  taskName: { required: true, message: "请输入任务名称", type: "string" },
  inspectionTeamName: { required: true, message: "请选择班组", type: "string" },
  siteName: { required: true, message: "请选择站点", type: "string" },
  inspectionDay: {
    required: true,
    message: "请选择每周打卡日期",
    type: "string",
  },
  startTime: { required: true, message: "请选择开始时间", type: "string" },
  endTime: { required: true, message: "请选择结束时间", type: "string" },
  inspectionFrequency: {
    required: true,
    message: "请设置打卡次数",
    type: "number",
  },
  inspectionInterval: {
    required: true,
    message: "请设置每次至少间隔时间",
    type: "number",
  },
};
onLoad((options: AnyObject | undefined) => {
  if (options?.id) {
    getDetail(options.id);
    //编辑
    uni.setNavigationBarTitle({
      title: "编辑配置",
    });
    isEdit.value = true;
    delId.value = options.id;
  } else {
    //新增
    uni.setNavigationBarTitle({
      title: "新增配置",
    });
    isEdit.value = false;
    //将store里面的站点和班组数据清空
    store.siteName = "";
    store.siteId = "";
    store.inspectionTeamId = "";
    store.inspectionTeamName = "";
  }
  //站点
  if (options?.siteName) {
    store.siteName = options.siteName;
  }
  //站点id
  if (options?.siteId) {
    store.siteId = options.siteId;
  }
});
const getDetail = async (id: string) => {
  //获取详情
  const res = await inspectionService.getSiteConfigDetail(id);
  configData.value = res as ConfigData;
  //存储站点
  store.siteName = res.siteName;
  store.siteId = res.siteId;
  //存储班组
  store.inspectionTeamId = res.inspectionTeamId;
  store.inspectionTeamName = res.inspectionTeamName;
  //将inspectionDay字符串中的值转换为number类型
  let inspectionDay = res.inspectionDay.split(",").map(Number);
  checkboxValue1.value = [...inspectionDay];
};
//复选框点击改变事件
const checkboxChange = (n: number[]) => {
  checkboxValue1.value = n;
};

const slectTeam = () => {
  //选择班组
  uni.navigateTo({
    url: `/subpages/inspection/setting/select-team/index?teamId=${store.inspectionTeamId}`,
  });
};
const slectState = () => {
  //选择站点
  uni.navigateTo({
    url: `/subpages/inspection/setting/select-state/index?siteId=${store.siteId}`,
  });
};
const onBack = () => {
  uni.navigateBack();
};
const onDel = () => {
  showConfirm.value = true;
};
const del = async () => {
  await inspectionService.deleteSiteConfig(delId.value);
  showConfirm.value = false;
  uni.navigateBack();
};
const onSave = async () => {
  configData.value.siteId = store.siteId;
  configData.value.siteName = store.siteName;
  configData.value.inspectionTeamId = store.inspectionTeamId;
  configData.value.inspectionTeamName = store.inspectionTeamName;
  configData.value.inspectionDay = checkboxValue1.value.join(",");
  const errors = validate();
  if (errors.length > 0) {
    uni.showToast({
      title: errors[0],
      icon: "none",
    });
    return;
  }
  await inspectionService.addSiteConfig(configData.value);
  uni.navigateBack();
};
const validate = () => {
  const errors: string[] = [];
  for (const key in configValidateRule) {
    const rule = configValidateRule[key as keyof typeof configValidateRule];
    const value = configData.value[key as keyof ConfigData];
    if (rule.required) {
      if (rule.type == "string") {
        if (!value || (value as string).trim() === "") {
          errors.push(rule.message);
        }
      } else if (rule.type == "number") {
        if (
          value === undefined ||
          value === null ||
          isNaN(value as number) ||
          (value as number) <= 0
        ) {
          errors.push(rule.message);
        }
      }
    }
  }
  return errors;
};
</script>
<style lang="scss" scoped>
/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  // height: 100%;
  width: 100%;
  background: #f6f8fa;
  color: #333333;
  font-size: 14px;
}

//卡片样式
.card {
  padding: 15px;
  background: #ffffff;
  box-shadow: 0px 0px 6px 0px rgba(146, 146, 146, 0.2);
  margin-bottom: 15px;
}

.basic-info {
  .info-item {
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    margin-bottom: 10px;

    .info-label {
      min-width: 65px;
      color: #666666;
    }

    .info-value {
      font-size: 14px;
    }

    .item-input {
      font-size: 14px;
      height: 32px;
      line-height: 32px;
      background-color: #f8f8f8;
      text-align: right;
      padding: 0 8px;
    }

    .select {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;
      font-size: 14px;
      color: #999999;

      .select-text {
        margin-right: 10px;
      }

      .selected-text {
        color: #333333;
        //超过一行，省略号
        width: 170px;
        text-align: right;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .remark {
    .label {
      margin: 10px 0;
      color: #666666;
    }

    .textarea {
      width: 100%;
      height: 56px;
      font-size: 14px;
      color: #333;
    }
  }
}

.work {
  .work-title {
    font-size: 14px;
    color: #333333;
    margin-bottom: 10px;
  }

  .work-time {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    /* 允许子元素换行 */
    justify-content: space-between;
    /* 两端对齐 */
    .check-group {
      display: flex;
      flex-wrap: wrap;
      gap: 8px 25%;
    }
  }
}

.clock {
  .clock-title {
  }

  .clock-startTime {
    margin: 10px 0;
  }

  .clock-startTime,
  .clock-endTime {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .time {
      flex: 1;
      position: relative;
      color: #999999;
      margin-left: 10px;
    }
  }
}

.limit {
  .limit-interval {
    margin-top: 10px;
  }

  .limit-times,
  .limit-interval {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .limit-times-value {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;

      .count {
        width: 14px;
        text-align: center;
        color: #333333;
      }

      .unit {
        color: #666666;
      }
      .unit-times {
        margin-right: 14px;
      }
    }
  }
}

.btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin: 27px 0;
  box-sizing: content-box; //底部留一点空隙
}

.select {
  color: #666;
}
</style>
