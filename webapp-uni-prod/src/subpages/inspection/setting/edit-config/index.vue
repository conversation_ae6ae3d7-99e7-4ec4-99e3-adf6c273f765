<template>
  <view class="container">
    <view class="card basic-info">
      <view class="info-item">
        <view class="info-label">任务名称</view>
        <view class="info-value">
          <up-input
            :placeholderClass="'placeholder'"
            placeholder="请输入任务名称"
            border="none"
            inputAlign="right"
            minlength="10"
            v-model="configData.taskName"
          ></up-input>
        </view>
      </view>
      <view class="info-item">
        <view class="info-label">班组</view>
        <view class="info-value select" @click="slectTeam()">
          <view class="select-text">{{
            configData.inspectionTeamName
              ? configData.inspectionTeamName
              : "请选择班组"
          }}</view>
          <view>
            <SlSubSvgIcon subpage="inspection" name="14-14-7" size="10" />
          </view>
        </view>
      </view>
      <view class="info-item">
        <view class="info-label">站点</view>
        <view class="info-value select" @click="slectState()">
          <view class="select-text">{{
            store.siteName ? store.siteName : "请选择站点"
          }}</view>
          <view>
            <SlSubSvgIcon subpage="inspection" name="14-14-7" size="10" />
          </view>
        </view>
      </view>
      <view class="remark">
        <view class="label">备注</view>
        <view>
          <up-textarea
            placeholder="请输入"
            maxlength="20"
            placeholderStyle="font-size: 14px"
            v-model="configData.remarks"
          ></up-textarea>
        </view>
      </view>
    </view>
    <view class="card work">
      <view class="work-title"> 每周打卡日期(默认) </view>
      <view class="work-time">
        <up-checkbox-group
          v-model="checkboxValue1"
          placement="row"
          @change="checkboxChange"
        >
          <up-checkbox
            :customStyle="{
              marginBottom: '8px',
              width: '33.3%',
            }"
            v-for="(item, index) in checkboxList1"
            :key="index"
            :label="item.name"
            :name="item.id"
          >
          </up-checkbox>
        </up-checkbox-group>
      </view>
    </view>
    <view class="card clock">
      <view class="clock-title">打卡时间</view>
      <view class="clock-startTime">
        <view>开始时间</view>
        <view class="time">
          <view class="time-select" @click="openStartTime()">
            <view :class="{ select: configData.startTime }">{{
              configData.startTime ? configData.startTime : "请选择"
            }}</view>
            <view>
              <SlSubSvgIcon subpage="inspection" name="12-12-7" size="12" />
            </view>
          </view>

          <!-- 时间选择器 -->
          <view class="time-picker" v-if="startTimeShow">
            <TimeSelect
              @close="onClose($event)"
              @confirm.stop="onConfirm($event)"
            ></TimeSelect>
          </view>
        </view>
      </view>
      <view class="clock-endTime">
        <view>结束时间</view>

        <view class="time">
          <view class="time-select" @click="openEndTime()">
            <view :class="{ select: configData.endTime }">
              <!-- {{ endTime <= startTime ? "请选择" : endTime }} -->
              {{
                configData.endTime
                  ? configData.endTime <= configData.startTime
                    ? "次日" + configData.endTime
                    : configData.endTime
                  : "请选择"
              }}
              <!-- {{ endTime ? endTime : "请选择" }} -->
            </view>
            <view>
              <SlSubSvgIcon subpage="inspection" name="12-12-7" size="12" />
            </view>
          </view>
          <!-- 时间选择器 -->
          <view class="time-picker" v-if="endTimeShow">
            <TimeSelect
              @close="onCloseEnd($event)"
              @confirm="onConfirmEnd($event)"
            ></TimeSelect>
          </view>
        </view>
      </view>
    </view>
    <view class="card limit">
      <view class="limit-times">
        <view>打卡次数</view>
        <!-- 打卡次数为0 -->
        <view class="limit-times-value">
          <SlSubSvgIcon
            subpage="inspection"
            name="20-20-24"
            size="20"
            @click="configData.inspectionFrequency--"
            v-if="configData.inspectionFrequency != 0"
          />
          <view v-if="configData.inspectionFrequency != 0" class="count">{{
            configData.inspectionFrequency
          }}</view>
          <SlSubSvgIcon
            subpage="inspection"
            name="20-20-25"
            size="20"
            @click="configData.inspectionFrequency++"
          />
          <view class="unit">次<text>次</text></view>
        </view>
      </view>
      <view class="limit-interval">
        <view>每天至少间隔</view>
        <!-- 间隔为0 -->
        <view class="limit-times-value">
          <SlSubSvgIcon
            subpage="inspection"
            name="20-20-24"
            size="20"
            @click="configData.inspectionInterval--"
            v-if="configData.inspectionInterval != 0"
          />
          <view v-if="configData.inspectionInterval != 0" class="count">{{
            configData.inspectionInterval
          }}</view>
          <SlSubSvgIcon
            subpage="inspection"
            name="20-20-25"
            size="20"
            @click="configData.inspectionInterval++"
          />
          <view class="unit">小时</view>
        </view>
      </view>
    </view>
    <view class="btn">
      <BaseButton btnType="cancel" style="margin-right: 10px" @click="onBack()"
        >取消</BaseButton
      >
      <BaseButton btnType="save" @click="onSave()">完成</BaseButton>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import TimeSelect from "@/subpages/inspection/components/time-select.vue";
import inspectionService from "@/service/inspection";
import { ConfigData } from "@/subpages/inspection/models/inspection";
import { useInspectionStore } from "@/subpages/inspection/utils/stores";

const store = useInspectionStore();
//选中的复选框值
const checkboxValue1 = ref<number[]>([1, 2, 3, 4, 5]);
//打卡开始时间
const startTimeShow = ref<boolean>(false);
//打卡结束时间
const endTimeShow = ref<boolean>(false);
let configData = reactive({
  remarks: "",
  taskName: "",
  siteName: "",
  siteId: "",
  startTime: "",
  endTime: "",
  inspectionFrequency: 0,
  inspectionInterval: 0,
  inspectionTeamName: "",
  inspectionTeamId: "",
} as ConfigData);
//复选框的值
const checkboxList1 = reactive([
  {
    id: 1,
    name: "周一",
  },
  {
    id: 2,
    name: "周二",
  },
  {
    id: 3,
    name: "周三",
  },
  {
    id: 4,
    name: "周四",
  },
  {
    id: 5,
    name: "周五",
  },
  {
    id: 6,
    name: "周六",
  },
  {
    id: 7,
    name: "周日",
  },
]);
onLoad((options: AnyObject | undefined) => {
  console.log(options, "获取opyions");
  if (options?.id) {
    getDetail(options.id);
    //编辑
    uni.setNavigationBarTitle({
      title: "编辑配置",
    });
  } else {
    //新增
    uni.setNavigationBarTitle({
      title: "新增配置",
    });
    //将store里面的站点和班组数据清空
    store.siteName = "";
    store.siteId = "";
    store.inspectionTeamId = "";
    store.inspectionTeamName = "";
  }
  //站点
  if (options?.siteName) {
    store.siteName = options.siteName;
  }
  //站点id
  if (options?.siteId) {
    store.siteId = options.siteId;
  }
});
const getDetail = async (id: string) => {
  //获取详情
  const res = await inspectionService.getSiteConfigDetail(id);
  configData = res as ConfigData;
  store.siteName = res.siteName;
  store.siteId = res.siteId;
  //将inspectionDay字符串中的值转换为number类型
  let inspectionDay = res.inspectionDay.split(",").map(Number);
  checkboxValue1.value = [...inspectionDay];
};
//复选框点击改变事件
const checkboxChange = (n: number[]) => {
  checkboxValue1.value = n;
};

const slectTeam = () => {
  //选择班组
  uni.navigateTo({
    url: "/subpages/inspection/setting/select-team/index",
  });
};
const slectState = () => {
  //选择站点
  uni.navigateTo({
    url: `/subpages/inspection/setting/select-state/index?siteId=${store.siteId}`,
  });
};
const openStartTime = () => {
  endTimeShow.value = false;
  startTimeShow.value = true;
};
const openEndTime = () => {
  startTimeShow.value = false;
  endTimeShow.value = true;
};
const onClose = (value: boolean) => {
  startTimeShow.value = value;
};
const onCloseEnd = (value: boolean) => {
  endTimeShow.value = value;
};
const onConfirmEnd = (value: string) => {
  configData.endTime = value;
  endTimeShow.value = false;
};
const onConfirm = (value: string) => {
  configData.startTime = value;
  startTimeShow.value = false;
};
const onBack = () => {
  uni.navigateBack();
};
const onSave = async () => {
  if (configData.taskName == "") {
    uni.showToast({
      title: "请输入任务名称",
      icon: "none",
    });
    return;
  }
  if (configData.startTime == "") {
    uni.showToast({
      title: "请选择开始时间",
      icon: "none",
    });
    return;
  }
  if (configData.endTime == "") {
    uni.showToast({
      title: "请选择结束时间",
      icon: "none",
    });
    return;
  }
  if (configData.siteName == "") {
    uni.showToast({
      title: "请选择站点",
      icon: "none",
    });
    return;
  }

  if (configData.startTime == configData.endTime) {
    uni.showToast({
      title: "开始时间和结束时间不能相同",
      icon: "none",
    });
    return;
  }

  await inspectionService.addSiteConfig(configData);
  uni.navigateBack();
};
</script>
<style lang="scss" scoped>
/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  // height: 100%;
  width: 100%;
  background: #f6f8fa;
  color: #333333;
  font-size: 14px;
}
//卡片样式
.card {
  padding: 15px;
  background: #ffffff;
  box-shadow: 0px 0px 6px 0px rgba(146, 146, 146, 0.2);
  margin-bottom: 15px;
}
.basic-info {
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    margin-bottom: 5px;
    .info-label {
      color: #666666;
    }
    .info-value {
      font-size: 14px;
    }
    .select {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;
      font-size: 14px;
      color: #999999;
      height: 32px;
      .select-text {
        margin-right: 10px;
      }
    }
  }
  .remark {
    .label {
      margin: 10px 0;
      color: #666666;
    }
  }
}
.work {
  .work-title {
    font-size: 14px;
    color: #333333;
    margin-bottom: 10px;
  }
  .work-time {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap; /* 允许子元素换行 */
    justify-content: space-between; /* 两端对齐 */
  }
}
.clock {
  .clock-title {
  }
  .clock-startTime {
    margin: 10px 0;
  }
  .clock-startTime,
  .clock-endTime {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .time {
      flex: 1;
      position: relative;
      color: #999999;
      margin-left: 10px;
      .time-select {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: 0 10px;
        border: 1px solid #cacaca;
        height: 32px;
        border-radius: 3px;
      }
      .time-picker {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        text-align: center;
        align-items: center;
        position: absolute;
        height: 200px;
        width: 100%;
        z-index: 2;
        top: 32px;
        left: 0;
        background: #ffffff;
        box-shadow: 0px 0px 6px 0px rgba(146, 146, 146, 0.2);
        border-radius: 15px;
      }
    }
  }
}
.limit {
  .limit-interval {
    margin-top: 10px;
  }
  .limit-times,
  .limit-interval {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .limit-times-value {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;
      .count {
        text-align: center;
        color: #333333;
      }
      .unit {
        color: #666666;
      }
    }
  }
}
.btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin: 27px 0;
  box-sizing: content-box; //底部留一点空隙
}
.select {
  color: #666;
}
//修改up-input的样式
::v-deep .up-input {
  background-color: #f8f8f8;
  padding: 6px 8px;
  width: 200px;
}
::v-deep .placeholder {
  color: #999999;
  font-size: 14px;
}
</style>
