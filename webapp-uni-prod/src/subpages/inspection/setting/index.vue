<template>
  <SlTabbarPage :tab-bar-root="'subpages/inspection'">
    <view class="container">
      <view class="card">
        <view class="card-item" v-for="item in configData" :key="item.id">
          <view class="item-title">
            <view class="title-text">{{ item.taskName }}任务</view>
            <view>
              <SlSubSvgIcon
                subpage="inspection"
                name="22-22-40"
                size="16"
                style="margin-right: 15px"
                @click="onEdit(item.id)"
              />
              <SlSubSvgIcon
                subpage="inspection"
                name="16-16-16"
                size="16"
                @click="onDel(item.id)"
              />
            </view>
          </view>
          <view class="item-content">
            <view class="item-content-item">
              <view class="item-label">班组</view>
              <view class="item.value">{{ item.inspectionTeamName }}</view>
            </view>
            <view class="item-content-item">
              <view class="item-label">打卡日期</view>
              <view class="item.value">{{ item.inspectionDay }}</view>
            </view>
            <view class="item-content-item">
              <view class="item-label">时间</view>
              <view class="item.value"
                >{{ item.startTime }} - {{ item.endTime }}</view
              >
            </view>
            <view class="item-content-item">
              <view class="item-label">每天打卡次数</view>
              <view class="item.value">{{ item.inspectionFrequency }}次</view>
            </view>
            <view class="item-content-item">
              <view class="item-label">每次至少间隔</view>
              <view class="item.value"> {{ item.inspectionInterval }}小时</view>
            </view>
            <view class="item-content-item">
              <view class="item-label">备注</view>
              <view class="item.value">
                {{ item.remarks ? item.remarks : "-- " }}
              </view>
            </view>
          </view>
          <view class="item-footer">
            <view class="footer-text" @click="onViewSite()">查看站点</view>
            <view>
              <SlSubSvgIcon subpage="inspection" name="14-14-7" size="10" />
            </view>
          </view>
        </view>
      </view>
      <view class="empty">
        <BaseEmpty v-if="!configData.length" />
      </view>

      <!-- 底部Tabbar -->
      <!-- <Tabbar :current="currentTab" @change="handleTabChange" /> -->
    </view>

    <AddBtn @add="add()" />
    <ConfirmDialog
      v-if="showConfirm"
      @close="showConfirm = false"
      @confirm="del"
      >您确定要删除该配置吗?</ConfirmDialog
    >
  </SlTabbarPage>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import AddBtn from "@/subpages/inspection/components/add-btn.vue";
import inspectionService from "@/service/inspection";
import { Params, Config } from "@/subpages/inspection/models/inspection";
const showConfirm = ref<Boolean>(false);
const configData = ref<Config[]>([]);
const delId = ref<string>("");
const params = reactive<Params>({
  ifPage: true,
  currentPage: 1,
  pageRecord: 10,
});

onLoad(() => {
  getData();
});
const getData = async () => {
  const res = await inspectionService.getSiteConfigList(params);
  if (res.result) {
    configData.value = res.result;
    //将inspectionFrequency的1,2,3,4,5,6,7转换为一|二|三|四|五|六|日
    configData.value.forEach((item) => {
      item.inspectionDay = item.inspectionDay
        .split(",")
        .map(
          (item) =>
            ["一", "二", "三", "四", "五", "六", "日"][parseInt(item) - 1]
        )
        .join(" | ");
      //如果是1,2,3,4,5,6,7则显示为每天
      if (item.inspectionDay == "一 | 二 | 三 | 四 | 五 | 六 | 日") {
        item.inspectionDay = "每天";
      }
    });
  }
};
const onEdit = (id: string) => {
  uni.navigateTo({
    url: `/subpages/inspection/setting/edit-config/index?id=${id}`,
  });
};
const onViewSite = () => {
  uni.navigateTo({
    url: "/subpages/inspection/setting/look-state/index",
  });
};
const add = () => {
  uni.navigateTo({
    url: "/subpages/inspection/setting/edit-config/index",
  });
};
const onDel = (id: string) => {
  showConfirm.value = true;
  delId.value = id;
};
const del = async () => {
  await inspectionService.deleteSiteConfig(delId.value);
  showConfirm.value = false;
};
</script>
<style lang="scss" scoped>
/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: #f6f8fa;
}
.card {
  .card-item {
    margin: 15px;
    background: #fff;
    border-radius: 10px;
    padding: 15px 15px 0 15px;
    box-shadow: 0px 0px 10px 0px rgba(146, 146, 146, 0.3);
    .item-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title-text {
        font-size: 14px;
        font-weight: 700;
      }
    }
    .item-content {
      font-size: 12px;
      margin: 8px 0;
      .item-content-item {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        .item-label {
          color: #666666;
          width: 112px;
        }
        .item.value {
          flex: 1;
        }
      }
    }
    .item-footer {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;
      font-size: 12px;
      color: #999999;
      height: 40px;
      width: 100%;
      border-top: 1px solid rgba(153, 153, 153, 0.2);
      .footer-text {
        margin-right: 10px;
      }
    }
  }
}
.empty {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
