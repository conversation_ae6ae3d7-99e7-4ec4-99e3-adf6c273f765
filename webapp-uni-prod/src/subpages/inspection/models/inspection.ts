//加载数据
export type LoadStatus = 'loading' | 'loadmore' | 'nomore';
// 楼层数据
export interface Floor {
    // 楼层名称
    floorName: string;
    // 楼层id
    id: string;
    // 创建时间
    sysCreated: string;
}
// 站点数据
export interface StationData {
    // 楼层名称
    floorName: string;
    // 备注
    remarks: string;
    // 站点名称
    siteName: string;
    // 站点编号
    siteNumber: string;
    // 站点位置
    sitePosition: string;
}
export interface Station extends StationData {
    checkInProgress: string;
    conten?: StationDetail;
    id: string;
    inspectionResult: string;
    siteConfigId: string;
    siteStatus: string;
    statusChangeMan: string;
    statusChangeTime: string;
    sysCreated: string;
    inspectionrecordList?: InspectionDetail[];
}
//添加配置
export interface ConfigData {
    //任务名称
    taskName: string;
    //巡检团队id
    inspectionTeamId: string;
    //巡检团队名称
    inspectionTeamName: string;
    //站点id
    siteId: string;
    //站点名称
    siteName: string;
    //备注
    remarks: string;
    //巡检日期
    inspectionDay: string;
    //开始时间
    startTime: string;
    //结束时间
    endTime: string;
    //巡检频率
    inspectionFrequency: number;
    //巡检间隔
    inspectionInterval: number;
}
//配置数据
export interface Config extends ConfigData {
    //id
    id: string;
    //创建时间
    sysCreated: string;
}
//站点详情
export interface InspectionDetail {
    id: string;
    inspectionMan: string;
    inspectionResult: string;
    inspectionTime: string;
    remarks: string;
    siteConfigId: string;
    siteId: string;
    siteName: string;
    siteStatus: string;
    sysCreated: string;
    contentList?: StationDetail[];
}
// 工作组
export interface WorkGroup {
    id: string;
    // 工作组名称
    name: string;
    // 排序号
    sortOrder: number;
    // 工作组成员列表
    personnelList: WorkGroupPersonnel[];
}
export interface WorkGroupPersonnel {
    id: string;
    // 姓名首字母
    nameInitials: string;
    // 姓名
    name: string;
}
//上传的文件图片
export interface StationDetail {
    // 文件存储的相对路径（按日期归档）
    filePath: string;
    // 文件大小（字节）
    fileSize: string;
    // 文件类型（扩展名）
    fileType: string;
    //文件原始名称（不含扩展名）
    fullName: string;
    // id
    id: string;
    // 关联的会议ID
    meetingId: string;
    // 文件在服务器上的存储名称（包含扩展名）
    storeName: string;
    repairrecordId?: string;
    sysCreated?: string;
    inspectionrecordId?: string;
    siteId?: string;
}

//请求参数
export interface Params {
    ifPage: boolean;
    currentPage?: number;
    pageRecord?: number;
}
//站点列表参数
export interface StationParams extends Params {
    floorName: string;
}
//巡检记录列表获取参数
export interface InspectionParams {
    //巡检日期
    queryDate: string;
    //职工-workers or 管理员-administrator
    loginUserRole: string;
    // 管理员需传入 白班巡检-white, 夜班巡检-night, 物业巡检-property, 保洁清洁-clean
    inspectionTeamType: string;
}
//添加站点参数
export interface AddStationParams extends StationData {
    content: StationDetail;
}
//新增站点区域参数
export interface AddStationAreaParams {
    // 站点名称
    floorName: string;
}
//巡检打卡请求参数
export interface InspectionClockParams {
    // 站点id
    siteId: string;
    // 关联配置Id
    siteConfigId: string;
    // 巡检结果 成功-success、异常-error
    inspectionResult?: string;
    // 巡检备注
    remarks?: string;
    // 二维码是否异常
    siteStatus?: string;
    //点位名称
    siteName?: string;
}