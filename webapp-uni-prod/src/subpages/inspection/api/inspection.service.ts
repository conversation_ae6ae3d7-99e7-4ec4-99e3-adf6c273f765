import { get, post } from '@/service/http';
import { Page } from '@/models/Page';
import { Params, Floor, InspectionParams, StationParams, Station, AddStationParams, AddStationAreaParams, Config, ConfigData, InspectionDetail, InspectionClockParams, WorkGroup } from '@/subpages/inspection/models/inspection';
//floor 获取楼层站点区域列表
export const getSiteAreaList = (params: Params) => {
    return post<Floor>('/Floor/getList', {
        data: params
    });
}
// floor 新增站点区域
export const addSiteArea = (params: AddStationAreaParams) => {
    return post<null>('/Floor/save', {
        data: params,
        custom: { successTip: '添加成功', errorTip: '添加失败，请重试~' }
    })
}
// 获取巡检记录列表详情
export const getRecordList = (params: InspectionParams) => {
    return post<any>('/Inspectionrecord/getInspectionrecord', {
        data: params
    });
}
//获取站点列表
export const getSiteList = (params: StationParams) => {
    return post<Page<Station>>('/Site/getList', {
        data: params
    });
}
//删除站点
export const deleteSite = (id: string) => {
    return get<null>(`/Site/delete/${id}`, {
        custom: { successTip: '删除成功', errorTip: '删除失败，请重试~' }
    });
}
//新增站点
export const addSite = (params: AddStationParams) => {
    return post<Page<Config>>('/Site/save', {
        data: params,
        custom: { successTip: '保存成功' }
    });
}
//获取站点详情
export const getSiteDetail = (id: string) => {
    return get<Station>(`/Site/getInfo/${id}`);
}
//获取站点列表
export const getAllFloorName = () => {
    return get<any>('/Site/getAllByFloorName');
}

//sitconfig 获取站点配置列表
export const getSiteConfigList = (params: Params) => {
    return post<Page<Config>>('/Siteconfig/getList', {
        data: params
    });
}
//删除配置
export const deleteSiteConfig = (id: string) => {
    return get<number>(`/Siteconfig/delete/${id}`, {
        custom: { successTip: '删除成功', errorTip: '删除失败，请重试~' }
    });
}
//获取配置详情
export const getSiteConfigDetail = (id: string) => {
    return get<Config>(`/Siteconfig/getInfo/${id}`);
}
//新增配置
export const addSiteConfig = (params: ConfigData) => {
    return post<string>('/Siteconfig/save', {
        data: params,
        custom: { successTip: '保存成功', errorTip: '保存失败，请重试~' }
    });
}
//workgroup 获取班组
export const getWorkGroup = (data: { ifPage: boolean }) => {
    return post<WorkGroup[]>('/Workgroup/getList', { data: data });
}
//获取巡检记录详情
export const getRecordDetail = (id: string) => {
    return get<InspectionDetail>(`/Inspectionrecord/getInfo/${id}`);
}
//下载巡检记录
export const downloadRecord = (data: { startTime: string, endTime: string }) => {
    return post<string>('/Inspectionrecord/download', {
        data: data,
    });
}

//获取当前巡检人员关联的巡检配置
export const getInspectionConfig = () => {
    return get<any>('/Siteconfig/getSiteconfigByLoginUser');
}
//巡检打卡
export const inspectionClock = (data: InspectionClockParams) => {
    return post<string>('/Inspectionrecord/save', {
        data: data,
    })
}
// 动态二维码解析2
export const getQrCode = (analyzeQrCode2: string) => {
    return get<any>(`/Inspectionrecord/analyzeQrCode2/${analyzeQrCode2}`)
}
// 日期
export const getDate = async () => {
    return get<any>('/Holidaycalendar/getInspectionrecordcalendarList');
}
//根据macAddr获取站设备信息，判断是否入库
export const getMacAddr = (macAddr: string) => {
    return get<any>(`/Qrcodedevice/getInfoByMacAddr/${macAddr}`)
}
// 新增编辑设备入库信息
export const saveQrCode = (data: { macAddr: string, period: number, id?: string }) => {
    return post<any>('/Qrcodedevice/save', {
        data: data,
        custom: { successTip: '更新成功', errorTip: '更新失败，请重试~' }
    })
}
// 校验设备mac地址是否已被绑定
export const checkMacAddr = (data: { macAddr: string, id?: string }) => {
    return post<any>(`/Site/checkMacAddrExists`, {
        data: data
    });
}

/**巡检相关请求服务 */
export default {
    getSiteAreaList,
    getRecordList,
    getSiteList,
    deleteSite,
    addSiteArea,
    addSite,
    getSiteDetail,
    getSiteConfigList,
    deleteSiteConfig,
    addSiteConfig,
    getAllFloorName,
    getSiteConfigDetail,
    getWorkGroup,
    getRecordDetail,
    downloadRecord,
    getInspectionConfig,
    inspectionClock,
    getQrCode,
    getDate,
    getMacAddr,
    saveQrCode,
    checkMacAddr
};
