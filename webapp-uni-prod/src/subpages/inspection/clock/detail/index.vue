<template>
  <InspectionDetail :id="id" />
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import InspectionDetail from "@/subpages/inspection/components/inspection-detail.vue";
const id = ref<string>("");
onLoad((options) => {
  if (!options) return;
  console.log(options);
  id.value = options.id;
});
</script>
<style lang="scss" scoped></style>
