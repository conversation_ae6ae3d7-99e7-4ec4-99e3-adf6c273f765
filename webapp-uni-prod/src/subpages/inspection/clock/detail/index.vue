<template>
  <view>
    <RecordDetail :type="'inspection'" :options="list" v-if="isDataLoaded" />
    <view v-else class="loading">加载中...</view>
  </view>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import RecordDetail from "@/subpages/inspection/components/inspection-detail.vue";
import inspectionService from "@/subpages/inspection/api/inspection.service";
import { InspectionDetail } from "@/subpages/inspection/models/inspection";
const list = ref({} as InspectionDetail);
const isDataLoaded = ref(false); //添加isDataLoaded 解决子组件onMounted比父组件onLoad先执行的问题
onLoad(async (options) => {
  if (options?.id) {
    await getData(options.id);
  }
});
const getData = async (id: string) => {
  const res = await inspectionService.getRecordDetail(id);
  list.value = res as InspectionDetail;
  isDataLoaded.value = true;
};
</script>
<style lang="scss" scoped></style>
