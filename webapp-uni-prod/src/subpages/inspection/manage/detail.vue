<template>
  <view class="container">
    <view class="inspection">
      <view class="inspection-item">
        <view class="item-label">站点名称</view>
        <view>{{ station.siteName }}</view>
      </view>
      <view class="inspection-item">
        <view class="item-label">设备MAC</view>
        <view>{{ station.macAddr }}</view>
      </view>
      <view class="inspection-item">
        <view class="item-label">设备编码</view>
        <view>{{ station.siteNumber }}</view>
      </view>
      <view class="inspection-item">
        <view class="item-label">区域</view>
        <view>{{ station.floorName }}</view>
      </view>
      <view class="inspection-item">
        <view class="item-label">点位位置</view>
        <view>{{ station.sitePosition }}</view>
      </view>
      <view class="inspection-item">
        <view class="item-label">图片</view>
        <view class="img">
          <image
            v-if="station.content?.storeDir"
            :src="
              baseUrl +
              '/upload/' +
              station.content.storeDir +
              '/' +
              station.content.storeName
            "
            mode="aspectFill"
            class="inspection-image"
            @click="onPreviewImage(station.content)"
          />
          <SlSubSvgIcon subpage="inspection" v-else name="48-48-1" size="32" />
        </view>
      </view>
      <view class="inspection-item">
        <view class="item-label">备注</view>
        <view>{{ station.remarks ? station.remarks : "--" }}</view>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import inspectionService from "@/subpages/inspection/api/inspection.service";
import {
  Station,
  StationDetail,
} from "@/subpages/inspection/models/inspection";
const baseUrl = import.meta.env.VITE_API_BASE_URL;
const station = ref({
  siteName: "",
  siteNumber: "",
  sitePosition: "",
  floorName: "",
  remarks: "",
  macAddr: "",
} as Station);
onLoad((options: AnyObject | undefined) => {
  if (options?.id) {
    getData(options.id);
  }
});
const getData = async (id: string) => {
  const res = await inspectionService.getSiteDetail(id);
  station.value = res;
};
const onPreviewImage = (item: StationDetail) => {
  const url = `${baseUrl}/upload/${item.storeDir}/${item.storeName}`;
  uni.previewImage({
    current: url,
    urls: [url],
  });
};
</script>
<style lang="scss" scoped>
/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  background: #f6f8fa;
}
.inspection {
  font-size: 14px;
  padding: 2px 15px;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  background: #fff;
  .inspection-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 12px 0;
    .item-label {
      color: #666666;
    }
    .img {
      .inspection-image {
        width: 32px;
        height: 32px;
        border-radius: 4px;
      }
    }
  }
}
</style>
