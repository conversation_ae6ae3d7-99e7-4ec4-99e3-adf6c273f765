<template>
  <view class="container">
    <siteDetail :siteInfo="station" />
  </view>
</template>
<script lang="ts" setup>
import inspectionService from "@/subpages/inspection/api/inspection.service";
import siteDetail from "../components/site-detail.vue";
import { Station } from "@/subpages/inspection/models/inspection";
const station = ref({
  siteName: "",
  siteNumber: "",
  sitePosition: "",
  floorName: "",
  remarks: "",
  macAddr: "",
} as Station);
onLoad((options: AnyObject | undefined) => {
  if (options?.id) {
    getData(options.id);
  }
});
const getData = async (id: string) => {
  const res = await inspectionService.getSiteDetail(id);
  station.value = res;
  console.log(station.value);
};
</script>
<style lang="scss" scoped>
/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  background: #f6f8fa;
}
</style>
