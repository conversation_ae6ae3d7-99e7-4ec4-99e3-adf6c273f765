<template>
  <view class="container">
    <view class="add-inspection">
      <view class="add-item">
        <view class="item-label"
          ><text style="color: #e50101">*</text>站点名称</view
        >
        <view>
          <up-input
            style="background-color: #f8f8f8; padding: 6px 8px; width: 200px"
            placeholder="请绑定点位名称"
            border="none"
            inputAlign="right"
            font-size="14px"
            maxlength="10"
            v-model="params.siteName"
          ></up-input>
        </view>
      </view>
      <view class="add-item">
        <view class="item-label"
          ><text style="color: #e50101">*</text>站点编号</view
        >
        <view>
          <up-input
            style="background-color: #f8f8f8; padding: 6px 8px; width: 200px"
            placeholder="请绑定站点编号"
            border="none"
            inputAlign="right"
            font-size="14px"
            maxlength="20"
            v-model="params.siteNumber"
          ></up-input>
        </view>
      </view>
      <view class="add-item">
        <view class="item-label"
          ><text style="color: #e50101">*</text>区域</view
        >
        <view>
          <SLSelect
            :options="options"
            placeholder="请选择站点区域"
            icon-name="12-12-7"
            icon-size="12"
            @change="handlPositionChange"
            v-model="params.floorName"
          >
            <template #dropdown-footer>
              <div
                class="select-btn-container"
                @click.stop="(newPositionName = ''), (showAddConfirm = true)"
              >
                <div class="select-add-btn">新增</div>
              </div>
            </template>
          </SLSelect>
        </view>
      </view>
      <view class="add-item">
        <view class="item-label"
          ><text style="color: #e50101">*</text>点位位置</view
        >
        <view>
          <up-input
            style="background-color: #f8f8f8; padding: 6px 8px; width: 200px"
            placeholder="请输入点位位置"
            border="none"
            inputAlign="right"
            font-size="14px"
            v-model="params.sitePosition"
          ></up-input>
        </view>
      </view>
      <view class="add-item">
        <view class="item-label">
          <text style="color: #fff">*</text>站点图片</view
        >
        <view>
          <UploadImage2 :maxCount="1" :size="32"></UploadImage2>
        </view>
      </view>
      <view class="remark">
        <view class="item-label"><text style="color: #fff">*</text>备注</view>
        <view>
          <up-textarea
            class="textarea"
            placeholder="请输入备注信息"
            maxlength="15"
            v-model="params.remarks"
          ></up-textarea>
        </view>
      </view>
    </view>
    <view class="btn">
      <BaseButton btnType="cancel" style="margin-right: 10px" @click="onBack()"
        >取消</BaseButton
      >
      <BaseButton btnType="save" @click="onConfirm()">完成</BaseButton>
    </view>
  </view>
  <ConfirmDialog
    v-if="showAddConfirm"
    @close="showAddConfirm = false"
    @confirm="onConfirmAdd"
    :showHeader="false"
  >
    <div class="add-box">
      <span class="label">区域名称：</span>
      <up-input
        type="text"
        placeholder="请输入"
        v-model="newPositionName"
        clearable
        maxlength="8"
        :border="'none'"
      />
    </div>
  </ConfirmDialog>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import BaseButton from "@/components/BaseButton.vue";
import SLSelect from "@/components/SLSelect.vue";
import {
  Floor,
  AddStationParams,
} from "@/subpages/inspection/models/inspection";
import inspectionService from "@/service/inspection";
interface Options {
  label: string;
  value: string;
}
const options = ref<Options[]>([]);
const newPositionName = ref<string>("");
const isEdit = ref<Boolean>(false); //true为编辑，false为新增

//区域名称input
const showAddConfirm = ref<Boolean>(false);
const params = reactive<AddStationParams>({
  siteName: "",
  siteNumber: "",
  floorName: "",
  sitePosition: "",
  remarks: "",
  content: {
    id: "",
    meetingId: "",
    storeName: "",
    filePath: "",
    fileType: "",
    fileSize: "",
    fullName: "",
  },
});
onLoad((options: AnyObject | undefined) => {
  if (options?.id) {
    uni.setNavigationBarTitle({
      title: "编辑巡检",
    });
    isEdit.value = true;
    getDetail(options.id);
  } else {
    uni.setNavigationBarTitle({
      title: "新增巡检",
    });
    isEdit.value = false;
  }
  getFloorList();
});
const getFloorList = async () => {
  const res = await inspectionService.getSiteAreaList({
    ifPage: false,
  });
  // 确保 res 是数组
  if (Array.isArray(res)) {
    options.value = res.map((item: Floor) => ({
      label: item.floorName,
      value: item.id,
    }));
  } else {
    options.value = []; // 处理非数组情况
  }
};
const getDetail = async (id: string) => {
  const res = await inspectionService.getSiteDetail(id);
  params.siteName = res.siteName;
  params.siteNumber = res.siteNumber;
  params.floorName = res.floorName;
  params.sitePosition = res.sitePosition;
  params.remarks = res.remarks;
  // params.content = res.content;
  newPositionName.value = res.floorName;
};

const onConfirmAdd = async () => {
  if (!newPositionName.value) {
    uni.showToast({
      title: "请输入区域名称",
      icon: "none",
    });
    return;
  }
  await inspectionService.addSiteArea({
    floorName: newPositionName.value,
  });
  getFloorList();
  showAddConfirm.value = false;
};
const handlPositionChange = (e: Options) => {
  params.floorName = e.label;
};
const onBack = () => {
  uni.navigateBack();
};
const onConfirm = () => {
  if (!params.siteName) {
    uni.showToast({
      title: "请输入站点名称",
      icon: "none",
    });
    return;
  }
  if (!params.siteNumber) {
    uni.showToast({
      title: "请输入站点编号",
      icon: "none",
    });
    return;
  }
  if (!params.floorName) {
    uni.showToast({
      title: "请选择区域",
      icon: "none",
    });
    return;
  }
  if (!params.sitePosition) {
    uni.showToast({
      title: "请输入站点位置",
      icon: "none",
    });
    return;
  }
  console.log(params, "请求参数");
  if (isEdit.value) {
    // inspectionService.editSite(params);
  } else {
    inspectionService.addSite(params);
  }
  uni.navigateBack();
};
</script>
<style lang="scss" scoped>
/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  background: #f6f8fa;
}
.add-inspection {
  font-size: 14px;
  padding: 14px;
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);

  .add-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;

    .item-label {
      color: #666666;
    }
    .select-btn-container {
      width: 100%;
      height: 28px;
      line-height: 28px;
      display: flex;
      text-align: center;
      justify-content: center;
      margin: 5px 0;
      .select-add-btn {
        width: 60px;
        color: #fff;
        font-size: 14px;
        font-weight: 400;
        background: #0066df;
      }
    }
  }
  .remark {
    display: flex;
    flex-direction: column;
    .item-label {
      color: #666666;
      margin-bottom: 10px;
    }
  }
}
.btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}
.add-box {
  height: 106px;
  display: flex;
  align-items: center;
}
// .inp {
//   width: 200px;
//   height: 40px;
//   font-size: 14px;
//   text-align: right;
//   background-color: #f8f8f8;
// }
</style>
