<template>
  <view class="container">
    <view class="add-inspection">
      <view class="add-item">
        <view class="item-label"><text class="star">*</text>站点名称</view>
        <view>
          <input
            placeholder="请输入站点名称"
            :border="'none'"
            inputAlign="right"
            type="text"
            v-model="params.siteName"
            :maxlength="10"
            class="item-input"
          />
        </view>
      </view>
      <view class="add-item">
        <view class="item-label no-star">设备MAC</view>
        <view :class="params.macAddr ? 'device' : 'device gray'">
          {{ params.macAddr ? params.macAddr : "写入设备MAC" }}
        </view>
      </view>
      <view class="add-item">
        <view class="item-label"><text class="star">*</text>设备编码</view>
        <view>
          <input
            placeholder="请输入设备编码"
            :border="'none'"
            inputAlign="right"
            type="text"
            v-model="params.siteNumber"
            :maxlength="17"
            class="item-input"
          />
        </view>
      </view>
      <view class="add-item">
        <view class="item-label"><text class="star">*</text>区域</view>
        <view class="item-area">
          <SLSelect
            :options="options"
            placeholder="请选择站点区域"
            icon-name="12-12-7"
            icon-size="12"
            @change="handlPositionChange"
            v-model="params.floorName"
            style="text-align: left"
          >
            <template #dropdown-footer>
              <div class="select-btn-container" @click.stop="onAddFloor()">
                <div class="select-add-btn">新增</div>
              </div>
            </template>
          </SLSelect>
        </view>
      </view>
      <view class="add-item">
        <view class="item-label"><text class="star">*</text>站点位置</view>
        <view>
          <input
            placeholder="请输入点位位置"
            :border="'none'"
            inputAlign="right"
            type="text"
            v-model="params.sitePosition"
            class="item-input"
          />
        </view>
      </view>
      <view class="add-item">
        <view class="item-label no-star"> 站点图片</view>
        <view>
          <view class="img">
            <image
              v-if="isImg"
              :src="
                baseUrl +
                '/upload/' +
                params.content.storeDir +
                '/' +
                params.content.storeName
              "
              mode="aspectFill"
              class="inspection-image"
              ><text class="img-text" @click="isImg = false">删除</text></image
            >
            <UploadImage2
              v-else
              :uploadType="'image'"
              :maxCount="1"
              :size="32"
              v-model:contentList="contentList"
            ></UploadImage2>
          </view>
        </view>
      </view>
      <view class="remark">
        <view class="item-label">备注</view>
        <view>
          <textarea
            class="textarea"
            v-model="params.remarks"
            placeholderStyle="color: #999999"
            placeholder="请输入"
            :maxlength="15"
          />
        </view>
      </view>
    </view>
    <view class="device">
      <view class="device-title">
        <view class="title-left">设备列表</view>
        <view class="title-right" @click="onSearch()">
          <view class="icon">
            <SlSubSvgIcon
              v-if="isSearch"
              subpage="inspection"
              name="16-16-39"
              size="16"
            />
            <SlSubSvgIcon
              v-else
              subpage="inspection"
              name="16-16-38"
              size="16"
            />
          </view>
          <view class="text">{{ isSearch ? "停止搜索" : "开始搜索" }}</view>
        </view>
      </view>
      <view class="device-list" v-for="item in deviceList" :key="item.deviceId">
        <view class="device-item" @click="onJumpDevice(item.deviceId)">
          <view class="item">
            <!-- 设备列表项的单选图标 -->
            <view class="device-radio" @click.stop="onCheck(item.deviceId)">
              <!-- 未选中时显示空心圆 -->
              <view
                class="un-checked"
                v-if="checkedDeviceId !== item.deviceId"
              ></view>
              <!-- 选中时显示勾选图标 -->
              <SlSubSvgIcon
                v-else
                subpage="inspection"
                name="16-16-9"
                size="16"
              />
            </view>
            <view class="device-info">
              <view class="info">
                <view class="info-label">设备MAC：</view>
                <view>{{ item.deviceId }}</view>
              </view>
            </view>
          </view>
          <view class="device-number"> {{ item.RSSI }}dBm </view>
        </view>
      </view>
    </view>
    <view class="mentation">
      <view class="mentation-title">温馨提示</view>
      <view class="mentation-content"
        >1.请先搜索二维码设备,并维护设备信息
      </view>
      <view class="mentation-content"
        >2.选择站点对应的二维码设备再完善站点信息</view
      >
      <view class="mentation-content"
        >3.为搜索到二维码设备，可尝试以下解决方法：</view
      >
      <view class="second mentation-content">a.检查手机蓝牙是否开启</view>
      <view class="second mentation-content">b.将手机靠近二维码设备</view>
    </view>
    <view class="btn">
      <BaseButton
        btnType="delete"
        style="margin-right: 10px"
        @click="onDel()"
        v-if="isEdit"
        >删除</BaseButton
      >
      <BaseButton
        btnType="cancel"
        style="margin-right: 10px"
        @click="onBack()"
        v-else
        >取消</BaseButton
      >
      <BaseButton btnType="save" @click="onConfirm()">完成</BaseButton>
    </view>
  </view>
  <ConfirmDialog
    v-if="showAddConfirm"
    @close="showAddConfirm = false"
    @confirm="onConfirmAdd"
    :showHeader="false"
  >
    <view class="add-box">
      <span class="label">区域名称：</span>
      <input
        type="text"
        placeholder="请输入"
        v-model="newPositionName"
        clearable
        :maxlength="10"
        border="none"
        class="area-input"
      />
    </view>
  </ConfirmDialog>

  <!-- 删除确认对话框 -->
  <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="del">
    您确定要删除此站点吗?
  </ConfirmDialog>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import BaseButton from "@/components/BaseButton.vue";
import SLSelect from "@/components/SLSelect.vue";
import { Content } from "@/models/Content";
import {
  Floor,
  AddStationParams,
  StationDetail,
} from "@/subpages/inspection/models/inspection";
import inspectionService from "@/subpages/inspection/api/inspection.service";
interface Options {
  label: string;
  value: string;
}
interface DevicesList {
  deviceId: string;
  RSSI: number;
}

const options = ref<Options[]>([]);
const newPositionName = ref<string>("");
const isEdit = ref<Boolean>(false); //true为编辑，false为新增
const contentList = ref<Content[]>([]);
const showConfirm = ref<Boolean>(false);
const delId = ref<string>("");
const baseUrl = import.meta.env.VITE_API_BASE_URL;
const isImg = ref<Boolean>(false); //编辑的时候是否有图片
const isSearch = ref<Boolean>(true); //是否搜索 true为搜索，false为不搜索
const checkedDeviceId = ref<string>(""); // 记录当前选中的设备ID
//区域名称input
const showAddConfirm = ref<Boolean>(false);
const params = reactive<AddStationParams>({
  id: "",
  siteName: "",
  siteNumber: "",
  floorName: "",
  sitePosition: "",
  remarks: "",
  macAddr: "",
  content: {} as StationDetail,
  siteStatus: "",
});
// 设备信息列表
const deviceList = ref([] as DevicesList[]);
onLoad((options: AnyObject | undefined) => {
  if (options?.id) {
    uni.setNavigationBarTitle({
      title: "编辑站点",
    });
    isEdit.value = true;
    getDetail(options.id);
  } else {
    uni.setNavigationBarTitle({
      title: "新增站点",
    });
    isEdit.value = false;
  }
  getFloorList();
  delId.value = options?.id;
  // 初始化蓝牙模块
  openBluetoothAdapter();
});
const getFloorList = async () => {
  const res = await inspectionService.getSiteAreaList({
    ifPage: false,
  });
  // 确保 res 是数组
  if (Array.isArray(res)) {
    options.value = res.map((item: Floor) => ({
      label: item.floorName,
      value: item.floorName,
    }));
  } else {
    options.value = []; // 处理非数组情况
  }
};
const getDetail = async (id: string) => {
  const res = await inspectionService.getSiteDetail(id);
  params.siteName = res.siteName;
  params.siteNumber = res.siteNumber;
  params.floorName = res.floorName;
  params.sitePosition = res.sitePosition;
  params.remarks = res.remarks;
  params.content = res.content as StationDetail;
  params.macAddr = res.macAddr;
  newPositionName.value = res.floorName;
  params.id = res.id;
  params.siteStatus = res.siteStatus;
  if (res.content?.storeDir) {
    // contentList.value = [res.content];
    isImg.value = true;
  } else {
    contentList.value = [];
  }
};

const onConfirmAdd = async () => {
  if (!newPositionName.value) {
    uni.showToast({
      title: "请输入区域名称",
      icon: "none",
    });
    return;
  }
  // 去重
  const isExist = options.value.some(
    (item) => item.label === newPositionName.value
  );
  if (isExist) {
    uni.showToast({
      title: "区域已存在",
      icon: "none",
    });
    return;
  }
  await inspectionService.addSiteArea({
    floorName: newPositionName.value,
  });
  getFloorList();
  showAddConfirm.value = false;
};
const handlPositionChange = (e: Options) => {
  params.floorName = e.label;
};
const onBack = () => {
  uni.navigateBack();
};
const onDel = () => {
  showConfirm.value = true;
};
const del = async () => {
  inspectionService.deleteSite(delId.value);
  showConfirm.value = false;
  uni.redirectTo({
    url: "/subpages/inspection/manage/index",
  });
};
const onConfirm = async () => {
  if (!params.siteName) {
    uni.showToast({
      title: "请输入站点名称",
      icon: "none",
    });
    return;
  }
  if (!params.siteNumber) {
    uni.showToast({
      title: "请输入设备编码",
      icon: "none",
    });
    return;
  }
  if (!params.floorName) {
    uni.showToast({
      title: "请选择区域",
      icon: "none",
    });
    return;
  }
  if (!params.sitePosition) {
    uni.showToast({
      title: "请输入站点位置",
      icon: "none",
    });
    return;
  }
  if (isImg.value) {
  } else if (contentList.value.length == 0) {
    params.content = {};
  } else {
    params.content = contentList.value[0];
  }

  if (isEdit.value) {
    //编辑
    await inspectionService.addSite(params);
  } else {
    // 新增 移除id和siteStatus
    delete params.id;
    delete params.siteStatus;
    await inspectionService.addSite(params);
  }
  uni.redirectTo({
    url: "/subpages/inspection/manage/index",
  });
};
const onJumpDevice = (deviceId: string) => {
  isStore(deviceId, "jump");
};
// ============================ 设备蓝牙处理 ============================
const openBluetoothAdapter = () => {
  wx.openBluetoothAdapter({
    success: (res) => {
      console.log("蓝牙适配器初始化成功", res);
      startBluetoothDevicesDiscovery();
    },
    fail: (err) => {
      console.error("蓝牙适配器初始化失败", err);
      wx.showToast({
        title: "请打开手机蓝牙",
        icon: "none",
      });
    },
  });
};
const onCheck = async (deviceId: string) => {
  // 点击已选中的设备，取消选中
  if (checkedDeviceId.value === deviceId) {
    checkedDeviceId.value = ""; // 清空选中ID
    disconnectDevice(deviceId); // 断开连接
    return;
  }

  // connectDevice(deviceId); // 连接新设备
  isStore(deviceId); // 执行入库判断逻辑
};
const isStore = async (deviceId: string, type?: string) => {
  // 先判断是否入库
  const res = await inspectionService.getMacAddr(deviceId);
  if (typeof res === "object") {
    if (type == "jump") {
      uni.navigateTo({
        url: `/subpages/inspection/manage/device?id=${deviceId}&editId=${res.id}&period=${res.period}`,
      });
    } else {
      // 判断设备是否被绑定
      await inspectionService.checkMacAddr({
        macAddr: deviceId,
        id: params.id,
      });
      params.macAddr = deviceId;
      // 选中新设备：更新选中ID，并连接设备
      checkedDeviceId.value = deviceId; // 记录当前选中设备ID
    }
  } else {
    // 未入库
    uni.showModal({
      title: "提示",
      content: "该设备未入库，是否维护入库？",
      success: async (res) => {
        if (res.confirm) {
          // 用户点击了确定按钮
          uni.navigateTo({
            url: `/subpages/inspection/manage/device?id=${deviceId}`,
          });
        } else if (res.cancel) {
          // 用户点击了取消按钮
        }
      },
    });
  }
};
const onSearch = async () => {
  isSearch.value = !isSearch.value;

  if (!isSearch.value) {
    // 开始搜索：确保蓝牙适配器已初始化
    const adapterState = await new Promise((resolve) => {
      wx.getBluetoothAdapterState({
        success: (res) => resolve(res.available),
        fail: () => resolve(false),
      });
    });

    if (!adapterState) {
      // 蓝牙未开启，重新初始化
      openBluetoothAdapter();
    } else {
      // 蓝牙已开启，直接开始搜索
      startBluetoothDevicesDiscovery();
    }
  } else {
    // 停止搜索：清理监听并停止搜索
    stopBluetoothDevicesDiscovery();
    wx.offBluetoothDeviceFound(); // 移除设备发现监听
    console.log("已停止搜索蓝牙设备");
  }
};
// 开始搜索蓝牙设备
const startBluetoothDevicesDiscovery = () => {
  // 先移除之前的监听（避免重复绑定）
  wx.offBluetoothDeviceFound();
  // 重新绑定设备发现监听
  onBluetoothDeviceFound();
  wx.startBluetoothDevicesDiscovery({
    allowDuplicatesKey: true, // 允许重复上报（确保持续发现设备）
    powerLevel: "high",
    services: ["FEF0"],
    success: (res) => {
      console.log("开始搜索蓝牙设备", res);
      onBluetoothDeviceFound();
    },
    fail: (err) => {
      console.error("搜索蓝牙设备失败", err);
    },
  });
};
// 监听发现新设备的事件
const onBluetoothDeviceFound = () => {
  console.log("监听发现新设备的事件");
  wx.onBluetoothDeviceFound((res) => {
    console.log(res, "监听到");
    //this.stopBluetoothDevicesDiscovery();
    const devices = res.devices;

    // 过滤有效设备（排除空名称或信号过弱的设备）
    const validDevices = devices.filter(
      (device) => device.name && device.RSSI !== undefined && device.RSSI > -100
    );

    if (validDevices.length > 0) {
      // 更新设备列表（去重处理，避免重复添加）
      validDevices.forEach((newDevice) => {
        const isExist = deviceList.value.some(
          (item) => item.deviceId === newDevice.deviceId
        );
        if (!isExist) {
          deviceList.value = [...deviceList.value, newDevice];
        }
      });
    }
  });
};
// 停止搜索蓝牙设备
const stopBluetoothDevicesDiscovery = () => {
  wx.stopBluetoothDevicesDiscovery({
    success: (res) => {
      console.log("停止搜索蓝牙设备", res);
    },
  });
};
// 断开蓝牙设备连接
const disconnectDevice = (deviceId: any) => {
  if (deviceId) {
    wx.closeBLEConnection({
      deviceId,
      success: (res) => {
        console.log("断开连接成功", res);
        wx.showToast({
          title: "已断开连接",
          icon: "none",
        });
      },
      fail: (err) => {
        console.error("断开连接失败", err);
      },
    });
  }
};
// 离开页面，断开蓝牙设备连接
onUnload(() => {
  stopBluetoothDevicesDiscovery();
  wx.offBluetoothDeviceFound(); // 移除设备发现监听
  console.log("已停止搜索蓝牙设备");
});
const onAddFloor = async () => {
  showAddConfirm.value = true;
  newPositionName.value = "";
};
</script>
<style lang="scss" scoped>
/* 容器样式 */
.container {
  // height: 100%;
  width: 100%;
  background: #f6f8fa;
}
.add-inspection {
  font-size: 14px;
  padding: 14px;
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);

  .add-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    .no-star {
      margin-left: 7px;
    }
    .star {
      width: 7px;
      color: #e50101;
    }
    .device {
      width: 172px;
      height: 32px;
      line-height: 32px;
      text-align: right;
      padding: 0 8px;
      box-sizing: border-box;
      background-color: #f8f8f8;
    }
    .gray {
      color: #999999;
    }

    .item-label {
      color: #666666;
    }
    .item-area {
      width: 172px;
      height: 32px;
    }
    .item-input {
      font-size: 14px;
      width: 156px;
      padding: 0 8px;
      height: 32px;
      line-height: 32px;
      background-color: #f8f8f8;
      text-align: right;
      overflow: scroll;
    }
    .img {
      .inspection-image {
        height: 32px;
        width: 32px;
        border-radius: 3px;
        position: relative;
        .img-text {
          width: 100%;
          position: absolute;
          bottom: 0;
          z-index: 2;
          font-size: 12px;
          color: #fff;
          text-align: center;
          text-shadow: 0 0 4px rgba(0, 0, 0, 0.8),
            0 0 10px rgba(255, 255, 255, 0.3);
        }
      }
    }
    .select-btn-container {
      width: 100%;
      height: 28px;
      line-height: 28px;
      display: flex;
      text-align: center;
      justify-content: center;
      margin: 5px 0;
      .select-add-btn {
        width: 60px;
        color: #fff;
        font-size: 14px;
        font-weight: 400;
        background: #0066df;
      }
    }
  }
  .remark {
    display: flex;
    flex-direction: column;
    margin-left: 7px;
    .item-label {
      color: #666666;
      margin-bottom: 10px;
    }
    .textarea {
      width: 100%;
      height: 56px;
      font-size: 14px;
      color: #333;
      background: #f8f8f8;
      padding: 9px 13px;
      border-radius: 3px;
      box-sizing: border-box;
    }
  }
}
.device {
  .device-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-size: 14px;
    margin: 10px 15px;
    .title-left {
      color: #333333;
    }
    .title-right {
      display: flex;
      flex-direction: row;
      align-items: center;
      color: #4f7af6;
      .icon {
        margin-top: 3px;
      }
      .text {
        margin-left: 5px;
      }
    }
  }
  .device-list {
    background: #fff;
    font-size: 12px;
    .device-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 0 15px;
      .item {
        display: flex;
        flex-direction: row;
        align-items: center;
        .device-radio {
          margin-right: 10px;
          .un-checked {
            height: 16px;
            width: 16px;
            border: 1px solid #999999;
            border-radius: 50%;
          }
        }
        .device-info {
          color: #333333;
          height: 50px;
          line-height: 50px;
          .info {
            display: flex;
            flex-direction: row;
            .info-label {
            }
          }
        }
      }
      .device-number {
        color: #999999;
      }
    }
  }
}
.mentation {
  font-size: 12px;
  color: #666666;
  margin: 20px 15px 0 15px;
  .mentation-title {
    color: #333333;
    margin-bottom: 12px;
  }
  .mentation-content {
    margin-bottom: 10px;
  }
  .second {
    margin-left: 12px;
  }
}
.btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 50px 0 20px 0;
  box-sizing: border-box;
}
.add-box {
  // height: 106px;
  display: flex;
  align-items: center;
  .area-input {
    flex: 1;
    width: 100%;
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    text-align: left;
    background: #f8f8f8;
    border-radius: 3px;
    padding: 0 9px;
  }
}
:deep(.select-value) {
  text-align: right;
}
</style>
