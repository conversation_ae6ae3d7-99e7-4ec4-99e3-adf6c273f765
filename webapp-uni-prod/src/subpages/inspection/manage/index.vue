<template>
  <SlTabbarPage :tab-bar-root="'subpages/inspection'">
    <view class="inspection-manage-page">
      <!-- 左侧楼层导航 -->
      <scroll-view scroll-y class="floor-nav">
        <view
          v-for="floor in floorData"
          :key="floor.id"
          class="floor-item"
          :class="{ active: floor.id === activeFloorId }"
          @click="handleFloorClick(floor)"
        >
          {{ floor.floorName }}
        </view>
      </scroll-view>

      <!-- 右侧巡检点列表 -->
      <scroll-view
        scroll-y
        class="inspection-list"
        @scrolltolower="loadMoreData"
      >
        <template v-if="recordData.length > 0">
          <view
            v-for="inspection in recordData"
            :key="inspection.id"
            class="inspection-card"
            @click="goDetail(inspection)"
          >
            <view class="card-left">
              <image
                v-if="inspection.content?.storeDir"
                :src="
                  baseUrl +
                  '/upload/' +
                  inspection.content.storeDir +
                  '/' +
                  inspection.content.storeName
                "
                mode="aspectFill"
                class="inspection-image"
              />
              <SlSubSvgIcon
                subpage="inspection"
                v-else
                name="48-48-1"
                size="48"
              />
            </view>
            <view class="card-right">
              <view class="card-header">
                <text class="position">{{ inspection.siteName }}</text>
                <view class="action-buttons">
                  <view class="edit-btn" @click.stop="onEdit(inspection)">
                    <SlSubSvgIcon
                      subpage="inspection"
                      name="16-16-36"
                      size="15"
                    />
                  </view>
                  <view class="delete-btn" @click.stop="onDel(inspection)">
                    <SlSubSvgIcon
                      subpage="inspection"
                      name="16-16-16"
                      size="15"
                    />
                  </view>
                </view>
              </view>

              <view class="card-body">
                <view class="info-row">
                  <text class="info-label">区域</text>
                  <text class="info-value">{{ inspection.floorName }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">站点位置</text>
                  <text class="info-value">{{ inspection.sitePosition }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">备注</text>
                  <text class="info-value">{{
                    inspection.remarks || "--"
                  }}</text>
                </view>
                <view
                  class="error-code info-row"
                  v-if="inspection.siteStatus == 'abnormal'"
                >
                  <view class="info-label"></view>
                  <text class="error-text">二维码标签异常</text>
                </view>
              </view>
            </view>
          </view>
          <up-loadmore :status="status" />
        </template>

        <BaseEmpty v-else />
      </scroll-view>

      <!-- 新增按钮 -->
      <AddBtn @add="onAdd()" />

      <!-- 删除确认对话框 -->
      <ConfirmDialog
        v-if="showConfirm"
        @close="showConfirm = false"
        @confirm="del"
      >
        您确定要删除此站点吗?
      </ConfirmDialog>
    </view>
  </SlTabbarPage>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import inspectionService from "@/subpages/inspection/api/inspection.service";
import {
  StationParams,
  Station,
  Floor,
} from "@/subpages/inspection/models/inspection";
import { LoadStatus } from "@/subpages/inspection/models/inspection";
import AddBtn from "@/subpages/inspection/components/add-btn.vue";
const baseUrl = import.meta.env.VITE_API_BASE_URL;
const showConfirm = ref<Boolean>(false);
const delId = ref<string>(""); //要删除的站点id
const status = ref<LoadStatus>("loadmore");
const recordData = ref<Station[]>([]);
const floorData = ref<Floor[]>([]);
const activeFloorId = ref<string>("");

const querySite = reactive<StationParams>({
  ifPage: true,
  floorName: "",
  currentPage: 1,
  pageRecord: 10,
});

onShow(() => {
  getFloorList();
});

const getFloorList = async () => {
  const res = await inspectionService.getSiteAreaList({
    ifPage: false,
  });
  // 确保 res 是数组
  if (Array.isArray(res)) {
    floorData.value = res.map((item: Floor) => ({
      ...item,
    }));
    activeFloorId.value = floorData.value[0].id;
    querySite.floorName = floorData.value[0].floorName;
    //先获取到第一个楼层数据再请求
    getData();
  } else {
    floorData.value = []; // 处理非数组情况
  }
};
const getData = async () => {
  if (status.value === "nomore") return;
  status.value = "loading";
  const res = await inspectionService.getSiteList(querySite);
  //recordCount为总数，大于就不会进行加载
  if (res.recordCount! > recordData.value.length) {
    if (res.result) {
      recordData.value = [...recordData.value, ...res.result];
      status.value =
        res.result.length < querySite.pageRecord! ? "nomore" : "loadmore";
    } else {
      recordData.value = [];
      status.value = "loadmore";
    }
  } else {
    status.value = "nomore";
  }
};
const loadMoreData = () => {
  if (status.value !== "loading") {
    querySite.currentPage! += 1;
    getData();
  }
};
//编辑
const onEdit = (item: any) => {
  uni.navigateTo({
    url: `/subpages/inspection/manage/edit-inspection?id=${item.id}`,
  });
};
//删除
const onDel = (item: Station) => {
  showConfirm.value = true;
  delId.value = item.id;
};
const del = async () => {
  await inspectionService.deleteSite(delId.value);
  showConfirm.value = false;
  recordData.value = [];
  status.value = "loadmore";
  getData();
};
//左侧导航栏选择
const handleFloorClick = (item: Floor) => {
  activeFloorId.value = item.id;
  querySite.floorName = item.floorName;
  querySite.currentPage = 1;
  recordData.value = [];
  status.value = "loadmore";
  getData();
};
//跳转到详情页
const goDetail = (item: Station) => {
  uni.navigateTo({
    url: `/subpages/inspection/manage/detail?id=${item.id}`,
  });
};
//跳转到新增页
const onAdd = () => {
  uni.navigateTo({
    url: `/subpages/inspection/manage/edit-inspection`,
  });
};
</script>

<style lang="scss" scoped>
.inspection-manage-page {
  display: flex;
  height: 100%;
  width: 100%;
  background: #f6f8fa;
  overflow: hidden;
}

.floor-nav {
  width: 56px;
  height: 100%;
  background: #ffffff;
  flex-shrink: 0;

  .floor-item {
    height: 40px;
    font-size: 14px;
    color: #636363;
    line-height: 40px;
    text-align: center;
    transition: all 0.2s;

    &.active {
      color: #333333;
      font-weight: bold;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: #005cc8;
        border-radius: 1.5px;
      }
    }
  }
}

.inspection-list {
  flex: 1;
  padding: 10px 8px 10px 12px;
  height: 100%;
  //border-box包括padding和border
  box-sizing: border-box;
  .inspection-card {
    display: flex;
    background: #ffffff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(146, 146, 146, 0.15);
    margin-bottom: 12px;
    padding: 12px;

    .card-left {
      margin-right: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .inspection-image {
        width: 48px;
        height: 48px;
        border-radius: 4px;
      }

      .left-times {
        width: 48px;
        height: 17px;
        font-size: 10px;
        line-height: 16.8px;
        text-align: center;
        border-radius: 2px;
        color: #782424;
        border: 1px solid #ebc1c1;
        background: #ffe4e4;
      }
    }

    .card-right {
      flex: 1;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .position {
          font-size: 14px;
          font-weight: bold;
          color: #333333;
        }

        .action-buttons {
          display: flex;

          .edit-btn,
          .delete-btn {
            margin-left: 15px;
          }
        }
      }

      .card-body {
        .info-row {
          display: flex;
          margin-bottom: 4px;
          font-size: 12px;

          .info-label {
            color: #666666;
            min-width: 60px;
          }

          .info-value {
            color: #333333;
            flex: 1;
          }
        }
        .error-code {
          display: flex;
          font-size: 12px;
          .error-text {
            color: #c93535;
            width: 99px;
            height: 23px;
            line-height: 23px;
            text-align: center;
            background: #ffe4e4;
            border-radius: 2px;
            box-sizing: border-box;
            border: 1px solid #ebc1c1;
          }
        }
      }
    }
  }
}

// 空状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style>
