<template>
    <SlTabbarPage :tab-bar-root="'subpages/inspection'" >
  <view class="container">
    <view class="manage">
      <view class="manage-floor">
        <scroll-view scroll-y class="manage-scroll">
          <view
            v-for="item in floorData"
            :key="item.id"
            @click="handleFloorClick(item)"
          >
            <view
              class="scroll-view-item"
              :class="{ active: item.id === activeValue }"
            >
              {{ item.name }}
            </view>
          </view>
        </scroll-view>
        <up-loadmore :status="status" />
      </view>
      <view class="manage-card" v-if="recordData.length > 0">
        <scroll-view scroll-y class="manage-scroll">
          <view
            class="card"
            v-for="item in recordData"
            :key="item.id"
            @click="goDetail(item)"
          >
            <view class="card-left">
              <image src="https://cdn.uviewui.com/uview/album/1.jpg" mode="aspectFill" />
              <view class="left-times">标签异常</view>
            </view>
            <view class="card-right">
              <view class="card-title">
                <view class="title-left">{{ item.position }}</view>
                <view class="title-right">
                  <span class="edit" @click.stop="onEdit(item)">
                    <SlSVgIcon name="22-22-40" size="16" />
                  </span>
                  <span class="del" @click.stop="onDel(item)">
                    <SlSVgIcon name="16-16-16" size="16" />
                  </span>
                </view>
              </view>
              <view class="card-content"> 
                <view class="card-content-item">
                  <view class="card-content-label">区域</view>
                  <view class="card-content-value">{{ item.name }}</view>
                </view>
                <view class="card-content-item">
                  <view class="card-content-label">站点位置</view>
                  <view class="card-content-value">{{ item.position }}</view>
                </view>
                <view class="card-content-item">
                  <view class="card-content-label">备注</view>
                  <view class="card-content-value">{{
                    item.status ? item.status : "--"
                  }}</view>
                </view>
              </view>
            </view>
          </view>
          <up-loadmore :status="status" />
        </scroll-view>
      </view>
      
    </view>
    <view class="empty">
      <BaseEmpty v-if="recordData.length == 0" />
    </view>

    <!-- <view>
      <up-float-button :isMenu="false" bottom="70px" @click="onAdd()">
      </up-float-button>
    </view> -->
    <!-- 底部Tabbar -->
    <!-- <Tabbar :current="currentTab" @change="handleTabChange" /> -->
    <AddBtn @add="onAdd()" />
    <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="del">您确定要删除此站点吗?</ConfirmDialog>
  </view>
</SlTabbarPage>
</template>

<script lang="ts" setup>
import Tabbar from "@/subpages/inspection/components/tabbar.vue";
import { ref } from "vue";
import { onShow } from "@dcloudio/uni-app";
import { LoadStatus } from "@/models/inspection";
import AddBtn from "@/subpages/inspection/components/add-btn.vue";
const showConfirm = ref<Boolean>(false);
// 当前激活的tab索引
const currentTab = ref(1);

// 处理tab切换
const handleTabChange = (index: any) => {
  currentTab.value = index;
};
//加载更多状态
const status = ref<LoadStatus>("loadmore");
// 左侧导航栏选中
const activeValue = ref<number | null>(1);
const recordData = ref([
{
    id: 1,
    times: 1,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
  },
  {
    id: 1,
    times: 1,
    position: "B1点位1",
    name: "张三",
    status: "异常",
    time: "2022-01-01 10:00:00",
  },
  {
    id: 1,
    times: 1,
    position: "B1点位1",
    name: "张三",
    status: "",
    time: "2022-01-01 10:00:00",
  },
  {
    id: 1,
    times: 3,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "B1F楼梯",
  },
  {
    id: 1,
    times: 3,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
  },
  {
    id: 1,
    times: 3,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
  },
  {
    id: 1,
    times: 3,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
  },
  {
    id: 1,
    times: 3,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
  },
]);
//楼层
const floorData = ref([
  {
    id: 1,
    name: "B1",
  },
  {
    id: 2,
    name: "F1",
  },
  {
    id: 3,
    name: "F2",
  },
  {
    id: 4,
    name: "F3",
  },
  {
    id: 5,
    name: "F4",
  },
  {
    id: 6,
    name: "F5",
  },
  {
    id: 7,
    name: "F6",
  },
  {
    id: 8,
    name: "F7",
  },
  {
    id: 9,
    name: "F8",
  },
  {
    id: 10,
    name: "F9",
  },
  {
    id: 11,
    name: "F10",
  },
  {
    id: 12,
    name: "F11",
  },
  {
    id: 13,
    name: "F12",
  },
]);
//编辑
const onEdit = (item: any) => {
  console.log("编辑", item);
  uni.navigateTo({
    url: `/subpages/inspection/manage/edit-inspection?id=${item.id}`,
  });
};
//删除
const onDel = (item: any) => {
    showConfirm.value = true;
};
const del = () => {
    showConfirm.value = false;
    uni.showToast({
        title: "删除成功",
        icon: "success",
        duration: 2000,
    })
    console.log("删除");
}
//左侧导航栏选择
const handleFloorClick = (item: any) => {
  activeValue.value = item.id;
};
//跳转到详情页
const goDetail = (item: any) => {
  //   uni.navigateTo({
  //     url: `/subpages/inspection/station-detail/index?id=${item.id}`,
  //   });
};
//跳转到新增页
const onAdd = () => {
  console.log("跳转到新增页");
  uni.navigateTo({
    url: `/subpages/inspection/manage/add-inspection`,
  });
};
</script>
<style lang="scss" scoped>
/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background: #f6f8fa;
}
.manage {
  display: flex;
  flex-direction: row;
  .manage-floor {
    position: fixed;
    height: 100%;
    width: 54px;
    background: #ffffff;

  }
  .manage-scroll {
      height: calc(100vh - 56px); //减去底部导航栏高度
      .scroll-view-item {
        height: 40px;
        font-size: 14px;
        color: #636363;
        line-height: 40px;
        text-align: center;
        &.active {
          position: relative;
          color: #333333;
          font-weight: bold;
          // border-left: 3px solid #005CC8;
          &::before {
            display: inline-block;
            width: 3px;
            height: 20px;
            background: #005cc8;
            content: "";
            position: absolute;
            top: 11px;
            left: 5px;
          }
        }
      }
    }

  .manage-card {
    margin-left: 54px; //左侧导航栏宽度
    width: calc(100vw - 62px);
    // width: 100%;
    .card {
      display: flex;
      flex-direction: row;
      width: calc(100% - 26px); //减去margin左右边距
      margin: 10px 10px 10px 15px;
      border-radius: 4px;
      box-shadow: 0px 0px 10px 0px rgba(146, 146, 146, 0.3);
      background: #ffffff;
      .card-left {
        margin: 12px;
        image {
          width: 48px; 
          height: 48px;
          margin-bottom: 16px;
          border-radius: 3px;
        }
        .left-times {
          width: 48px;
          height: 17px;
          font-size: 10px;
          line-height: 16.8px;
          text-align: center;
          border-radius: 2px;
          color: #782424;
          border: 1px solid #ebc1c1;
          background: #ffe4e4;
        }
      }
      .card-right {
        width: 100%;
        padding: 12px;
        .card-title {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          .title-left {
            font-size: 14px;
            font-weight: 700;
            color: #333333;
            font-family: 思源黑体;
          }
          .title-right {
            height: 23px;
            font-size: 12px;
            text-align: center;
            line-height: 23px;
            color: #666666;
            .edit {
              margin-right: 10px;
            }
            .del {
            }
          }
        }
        .card-content {
          .card-content-item {
            display: flex;
            flex-direction: row;
            font-size: 12px;
            margin: 9px 23px 2px 0;
            box-sizing: border-box;
            .card-content-label {
              width: 55px;
              color: #666666;
              margin-right: 14px;
            }
            .card-content-value {
              color: #333333;
              flex: 1;
              font-family: PingFangSC;
            }
          }
        }
      }
    }
  }
}
.empty {
  height: calc(100vh - 56px);
  margin-left: 54px; //左侧导航栏宽度
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
