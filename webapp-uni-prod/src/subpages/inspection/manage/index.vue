<template>
  <SlTabbarPage :tab-bar-root="'subpages/inspection'">
    <view class="inspection-manage-page" v-if="floorData.length > 0">
      <!-- 左侧楼层导航 -->
      <scroll-view
        scroll-y
        class="floor-nav"
        v-if="floorData.length > 0"
        :scroll-top="leftScrollTop"
      >
        <view
          v-for="(floor, index) in floorData"
          :key="floor.id"
          class="floor-item"
          :class="{ active: activeFloorIndex === index }"
          @click="handleFloorClick(index)"
        >
          {{ floor.floorName }}
        </view>
      </scroll-view>

      <!-- 右侧巡检点列表 -->
      <scroll-view
        id="right-scroll"
        scroll-y
        class="inspection-list"
        :scroll-top="rightScrollTop"
        @scroll="handleRightScroll"
        scroll-with-animation
        :scroll-into-view="scrollViewId"
        @refresherrefresh="onRefresh()"
        refresher-enabled
        :show-scrollbar="false"
        :refresher-triggered="refreshing"
      >
        <!-- 按楼层分组的巡检点区域 -->
        <view
          v-for="(floor, index) in floorData"
          :key="floor.id"
          class="floor-section"
          :id="'floor-section-' + index"
        >
          <!-- 楼层标题 -->
          <view class="section-title">{{ floor.floorName }}</view>

          <!-- 该楼层的巡检点列表 -->
          <view v-if="getFloorStations(floor.floorName).length > 0">
            <cardList
              :recordData="getFloorStations(floor.floorName)"
              @edit="onEdit"
              @del="onDel"
              @goDetail="goDetail"
            />
          </view>

          <!-- 空状态（单楼层） -->
          <BaseEmpty v-else />
        </view>

        <!-- 加载更多 -->
        <up-loadmore :status="status" />
      </scroll-view>

      <!-- 新增按钮 -->
      <AddBtn @add="onAdd()" />

      <!-- 删除确认对话框 -->
      <ConfirmDialog
        v-if="showConfirm"
        @close="showConfirm = false"
        @confirm="del"
      >
        您确定要删除此站点吗?
      </ConfirmDialog>
    </view>
    <BaseEmpty v-else />
  </SlTabbarPage>
</template>

<script lang="ts" setup>
import { ref, nextTick } from "vue";
import inspectionService from "@/subpages/inspection/api/inspection.service";
import { Station, Floor } from "@/subpages/inspection/models/inspection";
import { LoadStatus } from "@/subpages/inspection/models/inspection";
import AddBtn from "@/subpages/inspection/components/add-btn.vue";
import { uuidv4 } from "@/utils/uuid";
import cardList from "../components/card-list.vue";
import { useDebounce } from "@/hooks";
interface RawData {
  [key: string]: Station[];
}
const showConfirm = ref<boolean>(false);
const delId = ref<string>("");
const status = ref<LoadStatus>("nomore");
const isNavigateToInfo = ref<boolean>(true);
const scrollViewId = ref<string>("");

const activeFloorIndex = ref<number>(0); // 当前激活的楼层索引
const leftScrollTop = ref<number>(0); // 左侧导航滚动位置
const rightScrollTop = ref<number>(0); // 右侧内容滚动位置
const sectionTops = ref<number[]>([]); // 存储每个楼层区域的顶部位置
const leftClick = ref<boolean>(false); //判断左侧是否点击

// 数据存储
const rawData = ref<RawData[]>([]);
const floorData = ref<Floor[]>([]);

const refreshing = ref(false);
onShow(() => {
  if (!isNavigateToInfo.value) return;
  getListData();
});

// 获取数据并初始化
const getListData = async () => {
  try {
    // 调用接口获取后端数据：[{ '一层': [...], ... }]
    const res = await inspectionService.getAllFloorName();
    rawData.value = Array.isArray(res) ? res : [];

    // 解析楼层数据
    const floors: Floor[] = rawData.value.map((floorObj) => {
      const floorName = Object.keys(floorObj)[0];
      return {
        id: uuidv4(), // 生成唯一id
        floorName: floorName,
      };
    });
    floorData.value = floors;

    // 初始化选中状态
    if (floorData.value.length > 0) {
      activeFloorIndex.value = 0;
      // 数据加载完成后计算区域位置
      nextTick(() => {
        calculateSectionTops();
      });
    }
  } catch (error) {
    floorData.value = [];
    sectionTops.value = [];
  }
};
//下拉刷新
const onRefresh = () => {
  refreshing.value = true;
  getListData().finally(() => (refreshing.value = false));
};

// 计算每个楼层区域的顶部位置
const calculateSectionTops = (): void => {
  const query = uni.createSelectorQuery();
  query.selectAll(".floor-section").boundingClientRect();
  query.exec((res) => {
    if (res[0]) {
      sectionTops.value = res[0].map((item: UniApp.NodeInfo) => item.top);
    }
  });
};

// 根据楼层名获取对应的巡检点
const getFloorStations = (floorName: string): Station[] => {
  // 从rawData中找到对应楼层的站点数组
  const targetFloorObj = rawData.value.find((item) => {
    const key = Object.keys(item)[0];
    return key === floorName;
  });
  return targetFloorObj ? targetFloorObj[floorName] : [];
};

// 左侧楼层点击事件
const handleFloorClick = (index: number): void => {
  if (activeFloorIndex.value === index) return;
  leftClick.value = true;
  activeFloorIndex.value = index;
  scrollViewId.value = `floor-section-${index}`;
  setTimeout(() => {
    leftClick.value = false;
    scrollViewId.value = "";
  }, 900);
};

// 右侧滚动事件处理
const handleRightScroll = useDebounce((e: any): void => {
  const scrollTop = e.detail.scrollTop;

  // 找到当前可见的楼层区域
  for (let i = 0; i < sectionTops.value.length; i++) {
    // 判断当前滚动位置是否在第i个区域范围内
    const isLastSection = i === sectionTops.value.length - 1;
    const inCurrentSection =
      scrollTop >= sectionTops.value[i] - 46 &&
      (isLastSection || scrollTop < sectionTops.value[i + 1] - 46);

    if (inCurrentSection && activeFloorIndex.value !== i) {
      if (activeFloorIndex.value > i && leftClick.value) {
        activeFloorIndex.value = activeFloorIndex.value;
      } else {
        activeFloorIndex.value = i;
      }
      // 左侧导航滚动居中
      const navItemHeight = 40;
      const navVisibleHeight = 500;
      const scrollTo =
        i * navItemHeight - navVisibleHeight / 2 + navItemHeight / 2;
      leftScrollTop.value = Math.max(0, scrollTo);
      break;
    }
  }
}, 50);

// 编辑
const onEdit = (item: Station) => {
  uni.navigateTo({
    url: `/subpages/inspection/manage/edit-inspection?id=${item.id}`,
    success: () => {
      isNavigateToInfo.value = true; //onShow加载
    },
  });
};
// 删除
const onDel = (item: Station) => {
  showConfirm.value = true;
  delId.value = item.id;
};
const del = async () => {
  await inspectionService.deleteSite(delId.value);
  showConfirm.value = false;
  getListData();
};
// 跳转到详情页
const goDetail = (item: Station) => {
  uni.navigateTo({
    url: `/subpages/inspection/manage/detail?id=${item.id}`,
    success: () => {
      isNavigateToInfo.value = false; //onShow挡住
    },
  });
};
//跳转到新增页
const onAdd = () => {
  uni.navigateTo({
    url: `/subpages/inspection/manage/edit-inspection`,
    success: () => {
      isNavigateToInfo.value = true; //onShow加载
    },
  });
};
</script>

<style lang="scss" scoped>
.inspection-manage-page {
  display: flex;
  height: 100%;
  width: 100%;
  background: #f6f8fa;
  overflow: hidden;
}

.floor-nav {
  width: 56px;
  height: 100%;
  background: #ffffff;
  flex-shrink: 0;

  .floor-item {
    height: 40px;
    font-size: 14px;
    color: #636363;
    line-height: 40px;
    text-align: center;
    transition: all 0.2s;
    position: relative;

    &.active {
      color: #333333;
      font-weight: bold;

      &::before {
        content: "";
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: #005cc8;
        border-radius: 1.5px;
        transition: height 0.3s ease;
      }
    }
  }
}

.inspection-list {
  flex: 1;
  padding: 10px 8px 10px 12px;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

// 楼层区域样式
.floor-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style>
