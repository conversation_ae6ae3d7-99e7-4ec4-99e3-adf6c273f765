<template>
  <view
    v-for="inspection in recordData"
    :key="inspection.id"
    class="inspection-card"
    @click="goDetail(inspection)"
  >
    <view class="card-left">
      <image
        v-if="inspection.content?.storeDir"
        :src="
          baseUrl +
          '/upload/' +
          inspection.content.storeDir +
          '/' +
          inspection.content.storeName
        "
        mode="aspectFill"
        class="inspection-image"
      />
      <SlSubSvgIcon subpage="inspection" v-else name="48-48-1" size="48" />
    </view>
    <view class="card-right">
      <view class="card-header">
        <text class="position">{{ inspection.siteName }}</text>
        <view class="action-buttons" v-if="props.isEditDel">
          <view class="edit-btn" @click.stop="onEdit(inspection)">
            <SlSubSvgIcon subpage="inspection" name="16-16-36" size="15" />
          </view>
          <view class="delete-btn" @click.stop="onDel(inspection)">
            <SlSubSvgIcon subpage="inspection" name="16-16-16" size="15" />
          </view>
        </view>
        <view class="status" v-else>
          <view
            :class="
              inspection.inspectionResult == '已完成'
                ? 'finished'
                : inspection.inspectionResult == '待打卡'
                ? 'unfinish'
                : 'untime'
            "
            >{{ inspection.inspectionResult }}</view
          >
        </view>
      </view>

      <view class="card-body">
        <view class="info-row">
          <text class="info-label">区域</text>
          <text class="info-value">{{ inspection.floorName }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">站点位置</text>
          <text class="info-value">{{ inspection.sitePosition }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">备注</text>
          <text class="info-value">{{ inspection.remarks || "--" }}</text>
        </view>
        <view
          class="error-code info-row"
          v-if="inspection.siteStatus == 'abnormal'"
        >
          <view class="info-label"></view>
          <text class="error-text">二维码标签异常</text>
        </view>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { Station } from "@/subpages/inspection/models/inspection";
const baseUrl = import.meta.env.VITE_API_BASE_URL;
const recordData = ref<Station[]>([]);
const props = defineProps({
  recordData: {
    type: Array as () => Station[],
  },
  isEditDel: {
    //是否展示编辑删除按钮
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(["edit", "del", "goDetail"]);
watch(
  () => props.recordData,
  (newVal) => {
    recordData.value = newVal as Station[];
  },
  { immediate: true, deep: true }
);
const goDetail = (inspection: Station) => {
  emit("goDetail", inspection);
};
const onEdit = (inspection: Station) => {
  emit("edit", inspection);
};
const onDel = (inspection: Station) => {
  emit("del", inspection);
};
</script>
<style lang="scss" scoped>
.inspection-card {
  width: 100%;
  display: flex;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(146, 146, 146, 0.15);
  margin-bottom: 12px;
  padding: 12px;

  .card-left {
    margin-right: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .inspection-image {
      width: 48px;
      height: 48px;
      border-radius: 4px;
    }

    .left-times {
      width: 48px;
      height: 17px;
      font-size: 10px;
      line-height: 16.8px;
      text-align: center;
      border-radius: 2px;
      color: #782424;
      border: 1px solid #ebc1c1;
      background: #ffe4e4;
    }
  }

  .card-right {
    flex: 1;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .position {
        font-size: 14px;
        font-weight: bold;
        color: #333333;
      }
      .finished {
        color: #21843b;
      }
      .unfinish {
        color: #ff7605;
      }
      .untime {
        color: red;
      }
      .status {
        min-width: 32px;
        text-align: right;
        font-size: 12px;
      }

      .action-buttons {
        display: flex;

        .edit-btn,
        .delete-btn {
          margin-left: 15px;
        }
      }
    }

    .card-body {
      .info-row {
        display: flex;
        margin-bottom: 4px;
        font-size: 12px;

        .info-label {
          color: #666666;
          min-width: 60px;
        }

        .info-value {
          color: #333333;
          flex: 1;
        }
      }
      .error-code {
        display: flex;
        font-size: 12px;
        .error-text {
          color: #c93535;
          width: 99px;
          height: 23px;
          line-height: 23px;
          text-align: center;
          background: #ffe4e4;
          border-radius: 2px;
          box-sizing: border-box;
          border: 1px solid #ebc1c1;
        }
      }
    }
  }
}
</style>
