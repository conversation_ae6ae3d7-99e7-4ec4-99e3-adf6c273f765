<template>
  <view class="time">
    <!-- 时间选择触发按钮 -->
    <view class="time-select" @click="openTime">
      <view :class="{ select: timeValue }">
        {{
          timeValue
            ? props.showNextDay
              ? `次日${timeValue}`
              : timeValue
            : "请选择"
        }}
      </view>
      <view class="icon-wrap">
        <SlSubSvgIcon subpage="inspection" name="12-12-7" size="12" />
      </view>
    </view>

    <!-- 时间选择器弹窗 -->
    <view class="time-picker" v-if="isOpen">
      <view class="picker-container">
        <picker-view
          indicator-class="picker-selected"
          class="time-picker-view"
          :value="defaultIndex"
          @change="handleTimeChange"
        >
          <picker-view-column>
            <view
              class="picker-item"
              v-for="(hour, index) in hours"
              :key="index"
            >
              {{ hour }}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view
              class="picker-item"
              v-for="(minute, index) in minutes"
              :key="index"
            >
              {{ minute }}
            </view>
          </picker-view-column>
        </picker-view>
        <view class="picker-footer">
          <view class="cancel-btn" @click="closeDropdown">取消</view>
          <view class="confirm-btn" @click="onConfirm">确定</view>
        </view>
      </view>
    </view>

    <!-- 遮罩层：点击空白关闭下拉 -->
    <view class="dropdown-mask" v-if="isOpen" @click="closeDropdown" />
  </view>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from "vue";
const props = defineProps({
  // 父组件传入的默认时间，格式为 "HH:mm"
  modelValue: {
    type: String,
    default: "",
  },
  // 是否显示"次日"前缀
  showNextDay: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["update:modelValue"]);

const isOpen = ref(false); // 统一控制弹窗和遮罩显示
const timeValue = ref(""); // 选中的时间值
const defaultIndex = ref<number[]>([0, 0]); // 时间选择器索引
const hours = ref(
  Array.from({ length: 24 }, (_, i) => String(i).padStart(2, "0"))
);
const minutes = ref(
  Array.from({ length: 60 }, (_, i) => String(i).padStart(2, "0"))
);
// 解析默认值并设置初始选中项
const parseDefaultValue = async () => {
  const [hourStr, minuteStr] = timeValue.value.split(":");
  const hour = Math.min(23, Math.max(0, parseInt(hourStr) || 0));
  const minute = Math.min(59, Math.max(0, parseInt(minuteStr) || 0));

  await nextTick(() => {
    defaultIndex.value = [hour, minute];
  });
};

// 监听父组件传入的默认值变化
watch(
  () => props.modelValue,
  (newVal) => {
    timeValue.value = newVal;
  },
  { immediate: true }
);

// 打开时间选择器
const openTime = async () => {
  isOpen.value = true;
  await parseDefaultValue();
};

// 关闭选择器
const closeDropdown = () => {
  isOpen.value = false;
};

// 确认选择时间
const onConfirm = () => {
  const hour = hours.value[defaultIndex.value[0]];
  const minute = minutes.value[defaultIndex.value[1]];
  const time = `${hour}:${minute}`;
  timeValue.value = time;
  emit("update:modelValue", time);
  closeDropdown();
};

// 时间选择变化处理
const handleTimeChange = (e: { detail: { value: number[] } }) => {
  defaultIndex.value = e.detail.value;
};
</script>

<style lang="scss" scoped>
.time {
  flex: 1;
  position: relative;
  color: #999;
  width: 100%;

  .time-select {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    border: 1px solid #cacaca;
    height: 32px;
    border-radius: 3px;
    background: #fff;

    &:active {
      background-color: #f5f7fa;
    }

    .select {
      color: #333;
    }

    .icon-wrap {
      transition: transform 0.3s ease;
      transform: rotate(0deg);
    }
  }

  // 时间选择器弹窗
  .time-picker {
    position: absolute;
    z-index: 100;
    top: 34px;
    left: 0;
    right: 0;
    height: 174px;
    background: #fff;
    box-shadow: 0 2px 12px rgba(146, 146, 146, 0.3);
    border-radius: 15px;
    overflow: hidden;
    animation: fadeIn 0.2s ease-out forwards;

    .picker-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      .time-picker-view {
        flex: 1;
        height: 112px;
        .picker-item {
          height: 36px;
          line-height: 36px;
          text-align: center;
          font-size: 14px;
          color: #333;
        }
      }
    }
  }
}
// 底部按钮区域
.picker-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  border-top: 1px solid rgba(153, 153, 153, 0.0977);
  .cancel-btn,
  .confirm-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 28px;
    width: 60px;
    line-height: 28px;
    font-size: 14px;
    border-radius: 3px;
    transition: all 0.2s ease;
  }
  .cancel-btn {
    margin-right: 14px;
    border: 1px solid #4f7af6;
    color: #4f7af6;
    background: #fff;
    &:active,
    &:hover {
      background: rgba(0, 102, 223, 0.05);
    }
  }
  .confirm-btn {
    color: #fff;
    background: #0066df;
    &:active,
    &:hover {
      background: #4f7af6;
    }
  }
}
// 遮罩层样式
.dropdown-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 99; // 确保在弹窗下方
}

// 弹窗淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 选中项指示器样式
::v-deep .picker-selected {
  position: relative;
  height: 38px;
  line-height: 38px;
  &::before,
  &::after {
    content: "";
    position: absolute;
    left: 0;
    width: 100%;
    height: 1px;
  }

  &::before {
    top: 0;
  }

  &::after {
    bottom: 0;
  }
}
</style>
