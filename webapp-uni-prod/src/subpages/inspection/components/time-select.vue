<template>
  <view class="picker-container">
    <picker-view
      indicator-class="picker-selected"
      class="time-picker-view"
      :value="defaultIndex"
      @change="handleTimeChange"
    >
      <picker-view-column>
        <view class="picker-item" v-for="(hour, index) in hours" :key="index">{{
          hour
        }}</view>
      </picker-view-column>
      <picker-view-column>
        <view
          class="picker-item"
          v-for="(minute, index) in minutes"
          :key="index"
          >{{ minute }}</view
        >
      </picker-view-column>
    </picker-view>
    <view class="picker-footer">
      <view class="cancel-btn" @click="onclose()">取消</view>
      <view class="confirm-btn" @click="onConfirm()">确定</view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { ref } from "vue";
//传给父组件数据，更新
const emit = defineEmits(["close", "confirm"]);
const defaultIndex = ref<number[]>([0, 0, 0]);
const hours = ref(
  Array.from({ length: 24 }, (_, i) => String(i).padStart(2, "0"))
);
const minutes = ref(
  Array.from({ length: 60 }, (_, i) => String(i).padStart(2, "0"))
);
const handleTimeChange = (e: any) => {
  defaultIndex.value = e.detail.value;
};
const onclose = () => {
  emit("close", false);
};
const onConfirm = () => {
  const selectedHour = String(hours.value[defaultIndex.value[0]]).padStart(
    2,
    "0"
  );
  const selectedMinute = String(minutes.value[defaultIndex.value[1]]).padStart(
    2,
    "0"
  );
  emit("confirm", `${selectedHour}:${selectedMinute}`);
};
</script>
<style lang="scss" scoped>
.picker-container {
  height: 100%;
  width: 100%;
  background-color: #fff;
  border: 1px solid rgba(153, 153, 153, 0.0977);
  box-shadow: 0px 0px 6px 0px rgba(146, 146, 146, 0.2);
  border-radius: 15px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  display: flex;
  flex-direction: column;
  .time-picker-view {
    height: 112px;
    .picker-item {
      height: 36px;
      line-height: 36px;
      text-align: center;
      font-size: 14px;
      color: #333;
    }
  }
}
.picker-footer {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid rgba(153, 153, 153, 0.0977);
  .cancel-btn,
  .confirm-btn {
    height: 28px;
    width: 60px;
    line-height: 28px;
    font-size: 14px;
    border-radius: 3px;
  }
  .cancel-btn {
    margin-right: 14px;
    border: 1px solid #4f7af6;
    color: #4f7af6;
    background: #ffffff;
    &:active,
    &:hover {
      background: rgba(0, 102, 223, 0.05);
    }
  }
  .confirm-btn {
    color: #ffffff;
    background: #0066df;
    &:active,
    &:hover {
      background: #4f7af6;
    }
  }
}
::v-deep .picker-selected {
  position: relative;
  height: 38px;
  line-height: 38px;
  border-top: 1px solid rgba(153, 153, 153, 0.0977);
  &::before {
    content: "";
    position: absolute;
    top: 0px;
    border: none;
  }
}
</style>
