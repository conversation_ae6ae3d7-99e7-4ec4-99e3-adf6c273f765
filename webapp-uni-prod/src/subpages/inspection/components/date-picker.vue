<template>
  <view class="box">
    <view class="select-time" @click="show = true">
      <view :class="time ? 'time-text' : ''">
        {{ time || props.placeholder }}
      </view>
      <view>
        <SlSubSvgIcon subpage="inspection" name="14-14-4" size="14" />
      </view>
    </view>
  </view>
  <up-popup
    :show="show"
    :round="10"
    mode="bottom"
    class="popup"
    :closeOnClickOverlay="true"
  >
    <view class="popup-title">
      <view class="cancel" @click="cancel()">取消</view>
      <view class="title-text">选择时间</view>
      <view class="confirm" @click="submit">确定</view>
    </view>
    <view class="time">
      <DatePicker @change-item="onDateItem" v-model="currentDate" />
    </view>
  </up-popup>
</template>
<script setup lang="ts">
const show = ref<boolean>(false);
const currentDate = ref<string>("");
const time = ref<string>("");
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  //提示
  placeholder: {
    type: String,
    default: "请选择时间",
  },
});
const emit = defineEmits(["update:time"]);
watch(
  () => props.show,
  (val) => {
    show.value = val;
  }
);

const cancel = () => {
  show.value = false;
};
const submit = () => {
  show.value = false;
  time.value = currentDate.value;
  emit("update:time", time.value);
};

const onDateItem = (e: any) => {
  currentDate.value = e.date;
};
</script>
<style lang="scss" scoped>
.box {
  .select-time {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 185px;
    height: 32px;
    padding: 0 5px;
    font-size: 14px;
    color: #999999;
    border: 1px solid #cacaca;
    border-radius: 3px;
    .time-text {
      color: #333333;
    }
  }
}

.popup-title {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 14px;
  padding: 15px 25px;
  color: #333333;
  border-bottom: 1px solid rgba(153, 153, 153, 0.0977);
  .title-text {
    font-weight: 600;
  }
  .confirm {
    font-weight: 350;
    color: #005cc8;
  }
}
</style>
