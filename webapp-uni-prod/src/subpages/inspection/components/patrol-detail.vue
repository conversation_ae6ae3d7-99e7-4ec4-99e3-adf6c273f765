<template>
  <view class="container">
    <view class="inspection">
      <view class="inspection-item">
        <view class="item-label"> 站点名称 </view>
        <view>{{ inspectionDetail.siteName }}</view>
      </view>
      <view class="inspection-item">
        <view class="item-label">站点编号</view>
        <view>{{ inspectionDetail.siteNumber }}</view>
      </view>
      <view class="inspection-item">
        <view class="item-label">区域</view>
        <view>{{ inspectionDetail.floorName }}</view>
      </view>
      <view class="inspection-item">
        <view class="item-label">点位位置</view>
        <view>{{ inspectionDetail.sitePosition }}</view>
      </view>
      <view class="inspection-item">
        <view class="item-label">图片</view>
        <view></view>
      </view>
      <view class="inspection-item">
        <view class="item-label">备注</view>
        <view>{{
          inspectionDetail.remarks ? inspectionDetail.remarks : "--"
        }}</view>
      </view>
    </view>
    <view class="recent-record">
      <view class="recent-title"
        ><span class="split-line"></span>最近巡检记录</view
      >
      <view
        class="recent-item"
        v-for="(item, index) in recentRecord"
        :key="item.id"
        @click="onJumpRec(item.id)"
        v-if="recentRecord.length > 0"
      >
        <view class="recent-times">第{{ index + 1}}次</view>
        <view class="recent-time">{{ item.inspectionTime }}</view>
        <view
          :class="
            item.inspectionResult == 'success'
              ? 'recent-status normal'
              : ' recent-status unsual'
          "
          >{{ item.inspectionResult == "success" ? "正常" : "异常" }}</view
        </view>
        <view>
          <SlSubSvgIcon subpage="inspection" name="12-12-6" size="12" />
        </view>
      </view>
      <view v-else class="empty">
        <BaseEmpty></BaseEmpty>
      </view>
    </view>
    <view class="recent-record" v-if="detailType == 'inspection'">
      <view class="recent-title"><span class="split-line"></span>其他</view>
      <view
        class="unusal-item"
        v-for="item in otherRecord"
        :key="item.id"
        v-if="otherRecord.length"
      >
        <view class="recent-time">{{ item.inspectionTime }}</view>
        <view class="recent-name">{{ item.inspectionMan }}</view>
        <view class="error-status">二维码标签异常</view>
      </view>
      <view v-else class="empty">
        <BaseEmpty></BaseEmpty>
      </view>
    </view>
    <view class="recent-record code-error" v-if="detailType == 'station'">
      <view class="code-label">二维码标签异常</view>
      <view class="code-choose">
        <up-switch v-model="codeErrorValue" @change="codeChange"></up-switch>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import {
  Station,
  InspectionDetail,
} from "@/subpages/inspection/models/inspection";
type DetailType = "station" | "inspection";
const props = defineProps({
  type: {
    type: String as () => DetailType,
    default: "",
  },
  options: {
    type: Object as () => Station,
    default: () => ({}),
  },
});
const detailType = ref<DetailType>("station");
const codeErrorValue = ref(false);
//最近巡检记录
const recentRecord = ref([] as InspectionDetail[]);
//其他二维码异常
const otherRecord = ref([] as InspectionDetail[]);
const inspectionDetail = ref({
  siteName: "",
  siteNumber: "",
  sitePosition: "",
  floorName: "",
  remarks: "",
} as Station);
//接收巡检记录传过来的值
onMounted(() => {
  console.log(props.options, "子组件拿到options");
  getData();
  detailType.value = props.type;
});
const getData = () => {
  console.log(props.options, "子组件拿到options");
  const options = props.options;
  inspectionDetail.value = options as Station;
  //根据siteStatus字段判断是否是巡检记录的数据
  // abnormal的 是‘其他’这个块里的数据，否则就是‘最近巡检记录’这块的数据
  const list = options.inspectionrecordList as InspectionDetail[];
  //循环list
  list.forEach((item) => {
    if (item.siteStatus == "abnormal") {
      otherRecord.value.push(item);
    } else {
      recentRecord.value.push(item);
    }
  });
};
//跳转到巡检记录详情
const onJumpRec = (id: string) => {
  uni.navigateTo({
    url: `/subpages/inspection/record/detail/index?id=${id}`,
  });
};
const codeChange = (val: boolean) => {
  codeErrorValue.value = val;
  console.log(codeErrorValue.value);
};
</script>
<style lang="scss" scoped>
/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  background: #f6f8fa;
}
.split-line {
  display: inline-block;
  width: 2px;
  height: 13px;
  background: #1a58b2;
  margin-right: 4px;
}
.inspection {
  font-size: 14px;
  padding: 2px 15px;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  background: #fff;
  .inspection-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 12px 0;
    .item-label {
      color: #666666;
    }
  }
}
.recent-record {
  padding-top: 14px;
  margin: 10px 0;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  background: #fff;
  .recent-title {
    font-size: 14px;
    font-weight: 500;
    margin-left: 8px;
  }
  .recent-item,
  .unusal-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    padding: 10px 13px;
    gap: 5px;
    .recent-times {
      width: 50px;
      height: 25px;
      line-height: 25px;
      text-align: center;
      border-radius: 13px;
      background: #dbecff;
    }
    .recent-time {
    }
    .recent-status {
    }
    .recent-name {
      width: 70px;
      text-align: center;
    }
    .error-status {
      width: 100px;
      height: 23px;
      font-size: 12px;
      line-height: 23px;
      text-align: center;
      border-radius: 2px;
      color: #c93535;
      border: 1px solid #ebc1c1;
      background: #ffe4e4;
    }
  }
  //偶数背景色变化
  .recent-item:nth-child(2n) {
    background: #f9fcff;
  }
  .normal {
    color: #487824;
  }
  .unsual {
    color: #792322;
  }
}
.code-error {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 10px 15px;
  .code-label {
    font-size: 14px;
    color: #333333;
  }
}
.empty {
  padding: 20px 0;
}
</style>
