<template>
  <up-overlay :show="show" :zIndex="10">
    <view class="warp">
      <view class="rect" @tap.stop>
        <view class="title">动态二维码校时</view>
        <view class="time"
          >剩余时间<text style="color: #e50101">{{ timer }}</text
          >s</view
        >
        <view class="desc">
          <view> 11 </view>
          <view class="desc-text">
            请勿关闭界面,倒计时剩10秒时,去点亮二维码设备屏幕,倒计时结束前重新扫码完成校时
          </view>
        </view>
      </view>
    </view>
  </up-overlay>
</template>
<script lang="ts" setup>
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});
const show = ref(true);
onShow(() => {
  if (show.value) {
    setInterval(countDown, 1000);
    show.value = props.show;
  }
});
//添加倒计时计时器
const timer = ref(15);
const countDown = () => {
  if (timer.value > 0) {
    timer.value--;
  } else {
    timer.value = 15;
    show.value = false;
  }
};
</script>
<style lang="scss" scoped>
.warp {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  .rect {
    width: 290px;
    height: 170px;
    background: #ffffff;
    border-radius: 10px;
    padding: 0 28px;
    text-align: center;
    .title {
      font-size: 16px;
      font-weight: 700;
      margin: 17px;
    }
    .time {
      font-size: 14px;
      color: #333333;
      margin-bottom: 25px;
    }
    .desc {
      display: flex;
      flex-direction: row;
      .desc-text {
        font-size: 12px;
        color: #666666;
        text-align: left;
      }
    }
  }
}
</style>
