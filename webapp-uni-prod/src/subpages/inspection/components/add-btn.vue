<template>
  <view>
    <SlSubSvgIcon
      subpage="inspection"
      name="52-52-4"
      size="52"
      @click="onAdd()"
      class="add-btn"
    />
  </view>
</template>
<script lang="ts" setup>
const emit = defineEmits(["add"]);
const onAdd = () => {
  emit("add");
};
</script>
<style scoped lang="scss">
.add-btn {
  position: fixed;
  bottom: calc(89px + env(safe-area-inset-bottom));
  right: 14px;
  z-index: 999;
}
</style>
