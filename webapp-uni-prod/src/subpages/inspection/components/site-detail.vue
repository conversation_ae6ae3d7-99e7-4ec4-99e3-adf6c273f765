<template>
  <view class="inspection">
    <view class="inspection-item">
      <view class="item-label">站点名称</view>
      <view>{{ station.siteName }}</view>
    </view>
    <view class="inspection-item">
      <view class="item-label">设备MAC</view>
      <view class="item-value">{{
        station.macAddr ? station.macAddr : "--"
      }}</view>
    </view>
    <view class="inspection-item">
      <view class="item-label">设备编码</view>
      <view>{{ station.siteNumber }}</view>
    </view>
    <view class="inspection-item">
      <view class="item-label">区域</view>
      <view>{{ station.floorName }}</view>
    </view>
    <view class="inspection-item">
      <view class="item-label">站点位置</view>
      <view>{{ station.sitePosition }}</view>
    </view>
    <view class="inspection-item">
      <view class="item-label">站点图片</view>
      <view class="img">
        <image
          v-if="station.content?.storeDir"
          :src="
            baseUrl +
            '/upload/' +
            station.content.storeDir +
            '/' +
            station.content.storeName
          "
          mode="aspectFill"
          class="inspection-image"
          @click="onPreviewImage(station.content)"
        />
        <SlSubSvgIcon subpage="inspection" v-else name="48-48-1" size="32" />
      </view>
    </view>
    <view class="inspection-item">
      <view class="item-label">备注</view>
      <view>{{ station.remarks ? station.remarks : "--" }}</view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import {
  Station,
  StationDetail,
} from "@/subpages/inspection/models/inspection";
const baseUrl = import.meta.env.VITE_API_BASE_URL;
const station = ref({
  siteName: "",
  siteNumber: "",
  sitePosition: "",
  floorName: "",
  remarks: "",
  macAddr: "",
} as Station);
const props = defineProps({
  siteInfo: {
    type: Object,
    default: () => <Station>{},
  },
});
watch(
  () => props.siteInfo,
  (newVal) => {
    station.value = newVal as Station;
  },
  { immediate: true, deep: true }
);
const onPreviewImage = (item: StationDetail) => {
  const url = `${baseUrl}/upload/${item.storeDir}/${item.storeName}`;
  uni.previewImage({
    current: url,
    urls: [url],
  });
};
</script>
<style lang="scss" scoped>
.inspection {
  font-size: 14px;
  padding: 2px 15px;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  background: #fff;
  .inspection-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 12px 0;
    .item-label {
      min-width: 100px;
      color: #666666;
    }
    .item-value {
      text-align: right;
    }
    .img {
      .inspection-image {
        width: 32px;
        height: 32px;
        border-radius: 4px;
      }
    }
  }
}
</style>
