<template>
  <view class="container">
    <view class="header">
      <view class="top" v-if="isHasData">
        <view class="top-title">
          <view class="title-left">
            <view class="title-circle"></view>
            <view class="title-text">{{ config.taskName }}</view>
          </view>
          <view class="title-right" @click="open()">
            <view class="right-text">{{ expendValue ? "收起" : "展开" }}</view>
            <view class="rotate">
              <SlSubSvgIcon subpage="inspection" name="12-12-7" size="12" />
            </view>
          </view>
        </view>
        <view class="top-content" v-show="expendValue">
          <view class="content-item">
            <view class="item-label">班组</view>
            <view class="item-value">{{ config.inspectionTeamName }}</view>
          </view>
          <view class="content-item">
            <view class="item-label">打卡日期</view>
            <view class="item-value">{{ config.inspectionDay }}</view>
          </view>
          <view class="content-item">
            <view class="item-label">打卡时间</view>
            <view class="item-value"
              >{{ config.startTime }} -
              {{
                config.endTime <= config.startTime
                  ? "次日" + config.endTime
                  : config.endTime
              }}</view
            >
          </view>
          <view class="content-item">
            <view class="item-label">打卡次数</view>
            <view class="item-value">
              {{ config.inspectionFrequency }}<text>次</text>
            </view>
          </view>
          <view class="content-item">
            <view class="item-label">每次至少间隔</view>
            <view class="item-value">
              {{ config.inspectionInterval }}<text>小时</text>
            </view>
          </view>
          <view class="content-item">
            <view class="item-label">备注</view>
            <view class="item-value">{{ config.remarks || "--" }}</view>
          </view>
        </view>
      </view>
      <view class="date">
        <CalendarComponent
          v-model="currDate"
          @dateChange="onDateChange"
          :date-data="backendDateData"
        />
      </view>
    </view>
    <view v-if="isHasData">
      <view
        :class="['main-container', expendValue ? 'main-expend' : 'main-put']"
      >
        <!-- 左侧楼层导航 -->
        <scroll-view scroll-y class="left-nav" :scroll-top="leftScrollTop">
          <view
            v-for="(floor, index) in floorData"
            :key="floor.id"
            class="nav-item"
            :class="{ active: activeFloorIndex === index }"
            @click="handleFloorClick(index, floor)"
          >
            {{ floor.floorName }}
          </view>
        </scroll-view>

        <!-- 右侧内容区域 -->
        <scroll-view
          scroll-y
          class="right-content"
          :scroll-top="rightScrollTop"
          @scroll="handleRightScroll"
          scroll-with-animation
          :scroll-into-view="scrollViewId"
          @refresherrefresh="onRefresh()"
          refresher-enabled
          :show-scrollbar="false"
          :refresher-triggered="refreshing"
        >
          <view
            v-for="(floor, index) in floorData"
            :key="floor.id"
            class="floor-section"
            :id="'floor-section-' + index"
          >
            <view class="section-title">{{ floor.floorName }}</view>

            <view v-if="getFloorData(floor.floorName).length > 0">
              <cardList
                :recordData="getFloorData(floor.floorName)"
                :is-edit-del="false"
                @go-detail="onJump"
              />
            </view>
            <BaseEmpty v-else />
          </view>
        </scroll-view>
      </view>
    </view>

    <view class="empty" v-else>
      <BaseEmpty>今日无需打卡</BaseEmpty>
    </view>

    <!-- 扫码图标 -->
    <view class="sceen" v-if="isHasData && hasUnClockData">
      <OpenScan />
    </view>
    <view v-else class="sceen">
      <SlSubSvgIcon subpage="inspection" name="52-52-9" size="45" />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, getCurrentInstance } from "vue";
import {
  Floor,
  InspectionParams,
  Station,
  ConfigData,
  StationResponse,
  DateRes,
} from "../models/inspection";
import inspectionService from "@/subpages/inspection/api/inspection.service";
import { useInspectionStore } from "@/subpages/inspection/utils/stores";
import dayjs from "dayjs";
import OpenScan from "./open-scan.vue";
import { uuidv4 } from "@/utils/uuid";
import cardList from "./card-list.vue";
import SlSubSvgIcon from "@/components/SlSubSvgIcon.vue";
import CalendarComponent from "@/components/CalendarComponent.vue";
import { useDebounce } from "@/hooks";

type Title = "白班巡检" | "夜班巡检" | "保洁清洁" | "物业巡检";
type TeamType = "white" | "night" | "property" | "clean";
const props = defineProps({
  title: {
    type: String as () => Title,
    default: "白班巡检",
  },
  teamType: {
    type: String as () => TeamType,
  },
  list: {
    type: Array,
    default: () => [],
  },
});
const storeInspection = useInspectionStore();
const floorData = ref<Floor[]>([]);
const queryRecord = reactive<InspectionParams>({
  queryDate: "",
  loginUserRole: "workers",
});
const expendValue = ref<boolean>(false);
const isHasData = ref<boolean>(true);
const backendDateData = ref<DateRes[]>([]);
const currDate = ref(dayjs().format("YYYY-MM-DD"));
const backDate = ref(dayjs().format("YYYYMMDD"));
// 是否有待打卡数据
const hasUnClockData = ref<boolean>(false);
const patrolData = ref<Station[]>([]);
const config = ref<ConfigData>({
  taskName: "",
  inspectionTeamId: "",
  inspectionTeamName: "",
  siteId: "",
  siteName: "",
  remarks: "",
  inspectionDay: "",
  startTime: "",
  endTime: "",
  inspectionFrequency: 0,
  inspectionInterval: 0,
});
const list = ref<StationResponse>({}); //存储接口返回的数据
// 联动
const activeFloorIndex = ref<number>(0);
const leftScrollTop = ref<number>(0);
const rightScrollTop = ref<number>(0);
const sectionTops = ref<number[]>([]);
const scrollViewId = ref<string>("");
const instance = getCurrentInstance();

// 滑动距离顶部的距离
const headerHeight = ref<number>(0);
const leftClick = ref<boolean>(false); //判断是否是左侧点击
const refreshing = ref<boolean>(false);

onLoad(() => {
  getDate();
});
onShow(() => {
  getConfig();
});

const getPatrolList = async () => {
  try {
    const res = await inspectionService.getRecordList(queryRecord);
    if (typeof res === "object") {
      if (Object.keys(res).length === 0) {
        isHasData.value = false;
        patrolData.value = [];
        floorData.value = [];
      } else {
        isHasData.value = true;
        const floors = Object.keys(res);
        floorData.value = floors.map((floor) => ({
          floorName: floor,
          id: uuidv4(),
        }));
        activeFloorIndex.value = 0;
        list.value = res;

        nextTick(() => {
          getHeaderHeight();
          calculateSectionTops();
        });
        getFloorData(floorData.value[0]?.floorName || "");
        // 将所有楼层的记录合并成一个数组
        const allRecords = floors.reduce(
          (acc, floor) => acc.concat(res[floor]),
          [] as Station[]
        );
        storeInspection.inspectionRecordList = allRecords;
        // 判断是否有代打卡数据
        hasUnClockData.value = allRecords.some(
          (item) => item.inspectionResult === "待打卡"
        );
      }
    } else {
      resetData();
    }
  } catch (error) {
    resetData();
  }
};

const getDate = async () => {
  try {
    queryRecord.queryDate = currDate.value;
    const res = await inspectionService.getDate();
    backendDateData.value = res;
  } catch (error) {
    backendDateData.value = [];
  }
};
const onDateChange = (date: string) => {
  if (queryRecord.queryDate === date) return;
  currDate.value = dayjs(date).format("YYYY-MM-DD");
  backDate.value = dayjs(date).format("YYYY-MM-DD");
  queryRecord.queryDate = date;
  getConfig();
};
const getConfig = async () => {
  try {
    const res = await inspectionService.getInspectionConfig(currDate.value);
    if (res) {
      config.value = res;
      //将config对象的1,2,3,4,5,6,7转换为一|二|三|四|五|六|日
      const weekDays = ["一", "二", "三", "四", "五", "六", "日"];
      config.value.inspectionDay = config.value.inspectionDay
        .split(",")
        .map((day) => weekDays[parseInt(day) - 1])
        .join(" | ");
      if (config.value.inspectionDay == "一 | 二 | 三 | 四 | 五 | 六 | 日") {
        config.value.inspectionDay = "每天";
      }
      //获取站点数据
      await getPatrolList();
    }
  } catch (error) {
    config.value = {} as ConfigData;
    resetData();
  }
};
//下拉刷新
const onRefresh = () => {
  refreshing.value = true;
  getConfig().finally(() => (refreshing.value = false));
};
// 重置
const resetData = () => {
  isHasData.value = false;
  patrolData.value = [];
  floorData.value = [];
};

const getFloorData = (floorName: string): Station[] => {
  const data = list.value[floorName] || [];
  patrolData.value = data;
  return data;
};
const onJump = (item: Station) => {
  storeInspection.inspectionRecord = item;
  uni.navigateTo({
    url: `/subpages/inspection/record/inspection-detail/index?type=patrol`,
  });
};
const open = () => {
  expendValue.value = !expendValue.value;
  nextTick(() => {
    getHeaderHeight();
  });
};

const handleFloorClick = (index: number, floor: Floor) => {
  if (activeFloorIndex.value === index) return;
  leftClick.value = true;
  activeFloorIndex.value = index;
  scrollViewId.value = `floor-section-${index}`;
  console.log("scrollViewId", scrollViewId.value);
  //过段时间将leftClick转换为false
  setTimeout(() => {
    leftClick.value = false;
    scrollViewId.value = "";
  }, 900);
};

const calculateSectionTops = () => {
  if (floorData.value.length === 0) return;

  const query = uni.createSelectorQuery().in(instance);
  query.selectAll(".floor-section").boundingClientRect((sections: any) => {
    if (sections) {
      sectionTops.value = sections.map(
        (item: UniApp.NodeInfo) => item.top! - headerHeight.value
      );
    }
  });
  query.exec();
};

//获取头部的高度
const getHeaderHeight = (): void => {
  const query = uni.createSelectorQuery().in(instance);
  query.select(".header").boundingClientRect();
  query.exec((res) => {
    if (res[0]) {
      headerHeight.value = res[0].height;
    }
  });
};

const handleRightScroll = useDebounce((e: any) => {
  const scrollTop = e.detail.scrollTop;

  for (let i = 0; i < sectionTops.value.length; i++) {
    const isLast = i === sectionTops.value.length - 1;
    const inCurrent =
      scrollTop >= sectionTops.value[i] - 46 &&
      (isLast || scrollTop < sectionTops.value[i + 1] - 46);
    if (inCurrent && activeFloorIndex.value !== i) {
      //防止左侧点击时，右侧滚动影响左侧
      if (activeFloorIndex.value > i && leftClick.value) {
        activeFloorIndex.value = activeFloorIndex.value;
      } else {
        activeFloorIndex.value = i;
      }

      // 左侧导航居中
      const navItemHeight = 40;
      const navVisibleHeight = 500;
      leftScrollTop.value = Math.max(
        0,
        i * navItemHeight - navVisibleHeight / 2 + navItemHeight / 2
      );
      break;
    }
  }
}, 50);
</script>
<style lang="scss" scoped>
/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background: #f6f8fa;
  overflow: hidden;
}
.header {
  .date {
    // overflow: scroll;
    background: #fdfdfd;
    box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  }
  .top {
    margin-bottom: 10px;
    //   flex: 1;
    background: #fff;
    box-shadow: 0px 0px 10px 0px rgba(146, 146, 146, 0.3);
    .top-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 15px 15px 15px 8px;
      height: 40px;
      line-height: 40px;
      .title-left,
      .title-right {
        display: flex;
        flex-direction: row;
        align-items: center;
      }
      .title-left {
        .title-circle {
          width: 8px;
          height: 8px;
          background: #035ec9;
          border-radius: 50%;
        }
        .title-text {
          font-size: 14px;
          color: #333333;
          font-weight: 700;
          margin-left: 5px;
        }
      }
      .title-right {
        font-size: 12px;
        color: #666666;
        .right-text {
          margin-right: 5px;
        }
      }
    }
    .top-content {
      padding: 0 15px;
      height: 138px;
      .content-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        .item-label {
          width: 112px;
          color: #666666;
        }
        .item-value {
          flex: 1;
          color: #333333;
          width: calc(100% - 112px);
          //超过一行显示省略号
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

/* 主体内容容器 */
.main-container {
  flex: 1;
  display: flex;
  margin-top: 10px;
  overflow: hidden; /* 避免外层滚动 */
  // height: calc(100vh - 70px - 40px - 20px);
  box-sizing: border-box;
}
.main-expend {
  height: calc(100vh - 70px - 178px - 20px);
}
.main-put {
  height: calc(100vh - 70px - 40px - 20px);
}

/* 左侧导航样式 */
.left-nav {
  width: 54px;
  height: 100%;
  background: #fff;
}

.nav-item {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #636363;

  &.active {
    position: relative;
    color: #333333;
    background: #fff;
    font-weight: bold;
    // border-left: 3px solid #005CC8;
    &::before {
      display: inline-block;
      width: 3px;
      height: 20px;
      background: #005cc8;
      content: "";
      position: absolute;
      top: 11px;
      left: 5px;
      transition: height 0.3s ease;
    }
  }
}

/* 右侧内容区域 */
.right-content {
  height: 100%;
  box-sizing: border-box;
  margin: 0 10px;
  padding-bottom: 10px;
}
.sceen {
  position: fixed;
  bottom: 70px;
  right: 15px;
  z-index: 599;
}
.empty {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
}
// 楼层区域样式
.floor-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }
}
</style>
