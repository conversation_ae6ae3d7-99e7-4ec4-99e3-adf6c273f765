<template>
  <view class="container">
    <view class="header">
      <view class="top" v-if="isHasData">
        <view class="top-title">
          <view class="title-left">
            <view class="title-circle"></view>
            <view class="title-text">{{ config.taskName }}</view>
          </view>
          <view class="title-right" @click="open()">
            <view class="right-text">{{ expendValue ? "收起" : "展开" }}</view>
            <view class="rotate">
              <SlSubSvgIcon subpage="inspection" name="12-12-7" size="12" />
            </view>
          </view>
        </view>
        <view class="top-content" v-show="expendValue">
          <view class="content-item">
            <view class="item-label">班组</view>
            <view class="item-value">{{ config.inspectionTeamName }}</view>
          </view>
          <view class="content-item">
            <view class="item-label">打卡日期</view>
            <view class="item-value">{{ config.inspectionDay }}</view>
          </view>
          <view class="content-item">
            <view class="item-label">打卡时间</view>
            <view class="item-value"
              >{{ config.startTime }} -
              {{
                config.endTime <= config.startTime
                  ? "次日" + config.endTime
                  : config.endTime
              }}</view
            >
          </view>
          <view class="content-item">
            <view class="item-label">打卡次数</view>
            <view class="item-value"
              >{{ config.inspectionFrequency }}<text>次</text></view
            >
          </view>
          <view class="content-item">
            <view class="item-label">每次至少间隔</view>
            <view class="item-value"
              >{{ config.inspectionInterval }}<text>小时</text></view
            >
          </view>
          <view class="content-item">
            <view class="item-label">备注</view>
            <view class="item-value">{{
              config.remarks ? config.remarks : "--"
            }}</view>
          </view>
        </view>
      </view>
      <view class="date">
        <CalendarComponent
          v-model="currDate"
          @dateChange="onDateChange"
          :date-data="backendDateData"
        />
      </view>
    </view>
    <view v-if="isHasData">
      <view
        :class="['main-container', expendValue ? 'main-expend' : 'main-put']"
      >
        <!-- 左侧楼层导航 -->
        <scroll-view scroll-y class="left-nav">
          <view
            v-for="(item, index) in floorData"
            :key="index"
            class="nav-item"
            :class="{ active: activeValue == item.floorName }"
            @click="onChange(item)"
          >
            {{ item.floorName }}
          </view>
        </scroll-view>

        <!-- 右侧内容区域 -->
        <scroll-view scroll-y class="right-content" @scroll="onRightScroll">
          <view
            class="item-list"
            v-for="(item, idx) in patrolData"
            :key="idx"
            v-if="patrolData.length > 0"
          >
            <view class="item" @click="onJump(item)">
              <!-- <image :src="item.image" class="product-img" mode="aspectFill" />
                <text class="product-name">{{ item.name }}</text>
                <text class="product-price">¥{{ item.price }}</text> -->
              <view class="item-left">
                <image
                  v-if="item.content?.storeDir"
                  :src="
                    baseUrl +
                    '/upload/' +
                    item.content.storeDir +
                    '/' +
                    item.content.storeName
                  "
                  mode="aspectFill"
                  class="inspection-image"
                />
                <SlSubSvgIcon
                  subpage="inspection"
                  v-else
                  name="48-48-1"
                  size="48"
                />
              </view>
              <view class="item-right">
                <view class="title">
                  <view class="position">{{ item.siteName }}</view>
                  <view
                    :class="
                      item.inspectionResult === '已完成'
                        ? 'finished'
                        : item.inspectionResult === '未完成'
                        ? 'untime'
                        : 'unfinish'
                    "
                    >{{ item.inspectionResult }}</view
                  >
                </view>
                <view class="right-desc">
                  <view class="label">区域</view>
                  <view class="value">{{ item.floorName }}</view>
                </view>
                <view class="right-desc">
                  <view class="label">站点位置</view>
                  <view class="value">{{ item.sitePosition }}</view>
                </view>
                <view class="right-desc">
                  <view class="label">备注</view>
                  <view class="value">{{
                    item.remarks ? item.remarks : "--"
                  }}</view>
                </view>
                <view class="right-desc" v-if="item.siteStatus === 'abnormal'">
                  <view class="label"></view>
                  <view class="value code-error">二维码标签异常</view>
                </view>
              </view>
            </view>
            <!-- 扫码图标 -->
            <view class="sceen" v-if="isHasData">
              <OpenScan />
            </view>
            <view v-else class="sceen">
              <SlSubSvgIcon subpage="inspection" name="52-52-9" size="45" />
            </view>
          </view>
          <BaseEmpty v-else></BaseEmpty>
        </scroll-view>
      </view>
    </view>
    <view class="empty" v-else>
      <BaseEmpty>今日无需打卡</BaseEmpty>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import {
  Floor,
  InspectionParams,
  Station,
  ConfigData,
} from "../models/inspection";
import inspectionService from "@/subpages/inspection/api/inspection.service";
import { useInspectionStore } from "@/subpages/inspection/utils/stores";
import dayjs from "dayjs";
import OpenScan from "./open-scan.vue";
import { getCurrentInstance } from "vue";

const instance = getCurrentInstance();

type Title = "白班巡检" | "夜班巡检" | "保洁清洁" | "物业巡检";
type TeamType = "white" | "night" | "property" | "clean";
const props = defineProps({
  title: {
    type: String as () => Title,
    default: "白班巡检",
  },
  teamType: {
    type: String as () => TeamType,
  },
  list: {
    type: Array,
    default: () => [],
  },
});
const storeInspection = useInspectionStore();
const baseUrl = import.meta.env.VITE_API_BASE_URL;
// 楼层数据
const floorData = ref([] as Floor[]);
const queryRecord = reactive<InspectionParams>({
  queryDate: "",
  loginUserRole: "workers",
});
const expendValue = ref<boolean>(false);
const activeValue = ref("");
const isHasData = ref(true); //判断某个日期是否有打卡数据
const backendDateData = ref([]);
const currDate = ref(dayjs().format("YYYY-MM-DD"));
const backDate = ref(dayjs().format("YYYYMMDD"));

const patrolData = ref([] as Station[]);
const config = ref({} as ConfigData);

const scrollTop = ref(0);
const scrollTimer = ref<any>(null);
const loadMoreTimer = ref<any>(null);
onLoad((options: any) => {
  getDate();
  getFloorList();
  getConfig();
});
onShow(() => {
  getPatrolList();
});

const getFloorList = async () => {
  const res = await inspectionService.getSiteAreaList({
    ifPage: false,
  });
  // 确保 res 是数组
  if (Array.isArray(res)) {
    floorData.value = res.map((item: Floor) => ({
      ...item,
    }));
    //将activeValue赋值给第一个楼层的id
    activeValue.value = floorData.value[0].floorName;
  } else {
    floorData.value = [];
  }
};
const getPatrolList = async () => {
  const res = await inspectionService.getRecordList(queryRecord);
  //判断res是否为对象
  if (typeof res === "object") {
    //判断res对象是否有数据
    if (Object.keys(res).length == 0) {
      isHasData.value = false;
      patrolData.value = [];
    } else {
      isHasData.value = true;
      //返回的res取所有楼层的对应的数据，存到store里面
      const floors = Object.keys(res);
      // 将所有楼层的记录合并成一个数组
      const allRecords = floors.reduce((acc, floor) => {
        return acc.concat(res[floor]);
      }, [] as Station[]);
      storeInspection.inspectionRecordList = allRecords;

      //根据接口返回数据找到当前楼层对应的数据
      const floor = res[activeValue.value];
      if (floor) {
        patrolData.value = floor;
      } else {
        patrolData.value = [];
      }
    }
  } else {
    patrolData.value = [];
    isHasData.value = false;
  }

  // 检查是否需要加载更多数据
  nextTick(() => {
    checkIfNeedLoadMore();
  });
};
// 检查是否需要加载更多数据
const checkIfNeedLoadMore = () => {
  if (!isHasData.value || !patrolData.value.length) return;

  clearTimeout(loadMoreTimer.value);

  loadMoreTimer.value = setTimeout(() => {
    // 1. 创建查询并绑定组件实例
    const query = uni.createSelectorQuery().in(instance);

    // 2. 定义查询操作（获取右侧内容区高度和滚动位置）
    query.select(".right-content").boundingClientRect((contentRect: any) => {
      // 右侧内容区的位置信息
    });
    query.selectViewport().scrollOffset((scrollRes: any) => {
      // 视口滚动信息
    });

    // 3. 关键：调用 exec() 触发查询
    query.exec((res: any[]) => {
      // res[0] 对应第一个查询（.right-content 的 rect）
      // res[1] 对应第二个查询（视口滚动信息）
      const contentRect = res[0];
      const scrollRes = res[1];

      console.log("内容区域:", contentRect);
      console.log("滚动位置:", scrollRes);

      if (!contentRect || !scrollRes) return;

      const contentHeight = contentRect.height;
      const viewHeight = uni.getSystemInfoSync().windowHeight;

      // 内容高度不足时加载下一层
      if (contentHeight < viewHeight * 1.2) {
        const currentIndex = floorData.value.findIndex((f) => {
          console.log("加载了下一页了吗", f);
          // f.floorName === activeValue.value
        });
        if (currentIndex > -1 && currentIndex + 1 < floorData.value.length) {
          const nextFloor = floorData.value[currentIndex + 1].floorName;
          if (nextFloor !== activeValue.value) {
            onChangeFloor(nextFloor);
          }
        }
      }
    });
  }, 300);
};

// 右侧滚动事件 - 实现左右联动
const onRightScroll = (e: any) => {
  clearTimeout(scrollTimer.value);
  scrollTimer.value = setTimeout(() => {
    // 1. 创建查询并绑定组件实例（注意：setup 中用 instance 而非 this）
    const query = uni.createSelectorQuery().in(instance);

    // 2. 定义查询操作（获取所有 item-list 的位置和视口滚动位置）
    query.selectAll(".item-list").boundingClientRect(); // 获取右侧所有项的位置
    query.selectViewport().scrollOffset((scrollRes: any) => {
      // 视口滚动信息
    });

    // 3. 关键：调用 exec() 触发查询
    query.exec((res: any[]) => {
      // res[0] 对应所有 .item-list 的 rect 数组
      // res[1] 对应视口滚动信息
      const rects = res[0];
      const scrollRes = res[1];

      if (!rects || !scrollRes) return;

      const scrollTop = scrollRes.scrollTop;

      // 匹配当前滚动位置对应的楼层
      for (let i = 0; i < rects.length; i++) {
        const rect = rects[i];
        // 判断当前项是否在可视区域内（顶部距离 <= 滚动位置 + 20）
        if (
          scrollTop >= rect.top - 20 &&
          scrollTop < rect.top + rect.height - 20
        ) {
          const floorName = patrolData.value[i]?.floorName;
          if (floorName && activeValue.value !== floorName) {
            activeValue.value = floorName; // 更新左侧激活状态
          }
          break;
        }
      }
    });
  }, 100);
};
const onChangeFloor = (floorName: string) => {
  console.log("改变楼层了吗", "000");
  // if (activeValue.value === floorName) return;

  // activeValue.value = floorName;
  getPatrolList();

  // 滚动到对应位置的逻辑
  nextTick(() => {
    const index = floorData.value.findIndex((f) => f.floorName === floorName);
    if (index > -1) {
      const query = uni.createSelectorQuery();
      query.select("#floor-" + index).boundingClientRect();
      query.selectViewport().scrollOffset(function (res) {
        console.log(res, "是否滚动");
      });
      query.exec((res) => {
        if (res[0]) {
          scrollTop.value = scrollTop.value === 0 ? 1 : 0; // 强制触发滚动
          nextTick(() => {
            scrollTop.value = res[0].top + res[1].scrollTop - 80;
          });
        }
      });
    }
  });
};
const getDate = async () => {
  //将queryRecord.queryDate赋值为当前日期
  queryRecord.queryDate = currDate.value;
  const res = await inspectionService.getDate();
  backendDateData.value = res;
};
const onDateChange = (date: string) => {
  currDate.value = dayjs(date).format("YYYY-MM-DD");
  backDate.value = dayjs(date).format("YYYY-MM-DD");
  queryRecord.queryDate = date;
  getPatrolList();
};
const getConfig = async () => {
  const res = await inspectionService.getInspectionConfig();
  config.value = res as ConfigData;
  //将config对象的1,2,3,4,5,6,7转换为一|二|三|四|五|六|日
  const weekDays = ["一", "二", "三", "四", "五", "六", "日"];
  config.value.inspectionDay = config.value.inspectionDay
    .split(",")
    .map((day) => weekDays[parseInt(day) - 1])
    .join(" | ");
  if (config.value.inspectionDay == "一 | 二 | 三 | 四 | 五 | 六 | 日") {
    config.value.inspectionDay = "每天";
  }
};
const onJump = (item: Station) => {
  storeInspection.inspectionRecord = item;
  //跳转到站点详情
  uni.navigateTo({
    url: `/subpages/inspection/record/inspection-detail/index?type=patrol`,
  });
};
const open = () => {
  expendValue.value = !expendValue.value;
};
const onChange = (item: Floor) => {
  activeValue.value = item.floorName;
  getPatrolList();
};
</script>
<style lang="scss">
/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background: #f6f8fa;
  overflow: hidden;
}
.header {
  .date {
    overflow: scroll;
    background: #fdfdfd;
    box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  }
  .top {
    margin-bottom: 10px;
    //   flex: 1;
    background: #fff;
    box-shadow: 0px 0px 10px 0px rgba(146, 146, 146, 0.3);
    .top-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 15px 15px 15px 8px;
      height: 40px;
      line-height: 40px;
      .title-left,
      .title-right {
        display: flex;
        flex-direction: row;
        align-items: center;
      }
      .title-left {
        .title-circle {
          width: 8px;
          height: 8px;
          background: #035ec9;
          border-radius: 50%;
        }
        .title-text {
          font-size: 14px;
          color: #333333;
          font-weight: 700;
          margin-left: 5px;
        }
      }
      .title-right {
        font-size: 12px;
        color: #666666;
        .right-text {
          margin-right: 5px;
        }
      }
    }
    .top-content {
      padding: 0 15px;
      height: 138px;
      .content-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        .item-label {
          width: 112px;
          color: #666666;
        }
        .item-value {
          flex: 1;
          color: #333333;
          width: calc(100% - 112px);
          //超过一行显示省略号
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

/* 主体内容容器 */
.main-container {
  flex: 1;
  display: flex;
  margin-top: 10px;
  overflow: hidden; /* 避免外层滚动 */
  // height: calc(100vh - 70px - 40px - 20px);
  box-sizing: border-box;
}
.main-expend {
  height: calc(100vh - 70px - 178px - 20px);
}
.main-put {
  height: calc(100vh - 70px - 40px - 20px);
}

/* 左侧导航样式 */
.left-nav {
  width: 54px;
  height: 100%;
  background: #fff;
}

.nav-item {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #636363;

  &.active {
    position: relative;
    color: #333333;
    background: #fff;
    font-weight: bold;
    // border-left: 3px solid #005CC8;
    &::before {
      display: inline-block;
      width: 3px;
      height: 20px;
      background: #005cc8;
      content: "";
      position: absolute;
      top: 11px;
      left: 5px;
    }
  }
}

/* 右侧内容区域 */
.right-content {
  height: 100%;
  box-sizing: border-box;
  .item-list {
    font-size: 12px;
    margin-bottom: 10px;
    .item {
      display: flex;
      flex-direction: row;
      width: calc(100% - 20px); //减去左右外边距
      height: 100%;
      background: #fff;
      border-radius: 10px;
      padding: 15px;
      margin: 0 10px 10px 10px;
      box-shadow: 0px 0px 10px 0px rgba(146, 146, 146, 0.3);
      .item-left {
        .left-img {
          width: 48px;
          height: 48px;
          border-radius: 3px;
        }
        .inspection-image {
          width: 48px;
          height: 48px;
          border-radius: 4px;
          margin-bottom: 8px;
        }
      }
      .item-right {
        flex: 1;
        margin-left: 13px;
        .title {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          .position {
            font-size: 14px;
            font-weight: bold;
            color: #333;
          }
          .finished {
            color: #21843b;
          }
          .unfinish {
            color: #ff7605;
          }
          .untime {
            color: #c93535;
          }
        }
        .right-desc {
          display: flex;
          margin-top: 2px;
          .label {
            color: #666666;
            min-width: 60px;
          }
          .value {
            flex: 1;
            color: #333333;
          }
          .code-error {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 23px;
            color: #c93535;
            font-size: 12px;
            text-align: center;
            border: 1px solid #ebc1c1;
            background: #ffe4e4;
            border-radius: 2px;
          }
        }
      }
    }
  }
}
.sceen {
  position: fixed;
  bottom: 70px;
  right: 15px;
  z-index: 599;
}
.empty {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
}
</style>
