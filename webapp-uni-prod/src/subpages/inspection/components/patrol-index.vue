<template>
  <view class="container">
    <view class="header">
      <view class="desc" v-if="patrolData.length > 0">
        <view class="top">
          <view class="top-title">
            <view class="title-left">
              <view class="title-circle"></view>
              <view class="title-text">{{ props.title }}</view>
            </view>
            <view class="title-right" @click="open()">
              <view class="right-text">{{
                expendValue ? "展开" : "收起"
              }}</view>
              <view :class="expendValue ? 'rotate' : ''">
                <SlSubSvgIcon subpage="inspection" name="12-12-7" size="12" />
              </view>
            </view>
          </view>
          <view class="top-content" v-show="patrolDesc">
            <view class="content-item">
              <view class="item-label">班组</view>
              <view class="item-value">啥借鸡生蛋</view>
            </view>
            <view class="content-item">
              <view class="item-label">打卡日期</view>
              <view class="item-value">12222</view>
            </view>
            <view class="content-item">
              <view class="item-label">时间</view>
              <view class="item-value">17:00-19:00</view>
            </view>
            <view class="content-item">
              <view class="item-label">每天打卡次数</view>
              <view class="item-value">1<text>次</text></view>
            </view>
            <view class="content-item">
              <view class="item-label">每次至少间隔</view>
              <view class="item-value">1<text>小时</text></view>
            </view>
            <view class="content-item">
              <view class="item-label">备注</view>
              <view class="item-value">--</view>
            </view>
          </view>
        </view>
      </view>
      <view class="date">
        132313342412
        <CalendarComponen></CalendarComponen>
      </view>
    </view>
    <view v-if="patrolData.length > 0">
      <view class="main-container">
        <!-- 左侧楼层导航 -->
        <scroll-view scroll-y class="left-nav">
          <view
            v-for="(item, index) in floors"
            :key="index"
            class="nav-item"
            :class="{ active: activeFloor === index }"
            @click="activeFloor = index"
          >
            {{ item.name }}
          </view>
        </scroll-view>

        <!-- 右侧内容区域 -->
        <scroll-view scroll-y class="right-content">
          <view class="item-list">
            <view
              class="item"
              v-for="(item, idx) in patrolData"
              :key="idx"
              @click="onJump(item)"
            >
              <!-- <image :src="item.image" class="product-img" mode="aspectFill" />
                <text class="product-name">{{ item.name }}</text>
                <text class="product-price">¥{{ item.price }}</text> -->
              <view class="item-left">
                <image :src="item.image" class="left-img" mode="aspectFill" />
              </view>
              <view class="item-right">
                <view class="title">
                  <view class="position">{{ item.name }}</view>
                  <view
                    :class="
                      item.status === 0
                        ? 'finished'
                        : item.status === 1
                        ? 'untime'
                        : 'unfinish'
                    "
                    >{{ item.status }}</view
                  >
                </view>
                <view class="right-desc">
                  <view class="label">区域</view>
                  <view class="value">哈大家</view>
                </view>
                <view class="right-desc">
                  <view class="label">站点位置</view>
                  <view class="value">{{ item.position }}</view>
                </view>
                <view class="right-desc">
                  <view class="label">备注</view>
                  <view class="value">{{
                    item.remark ? item.remark : "--"
                  }}</view>
                </view>
                <view class="right-desc" v-if="item.status === 1">
                  <view class="label"></view>
                  <view class="value code-error">二维码标签异常</view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    <!-- <view v-else class="empty">
      <view class="empty-img">
        <image
          src="@/static/images/empty.png"
          class="empty-img"
          mode="aspectFill"
        />
      </view>

      <view class="empty-text">今日无需打卡</view>
    </view> -->
  </view>
  <!-- 扫码图标 -->
  <view class="sceen"> </view>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { toRpx } from "@/utils/toRpx";
type Title = "白班巡检" | "夜班巡检" | "保洁清洁" | "物业巡检";
const props = defineProps({
  title: {
    type: String as () => Title,
    default: "白班巡检",
  },
  list: {
    type: Array,
    default: () => [],
  },
});
const expendValue = ref<boolean>(false);
const activeFloor = ref(0);
const scrollHeight = ref(0);
const patrolDesc = ref<boolean>(false);
const descHeight = ref<number>(0);
//折叠title字体大小
const fontSize = toRpx(14);
const floors = ref([
  {
    id: "1",
    name: "1楼",
  },
  {
    id: "2",
    name: "2楼",
  },
  {
    id: "3",
    name: "3楼",
  },
  {
    id: "4",
    name: "4楼",
  },
  {
    id: "5",
    name: "5楼",
  },
  {
    id: "6",
    name: "6楼",
  },
  {
    id: "7",
    name: "7楼",
  },
]);

const patrolData = ref([
  {
    name: "巡检1",
    image: "https://cdn.uviewui.com/uview/album/1.jpg",
    price: 100,
    status: 0,
    position: "A栋B1F货架旁",
    remark: "--",
  },
  {
    name: "巡检1",
    image: "https://cdn.uviewui.com/uview/album/1.jpg",
    price: 100,
    status: 0,
    position: "哈大家沙甲和大家回到家啊基督教啊虎大将军",
    remark: "备注",
  },
  {
    name: "巡检1",
    image: "https://cdn.uviewui.com/uview/album/1.jpg",
    price: 100,
    status: 0,
    position: "哈大家沙甲和大家回到家啊基督教啊虎大将军",
    remark: "备注",
  },
  {
    name: "巡检2",
    image: "https://cdn.uviewui.com/uview/album/2.jpg",
    price: 200,
    status: 1,
    position: "货梯旁",
    remark: "备注",
  },
  {
    name: "巡检2",
    image: "https://cdn.uviewui.com/uview/album/2.jpg",
    price: 200,
    status: 2,
    position: "lo",
    remark: "",
  },
  {
    name: "巡检2",
    image: "https://cdn.uviewui.com/uview/album/2.jpg",
    price: 200,
    status: 2,
    position: "lo",
    remark: "",
  },
]);
onLoad((options: any) => {
  console.log(options, "opyions");
  console.log(props.list, "props.list");
  const height = uni
    .createSelectorQuery()
    .in(this)
    .select(".container")
    .boundingClientRect((rect) => {
      console.log(rect, "rect");
    });
  console.log(height, "height");
  // if (options.title == "白班巡检") {
  //   console.log("白班巡检");
  // } else if (options.title == "夜班巡检") {
  //   console.log("夜班巡检");
  // } else if (options.title == "保洁清洁") {
  //   console.log("保洁清洁");
  // } else if (options.title == "物业巡检") {
  //   console.log("物业巡检");
  // }
});
const onJump = (item: any) => {
  console.log("onJump", item);
  //跳转到站点详情
  uni.navigateTo({
    // url: "detail",
    url: `/subpages/inspection/station-detail/index`,
  });
};
// 计算滚动区域高度
const calculateScrollHeight = () => {
  const systemInfo = uni.getSystemInfoSync();
  const safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0; //安全区域高度
  console.log(systemInfo);
  const dateHeight = 70; // 日期组件高度
  descHeight.value = 40; // 折叠面板高度
  const marginSpace = 20; // 上下边距

  scrollHeight.value =
    // toRpx(
    //减去安全区域高度
    systemInfo.windowHeight -
    dateHeight -
    marginSpace -
    descHeight.value -
    safeAreaBottom;
  // );
  console.log(scrollHeight.value, "scrollHeight.value");
};
const open = () => {
  expendValue.value = !expendValue.value;
  patrolDesc.value = !patrolDesc.value;
  descHeight.value = 178;
  nextTick(() => {
    calculateScrollHeight();
  });
  // calculateScrollHeight();
};
// 生命周期钩子
onMounted(() => {
  calculateScrollHeight();
  uni.onWindowResize(calculateScrollHeight);
});

onUnmounted(() => {
  uni.offWindowResize(calculateScrollHeight);
});
</script>
<style lang="scss">
/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100vh;
  width: 100%;
  background: #f6f8fa;
  // overflow: scroll;
}
.header {
  .date {
    height: 70px;
    background: #fdfdfd;
    box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  }
  .desc {
    margin-bottom: 10px;
    //   flex: 1;
    background: #fff;
    box-shadow: 0px 0px 10px 0px rgba(146, 146, 146, 0.3);
  }
}
.top {
  .top-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 15px 15px 15px 8px;
    height: 40px;
    line-height: 40px;
    .title-left,
    .title-right {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    .title-left {
      .title-circle {
        width: 8px;
        height: 8px;
        background: #035ec9;
        border-radius: 50%;
      }
      .title-text {
        font-size: 14px;
        color: #333333;
        font-weight: 700;
        margin-left: 5px;
      }
    }
    .title-right {
      font-size: 12px;
      color: #666666;
      .right-text {
        margin-right: 5px;
      }
    }
  }
  .top-content {
    padding: 0 15px;
    height: 138px;
    .content-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
      .item-label {
        width: 112px;
        color: #666666;
      }
      .item-value {
        flex: 1;
        color: #333333;
      }
    }
  }
}

/* 主体内容容器 */
.main-container {
  display: flex;
  flex-grow: 1;
  margin-top: 10px;
  overflow: hidden; /* 避免外层滚动 */
}

/* 左侧导航样式 */
.left-nav {
  width: 54px;
  height: 100%;
  scrollbar-width: none; /* 隐藏滚动条 */
  background: #fff;
}

.nav-item {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #636363;

  &.active {
    position: relative;
    color: #333333;
    background: #fff;
    font-weight: bold;
    // border-left: 3px solid #005CC8;
    &::before {
      display: inline-block;
      width: 3px;
      height: 20px;
      background: #005cc8;
      content: "";
      position: absolute;
      top: 11px;
      left: 5px;
    }
  }
}

/* 右侧内容区域 */
.right-content {
  height: 100%;
  .item-list {
    font-size: 12px;
    margin-bottom: 10px;
    .item {
      display: flex;
      flex-direction: row;
      width: calc(100% - 20px); //减去左右外边距
      height: 100%;
      background: #fff;
      border-radius: 10px;
      padding: 15px;
      margin: 0 10px 10px 10px;
      box-shadow: 0px 0px 10px 0px rgba(146, 146, 146, 0.3);
      .item-left {
        .left-img {
          width: 48px;
          height: 48px;
          border-radius: 3px;
        }
      }
      .item-right {
        flex: 1;
        margin-left: 13px;
        .title {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          .position {
            font-size: 14px;
            font-weight: bold;
            color: #333;
          }
          .finished {
            color: #21843b;
          }
          .unfinish {
            color: #ff7605;
          }
          .untime {
            color: #c93535;
          }
        }
        .right-desc {
          display: flex;
          //   justify-content: space-between;
          margin-top: 2px;
          .label {
            width: 65px;
            color: #666666;
          }
          .value {
            flex: 1;
            color: #333333;
          }
          .code-error {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 23px;
            color: #c93535;
            font-size: 12px;
            text-align: center;
            border: 1px solid #ebc1c1;
            padding: 5px 7px;
            background: #ffe4e4;
            border-radius: 2px;
          }
        }
      }
    }
  }
}
.sceen {
  position: fixed;
  bottom: 45px;
  right: 15px;
  height: 45px;
  width: 45px;
  border-radius: 50%;
  background: #3b6eeb;
  box-shadow: 0px 0px 4px 0px rgba(74, 74, 74, 0.2);
  z-index: 3;
}
.empty {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #999999;
  .empty-img {
    .empty-img {
      width: 40px;
      height: 40px;
    }
  }
  .empty-text {
  }
}
.rotate {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out;
}
</style>
