<template>
  <view class="container">
    <view class="header">
      <view class="top">
        <view class="top-title">
          <view class="title-left">
            <view class="title-circle"></view>
            <view class="title-text">{{ props.title }}</view>
          </view>
          <view class="title-right" @click="open()">
            <view class="right-text">{{ expendValue ? "展开" : "收起" }}</view>
            <view :class="expendValue ? 'rotate' : ''">
              <SlSubSvgIcon subpage="inspection" name="12-12-7" size="12" />
            </view>
          </view>
        </view>
        <view class="top-content" v-show="expendValue">
          <view class="content-item">
            <view class="item-label">班组</view>
            <view class="item-value">啥借鸡生蛋</view>
          </view>
          <view class="content-item">
            <view class="item-label">打卡日期</view>
            <view class="item-value">12222</view>
          </view>
          <view class="content-item">
            <view class="item-label">时间</view>
            <view class="item-value">17:00-19:00</view>
          </view>
          <view class="content-item">
            <view class="item-label">每天打卡次数</view>
            <view class="item-value">1<text>次</text></view>
          </view>
          <view class="content-item">
            <view class="item-label">每次至少间隔</view>
            <view class="item-value">1<text>小时</text></view>
          </view>
          <view class="content-item">
            <view class="item-label">备注</view>
            <view class="item-value">--</view>
          </view>
        </view>
      </view>
      <view class="date">
        132313342412
        <CalendarComponen></CalendarComponen>
      </view>
    </view>
    <view>
      <view
        :class="['main-container', expendValue ? 'main-expend' : 'main-put']"
      >
        <!-- 左侧楼层导航 -->
        <scroll-view scroll-y class="left-nav">
          <view
            v-for="(item, index) in floorData"
            :key="index"
            class="nav-item"
            :class="{ active: activeValue == item.floorName }"
            @click="onChange(item)"
          >
            {{ item.floorName }}
          </view>
        </scroll-view>

        <!-- 右侧内容区域 -->
        <scroll-view scroll-y class="right-content">
          <view class="item-list" v-if="patrolData.length > 0">
            <view
              class="item"
              v-for="(item, idx) in patrolData"
              :key="idx"
              @click="onJump(item)"
            >
              <!-- <image :src="item.image" class="product-img" mode="aspectFill" />
                <text class="product-name">{{ item.name }}</text>
                <text class="product-price">¥{{ item.price }}</text> -->
              <view class="item-left">
                <!-- <image :src="item.image" class="left-img" mode="aspectFill" /> -->
              </view>
              <view class="item-right">
                <view class="title">
                  <view class="position">{{ item.siteName }}</view>
                  <view
                    :class="
                      item.inspectionResult === '已完成'
                        ? 'finished'
                        : item.inspectionResult === '未完成'
                        ? 'untime'
                        : 'unfinish'
                    "
                    >{{ item.inspectionResult }}</view
                  >
                </view>
                <view class="right-desc">
                  <view class="label">区域</view>
                  <view class="value">{{ item.floorName }}</view>
                </view>
                <view class="right-desc">
                  <view class="label">站点位置</view>
                  <view class="value">{{ item.sitePosition }}</view>
                </view>
                <view class="right-desc">
                  <view class="label">备注</view>
                  <view class="value">{{
                    item.remarks ? item.remarks : "--"
                  }}</view>
                </view>
                <view class="right-desc" v-if="item.siteStatus === 'abnormal'">
                  <view class="label"></view>
                  <view class="value code-error">二维码标签异常</view>
                </view>
              </view>
            </view>
          </view>
          <BaseEmpty v-else></BaseEmpty>
        </scroll-view>
      </view>
    </view>
    <!-- <view v-else class="empty">
      <view class="empty-img">
        <image
          src="@/static/images/empty.png"
          class="empty-img"
          mode="aspectFill"
        />
      </view>

      <view class="empty-text">今日无需打卡</view>
    </view> -->
  </view>
  <!-- 扫码图标 -->
  <view class="sceen"> </view>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { toRpx } from "@/utils/toRpx";
import { Floor, InspectionParams, Station } from "../models/inspection";
import inspectionService from "@/service/inspection";
import { useRepairStore } from "@/store";
import { useInspectionStore } from "@/subpages/inspection/utils/stores";

type Title = "白班巡检" | "夜班巡检" | "保洁清洁" | "物业巡检";
type TeamType = "white" | "night" | "property" | "clean";
const props = defineProps({
  title: {
    type: String as () => Title,
    default: "白班巡检",
  },
  teamType: {
    type: String as () => TeamType,
  },
  list: {
    type: Array,
    default: () => [],
  },
});
const store = useRepairStore();
const storeInspection = useInspectionStore();
// 楼层数据
const floorData = ref([] as Floor[]);
const queryRecord = reactive<InspectionParams>({
  queryDate: "2025-06-27",
  loginUserRole: store.ifStaff ? "workers" : "administrator",
  // loginUserRole: "administrator",
  inspectionTeamType: "white",
});
const expendValue = ref<boolean>(false);
const activeValue = ref("");

const patrolData = ref([] as Station[]);
onLoad((options: any) => {
  console.log(options, "opyions");
  console.log(props.list, "props.list");
  getFloorList();
  getPatrolList();
  getConfig();
});

const getFloorList = async () => {
  const res = await inspectionService.getSiteAreaList({
    ifPage: false,
  });
  // 确保 res 是数组
  if (Array.isArray(res)) {
    floorData.value = res.map((item: Floor) => ({
      ...item,
    }));
    //将activeValue赋值给第一个楼层的id
    activeValue.value = floorData.value[0].floorName;
  } else {
    floorData.value = [];
  }
};
const getPatrolList = async () => {
  if (queryRecord.loginUserRole == "workers") {
    queryRecord.inspectionTeamType = "";
  } else {
    queryRecord.inspectionTeamType = props.teamType!;
  }
  const res = await inspectionService.getRecordList(queryRecord);
  //判断res是否为对象
  if (typeof res === "object") {
    //根据接口返回数据找到当前楼层对应的数据
    const floor = res[activeValue.value];
    if (floor) {
      patrolData.value = floor;
    } else {
      patrolData.value = [];
    }
  } else {
    patrolData.value = [];
  }
};
const getConfig = async () => {
  const res = await inspectionService.getInspectionConfig();
  console.log(res, "res");
};
const onJump = (item: Station) => {
  storeInspection.inspectionRecord = item;
  //跳转到站点详情
  uni.navigateTo({
    url: `/subpages/inspection/record/inspection-detail/index?type=patrol`,
  });
};
const open = () => {
  expendValue.value = !expendValue.value;
};
const onChange = (item: Floor) => {
  activeValue.value = item.floorName;
  getPatrolList();
};
</script>
<style lang="scss">
/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background: #f6f8fa;
  overflow: hidden;
}
.header {
  .date {
    height: 70px;
    background: #fdfdfd;
    box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  }
  .top {
    margin-bottom: 10px;
    //   flex: 1;
    background: #fff;
    box-shadow: 0px 0px 10px 0px rgba(146, 146, 146, 0.3);
    .top-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 15px 15px 15px 8px;
      height: 40px;
      line-height: 40px;
      .title-left,
      .title-right {
        display: flex;
        flex-direction: row;
        align-items: center;
      }
      .title-left {
        .title-circle {
          width: 8px;
          height: 8px;
          background: #035ec9;
          border-radius: 50%;
        }
        .title-text {
          font-size: 14px;
          color: #333333;
          font-weight: 700;
          margin-left: 5px;
        }
      }
      .title-right {
        font-size: 12px;
        color: #666666;
        .right-text {
          margin-right: 5px;
        }
      }
    }
    .top-content {
      padding: 0 15px;
      height: 138px;
      .content-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        .item-label {
          width: 112px;
          color: #666666;
        }
        .item-value {
          flex: 1;
          color: #333333;
        }
      }
    }
  }
}

/* 主体内容容器 */
.main-container {
  flex: 1;
  display: flex;
  margin-top: 10px;
  overflow: hidden; /* 避免外层滚动 */
  // height: calc(100vh - 70px - 40px - 20px);
  box-sizing: border-box;
}
.main-expend {
  height: calc(100vh - 70px - 178px - 20px);
}
.main-put {
  height: calc(100vh - 70px - 40px - 20px);
}

/* 左侧导航样式 */
.left-nav {
  width: 54px;
  height: 100%;
  background: #fff;
}

.nav-item {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #636363;

  &.active {
    position: relative;
    color: #333333;
    background: #fff;
    font-weight: bold;
    // border-left: 3px solid #005CC8;
    &::before {
      display: inline-block;
      width: 3px;
      height: 20px;
      background: #005cc8;
      content: "";
      position: absolute;
      top: 11px;
      left: 5px;
    }
  }
}

/* 右侧内容区域 */
.right-content {
  height: 100%;
  box-sizing: border-box;
  .item-list {
    font-size: 12px;
    margin-bottom: 10px;
    .item {
      display: flex;
      flex-direction: row;
      width: calc(100% - 20px); //减去左右外边距
      height: 100%;
      background: #fff;
      border-radius: 10px;
      padding: 15px;
      margin: 0 10px 10px 10px;
      box-shadow: 0px 0px 10px 0px rgba(146, 146, 146, 0.3);
      .item-left {
        .left-img {
          width: 48px;
          height: 48px;
          border-radius: 3px;
        }
      }
      .item-right {
        flex: 1;
        margin-left: 13px;
        .title {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          .position {
            font-size: 14px;
            font-weight: bold;
            color: #333;
          }
          .finished {
            color: #21843b;
          }
          .unfinish {
            color: #ff7605;
          }
          .untime {
            color: #c93535;
          }
        }
        .right-desc {
          display: flex;
          margin-top: 2px;
          .label {
            color: #666666;
            min-width: 60px;
          }
          .value {
            flex: 1;
            color: #333333;
          }
          .code-error {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 23px;
            color: #c93535;
            font-size: 12px;
            text-align: center;
            border: 1px solid #ebc1c1;
            background: #ffe4e4;
            border-radius: 2px;
          }
        }
      }
    }
  }
}
.sceen {
  position: fixed;
  bottom: 45px;
  right: 15px;
  height: 45px;
  width: 45px;
  border-radius: 50%;
  background: #3b6eeb;
  box-shadow: 0px 0px 4px 0px rgba(74, 74, 74, 0.2);
  z-index: 3;
}
.empty {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #999999;
  .empty-img {
    .empty-img {
      width: 40px;
      height: 40px;
    }
  }
  .empty-text {
  }
}
.rotate {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out;
}
</style>
