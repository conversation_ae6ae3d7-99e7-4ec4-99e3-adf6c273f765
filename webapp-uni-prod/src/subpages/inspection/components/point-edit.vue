<template>
  <view class="container">
    <view class="add-inspection">
      <view class="add-item">
        <view class="item-label"><text style="color: #E50101;">*</text>点位名称</view>
        <view>
          <up-input
            style="background-color: #f8f8f8; padding: 6px 8px; width: 200px"
            placeholder="请绑定点位名称"
            border="none"
            inputAlign="right"
            font-size="14px"
            maxlength="10"
          ></up-input>
        </view> 
      </view>
      <view class="add-item">
        <view class="item-label"><text style="color: #E50101;">*</text>点位编号</view>
        <view>
          <up-input
            style="background-color: #f8f8f8; padding: 6px 8px; width: 200px"
            placeholder="请绑定点位编号"
            border="none"
            inputAlign="right"
            font-size="14px"
            maxlength="20"
          ></up-input>
        </view>
      </view>
      <view class="add-item">
        <view class="item-label"><text style="color: #E50101;">*</text>区域</view>
        <view>
          <SLSelect
            :options="options"
            placeholder="请选择站点区域"
            icon-name="12-12-7"
            icon-size="12"
            @change="handlPositionChange"
          >
            <template #dropdown-footer>
              <div
                class="select-btn-container"

                @click.stop="newPositionName = '', showAddConfirm = true">
              >
                <div class="select-add-btn">新增</div>
              </div>
            </template>
          </SLSelect>
          <!-- <up-input
          style="background-color: #f8f8f8; padding: 6px 8px; width: 200px"
          placeholder="请输入站点区域"
          border="none"
          inputAlign="right"
          font-size="14px"
        ></up-input> -->
        </view>
      </view>
      <view class="add-item">
        <view class="item-label"><text style="color: #E50101;">*</text>点位位置</view>
        <view>
          <up-input
            style="background-color: #f8f8f8; padding: 6px 8px; width: 200px"
            placeholder="请输入点位位置"
            border="none"
            inputAlign="right"
            font-size="14px"
          ></up-input>
        </view>
      </view>
      <view class="add-item">
        <view class="item-label"> <text style="color: #fff;">*</text>站点图片</view>
        <!-- <view v-if="!props.isEdit">
        <SlSVgIcon name="30-30-8" size="30" />
      </view> -->
        <view>
          <up-upload
            :fileList="fileList1"
            @afterRead="afterRead"
            @delete="deletePic"
            accept="all"
            name="1"
            multiple
            :maxCount="10"
          >
            <SlSVgIcon name="34-34-10" size="34" />
          </up-upload>
        </view>
      </view>
      <!-- <view class="add-item">
        <view class="item-label">每日巡检</view>
        <view>
          <up-input
            class="inp"
            style="background-color: #f8f8f8; padding: 6px 8px; width: 200px"
            placeholder="请输入每日巡检频率"
            border="none"
            type="number"
            inputAlign="right"
            font-size="14px"
          >
            <template #suffix>
              <up-text text="次" size="14px"></up-text>
            </template>
          </up-input>
        </view>
      </view> -->
      <!-- <view class="add-item">
        <view class="item-label">每日至少间隔</view>
        <view>
          <up-input
            class="inp"
            style="background-color: #f8f8f8; padding: 6px 8px; width: 200px"
            placeholder="请输入每次间隔时长"
            border="none"
            type="number"
            inputAlign="right"
            font-size="14px"
          >
            <template #suffix>
              <up-text text="小时" size="14px"></up-text>
            </template>
          </up-input>
        </view>
      </view> -->
      <view class="remark">
        <view class="item-label"><text style="color: #fff;">*</text>备注</view>
        <view>
          <up-textarea class="textarea" placeholder="请输入备注信息" maxlength="15"></up-textarea>
        </view>
      </view>
    </view>
    <view class="btn">
      <BaseButton btnType="cancel" style="margin-right: 10px" @click="onBack()">取消</BaseButton>
      <BaseButton btnType="save">完成</BaseButton>
    </view>
  </view>
  <ConfirmDialog
    v-if="showAddConfirm"
    @close="showAddConfirm = false"
    @confirm="onConfirmAdd"
    :showHeader="false"
  >
    <div class="add-box">
      <span class="label">区域名称：</span>
      <up-input
        type="text"
        placeholder="请输入"
        v-model="newPositionName"
        clearable
        maxlength="8"
        :border="'none'"
      />
    </div>
  </ConfirmDialog>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import BaseButton from "@/components/BaseButton.vue";
import SLSelect from "@/components/SLSelect.vue";

const props = defineProps({
  isEdit: {
    //为true时为编辑，false为新增
    type: Boolean,
    required: true,
  },
});
const options = ref([
  { label: "区域1", value: "1" },
  { label: "区域2", value: "2" },
  { label: "区域3", value: "3" },
  { label: "区域4", value: "4" },
  { label: "区域5", value: "5" },
]);
//区域名称
const newPositionName = ref<string>("");
const fileList1 = ref<any>([]);

// 删除图片
const deletePic = (event: any) => {
  fileList1.value.splice(event.index, 1);
};
//区域名称input
const showAddConfirm = ref<Boolean>(false);

//区域名称弹窗
const onConfirmAdd = () => {
  //将区域名称添加到点位列表中 newPositionName

  showAddConfirm.value = false;
};
const handlPositionChange = (e: any) => {
  console.log(e);
};
// 新增图片
const afterRead = async (event: any) => {
  // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
  let lists = [].concat(event.file);
  let fileListLen = fileList1.value.length;
  lists.map((item: any) => {
    fileList1.value.push({
      ...item,
      status: "uploading",
      message: "上传中",
    });
  });
  for (let i = 0; i < lists.length; i++) {
    // const result = await uploadFilePromise(lists[i].url);
    // let item = fileList1.value[fileListLen];
    // fileList1.value.splice(fileListLen, 1, {
    //   ...item,
    //   status: 'success',
    //   message: '',
    //   url: result,
    // });
    // fileListLen++;
  }
};

const uploadFilePromise = (url: any) => {
  return new Promise((resolve, reject) => {
    let a = uni.uploadFile({
      url: "http://************:7001/upload", // 仅为示例，非真实的接口地址
      filePath: url,
      name: "file",
      formData: {
        user: "test",
      },
      success: (res) => {
        setTimeout(() => {
          //   resolve(res.data.data);
        }, 1000);
      },
    });
  });
};
const onBack = () => {
  uni.navigateBack();
}
</script>
<style lang="scss" scoped>
/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  background: #f6f8fa;
}
.add-inspection {
  font-size: 14px;
  padding: 14px;
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);

  .add-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;

    .item-label {
      color: #666666;
    }
    .select-btn-container { 
    width: 100%;
    height: 28px;
    line-height: 28px;
    display: flex;
    text-align: center;
    justify-content: center;
    margin: 5px 0;
    .select-add-btn {
      width: 60px;
      color: #fff;
      font-size: 14px;
      font-weight: 400;
      background: #0066DF;
    }
}
  }
  .remark {
      display: flex;
      flex-direction: column;
      .item-label {
      color: #666666;
      margin-bottom: 10px;
    }
    }
}
.btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}
.add-box {
    height: 106px;
    display: flex;
    align-items: center;
}
// .inp {
//   width: 200px;
//   height: 40px;
//   font-size: 14px;
//   text-align: right;
//   background-color: #f8f8f8;
// }
</style>
