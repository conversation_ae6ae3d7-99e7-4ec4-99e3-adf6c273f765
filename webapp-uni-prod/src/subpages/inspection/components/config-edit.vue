<template>
  <view class="container">
    <view class="card basic-info">
      <view class="info-item">
        <view class="info-label">任务名称</view>
        <view class="info-value">
          <up-input
            style="background-color: #f8f8f8; padding: 6px 8px; width: 200px"
            placeholder="请绑定任务名称"
            border="none"
            inputAlign="right"
            font-size="14px"
            minlength="10"
          ></up-input>
        </view>
      </view>
      <view class="info-item">
        <view class="info-label">班组</view>
        <view class="info-value select" @click="slectTeam()">
          <view class="select-text">请选择班组</view>
          <view>
            <SlSVgIcon name="14-14-7" size="10" />
          </view>
        </view>
      </view>
      <view class="info-item">
        <view class="info-label">站点</view>
        <view class="info-value select" @click="slectState()">
          <view class="select-text">请选择站点</view>
          <view>
            <SlSVgIcon name="14-14-7" size="10" />
          </view>
        </view>
      </view>
      <view class="remark">
        <view class="label">备注</view>
        <view>
          <up-textarea
            placeholder="请输入"
            maxlength="20"
            placeholderStyle="font-size: 14px"
          ></up-textarea>
        </view>
      </view>
    </view>
    <view class="card work">
      <view class="work-title"> 每周打卡日期(默认) </view>
      <view class="work-time">
        <up-checkbox-group
          v-model="checkboxValue1"
          placement="row"
          @change="checkboxChange"
        >
          <up-checkbox
            :customStyle="{ marginBottom: '8px', width: '33.3%'}"
            v-for="(item, index) in checkboxList1"
            :key="index"
            :label="item.name"
            :name="item.id"
          >
          </up-checkbox>
        </up-checkbox-group>
      </view>
    </view>
    <view class="card clock">
      <view class="clock-title">打卡时间</view>
      <view class="clock-startTime">
        <view>开始时间</view>
        <view class="time" @click="startTimeShow = !startTimeShow">
          <view>请选择</view>
          <view>
            <SlSVgIcon name="12-12-7" size="12" />
          </view> 
          <!-- 时间选择器 -->
          <view class="time-picker" v-show="startTimeShow">
             <!-- 小时选择 --> 
              <scroll-view 
              class="picker-column" 
              scroll-y 
              scroll-with-animation 
              :scroll-top="hourScrollTop"
               @scroll="onHourScroll"
                @touchstart="stopScroll" 
                @touchend="startAnimation" 
                > 
                <view 
                class="picker-item" 
                v-for="h in 24" :key="h"> 
                {{ String(h - 1).padStart(2, '0') }} 
              </view> 
            </scroll-view> 
            <!-- <text class="separator">:</text>  -->
            <!-- 分钟选择 --> 
             <scroll-view 
             class="picker-column" 
             scroll-y scroll-with-animation 
             :scroll-top="minuteScrollTop" 
             @scroll="onMinuteScroll" 
             @touchstart="stopScroll" 
             @touchend="startAnimation" > 
             <view 
             class="picker-item" 
             v-for="m in 60" :key="m"> 
             {{ String(m - 1).padStart(2, '0') }} 
            </view> 
          </scroll-view> 
          </view>
          <!-- <up-datetime-picker
          hasInput
            :show="startTimeShow"
            v-model="startTime"
            mode="time"
          ></up-datetime-picker> -->
        </view>
      </view>
      <view class="clock-endTime">
        <view>结束时间</view>
        <!-- <view class="time">
          <up-datetime-picker
            hasInput
            :show="endTimeShow"
            v-model="endTime"
          mode="time"
            placeholder="请选择"
          ></up-datetime-picker>
        </view> --> 
      </view>
    </view>
    <view class="card limit">
      <view class="limit-times">
        <view>每天打卡次数</view>
        <!-- 打卡次数为0 -->
        <view class="limit-times-value">  
          <SlSVgIcon name="20-20-24" size="20" @click="count--" v-if="count != 0" />
          <view v-if="count != 0" class="count">{{count}}</view>
          <SlSVgIcon name="20-20-25" size="20" @click="count++" />
          <view class="unit">次</view>
        </view>
      </view>
      <view class=" limit-interval">
        <view>每天至少间隔</view>
        <!-- 间隔为0 -->
        <view class="limit-times-value">
          <SlSVgIcon name="20-20-24" size="20" @click="interval--" v-if="interval != 0" />
          <view v-if="interval != 0" class="count">{{interval}}</view>
          <SlSVgIcon name="20-20-25" size="20" @click="interval++" />
          <view class="unit">小时</view>
        </view>
      </view>
    </view>
    <view class="btn">
      <BaseButton btnType="cancel" style="margin-right: 10px">取消</BaseButton>
      <BaseButton btnType="save">完成</BaseButton>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
const props = defineProps({
  type: {
    type: String,
    default: "",
  },
  list: {
    type: Array,
    default: () => [],
  }
})
const count = ref<number>(0);
const interval = ref<number>(0);
//选中的复选框值
const checkboxValue1 = ref<number[]>([]);
//打卡开始时间
const startTime = ref("");
const startTimeShow = ref(false);
//打卡结束时间
const endTime = ref("");
const endTimeShow = ref(false);
//复选框的值
const checkboxList1 = reactive([
  {
    id: 1,
    name: "周一",
  },
  {
    id: 2,
    name: "周二",
  },
  {
    id: 3,
    name: "周三",
  },
  {
    id: 4,
    name: "周四",
  },
  {
    id: 5,
    name: "周五",
  },
  {
    id: 6,
    name: "周六",
  },
  {
    id: 7,
    name: "周日",
  },
]);
const hourScrollTop = ref(0);
const minuteScrollTop = ref(0);
const onHourScroll = (e: any) => {
  console.log("hourScrollTop", e.detail.scrollTop);
  hourScrollTop.value = e.detail.scrollTop;
};

const onMinuteScroll = (e: any) => {
  console.log("minuteScrollTop", e.detail.scrollTop);
  minuteScrollTop.value = e.detail.scrollTop;
};

const stopScroll = () => {
  // 停止滚动的逻辑
  console.log("stopScroll");
};

const startAnimation = () => {
  console.log("startAnimation");
  // 开始动画的逻辑
};
//复选框点击改变事件
const checkboxChange = (n: number[]) => {
  console.log("change", n);
  checkboxValue1.value = n;
};

const slectTeam = () => {
  console.log("选择班组");
  //选择班组
  uni.navigateTo({
    url: "/subpages/inspection/setting/select-team/index",
  });
};
const slectState = () => {
  console.log("选择站点");
  //选择站点
  uni.navigateTo({
    url: "/subpages/inspection/setting/select-state/index",
  });
};
</script>
<style lang="scss" scoped>
/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: #f6f8fa;
  color: #333333;
  font-size: 14px;
}
//卡片样式
.card {
  padding: 15px;
  background: #ffffff;
  box-shadow: 0px 0px 6px 0px rgba(146, 146, 146, 0.2);
  margin-bottom: 15px;
}
.basic-info {
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    margin-bottom: 5px;
    .info-label {
      color: #666666;
    }
    .info-value {
      font-size: 14px;
    }
    .select {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;
      font-size: 14px;
      color: #999999;
      height: 32px;
      .select-text {
        margin-right: 10px;
      }
    }
  }
  .remark {
    .label {
      margin: 10px 0;
      color: #666666;
    }
  }
}
.work {
  .work-title {
    font-size: 14px;
    color: #333333;
    margin-bottom: 10px;
  }
  .work-time {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap; /* 允许子元素换行 */
  justify-content: space-between; /* 两端对齐 */
}
}
.clock {
  .clock-title {
  }
  .clock-startTime {
    margin: 10px 0;
  }
  .clock-startTime,
  .clock-endTime {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .time {
      flex: 1;
      display: flex;
      flex-direction: row;
      position: relative;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      color: #999999;
      border: 1px solid #CACACA;
      height: 32px;
      border-radius: 3px;
      margin-left: 10px;
      .time-picker {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        text-align: center;
        align-items: center;
        position: absolute;
        width: 100%;
        z-index: 2;
        top: 32px;
        color: red;
        background: #ffffff;
        box-shadow: 0px 0px 6px 0px rgba(146, 146, 146, 0.2);
        border: 1px solid #CACACA;
        border-radius: 3px;
        .picker-column {
          height: 108px;
          .picker-item {
            height: 36px;
            line-height: 36px;
            // border: 1px solid rgba(153, 153, 153, 0.0977);
          }
        }
      }
    }
  }
}
.limit {
  .limit-interval {
    margin-top: 10px;
  }
  .limit-times,
  .limit-interval {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 50%;
    .limit-times-value {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;
      .count {
        text-align: center;
        color: #333333;
      }
      .unit {
        color: #666666;
      }
    }
  }
}
.btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
</style>
