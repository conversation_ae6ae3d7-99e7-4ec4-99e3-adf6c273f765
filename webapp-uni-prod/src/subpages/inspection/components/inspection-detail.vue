<template>
  <view class="container">
    <view class="basic-info">
      <view class="basic-item">
        <view class="item-label">巡检时间</view>
        <view class="item-value">{{ recordDetail.inspectionTime }}</view>
      </view>
      <view class="basic-item">
        <view class="item-label">巡检人</view>
        <view class="item-value">{{ recordDetail.inspectionMan }}</view>
      </view>
      <view class="basic-item">
        <view class="item-label">巡检结果</view>
        <view
          :class="
            recordDetail.inspectionResult == 'success' ? ' normal' : ' unusal'
          "
          >{{
            recordDetail.inspectionResult == "success" ? "正常" : "异常"
          }}</view
        >
      </view>
    </view>
    <view class="basic-msg">
      <view class="msg-title"><span class="split-line"></span>巡检信息</view>
      <div class="msg-desc" v-if="recordDetail.remarks">
        <view class="desc-label">巡检说明</view>
        <view class="desc-value">{{ recordDetail.remarks }}</view>
      </div>
      <div class="desc-empty" v-if="!recordDetail.remarks">
        <view class="desc-label">巡检说明</view>
        <view class="desc-value">--</view>
      </div>
      <view class="msg-record" v-if="recordDetail">
        <view class="record-label">图片/视频</view>
        <view>111</view>
      </view>
      <view class="record-empty" v-if="!recordDetail">
        <view class="record-label">巡检记录</view>
        <view>--</view>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { InspectionDetail } from "@/subpages/inspection/models/inspection";
const props = defineProps({
  options: {
    type: Object as () => InspectionDetail,
    default: () => ({}),
  },
});

const recordDetail = ref({} as InspectionDetail);
onMounted(() => {
  getData();
});
const getData = () => {
  recordDetail.value = props.options;
};
</script>
<style lang="scss" scoped>
.container {
  height: 100vh;
  width: 100vw;
  background: #f6f8fa;
}
.basic-info {
  font-size: 14px;
  padding: 14px 14px 2px 14px;
  margin-bottom: 10px;
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  .basic-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 12px;
    .item-label {
      color: #666666;
    }
    .item-value {
      color: #333333;
    }
  }
}
.basic-msg {
  font-size: 14px;
  padding-top: 14px;
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  .msg-title {
    font-weight: 500;
    margin-left: 8px;
    .split-line {
      display: inline-block;
      width: 2px;
      height: 13px;
      background: #1a58b2;
      margin-right: 4px;
    }
  }
  .msg-desc,
  .desc-empty {
    display: flex;
    flex-direction: row;
    padding: 12px 14px 0 14px;
  }
  .msg-desc {
    width: 100%;
    box-sizing: border-box;
    .desc-label {
      color: #666666;
      margin-right: 34px;
    }
    .desc-value {
      flex: 1;
      color: #333333;
    }
  }
  .msg-record,
  .record-empty {
    color: #666666;
    padding: 12px 14px 14px 14px;
  }
  .record-empty,
  .desc-empty {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    color: #666666;
  }
}
.normal {
  color: #21843b;
}
.unusal {
  color: #af2524;
}
</style>
