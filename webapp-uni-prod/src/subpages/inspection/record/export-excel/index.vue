<template>
  <view class="container">
    <view>
      <up-radio-group v-model="radiovalue1" placement="column">
        <up-radio
          :customStyle="{ marginBottom: '8px' }"
          v-for="(item, index) in radiolist1"
          :key="index"
          :label="item.name"
          :name="item.value"
          @change="radioChange"
          activeColor="#00c472"
          iconSize="15px"
          :labelSize="labelSize"
        >
        </up-radio>
      </up-radio-group>
    </view>
    <view class="time" v-if="radiovalue1 == 4">
      <view class="startTime">
        <view>开始时间</view>
        <DatePicker
          :show="startShow"
          placeholder="请选择开始时间"
          v-model:time="startTime"
        />
      </view>
      <view class="endTime">
        <view>结束时间</view>
        <DatePicker
          :show="endShow"
          placeholder="请选择结束时间"
          v-model:time="endTime"
        />
      </view>
    </view>
    <view class="btn">
      <BaseButton btnType="cancel" style="margin-right: 10px" @click="onBack()"
        >取消</BaseButton
      >
      <BaseButton btnType="save" @click="onSave()">完成</BaseButton>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import inspectionService from "@/subpages/inspection/api/inspection.service";
import DatePicker from "@/subpages/inspection/components/date-picker.vue";
import { toRpx } from "@/utils/toRpx";
const startShow = ref(false);
const endShow = ref(false);
const startTime = ref("");
const endTime = ref("");
const labelSize = toRpx(14);
// 基本案列数据
const radiolist1 = reactive([
  {
    name: "本月",
    value: 1,
  },
  {
    name: "本季度",
    value: 2,
  },
  {
    name: "本年度",
    value: 3,
  },
  {
    name: "自定义",
    value: 4,
  },
]);

// up-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
const radiovalue1 = ref<number>(1);
const radioChange = (n: any) => {
  radiovalue1.value = n;
  startTime.value = "";
  endTime.value = "";
};
const onBack = () => {
  uni.navigateBack();
};
// 格式化日期为 YYYY-MM-DD 格式
const formatDateTime = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};
const onSave = async () => {
  if (radiovalue1.value === 1) {
    // 计算本月开始时间和结束时间
    const startDate = new Date(
      new Date().getFullYear(),
      new Date().getMonth(),
      1
    );
    const endDate = new Date(
      new Date().getFullYear(),
      new Date().getMonth() + 1,
      0,
      23,
      59,
      59
    ); // 明确设置结束时间为23:59:59

    startTime.value = formatDateTime(startDate);
    endTime.value = formatDateTime(endDate);
  } else if (radiovalue1.value === 2) {
    // 计算本季度开始时间和结束时间
    let month = new Date().getMonth();
    let year = new Date().getFullYear();
    let startMonth = 0;
    let endMonth = 0;

    if (month < 3) {
      startMonth = 0;
      endMonth = 2;
    } else if (month < 6) {
      startMonth = 3;
      endMonth = 5;
    } else if (month < 9) {
      startMonth = 6;
      endMonth = 8;
    } else {
      startMonth = 9;
      endMonth = 11;
    }

    const startDate = new Date(year, startMonth, 1);
    const endDate = new Date(year, endMonth + 1, 0, 23, 59, 59); // 结束时间设为23:59:59

    startTime.value = formatDateTime(startDate);
    endTime.value = formatDateTime(endDate);
  } else if (radiovalue1.value === 3) {
    // 计算本年开始时间和结束时间
    const startDate = new Date(new Date().getFullYear(), 0, 1);
    const endDate = new Date(new Date().getFullYear(), 11, 31, 23, 59, 59); // 结束时间设为23:59:59

    startTime.value = formatDateTime(startDate);
    endTime.value = formatDateTime(endDate);
  } else if (radiovalue1.value === 4) {
    if (!startTime.value) {
      uni.showToast({
        title: "请选择开始时间",
        icon: "none",
      });
      return;
    }
    if (!endTime.value) {
      uni.showToast({
        title: "请选择结束时间",
        icon: "none",
      });
      return;
    }
    if (startTime.value > endTime.value) {
      uni.showToast({
        title: "请选择正确的时间范围",
        icon: "none",
      });
      return;
    }
  }
  const res = await inspectionService.downloadRecord({
    startTime: startTime.value,
    endTime: endTime.value,
  });
  wx.downloadFile({
    url: res,
    success: (res) => {
      if (res.statusCode === 200) {
        const tempFilePath = res.tempFilePath;
        uni.saveFile({
          tempFilePath: tempFilePath,
          filePath:
            (uni as any).env.USER_DATA_PATH +
            "/" +
            `巡检记录${startTime.value}~${endTime.value}` +
            ".xlsx", // fileName 需要保存的文件名称
          success: (res) => {
            // const savedFilePath = res.savedFilePath;
            uni.showToast({
              title: "下载成功",
              icon: "success",
            });
            wx.openDocument({
              filePath: res.savedFilePath,
              showMenu: true,
              success: function (res) {
                console.log("打开成功");
              },
            });
          },
          fail: (err) => {},
        });
      }
    },
    fail: (err) => {
      uni.showToast({
        title: "下载失败",
        icon: "error",
      });
    },
  });
};
</script>
<style lang="scss" scoped>
.container {
  padding: 15px;
  font-size: 14px;
  .time {
    margin-left: 20px;
    .startTime,
    .endTime {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 3px;
      margin: 10px 0;
      .select-time {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 185px;
        height: 32px;
        padding: 0 5px;
        font-size: 14px;
        color: #999999;
        border: 1px solid #cacaca;
        border-radius: 3px;
        .time-text {
          color: #333333;
        }
      }
    }
  }
}
.btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 30px;
}
</style>
