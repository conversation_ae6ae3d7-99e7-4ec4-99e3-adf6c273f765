<template>
  <view class="container">
    <view>
      <up-radio-group
        v-model="radiovalue1"
        placement="column"
      >
        <up-radio
          :customStyle="{ marginBottom: '8px' }"
          v-for="(item, index) in radiolist1"
          :key="index"
          :label="item.name"
          :name="item.value"
          @change="radioChange"
          activeColor="#00c472"
          iconSize="15px"
          labelSize="34rpx"
        >
        </up-radio>
      </up-radio-group>
    </view>
    <view class="time" v-if="radiovalue1 === 4">
      <view class="startTime">
        <view>开始时间</view>
        <view class="select-time" @click="startShow = true">
          <view :class="diyStartTime ? 'time-text' : ''"> {{diyStartTime || '请选择开始时间'}} </view>
          <view>
            <SlSVgIcon name="22-22-42" size="22" />
          </view>
        </view>
      </view>
      <view class="endTime">
        <view>结束时间</view>
        <view class="select-time" @click="endShow = true">
            <view :class="diyEndTime ? 'time-text' : ''"> {{diyEndTime || '请选择结束时间'}} </view>
          <view>
            <SlSVgIcon name="22-22-42" size="22" />
          </view>
        </view>
      </view>
    </view>
    <view class="btn">
      <BaseButton btnType="cancel" style="margin-right: 10px" @click="onBack()">取消</BaseButton>
      <BaseButton btnType="save" @click="onSave()">完成</BaseButton>
    </view>
  </view>
  <up-popup
    :show="startShow"
    :round="10" 
    mode="bottom"
    class="popup"
    :closeOnClickOverlay="true" 
  >
    <view class="title">
      <view class="cancel" @click="show = false">取消</view>
      <view class="title-text">选择时间</view>
      <view class="confirm" @click="submitStart()">确定</view>
    </view>
    <view class="time">
      <!-- <DatePicker @db-change-item="onDateItem" v-model="currentDate" :is-pure-mode="false" /> -->
      <DatePicker @change-item="onDateItem" v-model="currentDate"  />
    </view>
  </up-popup>

  <up-popup
    :show="endShow"
    :round="10" 
    mode="bottom"
    class="popup"
    :closeOnClickOverlay="true" 
  >
    <view class="title">
      <view class="cancel" @click="endShow = false">取消</view>
      <view class="title-text">选择时间</view>
      <view class="confirm" @click="submitEnd()">确定</view>
    </view>
    <view class="time">
      <!-- <DatePicker @db-change-item="onDateItem" v-model="currentDate" :is-pure-mode="false" /> -->
      <DatePicker @change-item="onDateItem" v-model="currentDate"  />
    </view>
  </up-popup>

  <!-- <up-calendar :show="startShow" round="17px" monthNum="6"></up-calendar>
  <up-calendar :show="endShow" round="17px" monthNum="6"></up-calendar> -->
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";

const startShow = ref(false);
const endShow = ref(false);
const show = ref(true);
const currentDate = ref('');
const diyStartTime = ref("");
const diyEndTime = ref("");
const startTime = ref("");
const endTime = ref("");
// 基本案列数据
const radiolist1 = reactive([
  {
    name: "本月",
    value: 1,
  },
  {
    name: "本季度",
    value: 2,
  },
  {
    name: "本年度",
    value: 3,
  },
  {
    name: "自定义",
    value: 4,
  },
]);

// up-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
const radiovalue1 = ref<number>(1);
const radioChange = (n: any) => {
  console.log("radioChange", n);
  radiovalue1.value = n;
};
const onDateItem = (item: { date: string, id: string }) => {
    console.log("🚀 ~ index.vue ~ onDateItem ~ item:", item)
    currentDate.value = item.date || ''
}
const submitStart = () => {
    console.log(currentDate.value)
    diyStartTime.value = currentDate.value
    startShow.value = false
}
const submitEnd = () => {
    console.log(currentDate.value)
    diyEndTime.value = currentDate.value
    endShow.value = false
}
const onBack = () => {
    uni.navigateBack()
}
const onSave = () => {
    if (radiovalue1.value === 1) {
    //计算本月开始时间和结束时间
    startTime.value = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toLocaleDateString()
    endTime.value = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toLocaleDateString()
  } else if (radiovalue1.value === 2) {
    //计算本季度开始时间和结束时间
    let month = new Date().getMonth()
    let year = new Date().getFullYear()
    let startMonth = 0
    let endMonth = 0
    if (month < 3) {
      startMonth = 0
      endMonth = 2
    } else if (month < 6) {
      startMonth = 3
      endMonth = 5
    } else if (month < 9) {
      startMonth = 6
      endMonth = 8
    } else {
      startMonth = 9
      endMonth = 11
    }
    startTime.value = new Date(year, startMonth, 1).toLocaleDateString()
    endTime.value = new Date(year, endMonth + 1, 0).toLocaleDateString()
  } else if (radiovalue1.value === 3) {
    //计算本年开始时间和结束时间
    startTime.value = new Date(new Date().getFullYear(), 0, 1).toLocaleDateString()
    endTime.value = new Date(new Date().getFullYear(), 11, 31).toLocaleDateString()
  } else if (radiovalue1.value === 4) {
    if (!diyStartTime.value) {
      uni.showToast({
        title: "请选择开始时间",
        icon: "none",
      })
      return
    }
    if (!diyEndTime.value) {
      uni.showToast({
        title: "请选择结束时间",
        icon: "none",
      })
      return
    }
    startTime.value = diyStartTime.value
    endTime.value = diyEndTime.value
  }
  //开始下载逻辑
  
  console.log(startTime.value, endTime.value)
}
</script>
<style lang="scss" scoped>
.container {
  padding: 15px;
  font-size: 14px;
  .time {
    margin-left: 20px;
    .startTime,
    .endTime {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 3px;
      margin: 10px 0;
      .select-time {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 185px;
        height: 32px;
        padding: 0 5px;
        font-size: 14px;
        color: #999999;
        border: 1px solid #cacaca;
        .time-text {
          color: #333333;
        }
      }
    }
  }
}
.popup {
  .title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-size: 14px;
    padding: 15px 25px;
    color: #333333;
    border-bottom: 1px solid rgba(153, 153, 153, 0.0977);
    .cancel {
    }
    .title-text {
        font-weight: 600;
    }
    .confirm {
        font-weight: 350;
        color: #005CC8;
    }
  }
}
.btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 30px;
}
</style>
