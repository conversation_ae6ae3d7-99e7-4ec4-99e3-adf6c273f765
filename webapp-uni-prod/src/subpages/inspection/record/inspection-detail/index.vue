<template>
  <PatrilDeatil type="inspection" :options="data" />
</template>
<script lang="ts" setup>
import PatrilDeatil from "@/subpages/inspection/components/patrol-detail.vue";
import { Station } from "@/subpages/inspection/models/inspection";
import { useInspectionStore } from "@/subpages/inspection/utils/stores";
const store = useInspectionStore();
const data = ref({} as Station);

onLoad((options) => {
  getData();
});
const getData = async () => {
  console.log(store.inspectionRecord, "store.inspectionRecord111");
  data.value = store.inspectionRecord;
  console.log(data.value, "data.value");
};
</script>
<style lang="scss" scoped></style>
