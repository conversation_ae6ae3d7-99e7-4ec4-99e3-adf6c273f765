<template>
  <PatrilDeatil :type="type" :options="data" />
</template>
<script lang="ts" setup>
import PatrilDeatil from "@/subpages/inspection/components/patrol-detail.vue";
import { Station } from "@/subpages/inspection/models/inspection";
import { useInspectionStore } from "@/subpages/inspection/utils/stores";
type DetailType = "station" | "inspection";
const store = useInspectionStore();
const data = ref({} as Station);
const type = ref<DetailType>("station");

onLoad((options) => {
  if (options?.type) {
    console.log(options?.type, "options?.type");
    if (options?.type == "patrol") {
      type.value = "station";
    } else {
      type.value = "inspection";
    }
  }
  getData();
});
const getData = async () => {
  console.log(store.inspectionRecord, "store.inspectionRecord111");
  data.value = store.inspectionRecord;
  console.log(data.value, "data.value");
};
</script>
<style lang="scss" scoped></style>
