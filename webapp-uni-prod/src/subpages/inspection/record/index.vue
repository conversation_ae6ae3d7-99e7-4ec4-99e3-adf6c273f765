<template>
  <SlTabbarPage :tab-bar-root="'subpages/inspection'">
    <view class="container">
      <!-- 吸顶头部 -->
      <view class="sticky-header">
        <view class="header-content">
          <view class="person-list">
            <view class="person-item" v-for="item in personType" :key="item.id">
              <view
                class="item-name"
                :class="{ active: item.id === activePerson }"
                @click="activePerson = item.id"
                >{{ item.name }}</view
              >
            </view>
          </view>
          <view class="time-list">
            <!-- <CalendarComponent></CalendarComponent> -->
          </view>
        </view>
      </view>

      <!-- 主体内容区域 -->
      <view class="main-container">
        <!-- 左侧楼层导航 -->
        <scroll-view
          scroll-y
          class="left-nav"
          :style="{ height: scrollViewHeight + 'px' }"
        >
          <view
            v-for="item in floorData"
            :key="item.id"
            @click="handleFloorClick(item)"
          >
            <view
              class="scroll-view-item"
              :class="{ active: item.id === activeValue }"
            >
              {{ item.name }}
            </view>
          </view>
        </scroll-view>

        <!-- 右侧内容区域 -->
        <scroll-view
          scroll-y
          class="right-content"
          :style="{ height: scrollViewHeight + 'px' }"
        >
          <view class="card-container">
            <view class="card-item" v-for="item in recordData" :key="item.id">
              <view
                :class="[
                  item.times === item.allTimes
                    ? 'card-item-content finish'
                    : 'card-item-content unfinish',
                ]"
                @click="goDetail(item)"
              >
                <view class="card-item-position">{{ item.position }}</view>
                <view>
                  <text :class="item.times == item.allTimes ? '' : 'gray'">{{
                    item.times
                  }}</text
                  >/{{ item.allTimes }}
                </view>
              </view>
            </view>
            <up-loadmore :status="status" />
          </view>
          <BaseEmpty v-if="recordData.length == 0" />
        </scroll-view>
      </view>

      <!-- 导出按钮 -->
      <view class="btn">
        <BaseButton btn-type="save" size="large" @click="onExport()"
          >导出Excel</BaseButton
        >
      </view>

      <!-- 底部Tabbar -->
      <!-- <Tabbar
      :current="currentTab" 
      @change="handleTabChange"
     /> -->
    </view>
  </SlTabbarPage>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from "vue";
import Tabbar from "@/subpages/inspection/components/tabbar.vue";
import BaseButton from "@/components/BaseButton.vue";
import { LoadStatus } from "@/models/inspection";

// 当前激活的tab索引
const currentTab = ref(0);

// 处理tab切换
const handleTabChange = (index: any) => {
  currentTab.value = index;
};

const activeValue = ref<number | null>(1);
const scrollViewHeight = ref<number>(0); // 滚动区域高度
//加载更多状态
const status = ref<LoadStatus>("loadmore");

// 顶部人员选中
const activePerson = ref<number | null>(1);
// 头部人员分类
const personType = ref([
  { id: 1, name: "白班巡检" },
  { id: 2, name: "夜班巡检" },
  { id: 3, name: "物业巡检" },
  { id: 4, name: "保洁清洁" },
]);

const recordData = ref([
  {
    id: 1,
    times: 1,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 3,
  },
  {
    id: 1,
    times: 1,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 3,
  },
  {
    id: 1,
    times: 3,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 3,
  },
  {
    id: 1,
    times: 3,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 3,
  },
  {
    id: 1,
    times: 3,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 3,
  },
  {
    id: 1,
    times: 3,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 3,
  },
  {
    id: 1,
    times: 1,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 3,
  },
  {
    id: 1,
    times: 1,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 3,
  },
  {
    id: 1,
    times: 1,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 3,
  },
  {
    id: 1,
    times: 1,
    position: "B1点位1",
    name: "张三",
    status: "异常",
    time: "2022-01-01 10:00:00",
    allTimes: 3,
  },
  {
    id: 1,
    times: 1,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 2,
  },
  {
    id: 1,
    times: 3,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 3,
  },
  {
    id: 1,
    times: 3,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 3,
  },
  {
    id: 1,
    times: 3,
    position: "B1点位1",
    name: "张三",
    status: "正常",
    time: "2022-01-01 10:00:00",
    allTimes: 4,
  },
]);

// 楼层数据
const floorData = ref([
  { id: 1, name: "B1" },
  { id: 2, name: "F1" },
  { id: 3, name: "F2" },
  { id: 4, name: "F3" },
  { id: 5, name: "F4" },
  { id: 6, name: "F5" },
  { id: 7, name: "F6" },
  { id: 8, name: "F7" },
  { id: 9, name: "F8" },
  { id: 10, name: "F9" },
  { id: 11, name: "F10" },
  { id: 12, name: "F11" },
  { id: 13, name: "F12" },
  { id: 14, name: "F13" },
  { id: 15, name: "F14" },
  { id: 16, name: "F15" },
  { id: 17, name: "F16" },
  { id: 18, name: "F17" },
  { id: 19, name: "F18" },
  { id: 20, name: "F19" },
]);

onMounted(() => {
  calculateScrollHeight();
  uni.onWindowResize(calculateScrollHeight);
});

// 计算滚动区域高度
const calculateScrollHeight = () => {
  const systemInfo = uni.getSystemInfoSync();
  const headerHeight = 38 + 70; // person-list和time-list的总高度
  const bottomHeight = 80; // 导出按钮和Tabbar的总高度

  scrollViewHeight.value =
    systemInfo.windowHeight - headerHeight - bottomHeight;
  console.log("scrollHeight", scrollViewHeight.value);
};

// 左侧导航栏选择
const handleFloorClick = (item: any) => {
  activeValue.value = item.id;
};

// 跳转到详情页
const goDetail = (item: any) => {
  console.log(111, item);
  uni.navigateTo({
    url: `inspection-detail/index?id=${item.id}`,
  });
};

const onExport = () => {
  uni.navigateTo({
    url: "export-excel/index",
  });
};

onUnmounted(() => {
  uni.offWindowResize(calculateScrollHeight);
});
</script>

<style lang="scss" scoped>
/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: #f6f8fa;
}

/* 吸顶头部样式 */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.header-content {
  display: flex;
  flex-direction: column;
}

/* 主体内容容器 */
.main-container {
  display: flex;
  flex: 1;
  overflow: hidden; /* 避免外层滚动 */
  margin-top: 10px;
}

/* 左侧导航样式 */
.left-nav {
  width: 54px;
  background-color: #fff;
  scrollbar-width: none; /* 隐藏滚动条 */
  position: relative;

  &::-webkit-scrollbar {
    display: none;
  }

  .scroll-view-item {
    height: 40px;
    font-size: 14px;
    color: #636363;
    line-height: 40px;
    text-align: center;
    &.active {
      position: relative;
      color: #333333;
      font-weight: bold;
      // border-left: 3px solid #005CC8;
      &::before {
        display: inline-block;
        width: 3px;
        height: 20px;
        background: #005cc8;
        content: "";
        position: absolute;
        top: 11px;
        left: 5px;
        transition: all 0.3s ease; /* 添加过渡效果 */
      }
    }
  }
}

/* 右侧内容区域 */
.right-content {
  flex: 1;
  // padding: 10px;
  overflow-y: auto;
}

.card-container {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 10px;
}

.card-item {
  width: calc(50% - 20px); // 一行两个，减去间距
  margin: 10px;
}

.card-item-content {
  border-radius: 10px;
  text-align: center;
  height: 60px;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.finish {
  border: 1px solid #1db968;
  background: #f3fff9;
}

.unfinish {
  border: 1px solid #cacaca;
  background-color: #fff;
}

/* 导出按钮 */
.btn {
  position: fixed;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  padding: 15px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 10px 10px 0px 0px;
  box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);
  z-index: 10;
}

/* 顶部人员导航 */
.person-list {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  width: 100%;
  background: #f9f9f9;
  position: relative;

  .person-item {
    width: 70px;
    height: 38px;
    line-height: 38px;
    text-align: center;

    .item-name {
      font-size: 14px;
      color: #666666;
      &.active {
        position: relative;
        color: #333333;
        font-weight: bold;
        // border-left: 3px solid #005CC8;
        &::before {
          display: inline-block;
          width: 20px;
          height: 3px;
          background: #005cc8;
          content: "";
          position: absolute;
          bottom: 4px;
          left: 25px;
          transition: all 0.3s ease; /* 添加过渡效果 */
        }
      }
    }
  }
}

/* 时间列表 */
.time-list {
  width: 100%;
  height: 70px;
  background-color: #fff;
}

/* 颜色置灰色 */
.gray {
  color: #999999;
}

/* 被点击的状态 */
.actived {
  font-weight: 700;
}

/* 吸顶优化 - iOS */
@media (device-type: ios) {
  .sticky-header {
    position: -webkit-sticky;
  }
}
</style>
