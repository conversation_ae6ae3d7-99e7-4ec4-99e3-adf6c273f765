<template>
  <SlTabbarPage :tab-bar-root="'subpages/inspection'">
    <view class="container">
      <!-- 吸顶头部 -->
      <view class="sticky-header">
        <view class="header-content">
          <view class="person-list">
            <view class="person-item" v-for="item in personType" :key="item.id">
              <view
                class="item-name"
                :class="{ active: item.id === activePerson }"
                @click="onPersonChange(item.id)"
                >{{ item.name }}</view
              >
            </view>
          </view>
          <view class="time-list">
            <CalendarComponent
              :base-date="currDate"
              @dateChange="onDateChange"
              :date-data="backendDateData"
            />
          </view>
        </view>
      </view>

      <!-- 主体内容区域 -->
      <view class="main-container">
        <!-- 左侧楼层导航 -->
        <scroll-view scroll-y class="left-nav">
          <view
            v-for="item in floorData"
            :key="item.id"
            @click="handleFloorClick(item.floorName)"
          >
            <view
              class="scroll-view-item"
              :class="{ active: item.floorName === activeValue }"
            >
              {{ item.floorName }}
            </view>
          </view>
        </scroll-view>

        <!-- 右侧内容区域 -->
        <scroll-view scroll-y class="right-content">
          <view class="card-container" v-if="recordData.length > 0">
            <view class="card-item" v-for="item in recordData" :key="item.id">
              <view
                :class="[
                  Number(item.checkInProgress.split('/')[0]) ===
                  Number(item.checkInProgress.split('/')[1])
                    ? 'card-item-content finish'
                    : 'card-item-content unfinish',
                ]"
                @click="goDetail(item)"
              >
                <view class="card-item-position">{{ item.siteName }}</view>
                <view>
                  <text
                    :class="
                      Number(item.checkInProgress.split('/')[0]) ===
                      Number(item.checkInProgress.split('/')[1])
                        ? ''
                        : 'gray'
                    "
                    >{{ item.checkInProgress.split("/")[0] }}</text
                  >/{{ item.checkInProgress.split("/")[1] }}
                </view>
              </view>
            </view>
            <!-- <up-loadmore :status="status" /> -->
          </view>
          <BaseEmpty v-else />
        </scroll-view>
      </view>

      <!-- 导出按钮 -->
      <view class="btn">
        <BaseButton btn-type="save" size="large" @click="onExport()"
          >导出Excel</BaseButton
        >
      </view>
    </view>
  </SlTabbarPage>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import CalendarComponent from "@/components/CalendarComponent.vue";
import BaseButton from "@/components/BaseButton.vue";
import inspectionService from "@/service/inspection";
import { useRepairStore } from "@/store";
import { useInspectionStore } from "@/subpages/inspection/utils/stores";
import { Floor, InspectionParams, Station } from "../models/inspection";
import dayjs from "dayjs";
interface RecordData {
  [floorName: string]: Station[];
}
// import { LoadStatus } from "@/models/inspection";
const store = useRepairStore();
const inspectionStore = useInspectionStore();
const backendDateData = ref([
  {
    id: "",
    date: "2025-06-07",
    officialType: 0,
    manualType: 0,
    officialName: "",
    type: 4,
    dayOrHolidayName: "周六",
  },
  {
    id: "",
    date: "2025-06-08",
    officialType: 0,
    manualType: 0,
    officialName: "",
    type: 4,
    dayOrHolidayName: "周日",
  },
  {
    id: "",
    date: "2025-06-09",
    officialType: 0,
    manualType: 0,
    officialName: "",
    type: 3,
    dayOrHolidayName: "周一",
  },
  {
    id: "",
    date: "2025-06-10",
    officialType: 0,
    manualType: 0,
    officialName: "",
    type: 3,
    dayOrHolidayName: "周二",
  },
  {
    id: "",
    date: "2025-06-11",
    officialType: 0,
    manualType: 0,
    officialName: "",
    type: 3,
    dayOrHolidayName: "周三",
  },
]);
const currDate = ref(dayjs().format("YYYY-MM-DD"));
const backDate = ref(dayjs().format("YYYYMMDD"));

const activeValue = ref<string>("");
//加载更多状态
// const status = ref<LoadStatus>("loadmore");

// 顶部人员选中
const activePerson = ref<string>("white");
// 头部人员分类
const personType = ref([
  { id: "white", name: "白班巡检" },
  { id: "night", name: "夜班巡检" },
  { id: "property", name: "物业巡检" },
  { id: "clean", name: "保洁清洁" },
]);

const recordData = ref([] as Station[]);

// 楼层数据
const floorData = ref([] as Floor[]);
const queryRecord = reactive<InspectionParams>({
  queryDate: "2025-06-27",
  // loginUserRole: store.ifStaff ? "workers" : "administrator",
  loginUserRole: "administrator",
  inspectionTeamType: "property",
});
onLoad(() => {
  getFloorList();
  getRecordList();
});
const getFloorList = async () => {
  const res = await inspectionService.getSiteAreaList({
    ifPage: false,
  });
  // 确保 res 是数组
  if (Array.isArray(res)) {
    floorData.value = res.map((item: Floor) => ({
      ...item,
    }));
    //将activeValue赋值给第一个楼层的id
    activeValue.value = floorData.value[0].floorName;
  } else {
    floorData.value = [];
  }
};
const getRecordList = async () => {
  if (queryRecord.loginUserRole == "workers") {
    queryRecord.inspectionTeamType = "";
  } else {
    queryRecord.inspectionTeamType = activePerson.value;
  }
  const res = await inspectionService.getRecordList(queryRecord);
  //判断res是否为对象
  if (typeof res === "object") {
    //根据接口返回数据找到当前楼层对应的数据
    const floor = res[activeValue.value];
    if (floor) {
      recordData.value = floor;
    } else {
      recordData.value = [];
    }
  } else {
    recordData.value = [];
  }
};
const onDateChange = (date: string) => {
  currDate.value = date;
  backDate.value = dayjs(date).format("YYYY-MM-DD");
  console.log("currDate", currDate.value, date);
};
const onPersonChange = (id: string) => {
  activePerson.value = id;
  getRecordList();
};

// 左侧导航栏选择
const handleFloorClick = (name: string) => {
  activeValue.value = name;
  getRecordList();
};

// 跳转到详情页
const goDetail = (item: Station) => {
  console.log("item", item);
  inspectionStore.inspectionRecord = item;
  uni.navigateTo({
    url: `/subpages/inspection/record/inspection-detail/index`,
  });
};

const onExport = () => {
  uni.navigateTo({
    url: "/subpages/inspection/record/export-excel/index",
  });
};
</script>

<style lang="scss" scoped>
/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: #f6f8fa;
}

/* 吸顶头部样式 */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.header-content {
  display: flex;
  flex-direction: column;
}

/* 主体内容容器 */
.main-container {
  display: flex;
  flex: 1;
  overflow: hidden; /* 避免外层滚动 */
  margin-top: 10px;
  height: calc(
    100% - 38px - 66px - 10px - 50px - 70px
  ); //减去顶部导航-时间-margin-底部-导出按钮高度
  overflow: hidden;
  box-sizing: border-box;
}

/* 左侧导航样式 */
.left-nav {
  width: 54px;
  height: 100%;
  background-color: #fff;
  scrollbar-width: none; /* 隐藏滚动条 */
  position: relative;

  &::-webkit-scrollbar {
    display: none;
  }

  .scroll-view-item {
    height: 40px;
    font-size: 14px;
    color: #636363;
    line-height: 40px;
    text-align: center;
    &.active {
      position: relative;
      color: #333333;
      font-weight: bold;
      // border-left: 3px solid #005CC8;
      &::before {
        display: inline-block;
        width: 3px;
        height: 20px;
        background: #005cc8;
        content: "";
        position: absolute;
        top: 11px;
        left: 5px;
        transition: all 0.3s ease; /* 添加过渡效果 */
      }
    }
  }
}

/* 右侧内容区域 */
.right-content {
  flex: 1;
  height: 100%;
  box-sizing: border-box;
}

.card-container {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 10px;
}

.card-item {
  width: calc(50% - 20px); // 一行两个，减去间距
  margin: 10px;
}

.card-item-content {
  border-radius: 10px;
  text-align: center;
  height: 60px;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.finish {
  border: 1px solid #1db968;
  background: #f3fff9;
}

.unfinish {
  border: 1px solid #cacaca;
  background-color: #fff;
}

/* 导出按钮 */
.btn {
  position: fixed;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  padding: 15px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 10px 10px 0px 0px;
  box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);
  z-index: 10;
}

/* 顶部人员导航 */
.person-list {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  width: 100%;
  background: #f9f9f9;
  position: relative;

  .person-item {
    width: 70px;
    height: 38px;
    line-height: 38px;
    text-align: center;

    .item-name {
      font-size: 14px;
      color: #666666;
      &.active {
        position: relative;
        color: #333333;
        font-weight: bold;
        // border-left: 3px solid #005CC8;
        &::before {
          display: inline-block;
          width: 20px;
          height: 3px;
          background: #005cc8;
          content: "";
          position: absolute;
          bottom: 4px;
          left: 25px;
          transition: all 0.3s ease; /* 添加过渡效果 */
        }
      }
    }
  }
}

/* 时间列表 */
.time-list {
  display: flex;
  align-items: center;
  width: 100%;
  height: 66px;
  background-color: #fff;
}

/* 颜色置灰色 */
.gray {
  color: #999999;
}

/* 被点击的状态 */
.actived {
  font-weight: 700;
}

/* 吸顶优化 - iOS */
@media (device-type: ios) {
  .sticky-header {
    position: -webkit-sticky;
  }
}
</style>
