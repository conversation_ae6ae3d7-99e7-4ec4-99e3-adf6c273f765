.form {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
}

.input-group {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  height: 40px;
  align-items: center;
  border-bottom: 1px solid rgba(153, 153, 153, 0.4);
  position: relative;
  justify-content: space-between;
  .input {
    flex: 1;
    height: 100%;
    box-sizing: border-box;
  }

  .clear,
  .eye {
    display: inline-flex;
    align-items: center;
    justify-content: flex-end;
    padding: 4px;
    z-index: 1;
  }
  .eye {
    padding: 0;
    margin-left: 11px;
  }
}

.tip {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: -18px;
  left: 0;
  width: 100%;
  font-size: $uni-font-size-sm;
}

.inline-flex {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}

.tip .text {
  color: $uni-text-color-grey;
  font-size: $uni-font-size-sm;

  &.error {
    color: #ff4e4e;
  }
}

.input-group {
  .country-code {
    flex: 0 0 auto;
  }

  .divider {
    flex: 0 0 auto;
    text-align: center;
    color: rgba(153, 153, 153, 0.4);
    margin-left: 6px;
    margin-right: 6px;
  }

  .btn-code {
    box-sizing: border-box;
    height: 100%;
    display: inline-flex;
    align-items: center;
    color: rgba(46, 103, 226, 1);
    border-radius: 0;
    padding: 0 0 0 10px;
    height: 32px;
    background-color: #fff;
    font-size: 14px;

    &.disabled {
      color: #666666;
    }
  }
}
