import { AuthLoginUser } from "@/models/Account";
import authService from "@/service/auth";
import { usePrincipalStore } from "@/store";
const principalStore = usePrincipalStore();
export const login = async (user: AuthLoginUser) => {
  try {
    // 认证
    await authService.authenticate(user);
    // 登录成功后设置身份信息
    await principalStore.identity(true);

  } catch (error) {
    // 认证失败，清除身份信息
    await principalStore.clearIndentity();
    throw error; // 重新抛出错误以便上层处理
  }
};
