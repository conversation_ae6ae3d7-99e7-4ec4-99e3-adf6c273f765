<template>
    <LoginPage :is-login-in="isLoginIn" @login="submit">
        <view class="form">
            <!-- 账号输入 -->
            <view class="input-group">
                <input class="input" type="text" v-model="username" placeholder="请输入账号" :maxlength="11" />
            </view>
            <!-- 密码输入 -->
            <view class="input-group">
                <input class="input" type="text" v-model="pwd" placeholder="请输入密码" :minLength="8" />
            </view>
            <view class="tip" v-if="tip">
                <SlSubSvgIcon class="inline-flex" subpage="login" name="12-12-11" size="10" />
                <text class="text error">{{ tip }}</text>
            </view>
        </view>
    </LoginPage>
</template>
<script setup lang="ts">
import LoginPage from '@/subpages/login/components/LoginPage.vue';
import { useLogin } from '@/composables/useLogin'
import { AuthLoginUser } from '@/models/Account';
const { login } = useLogin();
// TODO ： 移除默认账号密码
const username = ref('***********');//***********
const pwd = ref('demo123');
const tip = ref('')
const isLoginIn = ref(false); // 是否正在登录
const isEmpty = (value: string) => {
    return value === null || value === undefined || value.trim() === '';
};
//登录逻辑
const submit = async () => {
    if (isEmpty(username.value) || isEmpty(pwd.value)) {
        uni.showToast({
            title: '请输入账号和密码',
            icon: 'none'
        });
        return;
    }
    isLoginIn.value = true; // 设置正在登录状态
    const user: AuthLoginUser = {
        j_username: username.value,
        j_password: pwd.value
    };
    try {
        await login(user, 'password')
    } catch (error) {
        console.error('账号密码登录失败:', error);
        tip.value = '账号或密码错误，请稍后再试';
        return;
    } finally {
        // 清空输入框
        isLoginIn.value = false; // 重置登录状态
    }
};
</script>

<style lang="scss" scoped>
.form {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    width: 100%;
}

.input-group {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    height: 40px;
    align-items: center;
    border-bottom: 1px solid rgba(153, 153, 153, 0.4);

    .input {
        flex: 1;
        height: 100%;
        box-sizing: border-box;
    }
}

.tip {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: -18px;
    left: 0;
    width: 100%;
}

.inline-flex {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
}

.tip .text {
    color: $uni-text-color-grey;
    font-size: $uni-font-size-sm;

    &.error {
        color: #FF4E4E;
    }
}
</style>