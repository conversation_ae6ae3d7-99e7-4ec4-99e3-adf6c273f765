<template>
    <LoginPage :is-login-in="isLoginIn" @login="submit">
        <view class="form">
            <!-- 账号输入 -->
            <view class="input-group ">
                <input class="input" type="text" v-model="username" placeholder="请输入账号" :maxLength="10"
                    @focus="onUsernameFocus" @blur="onUsernameBlur" />
                <view class="clear" v-if="showClearUsername" @tap="username = ''">
                    <SlSubSvgIcon class="inline-flex" subpage="login" name="16-16-30" size="16" />
                </view>
            </view>
            <!-- 密码输入 -->
            <view class="input-group">
                <input class="input" :password="!eyeOpen" type="text" v-model="pwd" placeholder="请输入密码" :minLength="8"
                    @focus="onPwdFocus" @blur="onPwdBlur" />
                <view class="clear" v-if="showClearPwd" @tap="pwd = ''">
                    <SlSubSvgIcon class="inline-flex" subpage="login" name="16-16-30" size="16" />
                </view>
                <view class="eye" v-if="showEye" @tap="eyeOpen = !eyeOpen">
                    <SlSubSvgIcon class="inline-flex" subpage="login" :name="eyeOpen ? '20-20-34' : '20-20-33'"
                        size="20" />
                </view>
            </view>
            <view class="tip" v-if="tip">
                <SlSubSvgIcon class="inline-flex" subpage="login" name="12-12-11" size="10" />
                <text class="text error">{{ tip }}</text>
            </view>
        </view>
    </LoginPage>
</template>
<script setup lang="ts">
import LoginPage from '@/subpages/login/components/LoginPage.vue';
import { useLogin } from '@/composables/useLogin'
import { AuthLoginUser } from '@/models/Account';
const { login } = useLogin();
const username = ref('');
const pwd = ref('');
const tip = ref('')
const isLoginIn = ref(false); // 是否正在登录
const redirectUrl = ref<string | undefined>(undefined); // 重定向地址
const eyeOpen = ref(false); // 密码可见状态
// 焦点状态
const usernameFocused = ref(false)
const pwdFocused = ref(false)

//计算是否显示「清除」图标
const showClearUsername = computed(() => usernameFocused.value && username.value.length > 0)
const showClearPwd = computed(() => pwdFocused.value && pwd.value.length > 0)
const showEye = computed(() => pwd.value.length > 0)
onLoad((options: any) => {
    if (options.redirect) {
        redirectUrl.value = decodeURIComponent(options.redirect);
    }
});
const isEmpty = (value: string) => {
    return value === null || value === undefined || value.trim() === '';
};
//登录逻辑
const submit = async () => {
    if (isEmpty(username.value) || isEmpty(pwd.value)) {
        uni.showToast({
            title: '请输入账号和密码',
            icon: 'none'
        });
        return;
    }
    isLoginIn.value = true; // 设置正在登录状态
    const user: AuthLoginUser = {
        j_username: username.value,
        j_password: pwd.value
    };
    tip.value = ''; // 清空提示信息
    try {
        await login(user, 'password', redirectUrl.value);
    } catch (error) {
        tip.value = '账号或密码错误，请重新输入';
        console.error('账号密码登录失败:', error);
    } finally {
        // 清空输入框
        isLoginIn.value = false; // 重置登录状态
    }
};

const onUsernameFocus = () => { usernameFocused.value = true }
const onUsernameBlur = () => { usernameFocused.value = false }
const onPwdFocus = () => { pwdFocused.value = true }
const onPwdBlur = () => { pwdFocused.value = false }

</script>

<style lang="scss" scoped>
@import "@/subpages/login/styles/form.scss"
</style>