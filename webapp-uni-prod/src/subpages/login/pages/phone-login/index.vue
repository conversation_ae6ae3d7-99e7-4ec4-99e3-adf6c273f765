<template>
    <LoginPage :is-login-in="isLoginIn" @login="submit">
        <view class="form">
            <!-- 手机号输入 -->
            <view class="input-group">
                <text class="country-code">+86</text>
                <text class="divider">|</text>
                <input class="input" type="digit" @input="onInput($event, 'phone')" v-model="phone" placeholder="请输入手机号"
                    :maxlength="11" />
            </view>

            <!-- 验证码输入 -->
            <view class="input-group">
                <input class="input" type="digit" @input="onInput($event, 'code')" v-model="code" placeholder="请输入验证码"
                    :maxlength="6" />
                <view class="btn-code" :class="{ 'disabled': !canSendCode }" :disabled="!canSendCode" @click="sendCode">
                    {{ countdown > 0 ? `${countdown}s 后可重试` : '获取验证码' }}
                </view>
            </view>
            <view class="tip" v-if="tip">
                <SlSubSvgIcon class="inline-flex" subpage="login" :name="tipType == 'success' ? '12-12-12' : '12-12-11'"
                    size="10" />
                <text class="text" :class="{ 'error': tipType == 'error' }">{{ tip }}</text>
            </view>
        </view>
    </LoginPage>
</template>
<script setup lang="ts">
import LoginPage from '@/subpages/login/components/LoginPage.vue';
import authService from '@/service/auth';
import { useLogin } from '@/composables/useLogin';
import { AuthLoginUser } from '@/models/Account';
const { login } = useLogin();
// TODO:remove 默认手机号
const phone = ref('***********');
const code = ref('');
const countdown = ref(0);
const tip = ref('')
const tipType = ref<'success' | 'warn' | 'error'>('success'); // 用于存储提示类型
const isLoginIn = ref(false); // 是否正在登录

const isValidPhone = (phone: string) => {
    return /^1[3-9]\d{9}$/.test(phone);
};

const isEmpty = (value: string) => {
    return value === null || value === undefined || value.trim() === '';
};

const canSendCode = computed(() => {
    return countdown.value <= 0 && isValidPhone(phone.value);
});

// 只允许输入数字
const onInput = (event: any, type: string) => {
    const inputValue = event.detail.value;
    const val = inputValue.replace(/\D+/g, '')
    setTimeout(() => {
        if (type === 'phone') {
            phone.value = val;
        } else if (type === 'code') {
            code.value = val;
        }
    });
};

const sendCode = async () => {
    if (!canSendCode.value) return;
    if (!isValidPhone(phone.value)) {
        uni.showToast({
            title: '请输入有效的手机号',
            icon: 'none'
        });
        return;
    }
    countdown.value = 60;
    tip.value = '';
    tipType.value = 'success';
    let interval: ReturnType<typeof setInterval> | null = setInterval(() => {
        if (countdown.value > 0) {
            countdown.value--;
        } else {
            if (interval) clearInterval(interval);
            interval = null;
        }
    }, 1000);

    try {
        await authService.getCode(phone.value);
        tip.value = '验证码已发送，请注意查收';
        tipType.value = 'success';
    } catch (error) {
        tip.value = '验证码发送失败，请稍后再试';
        tipType.value = 'error';
        countdown.value = 0;
        if (interval) clearInterval(interval);
        interval = null;
    }
};

const submit = async () => {
    tip.value = ''; // 清除提示信息
    if (!isValidPhone(phone.value)) {
        tip.value = '请输入有效的手机号';
        return;
    }
    if (isEmpty(code.value)) {
        tip.value = '请输入验证码';
        return;
    }
    isLoginIn.value = true; // 设置正在登录状态
    const user: AuthLoginUser = {
        j_username: phone.value,
        j_password: code.value
    }
    try {
        await login(user, 'phone')
    } catch (error) {
        console.error('手机号登录失败:', error);
        tip.value = '手机号或验证码错误，请稍后再试';
        tipType.value = 'error';
        return;
    } finally {
        // 清空输入框
        phone.value = '';
        code.value = '';
        isLoginIn.value = false; // 重置登录状态
    }
};

</script>

<style lang="scss" scoped>
.form {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    width: 100%;
    position: relative;
}

.input-group {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    height: 40px;
    align-items: center;
    border-bottom: 1px solid rgba(153, 153, 153, 0.4);

    .country-code {
        flex: 0 0 auto;
    }

    .divider {
        flex: 0 0 auto;
        text-align: center;
        color: rgba(153, 153, 153, 0.4);
        margin-left: 6px;
        margin-right: 6px;
    }

    .input {
        flex: 1;
        height: 100%;
        box-sizing: border-box;
    }

    .btn-code {
        box-sizing: border-box;
        height: 100%;
        display: inline-flex;
        align-items: center;
        color: rgba(46, 103, 226, 1);
        border-radius: 0;
        padding: 0 0 0 10px;
        height: 32px;
        background-color: #fff;
        font-size: 14px;

        &.disabled {
            color: #666666;
        }
    }

}

.tip {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: -18px;
    left: 0;
    width: 100%;
}

.inline-flex {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
}

.tip .text {
    color: $uni-text-color-grey;
    font-size: $uni-font-size-sm;

    &.error {
        color: #FF4E4E;
    }
}
</style>