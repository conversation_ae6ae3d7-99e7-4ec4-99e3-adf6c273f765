<template>
    <LoginPage :is-login-in="isLoginIn" @login="submit" :pageType="pageType">
        <view class="form">
            <!-- 手机号输入 -->
            <view class="input-group">
                <text class="country-code">+86</text>
                <text class="divider">|</text>
                <input class="input" @focus="onPhoneFocus" @blur="onPhoneBlur" type="number" v-model="phone"
                   :placeholder="pageType==='change_phone'?'请输入新手机号':'请输入手机号'" :maxlength="11" />
                <view class="clear" v-if="showClearPhone" @tap="phone = ''">
                    <SlSubSvgIcon class="inline-flex" subpage="login" name="16-16-30" size="16" />
                </view>
            </view>

            <!-- 验证码输入 -->
            <view class="input-group">
                <input class="input" type="number" textContentType="oneTimeCode" v-model="code" placeholder="请输入验证码"
                    :maxlength="6" @focus="onCodeFocus" @blur="onCodeBlur" />
                <view class="clear" v-if="showClearCode" @tap="code = ''">
                    <SlSubSvgIcon class="inline-flex" subpage="login" name="16-16-30" size="16" />
                </view>
                <view class="btn-code" :class="{ 'disabled': !canSendCode }" :disabled="!canSendCode" @click="sendCode">
                    {{ countdown > 0 ? `${countdown}s 后可重试` : '获取验证码' }}
                </view>
            </view>
            <view class="tip" v-if="tip">
                <SlSubSvgIcon class="inline-flex" subpage="login" :name="tipType == 'success' ? '12-12-12' : '12-12-11'"
                    size="10" />
                <text class="text" :class="{ 'error': tipType == 'error' }">{{ tip }}</text>
            </view>
        </view>
    </LoginPage>
</template>
<script setup lang="ts">
import LoginPage from '@/subpages/login/components/LoginPage.vue';
import authService from '@/service/auth';
import { useLogin } from '@/composables/useLogin';
import { AuthLoginUser } from '@/models/Account';
import { changePhone, sendChangePhoneCode } from '@/subpages/login/service';
const { login } = useLogin();
const phone = ref('');
const code = ref('');
const countdown = ref(0);
const tip = ref('')
const tipType = ref<'success' | 'warn' | 'error'>('success'); // 用于存储提示类型
const isLoginIn = ref(false); // 是否正在登录
const pageType = ref<'phone_login' | 'change_phone'>('phone_login'); // 页面类型，默认为手机号登录
const redirectUrl = ref<string | undefined>(undefined); // 重定向地址
// 焦点状态
const phoneFocused = ref(false)
const codeFocused = ref(false)

//计算是否显示「清除」图标
const showClearPhone = computed(() => phoneFocused.value && phone.value.length > 0)
const showClearCode = computed(() => codeFocused.value && code.value.length > 0)

onLoad((options: any) => {
    if (options.type === 'change_phone') {
        pageType.value = 'change_phone'; // 如果是更换手机号页面
        phone.value = ''; // 清空手机号输入框
        //更改页面标题
        uni.setNavigationBarTitle({
            title: '更换手机号码'
        });
    }
    if (options.redirect) {
        redirectUrl.value = decodeURIComponent(options.redirect);
    }
});


const isValidPhone = (phone: string) => {
    return /^1[3-9]\d{9}$/.test(phone);
};

const isEmpty = (value: string) => {
    return value === null || value === undefined || value.trim() === '';
};

const canSendCode = computed(() => {
    return countdown.value <= 0 && isValidPhone(phone.value);
});

const sendCode = async () => {
    if (!canSendCode.value) return;
    if (!isValidPhone(phone.value)) {
        uni.showToast({
            title: '请输入有效的手机号',
            icon: 'none'
        });
        return;
    }
    countdown.value = 60;
    tip.value = '';
    tipType.value = 'success';
    let interval: ReturnType<typeof setInterval> | null = setInterval(() => {
        if (countdown.value > 0) {
            countdown.value--;
        } else {
            if (interval) clearInterval(interval);
            interval = null;
        }
    }, 1000);

    try {
        await sendCodeByPageType();
        tip.value = '验证码已发送，请注意查收';
        tipType.value = 'success';
    } catch (error) {
        tip.value = (error as Error).message || '验证码发送失败，请稍后再试';
        tipType.value = 'error';
        countdown.value = 0;
        if (interval) clearInterval(interval);
        interval = null;
    }
};
/**
 * 发送验证码
 * 如果是更换手机号页面，则调用更换手机号的验证码接口
 * 否则调用登录的验证码接口
 */
const sendCodeByPageType = async () => {
    return pageType.value == 'change_phone' ?
        sendChangePhoneCode(phone.value) : authService.getCode(phone.value);
}

const submit = async () => {
    tip.value = ''; // 清除提示信息
    if (!isValidPhone(phone.value)) {
        tip.value = '请输入有效的手机号';
        tipType.value = 'error';
        return;
    }
    if (isEmpty(code.value)) {
        tip.value = '请输入验证码';
        tipType.value = 'error';
        return;
    }
    if (pageType.value === 'change_phone') {
        await changePhoneSubmit();
    } else {
        await loginSubmit();
    }
};

const changePhoneSubmit = async () => {
    try {
        await changePhone(phone.value, code.value);
        tip.value = '手机号更换成功，请重新登录';
        tipType.value = 'success';
        setTimeout(() => {
            uni.reLaunch({ url: '/pages/login/index' });
        }, 2000);
    } catch (error: any) {
        console.error('更换手机号失败:', error);
        tip.value = (error as Error).message || '更换手机号失败，请稍后再试';
        tipType.value = 'error';
    }
}
const loginSubmit = async () => {
    isLoginIn.value = true; // 设置正在登录状态
    const user: AuthLoginUser = {
        j_username: phone.value,
        j_password: code.value
    }
    tip.value = ''; // 清空提示信息
    try {
        await login(user, 'phone', redirectUrl.value)
    } catch (error: any) {
        console.error('手机号登录失败:', error);
        tip.value = '手机号或验证码错误，请重新输入';
        tipType.value = 'error';
    } finally {
        isLoginIn.value = false; // 重置登录状态
    }
}

const onPhoneFocus = () => { phoneFocused.value = true }
const onPhoneBlur = () => { phoneFocused.value = false }
const onCodeFocus = () => { codeFocused.value = true }
const onCodeBlur = () => { codeFocused.value = false }

</script>

<style lang="scss" scoped>
@import "@/subpages/login/styles/form.scss";
</style>