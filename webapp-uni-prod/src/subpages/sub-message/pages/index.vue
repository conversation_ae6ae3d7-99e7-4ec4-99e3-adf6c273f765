<template>
  <view class="container">
    <template v-for="group in groups" :key="group.name">
      <template v-if="group.sub || (!group.sub && ifUnSub)">
        <view class="group-name">{{ group.name }}</view>
        <view class="group-content">
          <view class="title">{{ group.title }}</view>
          <view class="list">
            <template v-for="data in vDatas" :key="data.id">
              <view
                class="item"
                v-if="
                  (group.sub && data.ifSub && !data.ifHide) ||
                  (!group.sub && !data.ifSub)
                "
              >
                <view class="content">
                  <view class="name"
                    >{{ data.name || "--"
                    }}<text class="count" v-if="group.count && data.name"
                      >仅剩{{ data.currentUserSubscribeCount }}次提醒</text
                    ></view
                  >
                  <view class="desc">{{ data.description || "--" }}</view>
                </view>
                <button class="btn" v-if="data.name" @tap="onSub(data)">
                  {{ group.btn }}
                </button>
              </view>
            </template>
          </view>
          <view
            class="expend-btn"
            :class="{ expend: ifExpend }"
            v-if="ifShowExpend && group.expend"
            @tap="onExpend"
            >{{ ifExpend ? "收起" : "展开" }}全部订阅
            <SlSubSvgIcon
              subpage="sub-message"
              class="arrow"
              :name="'12-12-21'"
              size="12"
            />
          </view>
        </view>
      </template>
    </template>
    <view class="tip"
      >温馨提示：请确保在右上角[...]-[设置]-[通知管理]开启[接受通知]功能。</view
    >
  </view>
</template>
<script lang="ts" setup>
/**订阅信息 */
import { useWxSubscribe } from "@/hooks";
import { Sub } from "../model/Sub";
import { addSub, getMessagetemplate } from "@/service/sub-message.service";
const groups = [
  {
    name: "-已订阅消息-",
    title: "点击增加提醒次数",
    count: true,
    expend: true,
    sub: true,
    btn: "提醒我",
  },
  {
    name: "-未订阅消息-",
    title: "点击开启订阅",
    sub: false,
    btn: "开启",
  },
];
/**已订阅消息折叠时最多显示数量 */
const maViewxCount: number = 3;
const datas = ref<Sub[]>([
  { id: "1", name: "", ifSub: true },
  { id: "2", name: "", ifSub: false },
]);
/**当前是否已展开 */
const ifExpend = ref<boolean>(false);
/**是否显示未订阅消息列表-所有内容已开启订阅时就不显示 */
const ifUnSub = computed(() => {
  return datas.value.filter((item) => !item.ifSub).length > 0;
});
/**是否显示 展开订阅按钮 */
const ifShowExpend = computed(() => {
  return datas.value.filter((item) => item.ifSub).length > maViewxCount;
});
const vDatas = computed(() => {
  if (!ifShowExpend.value) return datas.value;
  let num = 0;
  datas.value.forEach((e) => {
    if (e.ifSub) {
      num++;
    }
    if (e.ifSub && num > maViewxCount && !ifExpend.value) {
      e.ifHide = true;
    } else {
      e.ifHide = false;
    }
  });
  return datas.value;
});
onShow(() => {
  getList();
});
onPullDownRefresh(async () => {
  try {
    await getList();
  } finally {
    uni.stopPullDownRefresh();
  }
});
/**展开订阅列表 */
const onExpend = () => {
  ifExpend.value = !ifExpend.value;
};
/**添加订阅次数 */
const onSub = (data: Sub) => {
  let { name = "", currentUserSubscribeCount = 0 } = data;
  if (currentUserSubscribeCount >= 99) {
    uni.showToast({ title: "最多可提醒99次", icon: "none" });
    return;
  }  
  useWxSubscribe([name], { forceSubscribe: true }).then(async (res) => {
    if (res.length) {
      await Promise.all(
        res.map(async (e) => {
          const item = datas.value.find((item) => item.wechatTemplateId === e);
          if (item && item.id) {
            await addSub(item.id);
          }
        })
      );
    }
    getList();
  });
};
const getList = async () => {
  let res: Sub[] = await getMessagetemplate(true);
  /**没有订阅列表时显示两个空数据 */
  if (!res.length) {
    res = [
      { id: "1", name: "", ifSub: true },
      { id: "2", name: "", ifSub: false },
    ];
  } else {
    res.forEach((e) => {
      e.ifSub = !!(
        e.currentUserSubscribeCount?.toString() &&
        e.currentUserSubscribeCount > -1
      );
    });
  }
  datas.value = res;
};
</script>
<style lang="scss" scoped>
.container {
  height: 100vh;
  background-color: #f4f6fa;
  position: relative;
  padding: 10px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.group-name {
  font-size: 12px;
  color: #666666;
}
.group-content {
  width: 290px;
  margin: 10px 0px;
  border-radius: 10px;
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
  overflow: hidden;
  .title {
    width: 100%;
    height: 38px;
    line-height: 38px;
    border-radius: 10px 10px 0px 0px;
    background: linear-gradient(90deg, #e7f6ff 3%, #ffffff 100%);
    color: #3d3d3d;
    padding: 0 15px;
  }
  .list {
    width: 100%;
    padding: 0 15px;
    .item {
      width: 100%;
      height: 60px;
      border-top: 1px solid rgba(153, 153, 153, 0.2);
      display: flex;
      align-items: center;
    }
    .content {
      width: 50%;
      flex-grow: 1;
      .name {
        color: #3d3d3d;
        margin: 4px 0px;
        display: flex;
        align-items: center;
      }
      .count {
        font-size: 10px;
        color: #ff8f00;
        margin-left: 10px;
      }
      .desc {
        font-size: 10px;
        color: #999999;
        margin: 4px 0px;
      }
    }
    .btn {
      width: 70px;
      height: 32px;
      border-radius: 17px;
      border: 1px solid #0066df;
      font-size: 12px;
      color: #0066df;
      &:active {
        background: rgba(0, 102, 223, 0.05);
      }
      &:after {
        border: none;
      }
    }
  }
  .expend-btn {
    width: 100%;
    height: 38px;
    border-top: 1px solid rgba(153, 153, 153, 0.2);
    padding: 0 15px;
    font-size: 10px;
    color: #666666;
    display: flex;
    align-items: center;
    justify-content: center;
    .arrow {
      display: inline-flex;
      margin: 0px 2px;
    }
    &.expend :deep(image) {
      transform: rotate(180deg);
      transition: transform 0.3s ease;
    }
  }
}
.tip {
  font-size: 12px;
  color: #999999;
}
</style>
