<template>
    <div class="control-select" @click="showModal = !showModal">
        <span class="select-name">{{ selectedName }}</span>
        <SlSVgIcon name="persons-12-6" size="12" style="transform: rotate(90deg);" />
        <template v-if="showModal">
            <div class="select-wrapper">
                <div class="select-wrapper-inner">
                    <div class="select-item" v-for="item of options" :key="item.value"
                        :class="{ active: selctedValues.indexOf(item.value) != -1 }" @click.stop="onClickItem(item)">
                        {{ item.name }}
                    </div>
                    <BaseEmpty v-if="options.length == 0" />
                </div>
                <div class="select-btn-container" @click.stop="onAdd">
                    <div class="select-add-btn">
                        新增
                    </div>
                </div>
            </div>
            <div class="overlay" @click.stop="showModal = false"></div>
        </template>
    </div>
    <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="onConfirmAdd" :showHeader="false">
        <div class="add-box">
            <span class="label">职务名称：</span>
            <up-input type="text" placeholder="请输入" v-model="newSelectName" clearable maxlength="8" :border="'none'" />
        </div>
    </ConfirmDialog>
</template>

<script setup lang="ts">
import useMessage from '@/hooks/use-message';

interface IOption {
    value: string;
    name: string;
}
const props = defineProps<{ options?: IOption[] }>()
const emit = defineEmits(['update:options'])
const options = ref<IOption[]>([])
const showModal = ref(false)
const selecteds = ref<IOption[]>([])
const showConfirm = ref(false)
const newSelectName = ref('')
const selectedName = computed(() => {
    return selecteds.value.map(item => item.name).join(',')
})
const selctedValues = computed(() => {
    return selecteds.value.map(item => item.value).join(',')
})
watchEffect(() => {
    options.value = props.options || []
})
const onClickItem = (item: IOption) => {
    console.log("🚀 ~ PositionSelect.vue ~ onClickItem ~ item:", item)
    const idx = selecteds.value.findIndex(ele => ele.value == item.value)
    if (idx == -1) {
        selecteds.value.push(item)
    } else {
        selecteds.value.splice(idx, 1)
    }
    showModal.value = false
}
const onAdd = () => {
    newSelectName.value = ''
    showConfirm.value = true
}
const message = useMessage()
const onConfirmAdd = () => {
    const isRepeat = options.value.some(item => item.name == newSelectName.value)
    if (isRepeat) {
        message.error('职务名称重复')
    } else {
        options.value.push({ value: newSelectName.value, name: newSelectName.value })
        emit('update:options', options.value)
        showConfirm.value = false
    }
}
</script>

<style scoped lang="scss">
.control-select {
    width: 186px;
    height: 26px;
    display: flex;
    align-items: center;
    border-radius: 3px;
    border: 1px solid #CACACA;
    position: relative;
    padding: 0 10px;
}

.select-name {
    display: inline-block;
    flex: 1;
    padding-left: 10px;
}

.select-wrapper {
    position: absolute;
    border-radius: 8px;
    padding: 0 15px;
    top: 26px;
    left: 0;
    z-index: 3;
    width: 185px;
    background-color: #fff;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);

    .select-wrapper-inner {
        max-height: 240px;
        min-height: 40px;
        overflow-y: auto;
    }

    .select-item {
        font-size: 14px;
        color: #333333;
        height: 40px;
        line-height: 40px;

        &.active {
            color: #fff;
        }
    }
}

.select-btn-container {
    width: 100%;
    display: flex;
    text-align: center;
    justify-content: center;
}

.select-add-btn {
    width: 60px;
    height: 28px;
    line-height: 28px;
    margin: 5px 0;
    border-radius: 3px;
    background: #0066DF;
    color: #fff;
}

.overlay {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
}

.add-box {
    height: 106px;
    display: flex;
    align-items: center;
    z-index: 4;
}
</style>