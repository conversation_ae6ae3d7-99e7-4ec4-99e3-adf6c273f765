.input-group {
  background-color: #f8f8f8;
  border-radius: 3px;
  // height: 41px;
  display: flex;
  align-items: center;
}

// .persons-wrapper {
//   background: #fdfdfd;
//   width: 100%;
//   min-height: 100vh;
//   display: flex;
//   flex-direction: column;
// }

// .content {
//   flex-grow: 1;
//   overflow-y: auto;
//   padding: 15px;
// }

// .footer {
//   padding: 15px;
//   flex: 1;
//   display: flex;
//   align-items: flex-end;
//   justify-content: space-between;

//   button {
//     width: 140px;
//     box-sizing: border-box;
//     height: 40px;
//     line-height: 40px;
//     border-radius: 3px;
//   }
// }

// .btn-cancel {
//   background: #ffffff;
//   border: 1px solid #0066df;
//   color: #0066df;
// }

// .btn-cancel-del {
//   background: #ffffff;
//   border: 1px solid #999;
//   background: #ffffff;
//   color: #666;

//   &:hover {
//     border: 1px solid #e50101;
//     color: #e50101;
//   }
// }

// .btn-save {
//   background: #0066df;
//   border-color: #0066df;
//   color: #fff;

//   &.disabled {
//     background: #999999;
//     border-color: #999;
//     pointer-events: none;
//   }
// }
