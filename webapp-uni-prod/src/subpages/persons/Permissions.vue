<template>
    <div class="permissions-wrapper">
        <div class="permissions-item" v-for="item of permissionsArr" :key="item.code">
            <up-checkbox :label="item.label" usedAlone :checked="checkedPermission.has(item.code)"
                @update:checked="checkedPermission.has(item.code) ? checkedPermission.delete(item.code) : checkedPermission.add(item.code)">
            </up-checkbox>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';

const permissionsArr = ref<{ label: string; code: string; }[]>([
    { label: '职工权限', code: '1' },
    { label: '理发管理', code: '2' },
    { label: '菜谱管理', code: '3' },
    { label: '外卖管理', code: '4' },
    { label: '报修管理', code: '5' },
    { label: '班车发车', code: '6' },
    { label: '人事管理', code: '7' },
    { label: '配置管理', code: '8' },
    { label: '巡检管理', code: '9' },
    { label: '物业巡检', code: '10' },
    { label: '白班巡检', code: '11' },
    { label: '晚班巡检', code: '12' },
    { label: '保洁清洁', code: '13' },
])
const checkedPermission = ref<Set<string>>(new Set())
const props = defineProps<{ selectedIds?: string[] }>()
const emit = defineEmits(['update:selectedIds'])
watchEffect(() => {
    if (props.selectedIds?.length) {
        checkedPermission.value = new Set(props.selectedIds)
    }
})
watchEffect(() => {
    emit('update:selectedIds', Array.from(checkedPermission.value))
})
</script>

<style scoped lang="scss">
.permissions-wrapper {
    display: flex;
    align-items: center;
    gap: 10px 15px;
    flex-wrap: wrap;
    padding: 10px 0;

    .permissions-item {
        display: flex;
        align-items: center;
        gap: 4px;
        flex-grow: 1;
    }
}
</style>