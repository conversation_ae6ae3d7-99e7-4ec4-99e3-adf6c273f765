<template>
    <div class="permissions-wrapper">
        <div class="permissions-item" v-for="item of permissionsArr" :key="item.code">
            <up-checkbox :label="item.label" usedAlone labelColor="#333" :checked="checkedPermission.has(item.code)"
                @update:checked="onCheckedItem(item)">
            </up-checkbox>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { IPermission } from './models/Personnel';
import { usePermissionStore } from './stores/permissionStore';
interface IPermissionItem { label: string; code: string; _init: IPermission }
const permissionsArr = ref<IPermissionItem[]>([
    // { label: '职工权限', code: '1' },
    // { label: '理发管理', code: '2' },
    // { label: '菜谱管理', code: '3' },
    // { label: '外卖管理', code: '4' },
    // { label: '报修管理', code: '5' },
    // { label: '班车发车', code: '6' },
    // { label: '人事管理', code: '7' },
    // { label: '配置管理', code: '8' },
    // { label: '巡检管理', code: '9' },
    // { label: '物业巡检', code: '10' },
    // { label: '白班巡检', code: '11' },
    // { label: '晚班巡检', code: '12' },
    // { label: '保洁清洁', code: '13' },
])
const checkedPermission = ref<Set<string>>(new Set())
const props = defineProps<{
    modelValue?: IPermission[],
    defaultNames?: string[],
    hiddenNames?: string[]
}>()
const emit = defineEmits<{
    (event: 'update:modelValue', value: IPermission[]): void
    (event: 'permissionChange', value: IPermission): void
}>()
watch(() => props.modelValue, () => {
    checkedPermission.value = new Set(props.modelValue?.map(ele => ele.id!))
}, { deep: true })
const onCheckedItem = (item: IPermissionItem) => {
    checkedPermission.value.has(item.code) ? checkedPermission.value.delete(item.code) : checkedPermission.value.add(item.code)
    emit('update:modelValue', Array.from(checkedPermission.value).map(code => permissionsArr.value.find(ele => ele.code === code)!._init) || [])
    emit('permissionChange', item._init)
}
const { getPermissions } = usePermissionStore();
onMounted(() => {
    getList()
})
const getList = () => {
    getPermissions().then(res => {
        // personnelService.getPermissions().then(res => {
        permissionsArr.value = res?.map(ele => {
            return {
                label: ele.name!,
                code: ele.id!,
                _init: ele
            }
        })
        if (props.hiddenNames?.length) {
            permissionsArr.value = permissionsArr.value.filter(ele => !props.hiddenNames?.includes(ele.label))
        }
        // 新增赋予默认值
        if (!props.modelValue?.length && props.defaultNames?.length) {
            checkedPermission.value = new Set(toValue(permissionsArr)?.filter(ele => props.defaultNames?.includes(ele.label))?.map(ele => ele.code) || [])
            emit('update:modelValue', Array.from(checkedPermission.value).map(code => permissionsArr.value.find(ele => ele.code === code)!._init) || [])
        }
    })
}
</script>

<style scoped lang="scss">
.permissions-wrapper {
    display: flex;
    align-items: center;
    gap: 10px 15px;
    flex-wrap: wrap;
    padding: 10px 0;
    box-sizing: border-box;

    .permissions-item {
        display: flex;
        align-items: center;
        gap: 4px;
        flex-grow: 1;
        box-sizing: border-box;
    }
}
</style>