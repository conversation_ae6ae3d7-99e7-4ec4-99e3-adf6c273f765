import { PageSearch } from '@/models/Page';

/**
 * 关联的人员信息
 *
 * Personnel
 */
export interface Personnel {
  /**
   * 部门
   */
  department?: string;
  organizationId?: string;
  organization?: any;
  /**
   * 邮箱
   */
  email?: string;
  id?: string;
  /**
   * 姓名
   */
  name?: string;
  /**
   * 手机号码
   */
  phone?: string;
  /**
   * 职务
   */
  position?: string;
  /**
   * 状态(0-禁用,1-启用)
   */
  status?: number;
  /**
   * 人员类型(0-内部成员,1-外部成员)
   */
  type?: number | null;
  // 部门人员
  members?: Personnel[];
  _isChecked?: boolean;
  permissionList?: { id?: string; name?: string }[];
  workgroupList?: IPersonnelGroupItem[];
}
export class PersonnelSearch extends PageSearch {
  /** 部门 */
  department?: string;
  /** 人员类型(0-内部成员,1-外部成员) */
  type?: number;
  //   是否加载车辆信息
  loadVehicles?: false;
  /**
   * 是否排除当前登录用户(临时使用固定发起人ID)
   */
  excludeCurrentUser?: boolean;
  /**
   * 是否按部门分组返回结果
   */
  // groupByDepartment?: boolean;
  groupByOrganization?: boolean;
  /** 是否按工作组分组 */
  groupByWorkgroup?: boolean;
  /** 是否返回分组结果 */
  ifGrouped?: boolean;
  /** 工作组名称(查询指定工作组的成员) */
  workgroupName?: string;
  /** 权限名称列表(查询指定权限的成员) */
  permissionNameList?: string[];
  /** 所属工作组ID（作为默认账号时，空字符串表示筛选非工作组默认账号） */
  ownerWorkgroupId?: string;
  /** 是否按当前用户所属分中心过滤人员 */
  ifFilterByCurrentSubBranch?: boolean;
  constructor() {
    super();
  }
}
/** 按部门分组返回的人员结果 */
export interface PersonnelData {
  groupType?: string;
  /** 名称 */
  groupName?: string;
  id?: string;
  // organizationName?: string;
  /** 工作组名称 */
  // workgroupName?: string;
  /** 人员 */
  members?: Personnel[];
  _uuid?: string;
  _open?: boolean;
  _isChecked?: boolean;
}
/** 工作组、部门 */
export interface IPersonnelGroupItem {
  id?: string;
  sysCreated?: string;
  name?: string;
  description?: string;
  leaderId?: string;
  status?: number;
  sortOrder?: number;
  /** 成员列表 */
  personnelList?: Personnel[];
  /** 权限列表 */
  permissionList?: IPermission[];
  username?: string;
  password?: string;
  /** 所属工作组ID；若有说明是默认账号类型。 */
  ownerWorkgroupId?: string;
  /** 机构类型：0-无,1-局机关,2-局属各单位、处事办事机构 */
  mainBranchDepartmentOrSubBranch?: 0 | 1 | 2;
}
export interface IPermission {
  id?: string;
  name?: string;
  sortOrder?: number;
}
