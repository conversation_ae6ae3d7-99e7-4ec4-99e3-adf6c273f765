<template>
    <div class="persons-department">
        <div class="content">
            <span class="label required">
                机构名称
            </span>
            <div class="input-group">
                <up-input type="text" placeholder="请输入" v-model="info.name" clearable maxlength="10" :border="'none'" />
            </div>
            <SlRadioGroup :options="options" v-model="info.mainBranchDepartmentOrSubBranch!" :disabled="!!props.id"
                :customStyle="{ 'display': 'flex', 'justify-content': 'space-between' }" />
        </div>
        <div class="footer">

            <BaseButton btnType="delete" v-if="props.id" @click="showConfirm = true">删除</BaseButton>
            <BaseButton btnType="cancel" v-else @click="cancel">取消</BaseButton>
            <BaseButton btnType="save" :disabled="!info.name" @click="save">完成</BaseButton>
        </div>
        <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="del">您确定要删除该机构吗？</ConfirmDialog>
    </div>
</template>

<script setup lang="ts">
import useMessage from '@/hooks/use-message';
import { isEmpty } from '@/utils';
import { ref } from 'vue';
import { IPersonnelGroupItem } from './models/Personnel';
import personnelService from './service/personnel.service';
const showConfirm = ref(false)
const props = defineProps<{ id?: string; name?: string }>()
const info = ref<IPersonnelGroupItem>({
    name: '',
    mainBranchDepartmentOrSubBranch: 1 // 默认为局机关
})
const options: { label: string, value: number }[] = [{ label: '局机关', value: 1 }, { label: '局属各单位、处事办事机构', value: 2 }]
watchPostEffect(() => {
    if (props.id) {
        getInfo()
        uni.setNavigationBarTitle({
            title: '编辑机构'
        })
    }

})
const getInfo = () => {
    if (!props.id) return;
    personnelService.organization.getInfo(props.id).then(res => {
        info.value = res
    })
}
const save = async () => {
    if (isEmpty(toValue(info)?.name)) {
        message.show('请输入机构名称！')
        return;
    }
    personnelService.organization.save(toValue(info), { successTip: `${props.id ? '修改成功' : '新增成功'}`, }).then(_ => {
        uni.navigateBack()
    })
}
const message = useMessage()
const cancel = () => {
    uni.navigateBack()
}
const del = () => {
    if (!props.id) return;
    personnelService.organization.delete(props.id, () => showConfirm.value = false).then(_ => {
        uni.navigateBack()
    })
}
</script>

<style scoped lang="scss">
.label {
    display: inline-block;
    padding-left: 10px;
    color: #333;
}

.required {
    position: relative;

    &::after {
        content: '*';
        color: red;
        position: absolute;
        left: 0;
        top: 0;
    }
}

.persons-department {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 15px;
    gap: 10px;
}

.footer {
    flex: 1;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
}

.input-group {
    background-color: #F8F8F8;
    border-radius: 3px;
    margin-top: 10px;
    height: 41px;
    display: flex;
    align-items: center;
    padding-left: 10px;
    margin-bottom: 10px;
}

.content {
    flex-grow: 1;
    overflow-y: auto;
}

view {
    box-sizing: border-box;
}
</style>