<template>
    <div class="persons-department">
        <div class="content">
            <span class="label required">
                部门名称
            </span>
            <!-- <input class="input" type="text" placeholder="请输入部门名称" v-model="departmentName" style="width: 100%;"> -->
            <div class="input-group">
                <up-input type="text" placeholder="请输入部门名称" v-model="departmentName" clearable maxlength="10"
                    :border="'none'" />
            </div>
        </div>
        <div class="footer">

            <BaseButton btnType="delete" v-if="props.id" @click="del">删除</BaseButton>
            <BaseButton btnType="cancel" v-else @click="cancel">取消</BaseButton>
            <BaseButton btnType="save" :disabled="!departmentName" @click="save">完成</BaseButton>
            <!-- <button class="footer_btn-delete medium" v-if="props.id" @click="del">删除</button>
            <button class="footer_btn-cancel medium" v-else @click="cancel">取消</button>
            <button type="primary" :class="['footer_btn-save', 'medium', { 'footer_btn-dis': !departmentName }]"
                @click="save">完成</button> -->
        </div>
    </div>
</template>

<script setup lang="ts">
import useMessage from '@/hooks/use-message';
import { isEmpty } from '@/utils';
import { ref, watchEffect } from 'vue';
// import { useRouter } from 'vue-router';

const departmentName = ref('')
const props = defineProps<{ id?: string; name?: string }>()
watchEffect(() => {
    console.log(props)
    if (props.name) {
        departmentName.value = props.name
        uni.setNavigationBarTitle({
            title: '编辑部门'
        })
    }

})
const save = () => {
    if (isEmpty(departmentName.value)) {
        message.error('请输入部门名称！')
        return
    }
    console.log("🚀 ~ PersonsDepartment.vue ~ save ~ departmentName.value:", departmentName.value, props.id)
}
// const router = useRouter()
const message = useMessage()
const cancel = () => {
    uni.navigateBack()
    // router.back()
}
const del = () => {
}
</script>

<style scoped lang="scss">
.label {
    display: inline-block;
    padding-left: 10px;
    color: #333;
}

.required {
    position: relative;

    &::after {
        content: '*';
        color: red;
        position: absolute;
        left: 0;
        top: 0;
    }
}

.persons-department {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 15px;
    gap: 10px;
}

.footer {
    flex: 1;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
}

.input-group {
    background-color: #F8F8F8;
    border-radius: 3px;
    margin-top: 10px;
    height: 41px;
    display: flex;
    align-items: center;
    padding-left: 10px;
}

.content {
    flex-grow: 1;
    overflow-y: auto;
}
</style>