<template>
    <div class="persons-department">
        <div class="content">
            <span class="label required">
                部门名称
            </span>
            <div class="input-group">
                <up-input type="text" placeholder="请输入部门名称" v-model="info.name" clearable maxlength="10"
                    :border="'none'" />
            </div>
        </div>
        <div class="footer">

            <BaseButton btnType="delete" v-if="props.id" @click="del">删除</BaseButton>
            <BaseButton btnType="cancel" v-else @click="cancel">取消</BaseButton>
            <BaseButton btnType="save" :disabled="!info.name" @click="save">完成</BaseButton>
        </div>
        <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="del">您确定要删除该部门吗？</ConfirmDialog>
    </div>
</template>

<script setup lang="ts">
import { useLoading } from '@/hooks';
import useMessage from '@/hooks/use-message';
import { IPersonnelGroupItem } from '@/models/Personnel';
import personnelService from '@/service/personnel/personnel.service';
import { isEmpty } from '@/utils';
import { ref } from 'vue';
const showConfirm = ref(false)
const props = defineProps<{ id?: string; name?: string }>()
const info = ref<IPersonnelGroupItem>({ name: '' })
const loading = useLoading()
watchPostEffect(() => {
    console.log(props)
    if (props.id) {
        getInfo()
        uni.setNavigationBarTitle({
            title: '编辑部门'
        })
    }
    // if (props.name) {
    //     departmentName.value = props.name
    // }

})
const getInfo = () => {
    if (!props.id) return;
    loading.showLoading()
    personnelService.organization.getInfo(props.id).then(res => {
        info.value = res
    }).finally(() => {
        loading.hideLoading()
    })
}
const save = async () => {
    if (isEmpty(toValue(info))) {
        message.error('请输入部门名称！')
        return;
    }
    loading.showLoading(props.id ? '编辑中...' : '新增中...')
    try {
        await personnelService.organization.save(toValue(info));
        message.success(props.id ? '编辑成功' : '新增成功')
        uni.navigateBack()
    } catch (error) {
        message.error(error as string || (props.id ? '编辑失败，请重试~' : '新增失败，请重试~'))
    } finally {
        loading.hideLoading()
    }
}
const message = useMessage()
const cancel = () => {
    uni.navigateBack()
}
const del = () => {
    if (!props.id) return;
    loading.showLoading('删除中...')
    personnelService.organization.delete(props.id).then(_ => {
        message.success('删除成功')
        uni.navigateBack()
    }).catch(err => {
        message.error(err || '删除失败，请重试~')
    }).finally(() => {
        loading.hideLoading()
    })
}
</script>

<style scoped lang="scss">
.label {
    display: inline-block;
    padding-left: 10px;
    color: #333;
}

.required {
    position: relative;

    &::after {
        content: '*';
        color: red;
        position: absolute;
        left: 0;
        top: 0;
    }
}

.persons-department {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 15px;
    gap: 10px;
}

.footer {
    flex: 1;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
}

.input-group {
    background-color: #F8F8F8;
    border-radius: 3px;
    margin-top: 10px;
    height: 41px;
    display: flex;
    align-items: center;
    padding-left: 10px;
}

.content {
    flex-grow: 1;
    overflow-y: auto;
}
</style>