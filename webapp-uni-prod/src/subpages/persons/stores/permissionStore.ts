import { IPermission } from '../models/Personnel';
import personnelService from '../service/personnel.service';

export const usePermissionStore = defineStore('permission', () => {
  const permissions = ref<IPermission[]>([]);

  async function getPermissions(forceRefresh: boolean = false) {
    if (toValue(permissions)?.length && !forceRefresh) {
      return toValue(permissions);
    }
    const res = await personnelService.getPermissions();
    permissions.value = res;
    return toValue(permissions);
  }
  function $reset() {
    permissions.value = [];
  }
  return { $reset, getPermissions };
});
