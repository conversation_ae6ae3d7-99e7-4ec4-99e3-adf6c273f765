<template>
    <div class="persons-wrapper">
        <div class="persons-header">
            <div class="header-item" :class="{ 'active': currentType == 0 }" @click="changeType(0)">
                <span class="label">机构</span>
            </div>
            <div class="header-item" :class="{ 'active': currentType == 1 }" @click="changeType(1)">
                <span class="label">工作组</span>
            </div>
            <div class="header-item" :class="{ 'active': currentType == 2 }" @click="changeType(2)">
                <span class="label">职工</span>
            </div>
        </div>
        <!-- 职工 -->
        <template v-if="currentType == 2">
            <div class="search">
                <up-input class="search-input" type="text" placeholder="请输入姓名/首字母" v-model="searchName" clearable
                    @confirm="getList" @clear="
                        searchName = '';
                    getList();
                    ">
                    <template #suffix>
                        <SlSubSvgIcon class="search_icon" subpage="persons" name="16-16-6" size="16" @click="getList" />
                    </template>
                </up-input>
            </div>
        </template>
        <div :class="['persons-content', { 'has-search': currentType == 2, 'no-footer': shouldHideFooter }]">
            <scroll-view scroll-y class="scroll-view_H" :scrollTop="scrollTop"
                @scroll="e => (old.scrollTop = e.detail.scrollTop)" @refresherrefresh="onRefresh" refresher-enabled
                :refresher-triggered="refreshing">
                <template v-if="currentType != 2">
                    <div class="content-info-wrapper" v-if="!currentInfoItem?.id">
                        <!-- 机构列表 -->
                        <template v-if="currentType == 0">
                            <template v-for="organization of organizationList" :key="organization.label">
                                <div v-show="organization.childrens?.length">
                                    <div class="organization-item">
                                        <span class="label">{{ organization.label }}</span>
                                    </div>
                                    <div class="content-item" v-for="child of organization.childrens" :key="child.uuid"
                                        @click="toInfo(child)">
                                        <SlSubSvgIcon subpage="persons" :name="'34-34-11'" size="34" />
                                        <span class="label">{{ child.label }}({{ child.childrens.length }})</span>
                                        <SlSubSvgIcon subpage="persons" name="12-12-6" size="12" />
                                    </div>
                                </div>
                            </template>
                        </template>
                        <!-- 工作组列表 -->
                        <template v-else>
                            <div class="content-item" v-for="item of workGrouplist" :key="item.uuid"
                                @click="toInfo(item)">
                                <SlSubSvgIcon subpage="persons" :name="'34-34-12'" size="34" />
                                <span class="label">{{ item.label }}({{ item.childrens.length }})</span>
                                <SlSubSvgIcon subpage="persons" name="12-12-6" size="12" />
                            </div>
                        </template>
                    </div>
                    <!-- 详细分组 -->
                    <div v-else class="group-wrapper">
                        <div class="group-title">
                            <div @click="currentInfoItem = null" class="icon-rotate">
                                <SlSubSvgIcon subpage="persons" name="12-12-6" size="12" />
                            </div>
                            <SlSubSvgIcon subpage="persons" v-if="currentType == 0 || currentType == 1"
                                :name="'34-34-' + (currentType == 0 ? '11' : '12')" size="34" />
                            <span class="label">{{ currentInfoItem?.label }}({{ currentInfoItem?.childrens.length
                                }})</span>
                        </div>
                        <!-- 工作组：开启设置账密，默认以工作组名称+账号生成第一项 -->
                        <div class="group-item info" v-for="child of currentInfoItem?.childrens" :key="child.uuid"
                            @click.stop="toInfoPersonnelByGroup(child)">
                            <div class="round">
                                {{ child.label[0] }}
                            </div>
                            <div class="group-item_name">
                                <div style="color: #333">
                                    {{ child.label }}
                                </div>
                                <div style="color: #999" v-show="child.position">
                                    {{ child.position }}
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <!-- 职工列表 -->
                <template v-else>
                    <div class="group-item" v-for="personnel of personnelList" :key="personnel.id"
                        @click="toInfoPersonnel(personnel.id!)">
                        <div class="round">
                            {{ personnel?.name ? personnel.name[0] : '' }}
                        </div>
                        <div class="group-item_name" style="border-bottom: 0">
                            <div style="color: #333">
                                {{ personnel.name }}
                            </div>
                            <div style="color: #999" v-show="personnel.position">
                                {{ personnel.position }}
                            </div>
                        </div>
                        <SlSubSvgIcon subpage="persons" name="12-12-6" size="12" />
                    </div>
                </template>
                <BaseEmpty v-if="isEmptyList" />
            </scroll-view>
        </div>
        <div class="persons-footer" v-if="!shouldHideFooter"
            :class="{ 'persons-footer_group': currentInfoItem?.id && currentType === 1 }">
            <!-- 新增按钮 （无当前项且可新增） -->
            <BaseButton class="footer-btn" v-if="!currentInfoItem?.id && (currentType !== 0 || canAddDepartment)"
                btn-type="save" size="large" @click="addGroup"> 新增{{ getCurrentTypeText }} </BaseButton>

            <!-- 机构编辑 -->
            <BaseButton class="footer-btn" v-else-if="currentInfoItem?.id && currentType === 0" btn-type="save"
                size="large" @click="editDepartment"> 编辑{{ getCurrentTypeText }} </BaseButton>

            <!-- 工作组操作 -->
            <div v-else-if="currentInfoItem?.id && currentType === 1" class="group-btn footer-btn">
                <div class="group-btn_item" @click="editGroup">编辑工作组</div>
                <div class="group-btn_item" @click="addGroupPerson">添加组员</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
/** 0:机构 1:工作组 2:职工 */
type PersonType = 0 | 1 | 2;
import { usePrincipalStore } from '@/store';
import { uuidv4 } from '@/utils/uuid';
import { ref, toValue } from 'vue';
import { IPersonnelGroupItem, Personnel, PersonnelSearch } from './models/Personnel';
import personnelService from './service/personnel.service';
import { usePermissionStore } from './stores/permissionStore';
interface RowItem {
    id: string;
    label: string;
    position: string;
    uuid: string;
    childrens: RowItem[];
    _init: IPersonnelGroupItem;
}
const currentType = ref<PersonType>(0);
const currentInfoItem = ref<RowItem | null>(null);
const searchName = ref('');
const scrollTop = ref(0);
const workGrouplist = ref<RowItem[]>([]);
const ORGANIZATION_MAIN_KEY = 1;
const ORGANIZATION_SUB_KEY = 2;
const organizationList = ref<{ label: string; isShowChildrens: boolean; childrens: RowItem[]; value: IPersonnelGroupItem['mainBranchDepartmentOrSubBranch'] }[]>([
    { label: '局机关', isShowChildrens: true, childrens: [], value: ORGANIZATION_MAIN_KEY },
    { label: '局属各单位、处事办事机构', isShowChildrens: true, childrens: [], value: ORGANIZATION_SUB_KEY },
]);
const personnelList = ref<Personnel[]>([]);
const { account } = storeToRefs(usePrincipalStore());
/** 是否可以新增机构（只有局中心才能新增机构） */
const canAddDepartment = computed(() => toValue(account)?.organization?.mainBranchDepartmentOrSubBranch == 1);
const shouldHideFooter = computed(() => !toValue(currentInfoItem)?.id && toValue(currentType) == 0 && !toValue(canAddDepartment));
const isEmptyList = computed(() => {
    if (toValue(currentInfoItem)?.id) return false;
    if (toValue(currentType) == 0) return toValue(organizationList).every(item => !item.childrens?.length);
    if (toValue(currentType) == 1) return !toValue(workGrouplist)?.length
    if (toValue(currentType) == 2) return !toValue(personnelList)?.length
})
const getCurrentTypeText = computed(() => ['机构', '工作组', '职工'][toValue(currentType)]);
onShow(() => {
    currentInfoItem.value = null;
    getList();
});
const { $reset } = usePermissionStore();
onUnmounted(() => {
    $reset();
})
const changeType = (type: PersonType) => {
    if (currentType.value == type) return;
    currentType.value = type;
    currentInfoItem.value = null;
    getList();
};
const refreshing = ref(false);
const onRefresh = () => {
    refreshing.value = true
    getList().finally(() => refreshing.value = false)
}
const getList = async () => {
    // refreshing.value = true
    if (toValue(currentType) == 0) {
        await getListDepartment();
    } else if (toValue(currentType) == 1) {
        await getListGroup();
    } else {
        await getListPersons();
    }
    // refreshing.value = false
};
const old = reactive({ scrollTop: 0 });
const toInfo = (item: RowItem) => {
    // 解决view层不同步的问题
    scrollTop.value = old.scrollTop;
    nextTick(() => {
        scrollTop.value = 0;
        currentInfoItem.value = item;
    });
};
const addGroup = () => {
    switch (toValue(currentType)) {
        case 0: // 机构
            uni.navigateTo({ url: '/subpages/persons/PersonsDepartment' });
            break;
        case 1: // 工作组
            uni.navigateTo({ url: '/subpages/persons/worker/WorkerHandle' });
            break;
        case 2: // 职员
            uni.navigateTo({ url: '/subpages/persons/PersonsHandle' });
            break;
        default:
            break;
    }
};
/** 编辑机构 */
const editDepartment = () => {
    const { id, label } = toValue(currentInfoItem) || {};
    // 保留当前页
    uni.navigateTo({ url: `/subpages/persons/PersonsDepartment?id=${id}&name=${label}` });
    // 销毁当前页
    // uni.redirectTo({ url: `/subpages/persons/PersonsDepartment?id=${id}&name=${label}` })
};
/** 编辑工作组 */
const editGroup = () => {
    const id = currentInfoItem?.value?.id;
    uni.navigateTo({ url: `/subpages/persons/worker/WorkerHandle?id=${id}` });
};
/** 添加组员 选择已有的职工加入工作组 */
const addGroupPerson = () => {
    const id = currentInfoItem?.value?.id;
    uni.navigateTo({ url: `/subpages/persons/worker/WorkerAddPersons?id=${id}` });
};
/** 职工详情 */
const toInfoPersonnel = (id: string) => {
    uni.navigateTo({ url: `/subpages/persons/PersonsHandle?id=${id}` });
};
/** 工作组/机构跳转 职工详情 */
const toInfoPersonnelByGroup = ({ id, _init }: RowItem) => {
    if (_init?.ownerWorkgroupId || !id) return;
    toInfoPersonnel(id);
};
/** 机构列表 */
const getListDepartment = async () => {
    /** ownerWorkgroupId 所属工作组ID（作为默认账号时，空字符串表示筛选非工作组默认账号） */
    const res = (await personnelService.organization.getList({ ifPage: false, loadPersonnelList: true, ownerWorkgroupId: '', onlyLeaf: true, ifFilterByCurrentSubBranch: true })) || [],
        mainList = transformList(res.filter(e => e.mainBranchDepartmentOrSubBranch == ORGANIZATION_MAIN_KEY)),
        mainItem = organizationList.value.find(e => e.value == ORGANIZATION_MAIN_KEY),
        subList = transformList(res.filter(e => e.mainBranchDepartmentOrSubBranch == ORGANIZATION_SUB_KEY)),
        subItem = organizationList.value.find(e => e.value == ORGANIZATION_SUB_KEY);
    if (mainItem) mainItem.childrens = mainList;
    if (subItem) subItem.childrens = subList;
};
/** 工作组列表 */
const getListGroup = async () => {
    const res = (await personnelService.workgroup.getList({ ifPage: false, loadPersonnelList: true })) || [];
    workGrouplist.value = transformList(res);
};
/** 人员列表 */
const getListPersons = async () => {
    const searchPersons = new PersonnelSearch();
    searchPersons.ifGrouped = false;
    searchPersons.ifPage = false;
    searchPersons.keyword = toValue(searchName) || '';
    searchPersons.ownerWorkgroupId = '';
    try {
        const res = (await personnelService.personnel.getList(searchPersons)) as Personnel[];
        personnelList.value = res || [];
    } catch (error) {
        personnelList.value = [];
    }
};
/** 转换机构/工作组成员列表 */
const transformList = (list: IPersonnelGroupItem[]) => {
    return (
        list?.map(ele => {
            return {
                id: ele.id!,
                uuid: uuidv4(),
                label: ele.name || '',
                _init: ele,
                position: '',
                childrens:
                    ele.personnelList?.map(child => {
                        return {
                            id: child.id!,
                            uuid: uuidv4(),
                            label: child.name || '',
                            childrens: [],
                            _init: child,
                            position: child.position || '',
                        };
                    }) || [],
            };
        }) || []
    );
};
</script>

<style scoped lang="scss">
$header-height: 40px;
$footer-height: 70px;
$search-height: 50px;
$padding-bottom: 10px;

.persons-header {
    width: 100%;
    height: $header-height;
    display: flex;
    align-items: center;
    justify-content: space-around;
    border-bottom: 1px solid rgba(153, 153, 153, 0.2);
    font-size: 14px;
    font-weight: bold;
    color: #666;

    .header-item {
        height: $header-height;
        line-height: $header-height;
        display: inline-block;
        width: 33%;
        text-align: center;
        cursor: pointer;
        font-weight: 400;
    }

    .header-item.active,
    .header-item:hover {
        color: #333;
        font-weight: bold;
    }

    .header-item.active {
        .label {
            position: relative;

            &::after {
                position: absolute;
                content: '';
                bottom: -35%;
                left: 50%;
                width: 50%;
                height: 2px;
                background-color: #4355fc;
                transform: translateX(-50%);
            }
        }
    }
}

.content-item {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    min-height: 50px;
    cursor: pointer;

    &:hover {
        background: rgba(79, 122, 246, 0.1);
    }

    .label {
        display: flex;
        flex: 1;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #333;
        padding: 0 8px;
    }
}

.organization-item {
    display: flex;
    align-items: center;
    padding: 0 15px;
    height: 30px;
    background-color: #f4f6fa;

    .label {
        display: flex;
        flex: 1;
        text-overflow: ellipsis;
        font-size: 10px;
        color: #666;
        padding: 0 5px;
    }
}

.group-title {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 8px;
    // padding-left: 10px;
    padding: 8px 15px;
    min-height: 50px;

    .label {
        display: flex;
        flex: 1;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #333;
    }
}

.group-item {
    padding-left: 50px;
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 50px;
    padding: 0 10px;

    &:hover {
        background: rgba(79, 122, 246, 0.1);
    }

    &_name {
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 12px;
        border-bottom: 1px solid rgba(153, 153, 153, 0.2);

        min-height: 50px;
        flex: 1;
        margin-right: 10px;
    }

    &.info {
        padding-left: 45px;
    }
}

.cust-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
}

.persons-footer {
    height: $footer-height;
    display: flex;
    align-items: center;
    padding: 0 15px;

    &_group {
        padding: 10px 0;
        // height: 50px;
        border-radius: 10px 10px 0px 0px;
        background: rgba(255, 255, 255, 0.7);
        box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);
    }
}

.content-info-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.persons-content {
    flex: 1;
    height: calc(100vh - #{$header-height} - #{$footer-height});
    overflow: hidden;

    // padding: 10px 0;
    &.has-search {
        height: calc(100vh - #{$header-height} - #{$footer-height} - #{$search-height});

        &.no-footer {
            height: calc(100vh - #{$header-height} - #{$footer-height} - #{$search-height} - #{$padding-bottom});
        }
    }

    &.no-footer {
        height: calc(100vh - #{$header-height} - #{$padding-bottom});
    }
}

.persons-content-inner {
    height: 100%;
    overflow-y: auto;
}

.icon-rotate {
    transform: rotate(180deg);
}

.round {
    width: 34px;
    height: 34px;
    background: #4f7af6;
    border-radius: 17px;
    color: #ffffff;
    font-size: 14px;
    text-align: center;
    line-height: 34px;
}

.group-btn {
    display: flex;
    align-items: center;
    // height: 40px;
    width: 100%;

    &_item {
        width: 50%;
        text-align: center;
        color: #0066df;

        &:first-child {
            border-right: 1px solid #999;
        }
    }
}

.scroll-view_H {
    height: 100%;
}

view {
    box-sizing: border-box;
}

.search_icon {
    display: inline-flex;
    margin-top: 3px;
    margin-left: 6px;
}

.search {
    padding: 0 15px;
    height: $search-height;
    display: flex;
    align-items: center;
}
</style>
