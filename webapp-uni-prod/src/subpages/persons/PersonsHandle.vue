<template>
    <div class="worker-handle">
        <div class="content">
            <div class="row">
                <div class="item">
                    <span class="label required">姓名</span>
                    <div class="control-right">
                        <div class="control input-group">
                            <up-input inputAlign="right" border="none" type="text" placeholder="请输入"
                                v-model="personInfo.name" maxlength="8" />
                        </div>
                    </div>
                </div>
                <div class="item">
                    <span class="label required">部门</span>
                    <div class="control-right">
                        <div class="control">
                            <SLSelect :options="organizationOption" placeholder="请选择部门"
                                v-model="personInfo.organizationId" />
                        </div>
                    </div>
                </div>
                <div class="item">
                    <span class="label">工作组</span>
                    <div class="control-right">
                        <div class="control">
                            <!-- <SLSelect :options="workGruopOptions" placeholder="请选择所属工作组" /> -->
                            <SLSelect :options="workGruopOptions" placeholder="请选择所属工作组" v-model="selectedWorkgroupList"
                                ifClear multiple />
                        </div>
                    </div>
                </div>
                <div class="item">
                    <span class="label">职务</span>
                    <div class="control-right">
                        <div class="control">
                            <SLSelect :options="positionOptions" placeholder="请选择职务" v-model="personInfo.position"
                                ifClear>
                                <template #dropdown-footer>
                                    <div class="select-btn-container"
                                        @click.stop="newPositionName = '', showAddConfirm = true">
                                        <div class="select-add-btn">
                                            新增
                                        </div>
                                    </div>
                                </template>
                                <template #custom-label="{ option }">
                                    <div class="positon-label">
                                        <span class="positon-label_name">{{ option.label }}</span>
                                        <div v-if="personInfo.position == option.value" class="positon-label_icon"
                                            @click.stop.prevent="onDeletePosition(option)">
                                            <SlSubSvgIcon style="display: inline-flex;" subpage="persons"
                                                name="20-20-38" size="20" />
                                        </div>
                                    </div>
                                </template>
                            </SLSelect>
                        </div>
                    </div>
                </div>
                <div class="item">
                    <span class="label required">手机号码</span>
                    <div class="control-right">
                        <div class="control input-group">
                            <up-input inputAlign="right" type="text" placeholder="请输入手机号码" v-model="personInfo.phone"
                                maxlength="11" :border="'none'" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" style="padding-top: 0;">
                <div class="item item-config">
                    <span class="label required">配置权限</span>
                </div>
                <Permissions class="item" v-model="personInfo.permissionList" :default-names="['职工权限']" />
            </div>
        </div>
        <div class="footer">
            <BaseButton btn-type="delete" v-if="personInfo.id" @click="showDelConfirm = true" />
            <BaseButton btn-type="cancel" v-else @click="cancel" />
            <BaseButton btn-type="save" :disabled="isDisabledSave" @click="save" />
        </div>
    </div>
    <ConfirmDialog v-if="showDelConfirm" @close="showDelConfirm = false" @confirm="del">您确定要删除该职工吗？</ConfirmDialog>
    <ConfirmDialog v-if="showAddConfirm" @close="showAddConfirm = false" @confirm="onConfirmAdd" :showHeader="false">
        <div class="add-box">
            <span class="label" style="padding-left: 0;">职务名称：</span>
            <div class="input-group_position" style="padding-right: 0;">
                <up-input type="text" placeholder="请输入" v-model="newPositionName" maxlength="8" :border="'none'"
                    :clearable="false" />
            </div>
        </div>
    </ConfirmDialog>
    <ConfirmDialog v-if="showDelPositionConfirm" @close="showDelPositionConfirm = false" @confirm="confirmDelPosition">
        您确定要删除该职务吗？
    </ConfirmDialog>
</template>

<script setup lang="ts">

import useMessage from '@/hooks/use-message';
import { isEmpty } from '@/utils';
import { onMounted, ref, toValue } from 'vue';
import Permissions from './Permissions.vue';
import personnelService from './service/personnel.service';
import { Personnel } from './models/Personnel';
interface ISelect {
    label: string;
    value: string | number;
    /** 原始数据 */
    _init?: any
}
const props = defineProps<{ id?: string }>()
const personInfo = ref<Personnel>({})
const showDelConfirm = ref(false)
const showAddConfirm = ref(false)
const showDelPositionConfirm = ref(false)
/** 部门列表 */
const organizationOption = ref<ISelect[]>([])
/** 工作组列表 */
const workGruopOptions = ref<ISelect[]>([])
/** 职务列表 */
const positionOptions = ref<ISelect[]>([])
let _positionDatas: { id: string; name: string }[] = []
const newPositionName = ref('')
const selectedWorkgroupList = ref<string[]>([])
onMounted(() => {
    getOptions()
    if (props.id) {
        uni.setNavigationBarTitle({ title: '编辑职工' })
        getInfo()
    }
})
const isDisabledSave = computed(() => {
    const { name, phone, organizationId, permissionList } = toValue(personInfo)
    return !name || !organizationId || !phone || phone.length != 11 || !permissionList?.length
})
// const loading = useLoading()
const getInfo = () => {
    if (!props.id) return
    personnelService.personnel.getInfo(props.id).then(res => {
        personInfo.value = res
        selectedWorkgroupList.value = res.workgroupList?.filter(Boolean)?.map(item => item.id!) || []
    })
}
const message = useMessage()
/** 保存工作组 */
const save = () => {
    const validateRequired: Partial<Record<keyof Personnel, string>> = {
        'name': '姓名',
        'organizationId': '部门',
        'phone': '手机号码',
        'permissionList': '配置权限'
    }
    let errorMsg = ''
    for (const key in validateRequired) {
        const valueIsEmpty = isEmpty(toValue(personInfo)[key as keyof Personnel])
        if (valueIsEmpty) {
            if (key == 'permissions')
                errorMsg = '请配置权限'
            else
                errorMsg = '请填写' + validateRequired[key as keyof Personnel]
            break;
        }
    }
    if (errorMsg) {
        message.show(errorMsg)
        return
    }
    personInfo.value.workgroupList = toValue(workGruopOptions).filter(item => toValue(selectedWorkgroupList).includes(item.value as string)).map(item => item._init) || []
    // loading.showLoading('保存中...')
    personnelService.personnel.save(toValue(personInfo)).then(_ => {
        // message.success('保存成功')
        uni.navigateBack()
    })
}
const cancel = () => {
    uni.navigateBack()
}
const del = () => {
    const id = toValue(personInfo)?.id
    if (!id) return;
    personnelService.personnel.delete(id, () => showDelConfirm.value = false).then(_ => {
        uni.navigateBack()
    })
}
const onConfirmAdd = () => {
    if (!toValue(newPositionName)) {
        message.show('请输入职务名称')
    }
    const isRepeat = positionOptions.value.some(item => item.label == toValue(newPositionName))
    if (isRepeat) {
        message.show('职务名称重复')
    } else {
        personnelService.position.save({ name: toValue(newPositionName) }, () => showAddConfirm.value = false).then(res => {
            showAddConfirm.value = false
            positionOptions.value.push({ value: toValue(newPositionName), label: toValue(newPositionName) })
            _positionDatas.push({ id: res as string, name: toValue(newPositionName) })
        })
    }
}
const getOptions = () => {
    // 部门
    personnelService.organization.getList({ ifPage: false, loadPersonnelList: false }).then(res => {
        organizationOption.value = res.map(item => ({ label: item.name!, value: item.id!, _init: item }))
    })
    // 工作组
    personnelService.workgroup.getList({ ifPage: false, loadPersonnelList: false }).then(res => {
        workGruopOptions.value = res.map(item => ({ label: item.name!, value: item.id!, _init: item }))
    })
    // 职务
    personnelService.position.getList({ ifPage: false }).then(res => {
        // 存 name
        positionOptions.value = res.map(item => ({ label: item.name, value: item.name }))
        _positionDatas = res || []
    })
}
let currentItem: ISelect | null = null
const onDeletePosition = (item: ISelect) => {
    console.log("🚀 ~ PersonsHandle.vue ~ onDeletePosition ~ item:", item)
    currentItem = { ...item }
    showDelPositionConfirm.value = true
}
const confirmDelPosition = () => {
    if (currentItem?.label) {
        const data = _positionDatas.find(e => e.name == currentItem?.label)
        if (data?.id) {
            personnelService.position.delete(data.id!, () => showDelPositionConfirm.value = false).then(_ => {
                personInfo.value.position = ''
                positionOptions.value = positionOptions.value.filter(e => e.label != currentItem?.label)
            })
        }
    }
}
</script>

<style scoped lang="scss">
.label {
    display: inline-block;
    padding-left: 10px;
    color: #333;
    font-size: 14px;
}

.required {
    position: relative;

    &::after {
        content: '*';
        color: red;
        position: absolute;
        left: 0;
        top: 0;
    }
}

.row {
    margin-bottom: 8px;
    display: flex;
    flex-direction: column;
    background: #FDFDFD;
    padding: 17px 0;
    box-shadow: 0px 0px 4px 0px #92929233;
    gap: 15px;
}

.password-placeholder {
    font-size: 12px;
    color: #999999;
    margin-bottom: 8px;
}

.permissions-wrapper {
    display: flex;
    align-items: center;
    gap: 10px 15px;
    flex-wrap: wrap;
    padding: 10px 0;

    .permissions-item {
        display: flex;
        align-items: center;
        gap: 4px;
    }
}

.item {
    display: flex;
    align-items: center;
    padding: 0 15px;
}

.item-config {
    border-bottom: 1px solid rgba(153, 153, 153, 0.2);
    height: 40px;
}

.footer {
    flex: 1;
    padding: 0 15px 15px;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
}

.control-right {
    flex: 1;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .u-select {
        width: 186px;
    }

    .control {
        width: 186px;
        height: 26px;
        line-height: 26px;
    }
}

.input-group {
    background-color: #f8f8f8;
    border-radius: 3px;
    padding-right: 11px;
}

.worker-handle {
    background: #f4f6fa;
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    z-index: 1;
}

.content {
    flex-grow: 1;
    overflow-y: auto;
}

.select-btn-container {
    width: 100%;
    display: flex;
    text-align: center;
    justify-content: center;
}

.select-add-btn {
    width: 60px;
    height: 28px;
    line-height: 28px;
    margin: 5px 0;
    border-radius: 3px;
    background: #0066DF;
    color: #fff;
}

.add-box {
    height: 106px;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;

    .input-group_position {
        border-radius: 3px;
        background-color: #f8f8f8;
        padding-left: 5px;
        height: 32px;
        display: flex;
        align-items: center;
    }
}

.positon-label {
    display: flex;
    align-items: center;
    height: 100%;

    &_name {
        flex: 1;
    }

    &_icon {
        width: 20px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
    }
}

view {
    box-sizing: border-box;
}
</style>