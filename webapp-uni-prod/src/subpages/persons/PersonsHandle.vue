<template>
    <div class="worker-handle">
        <div class="content">
            <div class="row">
                <div class="item">
                    <span class="label required">姓名</span>
                    <div class="control-right">
                        <div class="control input-group">
                            <up-input inputAlign="right" border="none" type="text" placeholder="请输入"
                                v-model="personInfo.name" maxlength="10" />
                        </div>
                    </div>
                </div>
                <div class="item">
                    <span class="label required">部门</span>
                    <div class="control-right">
                        <div class="control">
                            <SLSelect :options="organizationOption" placeholder="请选择部门"
                                v-model="personInfo.organizationId" />
                        </div>
                    </div>
                </div>
                <div class="item">
                    <span class="label">工作组</span>
                    <div class="control-right">
                        <div class="control">
                            <!-- <SLSelect :options="workGruopOptions" placeholder="请选择所属工作组" /> -->
                            <SLSelect :options="workGruopOptions" placeholder="请选择所属工作组" v-model="selectedWorkgroupList" ifClear
                                multiple />
                        </div>
                    </div>
                </div>
                <div class="item">
                    <span class="label">职务</span>
                    <div class="control-right">
                        <div class="control">
                            <SLSelect :options="positionOptions" placeholder="请选择职务" v-model="personInfo.position" ifClear>
                                <template #dropdown-footer>
                                    <div class="select-btn-container"
                                        @click.stop="newPositionName = '', showAddConfirm = true">
                                        <div class="select-add-btn">
                                            新增
                                        </div>
                                    </div>
                                </template>
                            </SLSelect>
                        </div>
                    </div>
                </div>
                <div class="item">
                    <span class="label required">手机号码</span>
                    <div class="control-right">
                        <div class="control input-group">
                            <up-input inputAlign="right" type="text" placeholder="请输入手机号码" v-model="personInfo.phone"
                                maxlength="11" :border="'none'" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" style="padding-top: 0;">
                <div class="item item-config">
                    <span class="label required">配置权限</span>
                </div>
                <Permissions class="item" v-model="personInfo.permissionList" />
            </div>
        </div>
        <div class="footer">
            <BaseButton btn-type="delete" v-if="personInfo.id" @click="showConfirm = true" />
            <BaseButton btn-type="cancel" v-else @click="cancel" />
            <BaseButton btn-type="save" @click="save" />
        </div>
    </div>
    <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="del">您确定要删除该职工吗</ConfirmDialog>
    <ConfirmDialog v-if="showAddConfirm" @close="showAddConfirm = false" @confirm="onConfirmAdd" :showHeader="false">
        <div class="add-box">
            <span class="label">职务名称：</span>
            <up-input type="text" placeholder="请输入" v-model="newPositionName" clearable maxlength="8"
                :border="'none'" />
        </div>
    </ConfirmDialog>
</template>

<script setup lang="ts">
import { useLoading } from '@/hooks';
import useMessage from '@/hooks/use-message';
import { Personnel } from '@/models/Personnel';
import { isEmpty } from '@/utils';
import { onMounted, ref, toValue } from 'vue';
import Permissions from './Permissions.vue';
import personnelService from '@/service/personnel/personnel.service';
interface ISelect {
    label: string;
    value: string | number;
    /** 原始数据 */
    _init?: any
}
const props = defineProps<{ id?: string }>()
const personInfo = ref<Personnel>({})
const showConfirm = ref(false)
const showAddConfirm = ref(false)
/** 部门列表 */
const organizationOption = ref<ISelect[]>([])
/** 工作组列表 */
const workGruopOptions = ref<ISelect[]>([])
/** 职务列表 */
const positionOptions = ref<ISelect[]>([])
const newPositionName = ref('')
const selectedWorkgroupList = ref<string[]>([])
onMounted(() => {
    getOptions()
    if (props.id) {
        uni.setNavigationBarTitle({ title: '编辑职工' })
        getInfo()
    }
})
// watchEffect(() => {
//     personInfo.value.permissionList = toValue(permissionsDatas)?.filter(Boolean)?.map(name => ({ name })) || []
// })
const loading = useLoading()
const getInfo = () => {
    if (!props.id) return
    personnelService.personnel.getInfo(props.id).then(res => {
        personInfo.value = res
        selectedWorkgroupList.value = res.workgroupList?.filter(Boolean)?.map(item => item.id!) || []
    })
}
const message = useMessage()
/** 保存工作组 */
const save = () => {
    const validateRequired: Partial<Record<keyof Personnel, string>> = {
        'name': '姓名',
        'organizationId': '部门',
        'phone': '手机号码',
        'permissionList': '配置权限'
    }
    let errorMsg = ''
    for (const key in validateRequired) {
        const valueIsEmpty = isEmpty(toValue(personInfo)[key as keyof Personnel])
        if (valueIsEmpty) {
            if (key == 'permissions')
                errorMsg = '请配置权限'
            else
                errorMsg = '请填写' + validateRequired[key as keyof Personnel]
            break;
        }
    }
    if (errorMsg) {
        message.error(errorMsg)
        return
    }
    personInfo.value.workgroupList = toValue(workGruopOptions).filter(item => toValue(selectedWorkgroupList).includes(item.value as string)).map(item => item._init) || []
    loading.showLoading('保存中...')
    personnelService.personnel.save(toValue(personInfo)).then(_ => {
        message.success('保存成功')
        uni.navigateBack()
    }).catch(err => {
        message.error(err || '保存失败，请重试~')
    }).finally(() => {
        loading.hideLoading()
    })
}
const cancel = () => {
    uni.navigateBack()
}
const del = () => {
    const id = toValue(personInfo)?.id
    if (!id) return;
    loading.showLoading('删除中...')
    personnelService.personnel.delete(id).then(_ => {
        message.success('删除成功')
        uni.navigateBack()
    }).catch(err => {
        message.error(err || '删除失败，请重试~')
    }).finally(() => {
        loading.hideLoading()
    })
}
const onConfirmAdd = () => {
    const isRepeat = positionOptions.value.some(item => item.label == toValue(newPositionName))
    if (isRepeat) {
        message.error('职务名称重复')
    } else {
        personnelService.position.save({ name: toValue(newPositionName) }).then(res => {
            message.success('新增成功~')
            showAddConfirm.value = false
            positionOptions.value.push({ value: toValue(newPositionName), label: toValue(newPositionName) })
        }).catch(err => {
            message.error(err || '新增失败，请重试~')
        })
    }
}
const getOptions = () => {
    // 部门
    personnelService.organization.getList({ ifPage: false, loadPersonnelList: false }).then(res => {
        organizationOption.value = res.map(item => ({ label: item.name!, value: item.id!, _init: item }))
    })
    // 工作组
    personnelService.workgroup.getList({ ifPage: false, loadPersonnelList: false }).then(res => {
        workGruopOptions.value = res.map(item => ({ label: item.name!, value: item.id!, _init: item }))
    })
    // 职务
    personnelService.position.getList({ ifPage: false }).then(res => {
        // 存 name
        positionOptions.value = res.map(item => ({ label: item.name, value: item.name }))
    })
}
</script>

<style scoped lang="scss">
.label {
    display: inline-block;
    padding-left: 10px;
    color: #333;
}

.required {
    position: relative;

    &::after {
        content: '*';
        color: red;
        position: absolute;
        left: 0;
        top: 0;
    }
}

.row {
    margin-bottom: 8px;
    display: flex;
    flex-direction: column;
    background: #FDFDFD;
    padding: 17px 0;
    box-shadow: 0px 0px 4px 0px #92929233;
    gap: 15px;
}

.password-placeholder {
    font-size: 12px;
    color: #999999;
    margin-bottom: 8px;
}

.permissions-wrapper {
    display: flex;
    align-items: center;
    gap: 10px 15px;
    flex-wrap: wrap;
    padding: 10px 0;

    .permissions-item {
        display: flex;
        align-items: center;
        gap: 4px;
    }
}

.item {
    display: flex;
    align-items: center;
    padding: 0 15px;
}

.item-config {
    border-bottom: 1px solid rgba(153, 153, 153, 0.2);
    height: 40px;
}

.footer {
    flex: 1;
    padding: 0 15px 15px;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
}

.control-right {
    flex: 1;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .u-select {
        width: 186px;
    }

    .control {
        width: 186px;
        height: 26px;
        line-height: 26px;
    }

    .input-group {
        background-color: #f8f8f8;
        border-radius: 3px;
        padding-right: 11px;
    }
}

.worker-handle {
    background: #f4f6fa;
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    z-index: 1;
}

.content {
    flex-grow: 1;
    overflow-y: auto;
}

.select-btn-container {
    width: 100%;
    display: flex;
    text-align: center;
    justify-content: center;
}

.select-add-btn {
    width: 60px;
    height: 28px;
    line-height: 28px;
    margin: 5px 0;
    border-radius: 3px;
    background: #0066DF;
    color: #fff;
}

.add-box {
    height: 106px;
    display: flex;
    align-items: center;
}
view{
    box-sizing: border-box;
}
</style>