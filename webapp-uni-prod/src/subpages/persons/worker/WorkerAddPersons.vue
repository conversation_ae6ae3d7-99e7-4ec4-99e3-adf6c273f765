<template>
    <div class="persons-wrapper">
        <div class="header">
            <up-input type="text" placeholder="请输入姓名/首字母" v-model="keyword" clearable>
                <template #suffix>
                    <SlSubSvgIcon subpage="persons" name="16-16-6" size="12" @click="onSearch" />
                    <!-- <SlSVgIcon name="16-16-6" size="16" @click="onSearch" /> -->
                </template>
            </up-input>
        </div>
        <div class="content">
            <!-- <div class="personnel-item" v-for="item of list" :key="item.uuid">
                <div class="personnel-item_parent content-item">
                    <div @click="onParentItem(item)" style="display: flex;align-items: center;">
                        <SlSVgIcon class="check-icon"
                            :name="'persons-20-' + (item?.childrens.every(c => checkedIds.has(c.id)) ? 'check' : 'uncheck')" />
                        <SlSVgIcon name="persons-34-11" size="34" />
                    </div>
                    <span class="label">{{ item.label }}({{ item.childrens.length }})</span>
                    <div @click="item.showChild = !item.showChild" :class="item.showChild ? 'icon-rotate' : ''">
                        <SlSVgIcon name="persons-12-6" size="12" />
                    </div>
                </div>
                <div v-show="item.showChild">
                    <div class="personnel-item_child" v-for="child of item?.childrens" :key="child.id"
                        @click="onChildItem(child)">
                        <SlSVgIcon class="check-icon"
                            :name="'persons-20-' + (checkedIds.has(child.id) ? 'check' : 'uncheck')" />
                        <div class="group-item">
                            <div class="round">
                                {{ child.label[0] }}</div>
                            <div class="group-item_name">
                                <div style="color: #999;">
                                    {{ child.label }}
                                </div>
                                <div style="color: #333;">
                                    {{ child.orgName || '员工' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <PersonsSelect :options="list" v-model:selected-ids="checkedBySelect" :show-org-name="true" />
            <!-- 高筛后，底部加入返回按钮，即保留高筛后勾选的结果 -->
            <!-- <div class="search-back-btn" @click="onSearchBack" v-show="!!keyword">
                返回
            </div> -->
        </div>
        <div class="footer">
            <BaseButton btn-type="cancel" @click="cancel" />
            <BaseButton btn-type="save" @click="save" />
        </div>
    </div>
</template>

<script setup lang="ts">
// import { Personnel } from '@/models/Personnel';
// import personnelService from '@/service/personnel.service';
import { useLoading } from '@/hooks';
import useMessage from '@/hooks/use-message';
import { IPersonnelGroupItem, Personnel, PersonnelData } from '@/models/Personnel';
import personnelService from '@/service/personnel/personnel.service';
import { uuidv4 } from '@/utils/uuid';

interface RowItem {
    id: string
    label: string;
    orgName?: string;
    uuid: string;
    childrens: RowItem[]
    showChild?: boolean
    iconName?: string
    _init?: any
}
const props = defineProps(['id'])
const list = ref<RowItem[]>([])
// const checkedIds = ref<Set<string>>(new Set())
const checkedBySelect = ref<string[]>([])
const checkedIds = computed(() => new Set(checkedBySelect.value || []))
const keyword = ref('')
const workInfo = ref<IPersonnelGroupItem>({})
const cancel = () => {
    uni.navigateBack()
}
const message = useMessage()
const save = () => {
    const checkedPersonnels = Array.from(
        new Map(
            allPersonnel
                .filter(item => item.id && checkedBySelect.value.includes(item.id))
                .map(item => [item.id, item]) // 使用 id 作为键
        ).values()
    );
    workInfo.value.personnelList = checkedPersonnels || []
    personnelService.workgroup.save(toValue(workInfo)).then(res => {
        message.success('保存成功~')
        uni.navigateBack()
    }).catch(err => {
        message.error(err || '保存失败，请重试~')
    })
}
onMounted(async () => {
    await getList()
    getInfo()
})
const loading = useLoading()
let allPersonnel: Personnel[] = []
const getList = async () => {
    loading.showLoading()
    allPersonnel = []
    try {
        personnelService.personnel.getList({ ifPage: false, keyword: toValue(keyword), ifGrouped: true, groupByOrganization: true, currentPage: 1, pageRecord: 5 }).then(res => {
            const datas = res as PersonnelData[] || []
            list.value = datas?.map((ele) => {
                return {
                    id: ele.id || uuidv4(), // 人员分组 此处无 id
                    uuid: uuidv4(),
                    label: ele.groupName || '',
                    iconName: '34-34-11', // 部门
                    _showChild: true,
                    _init: ele,
                    childrens: ele.members?.map(child => {
                        allPersonnel.push(child)
                        return {
                            id: child.id!,
                            uuid: uuidv4(),
                            _init: child,
                            label: child.name || '',
                            childrens: []
                        }
                    }) || []
                }
            })
        })
    } catch (error) {

    } finally {
        loading.hideLoading()
    }
}
const getInfo = () => {
    if (props.id) {
        personnelService.workgroup.getInfo(props.id).then(res => {
            workInfo.value = res
            checkedBySelect.value = res.personnelList?.map(item => item.id!) || []
        })
    }
}
let cacheCheckedIds: Set<string> = new Set()
const onSearch = () => {
    cacheCheckedIds = new Set(Array.from(checkedIds.value))
    // 暂存一下已经选中的数据
    getList().then(_ => {
        checkedBySelect.value = Array.from(cacheCheckedIds)
    })
}
</script>

<style scoped lang="scss">
// @import url("../persons-base.scss");
.persons-wrapper {
    background: #fdfdfd;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.content {
    flex: 1;
    overflow-y: auto;
}

.footer {
    padding: 15px;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
}

.btn-cancel {
    background: #ffffff;
    border: 1px solid #0066df;
    color: #0066df;
}

.btn-cancel-del {
    background: #ffffff;
    border: 1px solid #999;
    background: #ffffff;
    color: #666;

    &:hover {
        border: 1px solid #e50101;
        color: #e50101;
    }
}


.btn-save {
    background: #0066df;
    border-color: #0066df;
    color: #fff;

    &.disabled {
        background: #999999;
        border-color: #999;
        pointer-events: none;
    }
}


.header {
    height: 51px;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 15px;
    background-color: #0653AF;

    .u-input {
        height: 30px;
        line-height: 30px;
        background-color: #fff;
        text-align: right;
    }
}

.group-item {
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 50px;
    margin-bottom: 8px;
    flex: 1;


    &_name {
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 12px;
        border-bottom: 1px solid #999;
        min-height: 50px;
        flex: 1;
    }
}

.round {
    width: 34px;
    height: 34px;
    background: #4f7af6;
    border-radius: 17px;
    color: #ffffff;
    font-size: 14px;
    text-align: center;
    line-height: 34px;
}

.check-icon {
    margin-right: 15px;
}

.icon-rotate {
    transform: rotate(90deg);
}

.personnel-item {
    display: flex;
    flex-direction: column;
    padding: 0 15px;

    &_parent,
    &_child {
        height: 50px;
        display: flex;
        align-items: center;

        &:hover {
            background: rgba(79, 122, 246, 0.1);
        }
    }
}

.content-item {
    display: flex;
    align-items: center;
    cursor: pointer;

    &:hover {
        background: rgba(79, 122, 246, 0.1);
    }

    .label {
        display: flex;
        flex: 1;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #333;
        padding: 0 8px;
    }
}

.search-back-btn {
    width: 60px;
    height: 28px;
    border-radius: 3px;
    margin: 30px auto;
    background: #FFFFFF;
    text-align: center;
    line-height: 28px;
    border: 1px solid #4F7AF6;
    font-family: Source Han Sans;
    color: #4F7AF6;
}

view {
    box-sizing: border-box;
}
</style>