<template>
    <div class="worker-handle">
        <div class="content">
            <div class="row">
                <div class="item">
                    <span class="label required">工作组名称</span>
                    <div class="control-right">
                        <div class="control input-group">
                            <up-input input-align="right" border="none" type="text" placeholder="请输入"
                                v-model="departInfo.name" maxlength="10" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="item">
                    <span class="label">设置账密</span>
                    <div class="control-right">
                        <up-switch v-model="departInfo.isSetPassword" />
                    </div>
                </div>
                <template v-if="departInfo.isSetPassword">
                    <div class="item">
                        <span class="label required">账号</span>
                        <div class="control-right">
                            <div class="control input-group">
                                <up-input input-align="right" border="none" type="text" placeholder="请输入"
                                    v-model="departInfo.username" maxlength="10" />
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <span class="label required">密码</span>
                        <div class="control-right">
                            <div class="control input-group">
                                <up-input input-align="right" border="none" type="text" placeholder="请输入"
                                    v-model="departInfo.password" />
                            </div>
                        </div>
                    </div>
                    <div class="item password-placeholder">
                        密码为英文大写字母、小写字母、特殊字符、数字中的至少三种组合长度为8-20位
                    </div>
                </template>
            </div>
            <div class="row" style="padding-top: 0;">
                <div class="item item-config">
                    <span class="label required">配置权限</span>
                </div>
                <Permissions class="item" v-model="departInfo.permissionList" @permission-change="onPermissionChange"
                    :default-names="['职工权限']" />
            </div>
        </div>
        <div class="footer">
            <BaseButton btn-type="delete" v-if="departInfo.id" @click="showConfirm = true" />
            <BaseButton btn-type="cancel" v-else @click="cancel" />
            <BaseButton btn-type="save" @click="save" :disabled="isDisabledSave" />
        </div>
        <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="del">您确定要删除该工作组吗</ConfirmDialog>
    </div>
</template>

<script setup lang="ts">
import useMessage from '@/hooks/use-message';
import { isEmpty } from '@/utils';
import { computed, onMounted, ref, toValue } from 'vue';
import { IPermission } from '../models/Personnel';
import Permissions from '../Permissions.vue';
import personnelService from '../service/personnel.service';
class WorkerHandle {
    id?: string
    name?: string
    username?: string
    password?: string
    /** 权限 */
    permissionList?: IPermission[] = []
    /** 是否设置账密 */
    isSetPassword?: boolean = false  // 工作组默认关闭
}
const props = defineProps<{ id?: string }>()
const departInfo = ref<WorkerHandle>(new WorkerHandle())
const showConfirm = ref(false)
onMounted(() => {
    if (props.id) {
        uni.setNavigationBarTitle({ title: '编辑工作组' })
        getInfo()
    } else {
        uni.setNavigationBarTitle({ title: '新增工作组' })
    }
})
const getInfo = () => {
    if (!props.id) return
    personnelService.workgroup.getInfo(props.id).then(res => {
        departInfo.value = res
        departInfo.value.isSetPassword = !!res.username && !!res.password
    })
}
const isDisabledSave = computed(() => {
    const { password = '', isSetPassword, name, permissionList, username } = toValue(departInfo);
    /** 密码够8位数时按钮可点击 */
    return !name || !permissionList?.length || (isSetPassword ? (!username || !password || password?.length < 8) : false)
})
const message = useMessage()
/** 保存工作组 */
const save = () => {
    const { password = '', isSetPassword } = toValue(departInfo) || {}
    const validateRequired: Partial<Record<keyof WorkerHandle, string>> = isSetPassword ? {
        'name': '工作组名称',
        'username': '账号',
        'password': '密码',
        'permissionList': '权限'
    } : {
        'name': '工作组名称',
        'permissionList': '权限'
    }
    let errorMsg = '';
    for (const key in validateRequired) {
        const valueIsEmpty = isEmpty(departInfo.value[key as keyof WorkerHandle])
        if (valueIsEmpty) {
            if (key == 'permissionList') {
                errorMsg = '请配置权限'
            } else
                errorMsg = '请填写' + validateRequired[key as keyof WorkerHandle]
            break;
        }
    }
    if (errorMsg) {
        message.show(errorMsg)
        return
    }
    // 校验密码格式
    if (isSetPassword) {
        if (password && (password.length < 8 || password.length > 20)) {
            message.show('密码格式错误，请重试~')
            return
        }
        const types = [
            /[A-Z]/.test(password),  // 大写字母
            /[a-z]/.test(password),  // 小写字母
            /\d/.test(password),     // 数字
            /[#?!@$%^&*_-]/.test(password)  // 特殊符号
        ];
        const len = types.filter(Boolean).length
        if (len < 3) {
            message.show('密码格式错误，请重试~')
            return
        }
    }
    if (!isSetPassword) {
        departInfo.value.username = ''
        departInfo.value.password = ''
    }

    personnelService.workgroup.save(departInfo.value, { successTip: `${props.id ? '编辑成功' : '新增成功'}` }).then(_ => {
        uni.navigateBack()
    })
}
const cancel = () => {
    uni.navigateBack()
}
const del = () => {
    const id = toValue(departInfo)?.id
    if (!id) return;
    personnelService.workgroup.delete(id, () => showConfirm.value = false).then(_ => {
        uni.navigateBack()
    })
}
// 互斥权限
const conflictingPermissionNames = ['物业巡检', '白班巡检', '夜班巡检', '保洁清洁']
const onPermissionChange = ({ name: permissionName }: IPermission) => {
    if (conflictingPermissionNames.includes(permissionName!)) {
        // 过滤掉所有冲突的权限名，保留当前项的权限名
        toValue(departInfo).permissionList = toValue(departInfo).permissionList?.filter(
            permission => !conflictingPermissionNames.includes(permission.name!) || permission.name === permissionName
        );
    }
}
</script>

<style scoped lang="scss">
@import "../persons-base.scss";

.label {
    display: inline-block;
    padding-left: 10px;
    color: #333;
}

.required {
    position: relative;

    &::after {
        content: '*';
        color: red;
        position: absolute;
        left: 0;
        top: 0;
    }
}

.row {
    background-color: #fff;
    margin-bottom: 8px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    background: #FDFDFD;
    padding: 17px 0;
    box-shadow: 0px 0px 4px 0px #92929233;
}

.password-placeholder {
    font-size: 12px;
    color: #999999;
    margin-bottom: 8px;
}

.permissions-wrapper {
    display: flex;
    align-items: center;
    gap: 10px 15px;
    flex-wrap: wrap;
    padding: 10px 0;

    .permissions-item {
        display: flex;
        align-items: center;
        gap: 4px;
    }
}

.item {
    display: flex;
    align-items: center;
    padding: 0 15px;
}

.item-config {
    border-bottom: 1px solid rgba(153, 153, 153, 0.2);
    height: 40px;
}

.footer {
    flex: 1;
    padding: 0 15px 15px;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    button {
        width: 140px;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        border-radius: 3px;
    }
}

.btn-cancel {
    background: #FFFFFF;
    border: 1px solid #0066DF;
    color: #0066DF;
}

.btn-cancel-del {
    background: #FFFFFF;
    border: 1px solid #999999B2;
    background: #FFFFFF;
    color: #666;

    &:hover {
        border: 1px solid #E50101;
        color: #E50101;
    }
}

.btn-save {
    background: #0066DF;
    border-color: #0066DF;
    color: #fff;

    &.disabled {
        background: #999999;
        border-color: #999;
        pointer-events: none;
    }
}

.control-right {
    flex: 1;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .control {
        width: 186px;
        height: 26px;
        line-height: 26px;
    }

    .input-group {
        background-color: #f8f8f8;
        border-radius: 3px;
        padding-right: 11px;
    }
}

.align-right {
    text-align: right;
}

.worker-handle {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f4f6fa;
}

.content {
    flex-grow: 1;
    overflow-y: auto;
}

view {
    box-sizing: border-box;
}
</style>