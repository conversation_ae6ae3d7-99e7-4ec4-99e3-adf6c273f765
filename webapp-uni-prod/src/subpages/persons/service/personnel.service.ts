import { get, post } from '@/service/http';
import { IPermissionData, IPersonnelGroupItem, Personnel, PersonnelSearch } from '../models/Personnel';
const DELETE_TIP = {
  successTip: '删除成功',
};
const SAVE_TIP = {
  successTip: '保存成功',
};
/** 职工列表 */
const getList = (data: PersonnelSearch) => {
  data.ifFilterByCurrentSubBranch = true;
  return post('/Personnel/getList', { data });
};
/**
 * 机构列表
 * @params {onlyLeaf} 是否只查询叶子节点（没有子组织的组织）
 * @params {ifFilterByCurrentSubBranch} 是否按当前用户所属分中心过滤组织
 */
const getListOrganization = (data: { ifPage: boolean; loadPersonnelList: boolean; keyword?: string; ownerWorkgroupId?: string; onlyLeaf?: boolean; ifFilterByCurrentSubBranch?: boolean }) => {
  return post<IPersonnelGroupItem[]>('/Organization/getList', { data });
};
/**
 * 工作组列表
 * @params { onlyCurrentBranch 是否仅查询当前分支组织的工作组 }
 * **/
const getListWorkGroup = (data: { ifPage: boolean; loadPersonnelList: boolean; keyword?: string }) => {
  return post<IPersonnelGroupItem[]>('/Workgroup/getList', { data: { ...data, onlyCurrentBranch: true } });
};
const delete_organization = (id: string, beforeSleep?: Function) => {
  return get(`/Organization/delete/${id}`, { custom: { ...DELETE_TIP, beforeSleep } });
};
const save_organization = (data: IPersonnelGroupItem, tip: { successTip?: string; errorTip?: string } = {}) => {
  return post('/Organization/save', { data, custom: { ...SAVE_TIP, ...tip } });
};
const get_organization = (id: string) => {
  return get(`/Organization/getInfo/${id}`);
};
const get_workgroup = (id: string) => {
  return get<IPersonnelGroupItem>(`/Workgroup/getInfo/${id}`);
};
const delete_workgroup = (id: string, beforeSleep?: Function) => {
  return get(`/Workgroup/delete/${id}`, { custom: { ...DELETE_TIP, beforeSleep } });
};
const save_workgroup = (data: IPersonnelGroupItem, tip: { successTip?: string; errorTip?: string } = {}) => {
  return post('/Workgroup/save', { data, custom: { ...SAVE_TIP, ...tip } });
};
const delete_personnel = (id: string, beforeSleep?: Function) => {
  return get(`/Personnel/delete/${id}`, { custom: { ...DELETE_TIP, beforeSleep } });
};
const save_personnel = (data: Personnel) => {
  return post('/Personnel/save', { data, custom: { ...SAVE_TIP } });
};
const get_personnel = (id: string) => {
  return get<Personnel>(`/Personnel/getInfo/${id}`);
};
/** 权限列表（人员） */
const getPermissions = () => {
  return get<IPermissionData[]>(`/Permission/getGroupsForPersonnelSelection`);
};
/** 权限列表 */
const getPositions = (data?: { ifPage: boolean; currentPage?: number; pageRecord?: number; nameLike?: string }) => {
  return post<{ id: string; name: string }[]>(`/Position/getList`, { data });
};
const save_positions = (data: { id?: string; name?: string }, beforeSleep?: Function) => {
  return post('/Position/save', { data, custom: { ...SAVE_TIP, beforeSleep } });
};
const delete_positions = (id: string, beforeSleep?: Function) => {
  return get(`/Position/delete/${id}`, { custom: { ...DELETE_TIP, beforeSleep, errorTip: '' } });
};
const getListForWorkgroupSelection = (keyword?: string) => {
  return post('/Personnel/getGroupsForWorkgroupSelection', { data: { keyword } });
};

/** 权限列表（工作组） */
const getPermissionsForWorkgroup = () => {
  return get<IPermissionData[]>(`/Permission/getGroupsForWorkgroupSelection`);
};

/** 机构 */
const organization = {
  getList: getListOrganization,
  delete: delete_organization,
  save: save_organization,
  getInfo: get_organization,
};
/** 工作组 */
const workgroup = {
  getList: getListWorkGroup,
  delete: delete_workgroup,
  save: save_workgroup,
  getInfo: get_workgroup,
};
const personnel = {
  getList,
  delete: delete_personnel,
  save: save_personnel,
  getInfo: get_personnel,
  getListForWorkgroupSelection,
};
/** 职务 */
const position = {
  getList: getPositions,
  delete: delete_positions,
  save: save_positions,
  // getInfo: get_organization,
};
export default { organization, workgroup, personnel, getPermissions, position, getPermissionsForWorkgroup };
