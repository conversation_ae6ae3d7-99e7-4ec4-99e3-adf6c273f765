import { ICommuteCar, ICommuteMapStation } from '@/models/Commute';
import { WS_RES_CHANNEL, WsMessage } from '@/models/Websocket';
import { MapMarker, MapPolyline } from '@uni-helper/uni-app-types';
import { onUnmounted, ref, toValue } from 'vue';

interface LocationPoint {
  longitude: number;
  latitude: number;
}

interface BusMapOptions {
  busIconPath?: string;
  busUnActiveIconPath?: string;
  stationPastIconPath?: string;
  stationFutureIconPath?: string;
  startIconPath?: string;
  endIconPath?: string;
  animationInterval?: number;
  routeColor?: string;
}
const DEFAULT_OPTIONS: BusMapOptions = {
  // 绝对路径
  busIconPath: '/static/main/commute/bus3x.png',
  // busUnActiveIconPath: '/static/main/commute/bus-unactive.png',
  stationPastIconPath: '/static/main/commute/10-6-3x.png',
  stationFutureIconPath: '/static/main/commute/10-7-3x.png',
  startIconPath: '/static/main/commute/start3x.png',
  endIconPath: '/static/main/commute/end3x.png',
  animationInterval: 2000,
  routeColor: '#03CE6A',
};

export function useWmapDraw(options: BusMapOptions = {}) {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  const polyline = ref<MapPolyline[]>([]);
  const baseUrl = import.meta.env.VITE_API_BASE_URL as string;
  // 设置站点标记
  const drawMarker = (stations: ICommuteMapStation[]) => {
    const { startIconPath, endIconPath, stationPastIconPath, stationFutureIconPath } = mergedOptions;
    const markers = stations.filter(e => !e.hideDrawer);
    const markerArr =
      markers.map((e, index) => {
        const defaultUrl = index === 0 ? startIconPath : index === markers.length - 1 ? endIconPath : '';
        const markerTypePath = e._markerType == 'future' ? stationFutureIconPath : e._markerType == 'past' ? stationPastIconPath : '';
        const iconPath = e.iconUrl || markerTypePath || defaultUrl || stationFutureIconPath!;
        const width = e.width || 16;
        const height = e.height || (defaultUrl ? 24 : 16);
        return <MapMarker & { _label: MapMarker['label'] } & ICommuteMapStation>{
          id: generateRandomNumber(),
          longitude: e.longitude,
          latitude: e.latitude,
          width,
          height,
          iconPath,
          anchor: { x: 0.5, y: 0.5 },
          label: { content: e.name, anchorX: -width, anchorY: height / 2, color: '#333', fontSize: 12, bgColor: '#fff', borderColor: '#fff', padding: 6, borderRadius: 3 },
          _label: { content: e.name, anchorX: -width, anchorY: height / 2, color: '#333', fontSize: 12, bgColor: '#fff', borderColor: '#fff', padding: 6, borderRadius: 3 },
          _type: e._type,
        };
      }) || [];
    return markerArr;
  };

  // 设置路线
  const setRouteLine = (points: LocationPoint[], lineColor?: string, borderColor?: string) => {
    const polylineArr: MapPolyline[] = [
      {
        points,
        color: lineColor || mergedOptions.routeColor,
        width: 6,
        dottedLine: false,
        arrowLine: true,
        borderColor,
      },
    ];
    polyline.value = polylineArr;
    return polylineArr;
  };
  const socketTaskInstance = ref<UniApp.SocketTask | null>(null);
  /** 原始实时班车位置数据 */
  const newShuttleDatas = ref<ICommuteCar[]>([]);
  /** 原始实时班车marker */
  const newShuttleMarkers = computed(() => {
    const newMarkers =
      toValue(newShuttleDatas).map(s => {
        return <MapMarker>{
          id: generateRandomNumber(),
          longitude: s.lng,
          latitude: s.lat,
          iconPath: mergedOptions.busIconPath,
          height: 28,
          width: 28,
          anchor: { x: 0.5, y: 0.5 },
        };
      }) || [];
    return newMarkers;
  });
  /** 创建局部 websocket；获取实时公交位置 */
  const connectSocketBus = async (data?: { carId?: string; routeId?: string }) => {
    const wsReportUrl = baseUrl.replace(/^http(s)?:/, 'wss:'); // 小程序中必须是 wss:// 协议
    const url = `${wsReportUrl}/websocket-smartlogixmini`;
    closeSocketBus();
    // uni.connectSocket() 正常使用时是会返回 task 对象的，如果想获取 task ，则不要使用 Promise 化
    socketTaskInstance.value = uni.connectSocket({
      url,
      success() {
        console.log('实时公交位置 WebSocket 连接成功');
      },
      fail() {
        console.log('实时公交位置 WebSocket 连接失败');
      },
    });
    const { carId, routeId } = data || {};
    socketTaskInstance.value.onOpen(() => {
      socketTaskInstance.value?.onMessage(res => {
        const messageData = JSON.parse(res.data) as WsMessage<ICommuteCar[]>;
        if (messageData.type == WS_RES_CHANNEL.SHUTTLE) {
          let datas = messageData.data || [];
          // 优先剔除同路线
          if (routeId) {
            datas = datas.filter(s => s.routeId == routeId);
            if (carId) {
              datas.forEach(s => (s.isOtherCar = s.id != carId));
            }
          } else if (carId) {
            datas = datas.filter(s => s.id == carId);
          }
          newShuttleDatas.value = datas;
          console.log('🚀 ~ use-wmap-draw.ts ~ socketTaskInstance.value.onOpen ~ newShuttleDatas.value:', newShuttleDatas.value);
        }
      });
    });
  };
  const closeSocketBus = () => {
    toValue(socketTaskInstance)?.close({
      success: () => {
        socketTaskInstance.value = null;
      },
    });
  };
  // 清理资源
  const cleanup = () => {
    closeSocketBus();
  };

  onUnmounted(() => {
    cleanup();
  });
  /**
   * 起初为markerId设计，生成2-9位的随机数，用于markerId；
   * 建议为每个marker设置上Number类型id，保证更新marker时有更好的性能。最大限制9位数
   */
  function generateRandomNumber(): number {
    // 随机确定位数(2-9位)
    const length = Math.floor(Math.random() * 8) + 2;

    // 生成第一位数字(1-9)
    const firstDigit = Math.floor(Math.random() * 9) + 1;

    // 生成剩余的数字
    let remainingDigits = '';
    for (let i = 0; i < length - 1; i++) {
      remainingDigits += Math.floor(Math.random() * 10);
    }

    // 组合并返回结果
    return parseInt(`${firstDigit}${remainingDigits}`, 10);
  }
  return {
    drawMarker,
    connectSocketBus,
    newShuttleMarkers,
    closeSocketBus,
    newShuttleDatas,
    setRouteLine,
  };
}
