<template>
    <div class="config-manager-wrapper">
        <div class="config-item" v-for="item of options" :key="item.label" @click="navigateTo(item.url)">
            <div class="config-item_icon">
                <SlSVgIcon :name="item.icon" size="30" />
            </div>
            <div class="config-item_label">
                {{ item.label }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const options: { label: string, url: string, icon: string }[] = [
    { label: '假期配置', url: '/subpages/config-manager/components/' + 'holiday/index', icon: '30-30-4' },
    { label: '理发配置', url: '', icon: '30-30-5' },
    { label: '路线配置', url: '/subpages/config-manager/components/' + 'way/index', icon: '30-30-6' },
]
const navigateTo = (url: string) => {
    url && uni.navigateTo({ url })
}
</script>

<style scoped lang="scss">
.config-manager-wrapper {
    padding: 10px 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    background: #F4F6FA;
    width: 100%;
    height: 100%;
}

.config-item {
    width: 90px;
    height: 90px;
    border-radius: 10px;
    background: #FDFDFD;
    box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;

    &_icon {
        width: 30px;
        height: 30px;
    }

    &_label {
        font-size: 12px;
        color: #333333;
    }
}
</style>