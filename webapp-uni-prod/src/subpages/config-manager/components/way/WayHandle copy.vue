<template>
    <div class="way-handle-wrapper">
        <div class="content">
            <div class="content_control">
                <!-- 触摸开始：记录初始位置 -->
                <!-- 触摸移动：实时交换数据 -->
                <!-- 触摸结束：确认最终顺序 -->
                <div class="item drag-item" v-for="(item, index) of wayDatas" :key="item.id"
                    :class="{ 'dragging': index === draggingIndex }" :style="getItemStyle(index)"
                    @touchstart="handleTouchStart($event, index)" @touchmove="handleTouchMove($event, index)"
                    @touchend="handleTouchEnd" @touchcancel="handleTouchEnd">
                    <!-- todo：途经点ICon -->
                    <SlSVgIcon :name="'12-12-' + (index == 0 ? '19' : index == wayDatas.length - 1 ? '18' : '12')"
                        size="12" />
                    <div class="item-input">
                        <up-input type="text"
                            :placeholder="'输入' + (index == 0 ? '起点' : index == wayDatas.length - 1 ? '终点' : '经停站点')"
                            v-model="item.name" clearable :border="'none'" />
                    </div>
                    <!-- 可删除 -->
                    <SlSVgIcon v-if="index != wayDatas.length - 1 && index != 0" name="16-16-16" size="16"
                        @click="wayDatas.splice(index, 1)" />
                </div>
            </div>
            <div class="content_add">
                <div class="content_add-left" @click="onAddVia">
                    <SlSVgIcon style="display: inline-flex;" name="14-14-9" size="14" />
                    添加途径点
                </div>
                <div>
                    预计
                    <span style="color: #E50101;">
                        {{ arrivalTime }}
                    </span>
                </div>
            </div>
        </div>
        <div class="map">

        </div>
        <div class="footer">
            <BaseButton btnType="delete" v-if="props.id" @click="del" />
            <BaseButton btnType="cancel" v-else @click="cancel" />
            <BaseButton btnType="save" @click="save" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { uuidv4 } from '@/utils/uuid';

class RowItem {
    name?: string;
    id?: string
}
const props = defineProps(['id'])
/** 预计到达时间 */
const arrivalTime = ref('0时0分')
const wayDatas = ref<RowItem[]>(Array.from({ length: 2 }, () => ({ id: uuidv4() })))
const del = () => { }
const cancel = () => { }
const save = () => { }
/** 添加途经点 */
const onAddVia = () => {
    // 往倒数第二个位置的插入新数据
    const newVia = new RowItem()
    newVia.id = uuidv4()
    newVia.name = '途经点' + wayDatas.value.length
    const index = Math.max(0, wayDatas.value.length - 1); // 防止数组为空的情况
    wayDatas.value.splice(index, 0, newVia);
}


// 拖拽状态
const draggingIndex = ref(-1)
const startY = ref(0)
const currentY = ref(0)
const dragOffset = ref(0)
const isDragging = ref(false)
const placeholderIndex = ref(-1)

// 计算拖拽项的样式
const getItemStyle = (index: number) => {
    // 拖拽中的项
    if (index === draggingIndex.value) {
        return {
            transform: `translateY(${dragOffset.value}px) scale(1.02)`,
            opacity: '0.9',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            zIndex: '100',
            transition: 'none'
        }
    }

    // 占位项（被拖拽项原本的位置）
    if (index === placeholderIndex.value) {
        return {
            opacity: '0.5',
            height: '0',
            padding: '0',
            margin: '0',
            transition: 'all 0.2s ease-out'
        }
    }

    // 其他项的让位动画
    if (isDragging.value && placeholderIndex.value !== -1) {
        // 计算需要移动的距离
        const moveDistance = getItemMoveDistance(index)
        if (moveDistance !== 0) {
            return {
                transform: `translateY(${moveDistance}px)`,
                transition: 'transform 0.2s ease-out'
            }
        }
    }

    return {}
}

// 计算其他项需要移动的距离
const getItemMoveDistance = (index: number) => {
    if (placeholderIndex.value === -1) return 0

    const itemHeight = 32 // 列表项高度
    const dragDirection = Math.sign(dragOffset.value)

    // 拖拽项向下移动，下方的项需要上移
    if (dragDirection > 0 && index > placeholderIndex.value && index <= draggingIndex.value) {
        return -itemHeight
    }

    // 拖拽项向上移动，上方的项需要下移
    if (dragDirection < 0 && index < placeholderIndex.value && index >= draggingIndex.value) {
        return itemHeight
    }

    return 0
}

/**
 * 触摸开始：记录初始信息
 */
function handleTouchStart(e: TouchEvent, index: number) {
    if (isDragging.value) return

    draggingIndex.value = index
    startY.value = e.touches[0].clientY
    currentY.value = startY.value
    dragOffset.value = 0
    placeholderIndex.value = index
    isDragging.value = true

    // 添加拖拽中的类名，触发过渡动画
    nextTick(() => {
        isDragging.value = true
    })
}

/**
 * 触摸移动：计算偏移、交换数组项
 */
function handleTouchMove(e: TouchEvent, currentIndex: number) {
    if (!isDragging.value || currentIndex !== draggingIndex.value) return

    const moveY = e.touches[0].clientY
    dragOffset.value = moveY - startY.value

    // 只有移动超过阈值才进行排序
    if (Math.abs(dragOffset.value) < 32) return

    const itemHeight = 32 // 列表项高度
    const dragDirection = Math.sign(dragOffset.value)
    const dragDistance = Math.abs(dragOffset.value)

    // 计算目标索引（根据拖拽距离决定要交换的位置）
    const targetIndex = placeholderIndex.value + Math.floor(dragDistance / itemHeight) * dragDirection

    // 边界检查
    if (targetIndex < 0 || targetIndex >= toValue(wayDatas).length) return

    // 交换数组项
    if (targetIndex !== placeholderIndex.value) {
        const newList = [...toValue(wayDatas)]
        const draggedItem = newList[placeholderIndex.value]

        // 移除原位置的项
        newList.splice(placeholderIndex.value, 1)

        // 插入到新位置
        newList.splice(targetIndex, 0, draggedItem)

        wayDatas.value = newList
        draggingIndex.value = targetIndex
        placeholderIndex.value = targetIndex
    }
}

/**
 * 触摸结束：重置状态，提交更新
 */
function handleTouchEnd() {
    if (!isDragging.value) return

    // 重置状态
    isDragging.value = false
    draggingIndex.value = -1
    startY.value = 0
    currentY.value = 0
    dragOffset.value = 0
    placeholderIndex.value = -1

    // 通知父组件更新列表
}
</script>

<style scoped lang="scss">
.way-handle-wrapper {
    width: 100%;
    background: #fdfdfd;
    padding-bottom: 90px;
}

.content {
    padding: 15px;
}

.content_control {
    border-radius: 3px;
    background: #F8F8F8;
    box-sizing: border-box;
    border: 1px solid #F0F0F0;

    .item {
        height: 32px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        gap: 8px;

        .item-input {
            flex: 1;
        }
    }
}

.content_add {
    display: flex;
    width: 100%;
    justify-content: space-between;
    margin-top: 10px;

    &-left {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #4f7af6;
    }
}

.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    border-radius: 10px 10px 0px 0px;
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);
}


.drag-item {
    transition: all 0.2s ease;
    user-select: none;
}

.dragging {
    cursor: grabbing;
}
</style>