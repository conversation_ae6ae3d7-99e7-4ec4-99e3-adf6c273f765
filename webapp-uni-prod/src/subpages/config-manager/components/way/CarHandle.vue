<template>
    <div class="car-handle-wrapper">
        <div class="content">
            <div class="content-item">
                <span>车牌号</span>
                <div class="input-group">
                    <up-input type="text" placeholder="请输入车牌号" v-model="carCode" clearable maxlength="8"
                        :border="'none'" />
                </div>
            </div>
        </div>
        <div class="footer">
            <BaseButton btnType="delete" v-if="props.id" @click="del" />
            <BaseButton btnType="cancel" v-else @click="cancel" />
            <BaseButton btnType="save" :disabled="!carCode" @click="save">确定</BaseButton>
        </div>
    </div>
</template>

<script setup lang="ts">
const props = defineProps(['id', 'name'])
const carCode = ref('')
onMounted(() => {
    console.log(props, 'proprs')
    if (props.name && props.id) {
        uni.setNavigationBarTitle({ title: '编辑车辆' })
        carCode.value = props.name
    }
})
const del = () => { }
const cancel = () => { }
const save = () => { }
</script>

<style scoped lang="scss">
.car-handle-wrapper {
    display: flex;
    flex-direction: column;
    padding: 10px 15px;
    width: 100%;
    height: 100vh;
}

.content {
    flex: 1;
    &-item {
        display: flex;
        align-items: center;
        gap: 22px;
    }
}

.footer {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: space-between;
}

.input-group {
    background-color: #F8F8F8;
    border-radius: 3px;
    margin-top: 10px;
    height: 41px;
    display: flex;
    flex: 1;
    align-items: center;
    padding-left: 10px;
}
</style>