<template>
    <div class="way-container">
        <div class="content">
            <!-- 路线规划 -->
            <div class="way-box">
                <div class="title">路线规划</div>
                <div class="content">
                    <div class="item" v-for="item of wayDatas" :key="item.id">
                        <SlSVgIcon name="20-20-30" size="20" />
                        <div class="item_label content-item_way">
                            <span>{{ item.startWay }}</span>
                            <SlSVgIcon style="display: inline-flex;" name="14-14-13" size="14" />
                            <span>{{ item.endWay }}</span>
                        </div>
                        <SlSVgIcon name="20-20-29" size="20" @click="handelData.editWay(item)" />
                    </div>
                </div>
                <div class="content_footer">
                    <div class="btn-add" @click="handelData.addWay">新增</div>
                </div>
            </div>
            <!-- 车辆规划 -->
            <div class="car-box">
                <div class="title">车辆规划</div>
                <div class="content">
                    <div class="item" v-for="item of carDatas" :key="item.id">
                        <SlSVgIcon name="20-20-31" size="20" />
                        <span class="item_label">{{ item.name }}</span>
                        <SlSVgIcon name="20-20-29" size="20" @click="handelData.editCar(item)" />
                    </div>
                </div>
                <div class="content_footer">
                    <div class="btn-add" @click="handelData.addCar">新增</div>
                </div>
            </div>
        </div>
        <div class="footer">
            <BaseButton btnType="cancel" @click="cancel" />
            <BaseButton btnType="save" @click="save" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { uuidv4 } from '@/utils/uuid'

interface RowItem {
    id?: string
    uuid?: string
    name?: string
    startWay?: string
    endWay?: string
}
const URL_PREFIX = '/subpages/config-manager/components/way/'
const cancel = () => { }
const save = () => { }
const wayDatas = ref<RowItem[]>(Array.from({ length: 5 }, (_, i) => ({ id: uuidv4(), startWay: `start${i}`, endWay: `end${i}` })))
const carDatas = ref<RowItem[]>(Array.from({ length: 5 }, (_, i) => ({ id: uuidv4(), name: `津${i}` })))
const onEditWay = (item: RowItem) => {
    uni.navigateTo({ url: URL_PREFIX + `WayHandle?id=${item.id}` })
}
const onEditCar = (item: RowItem) => {
    console.log("🚀 ~ index.vue ~ onEditCar ~ onEditCar:", item)
    uni.navigateTo({ url: URL_PREFIX + `CarHandle?id=${item.id}&name=${item.name}` })
}

const handelData = {
    'editWay': onEditWay,
    'editCar': onEditCar,
    'addWay': () => { uni.navigateTo({ url: URL_PREFIX + 'WayHandle' }) },
    'addCar': () => { uni.navigateTo({ url: URL_PREFIX + 'CarHandle' }) },
}
</script>

<style scoped lang="scss">
.way-container {
    width: 100%;
    background: #fdfdfd;
    padding-bottom: 90px;
}

.way-box,
.car-box {
    margin-bottom: 10px;
    background-color: #fff;
    box-shadow: 0px 0px 6px 0px rgba(146, 146, 146, 0.2);
    padding: 10px;

}

.title {
    height: 40px;
    line-height: 40px;
}

.item {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(153, 153, 153, 0.2);

    &_label {
        display: inline-block;
        flex: 1;
        padding: 0 10px;
        text-overflow: ellipsis;
    }
}

.content-item_way {
    display: flex;
    align-items: center;
    gap: 5px;
}

.content_footer {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 20px 0;


    .btn-add {
        width: 60px;
        line-height: 28px;
        height: 28px;
        color: #fff;
        font-size: 14px;
        border-radius: 3px;
        background: #4F7AF6;
        text-align: center;
    }
}

.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    border-radius: 10px 10px 0px 0px;
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);
}
</style>