<template>
    <div class="way-handle-wrapper">
        <div class="content">
            <div class="content_control">
                <!-- 触摸开始：记录初始位置 -->
                <!-- 触摸移动：实时交换数据 -->
                <!-- 触摸结束：确认最终顺序 -->
                <div class="item" v-for="(item, index) of wayDatas" :key="item.id">
                    <!-- @touchstart="handleTouchStart($event, index)" @touchmove="handleTouchMove($event, index)"
                    @touchend="handleTouchEnd" -->
                    <SlSubSvgIcon subpage="config-manager" v-if="index == 0 || index == wayDatas.length - 1"
                        :name="'12-12-' + (index == 0 ? '19' : '18')" size="12" />
                    <!-- 途经点 -->
                    <SlSubSvgIcon subpage="config-manager" v-else name="10-10-3" size="10" />
                    <span @click.stop="mockInputtips(item.name || '')">mock</span>
                    <div class="item-input">
                        <up-input type="text"
                            :placeholder="'输入' + (index == 0 ? '起点' : index == wayDatas.length - 1 ? '终点' : '经停站点')"
                            v-model="item.name" clearable :border="'none'" />
                    </div>
                    <!-- 可删除 -->
                    <SlSubSvgIcon subpage="config-manager" v-if="index != wayDatas.length - 1 && index != 0"
                        name="16-16-16" size="16" @click="wayDatas.splice(index, 1)" />
                </div>
            </div>
            <div class="content_add">
                <div class="content_add-left" @click="onAddVia">
                    <SlSubSvgIcon subpage="config-manager" style="display: inline-flex;" name="14-14-9" size="14" />
                    添加途径点
                </div>
                <div @click="mockRoute">
                    预计
                    <span style="color: #E50101;">
                        {{ arrivalTime }}
                    </span>
                </div>
            </div>
        </div>
        <div class="map">

        </div>
        <div class="footer">
            <BaseButton btnType="delete" v-if="props.id" @click="showConfirm = true" />
            <BaseButton btnType="cancel" v-else @click="cancel" />
            <BaseButton btnType="save" @click="save" />
        </div>
    </div>
    <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="del">您确定要删除该路线吗</ConfirmDialog>
</template>

<script setup lang="ts">
import { useWmap } from '@/hooks';
import { uuidv4 } from '@/utils/uuid';

class RowItem {
    name?: string;
    id?: string
}
const props = defineProps(['id'])
const showConfirm = ref(false)
/** 预计到达时间 */
const arrivalTime = ref('0时0分')
const wayDatas = ref<RowItem[]>(Array.from({ length: 2 }, () => ({ id: uuidv4() })))
const del = () => {
    // showConfirm.value = false
}
const cancel = () => { }
const save = () => { }
/** 添加途经点 */
const onAddVia = () => {
    // 往倒数第二个位置的插入新数据
    const newVia = new RowItem()
    newVia.id = uuidv4()
    newVia.name = '途经点' + wayDatas.value.length
    const index = Math.max(0, wayDatas.value.length - 1); // 防止数组为空的情况
    wayDatas.value.splice(index, 0, newVia);
}
const busMapUtil = useWmap()
interface IAddress {
    name: string
    address: string
    uuid: string
    location?: string
    infoDoor?: IAddress[]
}
const waySearchResult = ref<IAddress[]>([
])
interface IAddressTips {
    tips: Array<{
        name: string; // 名称
        location: string; // 坐标，格式："longitude,latitude"
        address: string; // 地址
        adcode: string; // 行政区划代码
        district: string; // 行政区
        city: string; // 城市
        province: string; // 省份
        type: string; // 类型
        typecode: string; // 类型代码
    }>;
}
const mockInputtips = (keyword: string) => {
    console.log("🚀 ~ WayHandle.vue ~ mockInputtips ~ keyword:", keyword)
    waySearchResult.value = []
    if (!keyword) {
        return
    }
    busMapUtil.getInputtips(keyword).then(res => {
        if (res == null) {
            return
        }
        const data = res as IAddressTips | null
        waySearchResult.value = data?.tips?.map(ele => ({
            name: ele.name,
            address: ele.address,
            location: ele.location,
            uuid: uuidv4()
        })) || []
        console.log("🚀 ~ WayHandle.vue ~ busMapUtil.getInputtips ~ res:", res, waySearchResult.value)
    }).catch(err => {
        console.log("🚀 ~ WayHandle.vue ~ busMapUtil.getInputtips ~ err:", err)
    })

}
const mockRoute = async () => {
    const stores = waySearchResult.value.map(ele => {
        return {
            id: ele.uuid,
            name: ele.name,
            longitude: Number(ele.location?.split(',')[0])!,
            latitude: Number(ele.location?.split(',')[1])!,
            _init: ele
        }
    })
    const nearestStoreItem = await busMapUtil.getNearestStation<IAddress>(stores)
    if (nearestStoreItem) {
        console.log("🚀 ~ WayHandle.vue ~ mockRoute ~ nearestStoreItem:", nearestStoreItem)
        const startItem = waySearchResult.value.find(ele => ele.name != nearestStoreItem.name)
        if (startItem) {
            const [startLongitude, startLatitude] = startItem.location?.split(',').map(Number) || []
            const [endLongitude, endLatitude] = nearestStoreItem.location?.split(',').map(Number) || []
            const nearestData = await busMapUtil.getDrivingMsgForTime({ start: { lat: startLatitude, lng: startLongitude }, end: { lat: endLatitude, lng: endLongitude } })
            console.log("🚀 ~ WayHandle.vue ~ mockRoute ~ nearestData:", nearestData)
        }
    }

    // const item = waySearchResult.value[0]
    // if (item) {
    //     console.log("🚀 ~ WayHandle.vue ~ mockRoute ~ item:", item)
    //     const [longitude, latitude] = item.location?.split(',') || []
    //     const stop = {
    //         id: uuidv4(),
    //         name: item.name,
    //         longitude,
    //         latitude,
    //         _init: item,
    //     }
    //     const store = await busMapUtil.getNearestStation([stop as any])
    //     if(store){
    //         const nearestData = await busMapUtil.getDrivingMsgForTime({ start: { lat: latitude, lng: longitude }, end: { lat: store.latitude, lng: store.longitude } })
    //     }
    // }
}
// 拖动状态：当前拖动项索引、初始触摸坐标
let startIndex = -1
let startY = 0

/**
 * 触摸开始：记录初始信息
 * @param {TouchEvent} e 触摸事件对象
 * @param {number} index 当前项索引
 */
function handleTouchStart(e: TouchEvent, index: number) {
    console.log("🚀 ~ WayHandle.vue ~ 触摸开始:", index)
    startIndex = index
    // 取第一个触摸点的 Y 坐标
    startY = e.touches[0].clientY
}

/**
 * 触摸移动：计算偏移、交换数组项
 * @param {TouchEvent} e 触摸事件对象
 * @param {number} currentIndex 当前项索引
 */
function handleTouchMove(e: TouchEvent, currentIndex: number) {
    if (startIndex === -1) return // 未开始拖动，直接返回
    console.log("🚀 ~ WayHandle.vue ~ 触摸移动:", currentIndex)

    const moveY = e.touches[0].clientY
    const diffY = moveY - startY
    // 简单判断：Y 轴偏移超过一定阈值（如 30px）再触发交换，避免误操作
    if (Math.abs(diffY) < 30) return

    // 计算目标索引（向上/向下拖动时交换的位置）
    const targetIndex = currentIndex + (diffY > 0 ? 1 : -1)
    // 边界判断：防止越界
    if (targetIndex < 0 || targetIndex >= wayDatas.value.length) return

        // 交换数组项（核心：模拟“拖动”视觉）
        ;[wayDatas.value[currentIndex], wayDatas.value[targetIndex]] = [wayDatas.value[targetIndex], wayDatas.value[currentIndex]]
    // 更新初始索引和触摸位置，持续拖动时保持逻辑
    startIndex = targetIndex
    startY = moveY
}

/**
 * 触摸结束：重置状态
 */
function handleTouchEnd() {
    startIndex = -1
    startY = 0
}
</script>

<style scoped lang="scss">
.way-handle-wrapper {
    width: 100%;
    background: #fdfdfd;
    padding-bottom: 90px;
}

.content {
    padding: 15px;
}

.content_control {
    border-radius: 3px;
    background: #F8F8F8;
    box-sizing: border-box;
    border: 1px solid #F0F0F0;

    .item {
        height: 32px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        gap: 8px;

        .item-input {
            flex: 1;
        }
    }
}

.content_add {
    display: flex;
    width: 100%;
    justify-content: space-between;
    margin-top: 10px;

    &-left {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #4f7af6;
    }
}

.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px 10px;
    border-radius: 10px 10px 0px 0px;
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);
}
</style>