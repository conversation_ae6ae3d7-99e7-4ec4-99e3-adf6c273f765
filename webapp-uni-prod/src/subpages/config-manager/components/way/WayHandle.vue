<template>
    <div class="way-handle-wrapper">
        <div class="content">
            <div class="content_control">
                <!-- 触摸开始：记录初始位置 -->
                <!-- 触摸移动：实时交换数据 -->
                <!-- 触摸结束：确认最终顺序 -->
                <div class="item" v-for="(item, index) of wayDatas" :key="item.uuid">
                    <!-- @touchstart="handleTouchStart($event, index)" @touchmove="handleTouchMove($event, index)"
                    @touchend="handleTouchEnd" -->
                    <SlSubSvgIcon subpage="config-manager" v-if="index == 0 || index == wayDatas.length - 1"
                        :name="'12-12-' + (index == 0 ? '19' : '18')" size="12" />
                    <!-- 途经点 -->
                    <SlSubSvgIcon subpage="config-manager" v-else name="10-10-3" size="10" />
                    <!-- <span @click.stop="handleNameChange2($event, item)">mock</span> -->
                    <div class="item-input">
                        <up-input type="text"
                            :placeholder="'输入' + (index == 0 ? '起点' : index == wayDatas.length - 1 ? '终点' : '经停站点')"
                            :modelValue="item.name" clearable :border="'none'"
                            @update:modelValue="handleNameChange($event, item)" />
                    </div>
                    <!-- 可删除 -->
                    <SlSubSvgIcon subpage="config-manager" v-if="index != wayDatas.length - 1 && index != 0"
                        name="16-16-16" size="16" @click="onDeleteItem(index)" />
                </div>
            </div>
            <div class="content_add">
                <div class="content_add-left" @click="onAddVia">
                    <SlSubSvgIcon subpage="config-manager" style="display: inline-flex;" name="14-14-9" size="14" />
                    添加途径点
                </div>
                <div v-show="arrivalTime">
                    预计
                    <span style="color: #E50101;">
                        {{ arrivalTime }}
                    </span>
                </div>
            </div>
            <div class="text-box" v-if="waySearchResult?.length">
                <div class="text-item" v-for="item of waySearchResult" :key="item.uuid" @click="handleSelect(item)">
                    <div class="text-top">
                        <SlSVgIcon class="icon" name="16-16-24" size="16" />
                        <div class="text-name">
                            <div class="name">
                                {{ item.name }}
                            </div>
                            <div class="name-info" v-show="item.address">
                                {{ item.address }}
                            </div>
                        </div>
                        <SlSVgIcon class="icon" name="14-14-12" size="14" />
                    </div>
                    <div class="text-bottom">
                        <span class="text-bottom-item" v-for="itemDoor of item.infoDoor" :key="itemDoor.uuid">
                            {{ itemDoor.name }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="map">
            <CommuteMap v-model:location="location" :polyline="allPolyline" :markers="allMarkers" :scale="scale"
                :showControl="false" />
        </div>
        <div class="footer">
            <BaseButton btnType="delete" v-if="props.id" @click="showConfirm = true" />
            <BaseButton btnType="cancel" v-else @click="cancel" />
            <BaseButton btnType="save" @click="save" />
        </div>
    </div>
    <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="del">您确定要删除该路线吗？</ConfirmDialog>
</template>

<script setup lang="ts">
import CommuteMap from '@/components/CommuteMap.vue';
import { useDebounce, useLoading, useWmap } from '@/hooks';
import useMessage from '@/hooks/use-message';
import { CommuteRouteInfo } from '@/models/Commute';
import commuteService from '@/service/commute/commute.service';
import { uuidv4 } from '@/utils/uuid';
interface IAddressTips {
    tips: Array<{
        name: string; // 名称
        location: string; // 坐标，格式："longitude,latitude"
        address: string; // 地址
        adcode: string; // 行政区划代码
        district: string; // 行政区
        city: string; // 城市
        province: string; // 省份
        type: string; // 类型
        typecode: string; // 类型代码
    }>;
}
interface IAddress {
    id?: string
    name: string
    address?: string
    uuid: string
    location?: string
    infoDoor?: IAddress[]
    latitude?: number | null
    longitude?: number | null,
    iconUrl?: string
}
const props = defineProps(['id'])
/** 预计到达时间 */
const arrivalTime = ref('')
const showConfirm = ref(false)
const wayDatas = ref<IAddress[]>(Array.from({ length: 2 }, () => ({ uuid: uuidv4(), name: '' })))
const location = ref({ longitude: 0, latitude: 0 })
const allPolyline = ref<any[]>([])
const wayItem = ref<CommuteRouteInfo>()
const allMarkers = ref<any[]>([])
const scale = ref(16)
const busMapUtil = useWmap()
const message = useMessage()
const loading = useLoading()
/** 模糊提示词 */
const waySearchResult = ref<IAddress[]>([])
let isEdit = false
const del = () => {
    commuteService.deleteCommuteRoute(props.id).then(_ => {
        showConfirm.value = false
        uni.navigateBack()
    })
}
onShow(() => {
    if (props.id) {
        isEdit = true
        nextTick(() => {
            getInfo()
        })
    } else {
        isEdit = false
    }
})
const cancel = () => {
    uni.navigateBack()
}
const save = async () => {
    const validate = validateStore()
    if (!validate) return
    const datas = toValue(wayDatas);
    let params: CommuteRouteInfo | null = null;
    if (isEdit && toValue(wayItem) != null) {
        params = toValue(wayItem)!
    } else {
        params = {
            routeType: 0, // 新增路线不区分上下班类型，默认传0
            startStopName: '',
            endStopName: '',
            stopList: []
        }
    }
    if (params != null) {
        params.startStopName = datas[0].name;
        params.endStopName = datas[datas.length - 1].name;
        params.stopList = datas.map((ele, idx) => {
            return {
                sequence: idx,
                stopName: ele.name,
                lat: ele.latitude!,
                lng: ele.longitude!
            }
        })
        await commuteService.saveCommuteRoute(params)
        uni.navigateBack()
    }
}
const getInfo = async () => {
    if (!props.id) return;
    const res = await commuteService.getInfoCommuteRoute(props.id)
    wayItem.value = res
    wayDatas.value = res?.stopList?.map(ele => {
        return {
            id: ele.id!,
            uuid: uuidv4(),
            name: ele.stopName,
            latitude: ele.lat,
            longitude: ele.lng
        }
    })
    location.value.latitude = res.stopList[0].lat
    location.value.longitude = res.stopList[0].lng
    scale.value = 18
    updateDraw()
}
/** 添加途经点 */
const onAddVia = () => {
    // 往倒数第二个位置的插入新数据
    const newVia = { uuid: uuidv4(), name: '' }
    const index = Math.max(0, wayDatas.value.length - 1); // 防止数组为空的情况
    wayDatas.value.splice(index, 0, newVia);
}

const validateStore = (): boolean => {
    const datas = toValue(wayDatas);
    if (!datas.length || datas.length < 1) {
        message.show('请添加途径点')
        return false
    }
    if (datas[0].name == '') {
        message.show('起点不能为空')
        return false
    } else if (datas[datas.length - 1].name == '') {
        message.show('终点不能为空')
        return false
    }
    if (datas.some(ele => ele.name == '')) {
        message.show('途径点名称不能为空')
        return false
    }
    return true
}

const getInputtips = (keyword: string) => {
    waySearchResult.value = []
    if (!keyword) return;
    loading.showLoading()
    busMapUtil.getInputtips(keyword).then(res => {
        console.log("🚀 ~ WayHandle.vue ~ busMapUtil.getInputtips ~ res:", res)
        if (res == null) {
            return
        }
        const data = res as IAddressTips | null
        waySearchResult.value = data?.tips?.filter(ele => ele.location && typeof ele.location == 'string')?.map(ele => {
            const [longitude = 0, latitude = 0] = ele.location?.split(',').map(Number) || []
            let address = '';
            if (ele.address) { // ele.address => arr/string
                address = typeof ele.address === 'string' ? ele.address : Array.isArray(ele.address) ? (ele.address as string[]).join('') : ''
            }
            return {
                name: ele.name,
                address: ele.district + address,
                location: ele.location,
                latitude,
                longitude,
                uuid: uuidv4()
            }
        }) || []
    }).finally(() => {
        loading.hideLoading()
    })

}
// const mockRoute = async () => {
//     const stores = waySearchResult.value.map(ele => {
//         return {
//             id: ele.uuid,
//             name: ele.name,
//             longitude: ele.longitude!,
//             latitude: ele.latitude!,
//             _init: ele
//         }
//     })
//     const nearestStoreItem = await busMapUtil.getNearestStation<IAddress>(stores)
//     if (nearestStoreItem) {
//         const startItem = waySearchResult.value.find(ele => ele.name != nearestStoreItem.name)
//         if (startItem) {
//             const [startLongitude, startLatitude] = startItem.location?.split(',').map(Number) || []
//             const [endLongitude, endLatitude] = nearestStoreItem.location?.split(',').map(Number) || []
//             const nearestData = await busMapUtil.getDrivingMsgForTime([{ lat: startLatitude, lng: startLongitude }, { lat: endLatitude, lng: endLongitude }])
//         }
//     }
// }
const currentSearchItem = ref<IAddress | null>(null)
const handleSelect = (item: IAddress) => {
    if (currentSearchItem.value) {
        currentSearchItem.value = Object.assign(currentSearchItem.value, item)
        location.value.latitude = item.latitude!
        location.value.longitude = item.longitude!
        waySearchResult.value = []
        scale.value = 18
        updateDraw()
    }
}
const handleNameChange = useDebounce(($event: string, item: IAddress) => {
    currentSearchItem.value = item
    if ($event) {

        $event && getInputtips($event)
    } else {
        item.name = $event
        item.latitude = null
        item.longitude = null
        item.location = ''
        updateDraw();
    }
}, 500)
const updateDraw = async () => {
    const routes = toValue(wayDatas)?.filter(ele => ele.latitude && ele.longitude)?.map(ele => ({
        id: ele.id || uuidv4(),
        ...ele
    }) as any) || []
    const drawData = await busMapUtil.drawRoute(routes)
    allPolyline.value = drawData?.line || []
    allMarkers.value = drawData?.markers || []
    const nearestData = await busMapUtil.getDrivingMsgForTime(toValue(wayDatas)?.map(ele => (
        {
            lat: ele.latitude!,
            lng: ele.longitude!
        }
    )) || [])
    arrivalTime.value = nearestData?.duration || ''
}
const onDeleteItem = (index: number) => {
    wayDatas.value.splice(index, 1)
    updateDraw()
}
// #region 拖动
// 拖动状态：当前拖动项索引、初始触摸坐标
let startIndex = -1
let startY = 0

/**
 * 触摸开始：记录初始信息
 * @param {TouchEvent} e 触摸事件对象
 * @param {number} index 当前项索引
 */
function handleTouchStart(e: TouchEvent, index: number) {
    startIndex = index
    // 取第一个触摸点的 Y 坐标
    startY = e.touches[0].clientY
}

/**
 * 触摸移动：计算偏移、交换数组项
 * @param {TouchEvent} e 触摸事件对象
 * @param {number} currentIndex 当前项索引
 */
function handleTouchMove(e: TouchEvent, currentIndex: number) {
    if (startIndex === -1) return // 未开始拖动，直接返回
    const moveY = e.touches[0].clientY
    const diffY = moveY - startY
    // 简单判断：Y 轴偏移超过一定阈值（如 30px）再触发交换，避免误操作
    if (Math.abs(diffY) < 30) return

    // 计算目标索引（向上/向下拖动时交换的位置）
    const targetIndex = currentIndex + (diffY > 0 ? 1 : -1)
    // 边界判断：防止越界
    if (targetIndex < 0 || targetIndex >= wayDatas.value.length) return

        // 交换数组项（核心：模拟“拖动”视觉）
        ;[wayDatas.value[currentIndex], wayDatas.value[targetIndex]] = [wayDatas.value[targetIndex], wayDatas.value[currentIndex]]
    // 更新初始索引和触摸位置，持续拖动时保持逻辑
    startIndex = targetIndex
    startY = moveY
}

/**
 * 触摸结束：重置状态
 */
function handleTouchEnd() {
    startIndex = -1
    startY = 0
}
// #endregion
</script>

<style scoped lang="scss">
.way-handle-wrapper {
    width: 100%;
    background: #fdfdfd;
    padding-bottom: 90px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.content {
    padding: 15px;
    position: relative;
    max-height: 60%;
}

.content_control {
    border-radius: 3px;
    background: #F8F8F8;
    box-sizing: border-box;
    border: 1px solid #F0F0F0;

    .item {
        height: 32px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        gap: 8px;

        .item-input {
            flex: 1;
        }
    }
}

.content_add {
    display: flex;
    width: 100%;
    justify-content: space-between;
    margin-top: 10px;

    &-left {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #4f7af6;
    }
}

.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px 10px;
    border-radius: 10px 10px 0px 0px;
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0px 0px 5px 0px rgba(146, 146, 146, 0.2);
}

.text-box {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    padding: 11px 15px 30px;
    background-color: #fff;
    z-index: 2;

    .text-top {
        min-height: 31px;
        display: flex;
        // align-items: center;
        gap: 5px;
        width: 100%;

        .text-name {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;

            .name {
                color: #333;

                &.active {
                    color: #4F7AF6;
                }
            }

            .name-info {
                color: #666;
                font-size: 12px;
            }
        }
    }

    .text-bottom {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        width: 100%;
        padding-left: 10px;
    }

    .text-bottom-item {
        min-width: 130px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 20px;
        background-color: #fff;
        border: 1px solid rgb(202, 202, 202);

        &:hover {
            background-color: rgb(202, 202, 202, 0.2);
        }
    }

    .text-item {
        padding: 10px 0;
        display: flex;
        flex-direction: column;
        gap: 5px;
        border-bottom: 1px solid rgb(153, 153, 153, 0.2);
    }
}

.map {
    flex: 1;
    min-height: 40%;
    margin-bottom: -20px;
}

view {
    box-sizing: border-box;
}
</style>