<template>
    <div class="holiday-wrapper">
        <div class="content">
            <DatePicker ref="datePickerRef" @db-change-item="onDateItem" :cache-date="cacheData"
                :is-pure-mode="false" />
        </div>
        <div class="footer">
            <BaseButton btnType="cancel" :disabled="!cacheData.size" @click="reset">恢复默认配置</BaseButton>
            <!-- 批量保存 -->
            <BaseButton btnType="save" :disabled="!cacheData.size" @click="save" />
        </div>
    </div>
    <div class="overlay" v-if="currentInfo?.date">
        <div class="custom-date">
            <div style="color: #333;">将所选日期调整为：</div>
            <div class="custom-date_content">
                <div class="custom-date_content-item" v-for="{ label, value } of options" :key="label"
                    @click.stop="currentKey = value">
                    <SlSubSvgIcon subpage="config-manager" :name="'20-20-' + (currentKey == value ? '14' : '13')"
                        size="20" />
                    <span>
                        {{ label }}
                    </span>
                </div>
            </div>
            <!-- 调整单个日期按钮 -->
            <div class="footer">
                <BaseButton size="small" btnType="cancel" @click="currentInfo = null" />
                <!-- 不改变之前按钮可以是灰不可点击状态 -->
                <BaseButton size="small" btnType="save" :disabled="currentInfo.manualType == currentKey"
                    @click="saveCustomDate" />
            </div>
        </div>
    </div>
    <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="onConfirmReset">您确定要恢复默认配置吗？
    </ConfirmDialog>
</template>

<script setup lang="ts">
import { DateType, IDate } from '@/models/Date'
import DatePicker from '@/components/DatePicker.vue'
import useMessage from '@/hooks/use-message'
import { cloneDeep } from '@/utils/clone'
import calendarService from '@/service/calendar/calendar.service'
const options: { label: string, value: DateType }[] = [
    { label: '工作日', value: DateType.WorkDay },
    { label: '非工作日', value: DateType.NonWorkDay },
    { label: '节假日', value: DateType.Holiday },
    { label: '补班日', value: DateType.Substitute },
]
const currentKey = ref<DateType>()
const currentInfo = ref<IDate | null>()
const showConfirm = ref(false)
const datePickerRef = ref<InstanceType<typeof DatePicker>>()
/** 暂存的已经更改的日期数据 */
const cacheData = ref<Map<string, IDate>>(new Map())
const reset = () => {
    showConfirm.value = true
}
/** 批量更改日期 */
const save = async () => {
    const params = Array.from(toValue(cacheData).values()) || []
    if (params?.length) {
        await calendarService.saveList(params)
        cacheData.value = new Map()
        toValue(datePickerRef)?.resetDate();
    }
}
/** 手动更改日期类型 */
const saveCustomDate = () => {
    const currentData = toValue(currentInfo)
    if (!currentData) return;
    const { date } = currentData;
    cacheData.value?.delete(date);
    cacheData.value?.set(date, { ...currentData, manualType: toValue(currentKey) ?? null });
    currentInfo.value = null
}
const onDateItem = (item: { date: string, id: string, initData?: IDate }) => {
    currentInfo.value = item.initData
    currentKey.value = item.initData?.type
}
const message = useMessage();

const onConfirmReset = async () => {
    let _data = cloneDeep(cacheData.value)
    try {
        cacheData.value = new Map()
        await toValue(datePickerRef)?.resetDate();
        message.show('恢复配置成功')
    } catch (error) {
        cacheData.value = _data || new Map() // 恢复配置
        message.show('恢复配置失败，请重试~')
    } finally {
        showConfirm.value = false
    }
}
</script>

<style scoped lang="scss">
.holiday-wrapper {
    padding: 10px 15px;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.content {
    flex: 1;
}

.footer {
    display: flex;
    gap: 10px;
}

.overlay {
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
    background-color: rgba(51, 51, 51, 0.6);

    .custom-date {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 290px;
        height: 252px;
        border-radius: 10px;
        background: #FFFFFF;
        box-shadow: 0px 0px 10px 0px rgba(146, 146, 146, 0.3);
        padding: 15px;
        display: flex;
        flex-direction: column;

        &_content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 10px;
        }
    }

    .custom-date_content-item {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-left: 35%;
    }
}

view {
    box-sizing: border-box;
}
</style>