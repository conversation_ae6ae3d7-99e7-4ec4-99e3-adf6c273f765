<template>
    <div class="holiday-wrapper">
        <div class="content">
            <HolidayCalendar @changeItem="onDateItem" />
        </div>
        <div class="footer">
            <BaseButton btnType="cancel" @click="cancel" />
            <BaseButton btnType="save" @click="save" />
        </div>
    </div>
    <div class="overlay" v-if="currentDate">
        <div class="custom-date">
            <div style="color: #333;">将所选日期调整为：</div>
            <div class="custom-date_content">
                <div class="custom-date_content-item" v-for="{ label, value } of options" :key="label"
                    @click.stop="currentKey = value">
                    <SlSVgIcon :name="'20-20-' + (currentKey == value ? '14' : '13')" size="20" />
                    <span>
                        {{ label }}
                    </span>
                </div>
            </div>
            <div class="footer">
                <BaseButton size="small" btnType="cancel" @click="currentDate = ''" />
                <BaseButton size="small" btnType="save" @click="saveCustomDate" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import HolidayCalendar from './HolidayCalendar.vue'
const options = [
    { label: '工作日', value: 0 },
    { label: '非工作日', value: 1 },
    { label: '节假日', value: 2 },
    { label: '不办理', value: 3 },
]
const currentKey = ref(0)
const currentDate = ref('')
const cancel = () => { }
const save = () => { }
const saveCustomDate = () => { }
const onDateItem = (item: { date: string, id: string }) => {
    console.log("🚀 ~ index.vue ~ onDateItem ~ item:", item)
    currentDate.value = item.date || ''
}
</script>

<style scoped lang="scss">
.holiday-wrapper {
    padding: 10px 15px;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.content {
    flex: 1;
}

.footer {
    display: flex;
    gap: 10px;
}

.overlay {
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
    background-color: rgba(51, 51, 51, 0.6);

    .custom-date {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 290px;
        height: 252px;
        border-radius: 10px;
        background: #FFFFFF;
        box-shadow: 0px 0px 10px 0px rgba(146, 146, 146, 0.3);
        padding: 15px;
        display: flex;
        flex-direction: column;

        &_content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 10px;
        }
    }

    .custom-date_content-item {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-left: 35%;
    }
}
</style>