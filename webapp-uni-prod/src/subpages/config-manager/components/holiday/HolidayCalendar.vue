<template>
    <div class="calendar">
        <div class="calendar-header">
            <SlSVgIcon class="arrow" name="14-14-5" size="14" @click="changeMonth(-1)" />
            <div class="header-content">
                <span class="year">{{ year }}年</span>
                <span class="month">{{ month }}月</span>
            </div>
            <SlSVgIcon class="arrow" name="14-14-7" size="14" @click="changeMonth(1)" />
        </div>
        <div class="calendar-content">
            <div class="week" v-for="week of weekDays" :key="week.label">
                {{ week.label }}
            </div>
            <div v-for="item of getAllDays" :key="item.uuid" :class="['date', {
                'date-prefix': item.isPrefixMonth,
                'date-next': item.isNextMonth,
                'date-today': item.isToday,
                'date-holiday': item.isHoliday,
                'date-work': item.work,
            }]" @click="handleClick(item)">
                <!-- v-double-click="handleDateClick(item)" -->
                <!-- @dblclick.stop="handleDateClick(item)" @click.stop="handleDateClick2(item)" -->
                <span class="date-text">
                    {{ item.dayStr }}
                </span>
                <span class="date-text_lunar">
                    {{ item.dayLunar || '初一' }}
                </span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
interface RowItem {
    id?: string
    uuid?: string,
    content?: string,
    /** 时间戳 */
    date?: dayjs.Dayjs,
    dateMs?: number,
    /** 月/日 */
    dayStr?: string,
    /** 农历 */
    dayLunar?: string
    isToday?: boolean,
    /** 周X */
    dayOfWeek?: string,
    /** 是否是节假日  */
    isHoliday?: boolean,
    /** 是否是调休日  */
    // isInLieu: boolean
    /** 是否是被调休的工作日 */
    work?: boolean;
    isPrefixMonth?: boolean
    isNextMonth?: boolean
    /** 完整日期 YYYY-MM-DD */
    dateStr?: string
}
import { uuidv4 } from '@/utils/uuid';
import { isHoliday, isInLieu, isWorkday } from 'chinese-days';
import dayjs from 'dayjs';
import { computed, ref, toValue, watchPostEffect } from 'vue';
const emit = defineEmits(['changeItem'])
const year = ref(new Date().getFullYear())
const month = ref(new Date().getMonth() + 1)
/** 上个月的末尾天数，用于填补本月初始的空白，也可能无值 */
const daysInMonth = ref<RowItem[]>([])
/** 本月天数 */
const daysInThisMonth = ref<RowItem[]>([])
/** 下个月天数（末尾） */
const daysInNextMonth = ref<RowItem[]>([])
const currentItem = ref<RowItem | null>(null)
const weekDays = [{
    label: '一',
    value: 1
},
{
    label: '二',
    value: 2
},
{
    label: '三',
    value: 3
},
{
    label: '四',
    value: 4
},
{
    label: '五',
    value: 5
},
{
    label: '六',
    value: 6
},
{
    label: '日',
    value: 0
},
]
watchPostEffect(() => {
    if (toValue(year) || toValue(month)) {
        const prefixFirstDay = new Date(toValue(year), toValue(month) - 1, 1);
        const prefixOffset = prefixFirstDay.getDay() === 0 ? 6 : prefixFirstDay.getDay() - 1;
        // 上个月天数
        daysInMonth.value = Array.from({ length: prefixOffset }, (_, i) => {
            const date = new Date(prefixFirstDay)
            date.setDate(date.getDate() - prefixOffset + i)
            return formatDateItem(date, 'prefix')

        });
        // 这个月天数
        daysInThisMonth.value = Array.from({ length: new Date(toValue(year), toValue(month), 0).getDate() }, (_, i) => {
            const date = new Date(toValue(year), toValue(month) - 1, i + 1)
            return formatDateItem(date)
        })
        // 下个月天数
        daysInNextMonth.value = Array.from({ length: 42 - daysInMonth.value.length - daysInThisMonth.value.length }, (_, i) => {
            const date = new Date(toValue(year), toValue(month), i + 1)
            return formatDateItem(date, 'next')
        })
        // console.log("🚀 ~ 上个月的末尾天:", daysInMonth.value)
        // console.log("🚀 ~ 本月天:", daysInThisMonth.value)
        // console.log("🚀 ~ 下个月天:", daysInNextMonth.value)
    }
})
const getAllDays = computed(() => [...daysInMonth.value, ...daysInThisMonth.value, ...daysInNextMonth.value])
/** 月改变 */
const changeMonth = (step: number) => {
    month.value += step;
    if (month.value > 12) {
        year.value = year.value + 1
        month.value = 1
    }
    if (month.value < 1) {
        year.value = year.value - 1
        month.value = 12
    }
}
const formatDateItem = (dateDate: Date, type: 'prefix' | 'current' | 'next' = 'current') => {
    const isWeekend = dateDate.getDay() === 0 || dateDate.getDay() === 6;
    const dateMs = dateDate.valueOf();
    const dayOfWeek = weekDays.find((item) => item.value === dateDate.getDay())?.label;
    return <RowItem>{
        uuid: uuidv4(),
        date: dayjs(dateDate),
        dateMs,
        dayStr: `${dateDate.getDate()}`,
        dayOfWeek,
        isHoliday: (!isWeekend && isHoliday(dateMs)) || (isWeekend && isInLieu(dateMs)),
        work: isInLieu(dateMs) && isWorkday(dateMs),
        isPrefixMonth: type === 'prefix',
        isCurrentMonth: type === 'current',
        isNextMonth: type === 'next',
        isToday: dayjs(dateDate).isSame(dayjs(), 'day'),
        dateStr: `${dateDate.getFullYear()}-${dateDate.getMonth() + 1}-${dateDate.getDate()}`,
    }
}

let lastClickTime = 0
let clickCount = 0
const DOUBLE_CLICK_INTERVAL = 300 // 双击间隔时间(ms)
const handleClick = (item: RowItem) => {
    const now = Date.now()
    const delta = now - lastClickTime
    if (delta < DOUBLE_CLICK_INTERVAL) {
        clickCount++
        if (clickCount === 2) {
            currentItem.value = item
            emit('changeItem', { date: item.dateStr, id: item.id || item.uuid })
            // 重置状态
            clickCount = 0
            lastClickTime = 0
            return
        }
    } else {
        // 单击或首次点击
        clickCount = 1
    }

    lastClickTime = now
}
</script>

<style scoped lang="scss">
.calendar-header {
    display: flex;
    width: 100%;
    height: 40px;
    align-items: center;
    padding: 0 20px;
    justify-content: space-between;
}

.header-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    .year {
        color: #333;
    }

    .month {
        margin-left: 4px;
        color: #0066df;
    }
}

.calendar-content {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    grid-template-rows: repeat(6, 32px);
    justify-items: center;
    gap: 10px;
}

.arrow:hover {
    color: #4F7AF6;
}

.date {
    width: 32px;
    height: 32px;
    border-radius: 3px;
    color: #333;
    cursor: pointer;
    background-color: #f6f8fa;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    gap: 2px;
}

.date-text {
    color: #333;
    font-size: 14px;
}

.date-text_lunar {
    color: #666;
    font-size: 8px;
}

.date:hover,
.date-active {
    background-color: #005CC8;
    color: #fff;

    .date-text,
    .date-text_lunar {
        color: #fff;
    }
}

.date-prefix,
.date-next {
    background-color: #E4E9ED;
}

.date-today {
    background-color: rgba(0, 92, 200, 0.15);
}

.date-work,
.date-holiday {
    position: relative;

    &::after {
        position: absolute;
        border-radius: 50%;
        right: 0;
        top: 0;
        font-size: 0.8rem;
        padding: 1px;
    }
}

.date-holiday::after {
    content: '休';
    color: #E50101;
    background-color: #FFC4C4;
}

.date-work::after {
    content: '班';
    background-color: #4E5877;
    color: #fff;
}
</style>