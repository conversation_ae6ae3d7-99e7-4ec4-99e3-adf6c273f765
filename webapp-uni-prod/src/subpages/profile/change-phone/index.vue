<template>
    <view class="change-phone-page">
        <view class="img-container">
            <image class="phone-img" src="../static/phone.svg" mode="aspectFill" />
        </view>
        <view class="phone-container">
            <text class="phone-label">
                您的手机号码：
            </text>
            <text class="phone-value">
                {{ account?.phone || '未绑定' }}
            </text>
        </view>
        <view class="action-button" @tap="goChangePhone">
            <text class="button-text">更换手机号码</text>
        </view>
        <view class="tip">
            <text class="tip-text">更换手机号码后登录和人事通讯录手机号码均改变</text>
        </view>
    </view>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { usePrincipalStore } from '@/store';
import { Account } from '@/models/Account';
const account = ref<Account | undefined>();
onLoad(async () => {
    // 在页面加载时获取用户信息
    const principalStore = usePrincipalStore();
    account.value = await principalStore.identity();
});

const goChangePhone = () => {
    uni.navigateTo({
        url: '/subpages/login/pages/phone-login/index?type=change_phone'
    });
};

</script>
<style lang="scss" scoped>
.change-phone-page {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #F4F6FA;
    box-sizing: border-box;
    padding: 0 15px;

    .img-container {
        width: 147px;
        height: 147px;
        margin: 100px auto 0 auto;

        .phone-img {
            width: 100%;
            height: 100%;
        }
    }

    .phone-container {
        width: 100%;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;

        .phone-label {
            font-size: 14px;
            color: #999;
        }

        .phone-value {
            font-size: 22px;
            font-weight: medium;
            color: #333;
        }
    }

    .action-button {
        height: 40px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #0066DF;
        border-radius: 3px;

        .button-text {
            color: #FFFFFF;
        }
    }

    .tip {
        width: 100%;
        display: flex;
        justify-content: center;

        .tip-text {
            font-size: 12px;
            color: #999;
            margin-top: 8px;
        }
    }


}
</style>