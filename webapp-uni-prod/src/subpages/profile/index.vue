<template>
    <view class="profile-page">
        <view class="user-info">
            <view class="item">
                <text class="label"> 姓名 </text>
                <text class="value"> {{ account.name }} </text>
            </view>
            <view class="item">
                <text class="label"> 单位 </text>
                <text class="value"> 天津海事局 </text>
            </view>
            <view class="item">
                <text class="label"> 部门 </text>
                <text class="value"> {{ account.organization?.name }} </text>
            </view>
            <view class="item">
                <text class="label"> 职务 </text>
                <text class="value"> {{ account.position }} </text>
            </view>
            <view class="item">
                <text class="label"> 手机号码 </text>
                <div class="action" @tap="changePhone" :class="{ 'work-group': isWorkGroup }">
                    <text class="value">
                        {{ account.phone }}
                    </text>
                    <SlSubSvgIcon v-if="!isWorkGroup" class="inline-flex" subpage="profile" name="12-12-6" size="12" />
                </div>
            </view>
        </view>
    </view>
</template>
<script lang="ts" setup>
import { Account } from '@/models/Account';
import { usePrincipalStore } from '@/store';
const principalStore = usePrincipalStore();
const account = ref<Account>({
} as Account);
onLoad(async () => {
    // 在页面加载时获取用户信息
    const _account = await principalStore.identity()
    if (_account) {
        account.value = _account;
    }
});

// 判断是否为工作组，workgroupList 存在且长度大于0
const isWorkGroup = computed(() => {
    return account.value?.workgroupList && account.value.workgroupList.length > 0;
});

const changePhone = () => {
    if (isWorkGroup.value) {
        return;
    }
    uni.navigateTo({
        url: '/subpages/profile/change-phone/index'
    });
};
</script>

<style lang="scss" scoped>
.profile-page {
    background-color: #F4F6FA;
    height: 100%;
    width: 100%;

    .user-info {
        background: #ffffff;
        width: 100%;
        display: flex;
        flex-direction: column;

        .item {
            height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;

            .label,
            .value {
                font-size: 14px;
            }

            .label {
                color: #666666;
            }

            .action {
                display: flex;
                align-items: center;

                .inline-flex {
                    display: inline-flex;
                }

                &:not(.work-group) {
                    .value {
                        margin-right: 10px;
                    }
                }
            }
        }
    }
}
</style>