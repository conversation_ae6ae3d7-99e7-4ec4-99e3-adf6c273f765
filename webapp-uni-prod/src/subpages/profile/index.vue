<template>
    <view class="profile-page">
        <view class="user-info">
            <view class="item">
                <text class="label"> 姓名 </text>
                <text class="value"> 张三 </text>
            </view>
            <view class="item">
                <text class="label"> 单位 </text>
                <text class="value"> 研发部 </text>
            </view>
            <view class="item">
                <text class="label"> 部门 </text>
                <text class="value"> 研发部 </text>
            </view>
            <view class="item">
                <text class="label"> 职务 </text>
                <text class="value"> 研发部 </text>
            </view>
            <view class="item">
                <text class="label"> 手机号码 </text>
                <text class="value">
                    138****1234
                </text>
            </view>
        </view>
    </view>
</template>
<script lang="ts" setup></script>

<style lang="scss" scoped>
.profile-page {
    background-color: #F4F6FA;
    height: 100%;
    width: 100%;

    .user-info {
        background: #ffffff;
        width: 100%;
        display: flex;
        flex-direction: column;

        .item {
            height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;

            .label,
            .value {
                font-size: 14px;
            }

            .label {
                color: #666666;
            }
        }
    }
}
</style>