<template>
    <div class="wrapper" v-if="!drivingCarId">
        <div class="content">
            <div class="content-item">
                <div class="content_header">
                    请选择您驾驶的车辆
                </div>
                <div class="content_select">
                    <div class="car-item" v-for="car of carOptions" :key="car.id" @click=" onClickCarItem(car.id)">
                        <div class="shuttle_icon">
                            <SlSubSvgIcon subpage="commute-manager" name="54-54-1" size="54" />
                            <div class="shuttle_icon-text">{{ car.name }}</div>
                        </div>
                        <SlSubSvgIcon subpage="commute-manager" class="car-select"
                            :name="'14-14-' + (checkedData.car == car.id ? '14' : '15')" size="14" />
                    </div>
                    <BaseEmpty style="width: 100%;text-align: center;" v-if="!carOptions.length" />
                </div>
            </div>
            <div class="content-item">
                <div class="content_header">
                    请选择您行走的路线
                </div>
                <div class="content_select way">
                    <div class="way-item" :class="{ 'active': checkedData.way == way.id }" v-for="way of wayOptions"
                        :key="way.id" @click="onClickWayItem(way.id)">
                        <div class="way-item_start">
                            <SlSubSvgIcon subpage="commute-manager" style="display: inline-flex;"
                                :name="'14-14-' + (checkedData.way == way.id ? '14' : '15')" size="14" />
                            {{ way.startWay }}
                        </div>
                        <SlSubSvgIcon subpage="commute-manager" style="display: inline-flex;"
                            :name="checkedData.way == way.id ? 'commute-arrow-active' : 'commute-arrow-unactive'"
                            size="23" />
                        {{ way.endWay }}
                    </div>
                    <BaseEmpty v-if="!wayOptions.length" />
                </div>
            </div>
        </div>
        <div class="footer">
            <BaseButton btn-type="save" size="large" :disabled="!checkedData.car || !checkedData.way"
                @click="showConfirm = true">
                发车</BaseButton>
        </div>
    </div>
    <ShuttleDepartureInfo v-else v-model:id="drivingCarId" />
    <ConfirmDialog v-if="showConfirm" @close="showConfirm = false" @confirm="onConfirm" cancelText="关闭">您确定要发车吗？
    </ConfirmDialog>
</template>

<script setup lang="ts">
import { CommuteRouteType } from '@/models/Commute';
import commuteService from '@/service/commute/commute.service';
import { usePrincipalStore } from '@/store';
import ShuttleDepartureInfo from './ShuttleDepartureInfo.vue';
const showConfirm = ref(false)
/** 当前用户行程中carId */
const drivingCarId = ref('')
interface CarItem {
    id: string;
    name: string
}
interface WayItem {
    id: string;
    startWay: string;
    endWay: string
}

const carOptions = ref<CarItem[]>([])
const wayOptions = ref<WayItem[]>([])
/** 已勾选的车辆路线，均为单选 */
const checkedData = reactive<{ car: string; way: string }>({
    car: '',
    way: ''
})
const { account } = storeToRefs(usePrincipalStore())
onShow(() => {
    // getList()
    getDrivingInfo()
})
/** 选中路线 */
const onClickWayItem = (id: string) => {
    checkedData.way = checkedData.way == id ? '' : id
}
/** 选中车辆 */
const onClickCarItem = (id: string) => {
    checkedData.car = checkedData.car == id ? '' : id
}
const onConfirm = () => {
    if (!checkedData.car || !checkedData.way) return;
    showConfirm.value = false
    start()
}
watch(drivingCarId, (val, newVal) => {
    if (!val && newVal) {
        checkedData.car = ''
        checkedData.way = ''
        getList()
    }
})
/** 查看用户当前行驶中的数据 */
const getDrivingInfo = async () => {
    const routeType = getCurRouteTypeByTime()
    const res = await commuteService.getList({ userId: '', driverId: toValue(account)!.id, status: 1, routeType })
    if (res?.length) {
        drivingCarId.value = res[0].carId
        // drivingInfo.value = res[0] // 司机端，当前行驶中的数据最多只有一条
    } else {
        drivingCarId.value = ''
        getList()
    }
}
/** 开始发车 */
const start = async () => {
    const routeType = getCurRouteTypeByTime()
    await commuteService.updateStatus({
        driverId: toValue(account)!.id,
        carId: checkedData.car,
        routeId: checkedData.way,
        status: 1,
        routeType
    })
    drivingCarId.value = checkedData.car
    // const url = `/subpages/commute-manager/shuttle-departure/ShuttleDepartureInfo?id=${id}`
    // uni.navigateTo({ url })
}
const getList = () => {
    const routeType = getCurRouteTypeByTime()
    commuteService.getListCommuteVehicle().then(res => {
        carOptions.value = res.map(e => ({ id: e.id!, name: e.carName }))
    })
    // 路线：中午12点之前显示上班的路线，12点以后显示下班的线路
    commuteService.getListCommuteRoute(routeType).then(res => {
        wayOptions.value = res.map(e => ({ id: e.id!, startWay: e.startStopName, endWay: e.endStopName }))
    })
}
const getCurRouteTypeByTime = (): CommuteRouteType => {
    const currentTime = new Date().getHours();
    const isAfternoon = currentTime >= 12;
    return isAfternoon ? 1 : 0  // 路线类型 0：上班，1：下班，默认传0
    // return 0
}
</script>

<style scoped lang="scss">
.wrapper {
    width: 100%;
    height: 100%;
    padding: 8px;
    background-color: F4F6FA;
    display: flex;
    flex-direction: column;

    .content {
        flex: 1;
    }
}

.content_header {
    color: #333333;
    font-size: 14px;
    border-left: 2px solid #1A58B2;
    border-radius: 2px;
    padding-left: 6px;
    margin-left: 2%;
}

.content_select {
    padding: 4%;
    display: flex;
    flex-wrap: wrap;
    gap: 22px;

}

.way {
    flex-direction: column;
    gap: 12px;
}

// 车辆
.car-item {
    width: 72px;
    min-height: 60px;
    border-radius: 10px;
    background: #F0F7FF;
    border: 1px solid #E7F2FF;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-bottom: 5px;

    .car-select {
        position: absolute;
        top: -8px;
        right: 0;
    }
}

// 路线
.way-item {
    display: flex;
    align-items: center;
    border-radius: 10px;
    background: #F6FAFF;
    justify-content: space-around;
    gap: 6px;
    min-height: 34px;
    color: #333;

    &.active {
        background: #DFEEFF;
        border: 1px solid #A4CEFF;
        color: #005CC8;
    }

    .way-item_start {
        display: flex;
        align-items: center;
        gap: 4px;
    }
}

// 班车图标+车牌号
.shuttle_icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;

    &-text {
        border-radius: 15px;
        background: #FFFFFF;
        box-sizing: border-box;
        border: 1px solid #81BBFF;
        text-align: center;
        padding: 4px;
        font-size: 9px;
        font-weight: 500;
        color: #094895;
        // 贴近svg
        margin-top: -16px;
        width: 100%;
    }
}
</style>