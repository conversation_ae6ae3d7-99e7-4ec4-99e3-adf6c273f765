<template>
    <div class="wrapper">
        <div class="map">
            <CommuteMap />
        </div>
        <div class="content">
            <!-- 概要 -->
            <div class="content_summary">
                <div class="shuttle-msg">
                    <div class="shuttle_icon">
                        <SlSubSvgIcon subpage="commute-manager" name="54-54-1" size="54" />
                        <!-- <SlSVgIcon name="54-54-1" size="54" /> -->
                        <div class="shuttle_icon-text">{{ item.name }}</div>
                    </div>
                    <div class="way">
                        <div class="way-item">
                            <!-- <SlSVgIcon name="12-12-19" size="12" /> -->
                            <SlSubSvgIcon subpage="commute-manager" name="12-12-19" size="12" />
                            {{ item.startWay }}
                        </div>
                        <div class="way-item">
                            <SlSubSvgIcon subpage="commute-manager" name="12-12-19" size="18" />
                            <!-- <SlSVgIcon name="12-12-18" size="12" /> -->
                            {{ item.endWay }}
                        </div>
                    </div>
                    <div class="shuttle-right">
                        <div class="time-start">
                            发车 <span class="time">{{ item.startTime }}</span>
                        </div>
                    </div>
                </div>
                <div class="shuttle-tip">
                    <div>
                        当前停靠站
                        <span class="stop">
                            {{ item.tip }}
                        </span>
                    </div>
                </div>
            </div>
            <!-- 具体路线过程 -->
            <div class="way-process">
                <div class="process-title">
                    <div style="color: 666;">
                        全程预计2小时
                    </div>
                    <div class="right">
                        预计到达
                        <span class="time">
                            10:00
                        </span>
                    </div>
                </div>
                <div class="process-content" :class="{ 'collapse': isCollapse }">
                    <template v-for="(item, index) of processDatas" :key="item.id">
                        <!-- v-show="!isCollapse || index == 0 || index == processDatas.length - 1" v-show经转小程序后层级未优先 -->
                        <div :class="['process-content-item']"
                            :style="[!isCollapse || index == 0 || index == processDatas.length - 1 ? {} : { 'display': 'none' }]">
                            <div class="pointer" style="z-index: 2;">
                                <SlSubSvgIcon subpage="commute-manager" v-if="index == 0" name="12-12-19" size="12" />
                                <SlSubSvgIcon subpage="commute-manager" v-else-if="index == processDatas.length - 1"
                                    name="12-12-18" size="12" />
                                <!-- <SlSVgIcon v-if="index == 0" name="12-12-19" size="12" /> -->
                                <!-- <SlSVgIcon v-else-if="index == processDatas.length - 1" name="12-12-18" size="12" /> -->
                                <!-- 途经点 -->
                                <div v-else class="pointer-center"></div>
                            </div>
                            <div class="name">
                                <span>
                                    {{ item.name }}
                                </span>
                                <!-- 展开 -->
                                <template v-if="index == 0 && processDatas.length > 1">
                                    <div class="collapse" v-show="isCollapse">
                                        <div class="collapse-time">
                                            离终点站 · {{ time }}
                                        </div>
                                        <div class="collapse-btn" @click.stop="isCollapse = false">
                                            展开
                                            <SlSubSvgIcon subpage="commute-manager" name="8-8-1" size="8" />
                                            <!-- <SlSVgIcon name="8-8-1" size="8" /> -->
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>
                <!-- 收起按钮 -->
                <div class="un-collapse" v-if="!isCollapse" @click.stop="isCollapse = true">
                    收起
                    <SlSubSvgIcon subpage="commute-manager" name="8-8-2" size="8" />
                    <!-- <SlSVgIcon name="8-8-2" size="8" /> -->
                </div>
            </div>
            <div class="content_footer">
                <BaseButton :btnStyle="{
                    background: '#DF3600',
                    border: '1px solid #DF3600',
                    color: '#fff'
                }" size="large" @click="showConfirm = true">结束行程</BaseButton>
            </div>
        </div>
    </div>
    <ConfirmDialog v-show="showConfirm" @close="showConfirm = false" @confirm="onEndTrip" cancelText="关闭">您确定要结束行程吗？
    </ConfirmDialog>
</template>

<script setup lang="ts">
import { uuidv4 } from '@/utils/uuid'
import CommuteMap from '../commute-map/index.vue'
const showConfirm = ref(false)
/** 是否折叠详细路线信息 */
const isCollapse = ref(false)
/** 离终点站预计时间 */
const time = ref('1时25分')
const item = ref<any>({
    name: '深B·12345',
    startWay: '国务院',
    endWay: '伴景湾西门',
    tip: '石油大院',
    startTime: '10:00'
})
const processDatas = ref(Array.from({ length: 4 }, (_, i) => ({ id: uuidv4(), name: '站点' + i })))
const onEndTrip = () => {

}
</script>

<style scoped lang="scss">
.wrapper {
    width: 100%;
    height: 100%;
}

.map {
    min-height: 244px;
}

.content {
    flex: 1;
    border-radius: 10px 10px 0px 0px;
    background: linear-gradient(180deg, #E7F3FF 0%, #F3F5F9 100%);
    border: 1px solid #FFFFFF;
    box-shadow: 0px 0px 2px 0px #92929233;
    padding: 8px;
}

.content_footer {
    margin: 28px 7px;
    background-color: #fff;
}

.content_summary {
    width: 100%;
    min-height: 101px;
    border-radius: 4px;
    background: #FFFFFF;
    box-shadow: 0px 0px 2px 0px #9292924C;
    padding: 9px 13px;

}

.way-process {
    background-color: #fff;
    padding: 9px 13px;
    position: relative;
}

.shuttle-msg {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    height: 80px;

    .follow-start {
        position: absolute;
        top: 10px;
        right: 13px;
    }
}

.shuttle-tip {
    border-radius: 15px;
    background: #F7FBFF;
    height: 28px;
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 4px;
    justify-content: space-between;
    font-size: 12px;
    padding-left: 12px;
    color: #666;
}

// 班车图标+车牌号
.shuttle_icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;

    &-text {
        border-radius: 15px;
        background: #FFFFFF;
        box-sizing: border-box;
        border: 1px solid #81BBFF;
        text-align: center;
        padding: 4px;
        font-size: 9px;
        font-weight: 500;
        color: #094895;
        // 贴近svg
        margin-top: -16px;
    }
}

// 路线 起点至终点
.way {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
    position: relative;
    height: 100%;
    justify-content: space-around;

    &-item {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #333;
        z-index: 2;
    }

    &::before {
        position: absolute;
        left: 6px;
        top: 15px;
        height: 50%;
        width: 100%;
        content: '';
        border-left: 1px dashed #999;
    }
}

.stop {
    color: #333333;
    font-size: 14px;
    margin-left: 5px;
}


// 预计标题
.process-title {
    // height: 24px;
    padding: 5px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;

    .right {
        border-radius: 12px;
        background: #DFF5FF;
        border: 1px solid #A4E7FF;
        padding: 5px 6px;
        color: #1696c3;
    }
}

.time {
    font-size: 14px;
    font-weight: bold;
}

// 路线途径点
.process-content-item {
    width: 100%;
    // height: 55px;
    padding-bottom: 20px;
    display: flex;
    gap: 5px;
    position: relative;
    color: #666;

    &:not(:last-child) {
        &::before {
            position: absolute;
            content: '';
            width: 2px;
            height: 100%;
            top: 0.5rem;
            left: 5px;
            background-color: #e4effb;
            z-index: 1;
        }
    }

    &:last-child {
        height: 14px;
        padding-bottom: 0;
    }

    &:first-child,
    &:last-child {
        color: #333;
    }

    &.active {
        color: #005cc8;
    }
}

.pointer {
    width: 12px;
    text-align: center;
}

.pointer-center {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #D8D8D8;
    border: 1px solid #979797;
    margin-top: 0.5rem;
    margin-left: 0.2rem
}

// 收起
.un-collapse {
    font-size: 12px;
    color: #666;
    position: absolute;
    bottom: 14px;
    right: 20px;
}

// 展开
.collapse {
    margin-top: 10px;

    .collapse-time {
        color: #666;
        font-size: 12px;
    }

    .collapse-btn {
        display: flex;
        align-items: center;
        padding: 1px 4px;
        border-radius: 8px;
        background: #F0F0F0;
        font-size: 10px;
        color: #666;
        width: 40px;
        margin-top: 4px;
    }
}

.name {
    display: flex;
    flex-direction: column;
}

.shuttle-right {
    display: flex;
    align-items: flex-start;
    height: 100%;

    .time-start {
        border-radius: 12px;
        background: #DFEEFF;
        border: 1px solid #A4CEFF;
        min-width: 76px;
        padding: 5px 10px;
        font-size: 12px;
        color: #0058CB;
    }
}
</style>