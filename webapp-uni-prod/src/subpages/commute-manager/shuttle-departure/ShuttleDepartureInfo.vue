<template>
    <div class="wrapper">
        <div class="map">
            <CommuteMap v-model:location="location" :scale="scale" :polyline="allPolyline" :markers="getAllMarkers">
                <template #control-top-right>
                    <div class="next-stop-time" v-show="wayViewData.nextStopTime">
                        距离下一站剩余
                        <span class=" stop-time">{{ wayViewData.nextStopTime }}</span>
                        <SlSubSvgIcon class="stop-icon" subpage="commute-manager" name="6-6-1" size="6" />
                    </div>
                </template>
            </CommuteMap>
        </div>
        <div class="content">
            <!-- 概要 -->
            <div class="content_summary">
                <div class="shuttle-msg">
                    <div class="shuttle_icon">
                        <SlSubSvgIcon subpage="commute-manager" name="54-54-1" size="54" />
                        <div class="shuttle_icon-text">{{ wayViewData.name }}</div>
                    </div>
                    <div class="way">
                        <div class="way-item">
                            <SlSubSvgIcon subpage="commute-manager" name="12-12-19" size="12" />
                            {{ wayViewData.startWay }}
                        </div>
                        <div class="way-item">
                            <SlSubSvgIcon subpage="commute-manager" name="12-12-18" size="12" />
                            {{ wayViewData.endWay }}
                        </div>
                    </div>
                    <div class="shuttle-right">
                        <div class="time-start">
                            发车 <span class="time">{{ wayViewData.startTime }}</span>
                        </div>
                    </div>
                </div>
                <div class="shuttle-tip">
                    <div>
                        {{ wayViewData.stopTipTitle }}
                        <!-- 当前停靠站 -->
                        <span class="stop">
                            {{ wayViewData.stopTipName }}
                            <!-- {{ wayViewData.currentStop }} -->
                        </span>
                    </div>
                </div>
            </div>
            <!-- 具体路线过程 -->
            <div class="way-process">
                <div class="process-title">
                    <div style="color: 666;">
                        全程预计{{ wayViewData.duration }}
                    </div>
                    <div class="right">
                        预计到达
                        <span class="time">
                            {{ wayViewData.arriveTime }}
                        </span>
                    </div>
                </div>
                <div class="process-content" :class="{ 'collapse': isCollapse }">
                    <template v-for="(item, index) of processDatas" :key="item.id">
                        <div :class="['process-content-item', {
                            'process_active': currentStopIndex == index,
                            // 即将到达
                            'process_will-arrive': index > currentStopIndex,
                            // 已路过
                            'process_passed': index < currentStopIndex
                        }]"
                            :style="[!isCollapse || index == 0 || index == processDatas.length - 1 ? {} : { 'display': 'none' }]">
                            <div class="pointer" style="z-index: 2;">
                                <SlSubSvgIcon subpage="commute-manager" v-if="index == 0" name="16-16-34" size="16" />
                                <SlSubSvgIcon subpage="commute-manager" v-else-if="index == processDatas.length - 1"
                                    name="16-16-35" size="16" />
                                <!-- 途经点 -->
                                <div v-else class="pointer-center"></div>
                            </div>
                            <div class="name">
                                <span>
                                    {{ item.name }}
                                </span>
                                <!-- 展开 -->
                                <template v-if="index == 0 && processDatas.length > 1">
                                    <div class="collapse" v-show="isCollapse">
                                        <div class="collapse-time">
                                            离终点站 · {{ wayViewData.endTime }}
                                        </div>
                                        <div class="collapse-btn" @click.stop="isCollapse = false">
                                            展开
                                            <SlSubSvgIcon subpage="commute-manager" name="8-8-1" size="8" />
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>
                <!-- 收起按钮 -->
                <div class="un-collapse" v-if="!isCollapse" @click.stop="isCollapse = true">
                    收起
                    <SlSubSvgIcon subpage="commute-manager" name="8-8-2" size="8" />
                </div>
            </div>
            <div class="content_footer">
                <BaseButton :btnStyle="{
                    background: '#DF3600',
                    border: '1px solid #DF3600',
                    color: '#fff'
                }" size="large" @click="showConfirm = true">结束行程</BaseButton>
            </div>
        </div>
    </div>
    <ConfirmDialog v-show="showConfirm" @close="showConfirm = false" @confirm="onEndTrip" cancelText="关闭">您确定要结束行程吗？
    </ConfirmDialog>
</template>

<script setup lang="ts">
import { useLoading, useWmap } from '@/hooks'
import useMessage from '@/hooks/use-message'
import { CommuteVehicleInfo, ICommuteStopInfo } from '@/models/Commute'
import commuteService from '@/service/commute/commute.service'
import { usePrincipalStore } from '@/store'
import { uuidv4 } from '@/utils/uuid'
import { MapMarker, MapPolyline } from '@uni-helper/uni-app-types'
import dayjs from 'dayjs'
class WayData {
    name: string = '';
    startWay: string = '';
    endWay: string = '';
    tip: string = '';
    startTime: string = '';
    currentStop: string = '';
    /** 行程总时长 */
    duration: string = '';
    /** 预计到达 */
    arriveTime: string = '';
    /** 离终点站预计时间 */
    endTime: string = ''
    nextStopTime: string = ''
    /** 停靠站预告信息（当前停靠站/下一站） */
    stopTipTitle: string = ''
    /** 停靠站名 */
    stopTipName: string = ''
}
const props = defineProps(['id'])
const emit = defineEmits(['update:id'])
const showConfirm = ref(false)
/** 是否折叠详细路线信息 */
const isCollapse = ref(false)
const wayViewData = reactive<WayData>(new WayData())
const processDatas = ref<{ id: string; name: string }[]>([])
const detailCarInfo = ref<CommuteVehicleInfo>()
const location = ref<{ longitude: number, latitude: number }>()
const scale = ref(12)
const { account } = storeToRefs(usePrincipalStore())
const currentStopIndex = ref(0)
const allPolyline = ref<MapPolyline[]>([])
const busMapUtil = useWmap()
const allMarkers = ref<MapMarker[]>([])
const getAllMarkers = computed(() => [...toValue(allMarkers), ...toValue(busMapUtil.newShuttleMarkers)])
watch(busMapUtil.newShuttleMarkers, () => {
    getInfo(false)
}, { deep: true })
const loading = useLoading()
onMounted(async () => {
    await getInfo(true)
    // 只显示当前行程中的car
    busMapUtil.connectSocketBus(detailCarInfo.value?.id)
})
const getInfo = async (showLoading: boolean = false) => {
    if (!props.id) return;
    showLoading && loading.showLoading()
    try {
        const res = await commuteService.getVehicleDetail(props.id)
        detailCarInfo.value = res
        await updateView(detailCarInfo.value, showLoading)
    } catch (error) {
    } finally {
        showLoading && loading.hideLoading()
    }
}
const message = useMessage()
const onEndTrip = () => {
    // 结束行程
    commuteService.updateStatus({
        driverId: toValue(account)!.id,
        carId: toValue(detailCarInfo)?.carId || props.id,
        routeId: toValue(detailCarInfo)?.routeId,
        status: 0,
        routeType: toValue(detailCarInfo)?.commuteRoute.routeType
    }).then(_ => {
        message.show('行程结束成功')
        emit('update:id', '')
    }).catch(err => {
        message.show(err as string || '系统异常~')
    })
    // 返回上一页
}
const updateView = async (info: CommuteVehicleInfo, showLoading: boolean = false) => {
    const { commuteTrip, carName, commuteRoute } = info || {}
    const newData: WayData = new WayData()
    const currentStop = commuteTrip?.currentStop || null
    const nextStop = commuteTrip?.nextStop || null
    // 车牌号
    newData.name = carName || ''
    // 起点名称
    newData.startWay = commuteRoute?.startStopName || ''
    // 终点名称
    newData.endWay = commuteRoute?.endStopName || ''
    // 发车时间 只截取时分；
    if (commuteTrip?.startTime) {
        const timeStr = commuteTrip.startTime;
        const timePart = timeStr.split(' ')[1];
        const [hours, minutes] = timePart.split(':');
        newData.startTime = `${hours}:${minutes}`;
    }
    // 当前停靠站
    newData.currentStop = currentStop?.stopName || ''
    const allStopList = commuteRoute?.stopList || []
    currentStopIndex.value = allStopList.findIndex(s => s.id == currentStop?.id) || 0
    // 站点集合
    processDatas.value = allStopList.map(ele => {
        return {
            id: ele.id!,
            name: ele.stopName
        }
    }) || []
    drawRoute(commuteRoute.stopList || [])
    if (showLoading)
        nextTick(() => {
            location.value = {
                latitude: allStopList[0]?.lat,
                longitude: allStopList[0]?.lng
            }
        })
    /** 全程预计信息 */
    const allMsgData = await busMapUtil.getDrivingMsgForTime(allStopList.map(ele => ({
        lat: ele.lat,
        lng: ele.lng
    })) || [])
    const { duration, durationMs } = allMsgData || {}
    newData.duration = duration || ''
    if (durationMs && commuteTrip.startTime) {
        newData.arriveTime = dayjs(commuteTrip.startTime).add(durationMs, 'second').format('HH:mm')
    }

    if (toValue(busMapUtil.newShuttleDatas).length) {
        const currentShuttle = toValue(busMapUtil.newShuttleDatas).find(e => e.id == info.carId)
        if (currentShuttle) {
            /** 1.根据当前实时班车位置 获取时长预计信息 （班车当前位置-终点） */
            const endStopItem = allStopList[allStopList.length - 1]
            const realMsgData = await busMapUtil.getDrivingMsgForTime([{
                lat: currentShuttle.lat,
                lng: currentShuttle.lng
            }, {
                lat: endStopItem.lat,
                lng: endStopItem.lng
            }])
            newData.endTime = realMsgData?.duration || ''
            /** 2.根据当前实时班车位置 获取时长预计信息 （班车当前位置-下一站） */
            if (nextStop) {
                if (endStopItem.id == nextStop.id) {
                    newData.nextStopTime = realMsgData?.duration || ''
                } else {
                    const nextMsgData = await busMapUtil.getDrivingMsgForTime([{
                        lat: currentShuttle.lat,
                        lng: currentShuttle.lng
                    }, {
                        lat: nextStop.lat,
                        lng: nextStop.lng
                    }])
                    newData.nextStopTime = nextMsgData?.duration || ''
                }
            }
        }
    }
    Object.assign(wayViewData, newData)
    console.log("🚀 ~ ShuttleDepartureInfo.vue ~ updateView ~ wayViewData:", toValue(wayViewData))
}
const drawRoute = async (stopList: ICommuteStopInfo[]) => {
    const routes = toValue(stopList)?.filter(ele => ele.lat && ele.lng)?.map(ele => ({
        id: ele.id || uuidv4(),
        name: ele.stopName,
        longitude: ele.lng,
        latitude: ele.lat
        // ...ele
    })) || []
    const drawData = await busMapUtil.drawRoute(routes)
    allPolyline.value = drawData?.line || []
    allMarkers.value = drawData?.markers || []
}
</script>

<style scoped lang="scss">
.wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.map {
    // min-height: 244px;
    height: 40%;
    flex-shrink: 0;
}

.content {
    flex: 1;
    border-radius: 10px 10px 0px 0px;
    background: linear-gradient(180deg, #E7F3FF 0%, #F3F5F9 100%);
    border: 1px solid #FFFFFF;
    box-shadow: 0px 0px 2px 0px #92929233;
    padding: 8px;
    margin-top: -20px;
    z-index: 2;
}

.content_footer {
    margin: 28px 7px;
    background-color: #fff;
}

.content_summary {
    width: 100%;
    min-height: 101px;
    border-radius: 4px;
    background: #FFFFFF;
    box-shadow: 0px 0px 2px 0px #9292924C;
    padding: 9px 0;
}

.way-process {
    background-color: #fff;
    padding: 9px 0;
    position: relative;
    margin-top: 10px;
}

.shuttle-msg {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    height: 80px;
    width: 100%;
    padding-left: 13px;
    padding-right: 5px;

    .follow-start {
        position: absolute;
        top: 10px;
        right: 13px;
    }
}

.shuttle-tip {
    border-radius: 15px;
    background: #F7FBFF;
    height: 28px;
    display: flex;
    align-items: center;
    margin-top: 4px;
    justify-content: space-between;
    font-size: 12px;
    padding-left: 12px;
    color: #666;
    margin: 0 13px;
}

// 班车图标+车牌号
.shuttle_icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;

    &-text {
        border-radius: 15px;
        background: #FFFFFF;
        box-sizing: border-box;
        border: 1px solid #81BBFF;
        text-align: center;
        padding: 4px;
        font-size: 9px;
        font-weight: 500;
        color: #094895;
        // 贴近svg
        margin-top: -16px;
    }
}

// 路线 起点至终点
.way {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
    position: relative;
    height: 100%;
    justify-content: space-around;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-wrap: nowrap;

    &-item {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #333;
        z-index: 2;
    }

    &::before {
        position: absolute;
        left: 6px;
        top: 15px;
        height: 50%;
        width: 100%;
        content: '';
        border-left: 1px dashed #999;
    }
}

.stop {
    color: #333333;
    font-size: 14px;
    margin-left: 5px;
}

.process-content {
    padding: 5px 13px;
}

// 预计标题
.process-title {
    padding-left: 13px;
    padding-right: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;

    .right {
        border-radius: 12px;
        background: #DFF5FF;
        border: 1px solid #A4E7FF;
        padding: 5px 6px;
        color: #1696c3;
        display: inline-flex;
        align-items: center;
    }
}

.time {
    font-size: 14px;
    font-weight: bold;
}

// 路线途径点
.process-content-item {
    width: 100%;
    // height: 55px;
    padding-bottom: 20px;
    display: flex;
    gap: 5px;
    position: relative;
    color: #666;

    &:not(:last-child) {
        &::before {
            position: absolute;
            content: '';
            width: 2px;
            height: 100%;
            top: 0.5rem;
            left: 8px;
            background-color: #e4effb;
            z-index: 1;
        }
    }

    &:last-child {
        // height: 14px;
        padding-bottom: 0;
    }

    &:first-child,
    &:last-child {
        color: #333;
    }

    &.active {
        color: #005cc8;
    }
}

.pointer {
    width: 16px;
    text-align: center;
}

.pointer-center {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #D8D8D8;
    border: 1px solid #979797;
    margin-top: 0.5rem;
    margin-left: 0.2rem
}

// 收起
.un-collapse {
    font-size: 12px;
    color: #666;
    position: absolute;
    bottom: 14px;
    right: 20px;
}

// 展开
.collapse {
    margin-top: 10px;

    .collapse-time {
        color: #666;
        font-size: 12px;
    }

    .collapse-btn {
        display: flex;
        align-items: center;
        padding: 1px 4px;
        border-radius: 8px;
        background: #F0F0F0;
        font-size: 10px;
        color: #666;
        width: 40px;
        margin-top: 4px;
    }
}

.name {
    display: flex;
    flex-direction: column;
}

.shuttle-right {
    display: flex;
    align-items: flex-start;
    height: 100%;
    width: 76px;
    flex-shrink: 0;
    padding-top: 5px;

    .time-start {
        font-size: 12px;
        border-radius: 12px;
        background: #DFEEFF;
        border: 1px solid #A4CEFF;
        width: 76px;
        height: 24px;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #0058CB;
        white-space: nowrap;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
}


// 站点状态
.process-content-item {
    &.process_active {
        color: #005CC8;
        font-weight: 500;

        .pointer-center {
            background-color: #fff;
            border: 2px solid #005cc8;
        }
    }

    &.process_will-arrive {
        color: #333;

        .pointer-center {
            background-color: #fff;
            border-color: #005cc8;
        }
    }

    &.process_passed {
        color: #666;


    }
}

.next-stop-time {
    border-radius: 19px;
    background: #FFFFFF;
    box-shadow: 0px 0px 8px 0px #0000000C;
    padding: 7px 12px;
    color: #333;

    .stop-time {
        color: #005CC8;
    }

    .stop-icon {
        display: inline-flex;
        vertical-align: top;
        margin-left: 2px;
    }
}
</style>