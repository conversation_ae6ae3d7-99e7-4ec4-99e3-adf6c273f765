<template>
    <view class="container">
        <map class="amap" :longitude="location?.longitude" :latitude="location?.latitude" :scale="scale"
            :polyline="polyline" :markers="markers" @regionchange="handleRegionChange">
            <view class="cover-view">
                <view class="item" @click="onLocation">
                    <SlSVgIcon name="16-16-31" size="16" />
                </view>
                <view class="item" @click="onRefresh">
                    <SlSVgIcon name="16-16-32" size="16" />
                </view>
            </view>
        </map>
        <!-- <view>当前缩放级别: {{ currentScale }}</view> -->
    </view>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watchEffect } from 'vue'
const stations: any[] = [
]

// 地图状态
// const longitude = ref(stations[0]?.longitude)
// const latitude = ref(stations[0]?.latitude)
const scale = ref(12);
// const currentScale = ref(12); // 当前实际缩放级别
// watchEffect(() => {
//   console.log(scale.value)
// })
/** 站点markers */
// const markers = ref<any[]>([])
/** 公交站markers */
// const busMarkers = ref<any[]>([])
// const polyline = ref<any[]>([])
// const busMapUtil = useBusMap()
// const allMarkers = computed(() => [...markers.value, ...busMarkers.value])
const props = defineProps<{
    location?: { longitude: number, latitude: number };
    polyline?: any[]
    markers?: any[]
}>()
const emit = defineEmits(['update:location', 'update:polyline', 'update:markers'])
onMounted(() => {
    // myAmapFun = new amapFile.AMapWX({ key: '4327be16f354ad98efd2414bf4166102' });
    // 初始化标记点
    // console.log("🚀 ~ index.vue ~ onMounted ~ myAmapFun:", myAmapFun)
})
onUnmounted(() => {
})
// 监听地图视野变化
const handleRegionChange = (e: any) => {
    // console.log("🚀 ~ index.vue ~ regionchange ~ e:", e)
    // if (e.type === 'end') {
    //     // 当缩放结束时更新当前缩放级别
    //     updateCurrentScale(e.detail);
    // }
};

// 更新当前缩放级别
const updateCurrentScale = (mapData: any) => {
    // if (mapData && mapData.scale !== undefined) {
    //     currentScale.value = mapData.scale;
    //     console.log('缩放级别变化:', currentScale.value);
    // }
};
// const regionchange2 = (e: { causedBy:any, type, detail: { rotate, skew, scale, centerLocation, region } }) => {

// }
const draw = async () => {
    // console.log("🚀 ~ index.vue ~ draw ~ draw:")
    // try {
    //     const data = await busMapUtil.drawRoute(stations)
    //     console.log("🚀 ~ draw ~ data:", data)
    //     polyline.value = data?.line || []
    //     markers.value = data?.markers || []
    // } catch (e) {
    //     polyline.value = []
    //     markers.value = []
    // }
}
const onLocation = () => {
    uni.getLocation({
        type: 'wgs84',
        success: (res) => {
            console.log("🚀 ~ index.vue ~ onLocation ~ res:", res)
            const newLocation = { longitude: res.longitude, latitude: res.latitude }
            emit('update:location', newLocation)
            // longitude.value = res.longitude
            // latitude.value = res.latitude
        }
    })
}
const onRefresh = () => { }
</script>

<style lang="scss" scoped>
.container {
    width: 100%;
    height: 100%;
}

.amap {
    width: 100%;
    // height: calc(100vh - 100rpx);
    height: 100%;
    // height: 70%;

    .csssprite {
        display: none;
    }
}

.info-panel {
    padding: 20rpx;
    background-color: #ffffff;
    border-top: 1rpx solid #eeeeee;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.station-name {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
}

.station-desc {
    font-size: 28rpx;
    color: #666666;
    margin-top: 10rpx;
}

.marker {
    position: absolute;
}

.cover-view {
    position: absolute;
    bottom: 10rpx;
    right: 20rpx;
}

.item {
    width: 30px;
    height: 30px;
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    background: #FFFFFF;
    box-shadow: 0px 0px 7px 0px #00000016;
}

.item+.item {
    margin-top: 10px;
}
</style>