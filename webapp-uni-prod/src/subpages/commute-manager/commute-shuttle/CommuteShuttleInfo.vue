<template>
    <div class="wrapper">
        <div class="info-title">
            <div class="arrow-icon">
                <SlSubSvgIcon subpage="commute-manager" :name="'8-8-' + (showInfo ? '1' : '2')" size="8"
                    @click="showInfo = !showInfo" />
                <!-- <SlSVgIcon :name="'8-8-' + (showInfo ? '1' : '2')" size="8" @click="showInfo = !showInfo" /> -->
            </div>
            <div class="info-title_next">
                距
                <span class="info-title_name">
                    {{ nearestData?.stopName }}
                </span>
                最近的下一班
            </div>
            <div class="info-title_tip">
                <template v-for="(tipVal, tipKey, index) of nearestTip" :key="tipKey">
                    {{ tipVal }}
                    <template v-if="index !== 2 && tipVal"> · </template>
                </template>
            </div>
        </div>
        <div class="info-way" v-show="showInfo">
            <div class="info-way_title">
                <div style="display: flex;align-items: center;gap: 3px;">
                    <SlSubSvgIcon subpage="commute-manager" style="display: inline-flex;" name="12-12-19" size="12" />
                    <!-- <SlSVgIcon style="display: inline-flex;" name="12-12-19" size="12" /> -->
                    {{ data.startWay }}
                </div>
                <span>···</span>
                <div style="display: flex;align-items: center;gap: 3px;">
                    <SlSubSvgIcon subpage="commute-manager" style="display: flex" name="12-12-18" size="12" />
                    <!-- <SlSVgIcon style="display: flex" name="12-12-18" size="12" /> -->
                    {{ data.endWay }}
                </div>
            </div>
            <scroll-view :scroll-left="0" scroll-x class="info-way_content" scroll-with-animation scroll-anchoring>
                <div class="shuttle-icon">
                    <SlSubSvgIcon subpage="commute-manager" name="30-30-7" size="30" />
                    <!-- <SlSVgIcon name="30-30-7" size="30" /> -->
                </div>
                <div class="shuttle-way">
                    <template v-for="(item, index) of wayOptions" :key="item.id">
                        <div class="item shuttle-way-item">
                            <div class="shuttle-way-item_icon" v-if="nearestData?.id != item.id"></div>
                            <SlSubSvgIcon subpage="commute-manager" name="14-14-16" size="14" v-else />
                            <!-- <SlSVgIcon name="14-14-16" size="14" v-else /> -->
                            <div class="vertical-text-column">
                                <div v-for="(nameI, idx) of item.names" :key="idx">
                                    {{ nameI }}
                                </div>
                            </div>
                            <!-- <div class="vertical-text">
                                {{ item.name }}
                            </div> -->
                        </div>
                        <!-- 过渡区 -->
                        <div class="item shuttle-way-transit" v-if="index != wayOptions.length - 1">
                        </div>
                    </template>
                </div>
            </scroll-view>
        </div>
    </div>
</template>

<script setup lang="ts">
import commuteService from '@/service/commute/commute.service';
import { uuidv4 } from '@/utils/uuid';
import { CommuteVehicleInfo, ICommuteStopInfo } from '@/models/Commute';
import { useWmap } from '@/hooks';
const { getNearestStation, getDrivingMsgForTime } = useWmap()
interface WayItem {
    id: string;
    name: string;
    /** 数组组成的名称 间隔 */
    names?: string[]
}
class CommuteShuttleInfo {
    endWay?: string;
    tip?: string;
    startWay?: string
}
const data = reactive<CommuteShuttleInfo>({
    startWay: '伴景湾',
    endWay: '局机关'
})
const props = defineProps(['id'])
const nearestTip = reactive<{ time: string; stopLen: string; distance: string }>({
    /** 当前车辆距离最近一站时间 */
    time: '',
    /** 当前车辆距离最近一站的站点数 */
    stopLen: '',
    /** 当前车辆距离最近一站的距离 */
    distance: ''
})
// const name = ref('石油大院')
/** 距离自己最近的站点信息 */
const nearestData = ref<ICommuteStopInfo>()
const tip = ref('等待车辆发车')
const wayOptions = ref<WayItem[]>(Array.from({ length: 10 }, (_, i) => ({ id: uuidv4(), name: `地点${i}` })))
const showInfo = ref(true)
const info = ref<CommuteVehicleInfo>()
onMounted(() => {
    if (props.id) {
        getInfo(props.id)
    }
})
const getInfo = (id: string) => {
    commuteService.getVehicleDetail(id).then(res => {
        info.value = res
        console.log(res)
        const { commuteRoute, commuteTrip } = res
        wayOptions.value = commuteRoute?.stopList?.map(e => ({ id: e.id, name: e.stopName, names: e.stopName.split('') })) || []
        data.startWay = commuteRoute.startStopName
        data.endWay = commuteRoute.endStopName
        updateNearestData()
    })
}
const updateNearestData = async () => {
    await getNearestStop()
    await getNearestTip()
}
const getNearestTip = async () => {
    const currentShuttle = toValue(info)?.commuteTrip?.currentStop || null
    if (!currentShuttle?.id || !toValue(nearestData)?.id) return;
    const startPoint = { lat: currentShuttle.lat, lng: currentShuttle.lng }
    const endPoint = { lat: toValue(nearestData)!.lat, lng: toValue(nearestData)!.lng }
    const msg = await getDrivingMsgForTime({ start: startPoint, end: endPoint })
    nearestTip.time = msg?.duration || ''
    nearestTip.distance = msg?.distance || ''
}
const getNearestStop = async () => {
    const stopList = toValue(info)?.commuteRoute?.stopList || []
    const nearestStop = await getNearestStation(stopList?.map(s => {
        return {
            id: s.id,
            name: s.stopName,
            longitude: s.lng,
            latitude: s.lat,
            _init: s,
        }
    }) || []) || null
    const newNearestStop = nearestStop as unknown as ICommuteStopInfo;
    // const newNearestLen = stopList.findIndex(s => s.id == nearestStop?.id)
    // 当前车站停靠点距离终点最近

    // nearestTip.stopLen =
    nearestData.value = newNearestStop
    console.log("🚀 ~ CommuteShuttleInfo.vue ~ updateNearest ~ nearestData.value:", nearestData.value)
}
</script>

<style scoped lang="scss">
.info-title {
    .arrow-icon {
        width: 100%;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    padding: 0 12px 10px;
    border-radius: 4px;
    box-shadow: 0px 0px 2px 0px #9292924C;
    // height: 96px;
    background-color: #fff;
}

.info-way {
    background-color: #fff;
    min-height: 184px;
    margin: 10px 0;
    border-radius: 4px;
    box-shadow: 0px 0px 2px 0px #9292924C;
    background-color: #fff;
    padding: 15px 7px 0;

    .info-way_title {
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 100%;
    }
}

.info-title_name {
    color: #005CC8;
    font-size: 18px;
    display: inline-block;
    margin: 0 9px;
    font-weight: 500;
}


.info-title_tip {
    border-radius: 15px;
    background-color: #f7fbff;
    padding: 5px 15px;
    margin-top: 10px;
    color: #005CC8;
}

.vertical-text {
    /* 垂直排列，从左到右 */
    writing-mode: vertical-lr;
    color: #666666;
    font-size: 9px;
}

.vertical-text-column {
    /* 垂直排列，从左到右 */
    color: #666666;
    font-size: 9px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;
}

.info-way_content {
    white-space: nowrap;

    .item {
        flex-shrink: 0
    }

    .shuttle-way-item_icon {
        width: 8px;
        height: 8px;
        border-radius: 8px;
        background: #E0E0E0;
        border: 1px solid #979797;

        &.comming {
            background: #FFFFFF;
            border: 1px solid #005CC8;
        }
    }

    .shuttle-way {
        display: flex;
    }
}

.shuttle-way-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    width: 8px;
}

.shuttle-way-transit {
    width: 45px;
    height: 5px;
    background-color: #999;
    margin-top: 2px;

    &_comming {
        background-color: #2E67E2;
    }
}
</style>