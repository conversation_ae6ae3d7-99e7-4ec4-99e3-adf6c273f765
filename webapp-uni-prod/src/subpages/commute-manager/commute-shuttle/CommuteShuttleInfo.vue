<template>
    <div class="wrapper">
        <div class="info-title">
            <div class="arrow-icon">
                <SlSubSvgIcon subpage="commute-manager" :name="'8-8-' + (showInfo ? '1' : '2')" size="8"
                    @click="showInfo = !showInfo" />
            </div>
            <div class="info-title_next">
                距
                <span class="info-title_name">
                    {{ nearestData?.stopName }}
                </span>
                最近的下一班
            </div>
            <div class="info-title_tip">
                {{ nearestStopTip }}
                <!-- <template v-for="(tipVal, tipKey, index) of nearestTip" :key="tipKey">
                    {{ tipVal }}
                    <template v-if="index !== 2 && tipVal"> · </template>
</template> -->
            </div>
        </div>
        <div class="info-way" v-show="showInfo">
            <div class="info-way_title">
                <div style="display: flex;align-items: center;gap: 3px;">
                    <SlSubSvgIcon subpage="commute-manager" style="display: inline-flex;" name="12-12-19" size="12" />
                    <!-- <SlSVgIcon style="display: inline-flex;" name="12-12-19" size="12" /> -->
                    {{ data.startWay }}
                </div>
                <span>···</span>
                <div style="display: flex;align-items: center;gap: 3px;">
                    <SlSubSvgIcon subpage="commute-manager" style="display: flex" name="12-12-18" size="12" />
                    <!-- <SlSVgIcon style="display: flex" name="12-12-18" size="12" /> -->
                    {{ data.endWay }}
                </div>
            </div>
            <scroll-view :scroll-left="0" scroll-x class="info-way_content" scroll-with-animation scroll-anchoring>
                <!-- <div class="shuttle-icon"> -->
                <!-- <SlSubSvgIcon subpage="commute-manager" name="30-30-7" size="30" /> -->
                <!-- <SlSVgIcon name="30-30-7" size="30" /> -->
                <!-- </div> -->
                <div class="shuttle-icon">
                    <SlSubSvgIcon v-for="icon in shuttleIcons" :style="{
                        position: 'absolute',
                        left: icon.position.left,
                        top: icon.position.top,
                        transition: 'all 0.5s ease'
                    }" :key="icon.id" subpage="commute-manager" name="30-30-7" size="30" />
                </div>
                <div class="shuttle-way">
                    <template v-for="(item, index) of wayOptions" :key="item.id">
                        <div class="item shuttle-way-item"
                            :class="index == 0 ? 'first' : index == wayOptions.length - 1 ? 'last' : ''">
                            <div class="shuttle-way-item_icon icon" v-if="nearestData?.id != item.id"></div>
                            <SlSubSvgIcon subpage="commute-manager" class="icon" name="14-14-23" size="14" v-else />
                            <div class="vertical-text-column">
                                <div v-for="(nameI, idx) of item.names" :key="idx">
                                    {{ nameI }}
                                </div>
                            </div>
                        </div>
                        <!-- 过渡区 -->
                        <div class="item">
                            <div class="item shuttle-way-transit" v-if="index != wayOptions.length - 1">
                            </div>
                            <div class="nearest-item" v-if="nearestData?.id == item.id">
                                <span class="nearest-item_text">
                                    离我最近
                                </span>
                            </div>
                        </div>
                    </template>
                </div>
            </scroll-view>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useLoading, useWmap } from '@/hooks';
import { CommuteVehicleInfo, ICommuteCar, ICommuteStopInfo } from '@/models/Commute';
import commuteService from '@/service/commute/commute.service';
import { cloneDeep } from '@/utils/clone';
import { uuidv4 } from '@/utils/uuid';
const { getNearestStation, getDrivingMsgForTime, updateNearestData } = useWmap()
interface WayItem {
    id: string;
    name: string;
    /** 数组组成的名称 间隔 */
    names?: string[]
}
class CommuteShuttleInfo {
    endWay?: string;
    tip?: string;
    startWay?: string
}
const data = reactive<CommuteShuttleInfo>({
    startWay: '',
    endWay: ''
})
const props = defineProps<{
    id?: string
    newShuttleDatas?: ICommuteCar[]
}>()
const emit = defineEmits(['onShuttleInfoChange'])
const nearestStopTip = ref('')
const shuttleIcons = ref<{ id: string; position: { left: string; top: string } }[]>([{
    id: uuidv4(),
    position: {
        left: '0rpx',
        top: '0rpx'
    }
}])
/** 距离自己最近的站点信息 */
const nearestData = ref<ICommuteStopInfo>()
const wayOptions = ref<WayItem[]>([])
const showInfo = ref(true)
const info = ref<CommuteVehicleInfo>()
watch(() => props.newShuttleDatas, () => {
    console.log("🚀 ~ 职工端最新班车-info:", props.newShuttleDatas)
    getInfo()
}, { deep: true })
onMounted(() => {
    getInfo(true)
})
// const loading = useLoading()
const getInfo = async (showLoading: boolean = false) => {
    const id = props.id
    if (!id) return;
    // showLoading && loading.showLoading()
    try {
        const res = await commuteService.getVehicleDetail(id)
        info.value = res
        const { commuteRoute } = res
        wayOptions.value = commuteRoute?.stopList?.map(e => ({ id: e.id!, name: e.stopName, names: e.stopName.split('') })) || []
        data.startWay = commuteRoute.startStopName
        data.endWay = commuteRoute.endStopName
        if (commuteRoute?.stopList.length >= 2) {
            const nearestStopData = await getNearestStation(commuteRoute.stopList.map(ele => ({ id: ele.id!, name: ele.stopName, latitude: ele.lat, longitude: ele.lng })))
            console.log("🚀 ~ index.vue ~ 距离最近的站 ~ res:", nearestStopData)
            if (nearestStopData?.id) {
                const newNearestData = commuteRoute.stopList.find(ele => ele.id == nearestStopData.id)
                nearestData.value = cloneDeep(newNearestData)
            }
        }
        emit('onShuttleInfoChange', res)
    } catch (error) {

    } finally {
        // showLoading && loading.hideLoading()
    }
}
</script>

<style scoped lang="scss">
.info-title {
    .arrow-icon {
        width: 100%;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    padding: 0 12px 10px;
    border-radius: 4px;
    box-shadow: 0px 0px 2px 0px #9292924C;
    // height: 96px;
    background-color: #fff;
}

.info-way {
    background-color: #fff;
    min-height: 184px;
    margin-top: 10px;
    border-radius: 4px;
    box-shadow: 0px 0px 2px 0px #9292924C;
    background-color: #fff;
    padding: 15px 7px;

    .info-way_title {
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 100%;
    }
}

.info-title_name {
    color: #005CC8;
    font-size: 18px;
    display: inline-block;
    margin: 0 9px;
    font-weight: 500;
}


.info-title_tip {
    border-radius: 15px;
    background-color: #f7fbff;
    padding: 0 15px;
    margin-top: 10px;
    color: #005CC8;
    height: 28px;
    line-height: 28px;
}

.vertical-text {
    /* 垂直排列，从左到右 */
    writing-mode: vertical-lr;
    color: #666666;
    font-size: 9px;
}

.vertical-text-column {
    /* 垂直排列，从左到右 */
    color: #666666;
    font-size: 9px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;
}

.info-way_content {
    white-space: nowrap;

    .item {
        flex-shrink: 0
    }

    .shuttle-way-item_icon {
        width: 10px;
        height: 10px;
        border-radius: 10px;
        background: #E0E0E0;
        border: 1px solid #979797;
        margin-top: 2px;

        &.comming {
            background: #FFFFFF;
            border: 1px solid #005CC8;
        }
    }

    .shuttle-way {
        display: flex;
    }
}

.shuttle-icon {
    width: 100%;
    height: 30px;
    position: relative;
}

.shuttle-way-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    width: 18px;
    position: relative;

    &::before {
        position: absolute;
        content: '';
        top: 5px;
        left: 0;
        width: 100%;
        height: 5px;
        background-color: #999;
        z-index: 1;
    }

    &.first {
        &::before {
            left: 50%;
            width: 50%;
        }
    }

    &.first::before {
        left: 50%;
        width: 50%;
    }

    &.last::before {
        width: 50%;
    }

    .icon {
        z-index: 2;
    }
}

.shuttle-way-transit {
    width: 45px;
    height: 5px;
    background-color: #999;
    margin-top: 5px;

    &_comming {
        background-color: #fff;
        border: 1px solid #005cc8;
    }
}

.nearest-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 5px;

    .nearest-item_text {
        display: inline-block;
        border-radius: 6px;
        padding: 5px;
        background: linear-gradient(270deg, #F7FBFF 0%, #C4DFFF 100%);
        color: #005CC8;
        font-size: 12px;
        text-align: center;
        writing-mode: vertical-lr;
    }
}
</style>