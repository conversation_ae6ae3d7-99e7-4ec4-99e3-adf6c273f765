<template>
    <div class="wrapper">
        <div class="map">
            <CommuteMap :markers="getAllMarkers" v-model:location="location" :polyline="allPolyline" :scale="10" />
        </div>
        <div class="msg msg-list-wrapper">
            <div class="msg-header">
                <!-- 上班时间隐藏下班按钮，反之亦然 -->
                <div v-show="msgType == 0" :class="['msg-header_item', { 'active': msgType == 0 }]"
                    @click="msgType = 0; getList()">上班</div>
                <div v-show="msgType == 1" :class="['msg-header_item', { 'active': msgType == 1 }]"
                    @click="msgType = 1; getList()">下班</div>
            </div>
            <scroll-view class="msg-list" scroll-y @refresherrefresh="!refreshing && !isInit && getList()"
                refresher-enabled :show-scrollbar="false" :refresher-triggered="refreshing">
                <div class="msg-item" v-for="item of datas" :key="item.id" @click.self="onMsgItem(item)">
                    <div class="shuttle-msg">
                        <div class="shuttle_icon">
                            <SlSubSvgIcon subpage="commute-manager" name="54-54-1" size="54" />
                            <div class="shuttle_icon-text">{{ item.name }}</div>
                        </div>
                        <div class="way">
                            <div class="way-item">
                                <SlSubSvgIcon subpage="commute-manager" name="12-12-19" size="12" />
                                {{ item.startWay }}
                            </div>
                            <div class="way-item">
                                <SlSubSvgIcon subpage="commute-manager" name="12-12-18" size="12" />
                                {{ item.endWay }}
                            </div>
                        </div>
                        <div @click.stop="onFollowItem(item)" class="follow-start">
                            <SlSubSvgIcon subpage="commute-manager" :name="'16-16-' + (item.isFollow ? '26' : '25')"
                                size="16" />
                        </div>
                    </div>
                    <div class="shuttle-tip">
                        <div class="shuttle-tip_content">
                            {{ item.tip }}
                        </div>
                        <div class="shuttle-tip_time" v-if="item.timeTip">
                            <SlSubSvgIcon subpage="commute-manager" name="16-16-33" size="16"
                                style="display: inline-flex;" />
                            {{ item.timeTip }}
                        </div>
                    </div>
                </div>
                <BaseEmpty class="empty" v-if="!datas?.length" />
            </scroll-view>
        </div>
    </div>
    <ConfirmDialog v-if="showConfirmFollow" @close="showConfirmFollow = false" @confirm="handleFollow">您确定要取消关注本条路线吗？
    </ConfirmDialog>
</template>

<script setup lang="ts">
import useMessage from '@/hooks/use-message'
import { CommuteRouteType, CommuteVehicleInfo, ICommuteMapStation } from '@/models/Commute'
import commuteService from '@/service/commute/commute.service'
import { usePrincipalStore } from '@/store'
import { cloneDeep } from '@/utils/clone'
import { MapMarker, MapPolyline } from '@uni-helper/uni-app-types'
import { useWmapDraw } from '../hooks/use-wmap-draw'
interface RowItem {
    /** 车牌号 */
    name: string
    /** 起始点 */
    startWay: string
    /** 终点 */
    endWay: string
    id: string
    /** 是否关注 */
    isFollow: boolean
    /** 预计到站时间 */
    timeTip: string
    /** tip */
    tip: string
    _init?: CommuteVehicleInfo
}
/** 0上班 1下班 */
const msgType = ref<CommuteRouteType | null>(null)
const currentInfo = ref<RowItem | null>(null)
const currentFollowItem = ref<RowItem | null>(null)
const showConfirmFollow = ref(false)
const { account } = storeToRefs(usePrincipalStore())
const { newShuttleMarkers, closeSocketBus, setRouteLine, drawMarker } = useWmapDraw()
const allMarkers = ref<MapMarker[]>([])
const getAllMarkers = computed(() => [...toValue(allMarkers), ...toValue(newShuttleMarkers)])
const allPolyline = ref<MapPolyline[]>([])
const infos = ref<CommuteVehicleInfo[]>([])
const datas = ref<RowItem[]>([])
const BASE_LINE_COLORS = [{
    lineColor: '#03ce87',
    borderColor: '#0caa73'
}, {
    lineColor: '#43a8d9',
    borderColor: '#1e80b0'
}, {
    lineColor: '#6c71ce',
    borderColor: '#373ebb'
}]
const message = useMessage()
let isInit: boolean = false
onShow(() => {
    msgType.value = getCurRouteTypeByTime() // 上班时间隐藏下班按钮，反之亦然
    currentFollowItem.value = null
    currentInfo.value = null
    closeSocketBus()
    isInit = true
    // refreshing.value = true
    getList()
})
const location = ref<{ longitude: number, latitude: number }>()
const onFollowItem = (item: RowItem) => {
    currentFollowItem.value = cloneDeep(item)
    const isFollow = !item.isFollow
    if (isFollow) { // 关注
        showConfirmFollow.value = false
        handleFollow()
    } else { // 取消弹框
        showConfirmFollow.value = true
    }
}
const onMsgItem = (item: any) => {
    uni.navigateTo({
        url: `/subpages/commute-manager/commute-shuttle/CommuteShuttleInfo?id=${item.id}`
    })
}
const refreshing = ref(false);
const getList = async () => {
    if (toValue(msgType) == null) return;
    refreshing.value = true
    // const currentTimeForRouteType = getCurRouteTypeByTime()
    // // 如果当前时间是上班时间，显示上班路线，若获取下班列表，则置空；反之同理；
    // if (currentTimeForRouteType != toValue(msgType)) {
    //     datas.value = []
    //     infos.value = []
    //     allPolyline.value = []
    //     allMarkers.value = []
    //     setTimeout(() => {
    //         isInit = false
    //         refreshing.value = false
    //     });
    //     return;
    // }
    try {
        const res = await commuteService.getList({ userId: toValue(account)!.id, routeType: toValue(msgType)! }) || []
        infos.value = res
        datas.value = res?.map(ele => {
            const routeInfo = ele.commuteRoute || {}
            const routeTrip = ele.commuteTrip || null
            const itemTrip = routeTrip == null || routeTrip.status == 0 ? '等待发车' : ''
            return <RowItem>{
                // 车牌号 xx · xxxxx
                name: ele.carName ? `${ele.carName.slice(0, 2)} · ${ele.carName.slice(2)}` : '',
                startWay: routeInfo.startStopName,
                endWay: routeInfo.endStopName,
                id: ele.id,
                isFollow: ele.followed,
                _init: ele,
                tip: itemTrip,
            }
        }) || []
        location.value = {
            longitude: res[0]?.commuteRoute?.stopList?.[0]?.lng || 0,
            latitude: res[0]?.commuteRoute?.stopList?.[0]?.lat || 0
        }
        await updateDraw(res)
    } catch (error) {
        datas.value = []
    } finally {
        isInit = false
        refreshing.value = false
    }
}
const updateDraw = async (infos: CommuteVehicleInfo[]) => {
    allPolyline.value = []
    allMarkers.value = []
    const processedRouteIds = new Set();
    toValue(infos).forEach(({ routeId, commuteRoute }, idx) => {
        // 检查routeId是否已处理过
        if (processedRouteIds.has(routeId)) {
            return; // 跳过已处理的routeId
        }
        // 记录已处理的routeId
        processedRouteIds.add(routeId);
        const stopList = commuteRoute?.stopList?.filter(ele => ele.lat && ele.lng)
        const routes: ICommuteMapStation[] = stopList?.map((ele, idx) => (<ICommuteMapStation>{
            id: ele.id!,
            name: ele.name,
            latitude: ele.lat,
            longitude: ele.lng,
            hideDrawer: (idx != 0 && idx != stopList?.length - 1) || !ele.flag, // 途经点不做显示
            _type: idx == 0 ? 'start' : idx == stopList?.length - 1 ? 'end' : ''
        })) || []
        // 在三个随机色里取
        const { borderColor = '', lineColor = '' } = BASE_LINE_COLORS[idx % BASE_LINE_COLORS.length]
        if (routes?.length >= 2) {
            const line = setRouteLine(routes, lineColor, borderColor)
            allPolyline.value.push(...line)
            const markers = drawMarker(routes)
            allMarkers.value.push(...markers)
            // drawRoute(routes, lineColor, borderColor).then(({ line, markers }) => {
            //     if (line?.length) {
            //         allPolyline.value.push(...line)
            //     }
            //     if (markers?.length) {
            //         allMarkers.value.push(...markers)
            //     }
            // })
        }
    });
    processCommuteData(toValue(datas))
}
// async function processCommuteData(datas: RowItem[]) {
//     // 过滤并映射需要处理的元素
//     const elementsToProcess = toValue(datas).filter(ele =>
//         ele._init &&
//         ele._init.commuteTrip?.currentStop &&
//         ele._init.commuteTrip?.status !== 0 &&
//         ele._init.commuteRoute?.stopList?.length >= 2
//     );

//     // 并行处理所有元素（添加500ms延迟间隔）
//     const promises = elementsToProcess.map(async (ele, index) => {
//         // 添加延迟间隔（避免同时发送大量请求）
//         // await new Promise(resolve => setTimeout(resolve, 500 * index));

//         const { commuteRoute, commuteTrip } = ele._init!;
//         if (commuteTrip.status == 0 || (commuteTrip.status == 2 && !commuteTrip.nextStop?.id)) {
//             ele.tip = '等待发车'
//         } else {
//             try {
//                 const res = await updateNearestData(
//                     commuteRoute.stopList || [],
//                     commuteTrip.currentStop || {}
//                 );

//                 if (res?.nearestStop?.id) {
//                     const { nearestStop, duration } = res;
//                     const nearestLen = commuteRoute.stopList.findIndex(ele => ele.id == nearestStop.id);
//                     const curLen = commuteRoute.stopList.findIndex(ele => ele.id == commuteTrip.currentStop.id);

//                     if (curLen > -1 && nearestLen > -1) {
//                         if (curLen > nearestLen) {
//                             // 已经过当前的站
//                             ele.tip = '已过站';
//                             ele.timeTip = '';
//                         } else if (curLen < nearestLen) {
//                             // 未过当前的站
//                             // 当前如果处于终点站，不提示上车
//                             ele.tip = `${nearestLen - curLen}站 · ${nearestStop.name}${nearestLen == commuteRoute.stopList.length - 1 ? '' : '上车'
//                                 }`;
//                             ele.timeTip = `${duration}后到站`;
//                         } else {
//                             // 当前站
//                             ele.tip = `${commuteTrip.status == 2 ? '已到站' : '即将到站'} · ${nearestStop.name}上车`;
//                             ele.timeTip = '';
//                         }
//                     }
//                 }
//             } catch (_) {
//             }
//         }
//     });

//     // 等待所有处理完成
//     await Promise.all(promises);
// }
function processCommuteData(datas: RowItem[]) {
    // 过滤并映射需要处理的元素
    const elementsToProcess = toValue(datas).filter(ele =>
        ele._init &&
        ele._init.commuteTrip?.currentStop &&
        ele._init.commuteTrip?.status !== 0 &&
        ele._init.commuteRoute?.stopList?.length >= 2
    );

    // 并行处理所有元素（添加500ms延迟间隔）
    elementsToProcess.forEach((ele, index) => {
        const { status, nextStop, currentStop } = ele._init?.commuteTrip || {};
        // 不根据最近站点更新
        switch (status) {
            case 0:
                ele.tip = '等待发车'
                break;
            case 1:
                ele.tip = `下一站 · ${nextStop?.name || ''}`
                break;
            case 2:
                if (!nextStop?.id) {
                    ele.tip = '等待发车'
                } else {
                    ele.tip = `当前停靠站 · ${currentStop?.name} · 下一站 · ${nextStop?.name}`
                }
                break;
            default:
                break;
        }
    });
}
/** 确定取消关注 */
const handleFollow = () => {
    const userId = account.value?.id
    if (!toValue(currentFollowItem) || !userId) return;
    const { id, isFollow, _init } = toValue(currentFollowItem)!
    const newFollowStatus = !isFollow
    commuteService.saveFollow({ userId, carId: _init?.carId!, followed: newFollowStatus }).then(_ => {
        const item = datas.value.find(ele => ele.id == id)
        const itemIdx = datas.value.findIndex(ele => ele.id == id)
        if (item) item.isFollow = newFollowStatus
        const newItem = cloneDeep(item) as RowItem
        if (newFollowStatus) {
            // 把当前线路置顶
            if (itemIdx > -1) {
                datas.value.splice(itemIdx, 1)
                datas.value.unshift(newItem)
            }
            message.show('线路关注成功')
        } else {
            // 把当前线路列表里第一条有关注的重新置顶，若没有，不做操作;
            const followFirst = datas.value.find(ele => ele.isFollow) as RowItem
            const followFirstIdx = datas.value.findIndex(ele => ele.isFollow)
            if (followFirstIdx > -1) {
                datas.value.splice(followFirstIdx, 1)
                datas.value.unshift(followFirst)
            }
            message.show('线路已取消关注')
        }
        currentFollowItem.value = null
        showConfirmFollow.value = false
    }).catch(err => {
        message.error(err as string || '系统异常~')
    })
}
// const shuttleInfoChange = ($event: CommuteVehicleInfo) => {
//     if (toValue(currentInfo)?.id && $event != null) {
//         updateDraw([$event])
//         // allPolyline.value = commuteRoute.stopList.map(ele => ({ id: ele.id!, name: ele.stopName, longitude: ele.lng, latitude: ele.lat }))
//     }
// }
const getCurRouteTypeByTime = (): CommuteRouteType => {
    const currentTime = new Date().getHours();
    const isAfternoon = currentTime >= 12;
    return isAfternoon ? 1 : 0  // 路线类型 0：上班，1：下班，默认传0
}
</script>

<style scoped lang="scss">
.wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
}

.msg-header {
    padding-left: 5%;
    height: 35px;
    display: flex;
    align-items: center;
    gap: 32px;
    flex-shrink: 0;

    .msg-header_item {
        color: #666;
        font-size: 14px;

        &.active {
            color: #333;
            position: relative;

            &::after {
                position: absolute;
                content: '';
                bottom: -2px;
                left: 50%;
                width: 50%;
                height: 2px;
                background-color: #1a58b2;
                border-radius: 2px;
                transform: translateX(-50%);
            }
        }
    }
}

.map {
    flex: 1;
    min-height: 40%;
    margin-bottom: -18px;
}

.msg {
    border-radius: 10px 10px 0px 0px;
    background: linear-gradient(180deg, #E7F3FF 0%, #F3F5F9 100%);
    border: 1px solid #FFFFFF;
    box-shadow: 0px 0px 4px 0px #92929233;
    overflow-y: auto;
    max-height: 60%;
    padding: 8px;
    z-index: 2;
}

.msg-list-wrapper {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.msg-list {
    flex-grow: 1;
    overflow-y: auto;
    min-height: 200px;
}

.msg-item {
    border-radius: 4px;
    background: #FFFFFF;
    box-shadow: 0px 0px 2px 0px #9292924C;
    padding: 9px 13px;

    &:not(:last-child) {
        margin-bottom: 10px;
    }
}

.shuttle-msg {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    height: 80px;

    .follow-start {
        height: 100%;
        padding-top: 1rem;
        // position: absolute;
        // top: 10px;
        // right: 13px;
    }
}

.shuttle-tip {
    border-radius: 15px;
    background: #F7FBFF;
    height: 28px;
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 4px;
    justify-content: space-between;
    font-size: 12px;
    padding-left: 9px;
    padding-right: 12px;

    &_content {
        flex: 1;
        color: #666;
    }

    &_time {
        color: #005cc8;
        display: flex;
        align-items: center;
        grid-area: 2px;
    }
}

// 班车图标+车牌号
.shuttle_icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;

    &-text {
        border-radius: 15px;
        background: #FFFFFF;
        box-sizing: border-box;
        border: 1px solid #81BBFF;
        text-align: center;
        padding: 4px;
        font-size: 9px;
        font-weight: 500;
        color: #094895;
        // 贴近svg
        margin-top: -16px;
    }
}

// 路线 起点至终点
.way {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
    position: relative;
    height: 100%;
    justify-content: space-around;

    &-item {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #333;
        z-index: 2;
    }

    &::before {
        position: absolute;
        left: 6px;
        top: 15px;
        height: 50%;
        width: 100%;
        content: '';
        border-left: 1px dashed #999;
    }
}
</style>