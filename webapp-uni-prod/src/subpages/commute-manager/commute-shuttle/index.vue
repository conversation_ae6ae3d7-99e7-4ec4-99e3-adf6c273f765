<template>
    <div class="wrapper">
        <div class="map">
            <CommuteMap :markers="allMarkers" v-model:location="location" />
        </div>
        <div class="msg" v-if="!currentInfo?.id">
            <div class="msg-header">
                <div :class="['msg-header_item', { 'active': msgType == 0 }]" @click="msgType = 0; getList()">上班</div>
                <div :class="['msg-header_item', { 'active': msgType == 1 }]" @click="msgType = 1; getList()">下班</div>
            </div>
            <div class="msg-item" v-for="item of datas" :key="item.id" @click.self="onMsgItem(item)">
                <div class="shuttle-msg">
                    <div class="shuttle_icon">
                        <SlSubSvgIcon subpage="commute-manager" name="54-54-1" size="54" />
                        <div class="shuttle_icon-text">{{ item.name }}</div>
                    </div>
                    <div class="way">
                        <div class="way-item">
                            <SlSubSvgIcon subpage="commute-manager" name="12-12-19" size="12" />
                            {{ item.startWay }}
                        </div>
                        <div class="way-item">
                            <SlSubSvgIcon subpage="commute-manager" name="12-12-18" size="12" />
                            {{ item.endWay }}
                        </div>
                    </div>
                    <div @click.stop="onFollowItem(item)" class="follow-start">
                        <SlSubSvgIcon subpage="commute-manager" :name="'16-16-' + (item.isFollow ? '26' : '25')"
                            size="16" />
                    </div>
                </div>
                <div class="shuttle-tip">
                    <div class="shuttle-tip_content">
                        {{ item.tip }}
                    </div>
                    <div class="shuttle-tip_time">
                        <SlSubSvgIcon subpage="commute-manager" name="16-16-33" size="16"
                            style="display: inline-flex;" />
                        {{ item.time }}后到站
                    </div>
                </div>
            </div>
            <BaseEmpty class="empty" v-if="!datas?.length" />
        </div>
        <div class="msg" v-else>
            <CommuteShuttleInfo :id="currentInfo.id" />
        </div>
    </div>
    <ConfirmDialog v-if="showConfirmFollow" @close="showConfirmFollow = false" @confirm="handleFollow">您确定要取消关注本条路线吗？
    </ConfirmDialog>
</template>

<script setup lang="ts">
import { uuidv4 } from '@/utils/uuid'
import CommuteMap from '../commute-map/index.vue'
import CommuteShuttleInfo from './CommuteShuttleInfo.vue'
import useMessage from '@/hooks/use-message'
import { useLoading } from '@/hooks'
import commuteService from '@/service/commute/commute.service'
import { cloneDeep } from '@/utils/clone'
import { usePrincipalStore } from '@/store'
import { CommuteVehicleInfo } from '@/models/Commute'
interface RowItem {
    /** 车牌号 */
    name: string
    /** 起始点 */
    startWay: string
    /** 终点 */
    endWay: string
    id: string
    /** 是否关注 */
    isFollow: boolean
    /** 预计到站时间 */
    time: string
    /** tip */
    tip: string
    _init?: CommuteVehicleInfo
}
/** 0上班 1下班 */
const msgType = ref<0 | 1>(0)
const currentInfo = ref<RowItem | null>(null)
const currentFollowItem = ref<RowItem | null>(null)
const showConfirmFollow = ref(false)
const { account } = storeToRefs(usePrincipalStore())
const allMarkers = computed(() => {
    return []
})
const datas = ref<RowItem[]>(Array.from({ length: 4 }, (_, index) => {
    return {
        name: '深B12345--' + index,
        startWay: '伴景湾西门' + index,
        endWay: '国务院' + index,
        id: uuidv4(),
        isFollow: false,
        time: index + '分钟',
        tip: index + '站·石油大院上车'
    }
}))
onShow(() => {
    console.log("🚀 ~ index.vue ~ onShow ~ onShow:")
    currentFollowItem.value = null
    currentInfo.value = null
    getList()
})
const location = ref<{ longitude: number, latitude: number }>()
const onFollowItem = (item: RowItem) => {
    console.log("🚀 ~ index.vue ~ onFollowItem ~ item:", item)
    currentFollowItem.value = cloneDeep(item)
    const isFollow = !item.isFollow
    if (isFollow) { // 关注
        showConfirmFollow.value = false
        handleFollow()
    } else { // 取消弹框
        showConfirmFollow.value = true
    }
}
const onMsgItem = (item: RowItem) => {
    console.log("🚀 ~ index.vue ~ onMsgItem ~ item:", item)
    currentInfo.value = item
}
const message = useMessage()
const loading = useLoading()
const getList = async () => {
    // loading.showLoading()
    try {
        const res = await commuteService.getList(toValue(msgType)) || []
        datas.value = res?.map(ele => {
            const routeInfo = ele.commuteRoute || {}
            const routeTrip = ele.commuteTrip || {}
            return <RowItem>{
                // 车牌号 xx · xxxxx
                name: ele.carName ? `${ele.carName.slice(0, 2)} · ${ele.carName.slice(2)}` : '',
                startWay: routeInfo.startStopName,
                endWay: routeInfo.endStopName,
                id: ele.id,
                isFollow: ele.followed,
                _init: ele,
                // time: ele.time,
                // tip: ele.tip
            }
        }) || []
    } catch (error) {

    } finally {
        // loading.hideLoading()
    }
}
/** 确定取消关注 */
const handleFollow = () => {
    const userId = account.value?.id
    if (!toValue(currentFollowItem) || !userId) return;
    const { id, isFollow, _init } = toValue(currentFollowItem)!
    const newFollowStatus = !isFollow
    commuteService.saveFollow({ userId, carId: _init?.carId!, followed: newFollowStatus }).then(_ => {
        const item = datas.value.find(ele => ele.id == id)
        if (item) item.isFollow = newFollowStatus
        newFollowStatus ? message.show('线路关注成功') : message.show('线路已取消关注')
        currentFollowItem.value = null
        showConfirmFollow.value = false
    }).catch(err => {
        message.error(err as string || '系统异常~')
    })
}
</script>

<style scoped lang="scss">
.wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
}

.msg-header {
    padding-left: 5%;
    height: 35px;
    display: flex;
    align-items: center;
    gap: 32px;

    .msg-header_item {
        color: #666;
        font-size: 14px;

        &.active {
            color: #333;
            position: relative;

            &::after {
                position: absolute;
                content: '';
                bottom: -5px;
                left: 50%;
                width: 50%;
                height: 2px;
                background-color: #1a58b2;
                border-radius: 2px;
                transform: translateX(-50%);
            }
        }
    }
}

.map {
    flex: 1;
    min-height: 40%;
}

.msg {
    border-radius: 10px 10px 0px 0px;
    background: linear-gradient(180deg, #E7F3FF 0%, #F3F5F9 100%);
    border: 1px solid #FFFFFF;
    box-shadow: 0px 0px 4px 0px #92929233;
    overflow-y: auto;
    max-height: 60%;
    min-height: 20%;
    padding: 8px;
}

.msg-item {
    border-radius: 4px;
    background: #FFFFFF;
    box-shadow: 0px 0px 2px 0px #9292924C;
    padding: 9px 13px;

    &:not(:last-child) {
        margin-bottom: 10px;
    }
}

.shuttle-msg {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    height: 80px;

    .follow-start {
        height: 100%;
        padding-top: 1rem;
        // position: absolute;
        // top: 10px;
        // right: 13px;
    }
}

.shuttle-tip {
    border-radius: 15px;
    background: #F7FBFF;
    height: 28px;
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 4px;
    justify-content: space-between;
    font-size: 12px;
    padding: 0 4px;

    &_content {
        flex: 1;
        color: #666;
    }

    &_time {
        color: #005cc8;
        display: flex;
        align-items: center;
        grid-area: 2px;
    }
}

// 班车图标+车牌号
.shuttle_icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;

    &-text {
        border-radius: 15px;
        background: #FFFFFF;
        box-sizing: border-box;
        border: 1px solid #81BBFF;
        text-align: center;
        padding: 4px;
        font-size: 9px;
        font-weight: 500;
        color: #094895;
        // 贴近svg
        margin-top: -16px;
    }
}

// 路线 起点至终点
.way {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
    position: relative;
    height: 100%;
    justify-content: space-around;

    &-item {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #333;
        z-index: 2;
    }

    &::before {
        position: absolute;
        left: 6px;
        top: 15px;
        height: 50%;
        width: 100%;
        content: '';
        border-left: 1px dashed #999;
    }
}

.empty {
    height: 200px;
}

// .SlSubSvgIcon subpage="commute-manager" {
//     display: inline-flex;
// }</style>