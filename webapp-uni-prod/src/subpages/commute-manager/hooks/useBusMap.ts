import amapFile, { AMapWX } from '@/static/amap-wx.130.js';
import { onMounted, onUnmounted, ref, toValue } from 'vue';

interface Station {
  id: number;
  name: string;
  longitude: number;
  latitude: number;
  desc?: string;
}

interface LocationPoint {
  longitude: number;
  latitude: number;
}

interface BusMapOptions {
  busIconPath?: string;
  stationIconPath?: string;
  animationInterval?: number;
  routeColor?: string;
}

const DEFAULT_OPTIONS: BusMapOptions = {
  busIconPath: '../static/bus.svg',
  stationIconPath: '../static/point.svg',
  animationInterval: 2000,
  routeColor: '#03CE6A',
};

export function useBusMap(options: BusMapOptions = {}) {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  const markers = ref<any[]>([]);
  const polyline = ref<any[]>([]);
  const isLoading = ref(false);
  const myAmapFun = ref<AMapWX | null>(null);
  // 初始化地图
  const initMap = () => {
    // TODO: key
    // myAmapFun.value = new amapFile.AMapWX({ key: '' });
  };

  // 获取路线数据
  const getTransitRoute = (stations: any[]): Promise<LocationPoint[]> => {
    return new Promise((resolve, reject) => {
      if (toValue(myAmapFun) == null || stations.length < 2) {
        reject(new Error('地图未初始化或站点数量不足'));
        return;
      }
      const startPoi = `${stations[0].longitude},${stations[0].latitude}`;
      const endPoi = `${stations[stations.length - 1].longitude},${stations[stations.length - 1].latitude}`;
      const waypoints = stations
        .slice(1, stations.length - 1)
        .map(wp => `${wp.longitude},${wp.latitude}`)
        .join(';');
      toValue(myAmapFun)?.getDrivingRoute({
        origin: startPoi,
        destination: endPoi,
        waypoints,
        success: mapData => {
          let points: LocationPoint[] = [];
          if (mapData?.paths && mapData.paths[0] && mapData.paths[0].steps) {
            const steps = mapData.paths[0].steps;
            for (const step of steps) {
              const stepPoints = step.polyline.split(';').map((point: any) => {
                const [lng, lat] = point.split(',');
                return { longitude: parseFloat(lng), latitude: parseFloat(lat) };
              });
              points = points.concat(stepPoints);
            }
          }
          resolve(points);
        },
        fail: info => {
          console.error('获取路线失败:', info);
          reject(info);
        },
      });
    });
  };

  // 设置站点标记
  const setStationMarkers = (stations: Station[]) => {
    const markerArr =
      stations.map(station => ({
        id: station.id,
        longitude: station.longitude,
        latitude: station.latitude,
        width: 16,
        height: 16,
        iconPath: mergedOptions.stationIconPath,
        anchor: { x: 0.5, y: 0.8 },
      })) || [];
    markers.value = markerArr;
    return markerArr;
  };

  // 设置路线
  const setRouteLine = (points: LocationPoint[], lineColor?: string) => {
    const polylineArr = [
      {
        points,
        color: lineColor || mergedOptions.routeColor,
        width: 6,
        dottedLine: false,
        arrowLine: true,
      },
    ];
    polyline.value = polylineArr;
    return polylineArr;
  };

  // 绘制完整路线
  const drawRoute = async (stations: Station[], lineColor?: string) => {
    try {
      const markers = setStationMarkers(stations);
      const points = await getTransitRoute(stations);
      const line = setRouteLine(points);
      //   startBusAnimation();
      return { markers, line };
    } catch (error) {
      console.error('绘制路线失败:', error);
      return null;
    }
  };

  /** 获取输入提示词 */
  const getInputtips = (keyword: string) => {
    return new Promise((resolve, reject) => {
      if (!toValue(myAmapFun)) reject(null);
      toValue(myAmapFun)?.getInputtips({
        keywords: keyword,
        datatype: 'bus',
        success: function (data) {
          console.log('获取输入提示词：', data);
          resolve(data);
        },
        fail: function (info) {
          console.error(info);
          reject(null);
        },
      });
    });
  };

  // 清理资源
  const cleanup = () => {
    // stopBusAnimation();
  };

  // 生命周期钩子
  onMounted(() => {
    initMap();
  });

  onUnmounted(() => {
    cleanup();
  });

  return {
    drawRoute,
    myAmapFun,
    getInputtips,
  };
}
