import { Menu } from '@/subpages/cookbook/model/menu';
import { post, get } from '../../../service/http';
import { Dish } from '@/subpages/cookbook/model/dish';
import { TimePeriod } from '@/subpages/cookbook/composables/business';

/**菜品日历 */
function getCalendar() {
	return get<any>('/Holidaycalendar/getMenuHolidaycalendarList');
}

/**获取最新开放的日期 */
function getOpenDate() {
	return get<any>('/menu/getMenuDateList');
}

/**查询所有菜品（菜品库） */
function getDishList(timePeriod?: TimePeriod) {
	return post<Dish[]>('dish/getList', { data: { ifPage: false } });
}

/**
 * 查询菜谱
 * @param menuDate 查询日期：YYYYMMDD
 * @param userId
 * @returns
 */
function getMenuByDate(menuDate: string, userId: string) {
	return get<Menu>(`/menu/getMenuByDate/${menuDate}/${userId}`);
}

/**保存菜谱 */
function saveMenu(data: any) {
	return post<any>('/menu/save', { data });
}

/**修改菜谱菜品 */
function saveMenuDish(data: any) {
	return post<any>('/menudish/save', { data });
}

/**删除菜谱菜品 */
function deleteMenuDish(data: any) {
	return post<any>(`/menudish/syncDelete`, { data, custom: { successTip: '下架成功', errorTip: '下架失败，请重试~' } });
}

/**新增菜谱评论 */
function saveMenuComment(data: any) {
	return post<any>('/menucomment/save', { data, custom: { errorTip: '提交失败，请稍后重试~' } });
}

/**查询菜谱评论
 * @param menuDate 查询日期：YYYYMMDD
 */
function getMenuComment(menuDate: string, userId?: string) {
	return post<any>('/menucomment/getList', { data: { menuDate, ifPage: false, userId } });
}

/**菜品图片上传*/
function uploadDishImage(data: any, dishId?: string) {
	return post<any>(`/dishimage/upload${dishId ? `/${dishId}` : '/'}`, { data: { file: data } });
}

/**批量保存菜谱 */
function saveMenuList(data: any) {
	return post<any>('/menu/batchSave', { data: { menuList: data }, custom: { successTip: '发布成功', errorTip: '发布失败，请重试~' } });
}

/**新增菜品 */
function saveDish(data: any) {
	return post<any>('/dish/save', { data, custom: { successTip: '新增成功' } });
}

/**更新菜品 */
function updateDish(data: any) {
	return post<any>('/dish/update', { data, custom: { successTip: '修改成功' } });
}

/**下架菜品 */
function deleteDish(id: string) {
	return get<any>(`/dish/delete/${id}`, { custom: { successTip: '下架成功', errorTip: '下架失败，请重试~' } });
}

/**菜品详细 */
function getDishDetail(id: string) {
	return get<any>(`/dish/getInfo/${id}`);
}

/**更新通知信息 */
function updateNotice(messageId: string, userId: string) {
	return post<any>('/notifymessage/updateNotifyMessageStatus', { data: { messageId, userId } });
}

export const cookbookService = {
	getOpenDate,
	getCalendar,
	getDishList,
	getMenuByDate,
	saveMenu,
	saveMenuDish,
	deleteMenuDish,
	getMenuComment,
	saveMenuComment,
	uploadDishImage,
	saveDish,
	getDishDetail,
	updateDish,
	deleteDish,
	saveMenuList,
	updateNotice,
};
