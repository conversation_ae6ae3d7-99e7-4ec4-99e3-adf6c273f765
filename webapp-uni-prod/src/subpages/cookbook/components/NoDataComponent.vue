<template>
    <view class="nodata">
        <view class="content">
            <SlSubSvgIcon subpage="cookbook" name="40-40-1" size="40" />
            <text>{{ props.title }}</text>
        </view>
    </view>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
    title?: string
}>(), {
    title: '暂无数据'
    }
)

</script>

<style lang="scss">
    .nodata {
        display: flex;
        justify-content: center;
        align-items: center;
        .content {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
            font-size: 12px;
        }
}
</style>