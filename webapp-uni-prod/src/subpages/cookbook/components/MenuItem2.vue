<template>
    <view class="page" :class="layoutType">
        <view class="search-wrap">
            <up-input v-if="hasSearchInput"
                placeholder="请输入菜名"
                suffixIcon="search"
                suffixIconStyle="color: #909399"></up-input>
        </view>
        <view class="data-item" v-for="(item) in _dataList" :key="item.id">
            <template v-if="hasCheckbox">
                <SlSVgIcon v-if="item.selected"  name="14-14-14" size="14" style="margin-right: 13px" @click="onCheckbox(item, false)" />
                <SlSVgIcon v-else  name="14-14-15" size="14" style="margin-right: 13px" @click="onCheckbox(item, true)" />
            </template>
            <image v-if="item.imageId" :src="item.imageId" mode="aspectFill" :style="{width: sizeUnit, height: sizeUnit}"></image>
            <SlSVgIcon v-else name="70-70-1" :size="70" />
            <view class="content">
                <text>{{ item.name }}</text>
                <view class="rate-wrap" v-if="hasRate">
                    <text class="rate">评分</text>
                    <up-rate :count="5" v-model="item.rating"></ up-rate>
                </view>
            </view>
            <view class="btn-wrap">
                <text class="edit-btn" v-if="hasEdit" @click="edit(item)">修改</text>
                <text class="remove-btn" v-if="hasRemove" @click="remove(item)">下架</text>
            </view>
        </view>
    </view>
    <up-popup class="selected-modal" :show="showSelectedModal" :round="10" :safeAreaInsetBottom="false" @close="showSelectedModal = false">
        <view class="popup-header">
            <text class="title">已选菜品</text>
            <view class="clear-btn" @click="clearAll">
                <SlSVgIcon name="16-16-16" size="16" />
                <text>清空</text>
            </view>
        </view>
        <scroll-view class="scroll-Y" scroll-y>
            <view class="popup-list" v-for="(item) in selectedList" :key="item.id">
            <image v-if="item.imageId" :src="item.imageId" mode="aspectFill" :style="{width: sizeUnit, height: sizeUnit}"></image>
            <SlSVgIcon v-else name="70-70-1" :size="70" />
            <view class="popup-content">
                <text>{{ item.name }}</text>
                <SlSVgIcon name="20-20-24" size="20" @click="onCheckbox(item, false)" />
            </view>
        </view>
        </scroll-view>
        <view class="popup-footer">
            <view class="num">
                <SlSVgIcon name="20-20-23" size="20" />
                <view class="badge">
                    <up-badge max="99" :value="selectedList.length"></up-badge>
                </view>
            </view>
            <text>已选菜品</text>
        </view>
	</up-popup>
    <view class="selected-btn" v-if="hasSelected" @click="showSelectedModal = true">
        <view class="num">
            <SlSVgIcon name="20-20-23" size="20" />
            <view class="badge">
                <up-badge max="99" :value="selectedList.length"></up-badge>
            </view>
        </view>
        <text>已选菜品</text>
    </view>
  </template>

<script setup lang="ts">
import { cookbookService } from '@/service/cookbook/cookbook.service';
import { toRpx } from '@/utils/toRpx';
import { computed, watch,toRef } from "vue";

const props = withDefaults(defineProps<{
    size?: number,
    dataList: any[],
    hasRate?: boolean,
    hasSearchInput?: boolean,
    hasCheckbox?: boolean,
    hasEdit?: boolean
    hasRemove?: boolean,
    layoutType?: 'vertical'|'horizontal' |'column',
}>(), {
    dataList: () => [],
})
const showSelectedModal = ref(false)
const selectedList = ref<any[]>([])
const _dataList = toRef(props, 'dataList')
const sizeUnit = computed(() => toRpx(props.size ?? 70))
const hasSelected = computed(() => selectedList.value.length > 0)


function edit(item: any) {
    uni.navigateTo({
        url:`/subpages/cookbook/pages/manage/handle?id=${item.id}&data=${encodeURIComponent(JSON.stringify(item))}`})
}

function onCheckbox(item: any, checked: boolean) {
    item.selected = checked
    if (checked) {
        selectedList.value.push(item)
    }else {
        selectedList.value.splice(selectedList.value.indexOf(item), 1)
    }
}

function clearAll() {
    _dataList.value.forEach(item => {
        item.selected = false
    })
    selectedList.value = []
    showSelectedModal.value = false
}

async function remove(item: any) {
    try{
        await cookbookService.delMenuDish({id: item.id,dishId: item.dishId,synced: false})
        uni.showToast({
            title: '下架成功',
            icon: 'none'
        })
    }catch(e){
            uni.showToast({
                title: '下架失败，请重试~',
                icon: 'none'
            })
        }
    }

// 深度监听数据变化
watch(() => [...props.dataList], () => {
    console.log('dataList changed', props.dataList)
  // 强制更新
}, { deep: true })
</script>

<style lang="scss" scoped>
.page {
    position: relative;
    display: flex;
    flex-direction: column;
    &.horizontal {
        padding: 10px 15px 50px 15px;
    }
}
.search-wrap {
    margin-bottom: 10px;
}
.data-item {
    display: flex;
    align-items: center;
    position: relative;
    &:not(:last-child) {
        margin-bottom: 20px;
    }
    image {
        border-radius: 6px;
    }
    .content {
        flex: 1;
        padding-left: 20px;
        text-align: left;
        position: relative;
        display: flex;
        flex-direction: column;

        .rate-wrap {
            color: #999999;
            display: flex;
            .rate {
                padding-right: 9px;
            }

        }
    }
 }
 .selected-modal,.selected-btn{
    width: 100%;
    position: fixed;
    background: #fff;
    bottom: 50px;
 }
 .selected-modal {
    max-height: 350px;
    border-radius: 10px 10px 0px 0px;
}

 .selected-btn,
 .popup-footer {
    height: 50px;
    border-radius: 10px 10px 0px 0px;
    display: flex;
    align-items: center;
    justify-content: center;

    box-shadow: 0px 0px 5px 0px #92929233;
    bottom: 0;

    .num {
        margin-right: 10px;
        position: relative;
        .badge {
            position: absolute;
            top: -10px;
            right: -10px;
        }
    }
 }
 .popup-footer {
    border-radius: 0px 0px 10px 10px;
 }
 .btn-wrap {
    position: absolute;
    right: 0;
    bottom: 0;

    .edit-btn,.remove-btn {
        text-align: center;
        border-radius: 3px;
        padding: 4px 10px;
    }
    .edit-btn {
        background: #4F7AF633;
        color: #3968EE;
    }
    .remove-btn {
        background: #C1C2D133;
        color: #666666;
        margin-left: 10px;
        &.disabled {
            background: #FF0B0B33;
            color: #FF0B0B;
        }
    }
 }
 .scroll-Y {
    min-height: 0;
    max-height: 260px;
 }
 .popup-header {
    height: 40px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #99999933;
    .title {
        font-weight: bold;
    }
    .clear-btn {
        color: #666666;
        display: flex;
        height: 20px;
        text {
            padding-left: 4px;
            margin-top: -2px;
        }
    }
}
.popup-list {
    height: 70px;
    margin-top: 15px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &:first-child {
        margin-top: 10px;
    }
    &:last-child {
        margin-bottom: 10px;
    }
    .popup-content {
        flex: 1;
        padding-left: 20px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}
 :deep(.u-input) {
     padding: 0 9px !important;
 }
 :deep(.u-input__content) {
     height: 30px;
 }

</style>
