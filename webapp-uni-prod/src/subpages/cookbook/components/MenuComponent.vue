<template>
    <view class="page" :class="{column:layoutType==='column', horizontal:layoutType==='horizontal', vertical:layoutType==='vertical'}">
        <view class="list tab-list">
            <view class="tab-item" v-for="(item, index) in tabList" :key="index" :class="{active:currTabIndex===index}" @click="currTabIndex=index">
                <text class="tab-text">{{ item.name }}</text>
                <view v-if="layoutType=='column'" class="tab-datas">
                <menuItem :dataList="item.uuid==='1'?breakfastList:lunchList" :size="size" :hasRate="hasRate">
                </menuItem>
                </view>
            </view>
        </view>
        <view class="data-list" v-if="layoutType!=='column'">
            <scroll-view class="scroll-Y" scroll-y >
                <MenuItem :dataList="currDataList"></MenuItem>
            </scroll-view>
        </view>
    </view>
</template>

<script setup lang="ts">
import MenuItem from "./MenuItem.vue";
import { ref } from "vue";

interface TabList {
    name: string;
    uuid: string;
}
const { tabList,allDatas, layoutType } = withDefaults(defineProps<{
    size?: number,
    hasRate?: boolean,
  tabList?: TabList[],
    allDatas?: any[],
  layoutType?: 'vertical'|'horizontal' |'column'
}>(), {
    tabList: () => [ { name: '早餐', uuid: '1' }, { name: '午餐', uuid: '2' } ],
    allDatas: () => [],
    layoutType: 'horizontal'
})
/**激活的tab */
const currTabIndex = ref( 0 );
const currDataList = computed(() => {
    let currId = tabList[currTabIndex.value].uuid
    return allDatas.filter(item => item.uuid === currId)
})
const breakfastList = computed(() => {
    return allDatas.filter(item => item.uuid === '1')
})
const lunchList = computed(() => {
    return allDatas.filter(item => item.uuid === '2')
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    height: 100%;
    background: #fff;
    display: flex;
    &.vertical {
        .list {
            display: flex;
            align-items: center;
            position: relative;
        }
        .tab-list {
            height: 100%;
        }

        .tab-item.active {
            &::before {
                left: 10px;
                transform: translateX(-50%);
                width: 3px;
                height: 20px;
            }
        }
    }
    &.vertical,&.horizontal {
        box-shadow: 0px 0px 4px 0px #92929233;

        .tab-item.active {
            color: #333333;
            font-weight: bold;
            background: #fff;
            &::before {
                content: "";
                position: absolute;
                background: #005CC8;
            }
        }

        .tab-list {
            background: #F8F8F8;
        }

        .tab-item{
            width: 78px;
            text-align: center;
            display: inline-block;
        }
    }
    &.horizontal,&.column {
        .tab-list {
            flex-flow: column;
        }
    }
    &.horizontal,&.column {
        flex-direction: column;
    }
    &.horizontal {
        .tab-item.active {
            &::before {
                bottom: 0px;
                left: 50%;
                transform: translateX(-50%);
                width: 20px;
                height: 3px;
            }
        }
    }
    &.column {
        padding-left: 18px;

        .tab-text {
            position: relative;
            display: flex;
            &:after {
                content: "";
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 85%;
                height: 0.5px;
                background: #99999993;
            }
        }
        .tab-datas {
            padding-left: 48px;
        }
    }
}
.list {
    position: relative;
}
.tab-list {
    color: #636363;
    .tab-item{
        padding: 10px 0;
        position: relative;
    }

}
.data-list {
 width: calc(100% - 78px);
 padding: 15px 7px;
 .scroll-Y {
    height: 100%;
    min-height: 0;
 }
}
</style>
