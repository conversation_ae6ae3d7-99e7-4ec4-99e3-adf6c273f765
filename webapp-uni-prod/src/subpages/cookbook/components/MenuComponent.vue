<template>
    <view class="page" :class="{column:layoutType==='column', horizontal:layoutType==='horizontal', vertical:layoutType==='vertical'}">
        <view class="list tab-list">
            <text class="tab-item" v-for="(item, index) in tabList" :key="index" :class="{active:currTabIndex===index}" @click="currTabIndex=index">
                <text>{{ item.name }}</text>
            </text>
        </view>
    <view class="data-list">
        <scroll-view class="scroll-Y" scroll-y >
            <view class="data-item" v-for="item in dataList" :key="item.id">
            <image src="@/static/images/cookbook/banner.png" mode="aspectFill"></image>
            <view class="content">
                <text>{{ item.name }}</text>
            </view>
        </view>
        </scroll-view>
    </view>
    </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

interface TabList {
    name: string;
    uuid: string;
}
const { tabList,allDatas, layoutType } = withDefaults(defineProps<{
    foo?: string
  tabList?: TabList[]
    allDatas?: any[],
  layoutType?: 'vertical'|'horizontal' |'column'
}>(), {
    tabList: () => [ { name: '早餐', uuid: '1' }, { name: '午餐', uuid: '2' } ],
    allDatas: () => [],
    layoutType: 'horizontal'
})
/**激活的tab */
const currTabIndex = ref( 0 );
const dataList = computed(() => {
    let currId = tabList[currTabIndex.value].uuid
    return allDatas.filter(item => item.uuid === currId)
})
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    height: 100%;
    background: #fff;
    display: flex;
    box-shadow: 0px 0px 4px 0px #92929233;
    &.horizontal {
        .tab-list {
            flex-flow: column;
        }
    }
}
.list {
    display: flex;
    align-items: center;
    position: relative;
}
.tab-list {
    height: 100%;
    color: #636363;
    background: #F8F8F8;
    .tab-item{
        width: 78px;
        padding: 10px 0;
        text-align: center;
        position: relative;
        &.active {
        color: #333333;
        font-weight: bold;
        background: #fff;
        &::before {
            content: "";
            position: absolute;
            left: 10px;
            transform: translateX(-50%);
            width: 3px;
            height: 20px;
            background: #005CC8;
        }
    }
    }

}
.data-list {
 width: calc(100% - 78px);
 padding: 15px 7px;
 .scroll-Y {
    height: 100%;
    min-height: 0;
 }

 .data-item {
    height: 70px;
    display: flex;
    align-items: center;
    &:not(:last-child) {
        margin-bottom: 20px;
    }
    image {
        width: 70px;
        height: 70px;
        border-radius: 6px;
    }
    .content {
        flex: 1;
        padding-left: 20px;
        text-align: left;
        position: relative;
    }
 }
}
</style>
