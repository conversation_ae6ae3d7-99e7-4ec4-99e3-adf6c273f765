<template>
    <view class="data-item" v-for="item in dataList" :key="item.id">
        <image src="@/static/images/cookbook/banner.png" mode="aspectFill" :style="{width: sizeUnit, height: sizeUnit}"></image>
        <view class="content">
            <text>{{ item.name }}</text>
            <view class="rate-wrap" v-if="hasRate"><text class="rate">评分</text><up-rate :count="5" v-model="item.score"></up-rate></view>
        </view>
    </view>
  </template>

<script setup lang="ts">
import { toRpx } from '@/utils/toRpx';

const { dataList,size } = withDefaults(defineProps<{
    size?: number,
    dataList?: any[],
    hasRate?: boolean
}>(), {
})
const sizeUnit = computed(() => toRpx(size ?? 70))

const defaultImg = '@/static/images/cookbook/banner.png'
</script>

<style lang="scss" scoped>
.data-item {
    display: flex;
    align-items: center;
    &:not(:last-child) {
        margin-bottom: 20px;
    }
    image {
        border-radius: 6px;
    }
    .content {
        flex: 1;
        padding-left: 20px;
        text-align: left;
        position: relative;
        display: flex;
        flex-direction: column;

        .rate-wrap {
            color: #999999;
            display: flex;
            .rate {
                padding-right: 9px;
            }
        }
    }
 }
</style>
