<template>
    <view class="page" :class="layoutType">
        <view class="search-wrap">
            <up-input v-model="_search" @change="searchChange" v-if="hasSearchInput" placeholder="请输入菜名" :customStyle="{ padding: '6px 8px',height: '30px' }">
                <template #suffix>
                    <SlSubSvgIcon v-if="_search" subpage="cookbook" class="search-clear-icon" name="12-12-10" size="12" @click="onClear" />
                    <SlSubSvgIcon subpage="cookbook" class="search-icon" name="16-16-6" size="16" @click="onSearch" />
                </template>
            </up-input>
        </view>
        <view class="data-item" v-for="(item) in _dataList" :key="item.id">
            <template v-if="hasCheckbox">
                <SlSubSvgIcon subpage="cookbook" v-if="ifChecked(item)"  name="14-14-14" size="14" style="margin-right: 13px" @click="onCheckbox(item, false)" />
                <SlSubSvgIcon subpage="cookbook" v-else  name="14-14-15" size="14" style="margin-right: 13px" @click="onCheckbox(item, true)" />
            </template>
            <ImageComponent :src="item.imageUrl" :size="imgSize"></ImageComponent>
            <view class="content">
                <text>{{ item.name }}</text>
                <view class="rate-wrap" v-if="hasRate">
                    <text class="rate">评分</text>
                    <up-rate :count="5" :min-count="0" v-model="item.rating"></ up-rate>
                </view>
            </view>
            <view class="btn-wrap">
                <text class="edit-btn" v-if="hasEdit" @click="edit(item)">修改</text>
                <text class="remove-btn" hover-class="btn-hover" v-if="hasRemove" @click="clickRemove(item)">下架</text>
            </view>
        </view>
    </view>
    <Popup v-if="showRemoveTip" icon="20-20-23" iconSize="32" text="您确定下架该菜品吗？" :btnList="btnList"></Popup>
  </template>

<script setup lang="ts">
import ImageComponent from '@/components/ImageComponent.vue';
import Popup from '@/subpages/cookbook/components/Popup.vue';
import { toRpx } from '@/utils/toRpx';
import { computed, watch,toRef } from "vue";
import { useAccount } from '../composables/business';
import { cookbookService } from '@/service/cookbook/cookbook.service';
const {getAccount} = useAccount()

const props = withDefaults(defineProps<{
    isManager?: boolean,
    imgSize?: number,
    search: string,
    dataList: any[],
    selectedList?: any[],
    hasRate?: boolean,
    hasSearchInput?: boolean,
    hasCheckbox?: boolean,
    hasEdit?: boolean
    hasRemove?: boolean,
    layoutType?: 'vertical'|'horizontal' |'column',
}>(), {
    search: () => '',
    dataList: () => [],
    selectedList: () => [],
})

const btnList = [
        {
            text: '取消',
            type: 'close',
            click: () => {
                showRemoveTip.value = false
            }
        },
        {
            text: '确定',
            type: 'sure',
            click: () => {
                remove()
            }
        }
    ]
    let removeId = ref('');
    let showRemoveTip = ref(false);
    let _search = ref('')
const _dataList = toRef(props, 'dataList')
const sizeUnit = computed(() => toRpx(props.imgSize ?? 70))

const searchChange = (text: string) => {
  _search.value = text;
};

//发送给父组件
const emits = defineEmits(['selectedChange','refresh','searchChange']);

function ifChecked(item: any) {
    return props.selectedList?.some((selectedItem: any) => selectedItem.id === item.id)
}

function edit(item: any) {
    uni.navigateTo({
        url:`/subpages/cookbook/pages/manage/handle?id=${item.id}`})
}

function clickRemove(item: any) {
    showRemoveTip.value = true;
    removeId.value = item.id;
  }

  async function remove() {
    await cookbookService.deleteDish(removeId.value);
    showRemoveTip.value = false;
    emits('refresh')
  }

function onCheckbox(item: any, checked: boolean) {
    if (checked) {
        emits('selectedChange', {type:'add',data:item})
    }else {
        emits('selectedChange', {type:'remove',data:item})
    }
}

function onClear() {
    _search.value = ''
    emits('searchChange', _search.value)
}

function onSearch() {
    emits('searchChange', _search.value)
}

onMounted(() => {
   getAccount()
})

watch(() => props.search, () => {
    _search.value = props.search
})
// 深度监听数据变化
watch(() => [...props.dataList], () => {
    console.log('dataList changed', props.dataList)
  // 强制更新
}, { deep: true })
watch(() => [...props.selectedList], () => {
    console.log('selectedList changed', props.selectedList)
  // 强制更新
}, { deep: true })
</script>

<style lang="scss" scoped>
.page {
    position: relative;
    display: flex;
    flex-direction: column;
    &.horizontal {
        padding: 10px 15px 50px 15px;
    }
    &.column {
        .data-item {
            padding-left: 40px;
        }
    }
}
.search-wrap {
    margin-bottom: 10px;
    .search-icon {
        margin-left: 10px;
    }
}

.data-item {
    display: flex;
    align-items: center;
    position: relative;
    &:not(:last-child) {
        margin-bottom: 20px;
    }
    image {
        border-radius: 6px;
    }
    .content {
        flex: 1;
        padding-left: 20px;
        text-align: left;
        position: relative;
        display: flex;
        flex-direction: column;

        .rate-wrap {
            color: #999999;
            display: flex;
            .rate {
                padding-right: 9px;
            }

        }
    }
 }
 .btn-wrap {
    position: absolute;
    right: 0;
    bottom: 0;

    .edit-btn,.remove-btn {
        text-align: center;
        border-radius: 3px;
        padding: 4px 10px;
    }
    .edit-btn {
        background: #4F7AF633;
        color: #3968EE;
    }
    .remove-btn {
        background: #C1C2D133;
        color: #666666;
        margin-left: 10px;
        &.btn-hover {
            color: #FF0B0B;
            background: #FF0B0B33;
        }
        &.disabled {
            background: #FF0B0B33;
            color: #FF0B0B;
        }
    }
 }
 .scroll-Y {
    min-height: 0;
    max-height: 260px;
 }
 .popup-header {
    height: 40px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #99999933;
    .title {
        font-weight: bold;
    }
    .clear-btn {
        color: #666666;
        display: flex;
        height: 20px;
        text {
            padding-left: 4px;
            margin-top: -2px;
        }
    }
}
.popup-list {
    height: 70px;
    margin-top: 15px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &:first-child {
        margin-top: 10px;
    }
    &:last-child {
        margin-bottom: 10px;
    }
    .popup-content {
        flex: 1;
        padding-left: 20px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}

</style>
