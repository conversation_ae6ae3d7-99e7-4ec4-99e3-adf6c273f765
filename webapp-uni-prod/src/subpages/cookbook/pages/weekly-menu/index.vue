<template>
    <view class="page">
        <image class="banner" :src="appStore.getBaseUrl + '/menu_banner.jpg'" mode="aspectFill"></image>
        <view class="calendar">
            <CalendarComponent v-model="currDate" @dateChange="onDateChange" :date-data="canlendarList">
            </CalendarComponent>
        </view>
        <MenuComponent class="menu" layoutType="vertical" :tabGroup="tabGroup" :currTab="currTab" :size="70"
            :noDataText="noDataText" @currentTabChange="handleTabChange"></MenuComponent>
        <SlSubSvgIcon v-if="!isEmptyList" subpage="cookbook" class="opt-btn"
            :name="`52-52-${canEvaluate ? '2' : '3'}`" size="45" @click="clickOpt" />
    </view>
    <ConfirmDialog width="0" styleObj="{height: 0, background: 'transparent'}" v-if="showTip" :showHeader="false"
        :showFooter="false">
        <template>
            <view class="dialog-content">
                <SlSubSvgIcon subpage="cookbook" class="svg" name="260-260-1" size="260" sizeH="283"></SlSubSvgIcon
                    subpage="cookbook">
                <view class="dialog-box">
                    <SlSubSvgIcon subpage="cookbook" class="star" name="80-80-1" size="68" iconPath="images" />
                    <view class="text-box">
                        <text class="text">暂不可评</text>
                        <text class="text" v-if="isToday">请您<text class="time">11:30</text>之后再来评价哦！</text>
                    </view>
                    <text class="btn" @click="showTip = false">好的，我知道了</text>
                </view>
            </view>
        </template>
    </ConfirmDialog>
</template>
<script setup lang="ts">
import MenuComponent from '@/subpages/cookbook/components/MenuComponent.vue';
import { useAccount, useDate, useTab } from "../../composables/business";
import { cookbookService } from '@/subpages/cookbook/composables/cookbook.service';
import { useWxSubscribe } from '@/hooks';
const { currDate, backDate, canlendarList, isBeforeDay, formateDate, getCalendar, onDateChange } = useDate()
const { userId, getAccount } = useAccount()
const { currTab, tabGroup, computeEmpty, isEmptyList, handleTabChange } = useTab()
import { useAppStore } from "@/store";
const appStore = useAppStore();
const canEvaluate = ref(false);
const isToday = ref(false)
const showTip = ref(false)
const isPublished = ref(false);
const noDataText = ref('暂无菜品');
const newDateList = ref<any>([]);

// 计算当前是否在评价时间段(今天11:30后才返回true)
const isEvaluateTime = () => {
    const now = new Date()
    const evaluateEndDate = new Date(currDate.value);
    evaluateEndDate.setHours(23, 59, 59, 999);
    isToday.value = now.toDateString() === evaluateEndDate.toDateString();
    // 检查是否在11:30之后
    const hours = now.getHours()
    const minutes = now.getMinutes()
    const isAfter1130 = hours > 11 || (hours === 11 && minutes >= 30);
    return isToday.value && isAfter1130;
}

// 点击评价按钮
const clickOpt = () => {
    if (isBeforeDay() || isEvaluateTime()) {
        uni.navigateTo({ url: `/subpages/cookbook/pages/weekly-menu/menu-rate?date=${backDate.value}` })
    } else {
        showTip.value = true
    }

}
onMounted(() => {
    cookbookService.getOpenDate().then((res: any) => {
        newDateList.value = res;
    });
    getCalendar()
    useWxSubscribe(['每周菜谱更新通知'])
    getAccount().then((res: any) => {
        getList(backDate.value)
    })
})

onPullDownRefresh(async () => {
    try {
        await getList(backDate.value)
    } finally {
        uni.stopPullDownRefresh()
    }
})

async function getList(date: string) {
    try {
        canEvaluate.value = false
        let res: any = await cookbookService.getMenuByDate(date, userId.value);
        let commentRes: any = await cookbookService.getMenuComment(date, userId.value)
        const isRated = commentRes.length ? true : false
        // 未评价且在评价时间段
        canEvaluate.value = !isRated && (isBeforeDay() || isEvaluateTime())
        const { morningMenu, afternoonMenu } = res,
            { published, menuDishList: morningDishList } = morningMenu,
            { menuDishList: afternoonDishList } = afternoonMenu;
        noDataText.value = published && (morningDishList.length < 1 || afternoonDishList.length < 1) ? '今日休息' : '暂无菜品';
        isPublished.value = published;
        tabGroup.value[0].menuDishList = morningMenu.menuDishList.map((item: any) => ({ ...item, name: item.dishName }))
        tabGroup.value[0].orginMenuDishList = morningMenu.menuDishList.slice()
        tabGroup.value[1].menuDishList = afternoonMenu.menuDishList.map((item: any) => ({ ...item, name: item.dishName }))
        tabGroup.value[1].orginMenuDishList = morningMenu.menuDishList.slice()
        isEmptyList.value = computeEmpty()
    } catch (e) {
        isPublished.value = false;
        noDataText.value = isBeforeDay(formateDate(newDateList.value[newDateList.value.length - 1].menuDate)) ? '今日休息' : '暂无菜品';
        tabGroup.value[0].menuDishList = []
        tabGroup.value[1].menuDishList = []
        isEmptyList.value = true
    }
}

watch(backDate, () => {
    getList(backDate.value)
})
</script>
<style lang="scss" scoped>
.page {
    width: 100%;
    height: 100vh;
    position: relative;
    background: #F8F8F8;
    display: flex;
    flex-direction: column;
}

.banner {
    width: 100%;
    height: 120px;
}

.calendar {
    background: #fff;
    margin-bottom: 10px;
}

.menu {
    flex: 1;
    min-height: 0;
    overflow: hidden;
}

.opt-btn {
    position: fixed;
    bottom: 63px;
    right: 14px;
}

.dialog-content {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-top: -40px;

    .dialog-box {
        position: absolute;
        width: 260px;
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 14px;
    }
}

.text {
    line-height: 20px;
}

.time {
    font-weight: bold;
}

.btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    margin-top: 40px;
    color: #fff;
    text-align: center;
    border-radius: 24px;
    background: linear-gradient(111deg, #006AE8 0%, #0041F4 100%);
}

.svg {
    position: absolute;
}

.star {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
}

.text-box {
    display: flex;
    flex-direction: column;
    margin-top: 60px;
}
</style>