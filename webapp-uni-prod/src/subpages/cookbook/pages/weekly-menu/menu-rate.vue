<template>
	<view class="page">
		<MenuComponent
			class="menu"
			:tabGroup="tabGroup"
			layoutType="column"
			:imgSize="60"
			:hasRate="true"
			:readonlyRate="isRated"
		>
		</MenuComponent>
		<view class="total-rate">
			<text class="title">总体评价</text>
			<up-textarea
				v-model="pageData.comment"
				placeholder="亲，描述一下菜品口感怎么样吧，帮助我们更好的改善菜品哦~"
				count
				:height="140"
				:disabled="isRated"
				:placeholderStyle="{ color: '#b6b6b6', fontSize: '14px', 'line-height': 'normal', 'letter-spacing': '0px' }"
				maxlength="50"
			></up-textarea>
		</view>
		<BaseButton
			v-if="!isRated"
			class="save"
			size="large"
			:disabled="!isValidateSuccess"
			@click="save"
			>提交评价</BaseButton
		>
	</view>
	<Popup
		v-if="showTip"
		iconPath="images"
		:btnList="btnList"
	></Popup>
</template>

<script setup lang="ts">
	import Popup from '@/subpages/cookbook/components/Popup.vue';
	import MenuComponent from '@/subpages/cookbook/components/MenuComponent.vue';
	import { useAccount, useTab } from '../../composables/business';
	import { MenuComment, TabGroup } from '../../model/menu';
	import { cookbookService } from '@/subpages/cookbook/composables/cookbook.service';
	onLoad((options: any) => {
		date.value = options.date;
		getAccount().then((res: any) => {
			getList(date.value);
		});
	});
	const date = ref('');
	const { tabGroup } = useTab();
	const { userId, getAccount } = useAccount();
	//已评价
	const isRated = ref(false);
	//校验是否可提交评价
	const isValidateSuccess = computed(() => {
		return !!pageData.value.comment || tabGroup.value.some((e: TabGroup) => e.menuDishList.some((item: any) => item.rating > 0));
	});
	const showTip = ref(false);
	const pageData = ref<MenuComment>({});
	const btnList = [
		{
			text: '确定',
			type: 'sure',
			click: () => {
				showTip.value = false;
				uni.navigateBack();
			},
		},
	];

	async function save() {
		let ratingList: any = [];
		tabGroup.value.forEach((e: TabGroup) => {
			ratingList.push(
				...e.menuDishList.filter((item: any) => item.rating > 0).map((item: any) => ({ dishId: item.dishId, rating: item.rating + '' }))
			);
		});
		Object.assign(pageData.value, {
			menuDate: date.value,
			userId: userId.value,
			ratingList: ratingList,
		});
		try {
			await cookbookService.saveMenuComment(pageData.value);
			isRated.value = true;
			showTip.value = true;
		} catch (e) {}
	}

	onMounted(() => {});
	async function getList(date: string) {
		try {
			let res: any = await cookbookService.getMenuComment(date, userId.value);
			if (res.length) {
				isRated.value = true;
				pageData.value = res[0];
				tabGroup.value[0].menuDishList = res[0].ratingList
					.filter((e: any) => e.timePeriod === 'morning' && e.rating > 0)
					.map((item: any) => ({ ...item, name: item.dishName }));
				tabGroup.value[1].menuDishList = res[0].ratingList
					.filter((e: any) => e.timePeriod === 'afternoon' && e.rating > 0)
					.map((item: any) => ({ ...item, name: item.dishName }));
			} else {
				isRated.value = false;
				res = await cookbookService.getMenuByDate(date, userId.value);
				const { morningMenu, afternoonMenu } = res;
				tabGroup.value[0].menuDishList = morningMenu.menuDishList.map((item: any) => ({ ...item, name: item.dishName, rating: 0 }));
				tabGroup.value[1].menuDishList = afternoonMenu.menuDishList.map((item: any) => ({ ...item, name: item.dishName, rating: 0 }));
			}
		} catch (e) {
			isRated.value = false;
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		padding-bottom: 30px;
	}
	.total-rate {
		padding: 10px 15px 30px 15px;
		.title {
			color: #636363;
			display: inline-block;
			margin-bottom: 10px;
		}
	}
	/* 使用深度选择器穿透组件 */
	:deep(.total-rate .u-textarea),
	:deep(.total-rate .u-textarea__count) {
		color: #b6b6b6;
		background-color: #f5f5f5 !important;
	}
	:deep(.total-rate .u-textarea) {
		border-radius: 10px !important;
	}

	.dialog-content {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		width: 290px;
		height: 80px;
		.star {
			position: absolute;
			top: 10px;
		}
		.text {
			font-size: 14px;
			display: block;
			position: absolute;
		}
		.btn {
			position: absolute;
			bottom: 0;
			display: block;
			width: 100%;
			height: 40px;
			line-height: 40px;
			color: #3e6ce1;
			border-top: 1px solid #d8d8d8;
			font-weight: bold;
		}
	}

	:deep(.textarea-placeholder) {
		color: #b6b6b6;
		font-size: 14px;
		line-height: 20px;
	}
</style>
