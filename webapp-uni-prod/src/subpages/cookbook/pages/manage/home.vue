<template>
    <SlTabbarPage :tab-bar-root="'subpages/cookbook'" admin>
        <view class="page">
            <view class="calendar">
                <CalendarComponent v-model="currDate" @dateChange="onDateChange" :date-data="canlendarList"></CalendarComponent>
            </view>
            <MenuComponent class="menu" :tabGroup="tabGroup" :currTab="currTab" layoutType="horizontal" :hasSearchInput="!isPublished" :hasCheckbox="!isPublished" :hasEdit="!isPublished" @selectedChange="onSelectedChange" @currentTabChange="handleTabChange" @searchChange="onSearchChange" ></MenuComponent>
            <SlSubSvgIcon class="opt-btn" v-if="hasIssueBtn" subpage="cookbook"  name="52-52-5" size="45" @click="clickIssue" />
            <view class="sticky-wrapper sticky1">
                <view class="selected-btn" v-if="currSelectedList.length" @click="showSelectedModal = true">
                    <view class="num">
                        <SlSubSvgIcon subpage="cookbook" name="20-20-23" size="20" />
                        <view class="badge">
                            <up-badge max="99" :value="currSelectedList.length"></up-badge>
                        </view>
                    </view>
                    <text>已选菜品</text>
                </view>
            </view>
    </view>
    <Popup v-if="showIssueTip" icon="20-20-23" iconSize="32" :text="issueText" :btnList="issueBtnList"></Popup>
    <Popup v-if="showMessage" icon="20-20-23" iconSize="32" text="下周的日期已开放，可前往配置菜谱啦~" :btnList="btnList"></Popup>
    <up-popup class="selected-modal" :show="showSelectedModal" :round="10" :safeAreaInsetBottom="true" @close="showSelectedModal = false">
        <view class="popup-header">
            <text class="title">已选菜品</text>
            <view class="clear-btn" @click="clearAll">
                <SlSubSvgIcon class="clear-icon" subpage="cookbook" name="16-16-16" size="16" />
                <text>清空</text>
            </view>
        </view>
        <scroll-view class="scroll-Y" scroll-y>
            <view class="popup-list" v-for="(item) in currSelectedList" :key="item.id">
            <image v-if="item.imageId" :src="item.imageId" mode="aspectFill" :style="{width: sizeUnit, height: sizeUnit}"></image>
            <SlSubSvgIcon subpage="cookbook" v-else name="70-70-1" :size="70" />
            <view class="popup-content">
                <text>{{ item.name }}</text>
                <SlSubSvgIcon subpage="cookbook" name="20-20-24" size="20" @click="onCheckbox(item, false)" />
            </view>
        </view>
        </scroll-view>
        <view class="popup-footer">
            <view class="num">
                <SlSubSvgIcon subpage="cookbook" name="20-20-23" size="20" />
                <view class="badge">
                    <up-badge max="99" :value="currSelectedList.length"></up-badge>
                </view>
            </view>
            <text>已选菜品</text>
        </view>
	</up-popup>
    </SlTabbarPage>
</template>
<script setup lang="ts">
import Popup from '@/subpages/cookbook/components/Popup.vue';
import MenuComponent from '@/subpages/cookbook/components/MenuComponent.vue';
import { useRefreshStore } from '../../composables/store';
import { useDate,useTab,useAccount } from "../../composables/business";
import { cookbookService } from '@/service/cookbook/cookbook.service';
import { toRpx } from '@/utils/toRpx';
const { userId, getAccount } = useAccount()
const refreshStore = useRefreshStore()
const { currTab,tabGroup,handleTabChange,onSearchChange } = useTab()

const showSelectedModal = ref(false)
const showIssueTip = ref(false)
const issueText = ref('')
const issueBtnList = [
    {
            text: '确定',
            type: 'close',
            click: () => {
                showIssueTip.value = false
                issue()
            }
        },
        {
            text: '继续选择',
            type: 'sure',
            click: () => {
                showIssueTip.value = false
            }
        }
]


const { currDate,backDate,canlendarList,formateDate, getCalendar, onDateChange } = useDate()
const btnList = [
        {
            text: '关闭',
            type: 'close',
            click: () => {
                showMessage.value = false
            }
        },
        {
            text: '立即前往',
            type: 'sure',
            click: () => {
                backDate.value = newDateList.value[0].menuDate
                currDate.value = formateDate(backDate.value)
                console.log('===',backDate.value, currDate.value)
                showMessage.value = false
            }
        }
    ]
    const message = ref<any>({})
    const showMessage = ref(false)
    const isPublished = ref(false)
    const publishMap = ref<any>(new Map())
    const newDateList = ref<any>([])
    const sizeUnit = computed(() => toRpx(70))
    const hasIssueBtn = computed(() => {
        let menuList: any = getSaveList()
        return publishMap.value.size>0 && menuList.some((e: any) => e.menuDishList.length>0)
    })

    function clickIssue() {
        const keys = Array.from(publishMap.value.keys());
        const text: any = newDateList.value.map((e: any) => {
            if(!keys.includes(e.menuDate)) {
                return e.dayOfWeek
            }
        }).filter((e: any) => e).join('、');
        if(!text) {
            issue()
        }else {
            issueText.value = `您还没有选择${text}的菜谱，确定要发布了吗？`
            showIssueTip.value = true
        }
    }

async function issue() {
    let menuList: any = getSaveList()
    // console.log('menuList==',menuList)
    await cookbookService.saveMenuList(menuList)
    getList(backDate.value)
    publishMap.value = new Map()
    cookbookService.updateNotice(message.value.id,userId.value)
}

function getSaveList() {
    let menuList: any = [];
    publishMap.value.forEach((value: any, key:string) => {
        menuList.push(...value)
    })
    return menuList
}

function onCheckbox(item: any, checked: boolean) {
    const index = currTab.value==='morning'?0:1
    if (checked) {
        tabGroup.value[index].selectedList.push(item)
    } else {
        tabGroup.value[index].selectedList = tabGroup.value[index].selectedList.filter((e: any) => e.id !== item.id)
    }
    onSelectedChange({group:tabGroup.value})
}

onMounted(() => {
    cookbookService.getOpenDate().then((res: any) => {
        newDateList.value = res
    })
    getCalendar()
    getAccount().then((res: any) => {
        getList(backDate.value)
    })
})

async function getList(date: string) {
    try{
        const res: any = await cookbookService.getMenuByDate(date,userId.value)
    const {morningMenu,afternoonMenu,notifyMessage} =res,{published} = morningMenu
    isPublished.value = published
    const dishList: any = await cookbookService.getDishList()

    tabGroup.value[0].menuDishList = published?morningMenu.menuDishList.map((item: any) => ({...item,name:item.dishName})):dishList.filter((dish: any) => dish.timePeriod === 'morning')
    tabGroup.value[0].orginMenuDishList = tabGroup.value[0].menuDishList.slice()
    tabGroup.value[0].selectedList = getSelectedList()
    tabGroup.value[1].menuDishList = published?afternoonMenu.menuDishList.map((item: any) => ({...item,name:item.dishName})):dishList.filter((dish: any) => dish.timePeriod === 'afternoon')
    tabGroup.value[1].orginMenuDishList = tabGroup.value[1].menuDishList.slice()
    tabGroup.value[1].selectedList = getSelectedList()

    message.value = notifyMessage
    showMessage.value = !!notifyMessage
    }catch(e){
        isPublished.value = false
        tabGroup.value[0].menuDishList = []
        tabGroup.value[1].menuDishList = []
        tabGroup.value[0].selectedList = []
        tabGroup.value[1].selectedList = []
    }
    }

function onSelectedChange(e: any) {
    const {group} = e
    publishMap.value.set(backDate.value,[{
        "menuDate": backDate.value,
        timePeriod: 'morning',
        menuDishList: group[0].selectedList.map((item: any) => ({...item,dishId:item.id,dishName:item.name}))
    },{
        "menuDate": backDate.value,
        timePeriod: 'afternoon',
        menuDishList: group[1].selectedList.map((item: any) => ({...item,dishId:item.id,dishName:item.name}))
    }])
    console.log('publishMap==',publishMap.value)
}

function clearAll() {
    tabGroup.value.forEach(item => {
        item.selectedList = []
    })
    publishMap.value.delete(backDate.value)
    showSelectedModal.value = false
}

const currSelectedList = computed(() => {
    return getSelectedList()
})

function getSelectedList() {
    const value = publishMap.value.get(backDate.value)
    const e = value?.find((item: any) => item.timePeriod === currTab.value)
    return e?.menuDishList || []
}

watch(backDate, () => {
    getList(backDate.value)
})
onShow(() => {
    if (refreshStore.needRefresh.dishList){
        setTimeout(() => {
            getList(backDate.value)
            refreshStore.setNeedRefresh('dishList', false) // 重置状态
        })
    }
})
</script>
<style lang="scss" scoped>
.page {
    width: 100%;
    height: 100%;
    position: relative;
    background: #F8F8F8;
    display: flex;
    flex-direction: column;
    overflow-y: scroll;

    .opt-btn {
        position: fixed;
        bottom: 140px;
        right: 14px;
    }

    .sticky-wrapper {
        position: sticky;
        &.sticky0 {
            bottom: 62px;
            background: #fff;
            text-align: right;
            padding-right: 14px;
        }
        &.sticky1 {
            bottom: 0;
        }
    }

    .calendar {
        background: #fff;
        margin-bottom: 10px;
    }
}
.menu {
    flex: 1;
    min-height: 0;
    overflow: hidden;
}

.notify-content {
    position: relative;
}
.dialog-content {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .dialog-box {
        position: absolute;
    width: 260px;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 14px;
}
    }
    .text {
        line-height: 20px;
    }
    .time {
        font-weight: bold;
    }
    .btn {
        width: 200px;
        height: 40px;
        line-height: 40px;
        margin-top: 40px;
    color: #fff;
    text-align: center;
    border-radius: 24px;
    background: linear-gradient(111deg, #006AE8 0%, #0041F4 100%);
    }

.svg {
    position: absolute;
}

.star {
    position: absolute;
    top: -24px;
        left: 50%;
        transform: translateX(-50%);
}
.text-box {
    display: flex;
        flex-direction: column;
        margin-top: 60px;
}
.nodata {
    height:100%;
    flex: 1;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
}
.selected-modal,.selected-btn{
    width: 100%;
    background: #fff;
 }
 .selected-modal {
    border-radius: 10px 10px 0px 0px;
    .popup-header {
        height: 40px;
        border-bottom: 1px solid #99999933;
        display: flex;
        align-items: center;
        padding: 0 15px;
        justify-content: space-between;
        .title {
            font-weight: bold;
        }
        .clear-btn {
            color: #666666;
            display: flex;
            align-items: center;
            .clear-icon {
                margin-top: 4px;
                margin-right: 4px;
            }
        }
    }
}

.popup-footer {
    bottom: 0;

}

 .selected-btn,
 .popup-footer {
    height: 50px;
    border-radius: 10px 10px 0px 0px;
    display: flex;
    align-items: center;
    justify-content: center;

    box-shadow: 0px 0px 5px 0px #92929233;

    .num {
        margin-right: 10px;
        position: relative;
        .badge {
            position: absolute;
            top: -10px;
            right: -10px;
        }
    }
 }
 .popup-footer {
    border-radius: 0px 0px 10px 10px;
 }

 .scroll-Y {
    max-height: 270px;
    .popup-list {
        display: flex;
        padding: 0 15px;
        margin-top: 15px;
        &:first-child {
            margin-top: 10px;
        }
        &:last-child {
            margin-bottom: 10px;
        }
        .popup-content {
            flex: 1;
            padding-left: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }
 }
</style>