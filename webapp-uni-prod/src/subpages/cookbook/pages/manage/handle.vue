<template>
  <view class="page">
    <view class="row name">
      <text class="label">菜名</text>
      <view>
        <up-input
          v-model="pageData.name"
          placeholder="请输入菜名"
          maxlength="8"
          clearable
          disableDefaultPadding
          border="none"
          :customStyle="{
            width: '210px',
            height: '32px',
            backgroundColor: '#f8f8f8',
            borderRadius: '3px',
            padding: '0 6px',
          }"
          @change="onInputChange($event, 'name', pageData)"
        ></up-input>
      </view>
    </view>
    <view class="row img-box">
      <text class="label">菜品图片</text>
      <view class="img-info-wrap">
        <ImageComponent :src="pageData.imageUrl ||imageUrl" :size="110"></ImageComponent>
      </view>
    </view>
    <view class="row handle-img">
      <UploadImage :url="`/dishimage/upload${pageData.id?'/' + pageData.id : ''}`" @change="onImageChange"></UploadImage>
    </view>
    <view class="row sync">
      <view class="sync-header">
        <text>同步订餐菜品库</text>
        <SlSubSvgIcon subpage="cookbook"
          class="sync-opt"
          v-if="pageData.synced"
          name="36-36-2"
          size="36"
          @click="pageData.synced = false"
        />
        <SlSubSvgIcon subpage="cookbook"
          class="sync-opt"
          v-else
          name="36-36-3"
          size="36"
          @click="pageData.synced = true"
        />
      </view>
      <view class="sync-content" v-if="pageData.synced">
        <view class="sync-items" v-for="item in syncItems">
          <text class="name">{{ item.name }}</text>
          <view class="opt-wrap">
            <SlSubSvgIcon subpage="cookbook" name="20-20-24" size="20" @click="onNum(item.id, 'minus')" />
            <text>{{ pageData[item.id] }}</text>
            <SlSubSvgIcon subpage="cookbook" name="20-20-25" size="20" @click="onNum(item.id, 'add')" />
            <text class="unit">{{ item.unit }}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom-btns">
      <BaseButton class="btn" v-if="!!id" btnType="delete" @click="onRemove"
        >下架</BaseButton
      >
      <BaseButton class="btn" btnType="cancel" v-else @click="onCancel">取消</BaseButton>
      <BaseButton class="btn" :disabled="!pageData.name" @click="onSubmit"
        >完成</BaseButton
      >
    </view>
  </view>
  <Popup v-if="showRemoveTip" icon="20-20-23" iconSize="32" text="您确定下架该菜品吗？" :btnList="btnList"></Popup>
</template>

<script setup lang="ts">
import Popup from '@/subpages/cookbook/components/Popup.vue';

import { ref } from "vue";
import { onInputChange, useAccount } from "../../composables/business";
import ImageComponent from "@/subpages/cookbook/components/ImageComponent.vue";
import { Dish } from "../../model/dish";
import { cookbookService } from "@/service/cookbook/cookbook.service";
import { useRefreshStore } from "../../composables/store";
const refreshStore = useRefreshStore();
const {getAccount} = useAccount()
const showRemoveTip = ref(false);
const btnList = [
        {
            text: '取消',
            type: 'close',
            click: () => {
                showRemoveTip.value = false
            }
        },
        {
            text: '确定',
            type: 'sure',
            click: () => {
                remove()
            }
        }
    ]

interface SyncItem {
  name: string;
  id: "portionUnit" | "price" | "limitPerPerson";
  unit: string;
}
const id = ref("");
const type = ref<"morning" | "afternoon">("morning");
const pageData = ref<Dish>( new Dish( {} ) );
const imageUrl = ref('')
// 先设置静态标题避免模板闪现
uni.setNavigationBarTitle({ title: "加载中..." });
onLoad((options: any) => {
  // 通过参数判断模式
  id.value = options.id;
  type.value = options.type;
  getInfo(options.id);
  uni.setNavigationBarTitle({
    title: `${id.value ? "编辑" : "新增"}菜品`,
  });
  getAccount()
});
/**同步订餐菜品库 */
const syncItems = ref<SyncItem[]>([
  { name: "分量", id: "portionUnit", unit: "个/份" },
  { name: "价格", id: "price", unit: "元/份" },
  { name: "限购数量", id: "limitPerPerson", unit: "份/人" },
]);

async function getInfo(id: string) {
  if (!id) return;
  const data = await cookbookService.getDishDetail(id);
  pageData.value = data;
}

function onNum(id: "portionUnit" | "price" | "limitPerPerson", type: "add" | "minus") {
  if (type === "add") {
    pageData.value[id] += 1;
  } else {
    if (pageData.value[id] <= 0) return;
    pageData.value[id] -= 1;
  }
}

async function onSubmit() {
  pageData.value.timePeriod = type.value;
  pageData.value.id?await cookbookService.updateDish(pageData.value):await cookbookService.saveDish(pageData.value);
    // 设置需要刷新
    refreshStore.setNeedRefresh("dishList", true);
    uni.navigateBack();
}

function onCancel() {
  uni.navigateBack();
}

function onRemove() {
  showRemoveTip.value = true;
}

async function remove() {
  await cookbookService.deleteDish(pageData.value.id);
    showRemoveTip.value = false;
    refreshStore.setNeedRefresh("dishList", true);
  uni.navigateBack();
}

async function onImageChange( e: any ) {
  const {url='',file={}} =e
  pageData.value.imageId = url;
  imageUrl.value = file.path || '';
}
</script>

<style lang="scss">
.page {
  background: #f4f6fa;
  height: 100vh;
  white-space: nowrap;
  .row {
    background: #fff;
    display: flex;
    box-shadow: 0px 0px 4px 0px #92929233;
    margin-bottom: 6px;
    padding: 0 15px;
  }
  .name {
    height: 40px;
    align-items: center;
    justify-content: space-between;

    .label {
      display: inline-block;
      position: relative;
      padding-left: 5px;

      &::before {
        content: "*";
        color: red;
        position: absolute;
        left: -5px;
        top: 0;
      }
    }
  }
  .img-box {
    padding-top: 17px;
    padding-bottom: 17px;
    .label {
      padding-right: 34px;
    }
    .img-info-wrap {
      width:110px;
      height: 110px;
    }
  }
  .handle-img {
    display: block;
    padding: 0;
  }
  .sync {
    flex-direction: column;
    margin-bottom: 0;

    .sync-header {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .sync-opt {
        margin-top: 4px;
      }
    }
    .sync-items {
      display: flex;
      justify-content: space-between;
      .opt-wrap {
        display: flex;
        text {
          padding: 0 10px;
        }
        .unit {
          color: #666666;
        }
      }
    }
  }
  .bottom-btns {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 108px;
    background: #f4f6fa;
    .btn {
      &:not(:last-child) {
        margin-right: 10px;
      }
    }
  }
}
</style>
