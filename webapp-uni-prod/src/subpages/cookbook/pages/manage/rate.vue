<template>
    <SlTabbarPage :tab-bar-root="'subpages/cookbook'" admin>
        <view class="page">
        <view class="calendar">
            <CalendarComponent v-model="currDate" @dateChange="onDateChange" :date-data="canlendarList"></CalendarComponent>
        </view>
        <scroll-view class="scroll-Y" scroll-y v-if="dataList&&dataList.length">
            <view class="list-wrap">
            <view class="list-item" v-for="(item, index) in dataList" :key="index">
                <view class="name">{{item.surname}}</view>
                <view class="content">
                    <view class="time">{{item.rateTime}}</view>
                    <view class="grid-container" v-if="item?.ratingList?.length">
                        <view v-for="(subItem, index) in item.ratingList" :key="index" class="grid-item" :class="{ 'left-column': index % 2 === 0 }">
                            <view class="item-content">
                                <view class="img">
                                    <ImageComponent :src="subItem.imageUrl" :size="30"></ImageComponent>
                                </view>
                                <view class="info">
                                    <text class="title">{{ subItem.dishName }}</text>
                                    <up-rate count="5" size="12" v-model="subItem.rating" readonly></up-rate>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="total-rate">{{item.comment}}</view>
                </view>
            </view>
        </view>
        </scroll-view>
        <view class="nodata" v-else>
            <NoDataComponent :title="'暂无评价'"></NoDataComponent>
        </view>
        <SlSubSvgIcon subpage="cookbook" class="opt-btn" name="52-52-6" size="45" />
    </view>
    </SlTabbarPage>
</template>
<script setup lang="ts">
    import NoDataComponent from '@/subpages/cookbook/components/NoDataComponent.vue';
import ImageComponent from '@/components/ImageComponent.vue';
    import { useDate,useAccount } from "../../composables/business";
import { cookbookService } from '@/service/cookbook/cookbook.service';
import { MenuComment } from '../../model/menu';
    const { currDate,backDate,canlendarList, getCalendar, onDateChange } = useDate()
    const { userId,getAccount } = useAccount()
    const dataList = ref<MenuComment[]>([])

    onMounted(() => {
        getCalendar()
        getAccount().then(() => {
            getList(backDate.value)
        })
    })

    async function getList(date: string) {
        const res = await cookbookService.getMenuComment(date,userId.value)
        res.forEach((e: any) => {
            e.rateTime = e.commentTime.split(' ')[1]
        })
        dataList.value = res || []
        console.log(dataList.value)
    }

    watch(backDate, () => {
    getList(backDate.value)
})

</script>

<style lang="scss">
.page {
    position: relative;
    width: 100%;
    height: 100%;
    background: #F8F8F8;
    display: flex;
    flex-direction: column;

    .calendar {
        background: #fff;
        margin-bottom: 10px;
    }

    .list-wrap {
        box-shadow: 0px 0px 4px 0px #********;
        .list-item {
            margin-bottom: 4px;
            background: #FFFFFF;
            padding: 15px;
            position: relative;
            .name {
                position: absolute;
                left: 15px;
                top: 15px;
                width: 30px;
                height: 30px;
                background: #4F7AF6;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
                border-radius: 50%;
            }
            .content {
                margin-left: 45px;
                .time {
                    color: #999999;
                    font-size: 12px;
                }
            }
        }
    }
    .grid-container {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
    }
    .grid-item {
        width: 50%;
        padding: 0 10px 0 0;
        margin-top: 10px;

        /* 奇数项右侧虚线边框 */
        &.left-column {
            .item-content{
                border-right: 1px dashed #E4E9ED;
            }
        }

        .item-content {
            display: flex;
            .img {
                margin-right: 8px;
            }
            .info {
                flex: 1;
                display: flex;
                flex-direction: column;
                .title {
                    font-size: 12px;
                    color: #666666;
                    padding-bottom: 4px;
                }
            }
        }
    }
    .total-rate {
        margin-top: 15px;
        color: #3D3D3D;
    }

    .opt-btn {
        position: absolute;
        right: 14px;
        bottom: 8px;
    }
    .nodata {
        height: 100%;
        flex: 1;
    }
}
.scroll-Y {
    height: 100%;
    min-height: 0;
    flex: 1;
 }
</style>