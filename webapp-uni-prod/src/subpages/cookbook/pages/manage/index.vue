<template>
    <SlTabbarPage :tab-bar-root="'subpages/cookbook'" admin>
        <view class="page">
            <MenuComponent class="menu" :isManager="isManager" :currTab="currTab" :tabGroup="tabGroup" layoutType="horizontal" :hasEdit="true" :hasRemove="true" @currentTabChange="handleTabChange" @refresh="refresh"></MenuComponent>
            <SlSubSvgIcon subpage="cookbook" class="opt-btn" name="52-52-4" size="52" @click="onAdd" />
        </view>
    </SlTabbarPage>
</template>
<script setup lang="ts">
import MenuComponent from '@/subpages/cookbook/components/MenuComponent.vue';
import { useAccount,useTab } from "../../composables/business";
import { cookbookService } from '@/service/cookbook/cookbook.service';
import {TabGroup } from '../../model/menu';
import { onShow } from '@dcloudio/uni-app'
import { useRefreshStore } from '../../composables/store';

const refreshStore = useRefreshStore()
const {  currTab,tabGroup,handleTabChange } = useTab()
const {getAccount,isManager} = useAccount()

const onAdd = () => {
    uni.navigateTo({ url: `/subpages/cookbook/pages/manage/handle?type=${currTab.value}` })
}

function refresh() {
    getList()
}

onMounted(() => {
   getAccount()
    getList()
})

async function getList() {
    let res: any = await cookbookService.getDishList()
    tabGroup.value.forEach((item: TabGroup) => {
        item.menuDishList = (res&& res.filter((e: any) => e.timePeriod === item.timePeriod)) || []
    })
}
onShow(() => {
    if (refreshStore.needRefresh.dishList){
        setTimeout(() => {
            getList()
            refreshStore.setNeedRefresh('dishList', false) // 重置状态
        })
    }
})
</script>
<style lang="scss" scoped>
.page {
    width: 100%;
    height: 100%;
    position: relative;
    background: #F8F8F8;
    display: flex;
    flex-direction: column;

    .calendar {
        background: #fff;
        margin-bottom: 10px;
    }
}
.menu {
    flex: 1;
    min-height: 0;
    overflow: hidden;
}
.opt-btn {
    position: fixed;
    bottom: 103px;
    right: 14px;
}
.dialog-content {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .dialog-box {
        position: absolute;
        width: 260px;
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 14px;
    }
    }
    .text {
        line-height: 20px;
    }
    .time {
        font-weight: bold;
    }
    .btn {
        width: 200px;
        height: 40px;
        line-height: 40px;
        margin-top: 40px;
    color: #fff;
    text-align: center;
    border-radius: 24px;
    background: linear-gradient(111deg, #006AE8 0%, #0041F4 100%);
    }

.svg {
    position: absolute;
}

.star {
    position: absolute;
    top: -24px;
        left: 50%;
        transform: translateX(-50%);
}
.text-box {
    display: flex;
        flex-direction: column;
        margin-top: 60px;
}
</style>