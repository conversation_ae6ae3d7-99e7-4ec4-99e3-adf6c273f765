<template>
    <view class="page">
        <MenuComponent class="menu" :allDatas="allDatas" layoutType="column" :size="60" :hasRate="true">
        </MenuComponent>
        <view class="total-rate">
            <text class="title">总体评价</text>
            <up-textarea v-model="pj" placeholder="亲，描述一下菜品口感怎么样吧，帮助我们更好的改善菜品哦~" count :placeholderStyle="placeholderStyle" :height="140"></up-textarea>
        </view>
        <BaseButton class="save" size="large" :disabled="btnDisabled" @click="save">提交评价</BaseButton>
    </view>
</template>

<script setup lang="ts">
import MenuComponent from '@/subpages/cookbook/components/MenuComponent.vue';

    const allDatas = [ { name: '酸菜肉包', uuid: '1' }, { name: '炸果子', uuid: '1' }, { name: '煎饼果子', uuid: '1' }, { name: '锅巴菜', uuid: '1' }, { name: '馄饨汤面', uuid: '1' }, { name: '酸菜肉包', uuid: '1' }, { name: '炸果子', uuid: '1' }, { name: '煎饼果子', uuid: '1' }, { name: '锅巴菜', uuid: '1' }, { name: '馄饨汤面', uuid: '1' }, { name: '八珍豆腐', uuid: '2' }, { name: '土豆鸡块', uuid: '2' }, { name: '牛肉面', uuid: '2' }, { name: '米饭', uuid: '2' }, { name: '西湖牛肉羹', uuid: '2' } ]

    const placeholderStyle = {
        color: '#999999B2',
        fontSize: '14px'
    }
    const pj = ref('');
    const btnDisabled = computed(() => {
        return false
    })
    const save = () => {
        console.log( '=====save' )
        errToast()
    }

    function successToast() {
        uni.$u.toast('感谢您的反馈，祝您用餐愉快！');
    }

    function errToast() {
        uni.$u.toast('提交失败，请稍后重试~');
    }
</script>

<style lang="scss" scoped>
.page {
    padding-bottom: 30px;
}
.total-rate {
    padding: 10px 15px 30px 15px;
    .title {
        color: #636363;
        display: inline-block;
        margin-bottom: 10px;
    }
}
/* 使用深度选择器穿透组件 */
:deep(.total-rate .u-textarea),
:deep(.total-rate .u-textarea__count) {
  background-color: #f5f5f5 !important;
}
:deep(.total-rate .u-textarea) {
  border-radius: 10px !important;
}
</style>