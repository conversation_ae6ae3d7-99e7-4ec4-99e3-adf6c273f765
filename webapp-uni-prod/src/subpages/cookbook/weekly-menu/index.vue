<template>
    <view class="page">
        <image class="banner" src="@/static/images/cookbook/banner.png" mode="aspectFill"></image>
        <view><CalendarComponent></CalendarComponent></view>
        <MenuComponent class="menu" :allDatas="allDatas"></MenuComponent>
        <text class="evaluate-btn {{ isEvaluateTime ? 'active' : '' }}"
        @click="handleEvaluate">评价</text>
    </view>
    <ConfirmDialog background="skyblue"  v-if="showTip" @close="showTip = false" @confirm="onConfirmCancel"><template v-slot:header>555</template>
    </ConfirmDialog>
</template>
<script setup lang="ts">
import MenuComponent from '@/subpages/cookbook/components/MenuComponent.vue';
import ConfirmDialog from "@/components/ConfirmDialog.vue";
const allDatas = [ { name: '酸菜肉包', uuid: '1' }, { name: '炸果子', uuid: '1' }, { name: '煎饼果子', uuid: '1' }, { name: '锅巴菜', uuid: '1' }, { name: '馄饨汤面', uuid: '1' }, { name: '酸菜肉包', uuid: '1' }, { name: '炸果子', uuid: '1' }, { name: '煎饼果子', uuid: '1' }, { name: '锅巴菜', uuid: '1' }, { name: '馄饨汤面', uuid: '1' }, { name: '八珍豆腐', uuid: '2' }, { name: '土豆鸡块', uuid: '2' }, { name: '牛肉面', uuid: '2' }, { name: '米饭', uuid: '2' }, { name: '西湖牛肉羹', uuid: '2' } ]
const showTip = ref(false)
// 计算当前是否在评价时间段
const isEvaluateTime = computed(() => {
  const now = new Date()
  const hours = now.getHours()
  const minutes = now.getMinutes()

  // 每天11:30后开启 (11:30-23:59)
  return hours > 11 || (hours === 11 && minutes >= 30)
} )
// 点击评价按钮
const handleEvaluate = () => {
    // if ( !isEvaluateTime.value ) return
    showTip.value = true
}

function onConfirmCancel() {
    showTip.value = false
}
</script>
<style lang="scss" scoped>
.page {
    width: 100%;
    height: 100vh;
    position: relative;
    background: #F8F8F8;
    display: flex;
    flex-direction: column;
}
.banner {
    width: 100%;
    height: 120px;
}
.menu {
    flex: 1;
    min-height: 0;
    overflow: hidden;
}
.evaluate-btn {
    position: absolute;
    bottom: 63px;
    right: 14px;
    cursor: pointer;
}
</style>