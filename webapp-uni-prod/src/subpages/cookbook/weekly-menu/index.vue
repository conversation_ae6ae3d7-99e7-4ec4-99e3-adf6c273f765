<template>
    <view class="page">
        <image class="banner" src="@/static/images/cookbook/banner.png" mode="aspectFill"></image>
        <view>
            <CalendarComponent class="calendar" @dateChange="dateChange"></CalendarComponent>
        </view>
        <main v-if="allDatas&&allDatas.length">
            <MenuComponent class="menu" :allDatas="allDatas"></MenuComponent>
            <SlSvgIcon class="opt-btn" :name="`cookbook-45_${ isEvaluateTime ? '1' : '2' }`" size="45" :canClick="true" @click="handleEvaluate"></SlSvgIcon>
        </main>
        <view v-else>
            <text>今日休息</text>
        </view>
    </view>
    <ConfirmDialog width="260" styleObj="{height: 0, background: 'transparent'}" v-if="showTip" :showHeader="false">
        <template>
            <view class="dialog-content">
                <SlSvgIcon class="svg" name="cookbook-alertBg" size="260" sizeH="283"></SlSvgIcon>
                <view class="dialog-box">
                    <SlSvgIcon class="star" name="cookbook-68" size="68"></SlSvgIcon>
                    <view class="text-box">
                        <text class="text">暂不可评</text>
                        <text class="text">请您<text class="time">11:30</text>之后再来评价哦！</text>
                    </view>
                    <text class="btn" @click="showTip = false">好的，我知道了</text>
                </view>
            </view>
        </template>
        <template v-slot:footer></template>
    </ConfirmDialog>
</template>
<script setup lang="ts">
import MenuComponent from '@/subpages/cookbook/components/MenuComponent.vue';
import SlSvgIcon from '@/components/SlSVgIcon.vue'
import { useDate } from "../composables/business";

const allDatas = [ { name: '酸菜肉包', uuid: '1' }, { name: '炸果子', uuid: '1' }, { name: '煎饼果子', uuid: '1' }, { name: '锅巴菜', uuid: '1' }, { name: '馄饨汤面', uuid: '1' }, { name: '酸菜肉包', uuid: '1' }, { name: '炸果子', uuid: '1' }, { name: '煎饼果子', uuid: '1' }, { name: '锅巴菜', uuid: '1' }, { name: '馄饨汤面', uuid: '1' }, { name: '八珍豆腐', uuid: '2' }, { name: '土豆鸡块', uuid: '2' }, { name: '牛肉面', uuid: '2' }, { name: '米饭', uuid: '2' }, { name: '西湖牛肉羹', uuid: '2' } ]
const { currDate, dateChange } = useDate()

const showTip = ref(false)
// 计算当前是否在评价时间段
const isEvaluateTime = computed(() => {
  const now = new Date()
  const hours = now.getHours()
  const minutes = now.getMinutes()

  // 每天11:30后开启 (11:30-23:59)
  return hours > 11 || (hours === 11 && minutes >= 30)
} )
// 点击评价按钮
const handleEvaluate = () => {
    if ( !isEvaluateTime.value ) {
        showTip.value = true
    }else {
        uni.navigateTo({ url: '/subpages/cookbook/weekly-menu/menu-rate' })
    }

}

function onConfirmCancel() {
    showTip.value = false
}

</script>
<style lang="scss" scoped>
.page {
    width: 100%;
    height: 100vh;
    position: relative;
    background: #F8F8F8;
    display: flex;
    flex-direction: column;
}
.banner {
    width: 100%;
    height: 120px;
}
.menu {
    flex: 1;
    min-height: 0;
    overflow: hidden;
}
.opt-btn {
    position: fixed;
    bottom: 63px;
    right: 14px;
}
.dialog-content {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .dialog-box {
        position: absolute;
    width: 260px;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 14px;
}
    }
    .text {
        line-height: 20px;
    }
    .time {
        font-weight: bold;
    }
    .btn {
        width: 200px;
        height: 40px;
        line-height: 40px;
        margin-top: 40px;
    color: #fff;
    text-align: center;
    border-radius: 24px;
    background: linear-gradient(111deg, #006AE8 0%, #0041F4 100%);
    }

.svg {
    position: absolute;
}

.star {
    position: absolute;
    top: -24px;
        left: 50%;
        transform: translateX(-50%);
}
.text-box {
    display: flex;
        flex-direction: column;
        margin-top: 60px;
}
</style>