import { Dish } from './dish';

export class Menu {
  /**日期 */
  menuDate?: string;
  /**时段 */
  timePeriod?: 'morning' | 'afternoon';
  menuDishList?: Dish[] = [];
}

export class MenuDish extends Dish {
  id?: string = '';
  /**关联菜谱id */
  menuId?: string;
  /**是否已发布 */
  published?: boolean = false;
}

export class MenuComment {
  menuDate?: string;
  userId?: string;
  /**用户评论 */
  comment?: string;
  /**菜品评分列表 */
  ratingList?: { dishId: string; name: string; rating: number; imageId: string }[] = [];
}

export class TabGroup {
  name: string = '';
  timePeriod: 'morning' | 'afternoon' = 'morning';
  menuDishList: MenuDish[] = [];
  selectedList?: MenuDish[] = [];
}
