export class Dish {
	/**菜品ID */
	id: string = '';
	dishId?: string;
	/**菜品名 */
	name: string = '';
	timePeriod: 'morning' | 'afternoon' = 'morning';
	/**价格 */
	price: number = 0;
	/**分量 */
	portionUnit: number = 0;
	/**限购 */
	limitPerPerson: number = 0;
	/**图片ID */
	imageId?: string;
	imageUrl?: string;
	images?: any[];
	/**是否同步菜品库 */
	synced?: boolean;
	constructor(data: any) {
		Object.assign(this, data);
	}
}
