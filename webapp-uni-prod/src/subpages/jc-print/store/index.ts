import { defineStore } from "pinia";
import {
  BluetoothDeviceStatusEnum,
  type BluetoothDevice,
} from "../models/BluetoothDevice";
import storage from "@/service/common/storage.service";
const JC_PRINTER_CURRENT = "jcPrinterCurrent";
const JC_PRINTER_CURRENT_LIST = "jcPrinterCurrentList";
import { usePrint } from "../composables/print";
/**
 * 获取缓存的已连接设备列表
 * @returns {BluetoothDevice[]} 已连接设备列表
 */
const getCachedList = (): BluetoothDevice[] => {
  return storage.getJSON(JC_PRINTER_CURRENT_LIST) || [];
};
/**
 * 获取缓存的当前设备
 * @returns {BluetoothDevice} 当前设备
 */
const getCachedCurrent = (): BluetoothDevice => {
  return storage.getJSON(JC_PRINTER_CURRENT) || ({} as BluetoothDevice);
};
/**
 * 设置缓存的当前设备
 * @param {BluetoothDevice} device 当前设备
 */
const setCachedCurrent = (device: BluetoothDevice) => {
  storage.setJSON(JC_PRINTER_CURRENT, device);
};
/**
 * 设置缓存的已连接设备列表
 * @param {BluetoothDevice[]} devices 已连接设备列表
 */
const setCachedList = (devices: BluetoothDevice[]) => {
  storage.setJSON(JC_PRINTER_CURRENT_LIST, devices);
};

const useJcPrinterStore = defineStore("jcPrinter", {
  state: () => ({
    connectedList: getCachedList(), // 已连接设备列表
    available: [] as BluetoothDevice[], // 可用设备列表
    current: getCachedCurrent(), // 当前连接的设备
    isSearchCompleted: false, // 搜索是否完成
    curHandleDevice: {} as BluetoothDevice, // 当前处理的设备
  }),

  getters: {
    getCurrent: (state) => {
      return state.current;
    },
    getConnectedList: (state) => {
      return state.connectedList;
    },
    getAvailable: (state) => {
      return state.available;
    },
    getCurHandleDevice: (state) => {
      return state.curHandleDevice;
    },
  },
  actions: {
    /**
     * 打开设备
     * @param device 设备对象
     */
    openPrinter(device: BluetoothDevice) {
      this.current = device;
      // 如果设备已连接，则添加到已连接列表
      setCachedCurrent(device);
      const index = this.connectedList.findIndex(
        (item) => item.deviceId === device.deviceId
      );
      if (index === -1) {
        this.connectedList.push(device);
      } else {
        this.connectedList[index] = device; // 更新已连接设备信息
      }
      setCachedList(this.connectedList);
      setCachedCurrent(device);
    },

    // 从已连接列表中移除设备
    removePrinter(deviceId: string) {
      this.connectedList = this.connectedList.filter(
        (device) => device.deviceId !== deviceId
      );
      setCachedList(this.connectedList);
      // 如果当前设备是被移除的设备，则清空当前设备
      if (this.current?.deviceId === deviceId) {
        const { closePrinter } = usePrint();
        closePrinter();
        setCachedCurrent({} as BluetoothDevice);
      }
    },

    /**
     * 断开连接打印机
     * @param deviceId 设备ID
     */
    disconnectPrinter(deviceId: string) {
      // 将设备状态设置为断开连接
      const index = this.connectedList.findIndex(
        (device) => device.deviceId === deviceId
      );
      if (index !== -1) {
        this.connectedList[index].status = "disconnected";
        // 更新本地存储
        setCachedList(this.connectedList);

        // 如果当前设备是被断开连接的设备，则清空当前设备
        if (this.current?.deviceId === deviceId) {
          this.current = {} as BluetoothDevice;
          setCachedCurrent(this.current);
        }
      }
    },

    /**
     * 设置当前处理的设备
     * @param device 设备对象
     */
    setCurHandleDevice(device: BluetoothDevice) {
      this.curHandleDevice = device;
    },

    /**
     * 设置已连接设备列表
     * @param devices 设备列表
     */
    setConnectedList(devices: BluetoothDevice[]) {
      this.connectedList = devices;
    },

    /**
     * 设置可用设备列表
     * @param devices 设备列表
     */
    setAvailable(devices: BluetoothDevice[]) {
      this.available = devices;
    },

    /**
     * 重置当前设备
     */
    resetCurrent() {
      this.current = {} as BluetoothDevice; // 清空当前设备
      this.connectedList.forEach((device) => {
        device.status = BluetoothDeviceStatusEnum.DISCONNECTED; // "disconnected"; // 将所有已连接设备状态设置为断开连接
      });
      setCachedCurrent(this.current);
      setCachedList(this.connectedList); // 更新已连接设备列表
    },
  },
});

export default useJcPrinterStore;
