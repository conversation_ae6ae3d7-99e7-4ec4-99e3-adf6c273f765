import { PrintTakeoutOrder } from "@/subpages/takeout/models";
import { PrintJob, PrintJobState } from "../models/PrintJob";
import { useTakeoutAdminStore } from "@/subpages/takeout/store";
const JOB_LIST_KEY = "jcPrintJobList";

/**
 * 获取打印任务列表
 * @returns {PrintJob[]} 打印任务列表
 */
const getPrintJobList = (): PrintJob[] => {
  return uni.getStorageSync(JOB_LIST_KEY) || [];
};

const updatePrintJobList = (list: PrintJob[]): void => {
  uni.setStorageSync(JOB_LIST_KEY, list);
};

/**
 * 添加打印任务到列表
 * @param job 打印任务
 */
const addJobToList = (job: PrintJob): void => {
  const list = getPrintJobList();
  const index = list.findIndex((ele) => ele.orderDate === job.orderDate);
  if (index === -1) {
    list.push(job); // 添加新任务
  } else {
    list[index] = job; // 更新已存在的任务
  }
  updatePrintJobList(list);
};

/**
 * 清理已完成或打印中的任务
 * 只保留待处理和打印失败的任务
 */
const clearCompletedJobs = (): void => {
  const list = getPrintJobList();
  const filterList = list.filter(
    (ele) =>
      ele.status !== PrintJobState.COMPLETED &&
      ele.status !== PrintJobState.PRINTING
  );
  updatePrintJobList(filterList);
};

/**
 * 获取指定日期的打印任务
 * @param date
 * @returns
 */
const getPrintJobByDate = (date: string): PrintJob | undefined => {
  const list = getPrintJobList();
  return list.find((ele) => ele.orderDate === date);
};

const usePrintJobStore = defineStore("jcPrintJob", {
  state: () => ({
    printJob: {} as PrintJob, // 当前打印任务
  }),
  getters: {
    getPrintJob: (state) => state.printJob,
    getPrintJobList: () => getPrintJobList(),
  },
  actions: {
    initJob(force = false) {
      const takeoutAdminStore = useTakeoutAdminStore();
      const curDate = takeoutAdminStore.backCurDate;
      let job = getPrintJobByDate(curDate);
      if (!job) {
        const curPrintOrderList = takeoutAdminStore.getCurPrintOrderList;
        console.log("初始化打印任务:", curDate, curPrintOrderList);
        const orders = curPrintOrderList.map((item, index) => ({
          orderId: item.orderId,
          // status: PrintJobState.PENDING, // 默认状态为待处理
          idx: index, // 添加索引字段
        }));
        // 如果当前日期的打印任务不存在，则创建一个新的打印任务
        job = {
          orderDate: curDate,
          items: orders,
          createdAt: Date.now(),
          printed: 0,
          drawn: 0,
          pages: orders.length,
          remainingPages: orders.length,
          status: PrintJobState.PENDING, // 初始化状态为待处理
        };
      } else {
        if (force) {
          console.log("==重新打印任务，重置状态");
          job.createdAt = Date.now(); // 更新创建时间
          job.status = PrintJobState.PENDING; // 重置状态为待处理
          job.pages = job.items.length; // 更新总页数为当前订单项数量
          job.remainingPages = job.pages; // 重置剩余页数
          job.printed = 0; // 重置已打印页数
          job.drawn = 0; // 重置已绘制页数
        } else {
          console.log("==继续打印任务，更新状态");
          job.remainingPages = job.pages - job.printed; // 更新剩余页数
          job.drawn = job.printed; // 设置已绘制页数为已打印页数
        }
      }
      // 更新打印任务列表
      this.printJob = job;
      addJobToList(job);
      console.log("==当前打印任务:", this.printJob);
      console.log("==打印任务列表:", getPrintJobList());
    },

    // 获取指定索引的订单
    getOrderByIndex(index: number): PrintTakeoutOrder | undefined {
      const takeoutAdminStore = useTakeoutAdminStore();
      const orderList = takeoutAdminStore.getCurPrintOrderList;
      console.log("==获取订单列表:", orderList, index);
      return orderList[index];
    },
    updatePrinted() {
      const printed = this.printJob.printed + 1; // 增加已打印页数
      this.privateUpdateJobList("printed", printed);
      console.log("==更新已打印页数", this.printJob.printed);
    },
    updateDrawn() {
      const drawn = this.printJob.drawn + 1; // 增加已绘制页数
      this.privateUpdateJobList("drawn", drawn);
      console.log("==更新已绘制页数", this.printJob.drawn);
    },

    clearJob() {
      clearCompletedJobs(); // 清理已完成的打印任务
      this.printJob = {} as PrintJob; // 清空当前打印任务
    },
    // 当前打印任务已完成
    markAsCompleted() {
      this.privateUpdateJobState(PrintJobState.COMPLETED); // 更新任务状态为已完成
    },

    // 打印中
    markAsInProgress() {
      this.privateUpdateJobState(PrintJobState.PRINTING); // 更新任务状态为打印中
    },

    // 打印失败
    markAsFailed() {
      this.privateUpdateJobState(PrintJobState.FAILED); // 更新任务状态为打印失败
    },

    // 更新打印任务状态
    privateUpdateJobState(state: PrintJobState) {
      this.privateUpdateJobList("status", state); // 更新任务状态
    },

    // 更新打印任务的指定属性
    privateUpdateJobList(property: keyof PrintJob, value: any) {
      const list = getPrintJobList();
      const index = list.findIndex(
        (ele) => ele.orderDate === this.printJob.orderDate
      );
      if (index !== -1) {
        // 更新对应 job 的指定属性
        (list[index] as any)[property] = value;
        (this.printJob as any)[property] = value; // 同步更新当前任务状态
        updatePrintJobList(list);
      } else {
        console.warn(`$$ 打印任务 ${this.printJob.orderDate} 不存在，无法更新`);
      }
    },
  },
});

export default usePrintJobStore;
