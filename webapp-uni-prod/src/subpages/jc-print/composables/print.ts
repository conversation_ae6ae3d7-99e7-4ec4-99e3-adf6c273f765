// /**
//  * @Description
//  * @Version
//  * <AUTHOR>
//  * @date 2023/1/3 16:55
//  */
// // 引入打印类，CommonJs规范
// const api = require('../static/JCAPI/JCAPI.js');

// export function usePrint() {

//     return {
//         setPlatform(platform) {
//             api.setPlatform(platform)
//         },

//         setBuildPlatform(platform) {
//             api.setBuildPlatform(platform)
//         },
//         startJob(gapType, darkness, totalCount, callback) {
//             api.startJob(gapType, darkness, totalCount, callback)
//         },

//         startDrawLabel(canvasId, component, labelWidth, labelHeight, rotation = 0, canvas = null) {
//             api.startDrawLabel(canvasId, component, labelWidth, labelHeight, rotation, canvas)
//         },

//         drawTextInRect(content, x, y, width, height, fontHeight, rotation = 0, options = null) {
//             api.drawTextInRect(content, x, y, width, height, fontHeight, rotation, options)
//         },

//         drawText(content, x, y, fontHeight, rotation = 0, options = null) {
//             api.drawText(content, x, y, fontHeight, rotation, options)
//         },

//         drawBarcode(content, x, y, width, height, rotation = 0, fontSize, fontHeight, position = 2) {
//             api.drawBarcode(content, x, y, width, height, rotation, fontSize, fontHeight, position)
//         },

//         drawQRCode(content, x, y, width, height, rotation = 0, ecc = 2) {
//             api.drawQRCode(content, x, y, width, height, rotation, ecc)
//         },

//         drawRectangle(x, y, width, height, lineWidth, isFilled = false, rotation = 0) {
//             api.drawRectangle(x, y, width, height, lineWidth, isFilled, rotation)
//         },

//         drawLine(x, y, width, height, rotation = 0) {
//             api.drawLine(x, y, width, height, rotation)
//         },

//         drawImage(path, x, y, width, height, rotation = 0) {
//             api.drawImage(path, x, y, width, height, rotation)
//         },

//         endDrawLabel(callback) {
//             api.endDrawLabel(callback)
//         },

//         print(onePageCount = 1, callback, optionPara = null) {
//             api.print(onePageCount, callback, optionPara)
//         },

//         didReadPrintCountInfo(callback) {
//             api.didReadPrintCountInfo(callback)
//         },

//         didReadPrintErrorInfo(callback) {
//             api.didReadPrintErrorInfo(callback)
//         },

//         scanedPrinters(callback) {
//             api.scanedPrinters(callback)
//         },

//         openPrinter(name, successConnect, failConnect) {
//             api.openPrinter(name, successConnect, failConnect)
//         },

//         getConnName() {
//             return api.getConnName()
//         },

//         getSn(callback) {
//             return api.getSN(callback)
//         },

//         // 打印机断开连接
//         closePrinter() {
//             api.closePrinter()
//         }
//     }

// }

// @ts-ignore
// const JCAPI = require('@/subpages/jc-print/lib/JCAPI/JCAPI.js') as JCAPIStatic
const JCAPI = require("../static/JCAPI/JCAPI.js") as JCAPIStatic;
type Callback<T = any> = (res: T) => void;

export function usePrint() {
  return {
    setPlatform(platform: number) {
      JCAPI.setPlatform(platform);
    },

    setBuildPlatform(platform: number) {
      JCAPI.setBuildPlatform(platform);
    },

    startJob(
      gapType: number,
      darkness: number,
      totalCount: number,
      callback: Callback
    ) {
      JCAPI.startJob(gapType, darkness, totalCount, callback);
    },

    startDrawLabel(
      canvasId: string,
      component: object,
      labelWidth: number,
      labelHeight: number,
      rotation = 0,
      canvas: any = null
    ) {
      JCAPI.startDrawLabel(
        canvasId,
        component,
        labelWidth,
        labelHeight,
        rotation,
        canvas
      );
    },

    drawTextInRect(
      content: string,
      x: number,
      y: number,
      width: number,
      height: number,
      fontHeight: number,
      rotation = 0,
      options: object | null = null
    ) {
      JCAPI.drawTextInRect(
        content,
        x,
        y,
        width,
        height,
        fontHeight,
        rotation,
        options
      );
    },

    drawText(
      content: string,
      x: number,
      y: number,
      fontHeight: number,
      rotation = 0,
      options: object | null = null
    ) {
      JCAPI.drawText(content, x, y, fontHeight, rotation, options);
    },

    drawBarcode(
      content: string,
      x: number,
      y: number,
      width: number,
      height: number,
      rotation = 0,
      fontSize: number,
      fontHeight: number,
      position = 2
    ) {
      JCAPI.drawBarcode(
        content,
        x,
        y,
        width,
        height,
        rotation,
        fontSize,
        fontHeight,
        position
      );
    },

    drawQRCode(
      content: string,
      x: number,
      y: number,
      width: number,
      height: number,
      rotation = 0,
      ecc = 2
    ) {
      JCAPI.drawQRCode(content, x, y, width, height, rotation, ecc);
    },

    drawRectangle(
      x: number,
      y: number,
      width: number,
      height: number,
      lineWidth: number,
      isFilled = false,
      rotation = 0
    ) {
      JCAPI.drawRectangle(x, y, width, height, lineWidth, isFilled, rotation);
    },

    drawLine(
      x: number,
      y: number,
      width: number,
      height: number,
      rotation = 0
    ) {
      JCAPI.drawLine(x, y, width, height, rotation);
    },

    drawImage(
      path: string,
      x: number,
      y: number,
      width: number,
      height: number,
      rotation = 0
    ) {
      JCAPI.drawImage(path, x, y, width, height, rotation);
    },

    endDrawLabel(callback: Callback) {
      JCAPI.endDrawLabel(callback);
    },

    print(
      onePageCount = 1,
      callback?: Callback,
      optionPara: object | null = null
    ) {
      JCAPI.print(onePageCount, callback || (() => {}), optionPara);
    },

    didReadPrintCountInfo(callback: Callback) {
      JCAPI.didReadPrintCountInfo(callback);
    },

    didReadPrintErrorInfo(callback: Callback) {
      JCAPI.didReadPrintErrorInfo(callback);
    },

    scanedPrinters(callback: Callback<any[]>) {
      JCAPI.scanedPrinters(callback);
    },

    openPrinter(
      name: string,
      successConnect: () => void,
      failConnect: () => void
    ) {
      JCAPI.openPrinter(name, successConnect, failConnect);
    },

    closePrinter() {
      JCAPI.closePrinter();
    },

    getConnName(): string {
      return JCAPI.getConnName();
    },

    getSN(callback: Callback) {
      JCAPI.getSN(callback);
    },
  };
}
