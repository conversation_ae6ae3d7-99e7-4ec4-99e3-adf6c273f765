// @ts-ignore
const JCAPI = require("../static/JCAPI/JCAPI.js") as JCAPIStatic;
type Callback<T = any> = (res: T) => void;

export function usePrint() {
  return {
    setPlatform(platform: number | string) {
      JCAPI.setPlatform(platform);
    },

    setBuildPlatform(platform: number) {
      JCAPI.setBuildPlatform(platform);
    },

    /**
     * 开启打印任务
     *
     * @param gapType  设置打印的纸张类型 ，缺省1-间隙纸。2-⿊标纸 3-连续纸 5-透明纸，
     * @param darkness 设置该次打印浓度，B3S、B1、B203、B21、M2、K3、K3W浓度范围1-5
     * 默认3， Z401、B32浓度范围1-15，默认8
     * @param totalCount 设置总打印份数，表示所有⻚⾯的打印份数之和。
     * 例如，如果你有3⻚需要打印，第⼀⻚打印3份，第⼆⻚打印2份，第三⻚打印5份，那么totalCount的值应为10（3+2+5）。
     * @param callback 回调参数设置是否成功。
     */
    startJob(
      gapType: number,
      darkness: number,
      totalCount: number,
      callback: Callback
    ) {
      JCAPI.startJob(gapType, darkness, totalCount, callback);
    },

    /**
     * 打印前开始绘制
     * @param {string} canvasId - 画布ID
     * @param {object} component - 画布所在js对象
     * @param {number} labelWidth - 标签画布宽（标签宽度，单位mm）
     * @param {number} labelHeight - 标签画布⾼（标签⾼度，单位mm）
     * @param {number} rotation - 旋转⻆度，默认为0，⽀持0/90/180/270
     * @param {Canvas} canvas - 画布对象。当使⽤原⽣⼩程序IDE时，`canvas` 可为 `null`；若通过调⽤ {@link setBuildPlatform} ⽅法将编译平台设置为 uni 编译时，则需要传⼊ `canvas` 对象，否则打印会失败。
     * @description
     * 对应的 canvas 宽⾼建议使⽤变量
     */
    startDrawLabel(
      canvasId: string,
      component: object,
      labelWidth: number,
      labelHeight: number,
      rotation = 0,
      canvas: any = null
    ) {
      JCAPI.startDrawLabel(
        canvasId,
        component,
        labelWidth,
        labelHeight,
        rotation,
        canvas
      );
    },

    /**
     * 绘制多⾏⽂本
     * @param {string} content ⽂本内容
     * @param {number} x 起点x，单位mm
     * @param {number} y 起点y，单位mm（左下⻆坐标）
     * @param {number} width 预设⽂本框宽度，单位mm
     * @param {number} height 预设⽂本框⾼度，单位mm
     * @param {number} fontHeight 字体⼤⼩，单位mm
     * @param {number} rotation 旋转⻆度，默认为0，⽀持0/90/180/270，旋转中⼼点为左下⻆
     * @param {object} options 选项，具体如下：
     * - bold: 是否加粗，默认值为 false
     * - italic: 是否倾斜，默认值为 false
     * - family: 字体设置，默认值为 'SimHei'
     * - align: 对⻬⽅式，可选值为 'left'、'right'、'center'
     * - lineModel: 多⾏⽂本处理模式，若 options 为空、不传 lineModel 或值不为 3，则超出预设宽⾼的内容将被删除；若值为 3，则超出预设宽⾼的内容尾部将⽤省略号处理。
     *   使⽤多⾏⽂本时，options 参数不可为 null，否则内容超出预设宽⾼时会异常（下⼀版本修复）
     * 注意事项：
     * 1. iOS 斜体不⽀持中⽂，字体⾃定义也不⽀持中⽂，仅⽀持⼩程序内置字体。
     */
    drawTextInRect(
      content: string,
      x: number,
      y: number,
      width: number,
      height: number,
      fontHeight: number,
      rotation = 0,
      options: object | null = null
    ) {
      JCAPI.drawTextInRect(
        content,
        x,
        y,
        width,
        height,
        fontHeight,
        rotation,
        options
      );
    },

    /**
     * 绘制单⾏⽂本
     * @param {string} content ⽂本内容
     * @param {number} x 起点x,单位mm
     * @param {number} y 起点y,单位mm（左下⻆坐标）
     * @param {number} fontHeight 字体⼤⼩,单位mm
     * @param {boolean} dryRun 是否为不绘制模式，默认为 false。
     * italic：ios不⽀持中⽂斜体
     * @options {object}
     * {
     *  bold:false,
     *  italic:false,
     *  family:'SimHei',
     *  align:'left'/'right'/'center'
     * }
     * 注意事项：
     * 1. iOS 斜体不⽀持中⽂，字体⾃定义也不⽀持中⽂，仅⽀持⼩程序内置字体。
     * 2. 0° 对⻬⽅式中⼼点为 X 坐标，即左对⻬时⽂本左下⻆坐标为 x，右对⻬时⽂本右下⻆坐标为 x，居中对⻬时⽂本底部中⼼点坐标为 x。
     * 3. 90° 对⻬⽅式中⼼点为旋转后的 Y 坐标，即左对⻬时⽂本左下⻆坐标为旋转后的 Y 坐标，右对⻬时⽂本右下⻆坐标为旋转后的 Y 坐标，居中对⻬时⽂本底部中⼼点坐标为旋转后的 Y 坐标。
     * 4. 180° 和 270° 类似，可⾃⾏尝试。
     */
    drawText(
      content: string,
      x: number,
      y: number,
      fontHeight: number,
      dryRun = false,
      options: any | null = null
    ) {
      if (options) {
        options.family = options.family || "宋体";
      }
      // rotation 旋转⻆度，默认为0，⽀持0/90/180/270，旋转中⼼点为左下⻆
      if (dryRun) {
        return;
      }
      JCAPI.drawText(content, x, y, fontHeight, 0, options);
    },

    drawBarcode(
      content: string,
      x: number,
      y: number,
      width: number,
      height: number,
      rotation = 0,
      fontSize: number,
      fontHeight: number,
      position = 2
    ) {
      JCAPI.drawBarcode(
        content,
        x,
        y,
        width,
        height,
        rotation,
        fontSize,
        fontHeight,
        position
      );
    },

    drawQRCode(
      content: string,
      x: number,
      y: number,
      width: number,
      height: number,
      rotation = 0,
      ecc = 2
    ) {
      JCAPI.drawQRCode(content, x, y, width, height, rotation, ecc);
    },

    drawRectangle(
      x: number,
      y: number,
      width: number,
      height: number,
      lineWidth: number,
      isFilled = false,
      rotation = 0
    ) {
      JCAPI.drawRectangle(x, y, width, height, lineWidth, isFilled, rotation);
    },

    /**
     * 绘制线条
     * @param {number} x - 起点x，单位mm
     * @param {number} y - 起点y，单位mm（左下⻆坐标）
     * @param {number} width - 宽度，单位mm
     * @param {number} height - ⾼度，单位mm
     * @param {number} rotation - 旋转⻆度，默认为0，⽀持0/90/180/270，旋转中⼼点左上⻆
     * @description
     * 计算线条的左上⻆为y坐标+线⾼
     */
    drawLine(
      x: number,
      y: number,
      width: number,
      height: number,
      rotation = 0
    ) {
      JCAPI.drawLine(x, y, width, height, rotation);
    },

    drawImage(
      path: string,
      x: number,
      y: number,
      width: number,
      height: number,
      rotation = 0
    ) {
      JCAPI.drawImage(path, x, y, width, height, rotation);
    },

    /* 结束绘制，并在 canvas ⽣成标签图⽚。
     * @param callback 绘制完之后的回调
     */
    endDrawLabel(callback: Callback) {
      JCAPI.endDrawLabel(callback);
    },

    /**
     * 打印
     * @param onePageCount 设置当前⻚的打印份数，如果需要打印3⻚，第⼀⻚打印3份，第⼆⻚打印2份，
     * 第三⻚打印5份，那么在3次提交数据时onePageCount的值分别为3，2，5
     * @param callback 回调,表示可以发送下⼀⻚数据，不表示已打印完成，打印完成监听⻚码变化状态
     * @param {JCSDKPrintOptionParas} optionPara 可选参数
     */
    print(
      onePageCount = 1,
      callback?: Callback,
      optionPara: object | null = null
    ) {
      JCAPI.print(onePageCount, callback || (() => {}), optionPara);
    },

    /**
     * 获取打印过程中，打印失败的错误上报
     * @param callback 回调⻚码，count为⻚码，tid为B32R机型写⼊RFID标签是标签返回的tid数据
     * {'count':int 必需,'tid':string⾮必需}
     */
    didReadPrintCountInfo(callback: Callback) {
      JCAPI.didReadPrintCountInfo(callback);
    },

    /**
     * 获取打印过程中，打印失败的错误上报
     * @param callback 回调错误。
     */
    didReadPrintErrorInfo(callback: Callback) {
      JCAPI.didReadPrintErrorInfo(callback);
    },

    /**
     * 搜索打印机
     * @param {*} didGetScanedPrinters 搜索后的回调 function(list){}
     */
    scanedPrinters(callback: Callback<any[]>) {
      JCAPI.scanedPrinters(callback);
    },

    /**
     * 连接打印机
     * @param {*} printerName 打印名称，如果为空字符串，则打开当前客户端系统上
     * 的第⼀个⽀持的打印机。⽀持直接输⼊打印机型号。
     * @param {*} didConnectedHandler 连接打印机的回调。
     * @param {*} didDisconnectedHandler 断开打印机的回调。
     */
    openPrinter(
      name: string,
      successConnect: () => void,
      failConnect: () => void
    ) {
      JCAPI.openPrinter(name, successConnect, failConnect);
    },

    /**
     * 关闭打印机连接
     */
    closePrinter() {
      JCAPI.closePrinter();
    },

    /**
     * 获取已连接设备
     * @returns {Object} 返回已连接设备 包含 
     * {
     *  deviceId: string, // 设备ID
     *  name: string, // 设备名称
     *  readC: string, // 设备地址
     *  serviceId: string, // 服务ID
     *  write:string; 
     * }
     */
    getConnName(): Object {
      return JCAPI.getConnName();
    },

    /**
     * 获取已连接打印机SN
     * @param {JCSDKPrinterGetParas} callback 可选参数
     */
    getSN(callback: Callback) {
      JCAPI.getSN(callback);
    },
  };
}
