function a0_0x5f1c(_0x748ac,_0x3849a2){const _0x17945a=a0_0x1794();return a0_0x5f1c=function(_0x5f1c9f,_0x33b749){_0x5f1c9f=_0x5f1c9f-0x14b;let _0x1d2e12=_0x17945a[_0x5f1c9f];return _0x1d2e12;},a0_0x5f1c(_0x748ac,_0x3849a2);}function a0_0x1794(){const _0x2dcdfe=['push','JCSDK_PLATFORM_DD','reciveDatas','connectBLEDevice','deviceId','name','sendPrintError','-------搜索到设备','ios','getInfosCallback','characteristics','needCMD','BleAdapterState:--------------------succ','发送了：','readC','--------timeout','_openBleAdapter\x20\x20成功','offBLEConnectionStateChange','createData','112ULYLPb','printer','scanedPrinters','JCSDK_PLATFORM_WX','onBluetoothDeviceFound','对象不存在','notifyBLECharacteristicValueChange','openBluetoothAdapter','AAAA','JCSDK_PLATFORM_FS','value','serviceId','closeBLEConnection','BEF8D6C9-9C21-4C9E-B632-BD58C1009F9F','onBLEConnectionStateChange','writeNoResponse','forEach','onBluetoothAdapterStateChange','getBleAdapterState\x20\x20成功','prototype','devices','31956FPDcyF','toUpperCase','ab2hex','writeC','sdkPlatform','蓝牙连接成功。。。','scanDevicesCallback','0000E0FF-3C17-D293-8E48-14FE2E4DA212','getBLEDeviceCharacteristics','要连接的设备：','285561YAMaAT','---收到数据:','getBluetoothDevices','startsWith','connected','startBluetoothDevicesDiscovery','JCSDK_PRINT_ERR_DISCONNECT','disconnectBLEDevice','length','offBLECharacteristicValueChange','debug','closeBluetoothAdapter','offBluetoothDeviceFound','bleDevice','E7810A71-73AE-499D-8C15-FAA9AEF0C3F2','5555','_getFoundDevices','printErrorInfoCallback','_onValueChange','getBLEDeviceServices','errCode','offBLEConnectionStateChanged','_getBleAdapterState','offBluetoothAdapterStateChange','uuid','重复发送了-----2---command=','stopBluetoothDevicesDiscovery','_createBleConnection','writeBLECharacteristicValue','available','central','getFoundDevices\x20\x20搜索到设备：','characteristicId','getUint8','0000E3E5-0000-1000-8000-00805F9B34FB','_openBleAdapter\x20\x20失败','_stopBleDiscovery','insertAtHeadOfBuffer','scanDevice','readBLECharacteristicValue','蓝牙连接状态','getFoundPrinterByName\x20搜索到设备去连接','bleAdapterStateReady','---needCmd\x20=\x20','hexToBytes','254EGBzmG','11013737tzPprL','log','2729298FrLaKV','---bytelength=','onBLECharacteristicValueChange','clear','0000FFE1-0000-1000-8000-00805F9B34FB','_getDeviceServices','_openBleAdapter','setPrinter','openPrinter','适配器状态发送变化:','----走了失败','services','isEnterpriseWX','dealData','bleConnectionState','6093360CrDWKm','platform','byteLength','BleAdapterState:--------------------1','FFE1','sendData','scanedPrinters\x20\x20\x20\x20startBleDiscovery','901536YqKPup','683730nkAsho','curSendData','android','manager','_startBleDiscovery','setMtuSucc','openPrinterCallback','repeatTimeoutId','connectDeviceFound','bleAdapterSearchState','createBLEConnection','discovering','endsWith','openPrinterCallbackFaill','getBluetoothAdapterState'];a0_0x1794=function(){return _0x2dcdfe;};return a0_0x1794();}const a0_0x55543f=a0_0x5f1c;(function(_0x320e56,_0x1887d6){const _0x1c8074=a0_0x5f1c,_0x2de720=_0x320e56();while(!![]){try{const _0x1685c9=-parseInt(_0x1c8074(0x1b6))/0x1+parseInt(_0x1c8074(0x19c))/0x2*(parseInt(_0x1c8074(0x165))/0x3)+-parseInt(_0x1c8074(0x1b5))/0x4+-parseInt(_0x1c8074(0x1ae))/0x5+-parseInt(_0x1c8074(0x19f))/0x6+parseInt(_0x1c8074(0x19d))/0x7+-parseInt(_0x1c8074(0x150))/0x8*(-parseInt(_0x1c8074(0x16f))/0x9);if(_0x1685c9===_0x1887d6)break;else _0x2de720['push'](_0x2de720['shift']());}catch(_0x3cd11d){_0x2de720['push'](_0x2de720['shift']());}}}(a0_0x1794,0xc0514));import a0_0x37dd60 from'./JCDataUntil';import a0_0x51f155 from'./JCAPIErrorCode';function Ble(_0x5d476f){const _0x3a1191=a0_0x5f1c;this[_0x3a1191(0x199)]=![],this[_0x3a1191(0x1bf)]=![],this[_0x3a1191(0x16b)]=null,this['scanDevice']=[],this[_0x3a1191(0x1b9)]=_0x5d476f,this[_0x3a1191(0x1c7)]='',this['_onBleAdapterStateChange']();}Ble[a0_0x55543f(0x163)]={'constructor':Ble,'scanedPrinters':function(_0x43e249){const _0x254c83=a0_0x55543f;let _0x230ed8=this;(_0x230ed8[_0x254c83(0x1b9)][_0x254c83(0x179)]&0x10000)==0x10000&&console['log']('scanedPrinters\x20\x20开始搜索');var _0x57bd38=function(){_0x43e249!=null&&_0x43e249([]);},_0x23f7d0=function(){const _0x5c18fd=_0x254c83;var _0x1bde56=function(){const _0x22e0a3=a0_0x5f1c;(_0x230ed8['manager']['debug']&0x10000)==0x10000&&console[_0x22e0a3(0x19e)](_0x22e0a3(0x1b4)),_0x230ed8[_0x22e0a3(0x1ba)](),setTimeout(()=>{const _0x3dccc3=_0x22e0a3;_0x230ed8['_stopBleDiscovery']();let _0x24812a=function(){_0x43e249&&_0x43e249(_0x230ed8['scanDevice']);};_0x230ed8[_0x3dccc3(0x195)]=[],_0x230ed8[_0x3dccc3(0x17f)](_0x24812a);},0xbb8);};_0x230ed8[_0x5c18fd(0x185)](_0x1bde56,_0x57bd38);};this['_openBleAdapter'](_0x23f7d0,_0x57bd38);},'closePrinter':function(){const _0x2a8b97=a0_0x55543f;if(this['bleDevice']!=null&&this[_0x2a8b97(0x17c)][_0x2a8b97(0x1c9)]!=null){let _0x2fc0c6=this;var _0x355af3={'deviceId':_0x2fc0c6[_0x2a8b97(0x17c)]['deviceId'],'complete':()=>{const _0x125182=_0x2a8b97;_0x2fc0c6[_0x125182(0x1b9)][_0x125182(0x151)]=null;}};if(this[_0x2a8b97(0x1b9)][_0x2a8b97(0x169)]==a0_0x51f155['JCSDK_PLATFORM_DD'])dd['disconnectBLEDevice'](_0x355af3);else this[_0x2a8b97(0x1b9)]['sdkPlatform']==a0_0x51f155['JCSDK_PLATFORM_FS']?tt[_0x2a8b97(0x176)](_0x355af3):wx[_0x2a8b97(0x15c)](_0x355af3);}},'openPrinter':function(_0x5441f1,_0x56f65e,_0x102552){const _0x376fca=a0_0x55543f;let _0x479062=this;if(this[_0x376fca(0x1ad)]){this[_0x376fca(0x1c3)]&&(this['openPrinterCallbackFaill']=function(){const _0x421538=_0x376fca;_0x479062['bleDevice']=null,_0x479062[_0x421538(0x1b9)][_0x421538(0x151)]=null,setTimeout(()=>{const _0x4e2bfe=_0x421538;_0x479062[_0x4e2bfe(0x1a7)](_0x5441f1,_0x56f65e,_0x102552);},0x1f4);});if(this[_0x376fca(0x17c)]!=null&&this['bleDevice'][_0x376fca(0x1c9)]!=null){if(_0x479062[_0x376fca(0x1b9)][_0x376fca(0x169)]==a0_0x51f155[_0x376fca(0x1c6)])dd[_0x376fca(0x176)]({'deviceId':_0x479062[_0x376fca(0x17c)][_0x376fca(0x1c9)]});else _0x479062[_0x376fca(0x1b9)][_0x376fca(0x169)]==a0_0x51f155[_0x376fca(0x159)]?tt['disconnectBLEDevice']({'deviceId':_0x479062[_0x376fca(0x17c)][_0x376fca(0x1c9)]}):wx['closeBLEConnection']({'deviceId':_0x479062['bleDevice'][_0x376fca(0x1c9)]});}return;}var _0x163060=_0x102552,_0x52371d=function(){const _0x3ea7d8=_0x376fca;(_0x479062[_0x3ea7d8(0x1b9)]['debug']&0x1)==0x1&&console[_0x3ea7d8(0x19e)](_0x3ea7d8(0x1a9));if(_0x163060!=null){let _0x33be98=_0x163060;_0x163060=null,_0x33be98();}},_0x41a09d=![],_0x231206=![],_0x46da43=function(){const _0x2e331a=_0x376fca;if(_0x41a09d)return;_0x41a09d=!![];var _0x515710=function(){_0x231206=!![],_0x479062['_startBleDiscovery']();var _0x3bcbe2=setTimeout(()=>{const _0x12b28d=a0_0x5f1c;(_0x479062[_0x12b28d(0x1b9)][_0x12b28d(0x179)]&0x1)==0x1&&console[_0x12b28d(0x19e)](_0x12b28d(0x14c));_0x479062[_0x12b28d(0x193)]();if(_0x479062[_0x12b28d(0x1be)]==![]){_0x52371d();return;}if(_0x231206)return;_0x52371d();},0x3a98);let _0x5651af=function(_0x3d1963){const _0x486627=a0_0x5f1c;clearTimeout(_0x3bcbe2);let _0x42d3a4=_0x3d1963[_0x486627(0x1c9)];if(_0x42d3a4!=null){let _0x3c70a8=![],_0x2daa62=function(_0x97caba){const _0xecd6a6=_0x486627;if(_0x3c70a8)return;_0x3c70a8=!![];let _0x5265af=function(_0x1f3cce){let _0x443c75=function(_0x409a67){let _0x2234bd=function(){let _0x566b8d=function(){const _0x3a41d6=a0_0x5f1c;_0x479062['openPrinterCallback']=_0x56f65e,_0x479062[_0x3a41d6(0x1c3)]=_0x102552,_0x479062[_0x3a41d6(0x17c)]={},_0x479062[_0x3a41d6(0x17c)][_0x3a41d6(0x1c9)]=_0x97caba,_0x479062[_0x3a41d6(0x17c)][_0x3a41d6(0x15b)]=_0x1f3cce,_0x479062[_0x3a41d6(0x17c)][_0x3a41d6(0x168)]=_0x409a67,_0x479062[_0x3a41d6(0x17c)][_0x3a41d6(0x14b)]=_0x409a67,_0x479062[_0x3a41d6(0x17c)][_0x3a41d6(0x1ca)]=_0x5441f1;if(_0x479062[_0x3a41d6(0x1b9)]['useOutBleListen']){}else _0x479062[_0x3a41d6(0x181)]();let _0x19a77d=a0_0x37dd60['createData'](0xc1,0x1);_0x19a77d=a0_0x37dd60[_0x3a41d6(0x194)](0x3,_0x19a77d),_0x479062['manager'][_0x3a41d6(0x1d0)]=0xc2,_0x479062['sendData'](_0x19a77d,0x9,()=>{const _0x5c0bc9=_0x3a41d6;_0x479062[_0x5c0bc9(0x1c3)]&&(_0x479062[_0x5c0bc9(0x1c3)]=function(){const _0xa8d380=_0x5c0bc9;_0x479062[_0xa8d380(0x17c)]=null,_0x479062[_0xa8d380(0x1b9)][_0xa8d380(0x151)]=null,_0x52371d();});if(_0x479062[_0x5c0bc9(0x1b9)]['sdkPlatform']==a0_0x51f155[_0x5c0bc9(0x1c6)])dd['closeBLEConnection']({'deviceId':_0x479062[_0x5c0bc9(0x17c)][_0x5c0bc9(0x1c9)]});else _0x479062[_0x5c0bc9(0x1b9)]['sdkPlatform']==a0_0x51f155['JCSDK_PLATFORM_FS']?tt[_0x5c0bc9(0x176)]({'deviceId':_0x479062[_0x5c0bc9(0x17c)][_0x5c0bc9(0x1c9)]}):wx[_0x5c0bc9(0x15c)]({'deviceId':_0x479062['bleDevice'][_0x5c0bc9(0x1c9)]});});};_0x479062['_notiCharacteristic'](_0x97caba,_0x1f3cce,_0x409a67,_0x566b8d,_0x52371d);};_0x2234bd();};_0x479062['_getDeviceCharacteristics'](_0x97caba,_0x1f3cce,_0x443c75,_0x52371d);};_0x479062[_0xecd6a6(0x1a4)](_0x97caba,_0x5265af,_0x52371d);};_0x479062['_onConnectionStateChange'](_0x2daa62);if(_0x479062['manager'][_0x486627(0x1af)]==_0x486627(0x1cd))_0x479062[_0x486627(0x18a)](_0x42d3a4,_0x52371d);else{var _0x1a3c5d=0x0;let _0x4e993a=function(){_0x1a3c5d++,setTimeout(()=>{const _0x4d6476=a0_0x5f1c;_0x1a3c5d<0x3?_0x479062['_createBleConnection'](_0x42d3a4,_0x4e993a):_0x479062[_0x4d6476(0x18a)](_0x42d3a4,_0x52371d);},0x3e8);};_0x4e993a();}}else _0x52371d();};_0x479062['_getFoundPrinterByName'](_0x5441f1,_0x5651af);};_0x479062[_0x2e331a(0x185)](_0x515710,_0x52371d);};this[_0x376fca(0x1a5)](_0x46da43,_0x52371d);},'sendData':function(_0x229d33,_0x46cc7e=0xa,_0x1defb8){const _0x4c3e6c=a0_0x55543f;if(this[_0x4c3e6c(0x17c)]==null||this[_0x4c3e6c(0x17c)]===undefined||this['bleDevice']['deviceId']===undefined){(this[_0x4c3e6c(0x1b9)][_0x4c3e6c(0x179)]&0x1)==0x1&&console[_0x4c3e6c(0x19e)](_0x4c3e6c(0x155));this[_0x4c3e6c(0x1bd)]&&this['repeatTimeoutId']>0x0&&(clearTimeout(this[_0x4c3e6c(0x1bd)]),this[_0x4c3e6c(0x1bd)]=0x0);return;}var _0x1197fd=this;function _0x2d17ad(){const _0x1a28a4=_0x4c3e6c;_0x46cc7e>0x0?_0x1197fd[_0x1a28a4(0x1bd)]=setTimeout(()=>{const _0x58986a=_0x1a28a4;if(_0x1197fd[_0x58986a(0x1b9)][_0x58986a(0x151)]&&_0x1197fd[_0x58986a(0x1b9)][_0x58986a(0x151)]['isSendPictureDataing'])return;if(_0x1197fd[_0x58986a(0x1b9)][_0x58986a(0x1b7)]==null)return;const _0x39199c=new DataView(_0x1197fd['manager'][_0x58986a(0x1b7)]);if(_0x1197fd[_0x58986a(0x1b9)][_0x58986a(0x1b7)]&&_0x1197fd['manager'][_0x58986a(0x1b7)]['byteLength']>0x5){let _0x392acc=_0x39199c[_0x58986a(0x190)](0x2);(_0x1197fd[_0x58986a(0x1b9)][_0x58986a(0x179)]&0x100)==0x100&&console[_0x58986a(0x19e)](_0x58986a(0x188)+_0x392acc+_0x58986a(0x1a0)+_0x1197fd['manager']['curSendData'][_0x58986a(0x1b0)]+_0x58986a(0x19a)+_0x1197fd[_0x58986a(0x1b9)][_0x58986a(0x1d0)]);if(_0x392acc==0x86&&_0x1197fd[_0x58986a(0x1b9)]['needCMD']!=0xd3)return;else{if(_0x392acc==0x13&&_0x1197fd[_0x58986a(0x1b9)][_0x58986a(0x1d0)]!=0x14)return;else{if(_0x392acc==0xf3&&_0x1197fd['manager'][_0x58986a(0x1d0)]!=0xf4)return;else{if(_0x392acc==0xa3&&_0x1197fd['manager'][_0x58986a(0x1d0)]!=0xb3)return;}}}}_0x1197fd[_0x58986a(0x1b3)](_0x1197fd['manager']['curSendData'],--_0x46cc7e,_0x1defb8);},0x1f4):(_0x1197fd[_0x1a28a4(0x1b9)][_0x1a28a4(0x1b7)]=null,_0x1defb8&&_0x1defb8());}if(_0x1197fd[_0x4c3e6c(0x1b9)][_0x4c3e6c(0x169)]==a0_0x51f155[_0x4c3e6c(0x1c6)])(this[_0x4c3e6c(0x1b9)][_0x4c3e6c(0x179)]&0x100)==0x100&&console[_0x4c3e6c(0x19e)](_0x4c3e6c(0x1d2)+a0_0x37dd60[_0x4c3e6c(0x167)](_0x229d33)),this[_0x4c3e6c(0x1b9)][_0x4c3e6c(0x1b7)]=_0x229d33,dd[_0x4c3e6c(0x18b)]({'characteristicId':this[_0x4c3e6c(0x17c)][_0x4c3e6c(0x168)],'deviceId':this['bleDevice'][_0x4c3e6c(0x1c9)],'serviceId':this[_0x4c3e6c(0x17c)][_0x4c3e6c(0x15b)],'value':_0x229d33,'success':_0x3563ae=>{},'fail':_0x54aca2=>{},'complete':()=>{_0x2d17ad();}});else{if(_0x1197fd[_0x4c3e6c(0x1b9)][_0x4c3e6c(0x169)]==a0_0x51f155[_0x4c3e6c(0x159)]){var _0x11f1ee=a0_0x37dd60[_0x4c3e6c(0x167)](_0x229d33);(this[_0x4c3e6c(0x1b9)]['debug']&0x100)==0x100&&console[_0x4c3e6c(0x19e)](_0x11f1ee),this[_0x4c3e6c(0x1b9)][_0x4c3e6c(0x1b7)]=_0x229d33,tt[_0x4c3e6c(0x18b)]({'characteristicId':this[_0x4c3e6c(0x17c)][_0x4c3e6c(0x168)],'deviceId':this['bleDevice'][_0x4c3e6c(0x1c9)],'serviceId':this[_0x4c3e6c(0x17c)]['serviceId'],'value':_0x11f1ee,'success':_0x1253cd=>{_0x2d17ad();},'fail':_0x2cc8ca=>{}});}else(this[_0x4c3e6c(0x1b9)]['debug']&0x100)==0x100&&console[_0x4c3e6c(0x19e)](_0x4c3e6c(0x1d2)+a0_0x37dd60[_0x4c3e6c(0x167)](_0x229d33)),this[_0x4c3e6c(0x1b9)][_0x4c3e6c(0x1b7)]=_0x229d33,wx[_0x4c3e6c(0x18b)]({'characteristicId':this[_0x4c3e6c(0x17c)][_0x4c3e6c(0x168)],'deviceId':this[_0x4c3e6c(0x17c)][_0x4c3e6c(0x1c9)],'serviceId':this[_0x4c3e6c(0x17c)][_0x4c3e6c(0x15b)],'value':_0x229d33,'writeType':_0x4c3e6c(0x15f),'success':_0x3a0b11=>{},'fail':_0x5919c0=>{},'complete':()=>{_0x2d17ad();}});}},'clearRepeatAction':function(){const _0x3a340d=a0_0x55543f;this['repeatTimeoutId']>0x0&&(clearTimeout(this[_0x3a340d(0x1bd)]),this[_0x3a340d(0x1bd)]=0x0);},'_getFoundPrinterByName':function(_0x29dd7f,_0x53c2e5){const _0x32722a=a0_0x55543f;let _0x5d3758=this;var _0xb4c603=![];this[_0x32722a(0x1be)]=![];(_0x5d3758[_0x32722a(0x1b9)]['debug']&0x10000)==0x10000&&console[_0x32722a(0x19e)](_0x32722a(0x16e)+_0x29dd7f);if(this[_0x32722a(0x1b9)]['sdkPlatform']==a0_0x51f155[_0x32722a(0x153)]&&this[_0x32722a(0x1b9)][_0x32722a(0x1ab)]==!![]){this[_0x32722a(0x152)](_0x3ae6a8=>{const _0x4fd717=_0x32722a;for(let _0x280d08=0x0;_0x280d08<_0x3ae6a8[_0x4fd717(0x177)];_0x280d08++){const _0x369831=_0x3ae6a8[_0x280d08];if(_0x369831['name']==_0x29dd7f){_0x5d3758[_0x4fd717(0x1be)]=!![];if(_0x53c2e5){_0x5d3758[_0x4fd717(0x193)]();(_0x5d3758['manager'][_0x4fd717(0x179)]&0x10000)==0x10000&&console[_0x4fd717(0x19e)](_0x4fd717(0x198));setTimeout(()=>{_0x53c2e5(_0x369831);},0x1f4);break;}}}});return;}this['connectDeviceFound']=![];var _0x574088=function(_0x2d9a69){const _0x15b059=_0x32722a;if(_0xb4c603)return;if(_0x2d9a69[_0x15b059(0x164)]['length']>0x0){var _0x5140c1=_0x2d9a69[_0x15b059(0x164)][0x0];_0x5140c1['name']==_0x29dd7f&&(_0xb4c603=!![],_0x5d3758['connectDeviceFound']=!![],_0x53c2e5&&(_0x5d3758[_0x15b059(0x193)](),(_0x5d3758['manager'][_0x15b059(0x179)]&0x1)==0x1&&console[_0x15b059(0x19e)](_0x15b059(0x1cc)),_0x53c2e5(_0x5140c1)));}};if(_0x5d3758['manager'][_0x32722a(0x169)]==a0_0x51f155['JCSDK_PLATFORM_DD'])dd[_0x32722a(0x17b)](),dd[_0x32722a(0x154)](_0x574088);else _0x5d3758[_0x32722a(0x1b9)]['sdkPlatform']==a0_0x51f155[_0x32722a(0x159)]?(tt[_0x32722a(0x17b)](),tt[_0x32722a(0x154)](_0x574088)):(wx[_0x32722a(0x17b)](),wx[_0x32722a(0x154)](_0x574088));},'_onValueChange':function(){const _0x3088a8=a0_0x55543f;let _0x5ea902=this;var _0xbb5870=function(_0x1636fb){const _0x25e659=a0_0x5f1c;(_0x5ea902[_0x25e659(0x1b9)][_0x25e659(0x179)]&0x1000)==0x1000&&console[_0x25e659(0x19e)](_0x1636fb[_0x25e659(0x15a)]);if(_0x5ea902[_0x25e659(0x1c7)][_0x25e659(0x177)]>0x0){if(_0x1636fb[_0x25e659(0x15a)]['toUpperCase']()[_0x25e659(0x1c2)](_0x25e659(0x158))){var _0x436fd8=_0x5ea902['reciveDatas']+_0x1636fb['value'];_0x5ea902[_0x25e659(0x1c7)]='',a0_0x37dd60[_0x25e659(0x1ac)](a0_0x37dd60[_0x25e659(0x19b)](_0x436fd8),_0x5ea902['manager']);}else _0x5ea902[_0x25e659(0x1c7)]=_0x5ea902[_0x25e659(0x1c7)]+_0x1636fb['value'];}else _0x1636fb[_0x25e659(0x15a)][_0x25e659(0x172)](_0x25e659(0x17e))&&_0x1636fb[_0x25e659(0x15a)]['toUpperCase']()[_0x25e659(0x1c2)](_0x25e659(0x158))?(_0x5ea902[_0x25e659(0x1b9)][_0x25e659(0x169)]==a0_0x51f155['JCSDK_PLATFORM_FS']&&_0x5ea902[_0x25e659(0x1b9)][_0x25e659(0x1bb)]==![]&&(_0x1636fb[_0x25e659(0x15a)][_0x25e659(0x172)]('5555BF')&&_0x1636fb['value'][_0x25e659(0x166)]()[_0x25e659(0x1c2)]('AAAA')&&_0x1636fb[_0x25e659(0x15a)]['length']>0x32&&(_0x5ea902[_0x25e659(0x1b9)][_0x25e659(0x1bb)]=!![])),_0x5ea902[_0x25e659(0x1c7)]='',a0_0x37dd60[_0x25e659(0x1ac)](a0_0x37dd60[_0x25e659(0x19b)](_0x1636fb[_0x25e659(0x15a)]),_0x5ea902[_0x25e659(0x1b9)])):_0x5ea902[_0x25e659(0x1c7)]=_0x1636fb['value'];};if(_0x5ea902['manager']['sdkPlatform']==a0_0x51f155[_0x3088a8(0x1c6)])dd[_0x3088a8(0x178)](),dd['onBLECharacteristicValueChange'](_0xbb5870);else _0x5ea902['manager'][_0x3088a8(0x169)]==a0_0x51f155[_0x3088a8(0x159)]?(tt[_0x3088a8(0x178)](),tt[_0x3088a8(0x1a1)](_0xbb5870)):wx[_0x3088a8(0x1a1)](_0x3492c7=>{const _0x8972a5=_0x3088a8;(_0x5ea902['manager']['debug']&0x1000)==0x1000&&console[_0x8972a5(0x19e)](_0x8972a5(0x170)+a0_0x37dd60[_0x8972a5(0x167)](_0x3492c7['value'])),a0_0x37dd60[_0x8972a5(0x1ac)](_0x3492c7['value'],_0x5ea902[_0x8972a5(0x1b9)]);});},'onOutValueChange':function(_0x51a200){const _0xba24f8=a0_0x55543f;(_0x51a200['characteristicId'][_0xba24f8(0x166)]()==_0xba24f8(0x15d)||_0x51a200[_0xba24f8(0x18f)][_0xba24f8(0x166)]()=='FFE1'||_0x51a200[_0xba24f8(0x18f)][_0xba24f8(0x166)]()=='0000FFE1-0000-1000-8000-00805F9B34FB')&&((this[_0xba24f8(0x1b9)][_0xba24f8(0x179)]&0x1000)==0x1000&&console[_0xba24f8(0x19e)](_0xba24f8(0x170)+a0_0x37dd60[_0xba24f8(0x167)](_0x51a200[_0xba24f8(0x15a)])),a0_0x37dd60['dealData'](_0x51a200[_0xba24f8(0x15a)],this[_0xba24f8(0x1b9)]));},'_notiCharacteristic':function(_0x275472,_0x4312a9,_0x56f4f5,_0x5baba9,_0x172154){const _0x4a9d6f=a0_0x55543f;let _0x283a5e=this;var _0x1aa4ad={'deviceId':_0x275472,'serviceId':_0x4312a9,'characteristicId':_0x56f4f5,'state':!![],'success':_0x4be5a7=>{_0x5baba9&&_0x5baba9();},'fail':_0x1f9e8e=>{_0x172154&&_0x172154();}};if(_0x283a5e[_0x4a9d6f(0x1b9)][_0x4a9d6f(0x169)]==a0_0x51f155[_0x4a9d6f(0x1c6)])dd[_0x4a9d6f(0x156)](_0x1aa4ad);else _0x283a5e['manager'][_0x4a9d6f(0x169)]==a0_0x51f155[_0x4a9d6f(0x159)]?tt['notifyBLECharacteristicValueChange'](_0x1aa4ad):wx[_0x4a9d6f(0x156)](_0x1aa4ad);},'_readCharacteristic':function(_0x3a5295,_0x49e1a1,_0x12ee68,_0x4c2adc,_0x136cfb){const _0x6c464f=a0_0x55543f;var _0x45103c={'characteristicId':_0x12ee68,'deviceId':_0x3a5295,'serviceId':_0x49e1a1,'success':_0x226e5e=>{_0x4c2adc&&_0x4c2adc();},'fail':_0x2e7f97=>{_0x136cfb&&_0x136cfb();}};if(self[_0x6c464f(0x1b9)][_0x6c464f(0x169)]==a0_0x51f155[_0x6c464f(0x1c6)])dd[_0x6c464f(0x196)](_0x45103c);else self[_0x6c464f(0x1b9)][_0x6c464f(0x169)]==a0_0x51f155[_0x6c464f(0x159)]?tt[_0x6c464f(0x196)](_0x45103c):wx[_0x6c464f(0x196)](_0x45103c);},'_getDeviceCharacteristics':function(_0x5c5aa2,_0x2c0a79,_0x32a33b,_0x139e89){const _0x4a1193=a0_0x55543f;let _0x1c20cc=this;var _0x4750f8=function(_0x21cb3d){const _0x4a861f=a0_0x5f1c;var _0x1a7dbf=![];for(var _0x36b617 in _0x21cb3d[_0x4a861f(0x1cf)]){var _0x1bf56e=_0x21cb3d[_0x4a861f(0x1cf)][_0x36b617];if(_0x1bf56e[_0x4a861f(0x187)]['toUpperCase']()==_0x4a861f(0x15d)||_0x1bf56e[_0x4a861f(0x187)][_0x4a861f(0x166)]()=='FFE1'||_0x1bf56e[_0x4a861f(0x187)][_0x4a861f(0x166)]()==_0x4a861f(0x1a3)){_0x1a7dbf=!![];_0x32a33b&&_0x32a33b(_0x1bf56e['uuid']);break;}}!_0x1a7dbf&&_0x139e89&&_0x139e89();};if(_0x1c20cc[_0x4a1193(0x1b9)]['sdkPlatform']==a0_0x51f155[_0x4a1193(0x1c6)])_0x4750f8=function(_0x254c9e){const _0x8e34c4=_0x4a1193;var _0x57778f=![];for(var _0xb350d0 in _0x254c9e[_0x8e34c4(0x1cf)]){var _0x2801b5=_0x254c9e[_0x8e34c4(0x1cf)][_0xb350d0];if(_0x2801b5[_0x8e34c4(0x18f)][_0x8e34c4(0x166)]()==_0x8e34c4(0x15d)||_0x2801b5[_0x8e34c4(0x18f)][_0x8e34c4(0x166)]()==_0x8e34c4(0x1b2)||_0x2801b5[_0x8e34c4(0x18f)][_0x8e34c4(0x166)]()==_0x8e34c4(0x1a3)){_0x57778f=!![];_0x32a33b&&_0x32a33b(_0x2801b5['characteristicId']);break;}}!_0x57778f&&_0x139e89&&_0x139e89();},dd[_0x4a1193(0x16d)]({'deviceId':_0x5c5aa2,'serviceId':_0x2c0a79,'success':_0x4750f8,'fail':_0x142159=>{_0x139e89&&_0x139e89();}});else _0x1c20cc['manager'][_0x4a1193(0x169)]==a0_0x51f155[_0x4a1193(0x159)]?(_0x4750f8=function(_0x4248b5){const _0x47ed80=_0x4a1193;var _0x473e5b=![];for(var _0xf18167 in _0x4248b5['characteristics']){var _0x2847b4=_0x4248b5[_0x47ed80(0x1cf)][_0xf18167];if(_0x2847b4['characteristicId']['toUpperCase']()==_0x47ed80(0x15d)||_0x2847b4['characteristicId'][_0x47ed80(0x166)]()==_0x47ed80(0x1b2)||_0x2847b4[_0x47ed80(0x18f)][_0x47ed80(0x166)]()=='0000FFE1-0000-1000-8000-00805F9B34FB'){_0x473e5b=!![];_0x32a33b&&_0x32a33b(_0x2847b4['characteristicId']);break;}}!_0x473e5b&&_0x139e89&&_0x139e89();},tt['getBLEDeviceCharacteristics']({'deviceId':_0x5c5aa2,'serviceId':_0x2c0a79,'success':_0x4750f8,'fail':_0x31d5b7=>{_0x139e89&&_0x139e89();}})):wx[_0x4a1193(0x16d)]({'deviceId':_0x5c5aa2,'serviceId':_0x2c0a79,'success':_0x4750f8,'fail':_0x108fdb=>{_0x139e89&&_0x139e89();}});},'_getDeviceServices':function(_0x17a3e5,_0x33c68a,_0x5594b4){const _0x339329=a0_0x55543f;let _0x475b11=this;var _0x5e907f=function(_0x2a2d52){const _0x37174c=a0_0x5f1c;let _0x316f65=![];for(let _0x66adb8 in _0x2a2d52[_0x37174c(0x1aa)]){let _0x1a536e=_0x2a2d52['services'][_0x66adb8];if(_0x1a536e[_0x37174c(0x187)][_0x37174c(0x166)]()==_0x37174c(0x17d)||_0x1a536e[_0x37174c(0x187)][_0x37174c(0x166)]()==_0x37174c(0x16c)||_0x1a536e[_0x37174c(0x187)][_0x37174c(0x166)]()=='0000E3E5-0000-1000-8000-00805F9B34FB'){_0x33c68a&&_0x33c68a(_0x1a536e['uuid']);_0x316f65=!![];break;}}!_0x316f65&&_0x5594b4&&_0x5594b4();},_0x1696fa=function(_0x20a4d9){_0x5594b4&&_0x5594b4();};if(_0x475b11[_0x339329(0x1b9)]['sdkPlatform']==a0_0x51f155[_0x339329(0x1c6)])_0x5e907f=function(_0x32fd8d){const _0x3b3696=_0x339329;let _0x546c90=![];for(let _0x39057f in _0x32fd8d[_0x3b3696(0x1aa)]){let _0x516f4c=_0x32fd8d[_0x3b3696(0x1aa)][_0x39057f];console['log'](_0x516f4c['serviceId']);if(_0x516f4c[_0x3b3696(0x15b)][_0x3b3696(0x166)]()==_0x3b3696(0x17d)||_0x516f4c['serviceId'][_0x3b3696(0x166)]()==_0x3b3696(0x16c)||_0x516f4c['serviceId'][_0x3b3696(0x166)]()==_0x3b3696(0x191)){_0x33c68a&&_0x33c68a(_0x516f4c[_0x3b3696(0x15b)]);_0x546c90=!![];break;}}!_0x546c90&&_0x5594b4&&_0x5594b4();},dd[_0x339329(0x182)]({'deviceId':_0x17a3e5,'success':_0x5e907f,'fail':_0x1696fa});else _0x475b11[_0x339329(0x1b9)][_0x339329(0x169)]==a0_0x51f155[_0x339329(0x159)]?(_0x5e907f=function(_0xe2ca78){const _0x5e1695=_0x339329;let _0x4e4527=![];for(let _0x58b1ee in _0xe2ca78[_0x5e1695(0x1aa)]){let _0x40d2b8=_0xe2ca78[_0x5e1695(0x1aa)][_0x58b1ee];if(_0x40d2b8[_0x5e1695(0x15b)][_0x5e1695(0x166)]()=='E7810A71-73AE-499D-8C15-FAA9AEF0C3F2'||_0x40d2b8[_0x5e1695(0x15b)]['toUpperCase']()==_0x5e1695(0x16c)||_0x40d2b8[_0x5e1695(0x15b)]['toUpperCase']()=='0000E3E5-0000-1000-8000-00805F9B34FB'){_0x33c68a&&_0x33c68a(_0x40d2b8[_0x5e1695(0x15b)]);_0x4e4527=!![];break;}}!_0x4e4527&&_0x5594b4&&_0x5594b4();},tt[_0x339329(0x182)]({'deviceId':_0x17a3e5,'success':_0x5e907f,'fail':_0x1696fa})):wx[_0x339329(0x182)]({'deviceId':_0x17a3e5,'success':_0x5e907f,'fail':_0x1696fa});},'_onConnectionStateChange':function(_0x107b13,_0x3ff98f){const _0x28b981=a0_0x55543f;let _0xead4b7=this;if(_0xead4b7[_0x28b981(0x1b9)][_0x28b981(0x169)]==a0_0x51f155[_0x28b981(0x1c6)])dd['onBLEConnectionStateChanged'](_0x46636b=>{const _0x432fda=_0x28b981;(_0xead4b7['manager'][_0x432fda(0x179)]&0x1)==0x1&&console['log'](_0x46636b),_0xead4b7[_0x432fda(0x1ad)]=_0x46636b[_0x432fda(0x173)],_0x46636b['connected']?_0x107b13&&_0x107b13(_0x46636b[_0x432fda(0x1c9)]):(dd['offBLECharacteristicValueChange'](),dd[_0x432fda(0x184)](),_0xead4b7[_0x432fda(0x1b9)]&&_0xead4b7[_0x432fda(0x1b9)][_0x432fda(0x1a2)](),_0xead4b7[_0x432fda(0x1bd)]>0x0&&clearTimeout(_0xead4b7[_0x432fda(0x1bd)]),_0xead4b7[_0x432fda(0x17c)]!=null&&_0xead4b7[_0x432fda(0x17c)][_0x432fda(0x1c9)]!=null&&(_0xead4b7[_0x432fda(0x17c)][_0x432fda(0x1c9)]=null,_0xead4b7[_0x432fda(0x17c)]=null,_0xead4b7[_0x432fda(0x1c3)]&&_0xead4b7['openPrinterCallbackFaill'](),dd[_0x432fda(0x184)]()),_0x3ff98f&&_0x3ff98f());},_0x4e7da8=>{const _0x784fa2=_0x28b981;(_0xead4b7['manager'][_0x784fa2(0x179)]&0x1)==0x1&&console['log'](_0x4e7da8);});else _0xead4b7['manager']['sdkPlatform']==a0_0x51f155[_0x28b981(0x159)]?(tt[_0x28b981(0x14e)](),tt[_0x28b981(0x15e)](_0x33512c=>{const _0x18c59b=_0x28b981;(_0xead4b7[_0x18c59b(0x1b9)][_0x18c59b(0x179)]&0x1)==0x1&&console[_0x18c59b(0x19e)](_0x33512c),_0xead4b7[_0x18c59b(0x1ad)]=_0x33512c[_0x18c59b(0x173)],_0x33512c[_0x18c59b(0x173)]?_0x107b13&&_0x107b13(_0x33512c[_0x18c59b(0x1c9)]):(_0xead4b7['manager']&&_0xead4b7[_0x18c59b(0x1b9)][_0x18c59b(0x1a2)](),_0xead4b7[_0x18c59b(0x1bd)]>0x0&&clearTimeout(_0xead4b7[_0x18c59b(0x1bd)]),_0xead4b7[_0x18c59b(0x17c)]!=null&&_0xead4b7['bleDevice'][_0x18c59b(0x1c9)]!=null&&(_0xead4b7[_0x18c59b(0x17c)][_0x18c59b(0x1c9)]=null,_0xead4b7['bleDevice']=null,_0xead4b7[_0x18c59b(0x1c3)]&&_0xead4b7[_0x18c59b(0x1c3)](),tt['offBLEConnectionStateChange'](),tt[_0x18c59b(0x17b)](),tt[_0x18c59b(0x186)](),tt['closeBluetoothAdapter']()),_0x3ff98f&&_0x3ff98f());},_0x447176=>{const _0x1420c9=_0x28b981;(_0xead4b7[_0x1420c9(0x1b9)][_0x1420c9(0x179)]&0x1)==0x1&&console['log'](_0x447176);})):(wx['offBLEConnectionStateChange'](),wx[_0x28b981(0x15e)](_0x4f5f96=>{const _0x1f1ace=_0x28b981;(_0xead4b7[_0x1f1ace(0x1b9)]['debug']&0x1)==0x1&&console[_0x1f1ace(0x19e)](_0x4f5f96),(_0xead4b7[_0x1f1ace(0x1b9)][_0x1f1ace(0x179)]&0x10000)==0x10000&&console[_0x1f1ace(0x19e)](_0x1f1ace(0x197)+_0x4f5f96[_0x1f1ace(0x173)]),_0xead4b7['bleConnectionState']=_0x4f5f96[_0x1f1ace(0x173)],_0x4f5f96['connected']?((_0xead4b7[_0x1f1ace(0x1b9)]['debug']&0x10000)==0x10000&&console[_0x1f1ace(0x19e)](_0x1f1ace(0x16a)),_0x107b13&&_0x107b13(_0x4f5f96[_0x1f1ace(0x1c9)])):(_0xead4b7[_0x1f1ace(0x1b9)]&&_0xead4b7[_0x1f1ace(0x1b9)]['clear'](),_0xead4b7[_0x1f1ace(0x1bd)]>0x0&&clearTimeout(_0xead4b7[_0x1f1ace(0x1bd)]),_0xead4b7[_0x1f1ace(0x17c)]!=null&&_0xead4b7[_0x1f1ace(0x17c)][_0x1f1ace(0x1c9)]!=null&&(_0xead4b7[_0x1f1ace(0x17c)][_0x1f1ace(0x1c9)]=null,_0xead4b7['bleDevice']=null,_0xead4b7['openPrinterCallbackFaill']&&_0xead4b7[_0x1f1ace(0x1c3)](),wx[_0x1f1ace(0x14e)](),wx['offBluetoothDeviceFound'](),wx[_0x1f1ace(0x186)](),wx['closeBluetoothAdapter']()),_0x3ff98f&&_0x3ff98f());},_0x2fca2e=>{const _0x14bdf3=_0x28b981;(_0xead4b7[_0x14bdf3(0x1b9)]['debug']&0x1)==0x1&&console['log'](_0x2fca2e);}));},'_createBleConnection':function(_0x5c5d7d,_0x445720){const _0xf44bfe=a0_0x55543f;let _0x2ec182=this;var _0x4d289a=function(_0x461cc7){const _0x3f27ef=a0_0x5f1c;(_0x2ec182[_0x3f27ef(0x1b9)][_0x3f27ef(0x179)]&0x1)==0x1&&console[_0x3f27ef(0x19e)](_0x461cc7);if(_0x2ec182[_0x3f27ef(0x1b9)][_0x3f27ef(0x169)]==a0_0x51f155[_0x3f27ef(0x153)]){if(_0x461cc7[_0x3f27ef(0x183)]==-0x1){wx[_0x3f27ef(0x15c)]({'deviceId':_0x5c5d7d,'complete':()=>{_0x445720&&_0x445720();}});return;}}_0x445720&&_0x445720();};if(_0x2ec182[_0xf44bfe(0x1b9)][_0xf44bfe(0x169)]==a0_0x51f155['JCSDK_PLATFORM_DD'])dd[_0xf44bfe(0x1c8)]({'deviceId':_0x5c5d7d,'success':_0x5e520d=>{const _0x501bfd=_0xf44bfe;if(_0x2ec182[_0x501bfd(0x1b9)][_0x501bfd(0x1af)]==_0x501bfd(0x1b8)){}},'fail':_0x4d289a,'complete':()=>{}});else _0x2ec182[_0xf44bfe(0x1b9)][_0xf44bfe(0x169)]==a0_0x51f155['JCSDK_PLATFORM_FS']?tt['connectBLEDevice']({'deviceId':_0x5c5d7d,'success':_0x140e41=>{const _0x17bd4d=_0xf44bfe;_0x2ec182['manager']['platform']=='android'&&(_0x2ec182[_0x17bd4d(0x1b9)][_0x17bd4d(0x1bb)]=![],tt['setBLEMTU']({'deviceId':_0x5c5d7d,'mtu':0xdf,'success'(_0xb9f522){const _0x4294bb=_0x17bd4d;_0x2ec182['manager'][_0x4294bb(0x1bb)]=!![];},'fail'(_0x448ea9){const _0x2792d0=_0x17bd4d;_0x2ec182[_0x2792d0(0x1b9)][_0x2792d0(0x1bb)]=![];}}));},'fail':_0x4d289a,'complete':()=>{}}):((_0x2ec182['manager'][_0xf44bfe(0x179)]&0x10000)==0x10000&&console[_0xf44bfe(0x19e)]('createBleConnection\x20开始连接'),wx[_0xf44bfe(0x1c0)]({'deviceId':_0x5c5d7d,'success':_0x35643f=>{const _0x5b0502=_0xf44bfe;_0x2ec182[_0x5b0502(0x1b9)][_0x5b0502(0x1af)]==_0x5b0502(0x1b8)&&wx['setBLEMTU']({'deviceId':_0x5c5d7d,'mtu':0xdf});},'fail':_0x4d289a,'complete':()=>{}}));},'_containDevice':function(_0xbc7cd0){const _0x554a62=a0_0x55543f;let _0x1d6f8c=null;(this[_0x554a62(0x1b9)]['debug']&0x1)==0x1&&console[_0x554a62(0x19e)](this['scanDevice']);for(let _0x1f8d79=0x0;_0x1f8d79<this['scanDevice'][_0x554a62(0x177)];_0x1f8d79++){let _0x22b80c=this['scanDevice'][_0x1f8d79];if(_0x22b80c[_0x554a62(0x1ca)]==_0xbc7cd0){_0x1d6f8c=_0x22b80c[_0x554a62(0x1c9)];break;}}return _0x1d6f8c;},'_getFoundDevices':function(_0x4c6bce){const _0x2957fe=a0_0x55543f;let _0x15d340=this;var _0x493f38=function(_0x457d51){const _0x22918b=a0_0x5f1c;var _0x33811c=_0x457d51[_0x22918b(0x164)];_0x33811c[_0x22918b(0x160)](_0x55f769=>{const _0x28c73c=_0x22918b;(_0x15d340[_0x28c73c(0x1b9)]['debug']&0x10000)==0x10000&&console[_0x28c73c(0x19e)](_0x28c73c(0x18e)+_0x55f769[_0x28c73c(0x1ca)]),_0x15d340[_0x28c73c(0x195)][_0x28c73c(0x1c5)](_0x55f769);}),_0x4c6bce&&_0x4c6bce();};if(_0x15d340[_0x2957fe(0x1b9)][_0x2957fe(0x169)]==a0_0x51f155[_0x2957fe(0x1c6)])dd[_0x2957fe(0x171)]({'success':_0x493f38});else _0x15d340['manager'][_0x2957fe(0x169)]==a0_0x51f155[_0x2957fe(0x159)]?tt[_0x2957fe(0x171)]({'success':_0x493f38}):wx[_0x2957fe(0x171)]({'success':_0x493f38});},'_stopBleDiscovery':function(){const _0x5641ac=a0_0x55543f;let _0x26058a=this;if(_0x26058a[_0x5641ac(0x1b9)]['sdkPlatform']==a0_0x51f155['JCSDK_PLATFORM_DD'])dd[_0x5641ac(0x189)]({'success':()=>{},'fail':()=>{},'complete':()=>{}});else _0x26058a[_0x5641ac(0x1b9)]['sdkPlatform']==a0_0x51f155['JCSDK_PLATFORM_FS']?tt[_0x5641ac(0x189)]({'complete':()=>{}}):wx[_0x5641ac(0x189)]({'complete':()=>{}});},'_startBleDiscovery':function(){const _0x2785ad=a0_0x55543f;let _0x331f9d=this;if(_0x331f9d[_0x2785ad(0x1b9)][_0x2785ad(0x169)]==a0_0x51f155[_0x2785ad(0x1c6)])dd[_0x2785ad(0x174)]({'allowDuplicatesKey':!![]});else _0x331f9d['manager'][_0x2785ad(0x169)]==a0_0x51f155[_0x2785ad(0x159)]?tt[_0x2785ad(0x174)]({'allowDuplicatesKey':!![]}):wx['startBluetoothDevicesDiscovery']({'allowDuplicatesKey':!![]});},'_getBleAdapterState':function(_0x442de4,_0xcc0f2d){const _0x1ea788=a0_0x55543f;let _0x2395e6=this;(this['manager']['debug']&0x1)==0x1&&console['log'](_0x1ea788(0x1b1));var _0x2c13e0=function(_0xec6dc7){const _0x5d67c6=_0x1ea788;(_0x2395e6[_0x5d67c6(0x1b9)][_0x5d67c6(0x179)]&0x1)==0x1&&console['log'](_0x5d67c6(0x1d1)),(_0x2395e6[_0x5d67c6(0x1b9)][_0x5d67c6(0x179)]&0x10000)==0x10000&&console[_0x5d67c6(0x19e)](_0x5d67c6(0x162)),_0x442de4&&_0x442de4();},_0x3cfd46=function(){const _0x5ce72d=_0x1ea788;(_0x2395e6[_0x5ce72d(0x1b9)][_0x5ce72d(0x179)]&0x10000)==0x10000&&console[_0x5ce72d(0x19e)]('getBleAdapterState\x20\x20失败'),_0xcc0f2d&&_0xcc0f2d();};if(_0x2395e6[_0x1ea788(0x1b9)]['sdkPlatform']==a0_0x51f155[_0x1ea788(0x1c6)])dd[_0x1ea788(0x1c4)]({'success':_0x2c13e0,'fail':_0x3cfd46});else _0x2395e6[_0x1ea788(0x1b9)][_0x1ea788(0x169)]==a0_0x51f155[_0x1ea788(0x159)]?tt[_0x1ea788(0x1c4)]({'success':_0x2c13e0,'fail':_0x3cfd46}):wx[_0x1ea788(0x1c4)]({'success':_0x2c13e0,'fail':_0x3cfd46});},'_openBleAdapter':function(_0x6910cc,_0xdc39e5){const _0x4c8a4e=a0_0x55543f;let _0x4d3bb3=this;var _0x31f7eb=function(){const _0x8b7468=a0_0x5f1c;(_0x4d3bb3[_0x8b7468(0x1b9)]['debug']&0x10000)==0x10000&&console['log'](_0x8b7468(0x14d)),_0x6910cc&&_0x6910cc();},_0x202857=function(){const _0x59ead3=a0_0x5f1c;(_0x4d3bb3[_0x59ead3(0x1b9)]['debug']&0x10000)==0x10000&&console[_0x59ead3(0x19e)](_0x59ead3(0x192)),_0xdc39e5&&_0xdc39e5();};if(_0x4d3bb3[_0x4c8a4e(0x1b9)][_0x4c8a4e(0x169)]==a0_0x51f155['JCSDK_PLATFORM_DD'])dd[_0x4c8a4e(0x157)]({'autoClose':![],'mode':_0x4c8a4e(0x18d),'success':_0x31f7eb,'fail':_0x202857});else _0x4d3bb3[_0x4c8a4e(0x1b9)][_0x4c8a4e(0x169)]==a0_0x51f155[_0x4c8a4e(0x159)]?tt[_0x4c8a4e(0x157)]({'mode':'central','success':_0x31f7eb,'fail':_0x202857}):wx[_0x4c8a4e(0x157)]({'mode':_0x4c8a4e(0x18d),'success':_0x31f7eb,'fail':_0x202857});},'_onBleAdapterStateChange':function(){const _0x5b23e0=a0_0x55543f;let _0x271243=this;if(_0x271243[_0x5b23e0(0x1b9)][_0x5b23e0(0x169)]==a0_0x51f155['JCSDK_PLATFORM_DD'])dd[_0x5b23e0(0x161)](_0x263335=>{const _0xe90a93=_0x5b23e0;_0x271243[_0xe90a93(0x199)]=_0x263335[_0xe90a93(0x18c)],_0x271243[_0xe90a93(0x1bf)]=_0x263335[_0xe90a93(0x1c1)],_0x263335['available']==![]&&(_0x271243[_0xe90a93(0x1b9)]&&_0x271243[_0xe90a93(0x1b9)]['printer']&&(_0x271243[_0xe90a93(0x1b9)][_0xe90a93(0x1d0)]==0xb3||_0x271243[_0xe90a93(0x1b9)][_0xe90a93(0x1d0)]==0x2||_0x271243[_0xe90a93(0x1b9)][_0xe90a93(0x1d0)]==0xd3||_0x271243[_0xe90a93(0x1b9)][_0xe90a93(0x1d0)]==0x14||_0x271243[_0xe90a93(0x1b9)][_0xe90a93(0x1d0)]==0xe4||_0x271243[_0xe90a93(0x1b9)]['needCMD']==0xf4||_0x271243['manager']['needCMD']==0x71)&&_0x271243[_0xe90a93(0x1b9)][_0xe90a93(0x151)][_0xe90a93(0x1cb)](a0_0x51f155[_0xe90a93(0x175)],_0x271243[_0xe90a93(0x1b9)][_0xe90a93(0x151)][_0xe90a93(0x180)]),_0x271243[_0xe90a93(0x17c)]!=null&&_0x271243[_0xe90a93(0x17c)]['deviceId']!=null&&(_0x271243[_0xe90a93(0x17c)][_0xe90a93(0x1c9)]=null,_0x271243[_0xe90a93(0x17c)]=null,_0x271243['openPrinterCallbackFaill']&&_0x271243[_0xe90a93(0x1c3)]())),(_0x271243['manager'][_0xe90a93(0x179)]&0x1)==0x1&&(console[_0xe90a93(0x19e)](_0xe90a93(0x1a8)),console[_0xe90a93(0x19e)](_0x263335));});else _0x271243[_0x5b23e0(0x1b9)][_0x5b23e0(0x169)]==a0_0x51f155[_0x5b23e0(0x159)]?(tt[_0x5b23e0(0x186)](),tt[_0x5b23e0(0x161)](_0x97adc7=>{const _0x2a9c10=_0x5b23e0;_0x271243['bleAdapterStateReady']=_0x97adc7['available'],_0x271243[_0x2a9c10(0x1bf)]=_0x97adc7[_0x2a9c10(0x1c1)],_0x97adc7['available']==![]&&(tt[_0x2a9c10(0x14e)](),tt['offBluetoothDeviceFound'](),tt[_0x2a9c10(0x186)](),tt[_0x2a9c10(0x17a)](),_0x271243[_0x2a9c10(0x1b9)]&&_0x271243[_0x2a9c10(0x1b9)][_0x2a9c10(0x151)]&&(_0x271243[_0x2a9c10(0x1b9)][_0x2a9c10(0x1d0)]==0xb3||_0x271243[_0x2a9c10(0x1b9)][_0x2a9c10(0x1d0)]==0x2||_0x271243[_0x2a9c10(0x1b9)][_0x2a9c10(0x1d0)]==0xd3||_0x271243[_0x2a9c10(0x1b9)]['needCMD']==0x14||_0x271243['manager'][_0x2a9c10(0x1d0)]==0xe4||_0x271243[_0x2a9c10(0x1b9)][_0x2a9c10(0x1d0)]==0xf4||_0x271243[_0x2a9c10(0x1b9)][_0x2a9c10(0x1d0)]==0x71)&&_0x271243[_0x2a9c10(0x1b9)][_0x2a9c10(0x151)][_0x2a9c10(0x1cb)](a0_0x51f155[_0x2a9c10(0x175)],_0x271243[_0x2a9c10(0x1b9)]['printer'][_0x2a9c10(0x180)]),_0x271243[_0x2a9c10(0x17c)]!=null&&_0x271243[_0x2a9c10(0x17c)][_0x2a9c10(0x1c9)]!=null&&(_0x271243[_0x2a9c10(0x17c)]['deviceId']=null,_0x271243[_0x2a9c10(0x17c)]=null,_0x271243[_0x2a9c10(0x1c3)]&&_0x271243['openPrinterCallbackFaill']())),(_0x271243['manager'][_0x2a9c10(0x179)]&0x1)==0x1&&(console[_0x2a9c10(0x19e)](_0x2a9c10(0x1a8)),console[_0x2a9c10(0x19e)](_0x97adc7));})):(wx[_0x5b23e0(0x186)](),wx[_0x5b23e0(0x161)](_0x4db142=>{const _0x299212=_0x5b23e0;_0x271243['bleAdapterStateReady']=_0x4db142[_0x299212(0x18c)],_0x271243[_0x299212(0x1bf)]=_0x4db142['discovering'],_0x4db142[_0x299212(0x18c)]==![]&&(wx[_0x299212(0x14e)](),wx[_0x299212(0x17b)](),wx[_0x299212(0x186)](),wx[_0x299212(0x17a)](),_0x271243[_0x299212(0x1b9)]&&_0x271243[_0x299212(0x1b9)]['printer']&&(_0x271243[_0x299212(0x1b9)][_0x299212(0x1d0)]==0xb3||_0x271243['manager']['needCMD']==0x2||_0x271243[_0x299212(0x1b9)][_0x299212(0x1d0)]==0xd3||_0x271243[_0x299212(0x1b9)][_0x299212(0x1d0)]==0x14||_0x271243['manager']['needCMD']==0xe4||_0x271243[_0x299212(0x1b9)][_0x299212(0x1d0)]==0xf4||_0x271243['manager']['needCMD']==0x71)&&_0x271243['manager'][_0x299212(0x151)]['sendPrintError'](a0_0x51f155[_0x299212(0x175)],_0x271243[_0x299212(0x1b9)]['printer'][_0x299212(0x180)]),_0x271243[_0x299212(0x17c)]!=null&&_0x271243['bleDevice'][_0x299212(0x1c9)]!=null&&(_0x271243[_0x299212(0x17c)][_0x299212(0x1c9)]=null,_0x271243[_0x299212(0x17c)]=null,_0x271243['openPrinterCallbackFaill']&&_0x271243[_0x299212(0x1c3)]())),(_0x271243[_0x299212(0x1b9)][_0x299212(0x179)]&0x1)==0x1&&(console[_0x299212(0x19e)]('适配器状态发送变化:'),console[_0x299212(0x19e)](_0x4db142));}));},'reciveCmd':function(_0xc74e9c,_0x15473d){const _0x539742=a0_0x55543f;let _0x36bd7b=this;switch(_0xc74e9c){case 0xc2:{function _0x13c102(){const _0x47bd0b=a0_0x5f1c;if(_0x36bd7b[_0x47bd0b(0x1b9)][_0x47bd0b(0x169)]==a0_0x51f155['JCSDK_PLATFORM_DD'])dd[_0x47bd0b(0x176)]({'deviceId':this[_0x47bd0b(0x17c)]['deviceId'],'success':()=>{},'fail':()=>{},'complete':()=>{}});else _0x36bd7b[_0x47bd0b(0x1b9)][_0x47bd0b(0x169)]==a0_0x51f155[_0x47bd0b(0x159)]?tt['disconnectBLEDevice']({'deviceId':this[_0x47bd0b(0x17c)][_0x47bd0b(0x1c9)]}):wx['closeBLEConnection']({'deviceId':this[_0x47bd0b(0x17c)][_0x47bd0b(0x1c9)]});}if(this[_0x539742(0x1b9)]['needCMD']==0xc2){if(_0x15473d==0x2||_0x15473d==0x1||_0x15473d==0x3){this[_0x539742(0x1bd)]&&clearTimeout(this[_0x539742(0x1bd)]);if(_0x15473d==0x1){if(this[_0x539742(0x1c3)]){let _0x1ca74d=this;var _0x2f382f=this[_0x539742(0x1c3)];this[_0x539742(0x1c3)]=function(){const _0x533aa0=_0x539742;_0x1ca74d[_0x533aa0(0x17c)]=null,_0x1ca74d[_0x533aa0(0x1b9)][_0x533aa0(0x151)]=null,_0x2f382f();};}_0x13c102();return;}this[_0x539742(0x1b9)]['agreement']=_0x15473d;let _0x5e92ae=this;this['manager'][_0x539742(0x1ce)]=function(){const _0x4a0de2=_0x539742;_0x5e92ae['repeatTimeoutId']>0x0&&(clearTimeout(_0x5e92ae[_0x4a0de2(0x1bd)]),_0x5e92ae[_0x4a0de2(0x1bd)]=null);let _0x34c7cf=a0_0x37dd60[_0x4a0de2(0x14f)](0x40,0x8);_0x5e92ae[_0x4a0de2(0x1b9)][_0x4a0de2(0x1d0)]=0x48,_0x5e92ae[_0x4a0de2(0x1b3)](_0x34c7cf,0x9,()=>{const _0x524a5f=_0x4a0de2;if(_0x5e92ae[_0x524a5f(0x1c3)]){var _0x1db095=_0x5e92ae[_0x524a5f(0x1c3)];_0x5e92ae[_0x524a5f(0x1c3)]=function(){const _0x3b3c96=_0x524a5f;_0x5e92ae[_0x3b3c96(0x17c)]=null,_0x5e92ae[_0x3b3c96(0x1b9)][_0x3b3c96(0x151)]=null,_0x1db095();};}_0x13c102();});},this['manager'][_0x539742(0x1d0)]=0xb5;let _0x14c2ec=a0_0x37dd60[_0x539742(0x14f)](0xa5,0x1);this[_0x539742(0x1b3)](_0x14c2ec,0x9,()=>{const _0x1a13f9=_0x539742;if(_0x5e92ae[_0x1a13f9(0x1c3)]){var _0x1ef7e3=_0x5e92ae[_0x1a13f9(0x1c3)];_0x5e92ae[_0x1a13f9(0x1c3)]=function(){const _0x38cd7e=_0x1a13f9;_0x5e92ae[_0x38cd7e(0x17c)]=null,_0x5e92ae['manager'][_0x38cd7e(0x151)]=null,_0x1ef7e3();};}_0x13c102();});}}}break;case 0x48:{this['manager']['needCMD']==0x48&&(this['repeatTimeoutId']&&clearTimeout(this['repeatTimeoutId']),this[_0x539742(0x1b9)][_0x539742(0x1a6)](_0x15473d));}break;default:break;}},'openStatus':function(_0x18677e){const _0x107602=a0_0x55543f;let _0x4f1ab3=this;if(_0x18677e)this[_0x107602(0x1bc)]&&this[_0x107602(0x1bc)]();else{if(this[_0x107602(0x17c)]&&this[_0x107602(0x17c)]['deviceId']){if(_0x4f1ab3[_0x107602(0x1b9)]['sdkPlatform']==a0_0x51f155[_0x107602(0x1c6)])dd[_0x107602(0x176)]({'deviceId':this[_0x107602(0x17c)]['deviceId'],'success':()=>{},'fail':()=>{},'complete':()=>{}});else _0x4f1ab3[_0x107602(0x1b9)][_0x107602(0x169)]==a0_0x51f155[_0x107602(0x159)]?tt[_0x107602(0x176)]({'deviceId':this['bleDevice']['deviceId'],'succ':_0x5d8ec4=>{},'fail':_0x21c63f=>{},'complete':()=>{}}):wx['closeBLEConnection']({'deviceId':this['bleDevice'][_0x107602(0x1c9)]});}if(this[_0x107602(0x1c3)]){let _0x205cc8=this[_0x107602(0x1c3)];this['openPrinterCallbackFaill']=null,_0x205cc8();}}}};export default Ble;