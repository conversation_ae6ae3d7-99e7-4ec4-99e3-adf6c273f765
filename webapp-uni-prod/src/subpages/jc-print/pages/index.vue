<template>
  <view class="printer-container">
    <view class="btn primary statu-btn " @tap="goToConnectPage">{{ isConnected ? '打印机已连接' : '连接打印机' }}</view>
    <view v-if="printStatus === PrintJobState.PENDING || printStatus === PrintJobState.COMPLETED" class="btn start-btn"
      @tap="printStatus === PrintJobState.COMPLETED ? restartPrint() : handlePrint()" :class="{ 'primary': isConnected }">{{
        printStatus === PrintJobState.COMPLETED ? '重新打印' :
          '开始打印' }}</view>
    <!-- <view v-else-if="printStatus === PrintJobState.DRAWING" class="btn start-btn primary">正在提交打印数据...</view>-->
    <view v-else-if="printStatus === PrintJobState.PRINTING" class="btn start-btn primary">正在打印...</view>
    <view v-else-if="printStatus === PrintJobState.FAILED" class="btn start-btn" :class="{ 'primary': isConnected }">
      <view class="restart-btn" @tap="restartPrint">重新打印</view>
      <view class="divider"></view>
      <view class="continue-btn" @tap="continuePrint">继续打印</view>
    </view>
  </view>
  <view class="preview">
    <!-- 宽高，单位 px，1mm≈8px（200dpi）-->
    <canvas canvas-id="orderCanvas" id="orderCanvas" :width="canvasWidth" :height="canvasHeight"
      :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"></canvas>
  </view>
</template>
<script lang="ts" setup>
import { ref, getCurrentInstance } from 'vue'
import { usePrint } from '@/subpages/jc-print/composables/print';
import { PrintTakeoutOrder } from '@/subpages/takeout/models';
import useJcPrinterStore from '../store';
import usePrintJobStore from '../store/printJob';
import { PrintJobState } from '../models/PrintJob';
const jcPrinterStore = useJcPrinterStore();
const printJobStore = usePrintJobStore();
const {
  setPlatform,
  getConnName,
  getSN,
  startJob,
  startDrawLabel,
  endDrawLabel,
  drawText,
  drawLine,
  print,
  didReadPrintCountInfo,
  didReadPrintErrorInfo,
} = usePrint();

const labelW: number = 54
const labelH: number = 80
const quantityPerPage: number = 1; // 每页打印数量

/** 
 * 打印机型号：B3S_P 
 * 打印机精度: 200dpi 物理宽度*8，物理高度*8
 * 标签物理宽度：54mm 
 * 标签物理高度：40mm 
 */
const canvasWidth = ref<number>(labelW * 8)
const canvasHeight = ref<number>(labelH * 8)
// 获取当前组件实例
const instance = getCurrentInstance();
// 打印任务
const printJob = computed(() => printJobStore.printJob);
// 总的剩余页数
const remainingPages = computed(() => printJob.value.remainingPages);
// 总页数
const pages = computed(() => printJob.value.pages);
// 已打印页数
const printed = computed(() => printJob.value.printed);
// 已绘制页数
const drawn = computed(() => printJob.value.drawn);
// 是否已连接打印机
const isConnected = computed(() => {
  return jcPrinterStore.getCurrent?.status === 'connected';
});
// 打印任务状态
const printStatus = computed(() => {
  return printJobStore.printJob.status
});
const isFirst = ref<boolean>(true);
// 打印机平台
onMounted(() => {
  uni.getSystemInfo({
    success: (res) => {
      if (res.hostName === 'wechat' || res.hostName === 'wxwork') {
        setPlatform('微信')
      }
    }
  })
})

onUnmounted(() => {
  // 清理打印任务
  printJobStore.clearJob();
  console.log('打印任务已清理', printJobStore.getPrintJobList);
});

onShow(() => {
  // 初始化打印任务
  printJobStore.initJob();
  const connected = getConnName() as any;
  console.log('已连接设备:', connected);
  if (!connected || !connected.deviceId) {
    jcPrinterStore.resetCurrent();
    if (isFirst.value) {
      isFirst.value = false;
      goToConnectPage();
    }
  }
})

// 跳转到连接打印机页面
const goToConnectPage = () => {
  uni.navigateTo({
    url: '/subpages/jc-print/pages/connect/index'
  });
}

// 打印机连接状态变化
const isPrinterConnected = ()=>{
  if (!isConnected.value) {
    console.warn('$$ 打印机未连接，无法开始打印');
    uni.showToast({ title: '请先连接打印机', icon: 'none' });
  }
  return isConnected.value
}

// 重新打印
const restartPrint = () => {
  if(!isPrinterConnected()) return;
  printJobStore.initJob(true);
  console.log('重新打印，已打印页数:', printed.value, '总页数:', pages.value, '已绘制页数:', drawn.value, '剩余页数:', remainingPages.value);
  handlePrint();
};
// 继续打印
const continuePrint = () => {
 if(!isPrinterConnected()) return;
  console.log('继续打印，已打印页数:', printed.value, '总页数:', pages.value);
  if (printed.value >= pages.value) {
    uni.showToast({ title: '所有订单已打印完成', icon: 'none' });
    return;
  }
  printJobStore.initJob();
  handlePrint();
};

// 处理打印逻辑
const handlePrint = () => {
  if (!isConnected.value)return;
  getSN((res: { res: string, code: number }) => {
    console.log('__获取打印机SN', res);
    if (res.code === -4) {
      uni.showToast({ title: '打印机未连接', icon: 'none' });
      return;
    }
    initPrintJob()
  })
};
// 初始化打印任务
const initPrintJob = () => {
  didReadPrintCountInfo((res: { count: number }) => {
    const count = res.count;
    printJobStore.updatePrinted();
    if (count == remainingPages.value) {
      uni.hideLoading();
      uni.showToast({ title: '订单已全部打印完成', icon: 'none' });
      // 打印完成后，标记打印任务为已完成
      printJobStore.markAsCompleted();
    } else {
      // 如果还有订单未打印，继续打印下一页
      uni.showLoading({
        title: `正在打印第 ${printed.value + 1} 份，共 ${pages.value} 份`,
        mask: true
      })
    }
  });

  didReadPrintErrorInfo(res => {
    console.log('错误打印信息', res.errCode, res.msg)
    uni.hideLoading();
    printJobStore.markAsFailed();
    uni.showToast({
      title: `打印失败：${res.errCode}, ${res.msg}`,
      icon: 'none',
      duration: 6000,
      success: () => {
        const connected = getConnName() as any;
        console.log('已连接设备:', connected);
        if (!connected || !connected.deviceId) {
          jcPrinterStore.resetCurrent();
        }
      }
    });
  });
  console.log('==打印任务初始化，剩余页数:', remainingPages.value, '总页数:', pages.value, '已打印页数:', printed.value);
  // 3 连续纸 2 浓度 1 打印1份
  startJob(3, 2, remainingPages.value * quantityPerPage, () => {
    console.log('==打印任务已开始');
    printJobStore.markAsInProgress();// 设置打印状态为打印中
    startPrint()
  });
};

// 开始打印
const startPrint = () => {
  if (instance === null || instance.proxy === null) {
    uni.showToast({ title: '参数错误，请稍后再试', icon: 'none' });
    console.error('@@ 打印参数错误，无法获取当前组件实例');
    printJobStore.markAsFailed();
    return;
  }
  console.log('== 开始打印，当前页数:', printed.value, '总页数:', pages.value, '已绘制页数:', drawn.value);
  const order = printJobStore.getOrderByIndex(drawn.value);
  if (!order) {
    console.warn('$$ 没有可打印的订单数据，无法开始打印');
    uni.showToast({ title: '没有可打印的订单数据', icon: 'none' });
    return;
  }
  // 先算出需要的物理高度 dynamicH（mm）,干跑模式，计算高度
  const dynamicH = handleElementDrawing(order, true)
  // 转成像素并更新 canvas
  canvasHeight.value = dynamicH * 8
  // console.log('动态高度', dynamicH, '像素高度', canvasHeight.value);
  startDrawLabel("orderCanvas", instance.proxy, labelW, dynamicH, 0);
  handleElementDrawing(order);
  finalizePrintJob();
}

// 完成绘制提交打印任务
const finalizePrintJob = () => {
  endDrawLabel(() => {
    // 添加打印任务
    print(quantityPerPage, () => {
      console.log('打印任务已提交', pages.value, drawn.value);
      if (pages.value > drawn.value) {
        printJobStore.updateDrawn();
        startPrint();
      }
    });
  });
}


/**
 * 绘制打印元素
 * @param order 订单数据
 * @param dryRun 是否为干跑模式（不实际绘制，只计算高度）
 * @returns 返回动态高度
 */
const handleElementDrawing = (order: PrintTakeoutOrder, dryRun = false) => {
  let y = 4;              // 从 4mm 下边距开始
  // —— 标题 ——  
  drawText('食堂外卖订餐', 2, y, 5, dryRun);
  y += 10;  // 改成 8mm 间距

  // —— 取餐号 ——  
  drawText(order.pickupCode, labelW / 2, y, 6, dryRun, { align: 'center', bold: true, family: 'Calibri' });
  y += 8;  // 改成 8mm 间距

  // —— 用户信息 ——  
  drawText(`取餐时间：${order.pickupTime}`, 2, y, 3, dryRun); y += 4;
  drawText(`姓名：${order.userName}`, 2, y, 3, dryRun); y += 4;
  drawText(`手机号：${order.phone}`, 2, y, 3, dryRun); y += 4;
  drawText(`下单时间：${order.orderDate}`, 2, y, 3, dryRun); y += 4;
  drawText(`订单号：${order.displayId}`, 2, y, 3, dryRun); y += 8;

  // —— 表头 ——  
  drawText('菜名', 2, y, 3, dryRun);
  drawText('单价(元)', labelW * 0.45, y, 3, dryRun, { align: 'center' });
  drawText('数量', labelW * 0.65, y, 3, dryRun, { align: 'center' });
  drawText('小计(元)', labelW - 2, y, 3, dryRun, { align: 'right' });
  y += 1;
  if (!dryRun) {
    drawPatternLine(
      2,                   // 起点 X
      y,                   // 起点 Y 
      labelW - 4,          // 总长度
      [1.8, 1, 0.5, 1],    // 模式：长(2) 空(1) 短(1) 空(1)
      0.1                  // 线宽 0.2mm
    );
  }
  y += 4;
  // —— 明细行 ——  
  order.takeoutOrderItemList.forEach(item => {
    const subtotal = item.dishPrice * item.dishCount;
    if (item.dishName.length > 5) {
      // 如果菜名过长，换行展示，最多两行，菜名最多8个字
      const lines = item.dishName.match(/.{1,5}/g) || [];
      lines.forEach((line, index) => {
        if (index === 0) {
          drawText(line, 2, y, 3, dryRun);
          drawText(item.dishPrice.toString(), labelW * 0.45, y, 3, dryRun, { align: 'center' });
          drawText(`*${item.dishCount.toString()}`, labelW * 0.65, y, 3, dryRun, { align: 'center' });
          drawText(subtotal.toString(), labelW - 2 - 6, y, 3, dryRun, { align: 'right' });
          y += 3
        } else {
          drawText(line, 2, y, 3, dryRun);
          y += 6;  // 每行之间 6mm
        }
      });
    } else {
      drawText(item.dishName, 2, y, 3, dryRun);
      drawText(item.dishPrice.toString(), labelW * 0.45, y, 3, dryRun, { align: 'center' });
      drawText(`*${item.dishCount.toString()}`, labelW * 0.65, y, 3, dryRun, { align: 'center' });
      drawText(subtotal.toString(), labelW - 2 - 6, y, 3, dryRun, { align: 'right' });
      y += 6;  // 每行之间 7mm
    }
  });
  // —— 合计 ——  
  if (!dryRun) {
    drawPatternLine(
      2,                   // 起点 X
      y - 5,               // 起点 Y（比文字上移 5mm）
      labelW - 4,          // 总长度
      [1.8, 1, 0.5, 1],        // 模式：长(2) 空(1) 短(0.5) 空(1)
      0.1                  // 线宽 0.2mm
    );
  }
  y += 2;
  drawText('合计', 2, y, 3.5, dryRun, { bold: true });
  drawText(`${order.totalPrice}`, labelW - 2 - 6, y, 3.5, dryRun, { bold: true, align: 'right' });
  y += 6
  // console.log('绘制完成,总高度', y);
  return y; // 返回动态高度
};

/**
 * 在 (x, y) 位置绘制一条总长 totalWidth 的虚线
 * @param x           起点 X（mm）
 * @param y           起点 Y（mm）
 * @param totalWidth  虚线总长度（mm）
 * @param pattern     虚线模式数组，表示实线和空隙的长度，单位 mm
 * @param lineWidth   线宽（mm）
 */
const drawPatternLine = (
  x: number,
  y: number,
  totalWidth: number,
  pattern: number[],
  lineWidth = 0.1
) => {
  let offset = 0;      // 已绘制的长度
  let idx = 0;         // pattern 数组索引
  while (offset < totalWidth) {
    const dash = pattern[idx % pattern.length];
    const gap = pattern[(idx + 1) % pattern.length];
    const len = Math.min(dash, totalWidth - offset);
    drawLine(x + offset, y, len, lineWidth, 0);
    offset += dash + gap;
    idx += 2;
  }
}

</script>

<style scoped lang="scss">
.printer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  background: #F4F6FA;
  padding: 0 15px;
}

.btn {
  background: rgba(153, 153, 153, 0.8);
  color: #ffffff;
  margin: 12px 0;
  font-size: 14px;
  border-radius: 3px;
  padding: 0;
  height: 40px;
  width: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn.primary {
  background-color: #0066DF;
}

/* 将canvas元素置于屏幕左侧 */
canvas {
  /* 将canvas的左边缘定位到屏幕宽度的100%处，使其完全超出屏幕左侧显示区域 */
  /* position: absolute; */
  /* left: 100%; */
  /* 新增属性，canvas边框为1px，实线，黑色 ,用于调试时间查看canvas大小*/
  /* border: 1px solid #000; */
  /*新增属性，canvas背景白色，透明度为1，即不透明*/
  background: #fff;
  opacity: 1;
}

.divider {
  width: 21px;
  height: 1px;
  background-color: #fff;
  transform: rotate(90deg);
}

.restart-btn,
.continue-btn {
  width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
