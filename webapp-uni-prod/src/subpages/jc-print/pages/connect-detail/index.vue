<template>
    <view class="printer-detail-page">
        <view class="detail-list">
            <view class="item title">
                <view class="content">
                    <text class="label">设备名称</text>
                    <text class="value">{{ device.name }}</text>
                </view>
            </view>
            <view class="item">
                <view class="content">
                    <view class="action" @tap="toggleConnect" :class="{ disabled: toggleConnectBtnIsDisabled }">{{
                        connectTip
                    }}
                    </view>
                </view>
            </view>
            <view class="item">
                <view class="content">
                    <view class="action" @tap="removeConnect">取消配对</view>
                </view>
            </view>
        </view>
    </view>
</template>
<script lang="ts" setup>
import { BluetoothDevice, BluetoothDeviceStatusEnum } from '../../models';
import useJcPrinterStore from '../../store'
const jcPrinterStore = useJcPrinterStore();
import { usePrint } from '../../composables/print';
const { closePrinter, openPrinter } = usePrint();
const toggleConnectBtnIsDisabled = computed(() => {
    return device.value.status === BluetoothDeviceStatusEnum.CONNECTING || device.value.status === BluetoothDeviceStatusEnum.DISCONNECTING;
})

const connectTip = computed(() => {
    return device.value.status === BluetoothDeviceStatusEnum.CONNECTED ? '断开连接' :
        device.value.status === BluetoothDeviceStatusEnum.DISCONNECTED ? '连接' :
            device.value.status === BluetoothDeviceStatusEnum.CONNECTING ? '正在连接...' :
                device.value.status === BluetoothDeviceStatusEnum.DISCONNECTING ? '正在断开连接...' : '';
})
// 当前处理的设备
const device = computed(() => {
    return jcPrinterStore.getCurHandleDevice as BluetoothDevice;
});

const toggleConnect = () => {
    // 如果正在连接或断开连接，则不执行任何操作
    if (toggleConnectBtnIsDisabled.value) {
        return;
    }
    if (device.value.status === BluetoothDeviceStatusEnum.CONNECTED) {
        // 断开连接
        device.value.status = BluetoothDeviceStatusEnum.DISCONNECTING;
        closePrinter();
        jcPrinterStore.disconnectPrinter(device.value.deviceId);
    } else {
        // 连接
        device.value.status = BluetoothDeviceStatusEnum.CONNECTING;
        openPrinter(
            device.value.name,
            () => {
                device.value.status = BluetoothDeviceStatusEnum.CONNECTED;
                jcPrinterStore.openPrinter(device.value);
            },
            () => {
                uni.showToast({
                    icon: 'none',
                    title: '打印机连接失败，请确认打印机是否开机',
                });
            },
        );
    }
}

// 取消配对 
const removeConnect = () => {
    uni.showModal({
        title: '提示',
        content: '确定取消配对吗？',
        success: (res) => {
            if (res.confirm) {
                jcPrinterStore.removePrinter(device.value.deviceId);
                uni.navigateTo({
                    url: '/subpages/jc-print/pages/connect/index',
                });
            }
        }
    });
}

</script>
<style lang="scss" scoped>
.printer-detail-page {
    height: 100%;
    width: 100%;
    background: #F4F6FA;
    padding: 15px;
    box-sizing: border-box;

    .detail-list {
        border-radius: 10px;
        width: 100%;
        box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);
        background: rgba(255, 255, 255, 1);

        .item {
            height: 48px;
            display: flex;
            align-items: center;
            width: 100%;

            padding: 0 15px;

            .content {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-bottom: 1px solid rgba(153, 153, 153, 0.2);
            }

            .action {
                color: #3B6EEB;
                font-size: 14px;
                padding: 4px 10px;

                &.disabled {
                    color: #999999;
                    cursor: not-allowed;
                }
            }

            &:last-child {
                border-bottom: none;
                border-bottom-left-radius: 10px;
                border-bottom-right-radius: 10px;
            }

            &:first-child {
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
            }

            &.title {
                background: rgba(247, 247, 247, 1);
                font-weight: medium;

                .value {
                    color: #666666;
                }
            }
        }
    }
}
</style>