<template>
    <view class="bluetooth-wrapper">
        <view class="device-connected">
            <view class="connect-title flex-align-center">
                已配对设备
            </view>
            <view class="device-item-placeholder flex-align-center" v-if="connectedList.length === 0">
                <SlSubSvgIcon class="bluetooth-icon" subpage="jc-print" name="20-20-36" size="20" />
                <text class="device-name">--</text>
            </view>
            <view class="device-list" v-else>
                <view class="device-item  flex-space-between" v-for="(item) in connectedList" :key="item.deviceId">
                    <SlSubSvgIcon class="bluetooth-icon" subpage="jc-print" name="20-20-36" size="20"
                        @tap="toggleConnect(item)" />
                    <view v-if="getStatusText(item)" class="device-name toggle"
                        :class="{ connecting: item.status == 'connecting', connected: item.status == 'connected', disconnecting: item.status == 'disconnecting' }">
                        <view class="title" @tap="toggleConnect(item)">{{ item.name }}</view>
                        <view class="tip">{{ getStatusText(item) }}
                        </view>
                    </view>
                    <view v-else class="device-name" @tap="toggleConnect(item)">
                        {{ item.name }}
                    </view>
                    <view class="action" @tap="goToDetail(item)">
                        <SlSubSvgIcon class="bluetooth-icon" subpage="jc-print" name="12-12-6" size="12" />
                    </view>
                </view>
            </view>
        </view>
        <view class="device-unconnected">
            <view class="connect-title  flex-space-between ">
                <text class="label">可用设备</text>
                <view class="search-btn" @tap="handleSearchDevice">{{ scannAction == 2 ? '刷新' : '搜索设备' }}</view>
            </view>
            <view class="device-item-placeholder" v-if="buletoothList.length === 0">
                <SlSubSvgIcon class="bluetooth-icon" subpage="jc-print" name="20-20-36" size="20" />
                <text class="device-name">--</text>
            </view>

            <view class="device-list">
                <view class="device-item flex-align-center" v-for="(item) in buletoothList" :key="item.deviceId"
                    @tap="handleConnect(item)">
                    <SlSubSvgIcon class="bluetooth-icon" subpage="jc-print"
                        :name="item.isPrinter ? '20-20-36' : '20-20-37'" size="20" />
                    <view v-if="item.status == 'connecting'" class="device-name connecting">
                        <view class="title">{{ item.name }}</view>
                        <view class="tip">正在配对...</view>
                    </view>
                    <view v-else class="device-name">
                        {{ item.name }}
                    </view>
                </view>
            </view>

        </view>
        <!--连接提示-->
        <view class="device-connect-tip">
            <view class="title">温馨提示</view>
            <view>1.首次使用需将手机与打印机配对</view>
            <view>2.打印时请保持打印机与蓝牙均属于开启状态</view>
            <view>3.未搜索到可用机型,可尝试以下解决方法:</view>
            <view class="sub">a.检查打印机是否打开状态</view>
            <view class="sub">b.关闭手机蓝牙重新打开</view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { BluetoothDevice, BluetoothDeviceStatusEnum } from '../../models';
import { usePrint } from '../../composables/print';
import useJcPrinterStore from '../../store'
const { scanedPrinters, openPrinter, closePrinter } = usePrint();
const jcPrinterStore = useJcPrinterStore();
const scannAction = ref<0 | 1 | 2>(0); // 0:未扫描, 1:正在扫描, 2:已扫描
// 已连接过的打印机数据
const connectedList = computed(() => jcPrinterStore.getConnectedList)
// 搜索到的蓝牙设备数据
const buletoothList = ref<BluetoothDevice[]>([]);

onLoad(() => {
    // 初始化时获取已连接的打印机列表
    const curr = jcPrinterStore.getCurrent;
    console.log('当前连接的打印机:', curr);
    if (curr && curr.deviceId && curr.status !== BluetoothDeviceStatusEnum.CONNECTED) {
        handleConnect(curr);
    }
})

const getStatusText = (item: BluetoothDevice) => {
    switch (item.status) {
        case BluetoothDeviceStatusEnum.CONNECTING:
            return '正在连接...';
        case BluetoothDeviceStatusEnum.CONNECTED:
            return '已连接';
        case BluetoothDeviceStatusEnum.DISCONNECTING:
            return '正在断开连接...';
        default:
            return '';
    }
}

const handleSearchDevice = () => {
    uni.showLoading({
        title: '搜索打印机中...',
        mask: true,
    });
    // 清空之前的蓝牙设备列表
    buletoothList.value = [];
    scannAction.value = 1; // 设置为正在扫描状态
    // 扫描之前的打印机列表
    scanedPrinters((printers: BluetoothDevice[]) => {
        printers = printers.filter((val) => val.name && !val.name.includes("未知设备"));
        // 过滤掉已连接的设备
        const arr = printers.filter((item) => {
            return !connectedList.value.some((connectedItem) => connectedItem.deviceId === item.deviceId);
        })
        // 更新蓝牙设备列表
        buletoothList.value = arr.map((item) => {
            return {
                ...item,
                isPrinter: item.name.includes('B3S'), // 名称中包含"B3S"的为打印机设备
            };
        });
        uni.hideLoading();
        scannAction.value = 2; // 设置为已扫描状态
        if (buletoothList.value.length === 0) {
            uni.showToast({
                icon: 'none',
                title: '未搜索到可用打印机，请检查设备是否开启',
            });
        }
        jcPrinterStore.setAvailable(buletoothList.value);
    });
}

// 连接打印机
const handleConnect = (item: BluetoothDevice) => {
    item.status = BluetoothDeviceStatusEnum.CONNECTING;
    openPrinter(
        item.name,
        () => {
            item.status = BluetoothDeviceStatusEnum.CONNECTED;
            jcPrinterStore.openPrinter(item);
            buletoothList.value = buletoothList.value.filter(val => {
                return connectedList.value.every(connectedItem => connectedItem.deviceId !== val.deviceId);
            })
        },
        () => {
            uni.showToast({
                icon: 'none',
                title: '打印机连接失败，请确认打印机是否开机',
                success: () => {
                    item.status = BluetoothDeviceStatusEnum.UNKNOWN;
                },
            });
        },
    );
}

const toggleConnect = (item: BluetoothDevice) => {
    if (item.status === BluetoothDeviceStatusEnum.CONNECTED) {
        uni.showModal({
            title: '断开连接',
            content: `将断开与 “${item.name}” 的连接`,
            success: (res) => {
                if (res.confirm) {
                    disconnectPrinter(item);
                }
            },
        });
    } else {
        // 连接打印机
        handleConnect(item);
    }
}

const disconnectPrinter = (item: BluetoothDevice) => {
    item.status = BluetoothDeviceStatusEnum.DISCONNECTING;
    setTimeout(() => {
        closePrinter();
        item.status = BluetoothDeviceStatusEnum.DISCONNECTED;
        // 从已连接列表中移除
        jcPrinterStore.disconnectPrinter(item.deviceId);
    }, 1000);
}

const goToDetail = (item: BluetoothDevice) => {
    jcPrinterStore.setCurHandleDevice(item);
    uni.navigateTo({
        url: `/subpages/jc-print/pages/connect-detail/index`,
    });
}

</script>

<style lang="scss" scoped>
.bluetooth-icon {
    display: inline-flex;
}

.flex-align-center {
    display: flex;
    align-items: center;
}

.flex-space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.device-item-placeholder {
    border-radius: 10px;
}

.device-item-placeholder,
.device-item {
    height: 48px;
    width: 100%;
    background: #FFFFFF;
    padding: 0 15px;
    display: flex;
    align-items: center;
}

.device-item-placeholder>.device-name {
    margin-left: 12px;
}

.device-list {
    border-radius: 10px;
    background: #FFFFFF;
    box-shadow: 0px 0px 4px 0px rgba(146, 146, 146, 0.2);

    .device-item {
        position: relative;

        .bluetooth-icon {
            flex: 0 0 20px;
            margin-right: 12px;
        }

        view.device-name {
            display: flex;
            align-items: center;
            flex: 1;
            height: 100%;
            box-sizing: border-box;
            border-bottom: 1px solid rgba(153, 153, 153, 0.2);
            color: #333333;
            font-size: 14px;

            &.toggle {
                margin-right: 48px;
            }

            &.connecting,
            &.connected,
            &.disconnecting {
                align-items: flex-start;
                justify-content: center;
                flex-direction: column;

                .title {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    width: 100%;

                }

                .tip {
                    width: 100%;
                    font-size: 12px;
                    margin-top: 6px;
                    color: #999999;
                }
            }

        }

        view.action {
            position: absolute;
            right: 0;
            height: 100%;
            width: 48px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 12px;

            .bluetooth-icon {
                margin-right: 0;
                flex: 0;
            }
        }

        &:last-child {
            border-bottom: none;
            border-bottom-right-radius: 10px;
            border-bottom-left-radius: 10px;
        }

        &:first-child {
            border-top-right-radius: 10px;
            border-top-left-radius: 10px;
        }
    }
}

.bluetooth-wrapper {
    height: 100%;
    background: #F4F6FA;
    padding: 0 15px;

    .device-connected,
    .device-unconnected {
        display: flex;
        flex-direction: column;

        .connect-title {
            height: 42px;
            font-size: 12px;
            color: #666666;

            .search-btn {
                border: none;
                background: transparent;
                color: #3B6EEB;
                font-size: 12px;
                padding: 4px 0 6px;
            }
        }
    }

    .device-connect-tip {
        display: flex;
        flex-direction: column;
        margin-top: 30px;
        width: 100%;
        font-size: 12px;
        color: #666666;

        .title {
            color: #333333;
            margin-bottom: 10px;
        }

        .sub {

            padding-left: 15px;
        }
    }

    .device-unconnected {
        .device-name.connecting>.title {
            color: #999999;
        }
    }

}
</style>