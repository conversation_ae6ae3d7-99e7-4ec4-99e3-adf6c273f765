/**
 * 任务状态枚举
 * @enum {string}
 * @property {string} PENDING - 待处理
 * @property {string} PRINTING - 打印中
 * @property {string} COMPLETED - 已完成
 * @property {string} FAILED - 打印失败
 */
export enum PrintJobState {
  PENDING = "pending",
  PRINTING = "printing",
  COMPLETED = "completed",
  FAILED = "failed",
}

export interface PrintOrderItem {
  orderId: string; // 订单ID
  idx: number; // 订单在打印任务中的索引
  // status?: PrintJobState; // 任务状态
}

/**
 * 打印任务
 * @interface
 */
export interface PrintJob {
  orderDate: string; // 订单日期
  items: PrintOrderItem[]; // 订单项列表
  createdAt: number; // 创建时间，毫秒数
  status: PrintJobState; // 整体打印任务状态
  printerId?: string; // 打印机ID
  printed: number; // 已打印页数
  drawn: number; // 已绘制页数
  pages: number; // 总页数
  remainingPages: number; // 剩余未打印页数
}
