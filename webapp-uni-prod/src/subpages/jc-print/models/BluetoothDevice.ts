export enum BluetoothDeviceStatusEnum {
  CONNECTED = "connected", // 已连接
  CONNECTING = "connecting", // 正在连接
  DISCONNECTING = "disconnecting", // 正在断开连接
  DISCONNECTED = "disconnected", // 已断开连接
  UNKNOWN = "unknown", // 未知状态、默认状态
}

export type BluetoothDeviceStatus =
  | "connected" // 已连接
  | "connecting" // 正在连接
  | "disconnecting" // 正在断开连接
  | "disconnected" // 已断开连接
  | "unknown"; // 未知状态、默认状态

export interface BluetoothDevice {
  name: string;
  deviceId: string;
  isPrinter?: boolean; // 是否为打印机设备
  status?: BluetoothDeviceStatus; // 设备状态
}
