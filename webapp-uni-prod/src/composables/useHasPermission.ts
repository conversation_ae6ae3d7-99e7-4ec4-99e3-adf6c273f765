import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

export const useHasPermission = () => {
  const route = useRoute();
  const isSuper = ref(false);
  onMounted(() => {
    checkIfSuperAdmin();
  });
  /** 校验当前是否属于超管 */
  const checkIfSuperAdmin = (): boolean => {
    isSuper.value = route.path.includes('super');
    return isSuper.value;
  };
  return { checkIfSuperAdmin, isSuper };
};
