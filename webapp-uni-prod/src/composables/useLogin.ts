import { ref } from "vue";
import { AuthLoginUser } from "@/models/Account";
import authService from "@/service/auth";
import { usePrincipalStore } from "@/store";

// 登录方式类型定义, quick 为快速登录，phone 为手机号输入登录，password 为账号密码输入登录
export type LoginType = "quick" | "phone" | "password";

export function useLogin() {
  const principalStore = usePrincipalStore();

  // 当前登录方式
  const curLoginType = ref<LoginType | undefined>(undefined);

  // 执行登录流程
  async function login(user: AuthLoginUser, type: LoginType) {
    curLoginType.value = type;
    console.log("登录用户信息:", user);
    console.log("当前登录方式:", curLoginType.value);
    try {
      await authService.authenticate(user);
      await wxLogin();
      await principalStore.identity(true);
      uni.showToast({ title: "登录成功", icon: "success" });
      uni.switchTab({ url: "/pages/home/<USER>" });
      // uni.navigateTo({ url: "/subpages/takeout/pages/admin/index" });
    } catch (err) {
      console.log("登录失败的原因:", err);
      await principalStore.clearIndentity();
    }
  }

  // 微信快速登录：获取手机号 + 登录
  // 微信文档：https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/getPhoneNumber.html
  async function getPhoneNumber(e: any) {
    if (
      e.detail.errMsg.includes("fail") ||
      e.detail.errMsg.includes("cancel")
    ) {
      uni.showToast({ title: "用户拒绝授权", icon: "none" });
      return;
    }
    if (e.detail.errMsg.includes("ok") && e.detail.code) {
      const { code } = e.detail;
      const wxAccount = await authService.getWechatPhoneForLogin(code);
      if (wxAccount?.phoneNumber && wxAccount?.loginCode) {
        const user: AuthLoginUser = {
          j_username: wxAccount.phoneNumber,
          j_password: wxAccount.loginCode,
        };
        await login(user, "quick");
        return;
      }
    }
    uni.showToast({ title: "获取手机号失败，请稍后再试", icon: "none" });
  }

  // uni-app 微信登录并绑定
  function wxLogin(): Promise<void> {
    return new Promise((resolve, reject) => {
      uni.login({
        provider: "weixin",
        success: async ({ code }) => {
          try {
            await authService.bindWechatCode(code);
            resolve();
          } catch {
            reject(new Error("微信登录绑定失败"));
          }
        },
        fail: (err) => reject(err),
      });
    });
  }

  return {
    curLoginType,
    login,
    getPhoneNumber,
    wxLogin,
  };
}
