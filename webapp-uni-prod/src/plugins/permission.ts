import {
  ERROR404_PATH,
  isPathExists,
  LOGIN_PATH,
  removeQueryString,
  routes,
} from "@/router";
import { usePrincipalStore } from "@/store";

/**
 * 场景：
 * 1、外部链接进入到小程序某个页面A，此时用户没有登录，经过拦截器拦截到登录页面
 * 2、用户登录成功后，需要重定向到之前访问的页面A
 *
 * 问题：
 * 1、如果用户在页面A操作返回，直接返回到登录页面，而不是首页
 *
 * 解决方法：
 * 1、在pages.json（pages.config配置） 中需要配置页面A为自定义导航栏，navigationStyle: "custom"
 * 2、在页面A 配合使用components/sl-tabbar/SlTabbarPage.vue 组件
 * 3、设置 pressBackPath 地址，首页可直接设置 '/'
 * 4、在登录页面中，登录成功后，重定向页面A，页面A通过 SlTabbarPage 会自动处理返回逻辑
 * * 注意：pressBackPath 只在自定义导航栏页面中有效
 *
 * 需要重定向的页面路径列表
 * 注意：此处的路径是相对于小程序根目录的路径，不包含 query 参数
 * 例如：'/pages/home/<USER>'，而不是 '/pages/home/<USER>'
 */
const redirectUrls: string[] = [];
// 白名单路由,默认只有登录页面不需要权限校验
const whiteList: string[] = [];
console.log("路由列表:", routes);
routes.forEach((item) => {
  if (item.needLogin === false) {
    whiteList.push(item.path);
  }
  if (item.redirect === true) {
    redirectUrls.push(item.path);
  }
});
console.log("白名单:", whiteList);
console.log("重定向列表:", redirectUrls);

export function hasReirect(path = "") {
  console.log(`重定向检测: ${path}`);
  // 检测路径是否在重定向列表中
  const hasRedirect = redirectUrls.includes(removeQueryString(path));
  return hasRedirect;
}
/**
 * 检测用户是否登录
 * @returns {boolean} 是否登录
 */
const isLogin = () => {
  const principalStore = usePrincipalStore();
  return principalStore.isAuthenticated;
};
/**
 * 权限校验
 * @param {string} path
 * @returns {boolean} 是否有权限
 */
export function hasPerm(path = "") {
  console.log(`权限检测: ${path}`);
  if (!isPathExists(path) && path !== "/") {
    uni.redirectTo({
      url: ERROR404_PATH,
    });
    console.warn(`Path not found: ${path}`);
    return false;
  }
  const isWhitePath = whiteList.includes(removeQueryString(path));
  // 在白名单中或有token，直接放行
  const hasPermission = isWhitePath || isLogin();
  if (!hasPermission) {
    // 将用户的目标路径传递过去，这样可以实现用户登录之后，直接跳转到目标页面
    uni.redirectTo({
      url: `${LOGIN_PATH}?redirect=${encodeURIComponent(path)}`,
    });
  }
  return hasPermission;
}

function setupPermission() {
  console.log("Setting up permission interceptor...");
  // 注意：拦截uni.switchTab本身没有问题。但是在微信小程序端点击tabbar的底层逻辑并不是触发uni.switchTab。
  // 所以误认为拦截无效，此类场景的解决方案是在tabbar页面的页面生命周期onShow中处理。
  ["navigateTo", "redirectTo", "reLaunch", "switchTab"].forEach((item) => {
    // https://uniapp.dcloud.net.cn/api/interceptor.html
    uni.addInterceptor(item, {
      // 页面跳转前进行拦截, invoke根据返回值进行判断是否继续执行跳转
      invoke(args) {
        // args为所拦截api中的参数，比如拦截的是uni.redirectTo(OBJECT)，则args对应的是OBJECT参数
        return hasPerm(args.url);
      },
    });
  });
}

export default setupPermission;
