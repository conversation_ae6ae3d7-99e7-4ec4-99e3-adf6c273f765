export interface ServiceItem {
  icon: string;
  label: string;
  url: string;
  // 新增字段，用于区分不同场景的图标变体
  iconVariant?: string;
}

export interface ServiceCard {
  title: string;
  items: ServiceItem[];
}

export interface TabData {
  popular: ServiceItem[][];
  frequent: ServiceItem[][];
}
// 管理服务
export const MANAGE_SERVICE = {
  // 理发管理
  HAIRCUT_MANAGE: {
    icon: '26-26-6',
    label: '理发管理',
    url: `/subpages/haircut/pages/appointment/index?type=admin`,
  },

  // 菜谱管理
  MENU_MANAGE: {
    icon: '26-26-4',
    label: '菜谱管理',
    url: '/subpages/cookbook/pages/manage/home',
  },

  // 外卖管理
  TAKEOUT_MANAGE: {
    icon: '26-26-3',
    label: '外卖管理',
    url: '/subpages/takeout/pages/admin/index',
  },

  // 报修管理
  REPAIR_MANAGE: {
    icon: '26-26-1',
    label: '报修管理',
    url: '/subpages/repair/pages/index?type=admin',
  },

  // 班车管理 司机端发车
  // BUS_DRIVER_MANAGE: {
  //   icon: '26-26-2',
  //   label: '班车发车',
  //   url: '/subpages/commute-manager/shuttle-departure/index', // 司机端
  // },
  // 班车管理 职工端查看
  BUS_MANAGE: {
    icon: '26-26-2',
    label: '通勤班车',
    url: '/subpages/commute-manager/commute-shuttle/index', // 职工端
  },
  // 人事管理
  HR_MANAGE: {
    icon: '26-26-9',
    label: '人事管理',
    url: '/subpages/persons/index',
  },

  // 配置管理
  CONFIG_MANAGE: {
    icon: '26-26-18',
    label: '配置管理',
    url: '/subpages/config-manager/index',
  },

  // 巡检管理
  INSPECTION_MANAGE: {
    icon: '26-26-16',
    label: '巡检管理',
    url: '/subpages/inspection/record/index',
  },

  // 物业巡检
  PROPERTY_INSPECTION: {
    icon: '26-26-17',
    label: '物业巡检',
    url: '/subpages/inspection/patrol/property-patrol',
  },

  // 白班巡检
  DAY_SHIFT_INSPECTION: {
    icon: '26-26-19',
    label: '白班巡检',
    url: '/subpages/inspection/patrol/day-patrol',
  },

  // 晚班巡检
  NIGHT_SHIFT_INSPECTION: {
    icon: '26-26-20',
    label: '夜班巡检',
    url: '/subpages/inspection/patrol/night-patrol',
  },

  // 保洁清洁
  CLEANING_MANAGE: {
    icon: '26-26-21',
    label: '保洁清洁',
    url: '/subpages/inspection/patrol/cleaning-patrol',
  },
};

// 所有服务项的常量定义
export const SERVICE_ITEMS = {
  // 基础服务项
  COMMUTE_BUS: {
    icon: '26-26-2',
    label: '通勤班车',
    url: '/subpages/commute-manager/commute-shuttle/index', // 职工端
    iconVariant: '26-26-2', // 与基础图标相同
  },
  FAULT_REPAIR: {
    icon: '26-26-1',
    label: '故障报修',
    url: '/subpages/repair/pages/index',
    iconVariant: '20-20-1',
  },
  WEEKLY_MENU: {
    icon: '26-26-4',
    label: '每周菜谱',
    url: '/subpages/cookbook/pages/weekly-menu/index',
    iconVariant: '20-20-3',
  },
  TAKEOUT: {
    icon: '26-26-3',
    label: '外卖订餐',
    url: '/subpages/takeout/pages/staff/index',
    iconVariant: '20-20-8',
  },
  // 管理端外卖订餐
  TAKEOUT_ADMIN: {
    icon: '26-26-3',
    label: '外卖订餐',
    url: '/subpages/takeout/pages/admin/index',
    iconVariant: '20-20-8',
  },
  HAIRCUT: {
    icon: '26-26-6',
    label: '理发预约',
    url: '/subpages/haircut/pages/appointment/index',
    iconVariant: '20-20-9',
  },
  MEETING: {
    icon: '26-26-5',
    label: '会议助手',
    url: '',
    iconVariant: '20-20-4',
  },
  CAR_MOVE: {
    icon: '26-26-11',
    label: '挪车服务',
    url: '',
    iconVariant: '20-20-6',
  },
  ALL_SERVICES: {
    icon: '26-26-14',
    label: '全部',
    url: '/pages/home/<USER>/index',
  },

  // 其他服务项
  ASSETS: {
    icon: '26-26-7',
    label: '固定资产管理',
    url: '',
  },
  VEHICLE_DISPATCH: {
    icon: '26-26-10',
    label: '派车管理',
    url: '',
  },
  DAILY_GOODS: {
    icon: '26-26-13',
    label: '日用品库',
    url: '',
  },
  MESSAGE_PUSH: {
    icon: '26-26-12',
    label: '消息推送',
    url: '',
  },
  SAFETY: {
    icon: '26-26-8',
    label: '安全管理',
    url: '',
  },
};

// 全部
export const mainServices: ServiceItem[] = [
  SERVICE_ITEMS.COMMUTE_BUS,
  SERVICE_ITEMS.FAULT_REPAIR,
  SERVICE_ITEMS.WEEKLY_MENU,
  SERVICE_ITEMS.TAKEOUT,
  SERVICE_ITEMS.HAIRCUT,
  SERVICE_ITEMS.MEETING,
  SERVICE_ITEMS.CAR_MOVE
];

// 首页完整功能列表 (用于home)
export const fullFunctions: ServiceItem[] = [
  SERVICE_ITEMS.COMMUTE_BUS,
  SERVICE_ITEMS.FAULT_REPAIR,
  SERVICE_ITEMS.WEEKLY_MENU,
  SERVICE_ITEMS.TAKEOUT,
  SERVICE_ITEMS.HAIRCUT,
  SERVICE_ITEMS.MEETING,
  SERVICE_ITEMS.CAR_MOVE,
  SERVICE_ITEMS.ALL_SERVICES,
];

// 最小化模式功能列表
export const minimalFunctions: ServiceItem[] = [
  SERVICE_ITEMS.COMMUTE_BUS,
  SERVICE_ITEMS.TAKEOUT_ADMIN,
  MANAGE_SERVICE.REPAIR_MANAGE,
  MANAGE_SERVICE.HAIRCUT_MANAGE,
];

// 热门服务和我的常用
export const tabData: TabData = {
  popular: [
    [
      { ...SERVICE_ITEMS.COMMUTE_BUS }, // 使用基础图标
      { ...SERVICE_ITEMS.MEETING, icon: SERVICE_ITEMS.MEETING.iconVariant }, // 使用变体图标
    ],
    [
      {
        ...SERVICE_ITEMS.WEEKLY_MENU,
        icon: SERVICE_ITEMS.WEEKLY_MENU.iconVariant,
      },
      {
        ...SERVICE_ITEMS.FAULT_REPAIR,
        icon: SERVICE_ITEMS.FAULT_REPAIR.iconVariant,
      },
    ],
  ],
  frequent: [
    [
      { ...SERVICE_ITEMS.TAKEOUT, icon: SERVICE_ITEMS.TAKEOUT.iconVariant },
      { ...SERVICE_ITEMS.CAR_MOVE, icon: SERVICE_ITEMS.CAR_MOVE.iconVariant },
    ],
    [
      { ...SERVICE_ITEMS.HAIRCUT, icon: SERVICE_ITEMS.HAIRCUT.iconVariant },
      {
        ...SERVICE_ITEMS.WEEKLY_MENU,
        icon: SERVICE_ITEMS.WEEKLY_MENU.iconVariant,
      },
    ],
  ],
};

// 卡片服务数据 (用于home-ser)
export const serviceCards: ServiceCard[] = [
  {
    title: '办公服务',
    items: [SERVICE_ITEMS.MEETING],
  },
  {
    title: '出行服务',
    items: [SERVICE_ITEMS.COMMUTE_BUS,  SERVICE_ITEMS.CAR_MOVE],
  },
  {
    title: '生活服务',
    items: [SERVICE_ITEMS.WEEKLY_MENU, SERVICE_ITEMS.TAKEOUT, SERVICE_ITEMS.HAIRCUT],
  },
  {
    title: '更多服务',
    items: [SERVICE_ITEMS.FAULT_REPAIR],
  },
];

export const navigateTo = (item: ServiceItem) => {
  if (item?.url) {
    uni.navigateTo({ url: item.url });
  }else{
    uni.showToast({
      title: "功能正在升级中...",
      icon: "none",
    });
  }
};
