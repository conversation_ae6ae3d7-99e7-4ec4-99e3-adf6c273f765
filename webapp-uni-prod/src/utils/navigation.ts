export interface ServiceItem {
  icon: string
  label: string
  url: string
  // 新增字段，用于区分不同场景的图标变体
  iconVariant?: string
}

export interface ServiceCard {
  title: string
  items: ServiceItem[]
}

export interface TabData {
  popular: ServiceItem[][]
  frequent: ServiceItem[][]
}

// 所有服务项的常量定义
export const SERVICE_ITEMS = {
  // 基础服务项
  COMMUTE_BUS: { 
    icon: 'home-通勤服务', 
    label: '通勤班车', 
    url: '/pages/commute-bus/index',
    iconVariant: 'home-通勤服务' // 与基础图标相同
  },
  FAULT_REPAIR: { 
    icon: 'home-物业服务', 
    label: '故障报修', 
    url: '/subpages/property/repair/index',
    iconVariant: 'home-物业服务1'
  },
  WEEKLY_MENU: { 
    icon: 'home-用餐服务', 
    label: '每周菜谱', 
    url: '/subpages/cookbook/weekly-menu/index',
    iconVariant: 'home-用餐服务1'
  },
  TAKEOUT: { 
    icon: 'home-外卖服务', 
    label: '外卖订餐', 
    url: '/pages/takeaway/index',
    iconVariant: 'home-外面订餐1'
  },
  HAIRCUT: { 
    icon: 'home-理发', 
    label: '理发预约', 
    url: '/subpages/haircut/pages/appointment/index',
    iconVariant: 'home-理发预约1'
  },
  MEETING: { 
    icon: 'home-会议管理', 
    label: '会议助手', 
    url: '/pages/meetSchedule/index',
    iconVariant: 'home-会议管理1'
  },
  CAR_MOVE: { 
    icon: 'home-挪车', 
    label: '挪车服务', 
    url: '/pages/car-move/index',
    iconVariant: 'home-挪车1'
  },
  ALL_SERVICES: { 
    icon: 'home-全部', 
    label: '全部', 
    url: '/pages/home/<USER>/index'
  },
  
  // 其他服务项
  ASSETS: { 
    icon: 'home-固定资产', 
    label: '固定资产管理', 
    url: '/fixed-assets' 
  },
  VEHICLE_DISPATCH: { 
    icon: 'home-派车', 
    label: '派车管理', 
    url: '/vehicle-dispatch' 
  },
  DAILY_GOODS: { 
    icon: 'home-日用品', 
    label: '日用品库', 
    url: '/daily-goods' 
  },
  MESSAGE_PUSH: { 
    icon: 'home-消息推送1', 
    label: '消息推送', 
    url: '/message-push' 
  },
  SAFETY: { 
    icon: 'home-安全', 
    label: '安全管理', 
    url: '/safety' 
  }
}

// 全部 
export const mainServices: ServiceItem[] = [
  SERVICE_ITEMS.COMMUTE_BUS,
  SERVICE_ITEMS.FAULT_REPAIR,
  SERVICE_ITEMS.WEEKLY_MENU,
  SERVICE_ITEMS.TAKEOUT,
  SERVICE_ITEMS.HAIRCUT,
  SERVICE_ITEMS.MEETING
]

// 首页完整功能列表 (用于home)
export const fullFunctions: ServiceItem[] = [
  SERVICE_ITEMS.COMMUTE_BUS,
  SERVICE_ITEMS.FAULT_REPAIR,
  SERVICE_ITEMS.WEEKLY_MENU,
  SERVICE_ITEMS.TAKEOUT,
  SERVICE_ITEMS.HAIRCUT,
  SERVICE_ITEMS.MEETING,
  SERVICE_ITEMS.CAR_MOVE,
  SERVICE_ITEMS.ALL_SERVICES
]

// 最小化模式功能列表
export const minimalFunctions: ServiceItem[] = [
  SERVICE_ITEMS.COMMUTE_BUS
]

// 热门服务和我的常用
export const tabData: TabData = {
  popular: [
    [
      { ...SERVICE_ITEMS.COMMUTE_BUS }, // 使用基础图标
      { ...SERVICE_ITEMS.MEETING, icon: SERVICE_ITEMS.MEETING.iconVariant } // 使用变体图标
    ],
    [
      { ...SERVICE_ITEMS.WEEKLY_MENU, icon: SERVICE_ITEMS.WEEKLY_MENU.iconVariant },
      { ...SERVICE_ITEMS.FAULT_REPAIR, icon: SERVICE_ITEMS.FAULT_REPAIR.iconVariant }
    ]
  ],
  frequent: [
    [
      { ...SERVICE_ITEMS.TAKEOUT, icon: SERVICE_ITEMS.TAKEOUT.iconVariant },
      { ...SERVICE_ITEMS.CAR_MOVE, icon: SERVICE_ITEMS.CAR_MOVE.iconVariant }
    ],
    [
      { ...SERVICE_ITEMS.HAIRCUT, icon: SERVICE_ITEMS.HAIRCUT.iconVariant },
      { ...SERVICE_ITEMS.WEEKLY_MENU, icon: SERVICE_ITEMS.WEEKLY_MENU.iconVariant }
    ]
  ]
}

// 卡片服务数据 (用于home-ser)
export const serviceCards: ServiceCard[] = [
  {
    title: '办公服务',
    items: [
      SERVICE_ITEMS.MEETING,
      SERVICE_ITEMS.ASSETS,
      { icon: '', label: '', url: '' }
    ]
  },
  {
    title: '出行服务',
    items: [
      SERVICE_ITEMS.COMMUTE_BUS,
      SERVICE_ITEMS.VEHICLE_DISPATCH,
      SERVICE_ITEMS.CAR_MOVE,
      { icon: '', label: '', url: '' }
    ]
  },
  {
    title: '生活服务',
    items: [
      SERVICE_ITEMS.WEEKLY_MENU,
      SERVICE_ITEMS.TAKEOUT,
      SERVICE_ITEMS.HAIRCUT,
      SERVICE_ITEMS.DAILY_GOODS
    ]
  },
  {
    title: '更多服务',
    items: [
      SERVICE_ITEMS.FAULT_REPAIR,
      SERVICE_ITEMS.SAFETY,
      SERVICE_ITEMS.MESSAGE_PUSH,
      { icon: '', label: '', url: '' }
    ]
  }
]

// 统一的导航方法
export const navigateTo = (item: ServiceItem) => {
  if (item?.url) {
    uni.navigateTo({ url: item.url })
  }
}