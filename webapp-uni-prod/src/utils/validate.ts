// utils/validate.ts
/**
 * 判断是否为空（null、undefined、空字符串、空数组、空对象）
 * @param value 任意值
 * @returns 是否为空
 */
export function isEmpty(value: any) {
  // null 和 undefined
  if (value == null) return true;
  // 处理字符串
  if (typeof value === "string") {
    return value.trim().length === 0;
  }

  // 处理数组
  if (Array.isArray(value)) {
    return value.length === 0;
  }
  // 处理对象（包括普通对象、字面量对象等）
  if (typeof value === "object") {
    return Object.keys(value).length === 0;
  }
  // 其他类型（如数字、布尔值、函数等）通常不认为是空
  return false;
}

/**
 * 判断是否不为空（null、undefined、空字符串、空数组、空对象）
 * @param value 任意值
 * @returns 是否不为空
 */
export function isNotEmpty(value: any) {
  return !isEmpty(value);
}
