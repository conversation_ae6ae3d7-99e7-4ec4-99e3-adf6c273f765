export const cloneDeep = <T>(target: T): T => {
  // 若目标不是对象或为 null，则直接返回
  if (typeof target !== 'object' || target == null) {
    return target;
  }

  let clone: any;

  // 处理数组
  if (Array.isArray(target)) {
    clone = [];
    for (let i = 0; i < target.length; i++) {
      // 递归调用深克隆处理数组元素
      clone[i] = cloneDeep(target[i]);
    }
  }
  // 处理普通对象
  else {
    clone = {} as unknown as T;
    for (const key in target) {
      if (target.hasOwnProperty(key)) {
        // 递归调用深克隆处理对象属性
        clone[key] = cloneDeep(target[key]);
      }
    }
  }

  return clone as T;
};
