/**
 * 起初为markerId设计，生成2-9位的随机数，用于markerId；
 * 建议为每个marker设置上Number类型id，保证更新marker时有更好的性能。最大限制9位数
 */
export function generateRandomNumber(): number {
  // 随机确定位数(2-9位)
  const length = Math.floor(Math.random() * 8) + 2;

  // 生成第一位数字(1-9)
  const firstDigit = Math.floor(Math.random() * 9) + 1;

  // 生成剩余的数字
  let remainingDigits = '';
  for (let i = 0; i < length - 1; i++) {
    remainingDigits += Math.floor(Math.random() * 10);
  }

  // 组合并返回结果
  return parseInt(`${firstDigit}${remainingDigits}`, 10);
}
