/** 主要消息类型 */
export const formatMsgType = (code: number): string | number => {
  const messageTypes: { [key: number]: string } = {
    1: '位置报告（自主的、分配的或轮询响应）',
    2: '位置报告（自主的、分配的或轮询响应）',
    3: '位置报告（自主的、分配的或轮询响应）',
    4: '基站报告（位置、UTC/日期和当前时隙号）',
    5: '静态和与航次相关数据（A 类 SME）',
    6: '二进制报文（寻址、确认或广播）',
    7: '二进制报文（寻址、确认或广播）',
    8: '二进制报文（寻址、确认或广播）',
    9: '标准搜救飞机位置报告',
    10: 'UTC/日期（询问和响应）',
    11: 'UTC/日期（询问和响应）',
    12: '安全相关报文（寻址、确认或广播）',
    13: '安全相关报文（寻址、确认或广播）',
    14: '安全相关报文（寻址、确认或广播）',
    15: '询问（指定报文类型的请求）',
    16: '分配模式命令（由权威机构使用）',
    17: 'DGNSS 广播二进制报文',
    18: 'B 类 SME 位置报告（标准及扩展报告）',
    19: 'B 类 SME 位置报告（标准及扩展报告）',
    20: '数据链路管理（为基站保留时隙）',
    21: '助航设备报告—位置和状态报告',
    22: '信道管理',
  };
  return messageTypes[code] || code;
};
/** 格式化最大长度；（因n-input-number传入maxlength不生效；通过updateValue更改长度） */
export const formatMaxNumLen = (value: number | null, maxLength: number = 9): number | null => {
  if (value == null) return null;
  if (value && value.toString().length > maxLength) {
    const truncated = value.toString().slice(0, maxLength);
    return parseFloat(truncated);
  } else {
    return value;
  }
};
