// utils/download.ts
import { downloadRaw, downloadCSV } from "../service/http.service";
const download = (blob: Blob, filename: string) => {
  const blobUrl = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.style.display = "none";
  a.href = blobUrl;
  a.download = decodeURIComponent(filename); // 处理中文名
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(blobUrl);
};
/**
 * 下载文件（支持带 Token 和中文文件名）
 * @param url 下载地址（API）
 */
const downloadFile = async (
  url: string,
  onProgress?: (percent: number) => void
) => {
  try {
    const { blob, filename } = await downloadRaw(url, onProgress);
    download(blob, filename);
  } catch (error) {
    throw error;
  }
};

/**
 * 根据时间范围下载日志
 * @param url
 * @param param
 */
const downloadLogByTime = async (
  url: string,
  param: { startTime: string; endTime: string }
) => {
  try {
    // const { blob, filename } = await downloadJSON(url, param);
    const { blob, filename } = await downloadCSV(url, param);
    download(blob, filename);
  } catch (error) {
    throw error;
  }
};

export { downloadFile, downloadLogByTime };
