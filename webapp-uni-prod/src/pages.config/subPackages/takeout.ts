import { SubPackageConfig } from "../types";

/**
 * 外卖模块
 * @description 该模块包含外卖相关的页面和功能
 * @module takeout
 * <AUTHOR>
 * @date 2025-06-19
 */
export default <SubPackageConfig>{
  root: "subpages/takeout",
  pages: [
    // 职工端 外卖首页
    {
      path: "pages/staff/index",
      name: "takeoutStaffIndex",
      style: {
        navigationBarTitleText: "外卖订餐",
        enablePullDownRefresh: true, // 启用下拉刷新
      },
    },
    // 职工端 外卖订单支付页面
    {
      path: "pages/staff/checkout/index",
      name: "takeoutStaffCheckout",
      style: {
        navigationBarTitleText: "订单确认",
      },
    },
    // 职工端 外卖订单列表（全部、待支付、待取餐、已完成、已取消）
    {
      path: "pages/staff/order-list/index",
      name: "takeoutStaffOrderList",
      style: {
        navigationBarTitleText: "外卖订餐",
        // enablePullDownRefresh: true, // 订单需要分类，使用自定义下拉刷新
        // navigationStyle: "custom", // 使用自定义导航栏
      },
    },
    // 职工端 外卖订单详情
    {
      path: "pages/staff/order-detail/index",
      name: "takeoutStaffOrderDetail",
      style: {
        navigationBarTitleText: "订单详情",
        // navigationStyle: "custom", // 使用自定义导航栏
      },
    },

    // 管理端 外卖管理（菜品发布）
    {
      path: "pages/admin/index",
      name: "takeoutAdminIndex",
      style: {
        navigationBarTitleText: "外卖管理",
        enablePullDownRefresh: true,
      },
    },
    // 管理端 外卖管理 菜品库列表
    {
      path: "pages/admin/dish-list/index",
      name: "takeoutAdminDishList",
      style: {
        navigationBarTitleText: "外卖管理",
        // enablePullDownRefresh: true, // 自定义下拉刷新
      },
    },
    // 管理端 外卖管理 菜品-处理（添加）
    {
      path: "pages/admin/dish-handle/index",
      name: "takeoutAdminDishHandle",
      style: {
        navigationBarTitleText: "新增菜品",
      },
    },
    // 管理端 外卖管理 - 订单列表(详情，包含用户取餐码)
    {
      path: "pages/admin/order-detail-list/index",
      name: "takeoutAdminOrderDetailList",
      style: {
        navigationBarTitleText: "订单详情",
        enablePullDownRefresh: true,
      },
    },
  ],
  tabBar: {
    list: [
      {
        text: "首页",
        icon: "22-22-50",
        selectedIcon: "22-22-49",
        pagePath: "pages/staff/index",
      },
      {
        text: "订单",
        icon: "22-22-56",
        selectedIcon: "22-22-55",
        pagePath: "pages/staff/order-list/index",
      },
    ],
  },
  // 管理端的tabBar配置
  adminTabBar: {
    list: [
      {
        text: "首页", // 今日外卖订单菜品
        icon: "22-22-50",
        selectedIcon: "22-22-49",
        pagePath: "pages/admin/index",
      },
      {
        text: "菜品库", // 外卖订单的菜品库
        icon: "22-22-52",
        selectedIcon: "22-22-51",
        pagePath: "pages/admin/dish-list/index",
      },
    ],
  },
  icons: [
    "12-12-7",
    "12-12-10",
    "16-16-6",
    "16-16-7",
    "16-16-16",
    "16-16-29",
    "20-20-23",
    "20-20-24",
    "20-20-25",
    "20-20-26",
    "20-20-27",
    "20-20-28",
    "22-22-49",
    "22-22-50",
    "22-22-51",
    "22-22-52",
    "22-22-49",
    "22-22-56",
    "22-22-55",
    "40-40-1",
    "52-52-4",
    "52-52-5",
    "70-70-1",
  ],
};
