import { SubPackageConfig } from '../types';
/** 通勤管理 */
export default <SubPackageConfig>{
  root: 'subpages/commute-manager',
  pages: [
    {
      path: 'commute-shuttle/index',
      style: {
        navigationBarTitleText: '通勤班车',
      },
    },
    {
      path: 'commute-shuttle/CommuteShuttleInfo',
      style: {
        navigationBarTitleText: '车次信息',
      },
    },
    {
      path: 'shuttle-departure/index',
      style: {
        navigationBarTitleText: '班车发车',
      },
    },
    {
      path: 'shuttle-departure/ShuttleDepartureInfo',
      style: {
        navigationBarTitleText: '班车发车',
      },
    },
    // {
    //   path: 'commute-map/index',
    //   style: {
    //     navigationBarTitleText: 'map',
    //   },
    // },
  ],
  icons: [
    '6-6-1',

    '10-10-8',
    '10-10-9',

    '12-12-19',
    '12-12-18',

    '14-14-14',
    '14-14-15',
    '14-14-16',
    '14-14-23',
    '14-14-30',
    '14-14-31',
    '14-14-32',
    '14-14-33',

    '16-16-25',
    '16-16-26',
    '16-16-34',
    '16-16-35',
    '16-16-33',

    '18-18-1',
    '18-18-2',
    '18-18-3',

    '30-30-7',
    '54-54-1',
    'commute-arrow-active',
    'commute-arrow-unactive',
  ],
};
