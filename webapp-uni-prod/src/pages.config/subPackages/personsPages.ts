import { SubPackageConfig } from '../types';

export default <SubPackageConfig>{
  root: 'subpages/persons',
  pages: [
    {
      path: 'index',
      style: {
        'navigationBarTitleText': '人事管理',
        'enablePullDownRefresh': false,
      },
    },
    {
      path: 'PersonsDepartment',
      style: {
        'navigationBarTitleText': '新增部门',
        'enablePullDownRefresh': false,
      },
    },
    {
      path: 'worker/WorkerAddPersons',
      style: {
        'navigationBarTitleText': '添加组员',
        'enablePullDownRefresh': false,
      },
    },
    {
      path: 'worker/WorkerHandle',
      style: {
        'navigationBarTitleText': '编辑工作组',
        'enablePullDownRefresh': false,
      },
    },
    {
      path: 'PersonsHandle',
      style: {
        'navigationBarTitleText': '新增职工',
        'enablePullDownRefresh': false,
      },
    },
  ],
};
