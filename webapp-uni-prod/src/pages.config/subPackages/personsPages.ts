import { SubPackageConfig } from '../types';

export default <SubPackageConfig>{
  root: 'subpages/persons',
  pages: [
    {
      path: 'index',
      style: {
        'navigationBarTitleText': '人事管理',
        'enablePullDownRefresh': false,
      },
    },
    {
      path: 'PersonsDepartment',
      style: {
        'navigationBarTitleText': '新增部门',
        'enablePullDownRefresh': false,
      },
    },
    {
      path: 'worker/WorkerAddPersons',
      style: {
        'navigationBarTitleText': '添加组员',
        'enablePullDownRefresh': false,
      },
    },
    {
      path: 'worker/WorkerHandle',
      style: {
        'navigationBarTitleText': '编辑工作组',
        'enablePullDownRefresh': false,
      },
    },
    {
      path: 'PersonsHandle',
      style: {
        'navigationBarTitleText': '新增职工',
        'enablePullDownRefresh': false,
      },
    },
  ],
  icons: ['16-16-16',
    '12-12-6',
    '16-16-6',
    '16-16-8',
    '16-16-9',
    '20-20-38',
    '34-34-11',
    '34-34-12',
    //  '16-16-29', '20-20-23', '20-20-24', '20-20-25', '20-20-26', '20-20-28', '22-22-50', '22-22-49', '22-22-56', '22-22-55'
    ],
};
