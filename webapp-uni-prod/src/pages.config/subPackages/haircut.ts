import { SubPackageConfig } from '../types';

export default <SubPackageConfig>{
  root: "subpages/haircut",
  pages: [
    {
      path: "pages/appointment/index",
      style: {
        navigationBarTitleText: "理发预约",
        enablePullDownRefresh: true, // 启用下拉刷新
      },
    },
    {
      path: "pages/appointment/components/haircut-handle/index",
      style: {
        navigationBarTitleText: "理发管理",
        enablePullDownRefresh: true, // 启用下拉刷新
      },
    },
    {
      path: "pages/appointment/components/haircut-relocation/index",
      style: {
        navigationBarTitleText: "营业时间",
      },
    },
    {
      path: "pages/appointment/components/haircut-inform/index",
      style: {
        navigationBarTitleText: "理发管理",
        enablePullDownRefresh: true, // 启用下拉刷新
      },
    },
    {
      path: "pages/appointment/components/haircut-noticeWay/index",
      style: {
        navigationBarTitleText: "再次通知",
      },
    },
    {
      path: "pages/appointment/components/haircut-config/index",
      style: {
        navigationBarTitleText: "理发配置",
      },
    },
  ],
  adminTabBar: {
    list: [
      {
        text: "首页",
        icon: "22-22-42",
        selectedIcon: "22-22-41",
        pagePath: "pages/appointment/index?type=admin",
      },
      {
        text: "配置",
        icon: "22-22-44",
        selectedIcon: "22-22-43",
        pagePath: "pages/appointment/components/haircut-handle/index",
      },
      {
        text: "反馈",
        icon: "22-22-46",
        selectedIcon: "22-22-45",
        pagePath: "pages/appointment/components/haircut-inform/index",
      },
    ],
  },
  icons: [
    "20-20-22",
    "24-24-2",
    "16-16-9",
    "16-16-8",
    "36-36-2",
    "36-36-3",
    "16-16-17",
    "16-16-16",
    "16-16-21",
    "16-16-20",
    "20-20-21",
    "12-12-13",
    "12-12-14",
    "20-20-24",
    "20-20-25",
    "20-20-26",
    "20-20-35",
    "22-22-44",
    "22-22-46",
    "22-22-41",
    "22-22-42",
    "22-22-43",
    "22-22-45",
  ],
};
