import { formatDate, getOneDayAgo, getOneHourAgo } from '@/utils/generaterTime';

export class Page<T = any> {
  /** 当前页 */
  currentPage: number; //当前页
  /** 页容量 */
  pageRecord: number;
  /** 总数 */
  total: number;
  recordCount?: number;
  /** 是否分页 */
  ifPage: boolean;
  result?: Array<T> = [];
  constructor() {
    this.total = 0;
    this.currentPage = 1;
    this.pageRecord = 15;
    this.ifPage = true;
  }
}
export class PageSearch implements Pick<Page, 'currentPage' | 'pageRecord' | 'ifPage'> {
  currentPage: number;
  pageRecord: number;
  keyword?: string = '';
  startTime?: string; // YYYY-MM-DD hh:mm:ss
  endTime?: string; // YYYY-MM-DD hh:mm:ss
  ifPage: boolean;
  constructor(defaultTime?: 'oneHourAgo' | 'oneDayAgo') {
    this.currentPage = 1;
    this.pageRecord = 15;
    this.ifPage = true;
    if (defaultTime) {
      this.startTime = defaultTime == 'oneDayAgo' ? getOneDayAgo() : getOneHourAgo();
      this.endTime = formatDate(new Date());
    }
  }
}
