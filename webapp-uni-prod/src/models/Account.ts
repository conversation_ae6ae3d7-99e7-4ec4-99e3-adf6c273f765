export interface AuthLoginUser {
  /** 用户名，手机号 */
  j_username: string;
  /** 密码，验证码 */
  j_password?: string;
  /** 验证码 */
  code?: string;
}

export interface Organization {
  id: string;
  name: string;
  description: string;
  sortOrder: number;
  status: number;
  personnelList: any[]; // 暂未定义具体字段
}

export interface VehicleInfo {
  id: string;
  personnelId: string;
  licensePlateNumber: string;
}

export interface Workgroup {
  id: string;
  name: string;
  description: string;
  leaderId: string;
  status: number;
  sortOrder: number;
  leader: any; // 暂未定义具体字段
  personnelList: any[]; // 暂未定义具体字段
}

export interface Permission {
  id: string;
  name: string;
  sortOrder: number;
}

export interface Account {
  id: string;
  organizationId: string;
  name: string;
  nameInitials: string;
  phone: string;
  position: string;
  email: string;
  type: number;
  status: number;
  organization: Organization;
  vehicleinfoList: VehicleInfo[];
  workgroupList: Workgroup[];
  permissionList: Permission[];
}

export interface WeixinPhoneAccount {
  loginCode: string; // 微信登录临时票据
  personnel: Account; // 绑定的人员信息
  phoneNumber: string; // 微信手机号
}
