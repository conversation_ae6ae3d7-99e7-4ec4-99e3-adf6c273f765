// account.d.ts
export interface VehicleInfo {
  id: string;
  personnelId: string;
  licensePlateNumber: string;
}

export interface WorkGroup {
  id: string;
  name: string;
  description: string;
  leaderId: string;
  status: number;
  sortOrder: number;
}

export interface Organization {
  id: string;
  name: string;
  description: string;
  sortOrder: number;
  status: number;
}

export interface Account {
  id: string;
  sysCreated: string;
  organizationId: string;
  name: string;
  nameInitials: string;
  phone: string;
  position: string;
  email: string;
  type: number;
  status: number;
  organization: Organization;
  vehicles: VehicleInfo[];
  workgroups: WorkGroup[];
}
