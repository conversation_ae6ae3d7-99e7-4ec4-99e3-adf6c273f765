/**物业服务相关数据模型 */
import { PageSearch } from "./Page";

/**报修状态 */
export enum RepairStatus {
  /**待处理 */
  PENDING = 'pending',
  /**处理中 */
  PROCESSING = 'processing',
  /**已完结 */
  COMPLETED = 'completed',
  /**已取消 */
  CANCELLED = 'cancelled'
}

/**紧急程度 */
export enum UrgencyLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

/**报修项目 */
export interface Repair {
  /**报修ID */
  id?: string;          
  /**报修人电话 */
  phone: string;          
  /**报修位置 */
  location: string;    
  /**报修状态 */
  status?: RepairStatus;
  /**报修人员 */
  reporter: string;    
  /**紧急程度 */
  urgencyLevel: UrgencyLevel;
  /**报修内容 */
  content: string;     
  /**报修时间 */
  createTime?: string;  
  /**已提交时长（处理中状态显示） */
  duration?: string;   
  /**报修图片/视频 */
  images?: string[];   
  /**处理人ID */
  handlerId?: string;  
  /**处理人 */
  handler?: string;    
  /**处理时间 */
  handleTime?: string; 
  /**完成时间 */
  completeTime?: string;
  /**备注 */
  remark?: string;     
  /**催办内容 */
  urge?: string;     
}

/**报修查询参数 */
export class RepairSearch extends PageSearch {
  /**状态 */
  status?: RepairStatus;       
  /**紧急程度 */
  urgencyLevel?: UrgencyLevel; 
  /**位置 */
  location?: string;          
  
  constructor() {
    super();
    this.pageRecord = 10; // 每页10条
  }
}