/** 发车/结束 行程类 */
export class CommuteVehicle {
  /**
   * 班车id
   */
  carId?: string;
  /**
   * 司机的userId
   */
  driverID?: string;
  /**
   * 路线id
   */
  routeId?: string;
  /**
   * 发车状态0：停止 1：发车
   */
  status?: number;
}

/**
 * 车辆信息类型定义
 */
export interface CommuteVehicleInfo {
  id: string;
  sysCreated: string;
  /** 驾驶员ID */
  driverId: string;
  /** 所属路线ID */
  routeId: string;
  /** 车辆状态（1:在线，0:离线等，具体见业务枚举） */
  status: number;
  /** 车辆ID（与carPlate一致） */
  carId: string;
  /** 车牌号 */
  carPlate: string;
  /** 车牌颜色 */
  plateColor: string;
  /** 车牌颜色编码（1:蓝色，2:黄色等） */
  plateColorCode: string;
  /** 车辆显示名称（与carPlate一致） */
  carName: string;
  /** 所属团队ID */
  teamId: string;
  /** 团队名称 */
  teamName: string;
  /** 公司名称 */
  cmpName: string;
  /** 是否启用GPS定位（true:已启用，false:未启用） */
  gps: boolean;
  /** 通信密钥（用于GPS数据传输） */
  tmnKey: string;
  /** 有效期截止日期 */
  dueDate: string;
  /** 上线时间 */
  onlineTime: string;
  icon: string;
  iconLink: string;
  /** 车辆描述信息（包含历史牌照变更等记录） */
  des: string;
  /** 额外信息（JSON格式字符串，包含如'212':'QXY'等业务字段） */
  bxts: string;
  sysDeleted: number;
  /** 纬度坐标（GPS位置） */
  lat: number;
  /** 经度坐标（GPS位置） */
  lng: number;
  /** 关联的通勤行程信息 */
  commuteTrip: CommuteTripInfo;

  /** 关联的通勤路线信息 */
  commuteRoute: CommuteRouteInfo;
  followed?: boolean;
}
/**
 * 通勤行程信息类型
 */
interface CommuteTripInfo {
  /** 行程唯一标识 */
  id: string;
  /** 系统创建时间 */
  sysCreated: string;
  /** 车辆ID */
  carId: string;
  /** 路线ID */
  routeId: string;
  /** 驾驶员ID */
  driverId: string;
  /** 行程开始时间 */
  startTime: string;
  /** 预计结束时间 */
  predictEndTime: string;
  /** 行程状态（1:进行中，0:未开始等） */
  status: number;
  /** 系统删除标识（0:未删除，1:已删除） */
  sysDeleted: number;
  currentStop: ICommuteStopInfo;
  nextStop: ICommuteStopInfo;
}

/**
 * 通勤路线信息类型（含站点列表）
 */
export interface CommuteRouteInfo {
  /** 路线唯一标识 */
  id: string;
  /** 系统创建时间 */
  sysCreated: string;
  /** 路线名称 */
  name: string;
  /** 起始站点名称 */
  startStopName: string;
  /** 终点站点名称 */
  endStopName: string;
  /** 路线类型（0:上班，1:下班等） */
  routeType: number;
  /** 系统删除标识（0:未删除，1:已删除） */
  sysDeleted: number;

  /** 站点列表（按行驶顺序排列） */
  stopList: ICommuteStopInfo[];

  /** 车辆列表（当前为空，需根据业务补充） */
  vehicles: CommuteVehicleInfo[];

  /** 是否已关注路线（true:已关注，false:未关注） */
  followed: boolean;

  /** 路线类型中文描述 */
  routeTypeCn: string;
}

/**
 * 路线站点信息类型
 */
export interface ICommuteStopInfo {
  /** 站点唯一标识 */
  id: string;
  /** 系统创建时间 */
  sysCreated: string;
  /** 所属路线ID */
  routeId: string;
  /** 站点ID（关联基础站点数据） */
  stopId: string;
  /** 站点行驶顺序（0为起点，依次递增） */
  sequence: number;
  /** 系统删除标识（0:未删除，1:已删除） */
  sysDeleted: number;
  /** 站点名称 */
  stopName: string;
  lat: number;
  lng: number;
}
