/**
 * 日期类型：
 *  0-未知,1-节假日,2-补班日,3-工作日,4-非工作日
 **/
export enum DateType {
  /** 未知 */
  Unknown = 0,
  /** 节假日 */
  Holiday = 1,
  /** 补班日 */
  Substitute = 2,
  /** 工作日 */
  WorkDay = 3,
  /** 非工作日 */
  NonWorkDay = 4,
}
export interface IDate {
  date: string;
  dayOrHolidayName: string;
  id: string;
  /** 手动日期类型 */
  manualType: DateType | null;
  officialName: string;
  /** 官方日期类型 */
  officialType: DateType;
  /** 实际类型 */
  type: DateType;
}
