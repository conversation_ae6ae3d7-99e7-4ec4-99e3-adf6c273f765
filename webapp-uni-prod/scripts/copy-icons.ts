#!/usr/bin/env tsx
import fs from "fs";
import path from "path";

// 1. 项目根目录
const ROOT = process.cwd();
// 源目录
const ICONS_SRC = path.join(ROOT, "src", "static", "icons");
//build 输出目录
const BUILD_ICONS_DST = path.join(
  ROOT,
  "dist",
  "build",
  "mp-weixin",
  "static",
  "icons"
);

// 目标子包目录
const ICONS_DST = path.join(
  ROOT,
  "src",
  "subpages",
  "icons",
  "pages",
  "static",
  "icons"
);

// 2. 删除旧的目标目录（如果存在）
if (fs.existsSync(ICONS_DST)) {
  fs.rmSync(ICONS_DST, { recursive: true, force: true });
}
console.log(`✅ 已删除旧的图标目录：\n  ${ICONS_DST}`);
// 删除build 目录下的图标目录（如果存在）
if (fs.existsSync(BUILD_ICONS_DST)) {
  fs.rmSync(BUILD_ICONS_DST, { recursive: true, force: true });
}
console.log(`✅ 已删除旧的图标目录：\n  ${BUILD_ICONS_DST}`);

// 3. 递归创建目标目录
fs.mkdirSync(ICONS_DST, { recursive: true });

// 4. 1:1 复制整个目录
//    Node.js v16+ 支持 fs.cpSync
fs.cpSync(ICONS_SRC, ICONS_DST, { recursive: true });

console.log(`✅ 已将所有图标从\n  ${ICONS_SRC}\n  复制到\n  ${ICONS_DST}`);
