#!/usr/bin/env tsx
import fs from "fs";
import path from "path";
import minimist from "minimist";
const argv = minimist(process.argv.slice(2), {
  alias: { m: "mode" },
  string: ["mode"],
  default: { mode: "production" },
});
const mode = argv.mode as string;
const dir = mode === "development" ? "dev" : "build";
// 1. 项目根目录（脚本放在 scripts/ 下时，cwd 保持在项目根）
const ROOT = process.cwd();
const [, , customDir] = process.argv;
const ICONS_DST = path.join(ROOT, "dist", dir, "mp-weixin", "static", "icons");

// 如果不存在 OUT_DIR，则退出
if (!fs.existsSync(ICONS_DST)) {
  console.error(`⚠️ 图标目录不存在：${ICONS_DST}`);
  process.exit(1);
}

// 删除 OUT_DIR
if (fs.existsSync(ICONS_DST)) {
  fs.rmSync(ICONS_DST, { recursive: true, force: true });
}
console.log(`✅ 已删除图标目录：\n  ${ICONS_DST}`);
