#!/usr/bin/env tsx
import fs from "fs";
import path from "path";
import { SubpageIconConfig, SubPagesItem } from "./types";

/**
 * 按照 SlSvgIcon 的命名规则，把 src/static/icons 下对应的 SVG
 * 复制到各个分包下的 static/icons 目录。
 */
export function writeSubpageIcons(subPackages: SubPagesItem[]) {
  const configs: SubpageIconConfig[] = subPackages.map((ele) => {
    const { root, icons } = ele;
    return {
      root,
      icons: icons || [], // 如果没有 icons 则默认为空数组
    };
  });

  const PROJECT_ROOT = process.cwd();
  const SRC_ICONS_DIR = path.join(PROJECT_ROOT, "src", "static", "icons");

  for (const { root, icons } of configs) {
    // 目标目录：子包下的 static/icons
    const DEST_ICONS_DIR = path.join(
      PROJECT_ROOT,
      "src",
      root,
      "static",
      "icons"
    );

    // 清空旧目录
    if (fs.existsSync(DEST_ICONS_DIR)) {
      fs.rmSync(DEST_ICONS_DIR, { recursive: true, force: true });
    }

    // 判断icons 是否为空
    if (!icons || icons.length === 0) {
      console.warn(`⚠️ 子包 ${root} 没有配置图标，跳过复制`);
      continue;
    }

    fs.mkdirSync(DEST_ICONS_DIR, { recursive: true });
    const curIconNames = new Set<string>();
    for (const name of icons) {
      const arr = name.split("-");
      let srcSvg: string | null = null;

      // 同 SlSvgIcon 里的解析逻辑
      if (arr[0] === arr[1] && arr.length >= 3) {
        const folder = `${arr[0]}-${arr[1]}`;
        const fileName = `${arr.slice(2).join("-")}.svg`;
        srcSvg = path.join(SRC_ICONS_DIR, folder, fileName);
      } else if (arr.length >= 2) {
        const folder = arr[0];
        const fileName = `${arr.slice(1).join("-")}.svg`;
        srcSvg = path.join(SRC_ICONS_DIR, folder, fileName);
      }

      if (!srcSvg || !fs.existsSync(srcSvg)) {
        console.warn(`⚠️ 找不到图标：${name} -> ${srcSvg}`);
        continue;
      }
      // 计算相对子目录，保持原始结构
      const relDir = path.relative(SRC_ICONS_DIR, path.dirname(srcSvg));
      const destDir = path.join(DEST_ICONS_DIR, relDir);
      fs.mkdirSync(destDir, { recursive: true });
      fs.copyFileSync(srcSvg, path.join(destDir, path.basename(srcSvg)));
      curIconNames.add(name);
    }
    // 输出当前子包的图标列表
    console.log(`子包 ${root} 图标列表：`, Array.from(curIconNames).join(", "));
  }

  console.log(`✅ 分包图标复制完成`);
}
