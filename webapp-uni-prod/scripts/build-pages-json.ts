//全量定义的配置
import { getPagesJson } from "../src/pages.config";
import { writeSubpageIcons } from "./gen-subpage-icons";
import { RawPagesJson, SubPagesItem } from "./types";
import { writePagesJson } from "./gen-pages-json";
import { writeSubpageTabBars } from "./gen-subpage-tabbar";
// 完整配置
const raw: RawPagesJson = getPagesJson();
// 生成 pages.json
writePagesJson(raw);
// 分包 tabBar 配置
writeSubpageTabBars(raw.subPackages);
// 生成分包图标
writeSubpageIcons(raw.subPackages as SubPagesItem[]);
