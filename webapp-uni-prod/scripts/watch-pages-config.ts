import chokidar from "chokidar"
import fs from "fs"
import path from "path"
import { fileURLToPath } from "url"

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const configPath = path.resolve(__dirname, "../src/pages.config/index.ts")
const outFile = path.resolve(__dirname, "../src/pages.json")

async function writePagesJson() {
  const module = await import(`file://${configPath}?t=${Date.now()}`)
  const pagesJson = module.getPagesJson()

  fs.writeFileSync(outFile, JSON.stringify(pagesJson, null, 2))
  console.log("[pages.config] ✅ pages.json 已更新")
}

writePagesJson()

chokidar
  .watch(path.resolve(__dirname, "../src/pages.config"), {
    ignored: /(^|[\/\\])\../,
    ignoreInitial: true,
  })
  .on("all", (event, filePath) => {
    console.log(`[pages.config] 文件变动: ${event} -> ${filePath}`)
    writePagesJson()
  })
