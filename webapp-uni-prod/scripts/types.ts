export interface TabItem {
  text: string;
  icon: string;
  selectedIcon: string;
  pagePath: string; // 例如 "pages/takeout/index"
}

export interface SubPagesItem {
  root: string;
  pages: any[];
  independent?: boolean;
  tabBar?: any; // 原始里可能带 tabBar
  adminTabBar?: any; // 管理端的自定义 tabBar 配置
  icons?: string[]; // 可能存在的图标映射
}

export interface RawPagesJson {
  pages: any[];
  subPackages: SubPagesItem[];
  globalStyle: Record<string, any>;
  tabBar: Record<string, any>;
  easycom: Record<string, any>;
}

export interface RuntimeTabBarConfig {
  root: string;
  tabBar?: any;
  adminTabBar?: any; // 管理端的自定义 tabBar 配置
  navBar?: any; // 是否使用自定义导航栏
}

export interface RuntimeTabConfig {
  root: string;
  tabBar: TabItem[];
  adminTabBar: TabItem[];
}

export interface SubpageIconConfig {
  root: string; // e.g. "subpages/takeout"
  icons: string[]; // e.g. ["22-22-50","22-22-49",…]
}
