// scripts/write-pages-json.ts
import fs from "fs";
import path from "path";
import { RawPagesJson } from "./types";

/**
 * 将原始配置生成符合微信小程序要求的 pages.json
 * @param raw - 从 getPagesJson() 获得的完整配置对象
 * @param outputPath - 输出文件路径，默认 src/pages.json
 */
export function writePagesJson(
  raw: RawPagesJson,
  outputPath: string = path.resolve(process.cwd(), "src/pages.json")
) {
  // 过滤 subPackages，只保留 root, pages, independent
  const filteredSubPackages = raw.subPackages.map(
    ({ root, pages, independent }) => ({
      root,
      pages,
      independent,
    })
  );

  // 构造 pages.json 对象
  const pagesJson = {
    pages: raw.pages,
    subPackages: filteredSubPackages,
    globalStyle: raw.globalStyle,
    tabBar: raw.tabBar,
    easycom: raw.easycom,
  };

  // 写入文件
  fs.writeFileSync(outputPath, JSON.stringify(pagesJson, null, 2), "utf-8");
  console.log(`✅ ${outputPath} 生成完毕`);
}
