// scripts/gen-subpage-tabbars.ts
import fs from "fs";
import path from "path";
import { RuntimeTabBarConfig, TabItem } from "./types";
/**
 * 从原始 pages.json 配置中提取 subPackages 的 tabBar 和 adminTabBar
 * 并写入独立的 TS 文件
 *
 * @param subPackages 原始 subPackages 数组
 * @param outFilePath 输出文件路径，默认 src/subPageTabs.ts
 */
export function writeSubpageTabBars(
  subPackages: Array<{
    root: string;
    tabBar?: { list: TabItem[] };
    adminTabBar?: { list: TabItem[] };
    pages: any[];
  }>,
  outFilePath: string = path.resolve(process.cwd(), "src/subPageTabs.ts")
) {
  // console.log(`开始生成 ${outFilePath}...`, subPackages);
  // 过滤并生成运行时配置数组
  const configs: RuntimeTabBarConfig[] = subPackages
    .map((pkg) => {
      const formatList = (list?: TabItem[]) =>
        (list || []).map((item) => ({
          ...item,
          // 确保路由前缀拼接正确
          pagePath: path.posix.join(
            pkg.root,
            item.pagePath.replace(/^\/+/, "")
          ),
        }));
      const pages = pkg.pages || [];
      // 处理自定义导航栏配置
      const navBarList: any = [];
      // 查找pages 中是否有自定义导航栏配置
      for (const page of pages) {
        if (page.style?.navigationStyle === "custom") {
          navBarList.push({
            text: page.style?.navigationBarTitleText || "未命名",
            pagePath: path.posix.join(
              "/",
              pkg.root,
              page.path.replace(/^\/+/, "")
            ),
          });
        }
      }
      const item: RuntimeTabBarConfig = {
        root: pkg.root,
      };
      if (pkg.tabBar?.list) {
        item.tabBar = {
          list: formatList(pkg.tabBar.list),
        };
      }
      if (pkg.adminTabBar?.list) {
        item.adminTabBar = {
          list: formatList(pkg.adminTabBar.list),
        };
      }
      if (navBarList.length > 0) {
        item.navBar = { list: navBarList };
      }
      return item;
    });

  // 生成文件内容
  const content = `/**
 * 该文件由 scripts/build-pages-json.ts 自动生成，请勿手动修改
 */

import { SubPackageTabBarConfig } from "./models/TabBarItem";

export const SUB_TAB_BAR_LIST: SubPackageTabBarConfig[] = ${JSON.stringify(
    configs,
    null,
    2
  )};
`;

  // 确保输出目录存在
  const outDir = path.dirname(outFilePath);
  if (!fs.existsSync(outDir)) {
    fs.mkdirSync(outDir, { recursive: true });
  }

  // 写文件
  fs.writeFileSync(outFilePath, content, "utf-8");
  console.log(`✅ ${outFilePath} 生成完毕`);
}
