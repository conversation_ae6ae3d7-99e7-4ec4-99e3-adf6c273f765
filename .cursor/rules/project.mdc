---
description:
globs:
alwaysApply: true
---
<project>

## 项目简介

懃务通是一款智慧后勤小程序，集成便捷登录、物业报修、巡检记录、会议预约、派车管理等核心功能，覆盖后勤管理全场景；支持菜谱管理、面食预订、理发预约、通勤班车、挪车等生活服务线上化，实现固定资产、日用品智能管控，同步搭载安全管理、新闻公告、配置管理、人事管理等基础模块；以数字化推动后勤管理中心信息与智能一体化，通过一站式服务体系简化职工办事流程，提升后勤服务效率与体验，助力构建现代化后勤管理新模式。

## 技术栈要求

| 类别 | 技术选型 | 版本 |
|------|---------|------|
| 编程语言 | Kotlin | 1.2.41 |
| 构建工具 | Gradle | 4.10.1 |
| 框架 | Spring Boot | 2.0.4 |
| 安全框架 | Spring Security | 2.0.4 |
| 数据库 | MariaDB | 10.5.8 |
| ORM | MyBatis (注解动态SQL) | 3.5.1 |

</project>
