---
description: 
globs: 
alwaysApply: true
---
<meeting-module>

## 模块概述

"智慧后勤小程序"微信小程序的会议模块后端服务，实现会议预约创建、参会人员管理、会议通知等核心功能。该模块需提供完整的API支持前端实现会议预约管理流程。

## 技术栈要求

| 类别 | 技术选型 | 版本 |
|------|---------|------|
| 编程语言 | Kotlin | 1.2.41 |
| 构建工具 | Gradle | 4.10.1 |
| 框架 | Spring Boot | 2.0.4 |
| 安全框架 | Spring Security | 2.0.4 |
| 数据库 | MariaDB | 10.5.8 |
| ORM | MyBatis (注解动态SQL) | 3.5.1 |

## 详细功能规格

### 1. 会议管理

#### 会议预约创建
- 存储会议基本信息：主题、内容、预定时间、发起人信息
- 支持附件上传及存储（无格式和大小限制）
- 记录会议状态（待开始、进行中、已取消、已结束）

#### 会议服务管理
- 支持配置以下会议服务：
  - **用餐**：人数、用餐时间
  - **桌牌**：数量、桌牌内容
  - **纸笔**：数量
  - **茶水**：数量
  - **果盘**：数量

#### 会议提醒配置
- 通知方式选择：短信通知、语音电话

### 2. 会议室管理

#### 会议室信息维护
- 存储会议室属性：名称、可用设备（投影仪、麦克风、白板、笔记本）、人员容量
- 支持通过楼层进行筛选查询

#### 会议室预约时间冲突检测
- 同一时间段会议室只能被一个会议预约
- 提供API检查指定时间段会议室是否可用
- 支持查询会议室预约情况

### 3. 参会人员管理

#### 人员类型支持
- 分为内部成员和外部成员
- 需要信息：姓名、手机号码、单位、职务

#### 参会人员选择与展示
- 根据所属单位分组展示可选人员
- 设置特殊分组"外部成员"统一展示所有外部成员

#### 参会反馈处理
- 记录参会人员反馈：参加、建议延迟（原因）、不参加（原因）
- 提供反馈统计查询接口

### 4. 通知服务
- 实现参会邀请通知发送功能
- 根据配置的提醒时间发送会议提醒
- 支持短信和语音电话两种通知方式

### 5. 查询服务
- 提供按多种条件查询会议信息的API
- 支持会议室查询和搜索功能
- 提供会议状态查询接口

## 核心数据实体及关系

```mermaid
erDiagram
    "Meeting(会议)" ||--o{ "Meetingfacility(会议服务)" : "提供服务"
    "Meeting(会议)" ||--o{ "Notification(通知)" : "发送通知"
    "Meeting(会议)" }o--|| "Meetingroom(会议室)" : "使用会议室"
    "Meeting(会议)" ||--o{ "Content(附件)" : "上传附件"
    "Meeting(会议)" }o--o{ "Personnel(人员)" : "包含参会人员"
    "Personnel(人员)" ||--o{ "Notification(通知)" : "接收通知"
```

## 项目架构

```
src/main/kotlin/com/shenlan/smartlogixmini/
├── auto/
│   ├── Base.kt                 # 基础类(BaseModel, BaseMapper, BaseService等)和Result响应类
│   │                           # 基础类包含基础的CRUD方法，其他所有的相关类都继承自基础类
│   ├── [各业务模块].kt          # 每个文件包含模型类、Mapper接口、Service类、Controller类
│   └── ...
├── sql/                        # SQL脚本
│   ├── database.sql            # 数据库建表SQL
│   └── ...
├── webapp/                     # 前端根目录
├── webapp-vue/                 # 前端根目录
└── www/                        # 前端打包目录
```

</meeting-module>