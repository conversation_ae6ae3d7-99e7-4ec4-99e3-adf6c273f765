---
description: 
globs: 
alwaysApply: true
---
<personnel-module>

## 模块概述

人事管理模块后端服务，实现身份认证、权限管理、组织架构管理等核心功能。该模块为系统提供统一的用户身份验证和权限控制基础，确保系统安全性和功能访问控制。

## 详细功能规格

### 身份认证管理

#### 多方式登录支持

- **微信手机号一键登录**：集成微信授权，获取用户手机号进行快速登录
- **手机号验证码登录**：发送短信验证码，通过手机号+验证码方式登录
- **账号密码登录**：传统的用户名密码登录方式

#### 用户准入控制

- 系统不支持用户自主注册
- 只有管理员预先添加的人员才具备登录资格
- 提供用户登录状态验证和会话管理

### 人员信息管理

#### 人员档案维护

- 存储人员基本信息：姓名、手机号、账号、密码、职务名称等
- 支持人员在不同部门间调动

#### 组织关系管理

- 每个人员必须归属于一个部门
- 维护人员与部门的关联关系

#### 工作组关系管理

- 支持人员加入多个工作组
- 提供工作组成员的增删改查功能
- 支持按工作组查询人员列表

### 权限管理体系

#### 权限类型定义

系统支持以下权限类型：
- 职工权限
- 理发管理
- 菜谱管理
- 外卖管理
- 报修管理
- 班车发车
- 人事记录
- 配置管理
- 巡检管理
- 物业巡检
- 白班巡检
- 晚班巡检
- 保洁清洁

#### 权限分配机制

- **个人权限**：直接分配给人员的权限
- **工作组权限**：分配给工作组的权限，组内成员共享
- **权限合并规则**：用户最终权限 = 个人权限 ∪ 所属工作组权限

#### 权限验证服务

- 提供权限校验API，验证用户是否具备特定功能权限
- 提供用户权限清单查询接口

### 组织架构管理

#### 部门管理

- 支持部门信息的增删改查
- 当前为平级部门结构，预留层级扩展能力
- 提供部门人员统计功能

#### 工作组管理

- 支持工作组的创建、修改、删除
- 管理工作组成员关系
- 支持工作组权限批量分配

#### 职务管理

- 维护系统职务字典，为人员职务录入提供选项参考
- 支持职务信息的增删改查

### 查询与统计服务

#### 人员查询服务

- 支持按部门、工作组、职务等维度查询人员
- 提供人员模糊搜索功能（姓名、手机号）

#### 权限统计分析

- 提供各权限的人员分布统计
- 支持工作组权限覆盖情况查询
- 提供权限使用情况分析报告

## 核心数据实体及关系

```mermaid
erDiagram
    "Personnel(人员)" }o--|| "Organization(部门)" : "归属部门"
    "Personnel(人员)" }o--o{ "Workgroup(工作组)" : "参与工作组"
    "Permission(权限)" }o--o{ "Personnel(人员)" : "个人权限"
    "Permission(权限)" }o--o{ "Workgroup(工作组)" : "工作组权限"
    "Position(职务)"
```

</personnel-module>