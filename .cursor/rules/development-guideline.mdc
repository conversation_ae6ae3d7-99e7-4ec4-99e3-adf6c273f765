---
description:
globs:
alwaysApply: true
---
<development-guideline>

## 代码风格规范

- 使用英文编写日志文本，如`log.info("English message")`，以避免日志文件出现编码问题
- 代码注释应使用中文，提高中文阅读者的理解速度
- 将类属性定义分组，组内属性之间不加空行，组与组之间用一个空行隔开，并在每组开头添加明确的分组注释
- 方法体内部必须按逻辑分组，每组前加注释，组间空行，提升可读性和维护性
- 实体类属性应分为三组：`数据库表字段`、`关联字段`和`其他字段`，各组间用空行分隔
- 转换对象为特定类型列表时（如`as List<XXX>`），添加`@Suppress("UNCHECKED_CAST")`注解避免编译警告
- 日志打印应通过`import com.shenlan.smartlogixmini.util.log`，在类内使用`log.info("Log message")`等方法，而类外使用`"".log.info("Log message")`，因为`log`是扩展属性。
- 向前端返回结果时使用中文信息，如`Result.getSuccessInfo("中文信息")`、`Result.getError("中文信息")`
- 使用全局函数`fun errorlog(e: Throwable)`打印异常堆栈信息，确保异常被完整记录
- Kotlin字符串模板中，单独变量后接非标识符字符时省略花括号（如`"Hello $name"`），但属性访问（如`${user.name}`）、表达式（如`${a+b}`）或存在歧义（如`${name}123`）必须保留花括号
- **禁止完全限定名称**：必须使用import导入，如`import org.aspectj.lang.Signature`后使用`Signature`，严禁`org.aspectj.lang.Signature`
- 在Java/Kotlin中将`Date`转换为`Instant`进行时间处理时，需注意`toInstant()`方法默认使用UTC时区，在中国（UTC+8）环境下会导致时间偏差8小时；应使用`date.toInstant().atZone(ZoneId.systemDefault()).toInstant()`确保正确应用本地时区
- 优先使用Kotlin标准库方法：在处理集合操作、文件遍历、字符串处理等常见任务时，应首先考虑使用Kotlin标准库提供的扩展函数（如`walkTopDown()`、`filter()`、`map()`、`forEach()`等）和高阶函数，而非手动实现循环或递归，以提高代码简洁性、可读性和性能。
- 在`Kotlin 1.2.41`项目中，数据类构造函数、函数参数列表和对象构造调用的最后一个参数后不能有尾随逗号。务必检查并移除所有尾随逗号以保持兼容性。
- 单行文档注释使用 `/** 文档注释 */` 格式，不要使用多行格式
- 多行文档注释使用标准的 `/** ... */` 多行格式
- 普通注释使用 `//` 单行注释

## 命名规范

- 数据库表对应的模型类命名采用首字母大写其余小写形式，如`Userinfo`、`Loginlog`
- API路径中的实体类名应与实体类类名保持一致，首字母大写
- 数据库表名使用全小写无分隔符形式，仅添加`tbl_`前缀，而字段名则使用驼峰命名法
- 假设实体名为`Aaa`，其相关类（`AaaMapper`、`AaaService`、`AaaResource`）的方法应命名为`getListByBbb`，而非`getAaaByBbb`
- Kotlin接口命名不使用`I`前缀，应直接使用`interface Aaa`而非`interface IAaa`
- 集合类型字段命名应使用对应的类型后缀，如：List字段使用`List`后缀（如`userList`）、Set字段使用`Set`后缀（如`userSet`）、Map字段使用`Map`后缀（如`userMap`），以提高代码一致性和可读性

## 方法参数规范

- `BaseService`子类重写方法时，参数名应使用`page`而非`search`，如：`override fun getList(page: BaseSearch): Result`

## 数据库规范

- 表名除`tbl_`前缀外应全部小写且不含分隔符
- 字符串类型的表字段与类属性默认应为非空，默认值设为空字符串
- 计数查询使用`COUNT(*)`而非`COUNT(1)`
- 数据库事务管理通过`@Transactional`注解实现
- MariaDB已设置`ON UPDATE CURRENT_TIMESTAMP`，因此Mybatis SQL不应使用`sysUpdated = NOW()`
- 仅在使用动态SQL的Mybatis查询中添加`<script>`标签，静态SQL无需添加
- 默认采用物理删除策略而非逻辑删除
- 数据库建表SQL中的外键字段应紧跟在`id`字段之后定义，保持字段顺序的一致性和可读性
- MyBatis动态SQL中使用`<where>`标签时，无需添加`WHERE 1=1`条件，`<where>`标签会自动处理条件拼接和`WHERE`关键字的添加

## 异常处理规范

- `@RestController`/`@Controller`/`@Service`类的方法有全局异常处理器，应打印必要日志后直接抛出异常，避免手动返回错误结果和重复打印堆栈信息

## 架构与继承规范

- `BaseMapper`/`BaseService`/`BaseResource`已定义基础方法，子类应重写这些方法而非创建新方法
- 优先使用主构造函数，避免次要构造函数
- Kotlin继承类时，通过主构造函数调用父类构造函数，如`class Meetingroom : BaseModel()`
- 获取数据模型关联对象的操作必须在Service层完成而非Mapper层，因为Mapper层无法正确加载嵌套的关联对象（关联对象的关联对象）
- Service层补充关联对象数据时，必须调用其他实体的Service层方法，不应直接使用Mapper层，确保关联对象的完整性和一致性
- Service层的`getInfo`和`getList`方法查询关联对象时，必须使用其他实体Service层的`getInfo`和`getList`方法，而非Mapper层的同名方法，以确保关联数据的完整加载
- Spring依赖注入优先使用构造器注入方式
- 注意`BaseMapper`接口中不存在`update`方法，请使用正确的替代方法

## 技术实现规范

- 多线程实现使用Java线程而非Kotlin协程
- 查询方法`getList`应使用MyBatis动态SQL并传入`Search`对象，避免为每个条件创建单独方法
- **严格禁止**在Kotlin 1.2中使用`when(val data = result.datas)`语法，这是不支持的。必须先声明变量：`val data = result.datas; when(data) {...}`

## 开发流程规范

- **⚠️ 严格禁止 ⚠️** 在未经用户明确确认前修改`STAGES.md`文件。**必须获得用户明确许可**后才能进行任何更改。这是最高优先级规则，无例外！

## 构造器依赖注入正确示例

```kotlin
@Service
class AaaService(
    mapper: AaaMapper,
    private val bbbMapper: BbbMapper
) : BaseService<Aaa, AaaMapper>(mapper)
```

</development-guideline>
