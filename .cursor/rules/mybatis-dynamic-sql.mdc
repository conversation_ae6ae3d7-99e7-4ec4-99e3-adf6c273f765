---
description:
globs:
alwaysApply: true
---
<mybatis-dynamic-sql>

## 规范

- 如果字段为Kotlin非空字符串类型，不需要`test="!= null"`

## 查询示例
```kotlin
@Select("""
<script>
  <!-- 创建模糊查询模式 -->
  <bind name="pattern" value="'%' + keyword + '%'" />

  SELECT * FROM product
  <where>
    <!-- 多数据库支持 -->
    <if test="_databaseId == 'mysql'">/* MySQL优化提示 */</if>
    <if test="_databaseId == 'oracle'">/* Oracle优化提示 */</if>

    <!-- 条件选择 -->
    <choose>
      <when test="id != null">id = #{id}</when>
      <when test="keyword != null">name LIKE #{pattern}</when>
      <otherwise>is_featured = 1</otherwise>
    </choose>

    <!-- 基本if条件 -->
    <if test="categoryId != null">AND category_id = #{categoryId}</if>

    <!-- 集合处理 -->
    <if test="tagIds.size > 0">
      AND tag_id IN
      <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
        #{tagId}
      </foreach>
    </if>

    <!-- 自定义前缀/后缀处理 -->
    <trim prefix="AND (" suffix=")" prefixOverrides="AND|OR">
      <if test="minPrice != null">price >= #{minPrice}</if>
      <if test="maxPrice != null">AND price <= #{maxPrice}</if>
    </trim>
  </where>
  ORDER BY ${orderBy == null ? 'id DESC' : orderBy}
</script>
""")
fun findProducts(search: ProductsSearch): List<Product>
```

## 更新示例
```kotlin
@Update("""
<script>
  UPDATE product
  <set>
    <if test="name != null">name = #{name},</if>
    <if test="price != null">price = #{price},</if>
    <if test="description != null">description = #{description},</if>
    <if test="stock != null">stock = #{stock},</if>
    <if test="categoryId != null">category_id = #{categoryId},</if>
    <if test="status != null">status = #{status}</if>
  </set>
  WHERE id = #{id}
</script>
""")
fun updateProduct(product: Product): Int
```

## 插入示例
```kotlin
@Insert("""
<script>
  INSERT INTO product (name, price, category_id, created_at)
  VALUES (#{name}, #{price}, #{categoryId}, NOW())

  <selectKey keyProperty="id" resultType="long" order="AFTER">
    SELECT LAST_INSERT_ID()
  </selectKey>

  <!-- 根据数据库类型选择不同的批量插入语法 -->
  <if test="tags.size > 0">
    <choose>
      <when test="_databaseId == 'mysql'">
        <!-- MySQL批量插入 -->
        INSERT INTO product_tag (product_id, tag_id) VALUES
        <foreach collection="tags" item="tag" separator=",">
          (#{id}, #{tag.id})
        </foreach>
      </when>
      <when test="_databaseId == 'oracle'">
        <!-- Oracle批量插入 -->
        <foreach collection="tags" item="tag" separator=" UNION ALL ">
          SELECT #{id}, #{tag.id} FROM dual
        </foreach>
      </when>
    </choose>
  </if>
</script>
""")
fun insertProductWithTags(product: ProductDTO): Int
```


</mybatis-dynamic-sql>
