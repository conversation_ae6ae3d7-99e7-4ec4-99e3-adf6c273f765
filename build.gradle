import org.apache.tools.ant.filters.ReplaceTokens
import org.yaml.snakeyaml.Yaml


// ==============================
// 构建脚本配置
// ==============================
buildscript {
    ext {
        KOTLIN_VERSION = '1.2.41'
        SPRING_BOOT_VERSION = '2.0.4.RELEASE'
    }

    repositories {
        // 优先使用阿里云镜像（速度快）
        maven { url 'https://maven.aliyun.com/repository/public/' }

        // Maven Central官方仓库
        maven { url 'https://repo1.maven.org/maven2/' }
    }

    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:${SPRING_BOOT_VERSION}"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${KOTLIN_VERSION}"
        classpath "org.jetbrains.kotlin:kotlin-allopen:${KOTLIN_VERSION}"
        classpath 'org.flywaydb:flyway-gradle-plugin:7.15.0'
        classpath 'org.mariadb.jdbc:mariadb-java-client:3.3.3'
        classpath 'org.yaml:snakeyaml:1.21'
    }
}

ext {
    CONFIG_DIR = 'src/main/resources/config'
    JVM_VERSION = '1.8'
}

// ==============================
// Token替换器
// ==============================
class TokenReplacer {
    private final Project project
    private final String activeProfile
    private final Map<String, Object> activeConfig
    private final Map<String, String> tokenMap

    TokenReplacer(Project project, String activeProfile, Map<String, Object> activeConfig = null) {
        this.project = project
        this.activeProfile = activeProfile
        this.activeConfig = activeConfig
        this.tokenMap = createTokenMap()
    }

    /**
     * 创建Token映射表
     */
    private Map<String, String> createTokenMap() {
        Map<String, String> tokens = [:]

        try {
            // 基础项目信息
            tokens['projectName'] = project.rootProject.name

            // 环境配置信息
            tokens['profileName'] = activeProfile

            // 获取本地IP地址
            tokens['localIpAddress'] = InetAddress.getLocalHost().hostAddress

            // 从配置中获取服务端口
            if (activeConfig && activeConfig['server'] && activeConfig['server']['port']) {
                tokens['serverPort'] = activeConfig['server']['port'].toString()
                project.logger.info("Token mapping created: localIpAddress=${tokens['localIpAddress']}, profileName=${tokens['profileName']}, serverPort=${tokens['serverPort']}")
            } else {
                project.logger.info("Token mapping created: localIpAddress=${tokens['localIpAddress']}, profileName=${tokens['profileName']}")
                project.logger.debug("serverPort not available in current context")
            }

        } catch (Exception e) {
            project.logger.warn("Failed to resolve some tokens: ${e.message}")
            // 设置默认值
            tokens['localIpAddress'] = 'localhost'
            tokens['profileName'] = activeProfile ?: 'dev'
            if (activeConfig && activeConfig['server'] && activeConfig['server']['port']) {
                tokens['serverPort'] = activeConfig['server']['port'].toString()
            }
        }

        return tokens
    }

    /**
     * 替换字符串中的所有token
     */
    String replaceTokens(String text) {
        if (!text) return text

        String result = text
        tokenMap.each { String token, String value ->
            result = result.replace("@${token}@", value)
        }

        return result
    }

    /**
     * 递归替换Map中的所有token
     */
    Object replaceTokensInObject(Object obj) {
        if (obj instanceof String) {
            return replaceTokens(obj)
        } else if (obj instanceof Map<String, Object>) {
            Map<String, Object> result = [:]
            obj.each { key, value ->
                result[key] = replaceTokensInObject(value)
            }
            return result
        } else if (obj instanceof List) {
            return obj.collect { replaceTokensInObject(it) }
        } else {
            return obj
        }
    }

    /**
     * 获取token映射表（用于调试）
     */
    Map<String, String> getTokenMap() {
        return new HashMap(tokenMap)
    }
}

// ==============================
// 配置管理工具类
// ==============================
class ConfigManager {
    private final Project project
    private final Yaml yaml = new Yaml()
    private final Map<String, Object> configCache = [:]
    private TokenReplacer tokenReplacer

    ConfigManager(Project project) {
        this.project = project
        // TokenReplacer将在getTokenReplacer()方法中延迟初始化
    }

    /**
     * 统一错误处理
     */
    private static void handleError(String message, Exception cause = null) {
        if (cause) {
            throw new GradleException("${message}: ${cause.message}", cause)
        } else {
            throw new GradleException(message)
        }
    }

    /**
     * 获取TokenReplacer实例（延迟初始化，智能获取activeConfig）
     */
    TokenReplacer getTokenReplacer() {
        // 获取当前的activeProfile
        String currentActiveProfile = getCurrentActiveProfile()

        // 尝试获取activeConfig（如果已经设置了项目扩展属性）
        Map<String, Object> activeConfig = null
        if (project.hasProperty('ext') && project.ext.hasProperty('activeConfig')) {
            activeConfig = project.ext.activeConfig as Map<String, Object>
        }

        // 如果TokenReplacer不存在，或者activeConfig可用但当前TokenReplacer没有包含它，则重新创建
        if (tokenReplacer == null || (activeConfig != null && !hasActiveConfig())) {
            tokenReplacer = new TokenReplacer(project, currentActiveProfile, activeConfig)
        }

        return tokenReplacer
    }

    /**
     * 获取用于任务执行的TokenReplacer（动态获取配置）
     */
    TokenReplacer getTokenReplacerForTask() {
        String currentActiveProfile = getCurrentActiveProfile()

        // 在任务执行阶段，直接从文件获取配置
        try {
            Map<String, Object> taskConfig = getConfig(currentActiveProfile)
            return new TokenReplacer(project, currentActiveProfile, taskConfig)
        } catch (Exception e) {
            project.logger.warn("Failed to get config for task, using basic TokenReplacer: ${e.message}")
            return new TokenReplacer(project, currentActiveProfile)
        }
    }

    /**
     * 检查当前TokenReplacer是否包含activeConfig（通过检查是否有serverPort）
     */
    private boolean hasActiveConfig() {
        if (tokenReplacer == null) return false

        // 如果当前有activeConfig可用，但TokenReplacer中没有serverPort，说明需要重新创建
        if (project.hasProperty('ext') && project.ext.hasProperty('activeConfig')) {
            Map<String, Object> currentActiveConfig = project.ext.activeConfig as Map<String, Object>
            if (currentActiveConfig && currentActiveConfig['server'] && currentActiveConfig['server']['port']) {
                // activeConfig有serverPort，检查TokenReplacer是否也有
                return tokenReplacer.getTokenMap().containsKey('serverPort')
            }
        }

        // 如果没有activeConfig可用，那么当前的TokenReplacer就是有效的
        return true
    }

    /**
     * 获取当前活跃的profile
     */
    private String getCurrentActiveProfile() {
        if (project.hasProperty("profile")) {
            return project.property("profile").toString()
        }

        // 如果没有指定profile，返回默认值
        List<String> availableProfiles = scanAvailableProfiles()
        return availableProfiles.contains('dev') ? 'dev' : availableProfiles[0]
    }

    /**
     * 读取YAML配置文件（带缓存和token替换）
     */
    Object readYamlConfig(String filePath) {
        // 检查缓存
        String cacheKey = "${filePath}_processed"
        if (configCache.containsKey(cacheKey)) {
            return configCache[cacheKey]
        }

        File configFile = project.file(filePath)
        if (!configFile.exists()) {
            handleError("Configuration file not found: ${filePath}")
        }

        try {
            // 读取原始配置
            Object rawConfig = yaml.load(configFile.text)

            // 应用token替换
            Object config = getTokenReplacer().replaceTokensInObject(rawConfig)

            // 缓存处理后的配置
            configCache[cacheKey] = config

            project.logger.debug("Loaded and processed configuration from: ${filePath}")
            return config
        } catch (Exception e) {
            handleError("Failed to parse YAML configuration file: ${filePath}", e)
            return [:]
        }
    }

    /**
     * 加载基础配置文件（application.yml）
     */
    private Map<String, Object> getBaseConfig() {
        // 检查缓存
        String cacheKey = "base_config_processed"
        if (configCache.containsKey(cacheKey)) {
            return configCache[cacheKey] as Map<String, Object>
        }

        String baseConfigPath = "${project.ext.CONFIG_DIR}/application.yml"
        File baseConfigFile = project.file(baseConfigPath)

        // 如果基础配置文件不存在，返回空配置并记录警告
        if (!baseConfigFile.exists()) {
            project.logger.warn("Base configuration file not found: ${baseConfigPath}")
            Map<String, Object> emptyConfig = [:]
            configCache[cacheKey] = emptyConfig
            return emptyConfig
        }

        try {
            Map<String, Object> baseConfig = readYamlConfig(baseConfigPath) as Map<String, Object>
            configCache[cacheKey] = baseConfig
            project.logger.debug("Loaded base configuration from: ${baseConfigPath}")
            return baseConfig
        } catch (Exception e) {
            project.logger.warn("Failed to load base configuration, using empty config: ${e.message}")
            Map<String, Object> emptyConfig = [:]
            configCache[cacheKey] = emptyConfig
            return emptyConfig
        }
    }

    /**
     * 获取环境特定配置（私有方法，原getConfig方法）
     */
    private Map<String, Object> getProfileConfig(String profileName) {
        String configPath = "${project.ext.CONFIG_DIR}/application-${profileName}.yml"
        return readYamlConfig(configPath) as Map<String, Object>
    }

    /**
     * 获取完整配置（基础配置 + 环境配置合并）
     */
    Map<String, Object> getConfig(String profileName) {
        // 检查合并配置的缓存
        String cacheKey = "merged_${profileName}_processed"
        if (configCache.containsKey(cacheKey)) {
            return configCache[cacheKey] as Map<String, Object>
        }

        try {
            // 加载基础配置
            Map<String, Object> baseConfig = getBaseConfig()
            project.logger.info("Base configuration loaded for profile merging: ${profileName}")

            // 加载环境特定配置
            Map<String, Object> profileConfig = getProfileConfig(profileName)
            project.logger.info("Profile configuration loaded: ${profileName}")

            // 合并配置：基础配置作为底层，环境配置覆盖基础配置
            Map<String, Object> mergedConfig = deepMergeConfigs(baseConfig, profileConfig)

            // 缓存合并后的配置
            configCache[cacheKey] = mergedConfig
            project.logger.info("Configuration merged and cached for profile: ${profileName}")

            return mergedConfig
        } catch (Exception e) {
            project.logger.error("Failed to merge configuration for profile ${profileName}: ${e.message}")
            // 如果合并失败，尝试返回环境配置
            try {
                return getProfileConfig(profileName)
            } catch (Exception fallbackError) {
                handleError("Failed to load any configuration for profile: ${profileName}", fallbackError)
                return [:]
            }
        }
    }

    /**
     * 扫描可用环境配置
     */
    List<String> scanAvailableProfiles() {
        File configDir = project.file(project.ext.CONFIG_DIR)
        List<String> profiles = []

        if (!configDir.exists() || !configDir.isDirectory()) {
            handleError("Configuration directory not found: ${project.ext.CONFIG_DIR}")
        }

        configDir.listFiles()?.each { File configFile ->
            if (isValidConfigFile(configFile)) {
                String profileName = extractProfileName(configFile.name)
                if (profileName) {
                    profiles << profileName
                }
            }
        }

        if (profiles.isEmpty()) {
            handleError("No environment configuration files found in ${project.ext.CONFIG_DIR}/. Please create application-{profile}.yml files.")
        }

        return profiles.sort()
    }

    /**
     * 检查是否为有效的配置文件
     */
    private static boolean isValidConfigFile(File file) {
        return file.name.startsWith('application-') && file.name.endsWith('.yml')
    }

    /**
     * 从文件名提取环境名
     */
    private static String extractProfileName(String fileName) {
        String prefix = 'application-'
        String suffix = '.yml'

        if (fileName.length() <= prefix.length() + suffix.length()) {
            return null
        }

        String profileName = fileName.substring(prefix.length(), fileName.length() - suffix.length())
        return profileName?.trim() ?: null
    }

    /**
     * 加载所有环境配置（使用私有方法getProfileConfig）
     */
    Map<String, Object> loadAllProfileConfigs(List<String> profiles) {
        Map<String, Object> configs = [:]

        profiles.each { String profileName ->
            try {
                configs[profileName] = getProfileConfig(profileName)
                project.logger.info("Loaded configuration for profile: ${profileName}")
            } catch (Exception e) {
                project.logger.warn("Failed to load configuration for profile ${profileName}: ${e.message}")
            }
        }

        return configs
    }

    /**
     * 深度合并配置（优化版本）
     */
    static Map<String, Object> deepMergeConfigs(Map<String, Object> target, Map<String, Object> source) {
        if (!target) return new HashMap(source ?: [:])
        if (!source) return new HashMap(target)

        Map<String, Object> result = new HashMap(target)

        source.each { String key, Object value ->
            if (value instanceof Map && result[key] instanceof Map) {
                result[key] = deepMergeConfigs(result[key] as Map<String, Object>, value as Map<String, Object>)
            } else {
                result[key] = value
            }
        }

        return result
    }

    /**
     * 获取合并后的激活配置
     */
    Map<String, Object> getMergedActiveConfig(List<String> profileList, Map<String, Object> allConfigs) {
        Map<String, Object> mergedConfig = [:]

        profileList.each { String profileName ->
            Object currentConfig = allConfigs[profileName]
            if (currentConfig) {
                mergedConfig = deepMergeConfigs(mergedConfig, currentConfig as Map<String, Object>)
                project.logger.info("Merged configuration from profile: ${profileName}")
            } else {
                project.logger.warn("Configuration not found for profile '${profileName}', skipping...")
            }
        }

        // 使用默认配置作为后备
        if (mergedConfig.isEmpty() && !allConfigs.isEmpty()) {
            String firstProfile = allConfigs.keySet().iterator().next()
            mergedConfig = allConfigs[firstProfile] as Map<String, Object>
            project.logger.info("Using default configuration from profile: ${firstProfile}")
        }

        return mergedConfig
    }

    /**
     * 验证环境配置有效性
     */
    static void validateProfiles(List<String> requestedProfiles, List<String> availableProfiles) {
        requestedProfiles.each { String profile ->
            if (!availableProfiles.contains(profile)) {
                handleError("Invalid profile: ${profile}. Available profiles are: ${availableProfiles}")
            }
        }
    }
}

// ==============================
// 环境配置初始化
// ==============================

// 创建配置管理器实例
ConfigManager configManager = new ConfigManager(project)

// 扫描并加载所有可用环境配置
List<String> availableProfiles = configManager.scanAvailableProfiles()
println "Available environment profiles: ${availableProfiles}"

// 确定激活的环境配置
String defaultProfile = availableProfiles.contains('dev') ? 'dev' : availableProfiles[0]
String activeProfile = project.hasProperty("profile") ? project.property("profile") : defaultProfile
List<String> activeProfiles = activeProfile.toString().split(',').collect { it.trim() }

// 验证环境配置有效性
configManager.validateProfiles(activeProfiles, availableProfiles)
println "Building with profiles: ${activeProfiles}"

// 加载所有环境配置
Map<String, Object> profileConfigs = configManager.loadAllProfileConfigs(availableProfiles)

// 获取合并后的激活配置
Map<String, Object> activeConfig = configManager.getMergedActiveConfig(activeProfiles, profileConfigs)

// 将配置存储为项目扩展属性
project.ext.profileConfigs = profileConfigs
project.ext.availableProfiles = availableProfiles
project.ext.activeConfig = activeConfig
project.ext.configManager = configManager

// ==============================
// 应用插件
// ==============================
apply plugin: 'kotlin'
apply plugin: 'kotlin-spring'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'war'
apply plugin: 'org.flywaydb.flyway'

// ==============================
// 项目基本信息
// ==============================
// 动态生成包名：去除连字符，转为小写
String formattedProjectName = rootProject.name.replaceAll('-', '').toLowerCase()
String basePackage = "com.shenlan.${formattedProjectName}"

group = basePackage
version = ''
sourceCompatibility = JVM_VERSION

// ==============================
// Kotlin编译配置
// ==============================
compileKotlin {
    kotlinOptions {
        freeCompilerArgs = ['-Xjsr305=strict', '-java-parameters']
        jvmTarget = JVM_VERSION
    }
}

compileTestKotlin {
    kotlinOptions {
        freeCompilerArgs = ['-Xjsr305=strict', '-java-parameters']
        jvmTarget = JVM_VERSION
    }
}

// ==============================
// Spring Boot配置
// ==============================
springBoot {
    mainClassName = "${basePackage}.ApplicationKt"
}

// ==============================
// 仓库配置
// ==============================
repositories {
    // 优先使用阿里云镜像（国内速度快）
    maven { url 'https://maven.aliyun.com/repository/public' }

    // Maven Central官方仓库
    maven { url 'https://repo1.maven.org/maven2/' }
}

// ==============================
// 构建配置
// ==============================
// 禁用测试
test.enabled = false

// WAR配置
war {
    webAppDirName = 'www'  // 根目录设置为build/www
}

// 资源处理配置
processResources {
    // 处理配置文件中的占位符替换，使用统一的TokenReplacer
    filesMatching('config/*.yml') {
        // 直接使用TokenReplacer的完整token映射表
        Map<String, String> tokens = configManager.getTokenReplacer().getTokenMap()

        filter ReplaceTokens, tokens: tokens
    }

    // 复制前端资源文件到根目录
    from('www') {
        into('/')
    }

    // 将Flyway迁移脚本包含到WAR包中
    from('sql/flyway/migration') {
        into('sql/flyway/migration')
    }

    // 设置处理编码
    filteringCharset = 'UTF-8'
}

// ==============================
// 启动脚本复制
// ==============================
tasks.register('copyStartScripts', Copy) {
    description = 'Copy and process startup scripts with environment-specific tokens'
    group = 'application'

    from 'start-scripts'
    into 'build/scripts'

    filesMatching('**/*') {
        println "Processing startup script: ${name} for profile: ${activeProfile}"

        // 直接使用TokenReplacer的完整token映射表（已包含serverPort等所有token）
        Map<String, String> tokens = configManager.getTokenReplacerForTask().getTokenMap()

        // 设置过滤器
        filter ReplaceTokens, tokens: tokens
    }
}

// 让构建任务依赖脚本复制任务
build.dependsOn copyStartScripts

// 排除依赖
configurations {
    configureEach {
        exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
    }
}

// ==============================
// 项目依赖
// ==============================
dependencies {
    // 核心依赖
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
    implementation 'org.springframework.boot:spring-boot-starter-security'

    // Kotlin相关
    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8"
    implementation "org.jetbrains.kotlin:kotlin-reflect"

    // 数据库相关
    implementation 'org.mariadb.jdbc:mariadb-java-client'
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:2.0.1'
    implementation 'com.github.pagehelper:pagehelper-spring-boot-starter:1.2.12'

    // Flyway数据库迁移
    implementation 'org.flywaydb:flyway-core'

    // 工具库
    implementation 'com.alibaba:fastjson:1.2.83'
    implementation 'com.github.promeg:tinypinyin:2.0.3'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

    // 阿里云
    implementation 'com.aliyun:aliyun-java-sdk-core:4.5.14'
    implementation 'com.aliyun:aliyun-java-sdk-dybaseapi:1.1.1'
    implementation 'com.aliyun:dysmsapi20170525:4.1.0'
    implementation 'com.aliyun:dyvmsapi20170525:3.2.2'
    implementation 'com.aliyun.mns:aliyun-sdk-mns:1.1.9.1'
    implementation files('lib/alicom-mns-receive-sdk-1.1.3.jar')

    // 微信支付
    implementation("com.github.wechatpay-apiv3:wechatpay-java:0.2.17") {
        exclude group: "com.squareup.okhttp3", module: "okhttp"
        exclude group: "com.squareup.okio", module: "okio"
    }
    implementation("com.squareup.okhttp3:okhttp:3.12.13")
//    implementation("com.squareup.okio:okio:3.0.0")
    implementation("org.bouncycastle:bcprov-jdk18on:1.80")
    implementation("org.bouncycastle:bcpkix-jdk18on:1.80")

    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.junit.jupiter:junit-jupiter-api'
    testImplementation 'org.junit.jupiter:junit-jupiter-engine'
    testImplementation 'org.junit.jupiter:junit-jupiter-params'
    testImplementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter-test:2.0.1'
}

// ==============================
// 编码配置
// ==============================
tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
}

tasks.withType(Javadoc).configureEach {
    options.encoding = 'UTF-8'
}

tasks.withType(ProcessResources).configureEach {
    filteringCharset = 'UTF-8'
}

// ==============================
// Flyway配置管理器
// ==============================
class FlywayConfigManager {
    private final Project project
    private final ConfigManager configManager

    FlywayConfigManager(Project project, ConfigManager configManager) {
        this.project = project
        this.configManager = configManager
    }

    /**
     * 交互式选择Flyway运行环境
     */
    String selectEnvironmentInteractively(List<String> availableProfiles) {
        print "Enter environment (${availableProfiles.join('/')}): "
        System.out.flush()

        String input = readUserInput()

        if (!input || !availableProfiles.contains(input)) {
            println "\nAvailable environments: ${availableProfiles.join(', ')}"
            throw new GradleException("Invalid environment: '${input}'. Available: ${availableProfiles.join(', ')}")
        }

        return input
    }

    /**
     * 读取用户输入（支持多种环境）
     */
    private String readUserInput() {
        Console console = System.console() as Console
        if (console != null) {
            return console.readLine()?.trim()?.toLowerCase()
        }

        // 在Windows gradlew.bat或IDE环境下的备选方案
        try {
            BufferedReader reader = new BufferedReader(new InputStreamReader(System.in))
            return reader.readLine()?.trim()?.toLowerCase()
        } catch (Exception ignored) {
            println '\nWarning: Unable to read input in current environment.'
            println 'Please specify profile using -Pprofile=<env> parameter'
            println "Example: gradlew flywayInfo -Pprofile=${project.ext.availableProfiles[0]}"
            throw new GradleException('Interactive input not available. Use -Pprofile=<env> parameter instead.')
        }
    }

    /**
     * 确认生产环境操作
     */
    static void confirmProductionOperation(String profileName, String taskName) {
        if (profileName == 'prod' && taskName == 'flywayMigrate') {
            println 'WARNING: You are about to modify the PRODUCTION database!'
            println 'This operation will modify the production database structure and data.'
            print "Are you sure you want to continue? (type 'YES'): "

            String input = System.console()?.readLine()
            if (input != 'YES') {
                throw new GradleException('Production database migration cancelled.')
            }

            println 'Confirmation received. Starting production database migration...'
            println ''
        }
    }

    /**
     * 打印Flyway配置信息
     */
    void printFlywayInfo(String profileName, Map<String, Object> datasource) {
        println ''
        println '=========================================='
        println 'Flyway Database Migration'
        println '=========================================='
        println "Environment: ${profileName}"
        println "Database URL: ${datasource['url']}"
        println "Username: ${datasource['username']}"
        println 'Migration scripts location: sql/flyway/migration'
        println "Available environments: ${project.ext.availableProfiles.join(', ')}"
        println '=========================================='
        println ''
    }
}

// ==============================
// Flyway配置 - 支持交互式选择环境
// ==============================
FlywayConfigManager flywayConfigManager = new FlywayConfigManager(project, configManager)

// Flyway运行时配置
String flywayProfile = null
Map<String, Object> flywayDatasource = null

// 在Flyway任务执行前进行环境选择和配置
tasks.matching { task -> task.name.startsWith('flyway') }.configureEach {
    doFirst {
        // 环境选择逻辑
        if (flywayProfile == null) {
            if (project.hasProperty('profile')) {
                String specifiedProfile = project.property('profile').toString().toLowerCase()
                configManager.validateProfiles([specifiedProfile], availableProfiles)
                flywayProfile = specifiedProfile
                println "Using flyway profile from command line: ${flywayProfile}"
            } else {
                flywayProfile = flywayConfigManager.selectEnvironmentInteractively(availableProfiles)
            }

            // 使用ConfigManager获取完整配置
            Map<String, Object> config = configManager.getConfig(flywayProfile)

            // 获取数据源配置
            Map<String, Object> spring = config['spring'] as Map<String, Object>
            if (!spring) {
                throw new GradleException("Spring configuration not found for profile: ${flywayProfile}")
            }

            flywayDatasource = spring['datasource'] as Map<String, Object>
            if (!flywayDatasource) {
                throw new GradleException("Datasource configuration not found for profile: ${flywayProfile}")
            }

            // 获取flyway配置
            Map<String, Object> flywayConfig = spring['flyway'] as Map<String, Object>
            if (!flywayConfig) {
                throw new GradleException("Flyway configuration not found for profile: ${flywayProfile}")
            }

            // 动态设置所有flyway属性
            project['flyway']['url'] = flywayDatasource['url']
            project['flyway']['user'] = flywayDatasource['username']
            project['flyway']['password'] = flywayDatasource['password']
            project['flyway']['driver'] = flywayDatasource['driver-class-name']

            // 设置flyway基础配置
            def locationsConfig = flywayConfig['locations']
            project['flyway']['locations'] = locationsConfig instanceof String ? [locationsConfig] : locationsConfig
            project['flyway']['baselineOnMigrate'] = flywayConfig['baseline-on-migrate']
            project['flyway']['validateOnMigrate'] = flywayConfig['validate-on-migrate']
            project['flyway']['encoding'] = flywayConfig['encoding']
            project['flyway']['baselineVersion'] = flywayConfig['baseline-version']
            project['flyway']['baselineDescription'] = flywayConfig['baseline-description']

            project.logger.info("Flyway configuration loaded dynamically for profile: ${flywayProfile}")
        }

        // 打印配置信息
        flywayConfigManager.printFlywayInfo(flywayProfile, flywayDatasource)

        // 生产环境安全确认
        flywayConfigManager.confirmProductionOperation(flywayProfile, delegate.name)
    }
}
