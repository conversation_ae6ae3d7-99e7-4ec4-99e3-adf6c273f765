import org.apache.tools.ant.filters.ReplaceTokens

// ==============================
// 构建脚本配置
// ==============================
buildscript {
    ext {
        KOTLIN_VERSION = '1.2.41'
        SPRING_BOOT_VERSION = '2.0.4.RELEASE'
    }

    repositories {
        // 优先使用阿里云镜像（速度快）
        maven { url 'https://maven.aliyun.com/repository/public/' }

        // Maven Central官方仓库
        maven { url 'https://repo1.maven.org/maven2/' }
    }

    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:${SPRING_BOOT_VERSION}"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${KOTLIN_VERSION}"
        classpath "org.jetbrains.kotlin:kotlin-allopen:${KOTLIN_VERSION}"
        classpath 'org.flywaydb:flyway-gradle-plugin:7.15.0'
        classpath 'org.mariadb.jdbc:mariadb-java-client:3.3.3'
        classpath 'org.yaml:snakeyaml:1.21'
    }
}

ext {
    CONFIG_DIR = 'src/main/resources/config'
    JVM_VERSION = '1.8'
}

// ==============================
// 环境配置初始化
// ==============================

// 创建配置管理器实例
ConfigManager configManager = new ConfigManager(project)

// 扫描并加载所有可用环境配置
List<String> availableProfiles = configManager.scanAvailableProfiles()
println "Available environment profiles: ${availableProfiles}"

// 确定激活的环境配置
String defaultProfile = availableProfiles.contains('dev') ? 'dev' : availableProfiles[0]
String activeProfile = project.hasProperty("profile") ? project.property("profile") : defaultProfile
List<String> activeProfiles = activeProfile.toString().split(',').collect { it.trim() }

// 验证环境配置有效性
configManager.validateProfiles(activeProfiles, availableProfiles)
println "Building with profiles: ${activeProfiles}"

// 加载所有环境配置
Map<String, Object> profileConfigs = configManager.loadAllProfileConfigs(availableProfiles)

// 获取合并后的激活配置
Map<String, Object> activeConfig = configManager.getMergedActiveConfig(activeProfiles, profileConfigs)

// 将配置存储为项目扩展属性
project.ext.profileConfigs = profileConfigs
project.ext.availableProfiles = availableProfiles
project.ext.activeConfig = activeConfig
project.ext.configManager = configManager

// ==============================
// 应用插件
// ==============================
apply plugin: 'kotlin'
apply plugin: 'kotlin-spring'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'war'
apply plugin: 'org.flywaydb.flyway'

// ==============================
// 项目基本信息
// ==============================
// 动态生成包名：去除连字符，转为小写
String formattedProjectName = rootProject.name.replaceAll('-', '').toLowerCase()
String basePackage = "com.shenlan.${formattedProjectName}"

group = basePackage
version = ''
sourceCompatibility = JVM_VERSION

// ==============================
// Kotlin编译配置
// ==============================
compileKotlin {
    kotlinOptions {
        freeCompilerArgs = ['-Xjsr305=strict', '-java-parameters']
        jvmTarget = JVM_VERSION
    }
}

compileTestKotlin {
    kotlinOptions {
        freeCompilerArgs = ['-Xjsr305=strict', '-java-parameters']
        jvmTarget = JVM_VERSION
    }
}

// ==============================
// Spring Boot配置
// ==============================
springBoot {
    mainClassName = "${basePackage}.ApplicationKt"
}

// ==============================
// 仓库配置
// ==============================
repositories {
    // 优先使用阿里云镜像（国内速度快）
    maven { url 'https://maven.aliyun.com/repository/public' }

    // Maven Central官方仓库
    maven { url 'https://repo1.maven.org/maven2/' }
}

// ==============================
// 构建配置
// ==============================
// 禁用测试
test.enabled = false

// WAR配置
war {
    webAppDirName = 'www'  // 根目录设置为build/www
}

// 资源处理配置
processResources {
    // 处理配置文件中的占位符替换，使用统一的TokenReplacer
    filesMatching('config/*.yml') {
        // 直接使用TokenReplacer的完整token映射表
        Map<String, String> tokens = configManager.getTokenReplacer().getTokenMap()

        filter ReplaceTokens, tokens: tokens
    }

    // 复制前端资源文件到根目录
    from('www') {
        into('/')
    }

    // 将Flyway迁移脚本包含到WAR包中
    from('sql/flyway/migration') {
        into('sql/flyway/migration')
    }

    // 设置处理编码
    filteringCharset = 'UTF-8'
}

// ==============================
// 启动脚本复制
// ==============================
tasks.register('copyStartScripts', Copy) {
    description = 'Copy and process startup scripts with environment-specific tokens'
    group = 'application'

    from 'start-scripts'
    into 'build/scripts'

    filesMatching('**/*') {
        println "Processing startup script: ${name} for profile: ${activeProfile}"

        // 直接使用TokenReplacer的完整token映射表（已包含serverPort等所有token）
        Map<String, String> tokens = configManager.getTokenReplacerForTask().getTokenMap()

        // 设置过滤器
        filter ReplaceTokens, tokens: tokens
    }
}

// 让构建任务依赖脚本复制任务
build.dependsOn copyStartScripts

// 排除依赖
configurations {
    configureEach {
        exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
    }
}

// ==============================
// 项目依赖
// ==============================
dependencies {
    // 核心依赖
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
    implementation 'org.springframework.boot:spring-boot-starter-security'

    // Kotlin相关
    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8"
    implementation "org.jetbrains.kotlin:kotlin-reflect"

    // 数据库相关
    implementation 'org.mariadb.jdbc:mariadb-java-client'
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:2.0.1'
    implementation 'com.github.pagehelper:pagehelper-spring-boot-starter:1.2.12'

    // Flyway数据库迁移
    implementation 'org.flywaydb:flyway-core'

    // 工具库
    implementation 'com.alibaba:fastjson:1.2.83'
    implementation 'com.github.promeg:tinypinyin:2.0.3'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    implementation 'com.jayway.jsonpath:json-path:2.9.0'

    // 阿里云
    implementation 'com.aliyun:aliyun-java-sdk-core:4.5.14'
    implementation 'com.aliyun:aliyun-java-sdk-dybaseapi:1.1.1'
    implementation 'com.aliyun:dysmsapi20170525:4.1.0'
    implementation 'com.aliyun:dyvmsapi20170525:3.2.2'
    implementation 'com.aliyun.mns:aliyun-sdk-mns:1.1.9.1'
    implementation files('lib/alicom-mns-receive-sdk-1.1.3.jar')

    // 微信支付
    implementation("com.github.wechatpay-apiv3:wechatpay-java:0.2.17") {
        exclude group: "com.squareup.okhttp3", module: "okhttp"
        exclude group: "com.squareup.okio", module: "okio"
    }
    implementation("com.squareup.okhttp3:okhttp:3.12.13")
//    implementation("com.squareup.okio:okio:3.0.0")
    implementation("org.bouncycastle:bcprov-jdk18on:1.80")
    implementation("org.bouncycastle:bcpkix-jdk18on:1.80")
    implementation("com.google.zxing:core:3.5.1")
    implementation("com.google.zxing:javase:3.5.1")

    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.junit.jupiter:junit-jupiter-api'
    testImplementation 'org.junit.jupiter:junit-jupiter-engine'
    testImplementation 'org.junit.jupiter:junit-jupiter-params'
    testImplementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter-test:2.0.1'

    //导出excel
    implementation "org.apache.poi:poi-ooxml:5.2.3"
    implementation "org.apache.poi:poi:5.2.3"
    implementation 'commons-io:commons-io:2.17.0'
    implementation 'org.apache.commons:commons-lang3:3.17.0'
    implementation 'org.apache.logging.log4j:log4j-api:2.17.1'
}

// ==============================
// 编码配置
// ==============================
tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
}

tasks.withType(Javadoc).configureEach {
    options.encoding = 'UTF-8'
}

tasks.withType(ProcessResources).configureEach {
    filteringCharset = 'UTF-8'
}

// ==============================
// Flyway配置 - 支持交互式选择环境
// ==============================

// 在Flyway任务执行前进行环境选择和配置
tasks.matching { task -> task.name.startsWith('flyway') }.configureEach {
    doFirst {
        // 使用FlywayConfigManager静态方法处理所有配置逻辑
        FlywayConfigManager.configureFlywayTask(project, configManager, availableProfiles, delegate.name)
    }
}

// ==============================
// 清理任务扩展
// ==============================
clean {
    // 清理buildSrc的build目录
    delete 'buildSrc/build'
}
