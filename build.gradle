import org.apache.tools.ant.filters.ReplaceTokens

// ==============================
// 构建脚本配置
// ==============================
buildscript {
    ext {
        KOTLIN_VERSION = '1.2.41'
        SPRING_BOOT_VERSION = '2.0.4.RELEASE'
    }

    repositories {
        // 优先使用阿里云镜像（速度快）
        maven { url 'https://maven.aliyun.com/repository/public/' }

        // Maven Central官方仓库
        maven { url 'https://repo1.maven.org/maven2/' }
    }

    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:${SPRING_BOOT_VERSION}"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${KOTLIN_VERSION}"
        classpath "org.jetbrains.kotlin:kotlin-allopen:${KOTLIN_VERSION}"
        classpath 'org.yaml:snakeyaml:1.21'
    }
}

ext {
    CONFIG_DIR = 'src/main/resources/config'
    JVM_VERSION = '1.8'
}


// ==============================
// 应用插件
// ==============================
apply plugin: 'kotlin'
apply plugin: 'kotlin-spring'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'war'

// ==============================
// 项目基本信息
// ==============================
// 动态生成包名：去除连字符，转为小写
String formattedProjectName = rootProject.name.replaceAll('-', '').toLowerCase()
String basePackage = "com.shenlan.${formattedProjectName}"

group = basePackage
version = ''
sourceCompatibility = JVM_VERSION

// ==============================
// Kotlin编译配置
// ==============================
compileKotlin {
    kotlinOptions {
        freeCompilerArgs = ['-Xjsr305=strict', '-java-parameters']
        jvmTarget = JVM_VERSION
    }
}

compileTestKotlin {
    kotlinOptions {
        freeCompilerArgs = ['-Xjsr305=strict', '-java-parameters']
        jvmTarget = JVM_VERSION
    }
}

// ==============================
// Spring Boot配置
// ==============================
springBoot {
    mainClassName = "${basePackage}.ApplicationKt"
}

// ==============================
// 仓库配置
// ==============================
repositories {
    // 优先使用阿里云镜像（国内速度快）
    maven { url 'https://maven.aliyun.com/repository/public' }

    // Maven Central官方仓库
    maven { url 'https://repo1.maven.org/maven2/' }
}

// ==============================
// 构建配置
// ==============================
// 禁用测试
test.enabled = false

// WAR配置
war {
    webAppDirName = 'www'  // 根目录设置为build/www
}

// 资源处理配置
processResources {
    // 处理配置文件中的占位符替换
    filesMatching('config/*.yml') {
        // 创建基础token映射
        String currentProfile = project.hasProperty("profile") ? project.property("profile") : 'dev'
        Map<String, String> tokens = [
            'projectName': rootProject.name,
            'profileName': currentProfile
        ]
        filter ReplaceTokens, tokens: tokens
    }

    // 复制前端资源文件到根目录
    from('www') {
        into('/')
    }

    // 设置处理编码
    filteringCharset = 'UTF-8'
}

// ==============================
// 项目依赖
// ==============================
dependencies {
    // 核心依赖
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
    implementation 'org.springframework.boot:spring-boot-starter-security'

    // Kotlin相关
    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8"
    implementation "org.jetbrains.kotlin:kotlin-reflect"

    // 数据库相关
    implementation 'org.mariadb.jdbc:mariadb-java-client'
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:2.0.1'
    implementation "com.baomidou:mybatis-plus-boot-starter:3.0.5"
    implementation 'com.github.pagehelper:pagehelper-spring-boot-starter:1.2.12'

    // 工具库
    implementation 'com.alibaba:fastjson:1.2.83'
    implementation 'com.github.promeg:tinypinyin:2.0.3'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    implementation 'com.jayway.jsonpath:json-path:2.9.0'
    implementation 'cn.6tail:lunar:1.7.4'

    // 阿里云
    implementation 'com.aliyun:aliyun-java-sdk-core:4.5.14'
    implementation 'com.aliyun:aliyun-java-sdk-dybaseapi:1.1.1'
    implementation 'com.aliyun:dysmsapi20170525:4.1.0'
    implementation 'com.aliyun:dyvmsapi20170525:3.2.2'
    implementation 'com.aliyun.mns:aliyun-sdk-mns:1.1.9.1'
    implementation files('lib/alicom-mns-receive-sdk-1.1.3.jar')

    // 微信支付
    implementation("com.github.wechatpay-apiv3:wechatpay-java:0.2.17") {
        exclude group: "com.squareup.okhttp3", module: "okhttp"
        exclude group: "com.squareup.okio", module: "okio"
    }
    implementation("com.squareup.okhttp3:okhttp:3.12.13")
//    implementation("com.squareup.okio:okio:3.0.0")
    implementation("org.bouncycastle:bcprov-jdk18on:1.80")
    implementation("org.bouncycastle:bcpkix-jdk18on:1.80")
    implementation("com.google.zxing:core:3.5.1")
    implementation("com.google.zxing:javase:3.5.1")

    // 智谱ai
    implementation("cn.bigmodel.openapi:oapi-java-sdk:release-V4-2.4.1")

    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.junit.jupiter:junit-jupiter-api'
    testImplementation 'org.junit.jupiter:junit-jupiter-engine'
    testImplementation 'org.junit.jupiter:junit-jupiter-params'
    testImplementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter-test:2.0.1'

    //导出excel
    implementation "org.apache.poi:poi-ooxml:4.1.2"
    implementation "org.apache.poi:poi:4.1.2"
    implementation 'commons-io:commons-io:2.17.0'
    implementation 'org.apache.commons:commons-lang3:3.17.0'
}

// ==============================
// 编码配置
// ==============================
tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
}

tasks.withType(Javadoc).configureEach {
    options.encoding = 'UTF-8'
}

tasks.withType(ProcessResources).configureEach {
    filteringCharset = 'UTF-8'
}


// ==============================
// 清理任务扩展
// ==============================
clean {
    // 清理buildSrc的build目录
    delete 'buildSrc/build'
}
