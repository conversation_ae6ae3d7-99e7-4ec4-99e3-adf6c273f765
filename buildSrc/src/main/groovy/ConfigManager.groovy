import org.gradle.api.GradleException
import org.gradle.api.Project
import org.yaml.snakeyaml.Yaml

class ConfigManager {
    private final Project project
    private final Yaml yaml = new Yaml()
    private final Map<String, Object> configCache = [:]
    private TokenReplacer tokenReplacer

    ConfigManager(Project project) {
        this.project = project
        // TokenReplacer将在getTokenReplacer()方法中延迟初始化
    }

    /**
     * 统一错误处理
     */
    private static void handleError(String message, Exception cause = null) {
        if (cause) {
            throw new GradleException("${message}: ${cause.message}", cause)
        } else {
            throw new GradleException(message)
        }
    }

    /**
     * 获取TokenReplacer实例（延迟初始化，智能获取activeConfig）
     */
    TokenReplacer getTokenReplacer() {
        // 获取当前的activeProfile
        String currentActiveProfile = getCurrentActiveProfile()

        // 尝试获取activeConfig（如果已经设置了项目扩展属性）
        Map<String, Object> activeConfig = null
        if (project.hasProperty('ext') && project.ext.hasProperty('activeConfig')) {
            activeConfig = project.ext.activeConfig as Map<String, Object>
        }

        // 如果TokenReplacer不存在，或者activeConfig可用但当前TokenReplacer没有包含它，则重新创建
        if (tokenReplacer == null || (activeConfig != null && !hasActiveConfig())) {
            tokenReplacer = new TokenReplacer(project, currentActiveProfile, activeConfig)
        }

        return tokenReplacer
    }

    /**
     * 获取用于任务执行的TokenReplacer（动态获取配置）
     */
    TokenReplacer getTokenReplacerForTask() {
        String currentActiveProfile = getCurrentActiveProfile()

        // 在任务执行阶段，直接从文件获取配置
        try {
            Map<String, Object> taskConfig = getConfig(currentActiveProfile)
            return new TokenReplacer(project, currentActiveProfile, taskConfig)
        } catch (Exception e) {
            project.logger.warn("Failed to get config for task, using basic TokenReplacer: ${e.message}")
            return new TokenReplacer(project, currentActiveProfile)
        }
    }

    /**
     * 检查当前TokenReplacer是否包含activeConfig（通过检查是否有serverPort）
     */
    private boolean hasActiveConfig() {
        if (tokenReplacer == null) return false

        // 如果当前有activeConfig可用，但TokenReplacer中没有serverPort，说明需要重新创建
        if (project.hasProperty('ext') && project.ext.hasProperty('activeConfig')) {
            Map<String, Object> currentActiveConfig = project.ext.activeConfig as Map<String, Object>
            if (currentActiveConfig && currentActiveConfig['server'] && currentActiveConfig['server']['port']) {
                // activeConfig有serverPort，检查TokenReplacer是否也有
                return tokenReplacer.getTokenMap().containsKey('serverPort')
            }
        }

        // 如果没有activeConfig可用，那么当前的TokenReplacer就是有效的
        return true
    }

    /**
     * 获取当前活跃的profile
     */
    private String getCurrentActiveProfile() {
        if (project.hasProperty("profile")) {
            return project.property("profile").toString()
        }

        // 如果没有指定profile，返回默认值
        List<String> availableProfiles = scanAvailableProfiles()
        return availableProfiles.contains('dev') ? 'dev' : availableProfiles[0]
    }

    /**
     * 读取YAML配置文件（带缓存和token替换）
     */
    Object readYamlConfig(String filePath) {
        // 检查缓存
        String cacheKey = "${filePath}_processed"
        if (configCache.containsKey(cacheKey)) {
            return configCache[cacheKey]
        }

        File configFile = project.file(filePath)
        if (!configFile.exists()) {
            handleError("Configuration file not found: ${filePath}")
        }

        try {
            // 读取原始配置
            Object rawConfig = yaml.load(configFile.text)

            // 应用token替换
            Object config = getTokenReplacer().replaceTokensInObject(rawConfig)

            // 缓存处理后的配置
            configCache[cacheKey] = config

            project.logger.debug("Loaded and processed configuration from: ${filePath}")
            return config
        } catch (Exception e) {
            handleError("Failed to parse YAML configuration file: ${filePath}", e)
            return [:]
        }
    }

    /**
     * 加载基础配置文件（application.yml）
     */
    private Map<String, Object> getBaseConfig() {
        // 检查缓存
        String cacheKey = "base_config_processed"
        if (configCache.containsKey(cacheKey)) {
            return configCache[cacheKey] as Map<String, Object>
        }

        String baseConfigPath = "${project.ext.CONFIG_DIR}/application.yml"
        File baseConfigFile = project.file(baseConfigPath)

        // 如果基础配置文件不存在，返回空配置并记录警告
        if (!baseConfigFile.exists()) {
            project.logger.warn("Base configuration file not found: ${baseConfigPath}")
            Map<String, Object> emptyConfig = [:]
            configCache[cacheKey] = emptyConfig
            return emptyConfig
        }

        try {
            Map<String, Object> baseConfig = readYamlConfig(baseConfigPath) as Map<String, Object>
            configCache[cacheKey] = baseConfig
            project.logger.debug("Loaded base configuration from: ${baseConfigPath}")
            return baseConfig
        } catch (Exception e) {
            project.logger.warn("Failed to load base configuration, using empty config: ${e.message}")
            Map<String, Object> emptyConfig = [:]
            configCache[cacheKey] = emptyConfig
            return emptyConfig
        }
    }

    /**
     * 获取环境特定配置（私有方法，原getConfig方法）
     */
    private Map<String, Object> getProfileConfig(String profileName) {
        String configPath = "${project.ext.CONFIG_DIR}/application-${profileName}.yml"
        return readYamlConfig(configPath) as Map<String, Object>
    }

    /**
     * 获取完整配置（基础配置 + 环境配置合并）
     */
    Map<String, Object> getConfig(String profileName) {
        // 检查合并配置的缓存
        String cacheKey = "merged_${profileName}_processed"
        if (configCache.containsKey(cacheKey)) {
            return configCache[cacheKey] as Map<String, Object>
        }

        try {
            // 加载基础配置
            Map<String, Object> baseConfig = getBaseConfig()
            project.logger.info("Base configuration loaded for profile merging: ${profileName}")

            // 加载环境特定配置
            Map<String, Object> profileConfig = getProfileConfig(profileName)
            project.logger.info("Profile configuration loaded: ${profileName}")

            // 合并配置：基础配置作为底层，环境配置覆盖基础配置
            Map<String, Object> mergedConfig = deepMergeConfigs(baseConfig, profileConfig)

            // 缓存合并后的配置
            configCache[cacheKey] = mergedConfig
            project.logger.info("Configuration merged and cached for profile: ${profileName}")

            return mergedConfig
        } catch (Exception e) {
            project.logger.error("Failed to merge configuration for profile ${profileName}: ${e.message}")
            // 如果合并失败，尝试返回环境配置
            try {
                return getProfileConfig(profileName)
            } catch (Exception fallbackError) {
                handleError("Failed to load any configuration for profile: ${profileName}", fallbackError)
                return [:]
            }
        }
    }

    /**
     * 扫描可用环境配置
     */
    List<String> scanAvailableProfiles() {
        File configDir = project.file(project.ext.CONFIG_DIR)
        List<String> profiles = []

        if (!configDir.exists() || !configDir.isDirectory()) {
            handleError("Configuration directory not found: ${project.ext.CONFIG_DIR}")
        }

        configDir.listFiles()?.each { File configFile ->
            if (isValidConfigFile(configFile)) {
                String profileName = extractProfileName(configFile.name)
                if (profileName) {
                    profiles << profileName
                }
            }
        }

        if (profiles.isEmpty()) {
            handleError("No environment configuration files found in ${project.ext.CONFIG_DIR}/. Please create application-{profile}.yml files.")
        }

        return profiles.sort()
    }

    /**
     * 检查是否为有效的配置文件
     */
    private static boolean isValidConfigFile(File file) {
        return file.name.startsWith('application-') && file.name.endsWith('.yml')
    }

    /**
     * 从文件名提取环境名
     */
    private static String extractProfileName(String fileName) {
        String prefix = 'application-'
        String suffix = '.yml'

        if (fileName.length() <= prefix.length() + suffix.length()) {
            return null
        }

        String profileName = fileName.substring(prefix.length(), fileName.length() - suffix.length())
        return profileName?.trim() ?: null
    }

    /**
     * 加载所有环境配置（使用私有方法getProfileConfig）
     */
    Map<String, Object> loadAllProfileConfigs(List<String> profiles) {
        Map<String, Object> configs = [:]

        profiles.each { String profileName ->
            try {
                configs[profileName] = getProfileConfig(profileName)
                project.logger.info("Loaded configuration for profile: ${profileName}")
            } catch (Exception e) {
                project.logger.warn("Failed to load configuration for profile ${profileName}: ${e.message}")
            }
        }

        return configs
    }

    /**
     * 深度合并配置（优化版本）
     */
    static Map<String, Object> deepMergeConfigs(Map<String, Object> target, Map<String, Object> source) {
        if (!target) return new HashMap(source ?: [:])
        if (!source) return new HashMap(target)

        Map<String, Object> result = new HashMap(target)

        source.each { String key, Object value ->
            if (value instanceof Map && result[key] instanceof Map) {
                result[key] = deepMergeConfigs(result[key] as Map<String, Object>, value as Map<String, Object>)
            } else {
                result[key] = value
            }
        }

        return result
    }

    /**
     * 获取合并后的激活配置
     */
    Map<String, Object> getMergedActiveConfig(List<String> profileList, Map<String, Object> allConfigs) {
        Map<String, Object> mergedConfig = [:]

        profileList.each { String profileName ->
            Object currentConfig = allConfigs[profileName]
            if (currentConfig) {
                mergedConfig = deepMergeConfigs(mergedConfig, currentConfig as Map<String, Object>)
                project.logger.info("Merged configuration from profile: ${profileName}")
            } else {
                project.logger.warn("Configuration not found for profile '${profileName}', skipping...")
            }
        }

        // 使用默认配置作为后备
        if (mergedConfig.isEmpty() && !allConfigs.isEmpty()) {
            String firstProfile = allConfigs.keySet().iterator().next()
            mergedConfig = allConfigs[firstProfile] as Map<String, Object>
            project.logger.info("Using default configuration from profile: ${firstProfile}")
        }

        return mergedConfig
    }

    /**
     * 验证环境配置有效性
     */
    static void validateProfiles(List<String> requestedProfiles, List<String> availableProfiles) {
        requestedProfiles.each { String profile ->
            if (!availableProfiles.contains(profile)) {
                handleError("Invalid profile: ${profile}. Available profiles are: ${availableProfiles}")
            }
        }
    }
}
