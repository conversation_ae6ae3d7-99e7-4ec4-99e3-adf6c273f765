import org.gradle.api.Project

class TokenReplacer {
    private final Project project
    private final String activeProfile
    private final Map<String, Object> activeConfig
    private final Map<String, String> tokenMap

    TokenReplacer(Project project, String activeProfile, Map<String, Object> activeConfig = null) {
        this.project = project
        this.activeProfile = activeProfile
        this.activeConfig = activeConfig
        this.tokenMap = createTokenMap()
    }

    /**
     * 创建Token映射表
     */
    private Map<String, String> createTokenMap() {
        Map<String, String> tokens = [:]

        try {
            // 基础项目信息
            tokens['projectName'] = project.rootProject.name

            // 环境配置信息
            tokens['profileName'] = activeProfile

            // 获取本地IP地址
            tokens['localIpAddress'] = InetAddress.getLocalHost().hostAddress

            // 从配置中获取服务端口
            if (activeConfig && activeConfig['server'] && activeConfig['server']['port']) {
                tokens['serverPort'] = activeConfig['server']['port'].toString()
                project.logger.info("Token mapping created: localIpAddress=${tokens['localIpAddress']}, profileName=${tokens['profileName']}, serverPort=${tokens['serverPort']}")
            } else {
                project.logger.info("Token mapping created: localIpAddress=${tokens['localIpAddress']}, profileName=${tokens['profileName']}")
                project.logger.debug("serverPort not available in current context")
            }

        } catch (Exception e) {
            project.logger.warn("Failed to resolve some tokens: ${e.message}")
            // 设置默认值
            tokens['localIpAddress'] = 'localhost'
            tokens['profileName'] = activeProfile ?: 'dev'
            if (activeConfig && activeConfig['server'] && activeConfig['server']['port']) {
                tokens['serverPort'] = activeConfig['server']['port'].toString()
            }
        }

        return tokens
    }

    /**
     * 替换字符串中的所有token
     */
    String replaceTokens(String text) {
        if (!text) return text

        String result = text
        tokenMap.each { String token, String value ->
            result = result.replace("@${token}@", value)
        }

        return result
    }

    /**
     * 递归替换Map中的所有token
     */
    Object replaceTokensInObject(Object obj) {
        if (obj instanceof String) {
            return replaceTokens(obj)
        } else if (obj instanceof Map<String, Object>) {
            Map<String, Object> result = [:]
            obj.each { key, value ->
                result[key] = replaceTokensInObject(value)
            }
            return result
        } else if (obj instanceof List) {
            return obj.collect { replaceTokensInObject(it) }
        } else {
            return obj
        }
    }

    /**
     * 获取token映射表（用于调试）
     */
    Map<String, String> getTokenMap() {
        return new HashMap(tokenMap)
    }
}
