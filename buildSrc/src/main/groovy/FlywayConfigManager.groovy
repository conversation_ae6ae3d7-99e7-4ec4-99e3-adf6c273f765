import org.gradle.api.GradleException
import org.gradle.api.Project
import org.gradle.api.tasks.Console

class FlywayConfigManager {
    private final Project project
    private final ConfigManager configManager

    // Flyway运行时配置缓存
    private static String flywayProfile = null
    private static Map<String, Object> flywayDatasource = null

    FlywayConfigManager(Project project, ConfigManager configManager) {
        this.project = project
        this.configManager = configManager
    }

    /**
     * 交互式选择Flyway运行环境
     */
    String selectEnvironmentInteractively(List<String> availableProfiles) {
        print "Enter environment (${availableProfiles.join('/')}): "
        System.out.flush()

        String input = readUserInput()

        if (!input || !availableProfiles.contains(input)) {
            println "\nAvailable environments: ${availableProfiles.join(', ')}"
            throw new GradleException("Invalid environment: '${input}'. Available: ${availableProfiles.join(', ')}")
        }

        return input
    }

    /**
     * 读取用户输入（支持多种环境）
     */
    private String readUserInput() {
        Console console = System.console() as Console
        if (console != null) {
            return console.readLine()?.trim()?.toLowerCase()
        }

        // 在Windows gradlew.bat或IDE环境下的备选方案
        try {
            BufferedReader reader = new BufferedReader(new InputStreamReader(System.in))
            return reader.readLine()?.trim()?.toLowerCase()
        } catch (Exception ignored) {
            println '\nWarning: Unable to read input in current environment.'
            println 'Please specify profile using -Pprofile=<env> parameter'
            println "Example: gradlew flywayInfo -Pprofile=${project.ext.availableProfiles["0"]}"
            throw new GradleException('Interactive input not available. Use -Pprofile=<env> parameter instead.')
        }
    }

    /**
     * 确认生产环境操作
     */
    static void confirmProductionOperation(String profileName, String taskName) {
        if (profileName == 'prod' && taskName == 'flywayMigrate') {
            println 'WARNING: You are about to modify the PRODUCTION database!'
            println 'This operation will modify the production database structure and data.'
            print "Are you sure you want to continue? (type 'YES'): "

            String input = System.console()?.readLine()
            if (input != 'YES') {
                throw new GradleException('Production database migration cancelled.')
            }

            println 'Confirmation received. Starting production database migration...'
            println ''
        }
    }

    /**
     * 打印Flyway配置信息
     */
    void printFlywayInfo(String profileName, Map<String, Object> datasource) {
        println ''
        println '=========================================='
        println 'Flyway Database Migration'
        println '=========================================='
        println "Environment: ${profileName}"
        println "Database URL: ${datasource['url']}"
        println "Username: ${datasource['username']}"
        println 'Migration scripts location: sql/flyway/migration'
        println "Available environments: ${project.ext.availableProfiles.join(', ')}"
        println '=========================================='
        println ''
    }

    /**
     * 静态方法：配置Flyway任务的核心逻辑
     */
    static void configureFlywayTask(Project project, ConfigManager configManager, List<String> availableProfiles, String taskName) {
        // 环境选择逻辑
        if (flywayProfile == null) {
            if (project.hasProperty('profile')) {
                String specifiedProfile = project.property('profile').toString().toLowerCase()
                configManager.validateProfiles([specifiedProfile], availableProfiles)
                flywayProfile = specifiedProfile
                println "Using flyway profile from command line: ${flywayProfile}"
            } else {
                FlywayConfigManager tempManager = new FlywayConfigManager(project, configManager)
                flywayProfile = tempManager.selectEnvironmentInteractively(availableProfiles)
            }

            // 使用ConfigManager获取完整配置
            Map<String, Object> config = configManager.getConfig(flywayProfile)

            // 获取数据源配置
            Map<String, Object> spring = config['spring'] as Map<String, Object>
            if (!spring) {
                throw new GradleException("Spring configuration not found for profile: ${flywayProfile}")
            }

            flywayDatasource = spring['datasource'] as Map<String, Object>
            if (!flywayDatasource) {
                throw new GradleException("Datasource configuration not found for profile: ${flywayProfile}")
            }

            // 获取flyway配置
            Map<String, Object> flywayConfig = spring['flyway'] as Map<String, Object>
            if (!flywayConfig) {
                throw new GradleException("Flyway configuration not found for profile: ${flywayProfile}")
            }

            // 动态设置所有flyway属性
            project['flyway']['url'] = flywayDatasource['url']
            project['flyway']['user'] = flywayDatasource['username']
            project['flyway']['password'] = flywayDatasource['password']
            project['flyway']['driver'] = flywayDatasource['driver-class-name']

            // 设置flyway基础配置
            def locationsConfig = flywayConfig['locations']
            project['flyway']['locations'] = locationsConfig instanceof String ? [locationsConfig] : locationsConfig
            project['flyway']['baselineOnMigrate'] = flywayConfig['baseline-on-migrate']
            project['flyway']['validateOnMigrate'] = flywayConfig['validate-on-migrate']
            project['flyway']['encoding'] = flywayConfig['encoding']
            project['flyway']['baselineVersion'] = flywayConfig['baseline-version']
            project['flyway']['baselineDescription'] = flywayConfig['baseline-description']

            project.logger.info("Flyway configuration loaded dynamically for profile: ${flywayProfile}")
        }

        // 创建临时管理器实例用于调用非静态方法
        FlywayConfigManager tempManager = new FlywayConfigManager(project, configManager)

        // 打印配置信息
        tempManager.printFlywayInfo(flywayProfile, flywayDatasource)

        // 生产环境安全确认
        confirmProductionOperation(flywayProfile, taskName)
    }
}
