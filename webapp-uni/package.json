{"name": "webapp-vue", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=18", "pnpm": ">=8"}, "scripts": {"dev": "npm run dev:mp-weixin --mode development", "build": "npm run build:mp-weixin", "gitupdate": "git pull origin", "前端生产模式打包": "", "fprod": " npm run gitupdate && npm run build", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-weixin": "uni -p mp-weixin", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-weixin": "uni build -p mp-weixin"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4030620241128001", "@dcloudio/uni-app-plus": "3.0.0-4030620241128001", "@dcloudio/uni-components": "3.0.0-4030620241128001", "@dcloudio/uni-h5": "3.0.0-4030620241128001", "@dcloudio/uni-mp-weixin": "3.0.0-4030620241128001", "axios": "^1.8.3", "fast-glob": "^3.3.3", "lib-flexible": "^0.3.2", "nprogress": "^0.2.0", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "4.1.3", "showdown": "^2.1.0", "uview-plus": "^3.4.28", "vue": "^3.4.21", "vue-i18n": "9.14.4", "z-paging": "^2.8.4"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4030620241128001", "@dcloudio/uni-cli-shared": "3.0.0-4030620241128001", "@dcloudio/uni-stacktracey": "3.0.0-4030620241128001", "@dcloudio/vite-plugin-uni": "3.0.0-4030620241128001", "@rollup/plugin-strip": "^3.0.4", "@types/node": "^22.15.17", "@types/nprogress": "^0.2.3", "@types/postcss-pxtorem": "^6.1.0", "@types/showdown": "^2.0.6", "@uni-helper/uni-app-types": "1.0.0-alpha.6", "@unocss/eslint-plugin": "^0.63.6", "@unocss/preset-icons": "^0.63.6", "@vue/runtime-core": "^3.4.21", "@vue/tsconfig": "^0.7.0", "miniprogram-api-typings": "^4.0.7", "naive-ui": "^2.41.0", "postcss-pxtorem": "^6.1.0", "postcss-pxtransform": "^4.1.3-alpha.0", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.79.6", "sass-loader": "^16.0.4", "typescript": "~5.7.2", "unocss": "0.63.6", "unocss-preset-weapp": "^66.0.1", "unplugin-auto-import": "0.19.0", "unplugin-vue-components": "^28.5.0", "vite": "5.2.8", "vite-plugin-clean-build": "^1.4.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-restart": "^0.4.2", "vue-tsc": "^2.2.8"}}