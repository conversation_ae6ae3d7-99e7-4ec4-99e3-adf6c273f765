/// <reference types="vite/client" />
declare module "*.vue" {
  import { DefineComponent } from "vue";
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

declare interface Uni {
  $u: any;
}
interface ImportMetaEnv {
  readonly VITE_APP_ENV: string;
  readonly VITE_APP_TITLE: string;
  readonly VITE_API_BASE_URL: string;
  readonly VITE_APP_PORT: string;
  readonly VITE_APP_PROXY: string;
  readonly VITE_APP_API_PREFIX: string;
  readonly VITE_APP_DROP_CONSOLE: string;
}
