/**
 * 保存通知反馈信息请求参数
 */
export interface NoticeFeedback {
  /** 关联通知反馈信息的唯一标识（新增时可不传，后端会生成） */
  id?: string;
  /** 关联车主 ID */
  personnelId?: string;
  /** 车主姓名（仅列表查询时返回） */
  name?: string;
  /** 车主职位（仅列表查询时返回） */
  position?: string;
  /** 车主联系电话（仅列表查询时返回） */
  phone?: string;
  /** 关联车辆 ID */
  vehicleInfoId?: string;
  /** 车牌号（仅列表查询时返回） */
  licensePlateNumber?: string;
  /** 通知方式 */
  noticeMethod?: string;
  /** 通知时间，如：2023-10-03 09:15:00 */
  noticeTime?: string;
  /** 车主反馈内容 */
  feedbackContent?: string;
}

export interface NoticefeedbackSearch {
  /** 当前页码 */
  currentPage?: number;
  /** 每页记录数 */
  pageRecord?: number;
  /** 查询开始时间（例如：`2025-05-17T00:00:00Z`） */
  startTime?: string;
  /** 查询结束时间 */
  endTime?: string;
  /** 分片标识 */
  sharding?: string;
  /** 用户 ID */
  userId?: string;
  /** 是否分页 */
  ifPage?: boolean;
  /** 角色编码 */
  roleCode?: string;
}
