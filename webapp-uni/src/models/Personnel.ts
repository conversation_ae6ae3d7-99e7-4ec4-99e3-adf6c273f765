import type { Personnel } from './MettingHandle';
import { PageSearch } from './Page';

export class PersonnelSearch extends PageSearch {
  /** 部门 */
  department?: string;
  /** 人员类型(0-内部成员,1-外部成员) */
  type?: number;
  //   是否加载车辆信息
  loadVehicles?: false;
  /**
   * 是否排除当前登录用户(临时使用固定发起人ID)
   */
  excludeCurrentUser?: boolean;
  /**
   * 是否按部门分组返回结果
   */
  // groupByDepartment?: boolean;
  groupByOrganization?: boolean;
  /** 是否按工作组分组 */
  groupByWorkgroup?: boolean;
  /** 是否返回分组结果 */
  ifGrouped?: boolean;
  constructor() {
    super();
  }
}
/** 按部门分组返回的人员结果 */
export interface PersonnelAndOrganization {
  /** 部门名称 */
  organizationName?: string;
  /** 工作组名称 */
  workgroupName?: string;
  /** 人员 */
  members?: Personnel[];
  _uuid?: string;
  _open?: boolean;
  _isChecked?: boolean;
}
export interface PersonnelAndOrganization2 {
  /** 按组织机构分组的结果 */
  organizationGroupList?: PersonnelAndOrganization[];
  /** 按工作组分组的结果 */
  workgroupGroupList?: PersonnelAndOrganization[];
  /** 部门名称 */
  externalPersonnelList?: Personnel[];
  _uuid?: string;
  _open?: boolean;
  _isChecked?: boolean;
}
