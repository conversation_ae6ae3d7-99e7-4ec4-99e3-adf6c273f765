<template>
    <div class="sl-mr-container">
        <svg  xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1"
            width="291" height="146.999755859375" viewBox="0 0 291 146.999755859375" @click.self="clearSelection">
            <defs>
                <clipPath id="master_svg0_102_01460">
                    <rect x="128.5" y="124.5" width="6" height="6" rx="0" />
                </clipPath>
                <clipPath id="master_svg1_102_01774">
                    <rect x="247.5" y="73.5" width="6" height="6" rx="0" />
                </clipPath>
                <clipPath id="master_svg0_53_8760">
                    <rect x="199.5" y="124.5" width="6" height="6" rx="0" />
                </clipPath>
                <clipPath id="master_svg0_32_03912">
                    <rect x="0" y="0" width="16" height="16" rx="0" />
                </clipPath>
                <linearGradient x1="0.4583958089351654" y1="-0.1322835236787796" x2="1.2632941989403146"
                    y2="0.5322777142255726" id="master_svg1_1_5010">
                    <stop offset="0%" stop-color="#3E73FF" stop-opacity="1" />
                    <stop offset="100%" stop-color="#3EF9FF" stop-opacity="1" />
                </linearGradient>
                <filter id="master_svg2_32_03912/1_08328" filterUnits="objectBoundingBox"
                    color-interpolation-filters="sRGB" x="-0.6666666666666666" y="-0.5" width="2.3333333333333335"
                    height="2">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
                    <feOffset dy="0" dx="0" />
                    <feGaussianBlur stdDeviation="1" />
                    <feColorMatrix type="matrix"
                        values="0 0 0 0 0.23893804848194122 0 0 0 0 1 0 0 0 0 0.9238938093185425 0 0 0 0.30000001192092896 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape" />
                </filter>
                <linearGradient x1="0.8333751559257507" y1="0.7578933238983154" x2="0.4687796567421075"
                    y2="-0.052560190527749434" id="master_svg3_1_5013">
                    <stop offset="0%" stop-color="#3E5BFF" stop-opacity="1" />
                    <stop offset="100%" stop-color="#3EFFFF" stop-opacity="1" />
                </linearGradient>
            </defs>
            <g>
                <g>
                    <rect x="0.5" y="0.5" width="21" height="66" rx="0" fill="#E4E4E4" fill-opacity="1" />
                    <rect x="0.6500000059604645" y="0.6500000059604645" width="20.69999998807907"
                        height="65.69999998807907" rx="0" fill-opacity="0" stroke-opacity="1" stroke="#999999"
                        fill="none" stroke-width="0.30000001192092896" />
                </g>
                <g transform="matrix(1,0,0,-1,0,147)">
                    <path
                        d="M14.4942,80.8C14.4981,80.70041,14.5,80.60041,14.5,80.5L8,80.5L8,87.5C8.10045,87.5,8.20045,87.4976,8.3,87.4928C9.05475,87.4562,9.7839,87.281,10.48744,86.9672C11.28378,86.6119,11.9867,86.1061,12.5962,85.4497Q13.5104,84.4652,14.0052,83.17878C14.2994,82.41396,14.4624,81.62103,14.4942,80.8ZM14.194,80.8Q14.1465,81.97581,13.7252,83.07109Q13.2512,84.3035,12.3764,85.2456Q11.5038,86.1853,10.36523,86.6932Q9.36836,87.1379,8.3,87.19239999999999L8.3,80.8L14.194,80.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M14.4942,81.8C14.4981,81.70041,14.5,81.60041,14.5,81.5L8,81.5L8,88.5C8.10045,88.5,8.20045,88.4976,8.3,88.4928C9.05475,88.4562,9.7839,88.281,10.48744,87.9672C11.28378,87.6119,11.9867,87.1061,12.5962,86.4497Q13.5104,85.4652,14.0052,84.17878C14.2994,83.41396,14.4624,82.62103,14.4942,81.8ZM14.194,81.8Q14.1465,82.97581,13.7252,84.07109Q13.2512,85.3035,12.3764,86.2456Q11.5038,87.1853,10.36523,87.6932Q9.36836,88.1379,8.3,88.19239999999999L8.3,81.8L14.194,81.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path d="M0.5,81.5L0.5,121.5L32,121.5L32,117.4755L55.5,117.4755L55.5,81.5L0.5,81.5Z" fill="#E4E4E4"
                        fill-opacity="1" />
                    <path
                        d="M55.5,117.4755L32,117.4755L32,121.5L0.5,121.5L0.5,81.5L55.5,81.5L55.5,117.4755ZM31.7,117.1755L31.7,121.2L0.8,121.2L0.8,81.8L55.2,81.8L55.2,117.1755L31.7,117.1755Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g transform="matrix(-1,0,0,1,121,0)">
                    <path
                        d="M73.4942,81.8C73.4981,81.70041,73.5,81.60041,73.5,81.5L67,81.5L67,88.5C67.10045,88.5,67.20045,88.4976,67.3,88.4928C68.05475,88.4562,68.7839,88.281,69.48743999999999,87.9672C70.28378000000001,87.6119,70.9867,87.1061,71.5962,86.4497Q72.5104,85.4652,73.0052,84.17878C73.2994,83.41396,73.4624,82.62103,73.4942,81.8ZM73.194,81.8Q73.1465,82.97581,72.7252,84.07109Q72.2512,85.3035,71.3764,86.2456Q70.5038,87.1853,69.36523,87.6932Q68.36836,88.1379,67.3,88.19239999999999L67.3,81.8L73.194,81.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M13.4942,81.8C13.4981,81.70041,13.5,81.60041,13.5,81.5L7,81.5L7,88.5C7.10045,88.5,7.20045,88.4976,7.3,88.4928C8.05475,88.4562,8.7839,88.281,9.48744,87.9672C10.28378,87.6119,10.9867,87.1061,11.5962,86.4497Q12.5104,85.4652,13.0052,84.17878C13.2994,83.41396,13.4624,82.62103,13.4942,81.8ZM13.194,81.8Q13.1465,82.97581,12.7252,84.07109Q12.2512,85.3035,11.3764,86.2456Q10.5038,87.1853,9.36523,87.6932Q8.36836,88.1379,7.3,88.19239999999999L7.3,81.8L13.194,81.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g transform="matrix(1,0,0,-1,0,267)">
                    <path d="M29.5,145.4998L32,145.4998L32,149.5L55.5,149.5L55.5,133.5L29.5,133.5L29.5,145.4998Z"
                        fill="#E4E4E4" fill-opacity="1" />
                    <path
                        d="M32,149.5L32,145.4998L29.5,145.4998L29.5,133.5L55.5,133.5L55.5,149.5L32,149.5ZM32.3,145.1998L29.8,145.1998L29.8,133.8L55.2,133.8L55.2,149.2L32.3,149.2L32.3,145.1998Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g transform="matrix(-1,0,0,1,121,0)">
                    <path
                        d="M73.4942,117.8C73.4981,117.70041,73.5,117.60041,73.5,117.5L67,117.5L67,124.5C67.10045,124.5,67.20045,124.4976,67.3,124.4928C68.05475,124.4562,68.7839,124.281,69.48743999999999,123.9672C70.28378000000001,123.6119,70.9867,123.1061,71.5962,122.4497Q72.5104,121.4652,73.0052,120.17878C73.2994,119.41396,73.4624,118.62103,73.4942,117.8ZM73.194,117.8Q73.1465,118.97581,72.7252,120.07109Q72.2512,121.3035,71.3764,122.2456Q70.5038,123.1853,69.36523,123.6932Q68.36836,124.1379,67.3,124.19239999999999L67.3,117.8L73.194,117.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <rect x="21.5" y="0.5" width="21" height="66" rx="0" fill="#E4E4E4" fill-opacity="1" />
                    <rect x="21.650000005960464" y="0.6500000059604645" width="20.69999998807907"
                        height="65.69999998807907" rx="0" fill-opacity="0" stroke-opacity="1" stroke="#999999"
                        fill="none" stroke-width="0.30000001192092896" />
                </g>
                <g transform="matrix(-1,0,0,-1,87,147)">
                    <path
                        d="M56.4942,80.8C56.4981,80.70041,56.5,80.60041,56.5,80.5L50,80.5L50,87.5C50.10045,87.5,50.200450000000004,87.4976,50.3,87.4928C51.05475,87.4562,51.7839,87.281,52.48744,86.9672C53.28378,86.6119,53.9867,86.1061,54.596199999999996,85.4497Q55.510400000000004,84.4652,56.0052,83.17878C56.2994,82.41396,56.4624,81.62103,56.4942,80.8ZM56.194,80.8Q56.1465,81.97581,55.7252,83.07109Q55.2512,84.3035,54.376400000000004,85.2456Q53.5038,86.1853,52.36523,86.6932Q51.36836,87.1379,50.3,87.19239999999999L50.3,80.8L56.194,80.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M96.5,4.5L96.5,11.61475L99.02688,11.61475L99.02688,58.3689L114.7098,58.3689L114.7098,66.5L168.7688,66.5L168.7688,52.7787L190.5,52.7787L190.5,4.5L168.7686,4.5L96.5,4.5Z"
                        fill="#E4E4E4" fill-opacity="1" />
                    <path
                        d="M190.5,52.7787L168.7688,52.7787L168.7688,66.5L114.7098,66.5L114.7098,58.3689L99.02688,58.3689L99.02688,11.61475L96.5,11.61475L96.5,4.5L190.5,4.5L190.5,52.7787ZM168.4688,52.4787L168.4688,66.2L115.0098,66.2L115.0098,58.0689L99.32688,58.0689L99.32688,11.31475L96.8,11.31475L96.8,4.8L168.61860000000001,4.8L168.6188,52.4787L168.4688,52.4787ZM190.2,52.4787L168.9188,52.4787L168.9186,4.8L190.2,4.8L190.2,52.4787Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g transform="matrix(0,-1,1,0,76,209)">
                    <line x1="142.5" y1="66.34999999403954" x2="204.5" y2="66.34999999403954" fill-opacity="0"
                        stroke-opacity="1" stroke="#999999" fill="none" stroke-width="0.30000001192092896" />
                </g>
                <g>
                    <path
                        d="M231.7255859375,134.1465L259.4999859375,134.1465L259.4999859375,119.5L231.7255859375,119.5L231.7255859375,134.1465Z"
                        fill="#FFFFFF" fill-opacity="1" />
                    <path
                        d="M231.7255859375,134.1465L259.4999859375,134.1465L259.4999859375,119.5L231.7255859375,119.5L231.7255859375,134.1465ZM232.0255859375,119.8L259.1999859375,119.8L259.1999859375,133.8465L232.0255859375,133.8465L232.0255859375,119.8Z"
                        fill-rule="evenodd" fill="#000000" fill-opacity="1" />
                </g>
                <g transform="matrix(-1,0,0,1,525,0)">
                    <path
                        d="M275.4942,119.8C275.4981,119.70041,275.5,119.60041,275.5,119.5L269,119.5L269,126.5C269.10045,126.5,269.20045,126.4976,269.3,126.4928C270.05475,126.4562,270.7839,126.281,271.48744,125.9672C272.28378,125.6119,272.9867,125.1061,273.5962,124.4497Q274.5104,123.4652,275.0052,122.17878C275.2994,121.41396,275.4624,120.62103,275.4942,119.8ZM275.194,119.8Q275.1465,120.97581,274.7252,122.07109Q274.2512,123.3035,273.3764,124.2456Q272.5038,125.1853,271.36523,125.6932Q270.36836,126.1379,269.3,126.19239999999999L269.3,119.8L275.194,119.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path d="M61.5,66.5L80.5,66.5L80.5,60.5L61.5,60.5L61.5,66.5Z" fill="#E4E4E4" fill-opacity="1" />
                    <path
                        d="M61.5,66.5L80.5,66.5L80.5,60.5L61.5,60.5L61.5,66.5ZM61.8,60.8L80.2,60.8L80.2,66.2L61.8,66.2L61.8,60.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M98.80000305175781,66.4000015258789L114.80000305175781,66.4000015258789L114.80000305175781,58.400001525878906L98.80000305175781,58.400001525878906L98.80000305175781,66.4000015258789Z"
                        fill="#E4E4E4" fill-opacity="1" />
                    <path
                        d="M98.80000305175781,66.4000015258789L114.80000305175781,66.4000015258789L114.80000305175781,58.400001525878906L98.80000305175781,58.400001525878906L98.80000305175781,66.4000015258789ZM99.10000305175781,58.7000015258789L114.50000305175782,58.7000015258789L114.50000305175782,66.10000152587891L99.10000305175781,66.10000152587891L99.10000305175781,58.7000015258789Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path d="M168.5,63.5L190.5,63.5L190.5,52.5L168.5,52.5L168.5,63.5Z" fill="#E4E4E4"
                        fill-opacity="1" />
                    <path
                        d="M168.5,63.5L190.5,63.5L190.5,52.5L168.5,52.5L168.5,63.5ZM168.8,52.8L190.2,52.8L190.2,63.2L168.8,63.2L168.8,52.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g transform="matrix(0,1,-1,0,142,-19)">
                    <g
                        transform="matrix(0,-0.9999452829360962,0.9999452829360962,0,-0.9955405592918396,161.99559527635574)">
                        <path
                            d="M90.48988,86.8C90.49572,86.70058,90.49864,86.60058,90.49864,86.5L85.49932,86.5L85.49932,91.5C85.5999,91.5,85.6999,91.49708,85.79932,91.49124C86.3552,91.45858,86.89291,91.33464000000001,87.41248,91.1194C88.02496,90.86566,88.5656,90.50438,89.03437,90.03553Q89.73754,89.33227,90.11809,88.41342C90.3333,87.89377,90.45724,87.35597,90.48988,86.8ZM90.18932,86.8Q90.14089,87.57433,89.84092,88.29863Q89.4832,89.16235,88.82223,89.82342Q88.16126,90.48448,87.29766,90.84224Q86.57351,91.14223,85.79932,91.19067L85.79932,86.8L90.18932,86.8Z"
                            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                    </g>
                    <g
                        transform="matrix(0,0.9999452829360962,0.9999452829360962,0,19.003365099430084,-18.995595276355743)">
                        <path
                            d="M90.48934,66.79973Q90.49809,66.65059,90.49809,66.49973L85.49905,66.49973L85.49905,71.49945C85.59963,71.49945,85.69963,71.49653,85.79905,71.49069C86.35489,71.45804,86.89257,71.3341,87.4121,71.11887Q88.33078,70.73829,89.03391,70.03507Q89.73703,69.33184,90.11756,68.41304Q90.44036,67.63362,90.48934,66.79973ZM90.18877,66.79973Q90.14034,67.57401,89.84039,68.29825Q89.4827,69.16192,88.82176,69.82295Q88.16083,70.48398,87.29728,70.84171Q86.57318,71.14169,85.79905,71.19013L85.79905,66.79973L90.18877,66.79973Z"
                            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                    </g>
                </g>
                <g transform="matrix(0,1,-1,0,177,-54)">
                    <g
                        transform="matrix(0,-0.9999452829360962,0.9999452829360962,0,34.00445944070816,196.9936801791191)">
                        <path
                            d="M125.48988,86.8C125.49572,86.70058,125.49864,86.60058,125.49864,86.5L120.49932,86.5L120.49932,91.5C120.5999,91.5,120.6999,91.49708,120.79932,91.49124C121.3552,91.45858,121.89291,91.33464000000001,122.41248,91.1194C123.02496,90.86566,123.5656,90.50438,124.03437,90.03553Q124.73754,89.33227,125.11809,88.41342C125.3333,87.89377,125.45724,87.35597,125.48988,86.8ZM125.18932,86.8Q125.14089,87.57433,124.84092,88.29863Q124.4832,89.16235,123.82223,89.82342Q123.16126,90.48448,122.29766,90.84224Q121.57351,91.14223,120.79932,91.19067L120.79932,86.8L125.18932,86.8Z"
                            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                    </g>
                    <g
                        transform="matrix(0,0.9999452829360962,0.9999452829360962,0,54.003365099430084,-53.99368017911911)">
                        <path
                            d="M125.48934,66.79973Q125.49809,66.65059,125.49809,66.49973L120.49905,66.49973L120.49905,71.49945C120.59963,71.49945,120.69963,71.49653,120.79905,71.49069C121.35489,71.45804,121.89257,71.3341,122.4121,71.11887Q123.33078,70.73829,124.03391,70.03507Q124.73703,69.33184,125.11756,68.41304Q125.44036,67.63362,125.48934,66.79973ZM125.18877,66.79973Q125.14034,67.57401,124.84039,68.29825Q124.4827,69.16192,123.82176,69.82295Q123.16083,70.48398,122.29728,70.84171Q121.57318,71.14169,120.79905,71.19013L120.79905,66.79973L125.18877,66.79973Z"
                            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                    </g>
                </g>
                <g transform="matrix(0,1,-1,0,251,-134)">
                    <g
                        transform="matrix(0,-0.9999452829360962,0.9999452829360962,0,114.00429528951645,270.9894669651985)">
                        <path
                            d="M202.48988,83.8C202.49572,83.70058,202.49864,83.60058,202.49864,83.5L197.49932,83.5L197.49932,88.5C197.5999,88.5,197.6999,88.49708,197.79932,88.49124C198.3552,88.45858,198.89291,88.33464000000001,199.41248,88.1194C200.02496,87.86566,200.5656,87.50438,201.03437,87.03553Q201.73754,86.33227,202.11809,85.41342C202.3333,84.89377,202.45724,84.35597,202.48988,83.8ZM202.18932,83.8Q202.14089,84.57433,201.84092,85.29863Q201.4832,86.16235,200.82223,86.82342Q200.16126,87.48448,199.29766,87.84224Q198.57351,88.14223,197.79932,88.19067L197.79932,83.8L202.18932,83.8Z"
                            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                    </g>
                    <g
                        transform="matrix(0,0.9999452829360962,0.9999452829360962,0,134.00320094823837,-133.98946696519852)">
                        <path
                            d="M202.48934,63.79973Q202.49809,63.65059,202.49809,63.49973L197.49905,63.49973L197.49905,68.49945C197.59963,68.49945,197.69963,68.49653,197.79905,68.49069C198.35489,68.45804,198.89257,68.3341,199.4121,68.11887Q200.33078,67.73829,201.03391,67.03507Q201.73703,66.33184,202.11756,65.41304Q202.44036,64.63362,202.48934,63.79973ZM202.18877,63.79973Q202.14034,64.57401,201.84039,65.29825Q201.4827,66.16192,200.82176,66.82295Q200.16083,67.48398,199.29728,67.84171Q198.57318,68.14169,197.79905,68.19013L197.79905,63.79973L202.18877,63.79973Z"
                            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                    </g>
                </g>
                <g>
                    <path d="M190.5,22.5L205.5,22.5L205.5,4.5L190.5,4.5L190.5,22.5Z" fill="#E4E4E4" fill-opacity="1" />
                    <path
                        d="M190.5,22.5L205.5,22.5L205.5,4.5L190.5,4.5L190.5,22.5ZM190.8,4.8L205.2,4.8L205.2,22.2L190.8,22.2L190.8,4.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g id="room-1301" class="meeting-room" @click.stop="selectRoom('1301')"
                    :class="{ selected: selected === '1301' }">
                    <path
                        d="M225.5,81.67L231.59375,81.67L231.74352,119.673L259.0156,119.673L259.0156,125.5L290.5,125.5L290.5,0.5L225.5,0.5L225.5,66.0242L225.5,81.67Z"
                        fill="#FFFFFF" fill-opacity="1" />
                    <path
                        d="M259.0156,125.5L259.0156,119.673L231.74352,119.673L231.59375,81.67L225.5,81.67L225.5,0.5L290.5,0.5L290.5,125.5L259.0156,125.5ZM259.3156,119.373L232.04234,119.373L231.89257,81.37L225.8,81.37L225.8,0.8L290.2,0.8L290.2,125.2L259.3156,125.2L259.3156,119.373Z"
                        fill-rule="evenodd" fill="#333333" fill-opacity="1" />
                </g>
                <g>
                    <g
                        transform="matrix(0,-0.9999452829360962,0.9999452829360962,0,131.0055201248906,305.987311899662)">
                        <path
                            d="M231.4942,94.799267578125C231.4981,94.699677578125,231.5,94.599677578125,231.5,94.499267578125L225,94.499267578125L225,101.499267578125C225.10045,101.499267578125,225.20045,101.496867578125,225.3,101.492067578125C226.05475,101.455467578125,226.7839,101.280267578125,227.48744,100.966467578125C228.28378,100.611167578125,228.9867,100.105367578125,229.5962,99.448967578125Q230.5104,98.464467578125,231.0052,97.178047578125C231.2994,96.413227578125,231.4624,95.620297578125,231.4942,94.799267578125ZM231.194,94.799267578125Q231.1465,95.975077578125,230.7252,97.070357578125Q230.2512,98.302767578125,229.3764,99.244867578125Q228.5038,100.184567578125,227.36523,100.692467578125Q226.36836,101.137167578125,225.3,101.19166757812499L225.3,94.799267578125L231.194,94.799267578125Z"
                            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                    </g>
                    <g
                        transform="matrix(0,0.9999452829360962,0.9999452829360962,0,157.00336509943008,-156.98804432153702)">
                        <path
                            d="M231.4993,68.49964L231.4993,68.49962L224.99964,68.49962L224.99964,75.4992C225.10009,75.4992,225.20009,75.49680000000001,225.29964,75.492C226.05435,75.4554,226.78345,75.28020000000001,227.48695,74.9664C228.28325,74.6112,228.9861,74.1054,229.5956,73.4491Q230.5098,72.4646,231.0045,71.17826C231.2987,70.41347,231.4617,69.62058999999999,231.4935,68.79962C231.4974,68.70004,231.4993,68.60005,231.4993,68.49964ZM231.1933,68.79962Q231.1458,69.97534,230.7245,71.07056Q230.2506,72.3029,229.3757,73.245Q228.5032,74.1846,227.36474,74.69239999999999Q226.36794,75.1371,225.29964,75.1916L225.29964,68.79962L231.1933,68.79962Z"
                            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                    </g>
                </g>
                <g transform="matrix(0,-0.9999452829360962,0.9999452829360962,0,156.00150471925735,210.98995941877365)">
                    <path
                        d="M196.4942,34.8C196.4981,34.70041,196.5,34.60041,196.5,34.5L190,34.5L190,41.5C190.10045,41.5,190.20045,41.4976,190.3,41.4928C191.05475,41.4562,191.7839,41.281,192.48744,40.9672C193.28378,40.6119,193.9867,40.1061,194.5962,39.4497Q195.5104,38.465199999999996,196.0052,37.17878C196.2994,36.41396,196.4624,35.62103,196.4942,34.8ZM196.194,34.8Q196.1465,35.975809999999996,195.7252,37.07109Q195.2512,38.3035,194.3764,39.245599999999996Q193.5038,40.1853,192.36523,40.6932Q191.36836,41.1379,190.3,41.1924L190.3,34.8L196.194,34.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g transform="matrix(0,-0.9999452829360962,0.9999452829360962,0,103.0039333300374,219.99043077230453)">
                    <path
                        d="M174.4942,65.799267578125C174.4981,65.699677578125,174.5,65.599677578125,174.5,65.499267578125L168,65.499267578125L168,72.499267578125C168.10045,72.499267578125,168.20045,72.496867578125,168.3,72.492067578125C169.05475,72.455467578125,169.7839,72.280267578125,170.48744,71.966467578125C171.28378,71.611167578125,171.9867,71.105367578125,172.5962,70.448967578125Q173.5104,69.464467578125,174.0052,68.178047578125C174.2994,67.413227578125,174.4624,66.620297578125,174.4942,65.799267578125ZM174.194,65.799267578125Q174.1465,66.975077578125,173.7252,68.070357578125Q173.2512,69.302767578125,172.3764,70.244867578125Q171.5038,71.184567578125,170.36523,71.692467578125Q169.36836,72.137167578125,168.3,72.19166757812499L168.3,65.799267578125L174.194,65.799267578125Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <rect x="205.5" y="4.5" width="20" height="44" rx="0" fill="#E4E4E4" fill-opacity="1" />
                    <rect x="205.65000000596046" y="4.6500000059604645" width="19.69999998807907"
                        height="43.69999998807907" rx="0" fill-opacity="0" stroke-opacity="1" stroke="#999999"
                        fill="none" stroke-width="0.30000001192092896" />
                </g>
                <g>
                    <rect x="205.5" y="48.5" width="20" height="18" rx="0" fill="#E4E4E4" fill-opacity="1" />
                    <rect x="205.65000000596046" y="48.650000005960464" width="19.69999998807907"
                        height="17.69999998807907" rx="0" fill-opacity="0" stroke-opacity="1" stroke="#999999"
                        fill="none" stroke-width="0.30000001192092896" />
                </g>
                <g>
                    <rect x="42.5" y="0.5" width="20" height="66" rx="0" fill="#E4E4E4" fill-opacity="1" />
                    <rect x="42.650000005960464" y="0.6500000059604645" width="19.69999998807907"
                        height="65.69999998807907" rx="0" fill-opacity="0" stroke-opacity="1" stroke="#999999"
                        fill="none" stroke-width="0.30000001192092896" />
                </g>
                <g>
                    <g transform="matrix(1,0,0,-1,0,97)">
                        <path
                            d="M53.4927,54.8Q53.5,54.65073,53.5,54.5L47.5,54.5L47.5,60.5C47.60048,60.5,47.70048,60.4976,47.8,60.4927C48.48848,60.4591,49.15385,60.3093,49.796099999999996,60.0433C50.53119,59.7388,51.18003,59.3053,51.742599999999996,58.742599999999996C52.3053,58.18003,52.7388,57.53119,53.0433,56.796099999999996Q53.4423,55.83272,53.4927,54.8ZM53.1923,54.8Q53.1423,55.773089999999996,52.7661,56.6813Q52.3322,57.728790000000004,51.5305,58.5305Q50.7288,59.3322,49.6813,59.7661Q48.773089999999996,60.1423,47.8,60.1923L47.8,54.8L53.1923,54.8Z"
                            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                    </g>
                    <g transform="matrix(-1,0,0,-1,123,93)">
                        <path
                            d="M69.48902,50.8Q69.5,50.651089999999996,69.5,50.5L65.5,50.5L65.5,54.5C65.60073,54.5,65.70073,54.496340000000004,65.8,54.48902C66.22349,54.45779,66.63374,54.35996,67.03073,54.19552Q67.76582,53.891040000000004,68.32843,53.32843Q68.89104,52.76582,69.19552,52.03073Q69.44218,51.43524,69.48902,50.8ZM69.18812,50.8Q69.14214,51.37565,68.91835,51.91593Q68.63671,52.59588,68.1163,53.1163Q67.59588,53.63671,66.91593,53.918350000000004Q66.37565000000001,54.14215,65.8,54.18812L65.8,50.8L69.18812,50.8Z"
                            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                    </g>
                </g>
                <g>
                    <g transform="matrix(1,0,0,-1,0,145)">
                        <path
                            d="M53.4927,78.8Q53.5,78.65073,53.5,78.5L47.5,78.5L47.5,84.5C47.60048,84.5,47.70048,84.4976,47.8,84.4927C48.48848,84.4591,49.15385,84.30930000000001,49.796099999999996,84.0433C50.53119,83.7388,51.18003,83.3053,51.742599999999996,82.7426C52.3053,82.18003,52.7388,81.53119,53.0433,80.7961Q53.4423,79.83272,53.4927,78.8ZM53.1923,78.8Q53.1423,79.77309,52.7661,80.6813Q52.3322,81.72879,51.5305,82.5305Q50.7288,83.3322,49.6813,83.7661Q48.773089999999996,84.1423,47.8,84.1923L47.8,78.8L53.1923,78.8Z"
                            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                    </g>
                    <g transform="matrix(-1,0,0,-1,123,141)">
                        <path
                            d="M69.48902,74.8Q69.5,74.65109,69.5,74.5L65.5,74.5L65.5,78.5C65.60073,78.5,65.70073,78.49634,65.8,78.48902C66.22349,78.45779,66.63374,78.35996,67.03073,78.19552Q67.76582,77.89104,68.32843,77.32843Q68.89104,76.76582,69.19552,76.03073Q69.44218,75.43524,69.48902,74.8ZM69.18812,74.8Q69.14214,75.37565000000001,68.91835,75.91593Q68.63671,76.59588,68.1163,77.1163Q67.59588,77.63671,66.91593,77.91835Q66.37565000000001,78.14215,65.8,78.18812L65.8,74.8L69.18812,74.8Z"
                            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                    </g>
                </g>
                <g>
                    <rect x="62.5" y="4.5" width="18" height="56" rx="0" fill="#E4E4E4" fill-opacity="1" />
                    <rect x="62.650000005960464" y="4.6500000059604645" width="17.69999998807907"
                        height="55.69999998807907" rx="0" fill-opacity="0" stroke-opacity="1" stroke="#999999"
                        fill="none" stroke-width="0.30000001192092896" />
                </g>
                <g>
                    <line x1="42.5" y1="42.349999994039536" x2="61.5" y2="42.349999994039536" fill-opacity="0"
                        stroke-opacity="1" stroke="#999999" fill="none" stroke-width="0.30000001192092896" />
                </g>
                <g>
                    <line x1="62.5" y1="42.349999994039536" x2="80.5" y2="42.349999994039536" fill-opacity="0"
                        stroke-opacity="1" stroke="#999999" fill="none" stroke-width="0.30000001192092896" />
                </g>
                <g>
                    <line x1="62.5" y1="23.349999994039536" x2="80.5" y2="23.349999994039536" fill-opacity="0"
                        stroke-opacity="1" stroke="#999999" fill="none" stroke-width="0.30000001192092896" />
                </g>
                <g>
                    <path
                        d="M62.393402,5.605532Q62.35,5.561691,62.35,5.5Q62.35,5.4701631,62.361418,5.4425975Q62.372836,5.4150318,62.393934,5.393934Q62.4150318,5.372836,62.4425975,5.361418Q62.4701631,5.35,62.5,5.35Q62.562574,5.35,62.606598,5.394468L80.6066,23.5763Q80.6271,23.597,80.6384,23.6239Q80.64959999999999,23.6508,80.65,23.68Q80.6503,23.7092,80.6397,23.7363Q80.6291,23.7635,80.6091,23.7847L62.709824,42.7688L80.6049,60.3928L80.6053,60.3931Q80.65,60.4372,80.65,60.5Q80.65,60.5298,80.6386,60.5574Q80.6272,60.585,80.6061,60.6061Q80.58500000000001,60.6272,80.5574,60.6386Q80.5298,60.65,80.5,60.65Q80.4385,60.65,80.3947,60.6069L80.3944,60.6066L62.394747,42.8796Q62.373791,42.859,62.36218,42.8319Q62.35057,42.8049,62.350026,42.7755Q62.349481,42.7461,62.360083,42.7187Q62.370684,42.6912,62.390862,42.6698L80.2914,23.6844L62.393402,5.605532Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g transform="matrix(-1,0,0,1,161,0)">
                    <path
                        d="M80.393402,5.605532Q80.35,5.561691,80.35,5.5Q80.35,5.4701631,80.361418,5.4425975Q80.372836,5.4150318,80.393934,5.393934Q80.4150318,5.372836,80.4425975,5.361418Q80.4701631,5.35,80.5,5.35Q80.562574,5.35,80.606598,5.394468L98.6066,23.5763Q98.6271,23.597,98.6384,23.6239Q98.64959999999999,23.6508,98.65,23.68Q98.6503,23.7092,98.6397,23.7363Q98.6291,23.7635,98.6091,23.7847L80.709824,42.7688L98.6049,60.3928L98.6053,60.3931Q98.65,60.4372,98.65,60.5Q98.65,60.5298,98.6386,60.5574Q98.6272,60.585,98.6061,60.6061Q98.58500000000001,60.6272,98.5574,60.6386Q98.5298,60.65,98.5,60.65Q98.4385,60.65,98.3947,60.6069L98.3944,60.6066L80.394747,42.8796Q80.373791,42.859,80.36218,42.8319Q80.35057,42.8049,80.350026,42.7755Q80.349481,42.7461,80.360083,42.7187Q80.370684,42.6912,80.390862,42.6698L98.2914,23.6844L80.393402,5.605532Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M205.600384,48.388541L215.5,57.2982L225.3994,48.388767L225.3997,48.388506Q225.4424,48.35,225.5,48.35Q225.5298,48.35,225.5574,48.361418Q225.585,48.372836,225.6061,48.393934Q225.6272,48.4150318,225.6386,48.4425975Q225.65,48.4701631,225.65,48.5Q225.65,48.5668038,225.6003,48.611494L215.7242,57.5L225.6003,66.3885Q225.65,66.4332,225.65,66.5Q225.65,66.5298,225.6386,66.5574Q225.6272,66.58500000000001,225.6061,66.6061Q225.585,66.6272,225.5574,66.6386Q225.5298,66.65,225.5,66.65Q225.4424,66.65,225.3997,66.6115L225.3994,66.6113L215.5,57.7018L205.600345,66.6115Q205.5575603,66.65,205.5,66.65Q205.4701631,66.65,205.4425975,66.6386Q205.4150318,66.6272,205.393934,66.6061Q205.372836,66.58500000000001,205.361418,66.5574Q205.35,66.5298,205.35,66.5Q205.35,66.4332,205.399655,66.3885L215.27577,57.5L205.399695,48.61153L205.399655,48.611494Q205.35,48.5668044,205.35,48.5Q205.35,48.4701631,205.361418,48.4425975Q205.372836,48.4150318,205.393934,48.393934Q205.4150318,48.372836,205.4425975,48.361418Q205.4701631,48.35,205.5,48.35Q205.5575604,48.35,205.600345,48.388506L205.600384,48.388541Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M209.502,24.784C209.664,25.042,209.85,25.396,209.946,25.6L210.306,25.414C210.216,25.216,210.018,24.88,209.85,24.628L209.502,24.784ZM212.01,24.568C211.896,24.832,211.68,25.216,211.512,25.45L211.824,25.6C212.004,25.378,212.214,25.042,212.412,24.736L212.01,24.568ZM210.708,24.46L210.708,25.63L209.28,25.63L209.28,26.008L210.426,26.008C210.066,26.38,209.556,26.734,209.112,26.926000000000002C209.208,27.003999999999998,209.334,27.148,209.4,27.25C209.832,27.028,210.336,26.644,210.708,26.236L210.708,27.208L211.13400000000001,27.208L211.13400000000001,26.218C211.512,26.614,212.028,27.003999999999998,212.454,27.22C212.514,27.118000000000002,212.646,26.962,212.748,26.884C212.31,26.704,211.782,26.362000000000002,211.416,26.008L212.604,26.008L212.604,25.63L211.13400000000001,25.63L211.13400000000001,24.46L210.708,24.46ZM211.56,28.114C211.44,28.462,211.254,28.732,210.99,28.948C210.726,28.852,210.45,28.75,210.18,28.66C210.288,28.503999999999998,210.402,28.312,210.516,28.114L211.56,28.114ZM209.556,28.84C209.904,28.954,210.252,29.073999999999998,210.582,29.2C210.192,29.392,209.688,29.512,209.064,29.59C209.13,29.68,209.208,29.848,209.244,29.968C210.012,29.848,210.612,29.668,211.062,29.392C211.536,29.584,211.956,29.782,212.274,29.956L212.586,29.631999999999998C212.274,29.476,211.872,29.296,211.422,29.116C211.698,28.852,211.896,28.522,212.016,28.114L212.664,28.114L212.664,27.724L210.726,27.724C210.798,27.58,210.864,27.436,210.924,27.298000000000002L210.486,27.214C210.42,27.376,210.342,27.55,210.252,27.724L209.154,27.724L209.154,28.114L210.036,28.114C209.874,28.384,209.706,28.636,209.556,28.84ZM208.068,24.46L208.068,25.618L207.336,25.618L207.336,26.038L208.044,26.038C207.882,26.854,207.54,27.814,207.192,28.317999999999998C207.27,28.426000000000002,207.378,28.624,207.432,28.756C207.666,28.39,207.888,27.808,208.068,27.195999999999998L208.068,29.974L208.482,29.974L208.482,26.836C208.638,27.124,208.812,27.472,208.89,27.658L209.166,27.334C209.07,27.16,208.632,26.482,208.482,26.278L208.482,26.038L209.07,26.038L209.07,25.618L208.482,25.618L208.482,24.46L208.068,24.46ZM214.158,24.46L214.158,25.618L213.3,25.618L213.3,26.038L214.122,26.038C213.93,26.86,213.564,27.814,213.186,28.317999999999998C213.27,28.426000000000002,213.378,28.624,213.426,28.756C213.696,28.36,213.96,27.724,214.158,27.058L214.158,29.974L214.572,29.974L214.572,26.836C214.734,27.13,214.926,27.484,215.004,27.676000000000002L215.28,27.352C215.178,27.178,214.722,26.482,214.572,26.278L214.572,26.038L215.214,26.038L215.214,25.618L214.572,25.618L214.572,24.46L214.158,24.46ZM216.738,26.944L216.738,27.604L215.832,27.604L215.916,26.944L216.738,26.944ZM215.562,26.56C215.526,27.016,215.454,27.61,215.382,27.988L216.54,27.988C216.168,28.558,215.562,29.086,214.986,29.35C215.076,29.434,215.208,29.59,215.274,29.692C215.808,29.41,216.342,28.924,216.738,28.366L216.738,29.98L217.164,29.98L217.164,27.988L218.262,27.988C218.22,28.654,218.172,28.918,218.112,28.990000000000002C218.07,29.038,218.028,29.05,217.95,29.05C217.878,29.05,217.704,29.044,217.506,29.026C217.572,29.14,217.608,29.32,217.62,29.445999999999998C217.83,29.451999999999998,218.034,29.451999999999998,218.148,29.44C218.28,29.422,218.37,29.386,218.454,29.29C218.574,29.146,218.63400000000001,28.75,218.682,27.784C218.688,27.724,218.694,27.604,218.694,27.604L217.164,27.604L217.164,26.944L218.502,26.944L218.502,25.438L217.788,25.438C217.944,25.186,218.106,24.874,218.25,24.592L217.818,24.46C217.704,24.748,217.512,25.162,217.344,25.438L216.38400000000001,25.438L216.564,25.36C216.486,25.114,216.3,24.748,216.102,24.478L215.748,24.622C215.916,24.868,216.078,25.192,216.162,25.438L215.346,25.438L215.346,25.822L216.738,25.822L216.738,26.56L215.562,26.56ZM217.164,25.822L218.082,25.822L218.082,26.56L217.164,26.56L217.164,25.822ZM219.546,25.81L219.546,29.98L220.008,29.98L220.008,25.81L219.546,25.81ZM219.636,24.754C219.912,25.018,220.224,25.396,220.362,25.636L220.734,25.396C220.59,25.144,220.266,24.79,219.984,24.538L219.636,24.754ZM221.274,27.73L222.714,27.73L222.714,28.54L221.274,28.54L221.274,27.73ZM221.274,26.554000000000002L222.714,26.554000000000002L222.714,27.352L221.274,27.352L221.274,26.554000000000002ZM220.86599999999999,26.176000000000002L220.86599999999999,28.912L223.14,28.912L223.14,26.176000000000002L220.86599999999999,26.176000000000002ZM221.112,24.796L221.112,25.222L224.016,25.222L224.016,29.434C224.016,29.512,223.992,29.536,223.914,29.542C223.836,29.542,223.59,29.548000000000002,223.338,29.536C223.398,29.65,223.458,29.842,223.482,29.95C223.848,29.95,224.106,29.95,224.268,29.878C224.424,29.8,224.478,29.686,224.478,29.434L224.478,24.796L221.112,24.796Z"
                        fill="#333333" fill-opacity="0.6000000238418579" />
                </g>
                <g>
                    <path
                        d="M3.19,29.892L3.19,30.348L5.002,30.348L5.002,34.308L2.812,34.308L2.812,34.758L8.206,34.758L8.206,34.308L5.482,34.308L5.482,30.348L7.264,30.348L7.264,32.43C7.264,32.525999999999996,7.234,32.556,7.114,32.562C6.988,32.568,6.568,32.568,6.106,32.556C6.178,32.676,6.2620000000000005,32.874,6.286,33C6.838,33,7.216,32.994,7.438,32.922C7.66,32.844,7.726,32.706,7.726,32.442L7.726,29.892L3.19,29.892ZM9.934000000000001,29.556C9.706,30.414,9.315999999999999,31.248,8.824,31.782C8.937999999999999,31.842,9.136,31.974,9.225999999999999,32.052C9.454,31.782,9.664,31.44,9.856,31.062L11.278,31.062L11.278,32.388L9.49,32.388L9.49,32.82L11.278,32.82L11.278,34.35L8.83,34.35L8.83,34.788L14.194,34.788L14.194,34.35L11.746,34.35L11.746,32.82L13.69,32.82L13.69,32.388L11.746,32.388L11.746,31.062L13.906,31.062L13.906,30.624L11.746,30.624L11.746,29.46L11.278,29.46L11.278,30.624L10.054,30.624C10.186,30.318,10.3,29.988,10.39,29.658L9.934000000000001,29.556ZM15.046,30.81L15.046,34.980000000000004L15.508,34.980000000000004L15.508,30.81L15.046,30.81ZM15.136,29.754C15.412,30.018,15.724,30.396,15.862,30.636L16.234,30.396C16.09,30.144,15.766,29.79,15.484,29.538L15.136,29.754ZM16.774,32.730000000000004L18.214,32.730000000000004L18.214,33.54L16.774,33.54L16.774,32.730000000000004ZM16.774,31.554000000000002L18.214,31.554000000000002L18.214,32.352000000000004L16.774,32.352000000000004L16.774,31.554000000000002ZM16.366,31.176000000000002L16.366,33.912L18.64,33.912L18.64,31.176000000000002L16.366,31.176000000000002ZM16.612000000000002,29.796L16.612000000000002,30.222L19.516,30.222L19.516,34.434C19.516,34.512,19.492,34.536,19.414,34.542C19.336,34.542,19.09,34.548,18.838,34.536C18.898,34.65,18.958,34.842,18.982,34.95C19.348,34.95,19.606,34.95,19.768,34.878C19.924,34.8,19.978,34.686,19.978,34.434L19.978,29.796L16.612000000000002,29.796ZM7.906,44.676L8.242,44.525999999999996C7.726,43.674,7.48,42.653999999999996,7.48,41.634C7.48,40.62,7.726,39.606,8.242,38.748L7.906,38.592C7.354,39.492000000000004,7.024,40.458,7.024,41.634C7.024,42.816,7.354,43.782,7.906,44.676ZM12.514,40.374C12.328,41.166,12.046,41.784,11.608,42.252C11.164,42.048,10.702,41.85,10.246,41.67C10.432,41.298,10.636,40.848,10.834,40.374L12.514,40.374ZM9.562000000000001,41.88C10.132,42.096000000000004,10.696,42.342,11.23,42.594C10.648,43.038,9.862,43.314,8.776,43.47C8.878,43.59,8.98,43.782,9.027999999999999,43.926C10.228,43.722,11.092,43.38,11.722,42.834C12.49,43.224000000000004,13.174,43.620000000000005,13.666,43.974000000000004L14.038,43.572C13.54,43.230000000000004,12.844,42.846000000000004,12.076,42.474000000000004C12.532,41.94,12.826,41.25,13.018,40.374L14.164,40.374L14.164,39.894L11.026,39.894C11.212,39.408,11.38,38.916,11.5,38.466L11.014,38.4C10.888,38.862,10.708,39.378,10.504,39.894L8.86,39.894L8.86,40.374L10.3,40.374C10.054,40.944,9.796,41.478,9.562000000000001,41.88ZM15.094,44.676C15.646,43.782,15.976,42.816,15.976,41.634C15.976,40.458,15.646,39.492000000000004,15.094,38.592L14.752,38.748C15.268,39.606,15.526,40.62,15.526,41.634C15.526,42.653999999999996,15.268,43.674,14.752,44.525999999999996L15.094,44.676Z"
                        fill="#333333" fill-opacity="0.6000000238418579" />
                </g>
                <g>
                    <path
                        d="M14.532,99.5L16.944,99.5L16.944,99.044L16.062,99.044L16.062,95.102L15.642,95.102C15.402,95.24,15.120000000000001,95.342,14.73,95.414L14.73,95.762L15.516,95.762L15.516,99.044L14.532,99.044L14.532,99.5ZM18.912,99.578C19.698,99.578,20.328,99.11,20.328,98.324C20.328,97.718,19.914,97.334,19.398,97.208L19.398,97.178C19.866,97.016,20.178,96.656,20.178,96.122C20.178,95.426,19.637999999999998,95.024,18.894,95.024C18.39,95.024,18,95.246,17.67,95.546L17.964,95.894C18.216,95.642,18.522,95.468,18.876,95.468C19.338,95.468,19.62,95.744,19.62,96.164C19.62,96.638,19.314,97.004,18.402,97.004L18.402,97.424C19.422,97.424,19.77,97.772,19.77,98.306C19.77,98.81,19.404,99.122,18.876,99.122C18.378,99.122,18.048000000000002,98.882,17.79,98.618L17.508,98.972C17.796,99.29,18.228,99.578,18.912,99.578ZM22.332,99.578C23.166,99.578,23.7,98.822,23.7,97.286C23.7,95.762,23.166,95.024,22.332,95.024C21.492,95.024,20.964,95.762,20.964,97.286C20.964,98.822,21.492,99.578,22.332,99.578ZM22.332,99.134C21.834,99.134,21.492,98.576,21.492,97.286C21.492,96.002,21.834,95.456,22.332,95.456C22.83,95.456,23.172,96.002,23.172,97.286C23.172,98.576,22.83,99.134,22.332,99.134ZM25.566000000000003,99.578C26.304000000000002,99.578,27.006,99.032,27.006,98.072C27.006,97.1,26.406,96.668,25.68,96.668C25.416,96.668,25.218,96.734,25.02,96.842L25.134,95.57L26.79,95.57L26.79,95.102L24.654,95.102L24.509999999999998,97.154L24.804000000000002,97.34C25.055999999999997,97.172,25.242,97.082,25.536,97.082C26.088,97.082,26.448,97.454,26.448,98.084C26.448,98.726,26.034,99.122,25.512,99.122C25.002000000000002,99.122,24.678,98.888,24.432000000000002,98.636L24.156,98.996C24.456,99.29,24.875999999999998,99.578,25.566000000000003,99.578ZM27.39,100.574L27.792,100.574L29.586,94.736L29.189999999999998,94.736L27.39,100.574ZM30.204,99.5L32.616,99.5L32.616,99.044L31.734,99.044L31.734,95.102L31.314,95.102C31.074,95.24,30.792,95.342,30.402,95.414L30.402,95.762L31.188,95.762L31.188,99.044L30.204,99.044L30.204,99.5ZM34.584,99.578C35.370000000000005,99.578,36,99.11,36,98.324C36,97.718,35.586,97.334,35.07,97.208L35.07,97.178C35.538,97.016,35.85,96.656,35.85,96.122C35.85,95.426,35.31,95.024,34.566,95.024C34.062,95.024,33.672,95.246,33.342,95.546L33.635999999999996,95.894C33.888000000000005,95.642,34.194,95.468,34.548,95.468C35.010000000000005,95.468,35.292,95.744,35.292,96.164C35.292,96.638,34.986000000000004,97.004,34.074,97.004L34.074,97.424C35.094,97.424,35.442,97.772,35.442,98.306C35.442,98.81,35.076,99.122,34.548,99.122C34.05,99.122,33.72,98.882,33.462,98.618L33.18,98.972C33.468,99.29,33.9,99.578,34.584,99.578ZM38.004000000000005,99.578C38.838,99.578,39.372,98.822,39.372,97.286C39.372,95.762,38.838,95.024,38.004000000000005,95.024C37.164,95.024,36.635999999999996,95.762,36.635999999999996,97.286C36.635999999999996,98.822,37.164,99.578,38.004000000000005,99.578ZM38.004000000000005,99.134C37.506,99.134,37.164,98.576,37.164,97.286C37.164,96.002,37.506,95.456,38.004000000000005,95.456C38.501999999999995,95.456,38.844,96.002,38.844,97.286C38.844,98.576,38.501999999999995,99.134,38.004000000000005,99.134ZM41.472,99.578C42.156,99.578,42.738,99.002,42.738,98.15C42.738,97.226,42.257999999999996,96.77,41.513999999999996,96.77C41.172,96.77,40.788,96.968,40.518,97.298C40.542,95.936,41.04,95.474,41.652,95.474C41.916,95.474,42.18,95.606,42.348,95.81L42.66,95.474C42.414,95.21,42.084,95.024,41.628,95.024C40.775999999999996,95.024,40.001999999999995,95.678,40.001999999999995,97.4C40.001999999999995,98.852,40.632000000000005,99.578,41.472,99.578ZM40.53,97.736C40.818,97.328,41.153999999999996,97.178,41.424,97.178C41.958,97.178,42.216,97.556,42.216,98.15C42.216,98.75,41.891999999999996,99.146,41.472,99.146C40.92,99.146,40.59,98.648,40.53,97.736ZM17.898,106.484L17.898,106.91L15.144,106.91L15.144,106.484L17.898,106.484ZM14.7,106.136L14.7,108.992L15.144,108.992L15.144,107.996L17.898,107.996L17.898,108.482C17.898,108.572,17.868000000000002,108.596,17.766,108.602C17.67,108.608,17.31,108.608,16.944,108.596C17.004,108.70400000000001,17.07,108.854,17.094,108.968C17.586,108.968,17.904,108.968,18.102,108.908C18.288,108.848,18.348,108.73400000000001,18.348,108.488L18.348,106.136L14.7,106.136ZM15.144,107.234L17.898,107.234L17.898,107.672L15.144,107.672L15.144,107.234ZM16.259999999999998,103.46000000000001L16.259999999999998,103.862L14.244,103.862L14.244,104.216L16.259999999999998,104.216L16.259999999999998,104.618L14.448,104.618L14.448,104.966L16.259999999999998,104.966L16.259999999999998,105.398L13.854,105.398L13.854,105.758L19.146,105.758L19.146,105.398L16.716,105.398L16.716,104.966L18.57,104.966L18.57,104.618L16.716,104.618L16.716,104.216L18.822,104.216L18.822,103.862L16.716,103.862L16.716,103.46000000000001L16.259999999999998,103.46000000000001ZM19.788,107.162L19.788,107.594L22.572,107.594L22.572,108.98L23.034,108.98L23.034,107.594L25.224,107.594L25.224,107.162L23.034,107.162L23.034,105.968L24.804000000000002,105.968L24.804000000000002,105.542L23.034,105.542L23.034,104.618L24.942,104.618L24.942,104.186L21.342,104.186C21.444,103.982,21.534,103.772,21.618000000000002,103.556L21.162,103.436C20.874,104.252,20.376,105.032,19.8,105.524C19.914,105.59,20.106,105.74,20.19,105.812C20.514,105.5,20.832,105.086,21.108,104.618L22.572,104.618L22.572,105.542L20.778,105.542L20.778,107.162L19.788,107.162ZM21.228,107.162L21.228,105.968L22.572,105.968L22.572,107.162L21.228,107.162ZM26.046,103.856C26.412,104.054,26.916,104.342,27.168,104.528L27.432000000000002,104.156C27.174,103.988,26.664,103.712,26.298000000000002,103.538L26.046,103.856ZM25.752000000000002,105.506C26.118000000000002,105.70400000000001,26.616,105.992,26.862000000000002,106.16L27.114,105.788C26.856,105.62,26.352,105.35,25.997999999999998,105.176L25.752000000000002,105.506ZM25.89,108.596L26.274,108.902C26.628,108.344,27.048000000000002,107.594,27.366,106.958L27.036,106.664C26.688000000000002,107.342,26.214,108.134,25.89,108.596ZM27.42,105.218L27.42,105.65L29.154,105.65L29.154,106.646L27.852,106.646L27.852,108.974L28.272,108.974L28.272,108.71600000000001L30.414,108.71600000000001L30.414,108.944L30.846,108.944L30.846,106.646L29.58,106.646L29.58,105.65L31.242,105.65L31.242,105.218L29.58,105.218L29.58,104.168C30.102,104.078,30.588,103.964,30.984,103.832L30.624,103.484C29.958,103.718,28.740000000000002,103.91,27.701999999999998,104.018C27.75,104.12,27.810000000000002,104.294,27.834,104.402C28.259999999999998,104.36,28.71,104.306,29.154,104.24L29.154,105.218L27.42,105.218ZM28.272,108.30799999999999L28.272,107.06L30.414,107.06L30.414,108.30799999999999L28.272,108.30799999999999ZM32.034,103.952L32.034,104.354L34.356,104.354L34.356,103.952L32.034,103.952ZM35.418,103.562C35.418,103.988,35.418,104.42,35.4,104.846L34.542,104.846L34.542,105.278L35.382000000000005,105.278C35.31,106.646,35.07,107.9,34.248000000000005,108.65C34.367999999999995,108.71600000000001,34.524,108.866,34.602000000000004,108.974C35.484,108.134,35.742000000000004,106.766,35.826,105.278L36.72,105.278C36.653999999999996,107.408,36.576,108.206,36.414,108.386C36.354,108.458,36.288,108.476,36.18,108.476C36.054,108.476,35.736000000000004,108.476,35.4,108.44C35.478,108.572,35.525999999999996,108.758,35.538,108.884C35.856,108.908,36.186,108.908,36.372,108.89C36.564,108.872,36.684,108.818,36.804,108.662C37.013999999999996,108.398,37.086,107.54599999999999,37.17,105.074C37.17,105.008,37.17,104.846,37.17,104.846L35.844,104.846C35.856,104.42,35.861999999999995,103.988,35.861999999999995,103.562L35.418,103.562ZM32.034,108.236L32.04,108.23L32.04,108.242C32.178,108.158,32.394,108.092,34.062,107.714L34.176,108.116L34.572,107.984C34.458,107.564,34.188,106.85,33.96,106.31L33.588,106.412C33.708,106.694,33.828,107.024,33.936,107.336L32.507999999999996,107.636C32.742000000000004,107.096,32.97,106.424,33.120000000000005,105.794L34.464,105.794L34.464,105.38L31.824,105.38L31.824,105.794L32.658,105.794C32.501999999999995,106.496,32.25,107.20400000000001,32.166,107.402C32.064,107.63,31.986,107.792,31.89,107.822C31.944,107.93,32.010000000000005,108.146,32.034,108.236ZM38.394,107.20400000000001L38.394,107.6L40.266,107.6L40.266,108.404L37.854,108.404L37.854,108.812L43.17,108.812L43.17,108.404L40.728,108.404L40.728,107.6L42.635999999999996,107.6L42.635999999999996,107.20400000000001L40.728,107.20400000000001L40.728,106.574L40.266,106.574L40.266,107.20400000000001L38.394,107.20400000000001ZM38.64,106.682C38.826,106.61,39.108000000000004,106.586,41.976,106.364C42.114000000000004,106.502,42.234,106.64,42.318,106.748L42.666,106.502C42.42,106.19,41.903999999999996,105.728,41.484,105.404L41.153999999999996,105.626C41.31,105.752,41.478,105.89,41.64,106.03999999999999L39.318,106.202C39.66,105.95,40.001999999999995,105.65,40.32,105.332L42.510000000000005,105.332L42.510000000000005,104.94200000000001L38.538,104.94200000000001L38.538,105.332L39.738,105.332C39.402,105.674,39.048,105.962,38.916,106.05199999999999C38.760000000000005,106.172,38.622,106.25,38.507999999999996,106.268C38.556,106.382,38.616,106.592,38.64,106.682ZM40.11,103.526C40.194,103.664,40.278,103.838,40.344,103.994L37.92,103.994L37.92,105.056L38.358000000000004,105.056L38.358000000000004,104.402L42.629999999999995,104.402L42.629999999999995,105.056L43.086,105.056L43.086,103.994L40.848,103.994C40.782,103.814,40.656,103.58,40.542,103.4L40.11,103.526Z"
                        fill="#333333" fill-opacity="0.6000000238418579" />
                </g>
                <g>
                    <path
                        d="M24.19,29.892L24.19,30.348L26.002,30.348L26.002,34.308L23.812,34.308L23.812,34.758L29.206,34.758L29.206,34.308L26.482,34.308L26.482,30.348L28.264,30.348L28.264,32.43C28.264,32.525999999999996,28.234,32.556,28.114,32.562C27.988,32.568,27.567999999999998,32.568,27.106,32.556C27.178,32.676,27.262,32.874,27.286,33C27.838,33,28.216,32.994,28.438,32.922C28.66,32.844,28.726,32.706,28.726,32.442L28.726,29.892L24.19,29.892ZM30.934,29.556C30.706,30.414,30.316,31.248,29.823999999999998,31.782C29.938,31.842,30.136,31.974,30.226,32.052C30.454,31.782,30.664,31.44,30.856,31.062L32.278,31.062L32.278,32.388L30.490000000000002,32.388L30.490000000000002,32.82L32.278,32.82L32.278,34.35L29.83,34.35L29.83,34.788L35.194,34.788L35.194,34.35L32.746,34.35L32.746,32.82L34.69,32.82L34.69,32.388L32.746,32.388L32.746,31.062L34.906,31.062L34.906,30.624L32.746,30.624L32.746,29.46L32.278,29.46L32.278,30.624L31.054000000000002,30.624C31.186,30.318,31.3,29.988,31.39,29.658L30.934,29.556ZM36.046,30.81L36.046,34.980000000000004L36.507999999999996,34.980000000000004L36.507999999999996,30.81L36.046,30.81ZM36.135999999999996,29.754C36.412,30.018,36.724000000000004,30.396,36.862,30.636L37.234,30.396C37.09,30.144,36.766,29.79,36.484,29.538L36.135999999999996,29.754ZM37.774,32.730000000000004L39.214,32.730000000000004L39.214,33.54L37.774,33.54L37.774,32.730000000000004ZM37.774,31.554000000000002L39.214,31.554000000000002L39.214,32.352000000000004L37.774,32.352000000000004L37.774,31.554000000000002ZM37.366,31.176000000000002L37.366,33.912L39.64,33.912L39.64,31.176000000000002L37.366,31.176000000000002ZM37.612,29.796L37.612,30.222L40.516,30.222L40.516,34.434C40.516,34.512,40.492000000000004,34.536,40.414,34.542C40.336,34.542,40.09,34.548,39.838,34.536C39.897999999999996,34.65,39.958,34.842,39.982,34.95C40.348,34.95,40.606,34.95,40.768,34.878C40.924,34.8,40.978,34.686,40.978,34.434L40.978,29.796L37.612,29.796ZM28.906,44.676L29.242,44.525999999999996C28.726,43.674,28.48,42.653999999999996,28.48,41.634C28.48,40.62,28.726,39.606,29.242,38.748L28.906,38.592C28.354,39.492000000000004,28.024,40.458,28.024,41.634C28.024,42.816,28.354,43.782,28.906,44.676ZM30.862000000000002,40.164L32.254,40.164L32.254,40.812L30.862000000000002,40.812L30.862000000000002,40.164ZM32.704,40.164L34.12,40.164L34.12,40.812L32.704,40.812L32.704,40.164ZM30.862000000000002,39.162L32.254,39.162L32.254,39.804L30.862000000000002,39.804L30.862000000000002,39.162ZM32.704,39.162L34.12,39.162L34.12,39.804L32.704,39.804L32.704,39.162ZM29.932000000000002,41.784L29.932000000000002,42.198L31.906,42.198C31.624000000000002,42.84,31.048000000000002,43.32,29.758,43.59C29.848,43.686,29.962,43.866,29.998,43.980000000000004C31.468,43.65,32.098,43.025999999999996,32.397999999999996,42.198L34.294,42.198C34.21,43.025999999999996,34.108000000000004,43.391999999999996,33.976,43.506C33.916,43.56,33.844,43.566,33.712,43.566C33.574,43.566,33.178,43.56,32.788,43.524C32.86,43.638000000000005,32.92,43.812,32.926,43.938C33.316,43.956,33.682,43.962,33.874,43.956C34.084,43.938,34.222,43.908,34.354,43.788C34.546,43.596000000000004,34.66,43.128,34.774,41.982C34.78,41.922,34.792,41.784,34.792,41.784L32.524,41.784C32.566,41.598,32.602000000000004,41.406,32.626,41.202L34.588,41.202L34.588,38.778L30.418,38.778L30.418,41.202L32.158,41.202C32.134,41.406,32.098,41.598,32.05,41.784L29.932000000000002,41.784ZM36.094,44.676C36.646,43.782,36.976,42.816,36.976,41.634C36.976,40.458,36.646,39.492000000000004,36.094,38.592L35.752,38.748C36.268,39.606,36.525999999999996,40.62,36.525999999999996,41.634C36.525999999999996,42.653999999999996,36.268,43.674,35.752,44.525999999999996L36.094,44.676Z"
                        fill="#333333" fill-opacity="0.6000000238418579" />
                </g>
                <g>
                    <path
                        d="M45.002,18.784C45.164,19.042,45.35,19.396,45.446,19.6L45.806,19.414C45.716,19.216,45.518,18.88,45.35,18.628L45.002,18.784ZM47.51,18.568C47.396,18.832,47.18,19.216,47.012,19.45L47.324,19.6C47.504,19.378,47.714,19.042,47.912,18.736L47.51,18.568ZM46.208,18.46L46.208,19.63L44.78,19.63L44.78,20.008L45.926,20.008C45.566,20.38,45.056,20.734,44.612,20.926000000000002C44.708,21.003999999999998,44.834,21.148,44.9,21.25C45.332,21.028,45.836,20.644,46.208,20.236L46.208,21.208L46.634,21.208L46.634,20.218C47.012,20.614,47.528,21.003999999999998,47.954,21.22C48.014,21.118000000000002,48.146,20.962,48.248,20.884C47.81,20.704,47.282,20.362000000000002,46.916,20.008L48.104,20.008L48.104,19.63L46.634,19.63L46.634,18.46L46.208,18.46ZM47.06,22.114C46.94,22.462,46.754,22.732,46.49,22.948C46.226,22.852,45.95,22.75,45.68,22.66C45.788,22.503999999999998,45.902,22.312,46.016,22.114L47.06,22.114ZM45.056,22.84C45.403999999999996,22.954,45.752,23.073999999999998,46.082,23.2C45.692,23.392,45.188,23.512,44.564,23.59C44.63,23.68,44.708,23.848,44.744,23.968C45.512,23.848,46.112,23.668,46.562,23.392C47.036,23.584,47.456,23.782,47.774,23.956L48.086,23.631999999999998C47.774,23.476,47.372,23.296,46.922,23.116C47.198,22.852,47.396,22.522,47.516,22.114L48.164,22.114L48.164,21.724L46.226,21.724C46.298,21.58,46.364,21.436,46.424,21.298000000000002L45.986,21.214C45.92,21.376,45.842,21.55,45.752,21.724L44.653999999999996,21.724L44.653999999999996,22.114L45.536,22.114C45.374,22.384,45.206,22.636,45.056,22.84ZM43.568,18.46L43.568,19.618L42.836,19.618L42.836,20.038L43.544,20.038C43.382,20.854,43.04,21.814,42.692,22.317999999999998C42.77,22.426000000000002,42.878,22.624,42.932,22.756C43.166,22.39,43.388,21.808,43.568,21.195999999999998L43.568,23.974L43.982,23.974L43.982,20.836C44.138,21.124,44.312,21.472,44.39,21.658L44.666,21.334C44.57,21.16,44.132,20.482,43.982,20.278L43.982,20.038L44.57,20.038L44.57,19.618L43.982,19.618L43.982,18.46L43.568,18.46ZM49.658,18.46L49.658,19.618L48.8,19.618L48.8,20.038L49.622,20.038C49.43,20.86,49.064,21.814,48.686,22.317999999999998C48.769999999999996,22.426000000000002,48.878,22.624,48.926,22.756C49.196,22.36,49.46,21.724,49.658,21.058L49.658,23.974L50.072,23.974L50.072,20.836C50.234,21.13,50.426,21.484,50.504,21.676000000000002L50.78,21.352C50.678,21.178,50.222,20.482,50.072,20.278L50.072,20.038L50.714,20.038L50.714,19.618L50.072,19.618L50.072,18.46L49.658,18.46ZM52.238,20.944L52.238,21.604L51.332,21.604L51.416,20.944L52.238,20.944ZM51.062,20.56C51.025999999999996,21.016,50.954,21.61,50.882,21.988L52.04,21.988C51.668,22.558,51.062,23.086,50.486,23.35C50.576,23.434,50.708,23.59,50.774,23.692C51.308,23.41,51.842,22.924,52.238,22.366L52.238,23.98L52.664,23.98L52.664,21.988L53.762,21.988C53.72,22.654,53.672,22.918,53.612,22.990000000000002C53.57,23.038,53.528,23.05,53.45,23.05C53.378,23.05,53.204,23.044,53.006,23.026C53.072,23.14,53.108000000000004,23.32,53.12,23.445999999999998C53.33,23.451999999999998,53.534,23.451999999999998,53.647999999999996,23.44C53.78,23.422,53.87,23.386,53.954,23.29C54.074,23.146,54.134,22.75,54.182,21.784C54.188,21.724,54.194,21.604,54.194,21.604L52.664,21.604L52.664,20.944L54.002,20.944L54.002,19.438L53.288,19.438C53.444,19.186,53.606,18.874,53.75,18.592L53.318,18.46C53.204,18.748,53.012,19.162,52.844,19.438L51.884,19.438L52.064,19.36C51.986000000000004,19.114,51.8,18.748,51.602000000000004,18.478L51.248,18.622C51.416,18.868,51.578,19.192,51.662,19.438L50.846000000000004,19.438L50.846000000000004,19.822L52.238,19.822L52.238,20.56L51.062,20.56ZM52.664,19.822L53.582,19.822L53.582,20.56L52.664,20.56L52.664,19.822ZM55.046,19.81L55.046,23.98L55.507999999999996,23.98L55.507999999999996,19.81L55.046,19.81ZM55.135999999999996,18.754C55.412,19.018,55.724000000000004,19.396,55.862,19.636L56.234,19.396C56.09,19.144,55.766,18.79,55.484,18.538L55.135999999999996,18.754ZM56.774,21.73L58.214,21.73L58.214,22.54L56.774,22.54L56.774,21.73ZM56.774,20.554000000000002L58.214,20.554000000000002L58.214,21.352L56.774,21.352L56.774,20.554000000000002ZM56.366,20.176000000000002L56.366,22.912L58.64,22.912L58.64,20.176000000000002L56.366,20.176000000000002ZM56.612,18.796L56.612,19.222L59.516,19.222L59.516,23.434C59.516,23.512,59.492000000000004,23.536,59.414,23.542C59.336,23.542,59.09,23.548000000000002,58.838,23.536C58.897999999999996,23.65,58.958,23.842,58.982,23.95C59.348,23.95,59.606,23.95,59.768,23.878C59.924,23.8,59.978,23.686,59.978,23.434L59.978,18.796L56.612,18.796Z"
                        fill="#333333" fill-opacity="0.6000000238418579" />
                </g>
                <g>
                    <path
                        d="M64.712,35.052L64.712,35.916L63.224,35.916L63.224,35.052L64.712,35.052ZM65.186,35.052L66.728,35.052L66.728,35.916L65.186,35.916L65.186,35.052ZM64.712,34.632L63.224,34.632L63.224,33.774L64.712,33.774L64.712,34.632ZM65.186,34.632L65.186,33.774L66.728,33.774L66.728,34.632L65.186,34.632ZM62.756,33.33L62.756,36.726L63.224,36.726L63.224,36.354L64.712,36.354L64.712,36.99C64.712,37.692,64.91,37.878,65.582,37.878C65.732,37.878,66.746,37.878,66.908,37.878C67.55,37.878,67.694,37.56,67.772,36.647999999999996C67.634,36.612,67.44200000000001,36.528,67.322,36.444C67.28,37.224000000000004,67.22,37.422,66.884,37.422C66.668,37.422,65.792,37.422,65.612,37.422C65.252,37.422,65.186,37.35,65.186,37.002L65.186,36.354L67.19,36.354L67.19,33.33L65.186,33.33L65.186,32.472L64.712,32.472L64.712,33.33L62.756,33.33ZM69.158,32.46L69.158,33.618L68.3,33.618L68.3,34.038L69.122,34.038C68.93,34.86,68.564,35.814,68.186,36.318C68.27,36.426,68.378,36.624,68.426,36.756C68.696,36.36,68.96,35.724000000000004,69.158,35.058L69.158,37.974000000000004L69.572,37.974000000000004L69.572,34.836C69.734,35.13,69.926,35.484,70.004,35.676L70.28,35.352000000000004C70.178,35.178,69.722,34.482,69.572,34.278L69.572,34.038L70.214,34.038L70.214,33.618L69.572,33.618L69.572,32.46L69.158,32.46ZM71.738,34.944L71.738,35.604L70.832,35.604L70.916,34.944L71.738,34.944ZM70.562,34.56C70.526,35.016,70.45400000000001,35.61,70.382,35.988L71.53999999999999,35.988C71.168,36.558,70.562,37.086,69.986,37.35C70.076,37.434,70.208,37.59,70.274,37.692C70.80799999999999,37.41,71.342,36.924,71.738,36.366L71.738,37.980000000000004L72.164,37.980000000000004L72.164,35.988L73.262,35.988C73.22,36.653999999999996,73.172,36.918,73.112,36.99C73.07,37.038,73.028,37.05,72.95,37.05C72.878,37.05,72.70400000000001,37.044,72.506,37.025999999999996C72.572,37.14,72.608,37.32,72.62,37.446C72.83,37.452,73.034,37.452,73.148,37.44C73.28,37.422,73.37,37.386,73.45400000000001,37.29C73.574,37.146,73.634,36.75,73.682,35.784C73.688,35.724000000000004,73.694,35.604,73.694,35.604L72.164,35.604L72.164,34.944L73.502,34.944L73.502,33.438L72.788,33.438C72.944,33.186,73.106,32.874,73.25,32.592L72.818,32.46C72.70400000000001,32.748,72.512,33.162,72.344,33.438L71.384,33.438L71.564,33.36C71.486,33.114,71.3,32.748,71.102,32.478L70.748,32.622C70.916,32.868,71.078,33.192,71.162,33.438L70.346,33.438L70.346,33.822L71.738,33.822L71.738,34.56L70.562,34.56ZM72.164,33.822L73.082,33.822L73.082,34.56L72.164,34.56L72.164,33.822ZM74.54599999999999,33.81L74.54599999999999,37.980000000000004L75.008,37.980000000000004L75.008,33.81L74.54599999999999,33.81ZM74.636,32.754C74.912,33.018,75.224,33.396,75.362,33.636L75.734,33.396C75.59,33.144,75.266,32.79,74.984,32.538L74.636,32.754ZM76.274,35.730000000000004L77.714,35.730000000000004L77.714,36.54L76.274,36.54L76.274,35.730000000000004ZM76.274,34.554L77.714,34.554L77.714,35.352000000000004L76.274,35.352000000000004L76.274,34.554ZM75.866,34.176L75.866,36.912L78.14,36.912L78.14,34.176L75.866,34.176ZM76.112,32.796L76.112,33.222L79.01599999999999,33.222L79.01599999999999,37.434C79.01599999999999,37.512,78.992,37.536,78.914,37.542C78.836,37.542,78.59,37.548,78.338,37.536C78.398,37.65,78.458,37.842,78.482,37.95C78.848,37.95,79.106,37.95,79.268,37.878C79.424,37.8,79.47800000000001,37.686,79.47800000000001,37.434L79.47800000000001,32.796L76.112,32.796Z"
                        fill="#333333" fill-opacity="0.6000000238418579" />
                </g>
                <g>
                    <path
                        d="M209.712,57.052L209.712,57.916L208.224,57.916L208.224,57.052L209.712,57.052ZM210.186,57.052L211.728,57.052L211.728,57.916L210.186,57.916L210.186,57.052ZM209.712,56.632L208.224,56.632L208.224,55.774L209.712,55.774L209.712,56.632ZM210.186,56.632L210.186,55.774L211.728,55.774L211.728,56.632L210.186,56.632ZM207.756,55.33L207.756,58.726L208.224,58.726L208.224,58.354L209.712,58.354L209.712,58.99C209.712,59.692,209.91,59.878,210.582,59.878C210.732,59.878,211.746,59.878,211.908,59.878C212.55,59.878,212.694,59.56,212.772,58.647999999999996C212.63400000000001,58.612,212.442,58.528,212.322,58.444C212.28,59.224000000000004,212.22,59.422,211.88400000000001,59.422C211.668,59.422,210.792,59.422,210.612,59.422C210.252,59.422,210.186,59.35,210.186,59.002L210.186,58.354L212.19,58.354L212.19,55.33L210.186,55.33L210.186,54.472L209.712,54.472L209.712,55.33L207.756,55.33ZM214.158,54.46L214.158,55.618L213.3,55.618L213.3,56.038L214.122,56.038C213.93,56.86,213.564,57.814,213.186,58.318C213.27,58.426,213.378,58.624,213.426,58.756C213.696,58.36,213.96,57.724000000000004,214.158,57.058L214.158,59.974000000000004L214.572,59.974000000000004L214.572,56.836C214.734,57.13,214.926,57.484,215.004,57.676L215.28,57.352000000000004C215.178,57.178,214.722,56.482,214.572,56.278L214.572,56.038L215.214,56.038L215.214,55.618L214.572,55.618L214.572,54.46L214.158,54.46ZM216.738,56.944L216.738,57.604L215.832,57.604L215.916,56.944L216.738,56.944ZM215.562,56.56C215.526,57.016,215.454,57.61,215.382,57.988L216.54,57.988C216.168,58.558,215.562,59.086,214.986,59.35C215.076,59.434,215.208,59.59,215.274,59.692C215.808,59.41,216.342,58.924,216.738,58.366L216.738,59.980000000000004L217.164,59.980000000000004L217.164,57.988L218.262,57.988C218.22,58.653999999999996,218.172,58.918,218.112,58.99C218.07,59.038,218.028,59.05,217.95,59.05C217.878,59.05,217.704,59.044,217.506,59.025999999999996C217.572,59.14,217.608,59.32,217.62,59.446C217.83,59.452,218.034,59.452,218.148,59.44C218.28,59.422,218.37,59.386,218.454,59.29C218.574,59.146,218.63400000000001,58.75,218.682,57.784C218.688,57.724000000000004,218.694,57.604,218.694,57.604L217.164,57.604L217.164,56.944L218.502,56.944L218.502,55.438L217.788,55.438C217.944,55.186,218.106,54.874,218.25,54.592L217.818,54.46C217.704,54.748,217.512,55.162,217.344,55.438L216.38400000000001,55.438L216.564,55.36C216.486,55.114,216.3,54.748,216.102,54.478L215.748,54.622C215.916,54.868,216.078,55.192,216.162,55.438L215.346,55.438L215.346,55.822L216.738,55.822L216.738,56.56L215.562,56.56ZM217.164,55.822L218.082,55.822L218.082,56.56L217.164,56.56L217.164,55.822ZM219.546,55.81L219.546,59.980000000000004L220.008,59.980000000000004L220.008,55.81L219.546,55.81ZM219.636,54.754C219.912,55.018,220.224,55.396,220.362,55.636L220.734,55.396C220.59,55.144,220.266,54.79,219.984,54.538L219.636,54.754ZM221.274,57.730000000000004L222.714,57.730000000000004L222.714,58.54L221.274,58.54L221.274,57.730000000000004ZM221.274,56.554L222.714,56.554L222.714,57.352000000000004L221.274,57.352000000000004L221.274,56.554ZM220.86599999999999,56.176L220.86599999999999,58.912L223.14,58.912L223.14,56.176L220.86599999999999,56.176ZM221.112,54.796L221.112,55.222L224.016,55.222L224.016,59.434C224.016,59.512,223.992,59.536,223.914,59.542C223.836,59.542,223.59,59.548,223.338,59.536C223.398,59.65,223.458,59.842,223.482,59.95C223.848,59.95,224.106,59.95,224.268,59.878C224.424,59.8,224.478,59.686,224.478,59.434L224.478,54.796L221.112,54.796Z"
                        fill="#333333" fill-opacity="0.6000000238418579" />
                </g>
                <g>
                    <path
                        d="M115.368,33.5L117.78,33.5L117.78,33.044L116.898,33.044L116.898,29.102L116.478,29.102C116.238,29.240000000000002,115.956,29.342,115.566,29.414L115.566,29.762L116.352,29.762L116.352,33.044L115.368,33.044L115.368,33.5ZM119.748,33.578C120.534,33.578,121.164,33.11,121.164,32.324C121.164,31.718,120.75,31.334,120.234,31.208L120.234,31.178C120.702,31.016,121.014,30.656,121.014,30.122C121.014,29.426000000000002,120.474,29.024,119.73,29.024C119.226,29.024,118.836,29.246,118.506,29.546L118.8,29.894C119.05199999999999,29.642,119.358,29.468,119.712,29.468C120.174,29.468,120.456,29.744,120.456,30.164C120.456,30.637999999999998,120.15,31.003999999999998,119.238,31.003999999999998L119.238,31.424C120.258,31.424,120.606,31.772,120.606,32.306C120.606,32.81,120.24,33.122,119.712,33.122C119.214,33.122,118.884,32.882,118.626,32.618L118.344,32.972C118.632,33.29,119.064,33.578,119.748,33.578ZM123.168,33.578C124.002,33.578,124.536,32.822,124.536,31.286C124.536,29.762,124.002,29.024,123.168,29.024C122.328,29.024,121.8,29.762,121.8,31.286C121.8,32.822,122.328,33.578,123.168,33.578ZM123.168,33.134C122.67,33.134,122.328,32.576,122.328,31.286C122.328,30.002,122.67,29.456,123.168,29.456C123.666,29.456,124.008,30.002,124.008,31.286C124.008,32.576,123.666,33.134,123.168,33.134ZM126.018,33.5L126.588,33.5C126.66,31.778,126.846,30.752,127.878,29.432L127.878,29.102L125.124,29.102L125.124,29.57L127.26,29.57C126.396,30.77,126.096,31.832,126.018,33.5ZM115.236,38.69C115.41,38.93,115.59,39.266,115.668,39.476L116.028,39.308C115.95,39.104,115.758,38.786,115.578,38.546L115.236,38.69ZM113.46,37.466L113.46,38.672L112.746,38.672L112.746,39.092L113.46,39.092L113.46,40.418C113.16,40.507999999999996,112.884,40.592,112.668,40.646L112.782,41.09L113.46,40.868L113.46,42.446C113.46,42.524,113.43,42.548,113.358,42.548C113.292,42.548,113.076,42.548,112.842,42.542C112.896,42.662,112.956,42.854,112.968,42.962C113.316,42.968,113.538,42.95,113.676,42.878C113.82,42.806,113.88,42.686,113.88,42.44L113.88,40.730000000000004L114.474,40.538L114.414,40.118L113.88,40.286L113.88,39.092L114.48,39.092L114.48,38.672L113.88,38.672L113.88,37.466L113.46,37.466ZM115.908,37.574C116.004,37.730000000000004,116.106,37.916,116.184,38.09L114.798,38.09L114.798,38.486000000000004L118.056,38.486000000000004L118.056,38.09L116.658,38.09C116.568,37.903999999999996,116.44200000000001,37.682,116.322,37.507999999999996L115.908,37.574ZM117.114,38.552C117.006,38.834,116.784,39.230000000000004,116.604,39.494L114.588,39.494L114.588,39.884L118.212,39.884L118.212,39.494L117.048,39.494C117.21,39.26,117.384,38.954,117.53999999999999,38.678L117.114,38.552ZM117.09,40.934C116.97,41.312,116.79,41.612,116.526,41.852000000000004C116.19,41.714,115.848,41.594,115.524,41.492000000000004C115.638,41.324,115.764,41.132,115.884,40.934L117.09,40.934ZM114.9,41.684C115.29,41.804,115.722,41.954,116.136,42.128C115.716,42.362,115.152,42.506,114.42,42.584C114.498,42.674,114.57,42.842,114.612,42.968C115.476,42.842,116.124,42.644,116.592,42.326C117.084,42.548,117.522,42.782,117.816,42.992000000000004L118.11,42.65C117.816,42.446,117.402,42.236000000000004,116.946,42.032C117.228,41.744,117.42,41.384,117.53999999999999,40.934L118.278,40.934L118.278,40.544L116.106,40.544C116.208,40.358000000000004,116.298,40.172,116.376,39.992000000000004L115.956,39.914C115.872,40.112,115.764,40.328,115.644,40.544L114.51,40.544L114.51,40.934L115.416,40.934C115.242,41.21,115.062,41.474000000000004,114.9,41.684ZM120.99,41.275999999999996C121.272,41.6,121.578,42.05,121.70400000000001,42.344L122.088,42.116C121.956,41.828,121.638,41.396,121.362,41.084L120.99,41.275999999999996ZM120.03,37.472C119.772,37.897999999999996,119.232,38.402,118.764,38.708C118.83,38.798,118.95,38.978,118.998,39.08C119.526,38.72,120.102,38.162,120.45,37.64L120.03,37.472ZM122.136,37.49L122.136,38.24L120.816,38.24L120.816,38.647999999999996L122.136,38.647999999999996L122.136,39.41L120.462,39.41L120.462,39.824L122.982,39.824L122.982,40.496L120.534,40.496L120.534,40.91L122.982,40.91L122.982,42.434C122.982,42.512,122.952,42.542,122.856,42.542C122.76,42.548,122.424,42.554,122.064,42.536C122.124,42.662,122.196,42.848,122.214,42.968C122.682,42.968,122.988,42.968,123.18,42.896C123.366,42.824,123.426,42.698,123.426,42.434L123.426,40.91L124.23,40.91L124.23,40.496L123.426,40.496L123.426,39.824L124.272,39.824L124.272,39.41L122.586,39.41L122.586,38.647999999999996L123.96000000000001,38.647999999999996L123.96000000000001,38.24L122.586,38.24L122.586,37.49L122.136,37.49ZM120.132,38.798C119.78999999999999,39.416,119.214,40.034,118.674,40.43C118.752,40.538,118.878,40.772,118.914,40.874C119.142,40.682,119.382,40.454,119.61,40.208L119.61,42.974000000000004L120.042,42.974000000000004L120.042,39.692C120.222,39.452,120.39,39.2,120.528,38.954L120.132,38.798ZM125.394,41.204L125.394,41.6L127.26599999999999,41.6L127.26599999999999,42.403999999999996L124.854,42.403999999999996L124.854,42.812L130.17000000000002,42.812L130.17000000000002,42.403999999999996L127.72800000000001,42.403999999999996L127.72800000000001,41.6L129.636,41.6L129.636,41.204L127.72800000000001,41.204L127.72800000000001,40.574L127.26599999999999,40.574L127.26599999999999,41.204L125.394,41.204ZM125.64,40.682C125.826,40.61,126.108,40.586,128.976,40.364000000000004C129.114,40.502,129.234,40.64,129.318,40.748L129.666,40.502C129.42000000000002,40.19,128.904,39.728,128.484,39.403999999999996L128.154,39.626C128.31,39.752,128.478,39.89,128.64,40.04L126.318,40.202C126.66,39.95,127.002,39.65,127.32,39.332L129.51,39.332L129.51,38.942L125.538,38.942L125.538,39.332L126.738,39.332C126.402,39.674,126.048,39.962,125.916,40.052C125.76,40.172,125.622,40.25,125.508,40.268C125.556,40.382,125.616,40.592,125.64,40.682ZM127.11,37.525999999999996C127.194,37.664,127.27799999999999,37.838,127.344,37.994L124.92,37.994L124.92,39.056L125.358,39.056L125.358,38.402L129.63,38.402L129.63,39.056L130.086,39.056L130.086,37.994L127.848,37.994C127.782,37.814,127.656,37.58,127.542,37.4L127.11,37.525999999999996Z"
                        fill="#333333" fill-opacity="0.6000000238418579" />
                </g>
                <g>
                    <path
                        d="M149.368,33.5L151.78,33.5L151.78,33.044L150.898,33.044L150.898,29.102L150.478,29.102C150.238,29.240000000000002,149.956,29.342,149.566,29.414L149.566,29.762L150.352,29.762L150.352,33.044L149.368,33.044L149.368,33.5ZM153.748,33.578C154.534,33.578,155.164,33.11,155.164,32.324C155.164,31.718,154.75,31.334,154.234,31.208L154.234,31.178C154.702,31.016,155.014,30.656,155.014,30.122C155.014,29.426000000000002,154.474,29.024,153.73,29.024C153.226,29.024,152.836,29.246,152.506,29.546L152.8,29.894C153.052,29.642,153.358,29.468,153.712,29.468C154.174,29.468,154.456,29.744,154.456,30.164C154.456,30.637999999999998,154.15,31.003999999999998,153.238,31.003999999999998L153.238,31.424C154.258,31.424,154.606,31.772,154.606,32.306C154.606,32.81,154.24,33.122,153.712,33.122C153.214,33.122,152.88400000000001,32.882,152.626,32.618L152.344,32.972C152.632,33.29,153.064,33.578,153.748,33.578ZM157.168,33.578C158.002,33.578,158.536,32.822,158.536,31.286C158.536,29.762,158.002,29.024,157.168,29.024C156.328,29.024,155.8,29.762,155.8,31.286C155.8,32.822,156.328,33.578,157.168,33.578ZM157.168,33.134C156.67,33.134,156.328,32.576,156.328,31.286C156.328,30.002,156.67,29.456,157.168,29.456C157.666,29.456,158.008,30.002,158.008,31.286C158.008,32.576,157.666,33.134,157.168,33.134ZM160.51,33.578C161.332,33.578,161.88400000000001,33.08,161.88400000000001,32.444C161.88400000000001,31.838,161.53,31.508,161.14600000000002,31.286L161.14600000000002,31.256C161.404,31.052,161.728,30.656,161.728,30.194C161.728,29.516,161.272,29.036,160.522,29.036C159.838,29.036,159.316,29.486,159.316,30.152C159.316,30.614,159.59199999999998,30.944,159.91,31.166L159.91,31.19C159.508,31.406,159.106,31.82,159.106,32.408C159.106,33.086,159.694,33.578,160.51,33.578ZM160.81,31.112000000000002C160.288,30.908,159.814,30.674,159.814,30.152C159.814,29.726,160.108,29.444,160.516,29.444C160.984,29.444,161.26,29.786,161.26,30.224C161.26,30.548000000000002,161.10399999999998,30.848,160.81,31.112000000000002ZM160.516,33.17C159.988,33.17,159.59199999999998,32.828,159.59199999999998,32.36C159.59199999999998,31.94,159.844,31.592,160.198,31.364C160.822,31.616,161.362,31.832,161.362,32.426C161.362,32.864,161.026,33.17,160.516,33.17ZM149.236,38.69C149.41,38.93,149.59,39.266,149.668,39.476L150.028,39.308C149.95,39.104,149.758,38.786,149.578,38.546L149.236,38.69ZM147.46,37.466L147.46,38.672L146.746,38.672L146.746,39.092L147.46,39.092L147.46,40.418C147.16,40.507999999999996,146.884,40.592,146.668,40.646L146.782,41.09L147.46,40.868L147.46,42.446C147.46,42.524,147.43,42.548,147.358,42.548C147.292,42.548,147.076,42.548,146.842,42.542C146.896,42.662,146.956,42.854,146.968,42.962C147.316,42.968,147.538,42.95,147.676,42.878C147.82,42.806,147.88,42.686,147.88,42.44L147.88,40.730000000000004L148.474,40.538L148.414,40.118L147.88,40.286L147.88,39.092L148.48,39.092L148.48,38.672L147.88,38.672L147.88,37.466L147.46,37.466ZM149.908,37.574C150.004,37.730000000000004,150.106,37.916,150.184,38.09L148.798,38.09L148.798,38.486000000000004L152.056,38.486000000000004L152.056,38.09L150.658,38.09C150.568,37.903999999999996,150.442,37.682,150.322,37.507999999999996L149.908,37.574ZM151.114,38.552C151.006,38.834,150.784,39.230000000000004,150.604,39.494L148.588,39.494L148.588,39.884L152.212,39.884L152.212,39.494L151.048,39.494C151.21,39.26,151.38400000000001,38.954,151.54,38.678L151.114,38.552ZM151.09,40.934C150.97,41.312,150.79,41.612,150.526,41.852000000000004C150.19,41.714,149.848,41.594,149.524,41.492000000000004C149.638,41.324,149.764,41.132,149.88400000000001,40.934L151.09,40.934ZM148.9,41.684C149.29,41.804,149.722,41.954,150.136,42.128C149.716,42.362,149.152,42.506,148.42,42.584C148.498,42.674,148.57,42.842,148.612,42.968C149.476,42.842,150.124,42.644,150.592,42.326C151.084,42.548,151.522,42.782,151.816,42.992000000000004L152.11,42.65C151.816,42.446,151.402,42.236000000000004,150.946,42.032C151.228,41.744,151.42,41.384,151.54,40.934L152.278,40.934L152.278,40.544L150.106,40.544C150.208,40.358000000000004,150.298,40.172,150.376,39.992000000000004L149.956,39.914C149.872,40.112,149.764,40.328,149.644,40.544L148.51,40.544L148.51,40.934L149.416,40.934C149.242,41.21,149.062,41.474000000000004,148.9,41.684ZM154.99,41.275999999999996C155.272,41.6,155.578,42.05,155.704,42.344L156.088,42.116C155.956,41.828,155.638,41.396,155.362,41.084L154.99,41.275999999999996ZM154.03,37.472C153.772,37.897999999999996,153.232,38.402,152.764,38.708C152.83,38.798,152.95,38.978,152.998,39.08C153.526,38.72,154.102,38.162,154.45,37.64L154.03,37.472ZM156.136,37.49L156.136,38.24L154.816,38.24L154.816,38.647999999999996L156.136,38.647999999999996L156.136,39.41L154.462,39.41L154.462,39.824L156.982,39.824L156.982,40.496L154.534,40.496L154.534,40.91L156.982,40.91L156.982,42.434C156.982,42.512,156.952,42.542,156.856,42.542C156.76,42.548,156.424,42.554,156.064,42.536C156.124,42.662,156.196,42.848,156.214,42.968C156.682,42.968,156.988,42.968,157.18,42.896C157.36599999999999,42.824,157.426,42.698,157.426,42.434L157.426,40.91L158.23,40.91L158.23,40.496L157.426,40.496L157.426,39.824L158.272,39.824L158.272,39.41L156.586,39.41L156.586,38.647999999999996L157.96,38.647999999999996L157.96,38.24L156.586,38.24L156.586,37.49L156.136,37.49ZM154.132,38.798C153.79,39.416,153.214,40.034,152.674,40.43C152.752,40.538,152.878,40.772,152.914,40.874C153.142,40.682,153.382,40.454,153.61,40.208L153.61,42.974000000000004L154.042,42.974000000000004L154.042,39.692C154.222,39.452,154.39,39.2,154.528,38.954L154.132,38.798ZM159.394,41.204L159.394,41.6L161.266,41.6L161.266,42.403999999999996L158.85399999999998,42.403999999999996L158.85399999999998,42.812L164.17000000000002,42.812L164.17000000000002,42.403999999999996L161.728,42.403999999999996L161.728,41.6L163.636,41.6L163.636,41.204L161.728,41.204L161.728,40.574L161.266,40.574L161.266,41.204L159.394,41.204ZM159.64,40.682C159.826,40.61,160.108,40.586,162.976,40.364000000000004C163.114,40.502,163.234,40.64,163.318,40.748L163.666,40.502C163.42000000000002,40.19,162.904,39.728,162.484,39.403999999999996L162.154,39.626C162.31,39.752,162.478,39.89,162.64,40.04L160.318,40.202C160.66,39.95,161.002,39.65,161.32,39.332L163.51,39.332L163.51,38.942L159.538,38.942L159.538,39.332L160.738,39.332C160.402,39.674,160.048,39.962,159.916,40.052C159.76,40.172,159.622,40.25,159.508,40.268C159.556,40.382,159.61599999999999,40.592,159.64,40.682ZM161.11,37.525999999999996C161.194,37.664,161.278,37.838,161.344,37.994L158.92,37.994L158.92,39.056L159.358,39.056L159.358,38.402L163.63,38.402L163.63,39.056L164.086,39.056L164.086,37.994L161.848,37.994C161.782,37.814,161.656,37.58,161.542,37.4L161.11,37.525999999999996Z"
                        fill="#333333" fill-opacity="0.6000000238418579" />
                </g>
                <g>
                    <path
                        d="M252.368,61.5L254.78,61.5L254.78,61.044L253.898,61.044L253.898,57.102L253.478,57.102C253.238,57.24,252.956,57.342,252.566,57.414L252.566,57.762L253.352,57.762L253.352,61.044L252.368,61.044L252.368,61.5ZM256.748,61.578C257.534,61.578,258.164,61.11,258.164,60.324C258.164,59.718,257.75,59.334,257.234,59.208L257.234,59.178C257.702,59.016,258.014,58.656,258.014,58.122C258.014,57.426,257.474,57.024,256.73,57.024C256.226,57.024,255.836,57.246,255.506,57.546L255.8,57.894C256.052,57.642,256.358,57.468,256.712,57.468C257.174,57.468,257.456,57.744,257.456,58.164C257.456,58.638,257.15,59.004,256.238,59.004L256.238,59.424C257.258,59.424,257.606,59.772,257.606,60.306C257.606,60.81,257.24,61.122,256.712,61.122C256.214,61.122,255.88400000000001,60.882,255.626,60.618L255.344,60.972C255.632,61.29,256.064,61.578,256.748,61.578ZM260.168,61.578C261.002,61.578,261.536,60.822,261.536,59.286C261.536,57.762,261.002,57.024,260.168,57.024C259.328,57.024,258.8,57.762,258.8,59.286C258.8,60.822,259.328,61.578,260.168,61.578ZM260.168,61.134C259.67,61.134,259.328,60.576,259.328,59.286C259.328,58.002,259.67,57.456,260.168,57.456C260.666,57.456,261.008,58.002,261.008,59.286C261.008,60.576,260.666,61.134,260.168,61.134ZM262.358,61.5L264.77,61.5L264.77,61.044L263.888,61.044L263.888,57.102L263.468,57.102C263.228,57.24,262.946,57.342,262.556,57.414L262.556,57.762L263.342,57.762L263.342,61.044L262.358,61.044L262.358,61.5ZM250.442,70.848C250.67,70.764,251.006,70.74,254.186,70.47C254.324,70.65,254.444,70.824,254.528,70.974L254.93,70.72800000000001C254.666,70.278,254.096,69.63,253.556,69.15L253.178,69.354C253.412,69.57,253.652,69.822,253.868,70.074L251.138,70.284C251.564,69.888,251.99,69.408,252.362,68.916L255.008,68.916L255.008,68.478L250.034,68.478L250.034,68.916L251.75,68.916C251.36,69.45,250.904,69.924,250.742,70.068C250.556,70.242,250.418,70.356,250.286,70.386C250.34,70.506,250.418,70.746,250.442,70.848ZM252.524,65.46000000000001C251.984,66.264,250.928,67.026,249.752,67.524C249.86,67.608,250.016,67.8,250.082,67.914C250.43,67.752,250.766,67.572,251.084,67.374L251.084,67.74L253.946,67.74L253.946,67.32L251.162,67.32C251.678,66.984,252.14,66.606,252.518,66.19200000000001C252.878,66.564,253.382,66.972,253.946,67.32C254.27,67.524,254.618,67.70400000000001,254.96,67.842C255.032,67.722,255.182,67.536,255.278,67.446C254.306,67.11,253.328,66.456,252.776,65.886L252.956,65.646L252.524,65.46000000000001ZM258.752,65.742C258.992,66.144,259.244,66.678,259.34,67.008L259.748,66.822C259.652,66.492,259.382,65.976,259.13,65.58L258.752,65.742ZM256.178,65.874C256.448,66.156,256.772,66.55199999999999,256.928,66.804L257.27,66.522C257.114,66.276,256.778,65.904,256.502,65.628L256.178,65.874ZM260.492,65.832C260.294,67.08,259.98199999999997,68.202,259.34,69.102C258.734,68.262,258.368,67.176,258.152,65.904L257.726,65.976C257.984,67.398,258.374,68.58,259.034,69.48C258.614,69.95400000000001,258.068,70.35,257.366,70.65C257.45,70.746,257.576,70.914,257.636,71.02199999999999C258.332,70.71000000000001,258.884,70.30199999999999,259.322,69.828C259.772,70.332,260.336,70.72200000000001,261.032,70.998C261.104,70.878,261.248,70.69800000000001,261.356,70.608C260.648,70.356,260.084,69.966,259.628,69.462C260.354,68.49,260.714,67.272,260.954,65.904L260.492,65.832ZM255.776,67.338L255.776,67.776L256.622,67.776L256.622,69.894C256.622,70.206,256.46,70.41,256.364,70.506C256.442,70.572,256.574,70.72800000000001,256.622,70.824C256.718,70.70400000000001,256.874,70.584,257.93,69.834C257.882,69.744,257.816,69.57,257.78,69.45L257.06,69.94800000000001L257.06,67.338L255.776,67.338ZM262.394,69.20400000000001L262.394,69.6L264.266,69.6L264.266,70.404L261.854,70.404L261.854,70.812L267.17,70.812L267.17,70.404L264.728,70.404L264.728,69.6L266.636,69.6L266.636,69.20400000000001L264.728,69.20400000000001L264.728,68.574L264.266,68.574L264.266,69.20400000000001L262.394,69.20400000000001ZM262.64,68.682C262.826,68.61,263.108,68.586,265.976,68.364C266.114,68.502,266.234,68.64,266.318,68.748L266.666,68.502C266.42,68.19,265.904,67.728,265.484,67.404L265.154,67.626C265.31,67.752,265.478,67.89,265.64,68.03999999999999L263.318,68.202C263.66,67.95,264.002,67.65,264.32,67.332L266.51,67.332L266.51,66.94200000000001L262.538,66.94200000000001L262.538,67.332L263.738,67.332C263.402,67.674,263.048,67.962,262.916,68.05199999999999C262.76,68.172,262.622,68.25,262.508,68.268C262.556,68.382,262.616,68.592,262.64,68.682ZM264.11,65.526C264.194,65.664,264.278,65.838,264.344,65.994L261.92,65.994L261.92,67.056L262.358,67.056L262.358,66.402L266.63,66.402L266.63,67.056L267.086,67.056L267.086,65.994L264.848,65.994C264.782,65.814,264.656,65.58,264.542,65.4L264.11,65.526Z"
                        fill="#333333" fill-opacity="1" />
                </g>
                <g id="room-130234" class="meeting-room" @click.stop="selectRoom('130234')"
                    :class="{ selected: selected === '130234' }">
                    <path
                        d="M86.5,143.5L86.5,143.441Q101.628,144.8602,116.5,145.5L116.5,146.2227Q120.4394,146.38670000000002,124.3953,146.5L160.171,146.5Q166.075,146.3339,171.5,146.0592L171.5,145.5Q201.545,144.0852,231.871,140L231.871,81.5L55.5,81.5L55.5,139.8039Q70.8673,142.0554,86.5,143.5Z"
                        fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1" />
                    <path
                        d="M55.35,139.8039Q55.35,139.83100000000002,55.359423,139.8563Q55.368845,139.8816,55.386506,139.902Q55.4041677,139.9225,55.4278489,139.9355Q55.4515301,139.9484,55.478256,139.9524Q70.8494,142.2047,86.4862,143.6494Q86.5481,143.6551,86.5959,143.6154Q86.6034,143.6091,86.61,143.602Q101.61410000000001,145.0063,116.35,145.6437L116.35,146.2227Q116.35,146.2517,116.3608,146.27870000000001Q116.3717,146.3056,116.3918,146.32659999999998Q116.4118,146.3475,116.4383,146.3594Q116.4648,146.3714,116.4938,146.3726Q120.4306,146.5365,124.391,146.6499L124.3953,146.65L160.171,146.65L160.175,146.6499Q166.082,146.4838,171.50799999999998,146.209Q171.536,146.20749999999998,171.563,146.19549999999998Q171.589,146.18349999999998,171.60899999999998,146.1626Q171.62900000000002,146.14159999999998,171.639,146.1148Q171.65,146.088,171.65,146.0592L171.65,145.6431Q201.631,144.22469999999998,231.891,140.1487Q231.918,140.145,231.942,140.1321Q231.966,140.1192,231.984,140.0987Q232.002,140.0782,232.012,140.05270000000002Q232.021,140.0272,232.021,140L232.021,81.5Q232.021,81.4701631,232.01,81.4425975Q231.998,81.4150318,231.977,81.393934Q231.956,81.372836,231.928,81.361418Q231.901,81.35,231.871,81.35L55.5,81.35Q55.4701631,81.35,55.4425975,81.361418Q55.4150318,81.372836,55.393934,81.393934Q55.372836,81.4150318,55.361418,81.4425975Q55.35,81.4701631,55.35,81.5L55.35,139.8039ZM86.3899,143.3392Q86.4344,143.291,86.5,143.291Q86.507,143.291,86.514,143.2917Q101.6469,144.7109,116.5064,145.3501Q116.53540000000001,145.3514,116.5618,145.3633Q116.5883,145.3753,116.6083,145.3962Q116.6284,145.4172,116.6392,145.4441Q116.65,145.471,116.65,145.5L116.65,146.0788Q120.5116,146.23860000000002,124.3953,146.3499L124.3975,146.35L160.171,146.35Q165.994,146.1861,171.35,145.91660000000002L171.35,145.5Q171.35,145.4711,171.361,145.4442Q171.372,145.4174,171.39100000000002,145.3965Q171.411,145.3755,171.438,145.3635Q171.464,145.3515,171.493,145.3502Q201.468,143.9382,231.721,139.8688L231.721,81.65L55.65,81.65L55.65,139.67430000000002Q70.8891,141.9038,86.3899,143.3392Z"
                        fill-rule="evenodd" fill="#000000" fill-opacity="1" />
                </g>
                <g transform="matrix(-1,0,0,1,237,0)">
                    <path
                        d="M131.4942,81.8C131.4981,81.70041,131.5,81.60041,131.5,81.5L125,81.5L125,88.5C125.10045,88.5,125.20045,88.4976,125.3,88.4928C126.05475,88.4562,126.7839,88.281,127.48743999999999,87.9672C128.28378,87.6119,128.9867,87.1061,129.5962,86.4497Q130.5104,85.4652,131.0052,84.17878C131.2994,83.41396,131.4624,82.62103,131.4942,81.8ZM131.194,81.8Q131.1465,82.97581,130.7252,84.07109Q130.2512,85.3035,129.3764,86.2456Q128.5038,87.1853,127.36523,87.6932Q126.36836,88.1379,125.3,88.19239999999999L125.3,81.8L131.194,81.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M183.4942,81.8C183.4981,81.70041,183.5,81.60041,183.5,81.5L177,81.5L177,88.5C177.10045,88.5,177.20045,88.4976,177.3,88.4928C178.05475,88.4562,178.7839,88.281,179.48744,87.9672C180.28378,87.6119,180.9867,87.1061,181.5962,86.4497Q182.5104,85.4652,183.0052,84.17878C183.2994,83.41396,183.4624,82.62103,183.4942,81.8ZM183.194,81.8Q183.1465,82.97581,182.7252,84.07109Q182.2512,85.3035,181.3764,86.2456Q180.5038,87.1853,179.36523,87.6932Q178.36836,88.1379,177.3,88.19239999999999L177.3,81.8L183.194,81.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g transform="matrix(-1,0,0,1,459,0)">
                    <path
                        d="M242.4942,81.8C242.4981,81.70041,242.5,81.60041,242.5,81.5L236,81.5L236,88.5C236.10045,88.5,236.20045,88.4976,236.3,88.4928C237.05475,88.4562,237.7839,88.281,238.48744,87.9672C239.28378,87.6119,239.9867,87.1061,240.5962,86.4497Q241.5104,85.4652,242.0052,84.17878C242.2994,83.41396,242.4624,82.62103,242.4942,81.8ZM242.194,81.8Q242.1465,82.97581,241.7252,84.07109Q241.2512,85.3035,240.3764,86.2456Q239.5038,87.1853,238.36523,87.6932Q237.36836,88.1379,236.3,88.19239999999999L236.3,81.8L242.194,81.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M129.4942,81.8C129.4981,81.70041,129.5,81.60041,129.5,81.5L123,81.5L123,88.5C123.10045,88.5,123.20045,88.4976,123.3,88.4928C124.05475,88.4562,124.7839,88.281,125.48743999999999,87.9672C126.28378000000001,87.6119,126.9867,87.1061,127.5962,86.4497Q128.5104,85.4652,129.0052,84.17878C129.2994,83.41396,129.4624,82.62103,129.4942,81.8ZM129.194,81.8Q129.1465,82.97581,128.7252,84.07109Q128.2512,85.3035,127.3764,86.2456Q126.5038,87.1853,125.36523,87.6932Q124.36836,88.1379,123.3,88.19239999999999L123.3,81.8L129.194,81.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M68.4942,81.8C68.4981,81.70041,68.5,81.60041,68.5,81.5L62,81.5L62,88.5C62.10045,88.5,62.200450000000004,88.4976,62.3,88.4928C63.05475,88.4562,63.7839,88.281,64.48743999999999,87.9672C65.28378000000001,87.6119,65.9867,87.1061,66.5962,86.4497Q67.5104,85.4652,68.0052,84.17878C68.2994,83.41396,68.4624,82.62103,68.4942,81.8ZM68.194,81.8Q68.1465,82.97581,67.7252,84.07109Q67.2512,85.3035,66.3764,86.2456Q65.5038,87.1853,64.36523,87.6932Q63.36836,88.1379,62.3,88.19239999999999L62.3,81.8L68.194,81.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g transform="matrix(-1,0,0,1,343,0)">
                    <path
                        d="M184.4942,81.8C184.4981,81.70041,184.5,81.60041,184.5,81.5L178,81.5L178,88.5C178.10045,88.5,178.20045,88.4976,178.3,88.4928C179.05475,88.4562,179.7839,88.281,180.48744,87.9672C181.28378,87.6119,181.9867,87.1061,182.5962,86.4497Q183.5104,85.4652,184.0052,84.17878C184.2994,83.41396,184.4624,82.62103,184.4942,81.8ZM184.194,81.8Q184.1465,82.97581,183.7252,84.07109Q183.2512,85.3035,182.3764,86.2456Q181.5038,87.1853,180.36523,87.6932Q179.36836,88.1379,178.3,88.19239999999999L178.3,81.8L184.194,81.8Z"
                        fill-rule="evenodd" fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M139.254,128.64L139.254,129.168C139.254,129.624,139.074,130.23,136.896,130.638C136.998,130.728,137.124,130.89,137.178,130.98C139.44,130.512,139.722,129.78,139.722,129.174L139.722,128.64L139.254,128.64ZM139.656,130.11C140.406,130.338,141.378,130.71,141.882,130.98L142.11599999999999,130.608C141.6,130.338,140.61,129.984,139.878,129.786L139.656,130.11ZM137.652,128.01L137.652,129.942L138.102,129.942L138.102,128.37L140.958,128.37L140.958,129.906L141.426,129.906L141.426,128.01L137.652,128.01ZM138.264,126.042L139.434,126.042L139.362,126.51L138.174,126.51L138.264,126.042ZM139.86,126.042L141.024,126.042L140.97,126.51L139.788,126.51L139.86,126.042ZM139.212,127.32L138.012,127.32L138.108,126.828L139.308,126.828L139.212,127.32ZM139.644,127.32L139.734,126.828L140.934,126.828L140.88,127.32L139.644,127.32ZM136.842,126.48L136.842,126.858L137.652,126.858L137.502,127.686L141.3,127.686L141.396,126.858L142.158,126.858L142.158,126.48L141.438,126.48L141.528,125.682L137.868,125.682L137.724,126.48L136.842,126.48ZM142.89,125.958C143.244,126.27,143.7,126.708,143.91,126.99L144.24,126.69C144.018,126.414,143.556,125.994,143.202,125.7L142.89,125.958ZM144.036,127.71L142.758,127.71L142.758,128.136L143.604,128.136L143.604,129.84C143.34,129.948,143.04,130.218,142.734,130.548L143.016,130.92C143.322,130.512,143.61599999999999,130.164,143.82,130.164C143.958,130.164,144.162,130.368,144.408,130.518C144.828,130.77,145.326,130.842,146.07,130.842C146.718,130.842,147.768,130.812,148.188,130.782C148.194,130.662,148.266,130.458,148.314,130.344C147.696,130.404,146.784,130.452,146.076,130.452C145.41,130.452,144.9,130.41,144.498,130.164C144.288,130.026,144.156,129.918,144.036,129.852L144.036,127.71ZM144.684,125.682L144.684,126.036L147.222,126.036C146.976,126.222,146.67,126.408,146.37,126.552C146.076,126.42,145.764,126.294,145.494,126.198L145.206,126.456C145.578,126.594,146.016,126.786,146.382,126.966L144.678,126.966L144.678,130.074L145.10399999999998,130.074L145.10399999999998,129.078L146.118,129.078L146.118,130.05L146.526,130.05L146.526,129.078L147.57,129.078L147.57,129.624C147.57,129.696,147.546,129.72,147.468,129.726C147.39600000000002,129.726,147.144,129.726,146.856,129.72C146.91,129.822,146.964,129.972,146.982,130.086C147.38400000000001,130.086,147.642,130.086,147.798,130.02C147.954,129.954,148.002,129.846,148.002,129.624L148.002,126.966L147.216,126.966C147.096,126.894,146.946,126.816,146.772,126.732C147.222,126.498,147.678,126.186,148.002,125.874L147.72,125.658L147.63,125.682L144.684,125.682ZM147.57,127.314L147.57,127.842L146.526,127.842L146.526,127.314L147.57,127.314ZM145.10399999999998,128.178L146.118,128.178L146.118,128.724L145.10399999999998,128.724L145.10399999999998,128.178ZM145.10399999999998,127.842L145.10399999999998,127.314L146.118,127.314L146.118,127.842L145.10399999999998,127.842ZM147.57,128.178L147.57,128.724L146.526,128.724L146.526,128.178L147.57,128.178ZM150.306,130.578C150.99,130.578,151.572,130.002,151.572,129.15C151.572,128.226,151.092,127.77,150.348,127.77C150.006,127.77,149.622,127.968,149.352,128.298C149.376,126.936,149.874,126.474,150.486,126.474C150.75,126.474,151.014,126.606,151.182,126.81L151.494,126.474C151.248,126.21,150.918,126.024,150.462,126.024C149.61,126.024,148.836,126.678,148.836,128.4C148.836,129.852,149.466,130.578,150.306,130.578ZM149.364,128.736C149.652,128.328,149.988,128.178,150.258,128.178C150.792,128.178,151.05,128.556,151.05,129.15C151.05,129.75,150.726,130.146,150.306,130.146C149.754,130.146,149.424,129.648,149.364,128.736ZM153.498,130.578C154.332,130.578,154.86599999999999,129.822,154.86599999999999,128.286C154.86599999999999,126.762,154.332,126.024,153.498,126.024C152.65800000000002,126.024,152.13,126.762,152.13,128.286C152.13,129.822,152.65800000000002,130.578,153.498,130.578ZM153.498,130.13400000000001C153,130.13400000000001,152.65800000000002,129.576,152.65800000000002,128.286C152.65800000000002,127.002,153,126.456,153.498,126.456C153.996,126.456,154.338,127.002,154.338,128.286C154.338,129.576,153.996,130.13400000000001,153.498,130.13400000000001ZM157.902,125.478C157.88400000000001,126.402,157.92000000000002,129.336,155.418,130.602C155.556,130.698,155.7,130.842,155.784,130.956C157.254,130.17,157.89,128.826,158.172,127.62C158.466,128.742,159.114,130.224,160.62,130.932C160.692,130.806,160.824,130.65,160.95,130.554C158.826,129.6,158.454,127.086,158.364,126.366C158.394,126.006,158.4,125.7,158.406,125.478L157.902,125.478Z"
                        fill="#999999" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M255.764,79.5L258.53,79.5L258.53,79.026L257.312,79.026C257.09,79.026,256.82,79.05,256.592,79.068C257.624,78.09,258.32,77.196,258.32,76.314C258.32,75.534,257.822,75.024,257.036,75.024C256.478,75.024,256.094,75.276,255.74,75.666L256.058,75.978C256.304,75.684,256.61,75.468,256.97,75.468C257.516,75.468,257.78,75.834,257.78,76.338C257.78,77.094,257.144,77.97,255.764,79.176L255.764,79.5ZM260.51,79.578C261.332,79.578,261.884,79.08,261.884,78.444C261.884,77.838,261.53,77.508,261.146,77.286L261.146,77.256C261.404,77.052,261.728,76.656,261.728,76.194C261.728,75.516,261.272,75.036,260.522,75.036C259.838,75.036,259.316,75.486,259.316,76.152C259.316,76.614,259.592,76.944,259.91,77.166L259.91,77.19C259.508,77.406,259.106,77.82,259.106,78.408C259.106,79.086,259.694,79.578,260.51,79.578ZM260.81,77.112C260.288,76.908,259.814,76.674,259.814,76.152C259.814,75.726,260.108,75.444,260.516,75.444C260.984,75.444,261.26,75.786,261.26,76.224C261.26,76.548,261.104,76.848,260.81,77.112ZM260.516,79.17C259.988,79.17,259.592,78.828,259.592,78.36C259.592,77.94,259.844,77.592,260.198,77.364C260.822,77.616,261.362,77.832,261.362,78.426C261.362,78.864,261.026,79.17,260.516,79.17ZM264.902,74.478C264.884,75.402,264.92,78.336,262.418,79.602C262.556,79.69800000000001,262.7,79.842,262.784,79.956C264.254,79.17,264.89,77.826,265.172,76.62C265.466,77.742,266.114,79.224,267.62,79.932C267.692,79.806,267.824,79.65,267.95,79.554C265.826,78.6,265.454,76.086,265.364,75.366C265.394,75.006,265.4,74.7,265.406,74.478L264.902,74.478Z"
                        fill="#999999" fill-opacity="1" />
                </g>
                <g clip-path="url(#master_svg0_102_01460)">
                    <g>
                        <path
                            d="M132.2473334125328,127.4533989577961L132.2473334125328,127.3597989577961C132.9056734125328,126.8853389577961,133.0069734125328,125.9446389577961,132.46475341253281,125.3408919577961C131.9225234125328,124.7371409577961,130.97639341253281,124.7371409577961,130.4341634125328,125.3408929577961C129.8919424125328,125.9446389577961,129.9932424125328,126.8853389577961,130.6515734125328,127.3597989577961L130.6515734125328,127.44043895779609C129.7596364125328,127.8003489577961,129.1789788425328,128.6696289577961,129.1880582405328,129.6313989577961C129.21540441253282,129.8570389577961,129.4178304125328,130.0196189577961,129.64405841253281,129.99763895779608L133.2217334125328,129.99763895779608C133.4479634125328,130.0196189577961,133.6503934125328,129.8570389577961,133.6777334125328,129.6313989577961C133.6868634125328,128.6826089577961,133.1220234125328,127.8222589577961,132.2478134125328,127.4533989577961L132.2473334125328,127.4533989577961Z"
                            fill="#CBCDD0" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M134.17819073660507,129.1404106890297L134.15755073660506,129.1404106890297C134.15901073660507,129.1295106890297,134.15973073660507,129.11853068902968,134.15971073660506,129.1075306890297C134.19744073660507,128.4100606890297,133.82135073660507,127.7556606890297,133.19971073660506,127.43713068902969L133.19971073660506,127.36609068902969C133.45437073660506,127.16291068902969,133.60160073660506,126.8540206890297,133.59907073660506,126.52825068902969C133.61126073660506,126.06212968902969,133.30443073660507,125.6476136890297,132.85507073660506,125.5231307890297C132.92690073660506,125.5052404790297,133.00065073660505,125.4962133813297,133.07467073660507,125.49625080347069C133.62505073660506,125.5165748890297,134.05558073660507,125.9777766890297,134.03803073660507,126.52825068902969C134.04031073660505,126.8540606890297,133.89281073660507,127.16287068902969,133.63795073660506,127.3658506890297C133.92573073660506,127.5107706890297,134.14909073660507,127.75784068902969,134.26435073660505,128.0587306890297C134.37695073660507,128.3155606890297,134.45616073660506,128.58576068902968,134.50003073660505,128.8627306890297C134.48687073660506,129.0278006890297,134.34340073660508,129.1515806890297,134.17819073660507,129.1404106890297ZM128.82163073660507,129.1408906890297L128.84227073660506,129.1408906890297C128.84080773660506,129.12999068902968,128.84008573660506,129.1190106890297,128.84011073660506,129.1080106890297C128.80238173660507,128.4105406890297,129.17847573660507,127.75614068902969,129.80011073660506,127.4376106890297L129.80011073660506,127.36681068902969C129.54552073660506,127.16368068902969,129.39829173660507,126.85490068902969,129.40075073660506,126.5292106890297C129.38834173660507,126.0629136890297,129.69522073660505,125.6481376890297,130.14475073660506,125.5236111890297C130.07292073660506,125.50572101902969,129.99917073660507,125.49669380402969,129.92515073660508,125.4967312260297C129.37477173660506,125.51705548902969,128.94424573660507,125.97825668902969,128.96179073660505,126.5287306890297C128.95950673660505,126.85454068902969,129.10700773660506,127.1633506890297,129.36187073660506,127.3663306890297C129.07409273660505,127.5112506890297,128.85072973660508,127.75832068902969,128.73547073660507,128.0592106890297C128.62287073660505,128.3160406890297,128.54366463660506,128.5862406890297,128.49979073660506,128.8632106890297C128.51294993660505,129.02828068902969,128.65641873660508,129.1520606890297,128.82163073660507,129.1408906890297Z"
                            fill="#CBCDD0" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g clip-path="url(#master_svg1_102_01774)">
                    <g>
                        <path
                            d="M251.2473334125328,76.4533989577961L251.2473334125328,76.3597989577961C251.9056734125328,75.8853389577961,252.0069734125328,74.9446389577961,251.46475341253281,74.3408919577961C250.9225234125328,73.7371409577961,249.97639341253281,73.7371409577961,249.4341634125328,74.3408929577961C248.8919424125328,74.9446389577961,248.9932424125328,75.8853389577961,249.6515734125328,76.3597989577961L249.6515734125328,76.44043895779609C248.7596364125328,76.8003489577961,248.1789788425328,77.66962895779609,248.1880582405328,78.6313989577961C248.21540441253282,78.85703895779609,248.4178304125328,79.0196189577961,248.64405841253281,78.9976389577961L252.2217334125328,78.9976389577961C252.4479634125328,79.0196189577961,252.6503934125328,78.85703895779609,252.6777334125328,78.6313989577961C252.6868634125328,77.68260895779609,252.1220234125328,76.8222589577961,251.2478134125328,76.4533989577961L251.2473334125328,76.4533989577961Z"
                            fill="#CBCDD0" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                    <g>
                        <path
                            d="M253.17819073660507,78.1404106890297L253.15755073660506,78.1404106890297C253.15901073660507,78.1295106890297,253.15973073660507,78.1185306890297,253.15971073660506,78.10753068902969C253.19744073660507,77.41006068902969,252.82135073660507,76.7556606890297,252.19971073660506,76.43713068902969L252.19971073660506,76.36609068902969C252.45437073660506,76.16291068902969,252.60160073660506,75.8540206890297,252.59907073660506,75.52825068902969C252.61126073660506,75.06212968902969,252.30443073660507,74.6476136890297,251.85507073660506,74.5231307890297C251.92690073660506,74.5052404790297,252.00065073660505,74.4962133813297,252.07467073660507,74.49625080347069C252.62505073660506,74.5165748890297,253.05558073660507,74.9777766890297,253.03803073660507,75.52825068902969C253.04031073660505,75.8540606890297,252.89281073660507,76.16287068902969,252.63795073660506,76.3658506890297C252.92573073660506,76.5107706890297,253.14909073660507,76.75784068902969,253.26435073660505,77.05873068902969C253.37695073660507,77.3155606890297,253.45616073660506,77.5857606890297,253.50003073660505,77.86273068902969C253.48687073660506,78.02780068902969,253.34340073660508,78.1515806890297,253.17819073660507,78.1404106890297ZM247.82163073660507,78.14089068902969L247.84227073660506,78.14089068902969C247.84080773660506,78.1299906890297,247.84008573660506,78.1190106890297,247.84011073660506,78.1080106890297C247.80238173660507,77.41054068902969,248.17847573660507,76.75614068902969,248.80011073660506,76.4376106890297L248.80011073660506,76.36681068902969C248.54552073660506,76.16368068902969,248.39829173660507,75.85490068902969,248.40075073660506,75.5292106890297C248.38834173660507,75.0629136890297,248.69522073660505,74.6481376890297,249.14475073660506,74.5236111890297C249.07292073660506,74.50572101902969,248.99917073660507,74.49669380402969,248.92515073660508,74.4967312260297C248.37477173660506,74.51705548902969,247.94424573660507,74.97825668902969,247.96179073660505,75.5287306890297C247.95950673660505,75.85454068902969,248.10700773660506,76.1633506890297,248.36187073660506,76.3663306890297C248.07409273660505,76.5112506890297,247.85072973660508,76.75832068902969,247.73547073660507,77.0592106890297C247.62287073660505,77.31604068902969,247.54366463660506,77.5862406890297,247.49979073660506,77.8632106890297C247.51294993660505,78.0282806890297,247.65641873660508,78.1520606890297,247.82163073660507,78.14089068902969Z"
                            fill="#CBCDD0" fill-opacity="1" style="mix-blend-mode:passthrough" />
                    </g>
                </g>
                <g>
                    <path
                        d="M124.195999,111.5L126.608,111.5L126.608,111.044L125.726,111.044L125.726,107.102L125.306,107.102C125.066,107.24,124.784,107.342,124.393999,107.414L124.393999,107.762L125.18,107.762L125.18,111.044L124.195999,111.044L124.195999,111.5ZM128.576,111.578C129.362,111.578,129.992,111.11,129.992,110.324C129.992,109.718,129.578,109.334,129.062,109.208L129.062,109.178C129.53,109.016,129.842,108.656,129.842,108.122C129.842,107.426,129.302,107.024,128.558,107.024C128.054,107.024,127.664,107.246,127.334,107.546L127.628,107.894C127.88,107.642,128.186,107.468,128.54,107.468C129.002,107.468,129.284,107.744,129.284,108.164C129.284,108.638,128.978,109.004,128.066,109.004L128.066,109.424C129.086,109.424,129.434,109.772,129.434,110.306C129.434,110.81,129.068,111.122,128.54,111.122C128.042,111.122,127.712,110.882,127.454,110.618L127.172,110.972C127.46,111.29,127.892,111.578,128.576,111.578ZM131.996,111.578C132.83,111.578,133.364,110.822,133.364,109.286C133.364,107.762,132.83,107.024,131.996,107.024C131.156,107.024,130.628,107.762,130.628,109.286C130.628,110.822,131.156,111.578,131.996,111.578ZM131.996,111.134C131.498,111.134,131.156,110.576,131.156,109.286C131.156,108.002,131.498,107.456,131.996,107.456C132.494,107.456,132.836,108.002,132.836,109.286C132.836,110.576,132.494,111.134,131.996,111.134ZM133.922,111.5L136.688,111.5L136.688,111.026L135.47,111.026C135.248,111.026,134.978,111.05,134.75,111.068C135.782,110.09,136.478,109.196,136.478,108.314C136.478,107.534,135.98,107.024,135.194,107.024C134.636,107.024,134.252,107.276,133.898,107.666L134.216,107.978C134.462,107.684,134.768,107.468,135.128,107.468C135.674,107.468,135.938,107.834,135.938,108.338C135.938,109.094,135.302,109.97,133.922,111.176L133.922,111.5ZM137.054,112.574L137.456,112.574L139.25,106.736L138.85399999999998,106.736L137.054,112.574ZM139.868,111.5L142.28,111.5L142.28,111.044L141.398,111.044L141.398,107.102L140.978,107.102C140.738,107.24,140.456,107.342,140.066,107.414L140.066,107.762L140.852,107.762L140.852,111.044L139.868,111.044L139.868,111.5ZM144.248,111.578C145.034,111.578,145.664,111.11,145.664,110.324C145.664,109.718,145.25,109.334,144.734,109.208L144.734,109.178C145.202,109.016,145.514,108.656,145.514,108.122C145.514,107.426,144.974,107.024,144.23,107.024C143.726,107.024,143.336,107.246,143.006,107.546L143.3,107.894C143.552,107.642,143.858,107.468,144.212,107.468C144.674,107.468,144.956,107.744,144.956,108.164C144.956,108.638,144.65,109.004,143.738,109.004L143.738,109.424C144.758,109.424,145.106,109.772,145.106,110.306C145.106,110.81,144.74,111.122,144.212,111.122C143.714,111.122,143.38400000000001,110.882,143.126,110.618L142.844,110.972C143.132,111.29,143.564,111.578,144.248,111.578ZM147.668,111.578C148.502,111.578,149.036,110.822,149.036,109.286C149.036,107.762,148.502,107.024,147.668,107.024C146.828,107.024,146.3,107.762,146.3,109.286C146.3,110.822,146.828,111.578,147.668,111.578ZM147.668,111.134C147.17000000000002,111.134,146.828,110.576,146.828,109.286C146.828,108.002,147.17000000000002,107.456,147.668,107.456C148.166,107.456,148.508,108.002,148.508,109.286C148.508,110.576,148.166,111.134,147.668,111.134ZM150.90800000000002,111.578C151.694,111.578,152.324,111.11,152.324,110.324C152.324,109.718,151.91,109.334,151.394,109.208L151.394,109.178C151.862,109.016,152.174,108.656,152.174,108.122C152.174,107.426,151.63400000000001,107.024,150.89,107.024C150.386,107.024,149.996,107.246,149.666,107.546L149.96,107.894C150.212,107.642,150.518,107.468,150.872,107.468C151.334,107.468,151.61599999999999,107.744,151.61599999999999,108.164C151.61599999999999,108.638,151.31,109.004,150.398,109.004L150.398,109.424C151.418,109.424,151.766,109.772,151.766,110.306C151.766,110.81,151.4,111.122,150.872,111.122C150.374,111.122,150.044,110.882,149.786,110.618L149.504,110.972C149.792,111.29,150.224,111.578,150.90800000000002,111.578ZM152.726,112.574L153.128,112.574L154.922,106.736L154.526,106.736L152.726,112.574ZM155.54,111.5L157.952,111.5L157.952,111.044L157.07,111.044L157.07,107.102L156.65,107.102C156.41,107.24,156.128,107.342,155.738,107.414L155.738,107.762L156.524,107.762L156.524,111.044L155.54,111.044L155.54,111.5ZM159.92000000000002,111.578C160.70600000000002,111.578,161.336,111.11,161.336,110.324C161.336,109.718,160.922,109.334,160.406,109.208L160.406,109.178C160.874,109.016,161.186,108.656,161.186,108.122C161.186,107.426,160.64600000000002,107.024,159.902,107.024C159.398,107.024,159.008,107.246,158.678,107.546L158.972,107.894C159.224,107.642,159.53,107.468,159.88400000000001,107.468C160.346,107.468,160.628,107.744,160.628,108.164C160.628,108.638,160.322,109.004,159.41,109.004L159.41,109.424C160.43,109.424,160.778,109.772,160.778,110.306C160.778,110.81,160.412,111.122,159.88400000000001,111.122C159.386,111.122,159.05599999999998,110.882,158.798,110.618L158.516,110.972C158.804,111.29,159.236,111.578,159.92000000000002,111.578ZM163.34,111.578C164.174,111.578,164.708,110.822,164.708,109.286C164.708,107.762,164.174,107.024,163.34,107.024C162.5,107.024,161.972,107.762,161.972,109.286C161.972,110.822,162.5,111.578,163.34,111.578ZM163.34,111.134C162.84199999999998,111.134,162.5,110.576,162.5,109.286C162.5,108.002,162.84199999999998,107.456,163.34,107.456C163.838,107.456,164.18,108.002,164.18,109.286C164.18,110.576,163.838,111.134,163.34,111.134ZM167.042,111.5L167.558,111.5L167.558,110.288L168.14600000000002,110.288L168.14600000000002,109.85L167.558,109.85L167.558,107.102L166.952,107.102L165.122,109.928L165.122,110.288L167.042,110.288L167.042,111.5ZM167.042,109.85L165.692,109.85L166.69400000000002,108.35C166.82,108.134,166.94,107.912,167.048,107.702L167.072,107.702C167.06,107.924,167.042,108.284,167.042,108.5L167.042,109.85ZM137.942,120.848C138.17,120.764,138.506,120.74,141.686,120.47C141.824,120.65,141.944,120.824,142.028,120.974L142.43,120.72800000000001C142.166,120.278,141.596,119.63,141.056,119.15L140.678,119.354C140.912,119.57,141.152,119.822,141.368,120.074L138.638,120.284C139.064,119.888,139.49,119.408,139.862,118.916L142.508,118.916L142.508,118.478L137.534,118.478L137.534,118.916L139.25,118.916C138.86,119.45,138.404,119.924,138.242,120.068C138.056,120.242,137.918,120.356,137.786,120.386C137.84,120.506,137.918,120.746,137.942,120.848ZM140.024,115.46000000000001C139.484,116.264,138.428,117.026,137.252,117.524C137.36,117.608,137.516,117.8,137.582,117.914C137.93,117.752,138.266,117.572,138.584,117.374L138.584,117.74L141.446,117.74L141.446,117.32L138.662,117.32C139.178,116.984,139.64,116.606,140.018,116.19200000000001C140.378,116.564,140.882,116.972,141.446,117.32C141.77,117.524,142.118,117.70400000000001,142.46,117.842C142.532,117.722,142.682,117.536,142.778,117.446C141.806,117.11,140.828,116.456,140.276,115.886L140.456,115.646L140.024,115.46000000000001ZM146.252,115.742C146.492,116.144,146.744,116.678,146.84,117.008L147.248,116.822C147.152,116.492,146.882,115.976,146.63,115.58L146.252,115.742ZM143.678,115.874C143.948,116.156,144.272,116.55199999999999,144.428,116.804L144.77,116.522C144.614,116.276,144.278,115.904,144.002,115.628L143.678,115.874ZM147.992,115.832C147.794,117.08,147.482,118.202,146.84,119.102C146.234,118.262,145.868,117.176,145.652,115.904L145.226,115.976C145.484,117.398,145.874,118.58,146.534,119.48C146.114,119.95400000000001,145.568,120.35,144.86599999999999,120.65C144.95,120.746,145.076,120.914,145.136,121.02199999999999C145.832,120.71000000000001,146.38400000000001,120.30199999999999,146.822,119.828C147.272,120.332,147.836,120.72200000000001,148.532,120.998C148.60399999999998,120.878,148.748,120.69800000000001,148.856,120.608C148.148,120.356,147.584,119.966,147.128,119.462C147.85399999999998,118.49,148.214,117.272,148.454,115.904L147.992,115.832ZM143.276,117.338L143.276,117.776L144.122,117.776L144.122,119.894C144.122,120.206,143.96,120.41,143.864,120.506C143.942,120.572,144.074,120.72800000000001,144.122,120.824C144.218,120.70400000000001,144.374,120.584,145.43,119.834C145.382,119.744,145.316,119.57,145.28,119.45L144.56,119.94800000000001L144.56,117.338L143.276,117.338ZM149.894,119.20400000000001L149.894,119.6L151.766,119.6L151.766,120.404L149.35399999999998,120.404L149.35399999999998,120.812L154.67000000000002,120.812L154.67000000000002,120.404L152.228,120.404L152.228,119.6L154.136,119.6L154.136,119.20400000000001L152.228,119.20400000000001L152.228,118.574L151.766,118.574L151.766,119.20400000000001L149.894,119.20400000000001ZM150.14,118.682C150.326,118.61,150.608,118.586,153.476,118.364C153.614,118.502,153.734,118.64,153.818,118.748L154.166,118.502C153.92000000000002,118.19,153.404,117.728,152.984,117.404L152.654,117.626C152.81,117.752,152.978,117.89,153.14,118.03999999999999L150.818,118.202C151.16,117.95,151.502,117.65,151.82,117.332L154.01,117.332L154.01,116.94200000000001L150.038,116.94200000000001L150.038,117.332L151.238,117.332C150.902,117.674,150.548,117.962,150.416,118.05199999999999C150.26,118.172,150.122,118.25,150.008,118.268C150.056,118.382,150.11599999999999,118.592,150.14,118.682ZM151.61,115.526C151.694,115.664,151.778,115.838,151.844,115.994L149.42000000000002,115.994L149.42000000000002,117.056L149.858,117.056L149.858,116.402L154.13,116.402L154.13,117.056L154.586,117.056L154.586,115.994L152.348,115.994C152.282,115.814,152.156,115.58,152.042,115.4L151.61,115.526Z"
                        fill="#333333" fill-opacity="1" />
                </g>
                <g>
                    <path
                        d="M63.1429,0.5Q63.1428,0.4507543,63.1332,0.4024549Q63.1236,0.354155,63.1048,0.308658Q63.0859,0.263161,63.0586,0.222215Q63.0312,0.181269,62.9964,0.146447Q62.9616,0.11162499999999997,62.9206,0.08426499999999998Q62.8797,0.05690600000000001,62.8342,0.03805999999999998Q62.7887,0.019214999999999982,62.7404,0.009606999999999977Q62.6921,0,62.6429,0L0.5,0Q0.4507543,0,0.4024549,0.009606999999999977Q0.354155,0.019214999999999982,0.308658,0.03805999999999998Q0.263161,0.05690600000000001,0.222215,0.08426499999999998Q0.181269,0.11162499999999997,0.146447,0.146447Q0.11162499999999997,0.181269,0.08426499999999998,0.222215Q0.05690600000000001,0.263161,0.03805999999999998,0.308658Q0.019214999999999982,0.354155,0.009606999999999977,0.4024549Q0,0.4507543,0,0.5L0,121.976Q0,122.025,0.009606999999999977,122.073Q0.019214999999999982,122.122,0.03805999999999998,122.167Q0.05690600000000001,122.213,0.08426499999999998,122.253Q0.11162499999999997,122.294,0.146447,122.329Q0.181269,122.364,0.222215,122.391Q0.263161,122.419,0.308658,122.438Q0.354155,122.456,0.4024549,122.466Q0.4507543,122.476,0.5,122.476L29.3031,122.476L29.3031,134.111Q29.3031,134.161,29.3127,134.209Q29.3223,134.257,29.3412,134.303Q29.36,134.348,29.3874,134.389Q29.4148,134.43,29.4496,134.465Q29.4844,134.5,29.5253,134.527Q29.5663,134.555,29.6118,134.573Q29.6573,134.592,29.7056,134.602Q29.7539,134.611,29.8031,134.611L55.5749,134.611L55.5749,140.179Q55.5749,140.224,55.5828,140.268Q55.5906,140.311,55.6061,140.353Q55.6216,140.395,55.6442,140.433Q55.6669,140.472,55.6959,140.505Q55.725,140.539,55.7595,140.567Q55.7941,140.595,55.833,140.617Q55.872,140.638,55.9142,140.653Q55.9564,140.667,56.0004,140.674Q139.936,153.326,231.962,140.675Q232.006,140.669,232.049,140.655Q232.092,140.641,232.131,140.619Q232.171,140.598,232.206,140.57Q232.241,140.542,232.271,140.508Q232.3,140.474,232.323,140.435Q232.346,140.397,232.362,140.355Q232.378,140.313,232.386,140.268Q232.394,140.224,232.394,140.179L232.394,134.611L259.681,134.611Q259.73,134.611,259.779,134.602Q259.827,134.592,259.872,134.573Q259.918,134.555,259.959,134.527Q260,134.5,260.035,134.465Q260.07,134.43,260.097,134.389Q260.124,134.348,260.143,134.303Q260.162,134.257,260.172,134.209Q260.181,134.161,260.181,134.111L260.181,126.521L290.5,126.521Q290.549,126.521,290.598,126.511Q290.646,126.502,290.691,126.483Q290.737,126.464,290.778,126.437Q290.819,126.409,290.854,126.374Q290.888,126.34,290.916,126.299Q290.943,126.258,290.962,126.212Q290.981,126.167,290.99,126.118Q291,126.07,291,126.021L291,0.5Q291,0.4507543,290.99,0.4024549Q290.981,0.354155,290.962,0.308658Q290.943,0.263161,290.916,0.222215Q290.888,0.181269,290.854,0.146447Q290.819,0.11162499999999997,290.778,0.08426499999999998Q290.737,0.05690600000000001,290.691,0.03805999999999998Q290.646,0.019214999999999982,290.598,0.009606999999999977Q290.549,0,290.5,0L225.326,0Q225.277,0,225.228,0.009606999999999977Q225.18,0.019214999999999982,225.134,0.03805999999999998Q225.089,0.05690600000000001,225.048,0.08426499999999998Q225.007,0.11162499999999997,224.972,0.146447Q224.937,0.181269,224.91,0.222215Q224.883,0.263161,224.864,0.308658Q224.845,0.354155,224.835,0.4024549Q224.826,0.4507543,224.826,0.5L224.826,3.65792L63.1429,3.65792L63.1429,0.5ZM62.1429,1L1,1L1,121.476L29.8031,121.476Q29.8524,121.476,29.9007,121.485Q29.949,121.495,29.9945,121.514Q30.04,121.533,30.0809,121.56Q30.1219,121.587,30.1567,121.622Q30.1915,121.657,30.2189,121.698Q30.2462,121.739,30.2651,121.784Q30.2839,121.83,30.2935,121.878Q30.3031,121.926,30.3031,121.976L30.3031,133.611L56.0749,133.611Q56.1242,133.611,56.1725,133.621Q56.2207,133.631,56.2662,133.649Q56.3117,133.668,56.3527,133.696Q56.3936,133.723,56.4285,133.758Q56.4633,133.793,56.4906,133.834Q56.518,133.875,56.5368,133.92Q56.5557,133.966,56.5653,134.014Q56.5749,134.062,56.5749,134.111L56.5749,139.749Q139.979,152.254,231.394,139.743L231.394,134.111Q231.394,134.062,231.403,134.014Q231.413,133.966,231.432,133.92Q231.451,133.875,231.478,133.834Q231.505,133.793,231.54,133.758Q231.575,133.723,231.616,133.696Q231.657,133.668,231.702,133.649Q231.748,133.631,231.796,133.621Q231.844,133.611,231.894,133.611L259.181,133.611L259.181,126.021Q259.181,125.972,259.191,125.923Q259.2,125.875,259.219,125.83Q259.238,125.784,259.265,125.743Q259.293,125.702,259.328,125.667Q259.362,125.633,259.403,125.605Q259.444,125.578,259.49,125.559Q259.535,125.54,259.584,125.531Q259.632,125.521,259.681,125.521L290,125.521L290,1L225.826,1L225.826,4.15792Q225.826,4.20716,225.816,4.255459999999999Q225.807,4.3037600000000005,225.788,4.34926Q225.769,4.39476,225.741,4.435700000000001Q225.714,4.476649999999999,225.679,4.51147Q225.644,4.54629,225.604,4.57365Q225.563,4.60101,225.517,4.61986Q225.472,4.6387,225.423,4.64831Q225.375,4.65792,225.326,4.65792L62.6429,4.65792Q62.5936,4.65792,62.5453,4.64831Q62.497,4.6387,62.4515,4.61986Q62.406,4.60101,62.3651,4.57365Q62.3241,4.54629,62.2893,4.51147Q62.2545,4.476649999999999,62.2271,4.435700000000001Q62.1997,4.39476,62.1809,4.34926Q62.1621,4.3037600000000005,62.1525,4.255459999999999Q62.1428,4.20716,62.1429,4.15792L62.1429,1Z"
                        fill-rule="evenodd" fill="#333333" fill-opacity="1" />
                </g>
            </g>
            <!-- 路线层 -->
            <template v-if="selected">
                <polyline :points="routeString" class="route-line" stroke-linecap="round" stroke-linejoin="round" />
                <!-- <polyline :points="routeString2" class="route-line" stroke-linecap="round" stroke-linejoin="round" /> -->
                <!-- <polyline :points="routeString2_2" class="route-line" stroke-linecap="round" stroke-linejoin="round" /> -->
                <polyline v-for="lineItem of selectedData.routeStrs" :points="lineItem" class="route-line"
                    stroke-linecap="round" stroke-linejoin="round" />
                <!--定位-->
                <g :style="selectedData.positionStyle" clip-path="url(#master_svg0_32_03912)">
                    <g>
                        <path
                            d="M8,13Q8.17184,13,8.34347,12.998190000000001Q8.51511,12.99639,8.686119999999999,12.99278Q8.85713,12.98917,9.02711,12.98376Q9.19709,12.97836,9.36563,12.97118Q9.53417,12.963989999999999,9.70086,12.95505Q9.86755,12.9461,10.03199,12.935410000000001Q10.19643,12.92472,10.35823,12.91232Q10.52002,12.89991,10.67878,12.885819999999999Q10.83754,12.87173,10.99288,12.85598Q11.1482,12.84024,11.2998,12.82288Q11.4513,12.80552,11.5987,12.78659Q11.7461,12.76766,11.889,12.7472Q12.0319,12.72675,12.1699,12.70481Q12.3079,12.68288,12.4408,12.65952Q12.5736,12.63615,12.7009,12.61143Q12.8282,12.5867,12.9497,12.56066Q13.0713,12.53462,13.1867,12.50734Q13.3021,12.48005,13.4111,12.45159Q13.5201,12.42313,13.6225,12.39355Q13.7248,12.36397,13.8203,12.33335Q13.9158,12.30274,14.0041,12.27115Q14.0924,12.23957,14.1734,12.2071Q14.2545,12.17462,14.3279,12.14133Q14.4014,12.108039999999999,14.4672,12.07402Q14.5329,12.04,14.5908,12.00533Q14.6487,11.97066,14.6986,11.93543Q14.7485,11.90019,14.7902,11.86447Q14.832,11.82875,14.8655,11.79264Q14.899,11.75652,14.9242,11.7201Q14.9494,11.68367,14.9663,11.64703Q14.9831,11.61038,14.9916,11.5736Q15,11.53682,15,11.5Q15,11.46318,14.9916,11.4264Q14.9831,11.38962,14.9663,11.35297Q14.9494,11.31633,14.9242,11.2799Q14.899,11.24348,14.8655,11.20736Q14.832,11.17125,14.7902,11.13553Q14.7485,11.09981,14.6986,11.06457Q14.6487,11.02934,14.5908,10.994665Q14.5329,10.959995,14.4672,10.925975Q14.4014,10.891955,14.3279,10.858667Q14.2545,10.82538,14.1734,10.792905Q14.0924,10.76043,14.0041,10.728846Q13.9158,10.697262,13.8203,10.666645Q13.7248,10.636027,13.6225,10.606451Q13.5201,10.576875,13.4111,10.54841Q13.3021,10.519946000000001,13.1867,10.492662Q13.0713,10.465378,12.9497,10.43934Q12.8282,10.413302,12.7009,10.388573Q12.5736,10.363845,12.4408,10.340484Q12.3079,10.317124,12.1699,10.295189Q12.0319,10.273253,11.889,10.252796Q11.7461,10.232338,11.5987,10.213407Q11.4513,10.194476,11.2998,10.177118Q11.1482,10.15976,10.99288,10.144016Q10.83754,10.128272,10.67878,10.114181Q10.52002,10.100089,10.35823,10.0876839Q10.19643,10.0752786,10.03199,10.0645895Q9.86755,10.0539004,9.70086,10.0449531Q9.53417,10.0360059,9.36563,10.0288221Q9.19709,10.0216383,9.02711,10.0162352Q8.85713,10.0108322,8.686119999999999,10.00722291Q8.51511,10.00361363,8.34347,10.00180682Q8.17184,10,8,10Q7.82816,10,7.65653,10.00180682Q7.48489,10.00361363,7.31388,10.00722291Q7.14287,10.0108322,6.97289,10.0162352Q6.80291,10.0216383,6.63437,10.0288221Q6.46583,10.0360059,6.29914,10.0449531Q6.13245,10.0539004,5.96801,10.0645895Q5.80357,10.0752786,5.64177,10.0876839Q5.47998,10.100089,5.32122,10.114181Q5.16246,10.128272,5.00711,10.144016Q4.85177,10.15976,4.70022,10.177118Q4.5486699999999995,10.194476,4.40128,10.213407Q4.25389,10.232338,4.11101,10.252796Q3.96813,10.273253,3.8301,10.295189Q3.69208,10.317124,3.55925,10.340484Q3.42641,10.363845,3.29909,10.388573Q3.17176,10.413302,3.05025,10.43934Q2.92874,10.465378,2.81334,10.492662Q2.69794,10.519946000000001,2.58893,10.54841Q2.4799100000000003,10.576875,2.3775500000000003,10.606451Q2.2751799999999998,10.636027,2.17971,10.666645Q2.0842400000000003,10.697262,1.9959,10.728846Q1.907556,10.76043,1.826551,10.792905Q1.745546,10.82538,1.672075,10.858667Q1.598604,10.891955,1.532843,10.925975Q1.4670830000000001,10.959995,1.409191,10.994665Q1.3513,11.02934,1.301418,11.06457Q1.251535,11.09981,1.209781,11.13553Q1.168027,11.17125,1.134503,11.20736Q1.100979,11.24348,1.0757644,11.2799Q1.0505502,11.31633,1.0337069,11.35297Q1.0168636,11.38962,1.0084318,11.4264Q1,11.46318,1,11.5Q1,11.53682,1.0084318,11.5736Q1.0168636,11.61038,1.0337069,11.64703Q1.0505502,11.68367,1.0757644,11.7201Q1.100979,11.75652,1.134503,11.79264Q1.168027,11.82875,1.209781,11.86447Q1.251535,11.90019,1.301418,11.93543Q1.3513,11.97066,1.409191,12.00533Q1.4670830000000001,12.04,1.532843,12.07402Q1.598604,12.108039999999999,1.672075,12.14133Q1.745546,12.17462,1.826551,12.2071Q1.907556,12.23957,1.995899,12.27115Q2.0842400000000003,12.30274,2.17971,12.333359999999999Q2.2751799999999998,12.36397,2.3775500000000003,12.39355Q2.4799100000000003,12.42313,2.58893,12.45159Q2.69794,12.48005,2.81334,12.50734Q2.92874,12.53462,3.05025,12.56066Q3.17176,12.5867,3.29909,12.61143Q3.42641,12.63616,3.55925,12.65952Q3.69208,12.68288,3.8301,12.70481Q3.96813,12.72675,4.11101,12.7472Q4.25389,12.76766,4.40128,12.78659Q4.5486699999999995,12.80552,4.70022,12.82288Q4.85177,12.84024,5.00711,12.85598Q5.16246,12.87173,5.32122,12.885819999999999Q5.47998,12.89991,5.64177,12.91232Q5.80357,12.92472,5.96801,12.935410000000001Q6.13245,12.9461,6.29914,12.95505Q6.46583,12.963989999999999,6.63437,12.97118Q6.80291,12.97836,6.97289,12.98376Q7.14287,12.98917,7.31388,12.99278Q7.48489,12.99639,7.65653,12.998190000000001Q7.82816,13,8,13ZM14.4472,11.5Q14.3814,11.55241,14.2705,11.61242Q13.8,11.86712,12.845,12.07176Q10.84653,12.5,8,12.5Q5.15348,12.5,3.15502,12.07176Q2.20004,11.86712,1.72949,11.61242Q1.6186120000000002,11.55241,1.552785,11.5Q1.6186120000000002,11.44759,1.72949,11.38758Q2.20004,11.13288,3.15502,10.928241Q5.15347,10.5,8,10.5Q10.84653,10.5,12.845,10.928241Q13.8,11.13288,14.2705,11.38758Q14.3814,11.44759,14.4472,11.5Z"
                            fill-rule="evenodd" fill="url(#master_svg1_1_5010)" fill-opacity="0.6000000238418579" />
                    </g>
                    <g filter="url(#master_svg2_32_03912/1_08328)">
                        <path
                            d="M8,3C6.34302,3,5,4.43262,5,6.19994Q5,8.99977,8,11Q11,9.05002,11,6.19994C11,4.43262,9.65681,3.000000262817,8,3ZM8,7.57142C7.22021,7.57142,6.58821,6.88912,6.58821,6.04758C6.58821,5.20595,7.22021,4.52375,8,4.52375C8.77966,4.52375,9.41174,5.20595,9.41174,6.04758C9.41174,6.88908,8.77966,7.57142,8,7.57142Z"
                            fill="url(#master_svg3_1_5013)" fill-opacity="1" style="mix-blend-mode: passthrough" />
                    </g>
                </g>
                <!--门-->
                <g v-for="(doorStyle, i) in selectedData.doorsStyles" :key="`door-${i}`" :style="doorStyle">
                    <path
                        d="M0,0.5Q0,0.4507543,0.009606999999999977,0.4024549Q0.019214999999999982,0.354155,0.03805999999999998,0.308658Q0.05690600000000001,0.263161,0.08426499999999998,0.222215Q0.11162499999999997,0.181269,0.146447,0.146447Q0.181269,0.11162499999999997,0.222215,0.08426499999999998Q0.263161,0.05690600000000001,0.308658,0.03805999999999998Q0.354155,0.019214999999999982,0.4024549,0.009606999999999977Q0.4507543,0,0.5,0Q0.5492457,0,0.5975451,0.009606999999999977Q0.645845,0.019214999999999982,0.691342,0.03805999999999998Q0.736839,0.05690600000000001,0.777785,0.08426499999999998Q0.818731,0.11162499999999997,0.853553,0.146447Q0.888375,0.181269,0.915735,0.222215Q0.943094,0.263161,0.96194,0.308658Q0.980785,0.354155,0.9903930000000001,0.4024549Q1,0.4507543,1,0.5L1,1.333904L0,1.333904L0,0.5ZM1,3.31267L3.4,3.31267L0.5,8.335619999999999L-2.4,3.31267L0,3.31267L0,3.00171L1,3.00171L1,3.31267Z"
                        fill-rule="evenodd" fill="#FF592B" fill-opacity="1" />
                </g>
            </template>
        </svg>
    </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch, watchEffect } from 'vue';
const props = defineProps<{
    /** 线路坐标 */
    modelValue?: { x: number; y: number }[];
    /** 默认选中的房间 ID */
    selectedId?: "1031";
    /** 门的位置 */
    doorsPosition?: { x: number; y: number }[];
    /** 定位图标 */
    position?: { x: number; y: number };
}>();
const emit = defineEmits<{
    (e: "update:modelValue", val: { x: number; y: number }[]): void;
    (e: "update:selectedId", v: string | undefined): void;
}>();
const selected = ref<string | undefined>(undefined);
const selectedData = reactive({
    routeStrs: [] as string[],
    positionStyle: '',
    doorsStyles: [] as string[],
})
/** 固定线路 */
const routeString = computed(() => internalPoints.value.map((p) => `${p.x},${p.y}`).join(" "));
// 额外指引线 1301
const routeString1 = computed(() => internalPoints1.value.map((p) => `${p.x},${p.y}`).join(" "))
// 额外指引线 130234
const routeString2 = computed(() => internalPoints2.value.map((p) => `${p.x},${p.y}`).join(" "));
// 额外指引线 130234
const routeString2_2 = computed(() => internalPoints2_2.value.map((p) => `${p.x},${p.y}`).join(" "));
/** 固定线路 */
const internalPoints = ref<{ x: number; y: number }[]>([
    { x: 84, y: 34 },
    { x: 88, y: 34 },
    { x: 88, y: 75 },
    { x: 198, y: 75 },
    { x: 198, y: 34 },
]);
/** 1301 */
const internalPoints1 = ref<{ x: number; y: number }[]>([
    { x: 198, y: 75 },
    { x: 219, y: 75 },
]);
/** 130234-1 */
const internalPoints2 = ref<{ x: number; y: number }[]>([
    { x: 65, y: 75 },
    { x: 88, y: 75 },
]);
/** 130234-2 */
const internalPoints2_2 = ref<{ x: number; y: number }[]>([
    { x: 198, y: 75 },
    { x: 219, y: 75 },
]);

const selectRoom = (roomId: '1301' | '130234') => {
    selected.value = selected.value == roomId ? undefined : roomId;
    // selected.value = selected.value ? undefined : roomId;
    // if (roomId === '1301') {
    //     selectedData.routeStrs = [routeString1.value];
    //     selectedData.positionStyle = positionStyle1301.value;
    //     selectedData.doorsStyles = doorsStyles1301.value
    // } else {
    //     selectedData.routeStrs = [routeString2.value, routeString2_2.value];
    //     selectedData.positionStyle = positionStyle1302.value;
    //     selectedData.doorsStyles = doorsStyles1302.value
    // }
    emit("update:selectedId", selected.value);
};

// 定位点 1301
const position1301 = ref({ x: 250, y: 40 })
const positionStyle1301 = computed(() => `transform: translate(${position1301.value.x}px, ${position1301.value.y}px)`)
// 定位点 1302_1303_1304
const position1302 = ref({ x: 140, y: 90 })
const positionStyle1302 = computed(() => `transform: translate(${position1302.value.x}px, ${position1302.value.y}px)`)

// 门指向 1302_1303_1304
const doors1302 = ref<{ x: number; y: number }[]>([{ x: 219, y: 76 }, { x: 64, y: 76 }]);
const doorsStyles1302 = computed(() => doors1302.value.map((d) => `transform: translate(${d.x}px, ${d.y}px)`))
// 门指向 1301
const doors1301 = ref<{ x: number; y: number }[]>([{ x: 219, y: 75.5 }]);
const doorsStyles1301 = computed(() => doors1301.value.map((d) => `transform: translate(${d.x}px, ${d.y}px) rotate(-90deg)`))
const clearSelection = (_: MouseEvent) => {
    selected.value = undefined;
    emit("update:selectedId", undefined)
};
watchEffect(() => {
    if (selected.value != null) {
        if (selected.value === '1301') {
            selectedData.routeStrs = [routeString1.value];
            selectedData.positionStyle = positionStyle1301.value;
            selectedData.doorsStyles = doorsStyles1301.value
        } else {
            selectedData.routeStrs = [routeString2.value, routeString2_2.value];
            selectedData.positionStyle = positionStyle1302.value;
            selectedData.doorsStyles = doorsStyles1302.value
        }
    } else {
        selectedData.routeStrs = [];
        selectedData.positionStyle = '';
        selectedData.doorsStyles = []
    }
})
watch(
    () => props.selectedId,
    (v) => {
        selected.value = v ?? undefined;
    },
    { immediate: true }
);
</script>

<style lang="css" scoped>
@import "@/pages/meet/meeting-handle/styles/sl-meeting-room.css";
/* @import "@/styles/component/sl-meeting-room.css"; */
</style>