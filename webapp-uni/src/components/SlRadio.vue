<template>
  <label class="sl-radio" :class="{ checked, disabled }" @click="onClick">
    <span class="icon">
      <svg class="icon-16" v-if="checked">
        <use xlink:href="#icon-components-selected"></use>
      </svg>
      <svg class="icon-16" v-else>
        <use xlink:href="#icon-components-unselected"></use>
      </svg>
    </span>
    <span class="label">
      <slot />
    </span>
  </label>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface Props {
  modelValue: string | number | boolean;
  value?: string | number | boolean;
  disabled?: boolean;
}

// 定义 props 类型 + 默认值
const props = withDefaults(defineProps<Props>(), {
  value: "",
  disabled: false,
});

const emit = defineEmits<{
  (e: "update:modelValue", val: string | number | boolean): void;
  (e: "change", val: string | number | boolean): void;
}>();

const checked = computed(() => props.modelValue === props.value);

function onClick() {
  if (props.disabled || checked.value) return;
  emit("update:modelValue", props.value!);
  emit("change", props.value!);
}
</script>

<style scoped>
.sl-radio {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-size: 10px;
}

.sl-radio.disabled {
  cursor: default;
  /* cursor: not-allowed; */
  /* opacity: 0.6; */
}

.sl-radio .icon {
  display: inline-flex;
}

.sl-radio .label {
  margin-left: 12px;
}

.sl-radio.icon-checked,
.sl-radio.icon-unchecked {
  width: 100%;
  height: 100%;
}
</style>
