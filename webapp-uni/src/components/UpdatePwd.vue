<template>
    <div class="update-pwd">
        <h2>修改密码</h2>
        <n-form :model="form" :rules="rules" ref="formRef">
            <n-form-item label="旧密码" path="oldPassword">
                <n-input v-model:value="form.oldPassword" type="password" />
            </n-form-item>
            <n-form-item label="新密码" path="newPassword">
                <n-input v-model:value="form.newPassword" type="password" />
            </n-form-item>
            <n-form-item label="确认密码" path="confirmPassword">
                <n-input v-model:value="form.confirmPassword" type="password" />
            </n-form-item>
            <n-form-item>
                <button type="button" @click="handleSubmit">
                    提交
                </button>
                <!-- <n-button @click="handleSubmit">提交</n-button> -->
            </n-form-item>
        </n-form>
    </div>
</template>
<script setup lang="ts">
import { NForm, NFormItem, NInput } from 'naive-ui';
import { ref } from 'vue';

const form = ref({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
});

const rules = {
    oldPassword: [
        { required: true, message: '请输入旧密码' },
        { min: 6, max: 20, message: '密码长度在6到20个字符之间' }
    ],
    newPassword: [
        { required: true, message: '请输入新密码' },
        { min: 6, max: 20, message: '密码长度在6到20个字符之间' }
    ],
    confirmPassword: [
        { required: true, message: '请确认新密码' },
        { min: 6, max: 20, message: '密码长度在6到20个字符之间' }
    ]
};

const formRef = ref(null);
const handleSubmit = () => {
    if (!formRef.value) return;
    console.log('formRef', formRef.value);
    // formRef.value.validate((valid) => {
    //     if (valid) {
    //         if (form.value.newPassword !== form.value.confirmPassword) {
    //             alert('新密码和确认密码不一致');
    //             return;
    //         }
    //         // 这里可以添加提交表单的逻辑，比如发送请求到后端
    //         alert('密码修改成功');
    //     } else {
    //         alert('表单验证失败，请检查输入');
    //     }
    // });
};
</script>