<template>
    <image :src="iconUrl" :style="{ width: sizeUnit, height: sizeUnit }" :mode="mode" v-if="iconUrl" />
</template>

<script setup lang="ts">
import { computed, withDefaults, defineProps } from 'vue'
const DESIGN_WIDTH = 320
// 小程序 <image> 组件可选的 mode 类型
type ImageMode =
    | 'scaleToFill'
    | 'aspectFit'
    | 'aspectFill'
    | 'widthFix'
    | 'heightFix'
    | 'top'
    | 'bottom'
    | 'center'
    | 'left'
    | 'right'
    | 'top left'
    | 'top right'
    | 'bottom left'
    | 'bottom right'
/**
 * SvgIcon 组件：
 * @prop name: 图标文件名，不带扩展名，格式 “文件夹-完整文件名”
 *   例如：meeting-16-1-search => static/icons/meeting/16-1-search.svg
 * @prop size: 宽高，可传 Number（当 px 处理）或合法 CSS 大小字符串
 * @prop mode: <image> 渲染模式，默认 'aspectFit'
 */
const props = withDefaults(
    defineProps<{ name: string; size?: number | string; mode?: ImageMode }>(),
    { mode: 'aspectFit' as ImageMode }
)

// 计算宽高
// const sizePx = computed(() => {
//     const v = props.size ?? 24
//     return typeof v === 'number' ? `${v}px` : /^\d+$/.test(v) ? `${v}px` : v
// })
// 计算最终 rpx 值 
const sizeUnit = computed(() => {
    const raw = props.size ?? 24
    // 如果是数字或纯数字字符串，就按比例换算
    if (typeof raw === 'number' || /^\d+$/.test(raw)) {
        const px = typeof raw === 'number' ? raw : parseInt(raw, 10)
        const rpx = (px * 750) / DESIGN_WIDTH
        // 保留最多 4 位小数
        return `${+rpx.toFixed(4)}rpx`
    }
    // 否则直接返回用户传的字符串（比如 '2em'、'50%'）
    return raw as string
})


// 生成图标路径，使用 static 目录
const iconUrl = computed(() => {
    // 支持 folder-name
    const [folder, ...rest] = props.name.split('-')
    const fileName = rest.join('-')
    if (!folder || !fileName) return ''
    return `/static/icons/${folder}/${fileName}.svg`
})

const { mode } = props
</script>
