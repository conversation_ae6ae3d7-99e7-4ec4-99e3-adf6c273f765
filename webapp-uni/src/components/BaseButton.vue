<template>
    <button class="btn-rounded medium"
        :class="{ 'btn-del': btnType == 'delete', 'btn-border-primary': btnType == 'add' }" @click="handleClick">
        <i v-show="btnType == 'add'" class="app-icon icon-20-32"></i>
        <!-- 删除 -->
        <i v-show="btnType == 'delete'" class="app-icon icon-20-30 alway"></i>
        <i v-show="btnType == 'delete'" class="app-icon icon-20-31 hover"></i>
        <!-- 下载 -->
        <i v-show="btnType == 'download'" class="app-icon icon-20-19"></i>
        <!-- <i v-show="btnType == 'download'" class="app-icon icon-20-21 hover"></i> -->
        <slot>
            <span>{{ btnType == 'add' ? '添加' : btnType == 'delete' ? '删除' : '' }}</span>
        </slot>
    </button>
</template>

<script setup lang="ts">
const { btnType } = defineProps<{ btnType?: 'add' | 'delete' | 'download', disabled?: boolean }>();
const emits = defineEmits(['click']);
const handleClick = () => {
    emits('click');
};
</script>

<style scoped></style>