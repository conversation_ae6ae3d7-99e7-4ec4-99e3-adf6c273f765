<template>
  <view class="home-footer">
    <view
      :class="{ active: currentFooter === 0 }"
      @click="handleFooterClick(0)"
    >
      <view class="i-footer"></view>
      <text>工作台</text>
    </view>
    <view
      :class="{ active: currentFooter === 1 }"
      @click="handleFooterClick(1)"
    >
      <view class="i-footer"></view>
      <text>AI</text>
    </view>
    <view
      :class="{ active: currentFooter === 2 }"
      @click="handleFooterClick(2)"
    >
      <view class="i-footer"></view>
      <text>我的</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
const currentFooter = ref(0);
function handleFooterClick(index: number) {
  currentFooter.value = index;
  switch (index) {
    case 0:
      console.log("工作台被点击");
      break;
    case 1:
      uni.navigateTo({ url: "/pages/ai/index" });
      break;
    case 2:
      console.log("我的被点击");
      break;
  }
}
</script>

<style lang="scss" scoped>
.home-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  height: 49px;
  background: #ffffffef;
  backdrop-filter: blur(20px);
  border-radius: 10px 10px 0 0;
  box-shadow: 0 -0.5px 6px 0 #5d5d5d33;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.home-footer view {
  width: 57px;
  height: 41px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.home-footer view .i-footer {
  display: block;
  width: 22px;
  height: 22px;
  position: absolute;
  top: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.home-footer view text {
  color: #666;
  font-size: 10px;
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: 0;
}

.home-footer view.active text {
  color: #005ac5;
}

.home-footer view.active .i-footer {
  width: 48px;
  height: 48px;
  top: -17px;
}

/* 默认图标 */
.home-footer view:nth-child(1) .i-footer {
  background-image: url("/static/icons/home/<USER>");
}

.home-footer view:nth-child(2) .i-footer {
  background-image: url("/static/icons/home/<USER>");
}

.home-footer view:nth-child(3) .i-footer {
  background-image: url("/static/icons/home/<USER>");
}

/* 激活状态图标 */
.home-footer view:nth-child(1).active .i-footer {
  background-image: url("/static/icons/home/<USER>");
}

.home-footer view:nth-child(2).active .i-footer {
  background-image: url("/static/icons/home/<USER>");
}

.home-footer view:nth-child(3).active .i-footer {
  background-image: url("/static/icons/home/<USER>");
}
</style>
