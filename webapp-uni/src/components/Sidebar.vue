<script setup lang="ts">
import { useHasPermission } from '@/composables/useHasPermission';
import { onMounted, ref } from 'vue';
import type { MenuItem } from '../models/menu';
import Menu from './Menu.vue';
const { isSuper } = useHasPermission()
const superMenus = [
    {
        label: "用户管理",
        icon: "app-icon icon-26-5",
        name: 'super-user-list'
    },
    {
        label: "配置",
        icon: "app-icon icon-26-1",
        name: 'super-configure'
    },
]
const menuItems = ref<MenuItem[]>([])
const adminMenuItems: MenuItem[] = [
    {
        label: "硬件/软件信息",
        icon: "app-icon icon-26-4",
        path: "/hwsw",
    },
    {
        label: "监控",
        icon: "app-icon icon-26-6",
        children: [
            { label: "AIS数据记录", path: "/monitor/record" },
            { label: "状态", path: "/monitor/status" },

        ],
    },
    {
        label: "配置",
        icon: "app-icon icon-26-1",
        children: [
            { label: "综合", path: "/configure/general" },
            { label: "网络", path: "/configure/interface" },
            { label: "TCP连接", path: "/configure/port" },
            { label: "省流控制", path: "/configure/forward" },
        ],
    },
    // {
    //     label: "用户管理",
    //     icon: "icon-user",
    //     path: "/user/list"
    // },
    {
        label: "日志管理",
        icon: "app-icon icon-26-2",
        // path: "/user",
        children: [
            { label: "AIS日志", name: 'log-ais' },
            { label: "运行日志", name: 'log-run' },
        ]
    },
    {
        label: "模拟",
        icon: "icon-user",
        name: "mock",
        isProdHide: true,
    },
    // {
    //     label: "map",
    //     icon: "icon-user",
    //     name: "map",
    // },
    // {
    //     label: "DEMO",
    //     icon: "icon-user",
    //     children: [
    //         { label: "page-config", path: "/demo/page" },
    //         { label: "table", path: "/demo/table" },
    //         { label: "virtual", path: "/demo/virtual" },
    //         { label: "chat", name: 'demo-chat' },
    //     ],
    // },
];
onMounted(() => {
    menuItems.value = adminMenuItems
    if (isSuper.value) {
        menuItems.value = superMenus
    }
})
</script>

<template>
    <nav class="sidebar">
        <div class="sidebar-logo">
            <img src="@/assets/img/logo.png" alt="船载终端">
            <span>船载终端</span>
        </div>
        <Menu :menuItems="menuItems"></Menu>
    </nav>
</template>

<style scoped>
.sidebar {
    width: var(--mt--sidebar-width);
    height: 100%;
    background: var(--mt-sidebar-bg);
    display: flex;
    flex-direction: column;
    padding: 15px 0;
    color: var(--mt-sidebar-text-color);
}

.sidebar-logo {
    width: 100%;
    height: 288px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.sidebar-logo span {
    color: #fff;
    font-size: 24px;
    font-weight: bold;
}
</style>
