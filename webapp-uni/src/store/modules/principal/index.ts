import { defineStore } from "pinia";
import type { Account } from "@/models/Account";
// import apiService from "@/service/api.service";

const usePrincipalStore = defineStore("principal", {
  state: () => ({
    userIdentity: undefined as Account | undefined,
  }),
  getters: {
    account(state) {
      return state.userIdentity;
    },
  },
  actions: {
    hasAnyAuthority(authorities: string[]) {
      if (!this.userIdentity) {
        return false;
      }
      return authorities.some(
        (authority) =>
          // this.userIdentity?.authorities.includes(authority)
          true
      );
    },
    async logout() {
      // await apiService.logout();
      this.userIdentity = undefined;
      //   removeToken();
    },
    async identity(force?: boolean) {
      if (force === true) {
        this.userIdentity = undefined;
      }
      if (this.userIdentity) {
        return Promise.resolve(this.userIdentity);
      }
      return this.userIdentity;
      // return apiService
      //   .getAccount()
      //   .then((account) => {
      //     this.userIdentity = account;
      //     return this.userIdentity;
      //   })
      //   .catch((e: string) => {
      //     console.error(e);
      //     this.userIdentity = undefined;
      //     return this.userIdentity; // emit error
      //   });
    },
  },
});

export default usePrincipalStore;
