export const transformPosUnit = (
  lat: number,
  lng: number,
  sDecimal: number = 2
): {
  lat: { d: number; m: number; s: number };
  lng: { d: number; m: number; s: number };
  latStr: string;
  lngStr: string;
  latlngStr: string;
  latlngStr2: string;
} => {
  const latUnit = lat < 0 ? 'S' : 'N';
  const lngUnit = lng < 0 ? 'W' : 'E';
  const latObj = latlngToDMS(Number(Math.abs(lat)).toFixed(8), sDecimal, 2)!;
  const lngObj = latlngToDMS(Number(Math.abs(lng)).toFixed(8), sDecimal, 2)!;
  return {
    lat: latObj,
    lng: lngObj,
    latStr: `${latObj.d}°${latObj.m}′${latObj.s}″${latUnit}`,
    lngStr: `${lngObj.d}°${lngObj.m}′${lngObj.s}″${lngUnit}`,
    latlngStr: `${latObj.d}°${latObj.m}′${latObj.s}″${latUnit} ${lngObj.d}°${lngObj.m}′${lngObj.s}″${lngUnit}`,
    latlngStr2: `${latObj.d}°${latObj.m2}′${latUnit} ${lngObj.d}°${lngObj.m2}′${lngUnit}`,
  };
};
export const latlngToDMS = (latlng: string | number, sDecimal = 4, mDecimal = 4): { d: number; m: number; s: number; m2: number } => {
  let d = 0,
    m = 0,
    s = 0,
    m2 = 0;
  // 第一次分离出度位整数，分（可能带有小数）
  const [integerN, decimalN] = calculateUnit(latlng);
  d = integerN;
  // 第二次分离出分位整数，秒（可能带有小数）
  if (decimalN) {
    m2 = parseFloat(Number(decimalN).toFixed(mDecimal));
    const [integerN2, decimalN2] = calculateUnit(decimalN);
    m = integerN2;
    s = parseFloat(Number(decimalN2).toFixed(sDecimal));
  }
  return { d, m, s, m2 };
};
/**
 * 分离整数和小数
 * 如果使用 % （取余）的算法，会多导致一次精度的损失
 * L.latlng 获取的的坐标为小数位 8 位
 * 例如：38.34543221N  117.23424442E
 * 通过结构赋值，完整分离整数位和小数位
 * [a,b] = '38.34543221'.split(.)
 * b / 10 ** 8 * 60 (进一位)
 *
 * @param str
 * @returns
 */
const calculateUnit = (str: string | number): [a: number, b: number] => {
  if (typeof str === 'number') {
    str = str + '';
  }
  let integerNum = 0,
    decimal = 0;
  const [a, b] = str.split('.'); // 避免精度损失，完整获取到了小数
  integerNum = parseFloat(a);
  if (b !== undefined) {
    const bNum = parseFloat(b);
    if (bNum) {
      // 小数位不为0
      const len = b.length; // 数字的长度
      const power = 10 ** len; // 解构时 小数点位数需要处理
      decimal = (bNum / power) * 60;
    }
  }
  return [integerNum, decimal];
};
/**
 * 度分秒转换为坐标值
 * @param d
 * @param m
 * @param s
 */
export const DMSToLatlng = (d: number, m: number, s: number, decimals = 5): number => {
  return parseFloat(Number(d + m / 60 + s / 3600).toFixed(decimals));
};
// 提取 rule2 wkt里的经纬度（匹配字符串中的所有数字）
export const extractNumbers = (str: string): number[] => {
  const regex = /-?\d+(\.\d+)?/g;
  const matches = str.match(regex);
  // 如果有匹配结果，将匹配到的字符串转换为数字
  if (matches) {
    return matches.map(Number);
  }
  // 如果没有匹配结果，返回空数组
  return [];
};
