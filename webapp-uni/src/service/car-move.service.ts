import type { Personnel, PersonnelPayload } from "@/models/CarMove";
import { get, post } from "./http";
import type { PageSearch } from "@/models/Page";

/** 根据根据姓名或者车牌号模糊查询对应信息 */
const getListByKeyWord = async (keyWord: string) => {
  return get<Personnel[]>(`/Vehicleinfo/getListByKeyWord?keyWord=${keyWord}`);
};

/**
 * 为用户新增车辆
 * @param data
 * @returns
 */
const save = async (data: PersonnelPayload) => {
  return post<string>(`/Vehicleinfo/save`, { data });
};

const getList = (search: PageSearch) => {
  return post("/Personnel/getList", { data: search });
};
const getInfoPersonnel = (id: string) => {
  return get<Personnel>(`/Personnel/getInfo/${id}`);
};
/** 为用户新增车辆 */
const saveVehicleinfo = async (data: PersonnelPayload) => {
  return post<string>(`/Vehicleinfo/save`, { data });
};
export { getListByKeyWord, save, getList, getInfoPersonnel, saveVehicleinfo };
