const TokenKey = "MT_STORAGE_KEY";
const TokenPrefix = "Bearer ";
function isLogin() {
  return !!uni.getStorageSync(TokenKey);
}
function getToken() {
  return uni.getStorageSync(TokenKey);
}
function setToken(token: string) {
  uni.setStorageSync(TokenKey, token);
}
function clearToken() {
  uni.removeStorageSync(TokenKey);
}

const storage = {
  set(key: string | null, value: string | null) {
    if (key !== null && value !== null) uni.setStorageSync(key, value);
  },
  get(key: string | null) {
    if (key === null) return null;

    return uni.getStorageSync(key);
  },
  setJSON(key: any, jsonValue: any) {
    if (jsonValue !== null) this.set(key, JSON.stringify(jsonValue));
  },
  getJSON(key: any) {
    const value = this.get(key);
    if (value) return JSON.parse(value);
  },
  remove(key: string) {
    uni.removeStorageSync(key);
  },
};

export { clearToken, getToken, isLogin, setToken, TokenPrefix, storage };
