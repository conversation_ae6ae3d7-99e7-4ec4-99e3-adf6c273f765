<template>
    <view class="content">
        <!--静态文件夹-->
        <image class="logo" src="/static/logo.png" />
        <SlSvgIcon name="meeting-16-1-search" size="24" />
        <!-- 小程序端用 <image> 显示背景 -->
        <!-- #ifdef MP-WEIXIN -->
        <image class="home-bg" src="/static/icons/home/<USER>" mode="aspectFill" />
        <view class="text-area">
            <text class="title">{{ title }}</text>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SlSvgIcon from '@/components/SlSVgIcon.vue'
const title = ref('Hello')
</script>

<style lang="scss">
.content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 小程序端 <image> */
.home-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.logo {
    height: 200rpx;
    width: 200rpx;
    margin-top: 200rpx;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 50rpx;
}

.text-area {
    display: flex;
    justify-content: center;
}

.title {
    font-size: 36rpx;
    color: #8f8f94;
}

.icon-16 {
    width: 16px;
    height: 16px;
}
</style>