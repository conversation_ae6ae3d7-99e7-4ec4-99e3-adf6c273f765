<template>
    <div class="u-item p-main-vessel">
        <div class="u-item p-top">
            <SlSVgIcon name="home-前翻-2" size="16" @click="cancel" />
            <!-- <svg class="icon-16" @click="$router.back()">
                <use xlink:href="#icon-home-前翻-2"></use>
            </svg> -->
            <span>新建工作组</span>
            <span class="add-more" @click="toAddGroup">
                <SlSVgIcon name="meeting-16-6" size="16" />
                <!-- <svg class="icon-16">
                    <use xlink:href="#icon-meeting-16-6"></use>
                </svg> -->
            </span>
        </div>
        <div class="u-item search">
            <span>工作组名</span>
            <input type="text" v-model="groupName" @keyup="modelChange($event)" placeholder="请输入工作组名" />
            <!-- <i class="search-icon" @click="getList"></i> -->
        </div>
        <div class="u-item p-content">
            <n-virtual-list item-resizable :item-size="42" :items="moreGroupData.datas" key-field="id">
                <template #default="{ item: vItem }">
                    <div class="row-t">
                        <SlSVgIcon :name="'meeting-20-' + vItem._isChecked ? '14' : '2'" size="20"
                            @click.stop="onChangeExternal(vItem)" />
                        <!-- <svg class="icon-20" @click.stop="onChangeExternal(vItem)">
                            <use xlink:href="#icon-meeting-20-14" v-if="vItem._isChecked"></use>
                            <use xlink:href="#icon-meeting-20-2" v-else></use>
                        </svg> -->
                        <div class="round" :style="{ background: vItem.personnel?.type === 1 ? '#999999' : '#4f7af6' }">
                            {{ vItem.personnel?.name![0] }}</div>
                        <div class="row-name">{{ vItem.personnel?.name }}</div>
                    </div>
                </template>
            </n-virtual-list>
            <!-- </template> -->
        </div>
        <div class="base-btn-footer">
            <up-button type="primary" text="完成" @click="save"></up-button>
            <up-button text="取消" @click="cancel"></up-button>
            <!-- :class="{ 'disabled': getDisable }" -->
            <!-- <n-button type="primary" @click="save"> 完成 </n-button>
            <n-button @click="cancel"> 取消 </n-button> -->
        </div>
    </div>

</template>

<script setup lang="ts">
import useMessage from '@/hooks/use-message';
import type { Personnel } from '@/models/MettingHandle';
import meetHandleService from '@/service/meet-assistant/meet-handle/meet-handle.service';
import useMettingHandleStore from '@/store/modules/metting-handle';
import { storeToRefs } from 'pinia';
import { onMounted, ref } from 'vue';
// import { useRouter } from 'vue-router';
// const router = useRouter();
const groupName = ref('')
// const personnels = ref<any[]>([])
const store = useMettingHandleStore()
const { moreGroupData } = storeToRefs(store)
onMounted(() => {
    groupName.value = moreGroupData.value.name || ''
})
// const getDisable = () => {
//     return !personnels.value.length || !groupName.value || !personnels.value.every(ele => !ele._isChecked)
// }
function onChangeExternal(item: Personnel) {
    item._isChecked = !item._isChecked
}
const modelChange = ($event: Event) => {
    const target = $event.target as HTMLInputElement
    const value = target.value || ''
    moreGroupData.value.name = value
}
const toAddGroup = () => {
    uni.navigateTo({ url: './AddPersonnelGroupPersonnel' })
    // router.push({ name: 'meeting-handle-add-group-personnel' })
}
const message = useMessage()
const save = () => {
    let checkedData: any[] = []
    // personnels.value.forEach(ele => {
    moreGroupData.value.datas?.forEach(ele => {
        if (ele._isChecked) {
            checkedData.push(ele.personnel)
        }
    })
    if (!groupName.value) {
        message.error('请输入工作组名')
        return
    } else if (!checkedData.length) {
        message.error('请选择人员')
        return
    }
    // meetHandleService.saveOrganization({
    //     name: groupName.value,
    //     status: 1,
    //     personnelList: checkedData || []
    // }).then(_ => {
    meetHandleService.saveWorkGoup({
        name: groupName.value,
        personnelList: checkedData || []
    }).then(_ => {
        message.success('保存成功')
        store.resetGroupData()
        uni.navigateTo({ url: './AddPersonnel.vue' })
        // router.push({ name: 'meeting-handle-add-personnel' })
    }).catch(err => {
        message.error(err || '保存失败')
    })
}
const cancel = () => {
    store.resetGroupData()
    uni.navigateTo({ url: './AddPersonnel.vue' })
    // router.push({ name: 'meeting-handle-add-personnel' })
}
</script>

<style lang="css" scoped>
.row-0 {
    color: #666;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
}

/* .p-main-vessel {
    background: #fff;
} */

.p-main-vessel .p-top {
    width: 100%;
    /* height: 64px; */
    height: 44px;
    text-align: center;
    color: white;
    background: #0558bb;
    opacity: 1;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    font-size: 16px;
}

.p-main-vessel .p-top i {
    top: 50%;
    display: block;
    width: 9px;
    height: 16px;
    background-size: cover;
}

.p-main-vessel .p-top i:nth-child(1) {
    background-size: cover !important;
    background: url("@/static/images/meeting/back.png") center no-repeat;
    left: 15px;
}

.p-main-vessel .p-top i:last-child {
    background-size: cover !important;
    background: url("@/static/images/meeting/add.png") center no-repeat;
    right: 15px;
    width: 16px;
    height: 16px;
}

.p-main-vessel .search {
    width: 100%;
    height: 51px;
    padding: 0 15px;
    background: #fff;
    gap: 8px;
    /* padding-top: 9px; */
    position: relative;
    display: flex;
    align-items: center;
    font-size: 14px;
}

.p-main-vessel .search input {
    outline: none;
    height: 30px;
    /* width: 200px; */
    flex: 1;
    padding: 6px 10px;
    opacity: 1;
    border-radius: 3px;
    border: 1px solid rgba(153, 153, 153, 0.5);
    color: #999999;
    font-size: 14px;
}

/* .p-main-vessel .search .search-icon {
    display: block;
    width: 16px;
    height: 16px;
    background: url("@/static/images/meetingRoom/1.png") center no-repeat;
    background-size: cover;
    position: absolute;
    right: 25px;
    top: 15px;
} */

.p_checkall {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 8px;
    padding: 10px 0;
    font-size: 14px;
    color: #333;
}

.p-main-vessel {
    height: 100%;
}

.p-main-vessel .p-content {
    width: 100%;
    height: calc(100vh - 155px);
    overflow-y: auto;
    padding: 10px 15px;
}

.p-main-vessel .p-content .dept-icon {
    color: #3e75fe;
    font-size: 10px;
    width: 16px;
    height: 16px;
    line-height: 16px;
    border: 1px solid #3e75fe;
    text-align: center;
    border-radius: 9px;
    /* margin-right: 9px; */
}

.p-main-vessel .p-content .select-p {
    margin-bottom: 14px;
}

.p-main-vessel .p-content .select-p .row-o {
    color: #666666;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.p-main-vessel .p-content .select-p .row-o i {
    display: block;
    width: 12px;
    height: 12px;
    background: url("@/static/images/meeting/down.png") center no-repeat;
    background-size: cover;
    margin-left: 7px;
}

.row-t {
    display: flex;
    margin-bottom: 6px;
    align-items: center;
    padding-left: 3px;
    gap: 8px;
}

.row-t i {
    display: block;
    width: 20px;
    height: 20px;
    margin-right: 7px;
}

.row-t .round {
    width: 34px;
    height: 34px;
    background: #4f7af6;
    border-radius: 17px;
    color: #ffffff;
    font-size: 14px;
    text-align: center;
    line-height: 34px;
}

.row-t .row-name {
    /* border-bottom: 1px solid rgba(153, 153, 153, 0.2); */
    width: 74%;
    line-height: 34px;
    margin-left: 8px;
    color: #333333;
    font-size: 14px;
}

/* 底部通知按钮 */
.base-btn-footer {
    margin-bottom: 10px;
    padding-top: 10px;
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 20px;
}
.u-item{
    box-sizing: border-box;
}
</style>