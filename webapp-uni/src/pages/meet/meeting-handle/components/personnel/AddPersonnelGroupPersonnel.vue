<template>
    <div class="u-item p-main-vessel" id="p-main-vessel">
        <!-- <div class="p-top">
            <svg class="icon-16" @click="$router.back()">
                <use xlink:href="#icon-home-前翻-2"></use>
            </svg>
            <span>添加人员</span>
            <span v-show="false">
                <svg class="icon-16">
                    <use xlink:href="#icon-meeting-16-6"></use>
                </svg>
            </span>
        </div> -->
        <div class="u-item search">
            <input type="text" v-model="search.keyword" placeholder="请输入姓名/首字母" />
            <SlSVgIcon name="meeting-16-1-search" class="search-icon" size="16" @click="getList" />
            <!-- <svg class="icon-16 search-icon" @click="getList">
                <use xlink:href="#icon-meeting-16-1-search"></use>
            </svg> -->
        </div>
        <div class="u-item p-content">
            <div v-for="item in personnelDatas" class="select-p" :key="item._uuid">
                <div class="row-0">
                    <SlSVgIcon :name="'meeting-20-' + item._isChecked ? '14' : '2'" size="20"
                        @click.stop="onCheckedDepartment(item)" />
                    <!-- <svg class="icon-20" @click.stop="onCheckedDepartment(item)">
                        <use xlink:href="#icon-meeting-20-14" v-if="item._isChecked"></use>
                        <use xlink:href="#icon-meeting-20-2" v-else></use>
                    </svg> -->
                    <span class="dept-icon" v-if="item._type == 2">外</span>
                    {{ item.name }}  
                    <SlSVgIcon :name="'meeting-12-' + item._open ? '9' : '1'" size="12"
                        @click="item._open = !item._open" />
                    <!-- <svg class="icon-12" @click="item._open = !item._open">
                        <use xlink:href="#icon-meeting-12-9" v-if="item._open"></use>
                        <use xlink:href="#icon-meeting-12-1" v-else></use>
                    </svg> -->
                </div>
                <!-- <template v-for="(_,subIdx) in item.members" :key="subIdx"> -->
                <n-virtual-list v-if="item._open" item-resizable :item-size="42" :items="item.children" key-field="id">
                    <template #default="{ item: vItem }">
                        <div class="row-t">
                            <SlSVgIcon :name="'meeting-20-' + item._isChecked ? '14' : '2'" size="20"
                                @click.stop="onChangeExternal(vItem,item)" />
                            <!-- <svg class="icon-20" @click.stop="onChangeExternal(vItem, item)">
                                <use xlink:href="#icon-meeting-20-14" v-if="vItem._isChecked"></use>
                                <use xlink:href="#icon-meeting-20-2" v-else></use>
                            </svg> -->
                            <div class="round" :style="{ background: item._type == 2 ? '#999999' : '#4f7af6' }">
                                {{ vItem.name![0] }}</div>
                            <div class="row-name">{{ vItem.name }}</div>
                        </div>
                    </template>
                </n-virtual-list>
                <!-- </template> -->
            </div>
        </div>
        <div class="base-btn-footer">
            <up-button type="primary" text="完成" @click="save"></up-button>
            <up-button text="取消" @click="cancel"></up-button>
            <!-- <n-button type="primary" @click="save"> 完成 </n-button>
            <n-button @click="cancel"> 取消 </n-button> -->
        </div>
    </div>

</template>
<script setup lang="ts">
import type { Personnel } from '@/models/MettingHandle';
import { PersonnelSearch, type PersonnelAndOrganization2 } from '@/models/Personnel';
import meetHandleService from '@/service/meet-assistant/meet-handle/meet-handle.service';
// import { NButton, NVirtualList, useMessage, type MessageReactive } from 'naive-ui';
import { storeToRefs } from 'pinia';
import { onMounted, ref } from 'vue';
// import { useRouter } from 'vue-router';
import { useLoading } from '@/hooks';
import useMettingHandleStore from '@/store/modules/metting-handle';
import { generatePersonnelList, type PersonnelAndOrganizationGroup } from '../../meeting-handle-data';
// const router = useRouter();
const { moreGroupData } = storeToRefs(useMettingHandleStore())
const personnelDatas = ref<PersonnelAndOrganizationGroup[]>([])
onMounted(() => {
    getList()
})
// onUnmounted(() => {
//     loadingMsg?.destroy()
// })
function onChangeExternal(item: Personnel, _parentItem: PersonnelAndOrganizationGroup) {
    item._isChecked = !item._isChecked
    // parentItem._isChecked = parentItem.children?.every(ele => ele._isChecked)
    // 其他组里，同一个用户的勾选状态，也要改变
    personnelDatas.value.forEach(ele => {
        ele.children?.forEach(e => {
            if (e.id == item.id) {
                e._isChecked = item._isChecked
            }
        })
        ele._isChecked = ele.children?.every(m => m._isChecked)
    })
}
const search = ref(new PersonnelSearch())
// let loadingMsg: MessageReactive | null = null
// const message = useMessage()
const { showLoading, hideLoading } = useLoading()
function getList() {
    // if (loadingMsg != null) {
    //     loadingMsg.destroy()
    //     loadingMsg = null
    // }
    // loadingMsg = message.loading('数据加载中...')
    showLoading()
    search.value.groupByOrganization = true
    search.value.excludeCurrentUser = true
    search.value.groupByWorkgroup = true
    search.value.ifGrouped = true
    search.value.ifPage = false
    meetHandleService.getListPersonnel(search.value).then((res) => {
        const datas = res as PersonnelAndOrganization2
        const dataList = generatePersonnelList(datas)
        dataList.forEach(ele => {
            ele._open = true
            ele.children?.forEach(e => {
                const cacheItem = moreGroupData.value.datas.find(m => m.personnelId === e.id)
                if (cacheItem) e._isChecked = cacheItem._isChecked
                else e._isChecked = false
            })
            ele._isChecked = ele.children?.every(m => m._isChecked)
        })
        personnelDatas.value = dataList || []
        console.log("🚀 ~ meetHandleService.getListPersonnel ~ dataList:", dataList)
        // const datas = (res as PersonnelAndOrganization[] || []).filter(ele => ele.members?.length);
        // datas.forEach(ele => {
        //     ele._uuid = uuidv4() // 
        //     ele._open = true
        //     ele.members?.forEach(item => {
        //         const cacheItem = moreGroupData.value.datas.find(e => e.personnelId === item.id)
        //         if (cacheItem) item._isChecked = cacheItem._isChecked
        //         else item._isChecked = false
        //     })
        //     ele._isChecked = ele.members?.every(e => e._isChecked)
        // })
        // personnelDatas.value = datas || []
    }).finally(() => {
        hideLoading()
        // loadingMsg?.destroy()
        // loadingMsg = null
    })
}
const save = () => {
    // let checkedData: (Meetingpersonnel & { _isChecked: boolean })[] = []
    personnelDatas.value.forEach(ele => {
        ele.children?.forEach(item => {
            const checkedItem = moreGroupData.value.datas.find(c => c.personnelId === item.id)
            if (checkedItem) {
                checkedItem._isChecked = item._isChecked || false
            } else {
                if (item._isChecked) {
                    moreGroupData.value.datas.push({ personnel: item._init, personnelId: item.id || '', _isChecked: true })
                }
            }
        })
    })
    // moreGroupData.value.datas = checkedData
    // 回到添加人员
    uni.navigateBack()
    // router.back()
}
const cancel = () => {
    uni.navigateBack()
    // router.back()
}
/** 勾选上级单位 */
const onCheckedDepartment = (item: PersonnelAndOrganizationGroup) => {
    item._isChecked = !item._isChecked
    item.children?.forEach(ele => ele._isChecked = item._isChecked)
    personnelDatas.value.filter(ele => ele._uuid != item._uuid).forEach(ele => {
        ele.children?.forEach(e => {
            // 同步勾选
            if (item.children?.find(m => m.id === e.id)) e._isChecked = item._isChecked
        })
        ele._isChecked = ele.children?.every(m => m._isChecked)
    })
}
</script>
<style lang="css" scoped>
.row-0 {
    color: #666;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
}
.u-item{
    box-sizing: border-box;
}
.p-main-vessel .p-top {
    width: 100%;
    /* height: 64px; */
    height: 44px;
    text-align: center;
    color: white;
    /* background: #0558bb; */
    background: linear-gradient(180deg, #084d9f 0%, #035cc5 100%);
    opacity: 1;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    font-size: 16px;
}

.p-main-vessel .p-top i {
    top: 50%;
    display: block;
    width: 9px;
    height: 16px;
    background-size: cover;
}

.p-main-vessel .p-top i:nth-child(1) {
    background-size: cover !important;
    background: url("@/static/images/meeting/back.png") center no-repeat;
    left: 15px;
}

.p-main-vessel .p-top i:last-child {
    background-size: cover !important;
    background: url("@/static/images/meeting/add.png") center no-repeat;
    right: 15px;
    width: 16px;
    height: 16px;
}

.p-main-vessel .search {
    width: 100%;
    height: 51px;
    flex-shrink: 0;
    padding: 0 15px;
    background: #035bc2;
    padding-top: 9px;
    position: relative;
}

.p-main-vessel .search input {
    outline: none;
    height: 30px;
    width: 100%;
    padding: 6px 10px;
    opacity: 1;
    border-radius: 3px;
    border: 1px solid rgba(153, 153, 153, 0.5);
    color: #999999;
    font-size: 14px;
}

.search-icon {
    display: block;
    position: absolute;
    right: 25px;
    top: 15px;
}

.p_checkall {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 8px;
    padding: 10px 0;
    font-size: 14px;
    color: #333;
}

.p-main-vessel {
    height: 100%;
}

.p-main-vessel .p-content {
    width: 100%;
    height: calc(100vh - 155px);
    overflow-y: auto;
    padding: 10px 15px;
}

.p-main-vessel .p-content .dept-icon {
    color: #3e75fe;
    font-size: 10px;
    width: 16px;
    height: 16px;
    line-height: 16px;
    border: 1px solid #3e75fe;
    text-align: center;
    border-radius: 9px;
    /* margin-right: 9px; */
}

.p-main-vessel .p-content .select-p {
    margin-bottom: 14px;
}

.p-main-vessel .p-content .select-p .row-o {
    color: #666666;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.p-main-vessel .p-content .select-p .row-o i {
    display: block;
    width: 12px;
    height: 12px;
    background: url("@/static/images/meeting/down.png") center no-repeat;
    background-size: cover;
    margin-left: 7px;
}

.p-main-vessel .p-content .select-p .row-t {
    display: flex;
    margin-bottom: 6px;
    align-items: center;
    gap: 8px;
}

.p-main-vessel .p-content .select-p .row-t i {
    display: block;
    width: 20px;
    height: 20px;
    margin-right: 7px;
}

.p-main-vessel .p-content .select-p .row-t .round {
    width: 34px;
    height: 34px;
    background: #4f7af6;
    border-radius: 17px;
    color: #ffffff;
    font-size: 14px;
    text-align: center;
    line-height: 34px;
}

.p-main-vessel .p-content .select-p .row-t .row-name {
    border-bottom: 1px solid rgba(153, 153, 153, 0.2);
    width: 74%;
    line-height: 34px;
    margin-left: 8px;
    color: #333333;
    font-size: 14px;
}

/* 底部通知按钮 */
.base-btn-footer {
    margin-bottom: 10px;
    /* height: 40px; */
    padding-top: 10px;
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 20px;
}

.n-button {
    width: 140px;
    height: 41px;
}

.add-more {
    position: relative;
}

.more-modal-container {
    position: absolute;
    top: 25px;
    right: -5px;
    z-index: 10;
    /* width: 134; */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.modal-item {
    width: 134px;
    height: 44px;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #3E3E3E;
    color: #fff;
    font-size: 14px;
}

.modal-item span {
    display: inline-block;
    flex: 1;
    display: flex;
    justify-content: flex-start;
    padding-left: 10px;
}

.modal-item.top {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.modal-item.bottom {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}
</style>