<template>
    <div class="inner-container">
        <div class="input-c">
            <span class="label" :class="{ require: isRequire }">{{ title }}</span>
            <textarea id="content" v-model="content" :placeholder="'请输入' + title"></textarea>
        </div>
        <div class="base-btn-footer">
            <up-button type="primary" text="完成" @click="save"></up-button>
            <up-button text="取消" @click="cancel"></up-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import useMettingHandleStore from '@/store/modules/metting-handle';
import { computed, onMounted, ref } from 'vue';
// const { title = '会议名称', isRequire = false } = defineProps<{ title: string, isRequire: boolean }>()
const props = defineProps<{ title: string, isRequire: boolean }>()
const content = ref('')
const change = ref(false)
// 计算属性优化条件判断
const targetKey = computed(() => {
    const { title } = props
    switch (title) {
        case '会议主题': return 'title';
        case '会议内容': return 'content';
        default: return null; // 处理未知情况
    }
});
const store = useMettingHandleStore()
// 直接使用 onLoad 钩子获取参数
onLoad(() => {
    const key = toValue(targetKey);
    content.value = store.mettingHandle[key as 'title' | 'content'] || '';
});
onMounted(() => {
    // const key = targetKey.value;
    // content.value = store.mettingHandle[key as 'title' | 'content'] || '';
})
function save() {
    const key = targetKey.value;
    if (content.value) {
        // 类型断言确保类型安全
        store.mettingHandle[key as 'title' | 'content'] = content.value;
        uni.navigateBack()
        // router.back()
        console.log("🚀 ~ save ~ store.mettingHandle:", store.mettingHandle)
    }
}
function cancel() {
    uni.navigateBack()
    const key = targetKey.value;
    content.value = store.mettingHandle[key as 'title' | 'content'] || '';
}
</script>

<style lang="css" scoped>
/* @import "@/pages/meet-assistant/metting-handle/styles/metting-base.css"; */
@import "../../styles/metting-base.css";

.input-c {
    flex: 1;
}

.input-c #content {
    margin-top: 10px;
    width: 100%;
    display: block;
    outline: none;
    border: 1px solid rgba(153, 153, 153, 0.2);
    /* Converted #99999933 to rgba */
    min-height: 163px;
    border-radius: 5px;
    padding: 8px;
    color: black;
    font-size: 14px;
}

.input-c .label {
    font-size: 14px;
}

.require {
    position: relative;
    padding-left: 2px;
}

.require::before {
    content: '*';
    position: absolute;
    color: #e50101;
    top: 0;
    left: -7px;
}

/* 底部通知按钮 */
.base-btn-footer {
    margin: 10px 0;
    height: 40px;
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 20px;
}
</style>