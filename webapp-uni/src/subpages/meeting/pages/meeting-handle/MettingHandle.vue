<template>
    <div id="meeting-handle-main">
        <div class="meeting-handle-container">
            <div class="handle-item item__border-bottom">
                <label class="require">会议名称</label>
                <div class="content" @click="goMeetingHandleTitle('title')">
                    <span class="placeholder" :title="mettingHandle.title">
                        <span class="placeholder_content">
                            {{ mettingHandle.title || '请输入主题' }}
                        </span>
                        <SlSvgIcon name="meeting-12-1" size="12" />
                        <!-- <svg class="icon-12">
                            <use xlink:href="#icon-meeting-12-1"></use>
                        </svg> -->
                    </span>
                </div>
            </div>
            <div class="handle-item item__border-bottom">
                <label>内容</label>
                <div class="content">
                    <span class="placeholder" :title="mettingHandle.content" @click="goMeetingHandleTitle('content')">
                        <span class="placeholder_content">
                            {{ mettingHandle.content || '请输入会议内容' }}
                        </span>
                        <SlSvgIcon name="meeting-12-1" size="12" />
                        <!-- <svg class="icon-12">
                            <use xlink:href="#icon-meeting-12-1"></use>
                        </svg> -->
                    </span>
                </div>
            </div>
            <div class="file-item">
                <label>附件</label>
                <div class="content">
                    <div v-if="fileDatas?.length" class="content-file-box">
                        <div class="content-file-item" v-for="(item, index) in fileDatas" :key="index">
                            <div @click.stop="previewFile(item)">
                                <SlSvgIcon :name="'meeting-22-' + item.fileType?.substring(1)" size="22"
                                    @click.stop="deleteFile(item)" />
                                <!-- <svg class="icon-22">
                                    <use :xlink:href="'#icon-meeting-22-' + item.fileType?.substring(1)"></use>
                                </svg> -->
                            </div>
                            <div @click.stop="previewFile(item)" style="max-width: 80%;">
                                <div class="file-name">{{ item.fullName }}</div>
                                <div class="file-size">{{ convertBytes(item.fileSize!) }}</div>
                            </div>
                            <SlSvgIcon name="meeting-delete" size="12" @click.stop="deleteFile(item)" />
                            <!-- <svg class="icon-12" @click.stop="deleteFile(item)">
                                <use xlink:href="#icon-meeting-delete"></use>
                            </svg> -->
                        </div>
                    </div>
                    <!-- 最多一个 -->
                    <!-- <n-upload v-if="fileDatas?.length < 1" action="/api/Content/upload" :show-file-list="false"
                        :style="{ width: fileDatas?.length ? '20%' : '100%' }"
                        :trigger-style="{ width: '100%', display: 'inline-flex', 'justify-content': 'flex-end' }"
                        @before-upload="beforeUpload" :custom-request="firmwareMainUpload">
                        <span class="placeholder">
                            {{ fileDatas?.length ? '' : '请添加' }}
                            <svg class="icon-12">
                                <use xlink:href="#icon-meeting-12-1"></use>
                            </svg>
                        </span>
                    </n-upload> -->
                </div>
            </div>
            <!-- 预定时间 -->
            <div class="handle-item item__margin-top">
                <label class="require">预定时间</label>
                <div class="content">
                    <span class="placeholder" @click="$router.push({ name: 'meeting-handle-time' })">
                        <span class="placeholder_content">
                            {{ formatTime }}
                            <!-- {{ mettingHandle.startTime && mettingHandle.endTime ? `${mettingHandle.startTime} -
                            ${mettingHandle.endTime}` : '请选择' }} -->
                        </span>
                        <SlSvgIcon name="meeting-12-1" size="12" />
                        <!-- <svg class="icon-12">
                            <use xlink:href="#icon-meeting-12-1"></use>
                        </svg> -->
                    </span>
                </div>
            </div>
            <!-- 参会人员 -->
            <div class="handle-item item__margin-top">
                <label class="require">参会人员</label>
                <span id="data-participant">({{ personList.length }})</span>
                <div class="content">
                    <span class="placeholder" @click="toMorePersonnel">
                        更多人员
                        <SlSvgIcon name="meeting-12-1" size="12" />
                        <!-- <svg class="icon-12">
                            <use xlink:href="#icon-meeting-12-1"></use>
                        </svg> -->
                    </span>
                </div>
            </div>
            <!-- 请添加 -->
            <div class="participant-box">
                <template v-for="(item, idx) of personList" :key="item.id">
                    <div class="participant-item" v-if="idx < 10">
                        <div class="circle circle-large"
                            :class="{ 'circle-blue': item?.personnel?.type == 0, 'circle-out-icon': item?.personnel?.type == 1 }">
                            {{ item?.personnel?.name?.substring(0, 1) }}</div>
                        <span class="participant-name"> {{ item?.personnel?.name }}
                        </span>
                    </div>
                </template>
                <div class="add-participant participant-item" v-if="personList?.length < 10" @click="toMorePersonnel">
                    <span class="add-icon"> + </span>
                    <span class="add-text participant-name">添加</span>
                </div>
                <!-- </div> -->
            </div>
            <!-- 会议室 -->
            <div class="handle-item">
                <label class="require">会议室</label>
                <!-- radio radio-active -->
                <div class="radio-box" @click="onAutoCheck">
                    <SlSvgIcon name="meeting-12-3" v-if="mettingHandle.roomSelectionType == 1" size="12" />
                    <SlSvgIcon name="meeting-12-2" v-else size="12" />
                    <!--  <svg class="icon-12">
                        <use xlink:href="#icon-meeting-12-3" v-if="mettingHandle.roomSelectionType == 1"></use>
                        <use xlink:href="#icon-meeting-12-2" v-else></use>
                    </svg> -->
                    智能推荐
                </div>
                <div class="radio-box" @click="onManualCheck">
                    <SlSvgIcon name="meeting-12-3" v-if="mettingHandle.roomSelectionType == 0" size="12" />
                    <SlSvgIcon name="meeting-12-2" v-else size="12" />
                    <!--  <svg class="icon-12">
                        <use xlink:href="#icon-meeting-12-3" v-if="mettingHandle.roomSelectionType == 0"></use>
                        <use xlink:href="#icon-meeting-12-2" v-else></use>
                    </svg> -->
                    手动选择
                </div>
            </div>
            <div class="meeting-room-out">
                <div class="meeting-room-box" v-if="mettingHandle.roomSelectionType == 1">
                    <BaseEmpty v-if="!recommendRoomData.id">
                        <span class="meeting-room_placeholder">
                            {{ hasRecommendRoom ? '暂无推荐' : '待推荐' }}
                        </span>
                    </BaseEmpty>
                    <template v-else>
                        <div class="meeting-room-head" style="z-index: 3; position: relative">
                            <span class="room-title">
                                <SlSvgIcon v-show="selectedRoom.roomName" name="meeting-12-4" size="12" />
                                <!-- <svg class="icon-12" v-show="selectedRoom.roomName">
                                    <use xlink:href="#icon-meeting-12-4"></use>
                                </svg> -->
                                {{ selectedRoom.roomName }}
                            </span>
                        </div>
                        <div class="room-container">
                            <div class="meeting-room-content"
                                style="display: flex; justify-content: center; position: relative;pointer-events: none;">
                                <!-- <SlMeetingRoom5 v-if="selectedRoom.roomF == 5" :selectedId="selectedRoom.id"
                                    @update:selected-id="meetingRoomChange" />
                                <SlMeetingRoom8 v-if="selectedRoom.roomF == 8" :selectedId="selectedRoom.id"
                                    @update:selected-id="meetingRoomChange" />
                                <SlMeetingRoom9 v-if="selectedRoom.roomF == 9" :selectedId="selectedRoom.id"
                                    @update:selected-id="meetingRoomChange" />
                                <SlMeetingRoom13 v-if="selectedRoom.roomF == 13" :selectedId="selectedRoom.id"
                                    @update:selected-id="meetingRoomChange" /> -->
                            </div>
                        </div>
                    </template>
                </div>
                <div v-else class="meeting-room-box">
                    <div class="meeting-room-head" style="z-index: 3; position: relative">
                        <span class="room-title">
                            <SlSvgIcon v-show="selectedRoom.roomName" name="meeting-12-4" size="12" />
                            <!-- <svg class="icon-12" v-show="selectedRoom.roomName">
                                <use xlink:href="#icon-meeting-12-4"></use>
                            </svg> -->
                            {{ selectedRoom.roomName }}
                        </span>
                        <!-- v-model="selectedRoom.roomF" -->
                        <select class="room-select" :value="selectedRoom.roomF" @change="changeRoomF()">
                            <option :value="5">5F</option>
                            <option :value="8">8F</option>
                            <option :value="9">9F</option>
                            <option :value="13">13F</option>
                        </select>
                    </div>
                    <div class="room-container">
                        <div class="meeting-room-content"
                            style="display: flex; justify-content: center; position: relative">
                            <!-- <SlMeetingRoom5 v-if="selectedRoom.roomF == 5" :selectedId="selectedRoom.id"
                                @update:selected-id="meetingRoomChange" />
                            <SlMeetingRoom8 v-if="selectedRoom.roomF == 8" :selectedId="selectedRoom.id"
                                @update:selected-id="meetingRoomChange" />
                            <SlMeetingRoom9 v-if="selectedRoom.roomF == 9" :selectedId="selectedRoom.id"
                                @update:selected-id="meetingRoomChange" /> -->
                            <!-- <img :src="meetingRoomUrl" alt=""> -->
                            <!-- <SlMeetingRoom13 :selectedId="selectedRoom.id" @update:selected-id="meetingRoomChange" /> -->
                            <!-- <scroll-view :scroll-y="true" style="height: 800rpx;">
                                <web-view src="http://192.168.1.145:9900/meeting-room" :webviewStyles="{
                                    width: '100rpx', height: '100rpx'
                                }" :fullscreen="false" :update-title="false"></web-view>
                            </scroll-view> -->
                        </div>
                    </div>
                    <div class="meeting-room-rect">
                        <div>
                            <span class="rect"></span>
                            可选
                        </div>
                        <div>
                            <span class="rect" style="border-color: #999999; background-color: #e4e4e4"></span>
                            不可选
                        </div>
                        <div>
                            <span class="rect" style="border-color: #ff3737; background-color: #ffe6e4"></span>
                            已选
                        </div>
                    </div>
                </div>
            </div>
            <!-- 会议服务 -->
            <div class="handle-item-out">
                <div class="handle-item">
                    <label>会议服务</label>
                    <div class="content" id="to-meeting-service">
                        <!-- switch-active -->
                        <div class="switch-box" @click="mettingHandle._isServiceOpen = !mettingHandle._isServiceOpen">
                            <SlSvgIcon v-if="!mettingHandle._isServiceOpen" name="meeting-36-1" size="36" />
                            <SlSvgIcon v-else name="meeting-36-2" size="36" />
                            <!-- <svg class="icon-36">
                                <use xlink:href="#icon-meeting-36-1" v-if="!mettingHandle._isServiceOpen"></use>
                                <use xlink:href="#icon-meeting-36-2" v-else></use>
                            </svg> -->
                        </div>
                    </div>
                </div>
                <div class="meeting-service-box-out" v-if="mettingHandle._isServiceOpen">
                    <div class="meeting-service-box">
                        <div>会议服务要求</div>
                        <div class="meeting-service-item" v-for="(item, index) in serviceOptions" :key="index">
                            <div class="service-title" @click.stop="onServiceCheck(item)"
                                :style="item.checked ? 'color:#4F7AF6;' : ''">
                                <SlSvgIcon name="meeting-12-3" size="12" v-if="item.checked" />
                                <SlSvgIcon name="meeting-12-2" size="12" v-else />
                                <!--  <svg class="icon-12">
                                    <use xlink:href="#icon-meeting-12-3" v-if="item.checked"></use>
                                    <use xlink:href="#icon-meeting-12-2" v-else></use>
                                </svg> -->
                                {{ item.title }}
                            </div>
                            <div class="service-content" v-show="item.checked && item.children?.length">
                                <div class="service-content-item" v-for="(subItem, subIndex) in item.children"
                                    :key="subIndex">
                                    <span class="label">{{ subItem.title }}：</span>
                                    <div class="content">
                                        <up-select v-model:current="subItem.value" label="按参会人数"
                                            :options="subItem.options"></up-select>
                                        <!-- <select v-if="subItem.type == 'select'" :value="subItem.value"
                                            @change="selectChange($event, subItem, item)">
                                            <option :value=0>按参会人数</option>
                                            <option :value="opt.value" v-for="opt of subItem.options" :key="opt.value">
                                                {{ opt.label }}</option>
                                        </select> -->
                                        <input type="text" v-if="subItem.type == 'input'"
                                            :value="(subItem.value) as string"
                                            @input="inputChange($event, subItem, item)" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="handle-item-out">
                <div class="handle-item">
                    <label>会议提醒</label>
                    <div class="content" @click="toMeetingRemind">
                        <span class="placeholder">
                            <span class="placeholder_content">
                                {{ notifyMinutesBeforeText || '请选择' }}
                            </span>
                            <SlSvgIcon name="meeting-12-1" size="12" />
                            <!-- <svg class="icon-12">
                                <use xlink:href="#icon-meeting-12-1"></use>
                            </svg> -->
                        </span>
                    </div>
                </div>
            </div>
            <div class="handle-item">
                <label>通知方式</label>
                <div class="content" @click="toNotify">
                    <span class="placeholder">
                        <span v-if="!!!mettingHandle.notifyBySms && !!!mettingHandle.notifyByVoiceCall">请选择</span>
                        <span v-else class="placeholder_content">
                            {{ mettingHandle.notifyBySms == 0 ? '' : '短信通知' }}
                            {{ mettingHandle.notifyByVoiceCall == 0 ? '' : '机器人语音电话通知' }}
                        </span>
                        <SlSvgIcon name="meeting-12-1" size="12" />
                        <!-- <svg class="icon-12">
                            <use xlink:href="#icon-meeting-12-1"></use>
                        </svg> -->
                    </span>
                </div>
            </div>
            <div class="handle-item footer-btn">
                <up-button type="primary" :text="isEdit ? '保存并重新提交' : '预定'" @click="onSave"></up-button>
                <!-- <n-button class="save-btn" type="primary" @click="onSave">
                    {{ isEdit ? '保存并重新提交' : '预定' }}
                </n-button> -->
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import BaseEmpty from "@/components/BaseEmpty.vue";
// import SlMeetingRoom13 from "@/components/SlMeetingRoom13.vue";
// import SlMeetingRoom5 from "@/components/SlMeetingRoom5.vue";
// import SlMeetingRoom8 from "@/components/SlMeetingRoom8.vue";
// import SlMeetingRoom9 from "@/components/SlMeetingRoom9.vue";
import type { Content } from "@/models/Content";
import type { Meetingroom } from '@/models/MettingHandle';
import meetHandleService from '@/service/meet-assistant/meet-handle/meet-handle.service';
import { convertBytes } from "@/utils/transformBytes";
import SlSvgIcon from '@/components/SlSVgIcon.vue'
// import axios from "axios";
// import { NButton, NUpload, useMessage, type UploadCustomRequestOptions, type UploadSettledFileInfo } from 'naive-ui';
import { storeToRefs } from 'pinia';
import { computed, onMounted, ref, toRaw } from 'vue';
// import { useRouter } from 'vue-router';
import useMessage from "@/hooks/use-message";
import useMettingHandleStore from "@/store/modules/metting-handle";
import { generateSelectedMeetingRoom, MeetingRoomNameMap, MeetingRoomNameMapForService, type SelectedMeetingRoom } from "./meeting-handle-data";
import { useLoading } from "@/hooks";
interface IServiceOption {
    key: string;
    title: string;
    type?: 'select' | 'input';
    checked?: boolean;
    options?: { label: string; value: string | number }[];
    children?: IServiceOption[];
    value?: number | string;
}
// const props = defineProps<{ id?: string }>()
const serviceOptions = ref<IServiceOption[]>([
    {
        title: '用餐',
        checked: false,
        key: 'useFood',
        value: 0,
        children: [
            {
                title: '人数',
                key: 'personNum',
                type: 'select',
                options: Array.from({ length: 30 }, (_, index) => {
                    return {
                        name: `${index + 1}人`,
                        id: index + '',
                        label: `${index + 1}人`,
                        value: index + 1,
                    };
                }),
            },
            { title: '时间', type: 'input', key: 'time' },
        ],
    },
    {
        title: '桌牌',
        checked: false,
        key: 'tableCard',
        value: 1,
        children: [
            {
                title: '数量',
                key: 'num',
                type: 'select',
                options: Array.from({ length: 30 }, (_, index) => {
                    return {
                        name: `${index + 1}人`,
                        id: index + '',
                        label: `${index + 1}人`,
                        value: index + 1,
                    };
                }),
            },
            { title: '内容', type: 'input', key: 'content' },
        ],
    },
    {
        title: '纸笔',
        checked: false,
        key: 'pencil',
        value: 2,
        children: [
            {
                title: '数量',
                type: 'select',
                key: 'num',
                options: Array.from({ length: 30 }, (_, index) => {
                    return {
                        name: `${index + 1}人`,
                        id: index + '',
                        label: `${index + 1}人`,
                        value: index + 1,
                    };
                }),
            },
        ],
    },
    {
        title: '茶水',
        checked: false,
        key: 'teaWater',
        value: 3,
        children: [
            {
                title: '数量',
                type: 'select',
                key: 'num',
                options: Array.from({ length: 30 }, (_, index) => {
                    return {
                        name: `${index + 1}人`,
                        id: index + '',
                        label: `${index + 1}人`,
                        value: index + 1,
                    };
                }),
            },
        ],
    },
    {
        title: '摄影',
        checked: false,
        key: 'fruitPlate',
        value: 4,
        // children: [
        //     {
        //         title: '数量',
        //         type: 'select',
        //         key: 'num',
        //         options: Array.from({ length: 30 }, (_, index) => {
        //             return {
        //                 label: `${index + 1}人`,
        //                 value: index + 1,
        //             };
        //         }),
        //     },
        // ],
    },
]);
const isEdit = ref(false)
const selectedRoom = ref<SelectedMeetingRoom>({
    id: '1031',
    roomF: 13,
    roomName: '',
})
const { showLoading, hideLoading } = useLoading()
// const router = useRouter()
const store = useMettingHandleStore()
const { mettingHandle, timeChanged, personnelChanged } = storeToRefs(store)
const { reset } = store
const personList = computed(() => mettingHandle.value.meetingpersonnelList || [])
const notifyMinutesBeforeText = computed(() => {
    const { sendInstantNotification, notifyMinutesBefore } = mettingHandle.value
    if (sendInstantNotification == null && notifyMinutesBefore == null) return ''
    if (sendInstantNotification == 1) return '立即通知'
    else return notifyMinutesBefore == null ? '' : `开始前${notifyMinutesBefore}分钟`
})
/** 统一修改日期格式为2025.05.14 14:00~2025.05.14 16:00 */
const formatTime = computed(() => {
    const { startTime = '', endTime = '' } = mettingHandle.value
    const _formatStart = startTime.replace(/-/g, '.')
    const _formatEnd = endTime.replace(/-/g, '.')
    return startTime && endTime ? `${_formatStart} - ${_formatEnd}` : '请选择';
})
/** 会议室 */

const goMeetingHandleTitle = (type: 'title' | 'content') => {
    // 处理标题逻辑
    if (type == 'title') { // 会议主题
        uni.navigateTo({ url: './components/meeting-title/index?title=会议主题' })
        // router.push({ name: 'meeting-title' })
    } else { // 会议内容
        uni.navigateTo({ url: './components/meeting-title/index?title=会议内容' })
        // router.push({ name: 'meeting-content' })
    }
}
// const selectChange = (event: Event, _item: IServiceOption, parentItem: IServiceOption) => {
//     const target = event.target as HTMLSelectElement
//     const value = Number(target.value)
//     const type = parentItem.value as number
//     _item.value = value
//     if (type == null) return;
//     if (!mettingHandle.value.meetingfacilityList?.length) mettingHandle.value.meetingfacilityList = []
//     const facilityItem = mettingHandle.value.meetingfacilityList?.find(ele => ele.type == type)
//     if (facilityItem) {
//         facilityItem.quantity = value
//         facilityItem._selectType = 'select'
//     } else {
//         // 数量
//         mettingHandle.value.meetingfacilityList?.push({ type, quantity: value, _selectType: 'select' })
//     }
// }
// TODO: uni event 类型
const inputChange = (event: any, _item: IServiceOption, parentItem: IServiceOption) => {
    const target = event.target as HTMLInputElement
    const value = target.value
    const type = parentItem.value as number
    _item.value = value
    if (type == null) return;
    if (!mettingHandle.value.meetingfacilityList?.length) mettingHandle.value.meetingfacilityList = []
    const facilityItem = mettingHandle.value.meetingfacilityList?.find(ele => ele.type == type)
    if (facilityItem) {
        facilityItem.content = value
        facilityItem._selectType = 'input'
    } else {
        // 数量
        mettingHandle.value.meetingfacilityList?.push({ type, content: value, _selectType: 'input' })
    }
}
/** 勾选具体的服务前缀 */
const onServiceCheck = (item: IServiceOption) => {
    console.log("🚀 ~ MettingHandle.vue ~ onServiceCheck ~ item:", item)
    item.checked = !item.checked
    const facilityIdx = mettingHandle.value.meetingfacilityList?.findIndex(ele => ele.type == item.value!)
    if (item.checked) {
    } else {
        if (facilityIdx != undefined && facilityIdx > -1)
            mettingHandle.value.meetingfacilityList?.splice(facilityIdx, 1)
        item.children?.forEach(ele => {
            ele.value = undefined
        })
    }
}
const onAutoCheck = () => {
    mettingHandle.value.roomSelectionType = 1
    timeChanged.value = true
    personnelChanged.value = true
    getRecommendRoom()
}
const onManualCheck = () => {
    mettingHandle.value.roomSelectionType = 0
}
// const removeGuard = router.beforeEach((to) => {
//     if (!to.fullPath.includes('meeting') || to.name == 'meeting-info') {
//         reset()
//     }
//     personnelChanged.value = false
//     timeChanged.value = false
//     // 移除守卫
//     removeGuard();
// });
// 生命周期钩子
onMounted(async () => {
    // console.log("MettingHandle.vue ~ onMounted ~ props.id:", meetingRoomUrl.value)
    // if (props?.id) {
    //     isEdit.value = true
    //     // getInfo(props.id)
    // } else {
    //     // 新增
    //     if (mettingHandle.value.roomSelectionType == 1) {
    //         await getRecommendRoom()
    //         if (selectedRoom.value.id) {
    //             console.log(selectedRoom.value.id, 'id')
    //             return
    //         }
    //     }
    //     if (mettingHandle.value?._meetingroom) {
    //         // 读取已缓存的会议室
    //         const { id: romId, roomF, roomName } = mettingHandle.value._meetingroom
    //         selectedRoom.value.id = romId
    //         selectedRoom.value.roomF = roomF
    //         selectedRoom.value.roomName = roomName
    //         // 重新读取上一次智能会议室的图
    //         if (mettingHandle.value.roomSelectionType == 1) {
    //             recommendRoomData.value.id = romId
    //             hasRecommendRoom.value = !!romId
    //         }
    //     }
    //     // 回填会议服务
    //     if (mettingHandle.value.meetingfacilityList?.length) {
    //         serviceOptions.value.forEach(item => {
    //             const facilityItem = mettingHandle.value.meetingfacilityList?.find(ele => ele.type == item.value)
    //             item.checked = !!facilityItem
    //             if (facilityItem) {
    //                 item.children?.forEach(ele => {
    //                     if (ele.type == 'select') {
    //                         ele.value = facilityItem.quantity
    //                     } else {
    //                         ele.value = facilityItem.content
    //                     }
    //                 })
    //             }
    //         })
    //     }
    // }
})
function getInfo(id: string) {
    showLoading()
    meetHandleService.getInfoMeeting(id).then(async (res) => {
        if (mettingHandle.value?.id == id) {
            // 已有缓存数据，上一次更改已同步到缓存里
            mettingHandle.value.status = res.status // 更新一下当前会议状态，避免停留太长，会议状态已发生改变
            // 更新会议室数据
            if (mettingHandle.value.roomSelectionType == 1) {
                await getRecommendRoom()
                if (selectedRoom.value.id) {
                    return
                }
            }
            if (mettingHandle.value?._meetingroom?.id) {
                // 先读取已缓存的会议室
                const { id: romId, roomF, roomName } = mettingHandle.value._meetingroom
                selectedRoom.value.id = romId
                selectedRoom.value.roomF = roomF
                selectedRoom.value.roomName = roomName
            } else if (res.meetingroom?.id) {
                // 若没有，读取详细的
                const selected = generateSelectedMeetingRoom(res.meetingroom)
                selectedRoom.value = selected
                mettingHandle.value._meetingroom = {
                    id: selected.id,
                    roomF: selected.roomF,
                    roomName: selected.roomName
                }
            }
            if (mettingHandle.value.roomSelectionType == 1) {
                // 更新智能推荐的会议室图
                recommendRoomData.value.id = selectedRoom.value?.id || ''
                hasRecommendRoom.value = !!selectedRoom.value?.id
            }
            // 回填会议服务
            if (mettingHandle.value.meetingfacilityList?.length) {
                serviceOptions.value.forEach(item => {
                    const facilityItem = mettingHandle.value.meetingfacilityList?.find(ele => ele.type == item.value)
                    item.checked = !!facilityItem
                    if (facilityItem) {
                        item.children?.forEach(ele => {
                            if (ele.type == 'select') {
                                ele.value = facilityItem.quantity
                            } else {
                                ele.value = facilityItem.content
                            }
                        })
                    }
                })
            }
            // 已有缓存数据，上一次更改已同步到缓存里，不需要完全覆盖res
            return;
        }
        mettingHandle.value = res
        // 会议服务
        if (res.meetingfacilityList?.length) {
            mettingHandle.value._isServiceOpen = true
            serviceOptions.value.forEach(item => {
                const facilityItem = res.meetingfacilityList?.find(ele => ele.type == item.value)
                item.checked = !!facilityItem
                if (facilityItem) {
                    item.children?.forEach(ele => {
                        if (ele.type == 'select') {
                            ele.value = facilityItem.quantity
                        } else {
                            ele.value = facilityItem.content
                        }
                    })
                }
            })
        }
        // 更新会议室数据
        if (mettingHandle.value?._meetingroom?.id) {
            // 先读取已缓存的会议室
            const { id: romId, roomF, roomName } = mettingHandle.value._meetingroom
            selectedRoom.value.id = romId
            selectedRoom.value.roomF = roomF
            selectedRoom.value.roomName = roomName
        } else if (res.meetingroom?.id) {
            // 若没有，读取详细的
            const selected = generateSelectedMeetingRoom(res.meetingroom)
            selectedRoom.value = selected
            mettingHandle.value._meetingroom = {
                id: selected.id,
                roomF: selected.roomF,
                roomName: selected.roomName
            }
        }
        if (mettingHandle.value.roomSelectionType == 1) {
            // 更新智能推荐的会议室图
            recommendRoomData.value.id = selectedRoom.value?.id || ''
            hasRecommendRoom.value = !!selectedRoom.value?.id
        }
    }).finally(() => {
        hideLoading()
    })
}
// 更多人员
function toMorePersonnel() {
    personnelChanged.value = false
    uni.navigateTo({ url: './components/personnel/AddPersonnel' })
    // router.push({ name: 'meeting-handle-add-personnel' })
}
// 会议提醒
function toMeetingRemind() {
    uni.navigateTo({ url: './components/meeting-remind/index' })
    // router.push({ name: 'meeting-handle-remind' })
}
function toNotify() {
    uni.navigateTo({ url: './components/notification/index' })
    // router.push({ name: 'meeting-handle-notification' })
}
const message = useMessage()
async function onSave() {
    await setRoomIdBeforeSave()
    const { title, meetingroomId, meetingpersonnelList = [], meetingfacilityList = [], _isServiceOpen } = mettingHandle.value
    if (!title || !meetingroomId || !meetingpersonnelList.length) return message.error('请完善会议信息')
    // 会议服务
    if (!_isServiceOpen) {
        mettingHandle.value.meetingfacilityList = []
    } else {
        // 会议服务里若选了按参会人数，则统计当前页面已经选择的参会人员的数量，再传
        meetingfacilityList?.forEach(ele => {
            if (ele._selectType == 'select' && ele.quantity == 0) ele.quantity = meetingpersonnelList.length
        })
    }
    console.log('save', toRaw(mettingHandle.value))
    meetHandleService.saveMeeting(mettingHandle.value).then(res => {
        message.success('保存成功')
        console.log("🚀 ~ meetHandleService.saveMeeting ~ res:", res)
        // router.back();
    }).catch(err => {
        console.error("🚀 ~ meetHandleService.saveMeeting ~ err:", err)
        message.error(err || '保存失败')
    })
}
const meetingRoomChange = (roomId: string | undefined) => {
    console.log('roomId', roomId)
    selectedRoom.value.id = roomId
    mettingHandle.value.meetingroomId = roomId
    if (roomId) selectedRoom.value.roomName = MeetingRoomNameMap.get(roomId) || ''
    else selectedRoom.value.roomName = ''
    mettingHandle.value._meetingroom = selectedRoom.value || {}
}
const setRoomIdBeforeSave = async () => {
    if (!selectedRoom.value.id) return
    const search = {
        ifPage: false,
        currentPage: 1,
        pageRecord: 1,
        floor: selectedRoom.value.roomF
    }
    try {
        const list = await meetHandleService.getMeetRoomList(search) || []
        const item = list.find(ele => {
            return !!selectedRoom.value.id && MeetingRoomNameMapForService.get(ele.name!) == selectedRoom.value.id
        })
        if (!item) {
            mettingHandle.value.meetingroomId = ''
            message.error('会议室不存在')
            return
        } else {
            mettingHandle.value.meetingroomId = item.id
        }
    } catch (error) {
        mettingHandle.value.meetingroomId = ''
    }
}
const changeRoomF = () => {
    selectedRoom.value.roomName = ''
    selectedRoom.value.id = undefined
}
const fileDatas = computed(() => mettingHandle.value.contentList || [])
// const firmwareMainUpload = async ({
//     file,
//     data,
//     headers,
//     withCredentials,
//     action,
//     onFinish,
//     onError,
// }: UploadCustomRequestOptions) => {
//     const uploadErrorTip = (err: string) => {
//         message.error(err || '上传失败！')
//         onError()
//     }
//     const formData = new FormData()
//     if (data) {
//         Object.keys(data).forEach((key) => {
//             formData.append(
//                 key,
//                 data[key as keyof UploadCustomRequestOptions['data']]
//             )
//         })
//     }
//     formData.append('file', file.file as File, file.name)
//     // axios.post(action as string, formData, {
//     //     headers: {
//     //         ...headers,
//     //     },
//     //     withCredentials,
//     // }).then((res) => {
//     //     const data = res?.data
//     //     if (res && res.status == 200 && data && data.rlt === 0) {
//     //         if (!mettingHandle.value.contentList) mettingHandle.value.contentList = []
//     //         mettingHandle.value.contentList?.push(data.datas)
//     //         console.log("🚀 ~ fileDatas.value:", fileDatas.value)
//     //         onFinish()
//     //     } else {
//     //         uploadErrorTip(data.msg || '')
//     //     }
//     // }).catch((err) => {
//     //     message.error(err || '添加失败！')
//     //     onError()
//     // })
// }
// const beforeUpload = ({ file }: { file: UploadSettledFileInfo }) => {
//     if (file.file && file.file?.size > 5 * 1024 * 1024) {
//         // 最多三个，每个文件不大于 5m
//         message.error('文件不能超过5MB！');
//         return false
//     }
//     return true
// }
const deleteFile = (item: Content) => {
    console.log("🚀 ~ deleteFile ~ item:", item)
    const index = mettingHandle.value.contentList?.findIndex(ele => ele.id == item.id)
    if (index != undefined && index != -1) {
        mettingHandle.value.contentList?.splice(index, 1)
        if (item.id)
            meetHandleService.deleteContent(item.id)
    }
}
// const showPreview = ref(false)
// const previewId = ref('')
const previewFile = (_item: Content) => {
    // console.log("🚀 ~ previewFile ~ item:", _item)
    // showPreview.value = true
    // if (_item.id) {
    //     previewId.value = _item.id
    //     meetHandleService.previewContent(_item.id).then(res => {
    //         console.log("🚀 ~ meetHandleService.previewContent ~ res:", res

    //         )
    //     })
    // }
}
/** 获取智能推荐会议室，或已选了会议时间、参会人数且是选择智能推荐 */
const recommendRoomData = ref<Meetingroom>({})
/** 是否有推荐的会议室 */
const hasRecommendRoom = ref(false)
const getRecommendRoom = async () => {
    const { roomSelectionType, startTime = '', endTime = '', meetingpersonnelList = [] } = mettingHandle.value
    if (roomSelectionType != 1 || !startTime || !endTime) return;
    if (timeChanged.value || personnelChanged.value) {
        hasRecommendRoom.value = false
        try {
            const res = await meetHandleService.recommendMeetingRoom({
                startTime,
                endTime,
                attendeeCount: meetingpersonnelList?.length || 0
            })
            recommendRoomData.value = res || {}
            hasRecommendRoom.value = res != null
            // 更新已选中的会议室
            if (res?.id) {
                const selected = generateSelectedMeetingRoom(res)
                selectedRoom.value = selected
                mettingHandle.value._meetingroom = {
                    id: selectedRoom.value.id,
                    roomF: selectedRoom.value.roomF,
                    roomName: selectedRoom.value.roomName
                }
            }
        } catch (error) {

        }
    }
}
</script>

<style lang="css" scoped>
.meeting-handle-container {
    width: 100%;
    position: relative;
}

.handle-item {
    /* height: 2.5625rem; */
    height: 41px;
    width: 100%;
    display: flex;
    align-items: center;
    background-color: #fff;
    padding-left: 15px;
    padding-right: 10px;
    font-size: 14px;
    box-sizing: border-box;
    /* padding: 0 15px; */
}

.handle-item-out {
    display: flex;
    flex-direction: column;
    margin-bottom: 4px;
}

.file-item {
    min-height: 41px;
    width: 100%;
    display: flex;
    background-color: #fff;
    align-items: center;
    padding-left: 15px;
    padding-right: 10px;
}

.content {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    overflow: hidden;
    color: #666;
}

.content-file-box {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 80%;
}

.content-file-item {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 4px;
}

.file-name {
    font-size: 14px;
    color: #333;
    text-overflow: ellipsis;
    overflow: hidden;
}

.file-size {
    font-size: 12px;
    color: #666;
}

.require {
    position: relative;
}

.require::before {
    content: '*';
    position: absolute;
    color: #e50101;
    top: 0;
    /* left: -0.5rem; */
    left: -8px;
}

.placeholder {
    color: #666;
    display: inline-flex;
    align-items: center;
    flex-wrap: nowrap;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: right;
    max-width: 80%;
    gap: 4px;
    font-size: 14px;
}

.placeholder_content {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.item__border-bottom {
    border-bottom: 1px solid rgba(153, 153, 153, 0.2);
}

.item__margin-top {
    /* margin-top: 0.25rem; */
    margin-top: 4px;
}

/* 参会人员 */
.participant-box {
    display: flex;
    align-items: center;
    /* justify-content: space-between; */
    flex-wrap: wrap;
    gap: 10px 0;
    margin: 0 0 8px;
    background-color: #fff;
    width: 100%;
    padding: 0 10px 10px;
}

.participant-item {
    /* min-width: 34px; */
    width: 20%;
    min-height: 54px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.participant-name {
    color: #333;
    font-size: 10px;
    margin-top: 4px;
}

.add-participant {
    display: flex;
    /* width: 25%; */
    /* width: 35px;
    height: 54px; */
    /* width: 2.1875rem; */
    /* height: 3.375rem; */
    flex-direction: column;
    align-items: center;
    /* gap: 0.25rem; */
    /* gap: 4px; */
}

.add-icon {
    width: 34px;
    height: 34px;
    line-height: 30px;
    text-align: center;
    font-size: 30px;
    color: #cbcdd0;
    border-radius: 20px;
    border: 1px dashed #cbcdd0;
}

.add-text {
    color: #999;
    font-size: 10px;
}

/* radio */
/* 激活 radio-active */
.radio-box {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px;
    font-size: 12px;
}

.radio-box .active {
    display: none;
}

.radio-box.radio-active .active {
    display: inline-block;
}

.radio-box.radio-active .not-active {
    display: none;
}

.switch-box .active {
    display: none;
}

.switch-box.switch-active .active {
    display: inline-block;
}

.switch-box.switch-active .not-active {
    display: none;
}

.meeting-service-box-out {
    background-color: #fff;
    padding: 0 15px 12px;
}

.meeting-service-box {
    border-radius: 3px;
    padding: 11px;
    border: 1px solid rgba(153, 153, 153, 0.2);
    font-size: 12px;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
}

.service-title {
    display: flex;
    align-items: center;
    gap: 4px;
}

.service-title:not(:last-child) {
    margin-bottom: 4px;
}

.service-content-item {
    display: flex;
    align-items: center;
    min-height: 26px;
    padding-left: 15px;
}

.service-content-item:not(:last-child) {
    margin-bottom: 4px;
}

.service-content-item .label {
    display: inline-block;
    width: 36px;
}

.service-content-item .content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.service-content-item .content select,
.service-content-item .content input {
    width: 120px;
    height: 30px;
}

/* 会议室 */
.meeting-room-out {
    padding: 6px 15px;
    background-color: #fff;
    margin-bottom: 4px;
}

.meeting-room-box {
    /* width: 290px; */
    height: 180px;
    border-radius: 3px;
    border: 1px solid rgba(153, 153, 153, 0.2);
    padding: 6px 10px;
    position: relative;
}

.meeting-room-head {
    height: 26px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    justify-content: space-between;
}

.room-container {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
}

.meeting-room-content {
    width: 190px;
    height: 120px;
}

.meeting-room_placeholder {
    color: #999;
    font-size: 12px;
}

.room-title {
    color: #666;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.room-select {
    width: 61px;
    height: 26px;
    font-size: 12px;
}

.meeting-room-rect {
    position: absolute;
    bottom: 6px;
    right: 10px;
    font-size: 8px;
    color: #666;
}

.rect {
    display: inline-block;
    border-radius: 1px;
    background: #ffffff;
    border: 1px solid rgba(153, 153, 153, 0.2);
    background-color: #fff;
    width: 8px;
    height: 8px;
}

.footer-btn {
    margin: 10px 0;
}

.save-btn {
    width: 100%;
    height: 40px;
    background: #0066df;
    border-radius: 3px;
}


.circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    /* text-align: center; */
    /* line-height: 24px; */
    margin-right: 5px;
    background-color: #999;
    color: #fff;
    font-size: 10px;
}

.circle-large {
    width: 34px;
    height: 34px;
    font-size: 14px;
    margin-right: 0;
}

.circle.circle-blue {
    background-color: #4f7af6;
}

.circle-out-icon {
    position: relative;
}

.circle-out-icon::after {
    position: absolute;
    content: '外';
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    line-height: 16px;
    border: 1px solid #3e75fe;
    border-radius: 50%;
    color: #3e75fe;
    text-align: center;
}

label {
    font-size: 14px;
}

/* 遮罩层样式 */
.preview-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    /* 半透明黑色遮罩 */
    display: flex;
    /* 使用 flex 布局实现垂直水平居中 */
    align-items: center;
    justify-content: center;
    z-index: 9999;
    /* 确保层级最高 */
}

/* 预览盒子样式 */
.preview-box {
    position: relative;
    width: 80%;
    /* 宽度占屏幕 80% */
    max-width: 1200px;
    /* 最大宽度限制 */
    height: 80%;
    /* 高度占屏幕 80% */
    max-height: 800px;
    /* 最大高度限制 */
    background: #fff;
    /* 白色背景 */
    border-radius: 12px;
    /* 圆角 */
    overflow: hidden;
    /* 溢出隐藏 */
}

/* 关闭按钮样式 */
.close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 24px;
    color: #333;
    cursor: pointer;
    padding: 4px 12px;
    border-radius: 4px;
    background: #fff;
    /* 白色背景 */
    transition: color 0.3s;
    /* 点击动画 */
}

.close-btn:hover {
    color: #ff4444;
    /* 悬停变红 */
}

/* iframe 样式 */
.preview-box iframe {
    width: 100%;
    height: 100%;
}

.preview-iframe {
    width: 100%;
    height: 100%;
}
</style>