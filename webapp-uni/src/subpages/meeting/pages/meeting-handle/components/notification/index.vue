<template>
    <div class="notification inner-container">
        <div class="remind-c">
            <span class="inner-title">通知方式</span>
            <div class="content" @click="data.notifyBySms = data.notifyBySms == 1 ? 0 : 1">
                <span class="icon">
                    <SlSVgIcon name="components-selected" size="16" v-if="data.notifyBySms == 1" />
                    <SlSVgIcon name="components-unselected" size="16" v-else />
                    <!-- <svg class="icon-16" v-if="data.notifyBySms == 1">
                        <use xlink:href="#icon-components-selected"></use>
                    </svg>
                    <svg class="icon-16" v-else>
                        <use xlink:href="#icon-components-unselected"></use>
                    </svg> -->
                </span>
                <span>短信通知</span>
            </div>
            <div class="content" @click="data.notifyByVoiceCall = data.notifyByVoiceCall == 1 ? 0 : 1">
                <span class="icon">
                    <SlSVgIcon name="components-selected" size="16" v-if="data.notifyByVoiceCall == 1" />
                    <SlSVgIcon name="components-unselected" size="16" v-else />
                    <!-- <svg class="icon-16" v-if="data.notifyByVoiceCall == 1">
                        <use xlink:href="#icon-components-selected"></use>
                    </svg>
                    <svg class="icon-16" v-else>
                        <use xlink:href="#icon-components-unselected"></use>
                    </svg> -->
                </span>
                <span>机器人语音电话通知</span>
            </div>
        </div>
        <div class="base-btn-footer">
            <up-button type="primary" text="通知" @click="save"></up-button>
            <up-button text="取消" @click="cancel"></up-button>
            <!-- <n-button class="medium" type="primary" @click="save">
                通知
            </n-button>
            <n-button class="medium" @click="cancel">
                取消
            </n-button> -->
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Meeting } from '@/models/MettingHandle';
import useMettingHandleStore from '@/store/modules/metting-handle';
import { onMounted, ref } from 'vue';
const store = useMettingHandleStore()
const data = ref<Meeting>({ notifyBySms: 0, notifyByVoiceCall: 0 })
onMounted(() => {
    const { notifyBySms, notifyByVoiceCall } = store.mettingHandle
    data.value.notifyBySms = notifyBySms || 0
    data.value.notifyByVoiceCall = notifyByVoiceCall || 0
})
function save() {
    const { notifyBySms, notifyByVoiceCall } = data.value
    store.mettingHandle.notifyBySms = notifyBySms
    store.mettingHandle.notifyByVoiceCall = notifyByVoiceCall
    uni.navigateBack()
    // router.back()
}
function cancel() {
    uni.navigateBack()
    // router.back()
}
</script>

<style scoped>
@import "../../styles/metting-base.css";

.remind-c {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.inner-title {
    color: #333;
    font-size: 14px;
    font-weight: bold;
}

.content {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    font-size: 14px;
}

.icon {
    display: inline-flex;
    align-items: center;
    margin-right: 12px;
}

/* 底部通知按钮 */
.base-btn-footer {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 26px;
    gap: 20px;
}

.base-btn-footer div {
    height: 41px;
    /* 12.8125vw → 41px */
    width: 140px;
    /* 43.75vw → 140px */
    line-height: 41px;
    /* 12.8125vw → 41px */
    text-align: center;
    font-size: 14px;
    /* 4.375vw → 14px */
}

.base-btn-footer div:nth-child(1) {
    border-radius: 3px;
    /* 0.9375vw → 3px */
    background: #0066DF;
    color: #ffffff;
}

.base-btn-footer div:nth-child(2) {
    color: #0066DF;
    opacity: 1;
    border-radius: 3px;
    /* 0.9375vw → 3px */
    background: #ffffff;
    border: 1px solid #0066DF;
    /* 0.3125vw → 1px */
}

.n-button {
    width: 140px;
    height: 41px;
}
</style>