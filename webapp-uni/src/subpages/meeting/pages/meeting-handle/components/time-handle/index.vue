<template>
  <div class="inner-container">
    <div class="input-c">
      <span class="require">预约时间</span>
      <div class="start">
        <span>开始时间</span>
        <input type="text" placeholder="请选择开始时间" readonly :value="startTime" @click="openCalendar('start')" />
        <svg class="icon-16 ti-icon">
          <use :xlink:href="`#icon-meeting-12-10`" />
        </svg>
      </div>
      <div class="end">
        <span>结束时间</span>
        <input type="text" placeholder="请选择结束时间" readonly :value="endTime" @click="openCalendar('end')" />
        <svg class="icon-16 ti-icon">
          <use :xlink:href="`#icon-meeting-12-10`" />
        </svg>
      </div>
    </div>
    <div class="base-btn-footer">
      <n-button class="medium" type="primary" @click="handleConfirm">
        完成
      </n-button>
      <n-button class="medium" @click="handleCancel"> 取消 </n-button>
    </div>
    <!-- <div class="base-btn-footer">
      <div @click="handleConfirm">完成</div>
      <div @click="handleCancel">取消</div>
    </div> -->

    <!-- 日历弹窗 -->
    <div v-if="showPopup" class="popup-overlay">
      <div class="popup">
        <div class="calendar-header">
          <button class="btn-l" @click="prevYear">«</button>
          <!-- <select v-model="currentYear">
            <option v-for="y in years" :key="y" :value="y">{{ y }}</option>
          </select>
          <select v-model="currentMonth">
            <option v-for="m in months" :key="m.value" :value="m.value">
              {{ m.label }}
            </option>
          </select> -->
          <button class="btn-r" @click="nextYear">»</button>
        </div>

        <div class="calendar-week">
          <div v-for="d in weekDays" :key="d">{{ d }}</div>
        </div>

        <div class="calendar-grid">
          <div v-for="(_, i) in blankDays" :key="'b' + i"></div>
          <!-- <div v-for="day in daysInMonth" :key="day" :class="{ selected: isSelected(day) }" @click="selectDay(day)">
            {{ day }}
          </div> -->
          <div v-for="day in daysInMonth" :key="day" :class="[
            { selected: isSelected(day) },
            { disabled: isPastDate(day) },
          ]" @click="!isPastDate(day) && selectDay(day)">
            {{ day }}
          </div>
        </div>

        <div class="time-quantum">
          <span>{{ selectedTime }}</span>
          <!-- TODO：type不支持time，切换 -->
          <!-- <input type="time" v-model="selectedTime" step="60" /> -->
        </div>
        <div class="base-btn-footer">
          <n-button @click="closePopup">
            取消
          </n-button>
          <n-button type="primary" @click="confirmDate">
            确定
          </n-button>
        </div>
        <!-- <div class="time-btn-footer">
          <div @click="closePopup">取消</div>
          <div @click="confirmDate">确定</div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import useMettingHandleStore from "@/store/modules/metting-handle";
import { NButton } from "naive-ui";
import { storeToRefs } from "pinia";
import { computed, onMounted, ref } from "vue";
import { useRouter } from "vue-router";

const startTime = ref("");
const endTime = ref("");
const showPopup = ref(false);
const activeField = ref("start");
const today = new Date();
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth());
const selectedDay = ref(new Date().getDate());
const selectedTime = ref("09:00");
const { mettingHandle, timeChanged } = storeToRefs(useMettingHandleStore());
const weekDays = ["日", "一", "二", "三", "四", "五", "六"];
// const months = Array.from({ length: 12 }, (_, i) => `${i + 1}月`);
// const years = Array.from({ length: 2100 - 1970 + 1 }, (_, i) => 1970 + i);
const years = computed(() => {
  const nowYear = today.getFullYear();
  return Array.from({ length: 2100 - nowYear + 1 }, (_, i) => nowYear + i);
});
const months = computed(() => {
  const startMonth =
    currentYear.value === today.getFullYear() ? today.getMonth() : 0;
  return Array.from({ length: 12 - startMonth }, (_, i) => ({
    label: `${startMonth + i + 1}月`,
    value: startMonth + i,
  }));
});
onMounted(() => {
  startTime.value = mettingHandle.value.startTime || "";
  endTime.value = mettingHandle.value.endTime || "";
  timeChanged.value = false;
});
const blankDays = computed(() => {
  const firstDay = new Date(currentYear.value, currentMonth.value, 1).getDay();
  return Array.from({ length: firstDay });
});

const daysInMonth = computed(() => {
  return new Date(currentYear.value, currentMonth.value + 1, 0).getDate();
});

const selectedDate = computed(() => {
  return new Date(currentYear.value, currentMonth.value, selectedDay.value);
});

function openCalendar(field: string) {
  activeField.value = field;
  const targetValue = field === "start" ? startTime.value : endTime.value;

  if (targetValue) {
    const [datePart, timePart] = targetValue.split(" ");
    const [y, m, d] = datePart.split("-").map(Number);
    currentYear.value = y;
    currentMonth.value = m - 1;
    selectedDay.value = d;
    selectedTime.value = timePart || "09:00";
  } else {
    const now = new Date();
    currentYear.value = now.getFullYear();
    currentMonth.value = now.getMonth();
    selectedDay.value = now.getDate();
    selectedTime.value = `${String(now.getHours()).padStart(2, "0")}:${String(
      now.getMinutes()
    ).padStart(2, "0")}`;
  }

  showPopup.value = true;
}

function isSelected(day: number) {
  return (
    selectedDay.value === day &&
    selectedDate.value.getMonth() === currentMonth.value &&
    selectedDate.value.getFullYear() === currentYear.value
  );
}

function selectDay(day: number) {
  selectedDay.value = day;
}

function closePopup() {
  showPopup.value = false;
}

function confirmDate() {
  const formatted = `${currentYear.value}-${String(
    currentMonth.value + 1
  ).padStart(2, "0")}-${String(selectedDay.value).padStart(2, "0")} ${selectedTime.value
    }`;

  const targetRef = activeField.value === "start" ? startTime : endTime;
  const otherRef = activeField.value === "start" ? endTime : startTime;

  const thisDate = new Date(formatted.replace(" ", "T"));
  const otherDate = new Date(otherRef.value.replace(" ", "T"));

  if (
    startTime.value &&
    endTime.value &&
    startTime.value !== "" &&
    endTime.value !== ""
  ) {
    if (activeField.value === "start" && thisDate > otherDate) {
      alert("开始时间不能晚于结束时间");
      return;
    }
    if (activeField.value === "end" && thisDate < otherDate) {
      alert("结束时间不能早于开始时间");
      return;
    }
  }

  targetRef.value = formatted;
  closePopup();
}

function prevYear() {
  if (currentYear.value > today.getFullYear()) {
    currentYear.value--;
  }
}
function nextYear() {
  currentYear.value++;
}
const router = useRouter();
function handleCancel() {
  // 跳转或清空
  router.back();
  // window.location.hash = '/meeting-handle.html';
}

function handleConfirm() {
  mettingHandle.value.startTime = startTime.value || "";
  mettingHandle.value.endTime = endTime.value || "";
  timeChanged.value = true;
  router.back();
  console.log("开始时间:", startTime.value);
  console.log("结束时间:", endTime.value);
}
function isPastDate(day: number) {
  const today = new Date();
  const date = new Date(currentYear.value, currentMonth.value, day);
  today.setHours(0, 0, 0, 0); // 去掉时分秒精度，避免比较误差
  return date < today;
}
</script>

<style scoped>
.input-c {
  padding: 0 17px;
  flex: 1;
}

.input-c .start,
.input-c .end {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  position: relative;
}

.input-c .start span,
.input-c .end span {
  font-size: 14px;
}

.input-c .start input,
.input-c .end input {
  height: 36px;
  width: 76%;
  border: 1px solid #99999933;
  border-radius: 5px;
  padding: 8px;
  outline: none;
}

.input-c .start i,
.input-c .end i {
  position: absolute;
  right: 10px;
}

.require {
  position: relative;
  font-size: 14px;
  font-weight: 550;
}

.require::before {
  content: "*";
  position: absolute;
  color: #e50101;
  top: 0;
  left: -0.5rem;
}

.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  /* 半透明遮罩 */
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 999;
}

.popup {
  background: white;
  padding: 20px;
  border-radius: 10px 10px 0px 0px;
  width: 100%;
  position: relative;
  position: absolute;
  bottom: 0px;
  left: 0px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  text-align: center;
}

.calendar-grid div {
  padding: 8px;
  background: #f0f0f0;
  border-radius: 16.8px;
  cursor: pointer;
  font-size: 12.8px;
}

.calendar-grid div:hover {
  background: #9effd7;
}

.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 9.6px;
}

button {
  cursor: pointer;
}

.close {
  border-radius: 26.4px;
  position: absolute;
  right: 20px;
  bottom: 4.8px;
  font-size: 12.8px;
  color: #4589ff;
}

.btn-r,
.btn-l {
  background: none;
  border-radius: 13.6px;
  font-size: 23px !important;
  border: 0px;
  width: 20px !important;
}

select {
  width: 70.4px;
  height: 32px;
  border-radius: 12.8px;
  font-size: 16px;
  outline: none;
  text-align: center;
}

.time-quantum {
  height: 41px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-size: 14px;
    margin-bottom: 10px;
    margin-top: 10px;
    display: block;
  }
}

.time-quantum input {
  float: right;
  height: 80%;
  width: 80px;
  font-size: 14px;
  margin-bottom: 10px;
  margin-top: 10px;
}

.time-btn-footer {
  font-size: 14px;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

/* .time-btn-footer div {
  height: 41px;
  width: 133.6px;
  text-align: center;
  line-height: 41px;
}
.time-btn-footer div:nth-child(1) {
  color: #ffffff;
  opacity: 1;
  border-radius: 6.2px;
  background: #b3b3b3;
  border: 1px solid #b3b3b3;
}
.time-btn-footer div:nth-child(2) {
  border-radius: 6.2px;
  background: #0066DF;
  color: #ffffff;
} */

.calendar-grid div.selected {
  background-color: #1890ff;
  color: white;
  border-radius: 50%;
}

/* .start,.end  */
.ti-icon {
  position: absolute;
  right: 9px;
}

input {
  font-size: 14px;
}

.disabled {
  color: #ccc;
  pointer-events: none;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.n-button {
  width: 140px;
  height: 41px;
}

/* 底部通知按钮 */
.base-btn-footer {
  margin-bottom: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  width: 100%;
  gap: 20px;
}
@import "../../styles/metting-base.css";
</style>
