<template>
  <!-- <div class="base-top">
  <i id="goBack"></i>
  <span>新增人员</span>
  <span></span>
</div> -->
  <div class="radio-container">
    <div class="radio-box" @click="onSelect(0)">
      <SlSVgIcon :name="'meeting-12-' + (user.type && user.type == 1 ? '2' : '3')" size="12" />
      <!--  <svg class="icon-12">
        <use xlink:href="#icon-meeting-12-3" v-if="user.type == 0"></use>
        <use xlink:href="#icon-meeting-12-2" v-else></use>
      </svg> -->
      内部成员
    </div>
    <div class="radio-box" @click="onSelect(1)">
      <SlSVgIcon :name="'meeting-12-' + (user.type && user.type == 1 ? '3' : '2')" size="12" />
      <!--  <svg class="icon-12">
        <use xlink:href="#icon-meeting-12-3" v-if="user.type == 1"></use>
        <use xlink:href="#icon-meeting-12-2" v-else></use>
      </svg> -->
      外部成员
    </div>
  </div>
  <div>
    <!-- <up-calendar :show="show"></up-calendar> -->
    <!-- <up-select label="分类" :options="cateList" @select="selectItem"></up-select> -->
  </div>
  <div class="externalPerson-add" v-if="user.type == 0">
    <div class="input-group">
       <span class="label">部门</span>
      <!-- <up-select v-model:current="user.organizationId" :options="departmentList" @select="selectItem"></up-select> -->
      <picker :value="user.organizationId" :range="departmentList2" @change="selectItem">
        <!-- <option :value="opt.id" v-for="(opt, idx) of departmentList" :key="idx">
          {{ opt.department }}</option> -->
      </picker>
    </div>
    <div class="input-group">
       <span class="label">姓名</span>
      <input type="text" v-model="user.name" placeholder="请输入姓名" />
    </div>
    <div class="input-group">
       <span class="label">职务</span>
      <input type="text" v-model="user.position" placeholder="请输入职务" />
    </div>
    <div class="input-group">
       <span class="label">手机号码</span>
      <input type="text" v-model="user.phone" placeholder="请输入手机号码" />
    </div>

  </div>
  <div class="externalPerson-add" v-if="user.type == 1">
    <div class="input-group">
       <span class="label">姓名</span>
      <input type="text" v-model="user.name" placeholder="请输入姓名" />
    </div>
    <div class="input-group">
       <span class="label">单位</span>
      <input type="text" v-model="user.department" placeholder="请输入单位" />
    </div>
    <div class="input-group">
       <span class="label">职务</span>
      <input type="text" v-model="user.position" placeholder="请输入职务" />
    </div>
    <div class="input-group">
       <span class="label">手机号码</span>
      <input type="text" v-model="user.phone" placeholder="请输入手机号码" />
    </div>
  </div>
  <div class="base-btn-footer">
    <up-button type="primary" text="完成" @click="save"></up-button>
    <up-button text="取消" @click="cancel"></up-button>
    <!-- <n-button type="primary" @click="save"> 完成 </n-button>
    <n-button @click="cancel"> 取消 </n-button> -->
  </div>
</template>

<script setup lang="ts">
import useMessage from "@/hooks/use-message";
import type { Personnel } from "@/models/MettingHandle";
import meetHandleService from "@/service/meet-assistant/meet-handle/meet-handle.service";
import { onMounted, ref } from "vue";
const cateId = ref('')
const cateList = ref([
  {
    id: '1',
    name: '分类1'
  },
  {
    id: '2',
    name: '分类2'
  },
  {
    id: '3',
    name: '分类4'
  },
])
const departmentList = ref<Personnel[]>([])
const departmentList2 = ref<string[]>(['分类1', '分类2', '分类3'])
const user = ref<Personnel>({
  name: "",
  phone: "",
  department: "",
  position: "",
  type: null, // 该页面的新增人员为外部人员
});
const onSelect = (type: 0 | 1) => {
  user.value = {
    name: "",
    phone: "",
    department: "",
    position: "",
    type
  }
}
onMounted(() => {
  // getDepartmentList()
})
const message = useMessage();
function save() {
  const { name, phone, type, organizationId } = user.value
  if (!name || !phone || type == null) {
    message.error("请填写完整信息");
    return;
  }
  const phoneReg = /^1[3-9]\d{9}$/;
  if (phone && !phoneReg.test(phone)) {
    message.error("请输入正确的手机号");
    return;
  }
  if (type == 0 && !organizationId) {
    message.error("请选择部门");
    return;
  }
  meetHandleService.savePersonnel(user.value).then((_) => {
    message.success("保存成功");
    uni.navigateBack();
    // router.back();
  }).catch(err => {
    message.error(err || "保存失败");
  })
}
const getDepartmentList = () => {
  // type:0 只加载内部成员列表
  meetHandleService.getListOrganization({ ifPage: false }).then((res: any) => {
    departmentList2.value = []
    res?.forEach((ele: any) => {
      departmentList2.value.push(ele.name)
    })
    departmentList.value = res?.map((ele: any) => {
      return {
        department: ele.name,
        id: ele.id,
        name: ele.name
      }
    })
  })
}
function cancel() {
  uni.navigateTo({ url: './AddPersonnel' })
  // router.push({ name: 'meeting-handle-add-personnel' })
}
const selectItem = (item: any) => {
  console.log(item);
};  
</script>
<style lang="css" scoped>
.externalPerson-add {
  width: 100%;
  height: 100%;
  /* 51.875vw → 166px */
  background: #ffffff;
  padding: 0 15px;
  /* 4.6875vw → 15px */
  display: flex;
  flex-direction: column;
}

.externalPerson-add div {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.externalPerson-add div span {
  color: #333333;
  font-size: 14px;
  /* 4.375vw → 14px */
}

.externalPerson-add div input {
  outline: none;
  height: 40px;
  /* 12.5vw → 40px */
  width: 185px;
  /* 57.8125vw → 185px */
  border: 0;
  border-bottom: 1px solid #99999933;
  /* 0.3125vw → 1px */
  color: black;
  font-size: 14px;
  /* 4.375vw → 14px */
}

/* 底部通知按钮 */
.base-btn-footer {
  display: flex;
  justify-content: center !important;
  width: 100%;
  gap: 20px;
  position: absolute;
  bottom: 26px;
  left: 0px;
}

.base-btn-footer div {
  height: 41px;
  /* 12.8125vw → 41px */
  width: 140px;
  /* 43.75vw → 140px */
  line-height: 41px;
  /* 12.8125vw → 41px */
  text-align: center;
  font-size: 14px;
  /* 4.375vw → 14px */
}

.base-btn-footer div:nth-child(1) {
  border-radius: 3px;
  /* 0.9375vw → 3px */
  background: #0066DF;
  color: #ffffff;
}

.base-btn-footer div:nth-child(2) {
  color: #0066DF;
  opacity: 1;
  border-radius: 3px;
  /* 0.9375vw → 3px */
  background: #ffffff;
  border: 1px solid #0066DF;
  /* 0.3125vw → 1px */
}

button {
  width: 140px;
  height: 41px;
}

.radio-container {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px;
  font-size: 14px;
  background-color: #fff;
}

.input-group .label {
  flex: 1;
}

select {
  height: 30px;
  width: 150px;
}
</style>
