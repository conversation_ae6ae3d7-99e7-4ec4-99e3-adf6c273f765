<template>
    <div class="p-main-vessel" id="p-main-vessel">
        <div class="p-top">
            <SlSVgIcon name="home-前翻-2" size="16" @click="cancel" />
            <!-- <svg class="icon-16" @click="$router.back()">
                <use xlink:href="#icon-home-前翻-2"></use>
            </svg> -->
            <span>添加人员</span>
            <span class="add-more" @click="toAddMoreModal">
                <SlSVgIcon name="meeting-16-6" size="16" />
                <!-- <svg class="icon-16">
                    <use xlink:href="#icon-meeting-16-6"></use>
                </svg> -->
                <div class="more-modal-container" v-if="showMoreModal">
                    <div class="modal-item top" @click="toAddGroup">
                        <SlSVgIcon name="meeting-16-group" size="16" />
                        <!-- <svg class="icon-16">
                            <use xlink:href="#icon-meeting-16-group"></use>
                        </svg> -->
                        <span>
                            新增工作组
                        </span>
                    </div>
                    <div class="modal-item bottom" @click="toAddPersonnel">
                        <SlSVgIcon name="meeting-16-add" size="16" />
                        <!-- <svg class="icon-16">
                            <use xlink:href="#icon-meeting-16-add"></use>
                        </svg> -->
                        <span>
                            新增人员
                        </span>
                    </div>
                </div>
            </span>
        </div>
        <div class="search">
            <input type="text" class="search-input" v-model="search.keyword" placeholder="请输入姓名/首字母" />
            <SlSVgIcon name="meeting-16-1-search" class="search-icon" size="16" @click="getList" />
            <!-- <svg class="icon-16 search-icon">
                <use xlink:href="#icon-meeting-16-1-search"></use>
            </svg> -->
        </div>
        <div class="p-content">
            <div v-for="item in personnelDatas" class="select-p" :key="item._uuid">
                <div class="row-0">
                    <SlSVgIcon :name="'meeting-20-' + (item._isChecked ? '14' : '2')" size="20"
                        @click.stop="onCheckedDepartment(item)" />
                    <!-- <svg class="icon-20" @click.stop="onCheckedDepartment(item)">
                        <use xlink:href="#icon-meeting-20-14" v-if="item._isChecked"></use>
                        <use xlink:href="#icon-meeting-20-2" v-else></use>
                    </svg> -->
                    <span class="dept-icon" v-if="item._type == 2">外</span>
                    {{ item.name }}  
                    <SlSVgIcon :name="'meeting-12-1-' + (item._open ? '9' : '1')" size="12" />
                    <!-- <svg class="icon-12" @click="item._open = !item._open">
                        <use xlink:href="#icon-meeting-12-9" v-if="item._open"></use>
                        <use xlink:href="#icon-meeting-12-1" v-else></use>
                    </svg> -->
                </div>
                <!-- <up-list>
                    <up-list-item v-for="(vItem, index) in item.children" :key="index"> -->
                <div class="row-t" v-for="(vItem, index) in item.children" :key="index">
                    <SlSVgIcon :name="'meeting-20-' + vItem._isChecked ? '14' : '2'" size="20"
                        @click.stop="onChangeExternal(vItem, item)" />
                    <!-- <svg class="icon-20" @click.stop="onChangeExternal(vItem, item)">
                                <use xlink:href="#icon-meeting-20-14" v-if="vItem._isChecked"></use>
                                <use xlink:href="#icon-meeting-20-2" v-else></use>
                            </svg> -->
                    <div class="round" :style="{ background: item._type == 2 ? '#999999' : '#4f7af6' }">
                        {{ vItem.name![0] }}</div>
                    <div class="row-name">{{ vItem.name }}</div>
                </div>
                <!-- </up-list-item>
                </up-list> -->
            </div>
        </div>
        <div class="base-btn-footer">
            <up-button type="primary" text="完成" @click="save"></up-button>
            <up-button text="取消" @click="cancel"></up-button>
            <!-- <n-button type="primary" @click="save"> 完成 </n-button>
            <n-button @click="cancel"> 取消 </n-button> -->
        </div>
    </div>

</template>
<script setup lang="ts">
import { useLoading } from '@/hooks';
import type { Meetingpersonnel, Personnel } from '@/models/MettingHandle';
import { PersonnelSearch, type PersonnelAndOrganization2 } from '@/models/Personnel';
import meetHandleService from '@/service/meet-assistant/meet-handle/meet-handle.service';
import useMettingHandleStore from '@/store/modules/metting-handle';
import { storeToRefs } from 'pinia';
import { onMounted, ref } from 'vue';
import { generatePersonnelList, type PersonnelAndOrganizationGroup } from '../../meeting-handle-data';
const { mettingHandle, personnelChanged } = storeToRefs(useMettingHandleStore())
const personnelDatas = ref<PersonnelAndOrganizationGroup[]>([])
const showMoreModal = ref(false)
function toAddMoreModal() {
    showMoreModal.value = !showMoreModal.value
}
onMounted(() => {
    // checkedAll.value = mettingHandle.value._isAllCheckedPerson || false
    getList()
    personnelChanged.value = false
})
function onChangeExternal(item: Personnel, _parentItem: PersonnelAndOrganizationGroup) {
    item._isChecked = !item._isChecked
    // parentItem._isChecked = parentItem.children?.every(ele => ele._isChecked)
    // 其他组里，同一个用户的勾选状态，也要改变
    personnelDatas.value.forEach(ele => {
        ele.children?.forEach(e => {
            if (e.id == item.id) {
                e._isChecked = item._isChecked
            }
        })
        ele._isChecked = ele.children?.every(m => m._isChecked)
    })
    // let checkedLen = 0
    // personnelDatas.value.forEach(ele => {
    //     ele._isChecked = ele.members?.every(m => m._isChecked)
    //     ele.members?.forEach(e => {
    //         if (e._isChecked) checkedLen++
    //     })
    // })
    // checkedAll.value = len.value == checkedLen
    // const idx = mettingHandle.value.meetingpersonnelList?.findIndex(ele => ele?.personnel?.id === item.id)
    // if (!mettingHandle.value.meetingpersonnelList?.length) mettingHandle.value.meetingpersonnelList = []
    // if (item._isChecked) {
    //     if (idx == undefined || idx == -1) {
    //         mettingHandle.value.meetingpersonnelList.push({ personnel: item, personnelId: item.id })
    //     }
    //     let len = 0;
    //     personnelDatas.value.forEach(ele => {
    //         len += ele.members.length
    //     })
    //     if (len > 0 && len == mettingHandle.value.meetingpersonnelList.length) {
    //         checkedAll.value = true
    //     }
    // } else {
    //     if (idx != undefined && idx > -1) {
    //         mettingHandle.value.meetingpersonnelList.splice(idx, 1)
    //     }
    //     checkedAll.value = false
    // }
    // mettingHandle.value._isAllCheckedPerson = checkedAll.value
    // personnelChanged.value = true
}
const store = useMettingHandleStore()
const search = ref(new PersonnelSearch())
const { showLoading, hideLoading } = useLoading()
// let loadingMsg: MessageReactive | null = null
function getList() {
    // if (loadingMsg != null) {
    //     loadingMsg?.destroy()
    //     loadingMsg = null
    // }
    // loadingMsg = message.loading('数据加载中...')
    hideLoading()
    showLoading()
    search.value.groupByOrganization = true
    search.value.excludeCurrentUser = true
    search.value.groupByWorkgroup = true
    search.value.ifGrouped = true
    search.value.ifPage = false
    meetHandleService.getListPersonnel(search.value).then((res) => {
        // 按照分组res类型不一样
        const datas = res as PersonnelAndOrganization2
        const dataList = generatePersonnelList(datas)
        console.log("🚀 ~ meetHandleService.getListPersonnel ~ dataList:", dataList)
        personnelDatas.value = dataList || []
        personnelDatas.value?.forEach(ele => {
            ele._open = true;
            ele.children?.forEach(e => {
                if (store.getPersonnelIds?.includes(e.id)) e._isChecked = true
            })
            ele._isChecked = ele.children?.every(m => m._isChecked)
        })
        personnelDatas.value.sort((a, b) => {
            if (a.name === "青五月创新创意大赛组") return -1; // a 排前面
            if (b.name === "青五月创新创意大赛组") return 1;  // b 排前面
            return 0; // 其他情况保持原顺序
        });
    }).finally(() => {
        hideLoading()
        // loadingMsg?.destroy()
        // loadingMsg = null
    })
}
const save = () => {
    let checkedData: Meetingpersonnel[] = []
    personnelDatas.value.forEach(ele => {
        ele.children?.forEach(item => {
            if (item._isChecked) {
                const aleradyItem = checkedData.find(e => e.personnelId === item.id)
                if (!aleradyItem) {
                    // 避免重复添加同一个人员。
                    checkedData.push({ personnel: item._init, personnelId: item.id })
                }
            }
        })
        mettingHandle.value.meetingpersonnelList = checkedData
    })
    personnelChanged.value = true
    uni.navigateBack()
    // router.back()
}
const cancel = () => {
    uni.navigateBack()
    // router.back()
}
/** 勾选上级单位 */
const onCheckedDepartment = (item: PersonnelAndOrganizationGroup) => {
    item._isChecked = !item._isChecked
    item.children?.forEach(ele => ele._isChecked = item._isChecked)
    personnelDatas.value.filter(ele => ele._uuid != item._uuid).forEach(ele => {
        ele.children?.forEach(e => {
            // 同步勾选
            if (item.children?.find(m => m.id === e.id)) e._isChecked = item._isChecked
        })
        ele._isChecked = ele.children?.every(m => m._isChecked)
    })
}
const toAddGroup = () => {
    // 新建工作组
    store.resetGroupData();
    uni.navigateTo({ url: './AddPersonnelGroup' })
    // router.push({ name: 'meeting-handle-add-group' })
}
// 新增人员
const toAddPersonnel = () => {
    uni.navigateTo({ url: './AddExternalPerson' })
    // router.push({ name: 'meeting-handle-add-external' })
}
</script>
<style scoped>
.row-0 {
    color: #666;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
}

.p-main-vessel .p-top {
    box-sizing: border-box;
    width: 100%;
    /* height: 64px; */
    height: 44px;
    text-align: center;
    color: white;
    /* background: #0558bb; */
    background: linear-gradient(180deg, #084d9f 0%, #035cc5 100%);
    opacity: 1;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    font-size: 16px;
}

.p-main-vessel .p-top i {
    top: 50%;
    display: block;
    width: 9px;
    height: 16px;
    background-size: cover;
}

.p-main-vessel .p-top i:nth-child(1) {
    background-size: cover !important;
    background: url("@/static/images/meeting/back.png") center no-repeat;
    left: 15px;
}

.p-main-vessel .p-top i:last-child {
    background-size: cover !important;
    background: url("@/static/images/meeting/add.png") center no-repeat;
    right: 15px;
    width: 16px;
    height: 16px;
}

.p-main-vessel .search {
    box-sizing: border-box;
    width: 100%;
    height: 51px;
    flex-shrink: 0;
    padding: 0 15px;
    background: #035bc2;
    padding-top: 9px;
    position: relative;
}

.p-main-vessel .search .search-input {
    outline: none;
    height: 30px;
    width: 100%;
    padding: 6px 10px;
    opacity: 1;
    border-radius: 3px;
    border: 1px solid rgba(153, 153, 153, 0.5);
    color: #999999;
    font-size: 14px;
}

.search-icon {
    display: block;
    position: absolute;
    right: 25px;
    top: 15px;
}

.p_checkall {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 8px;
    padding: 10px 0;
    font-size: 14px;
    color: #333;
}

.p-main-vessel {
    height: 100%;
    box-sizing: border-box;
}

.p-main-vessel .p-content {
    box-sizing: border-box;
    width: 100%;
    height: calc(100vh - 155px);
    overflow-y: auto;
    padding: 10px 15px;
}

.p-main-vessel .p-content .dept-icon {
    color: #3e75fe;
    font-size: 10px;
    width: 16px;
    height: 16px;
    line-height: 16px;
    border: 1px solid #3e75fe;
    text-align: center;
    border-radius: 9px;
    /* margin-right: 9px; */
}

.p-main-vessel .p-content .select-p {
    margin-bottom: 14px;
}

.p-main-vessel .p-content .select-p .row-o {
    color: #666666;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.p-main-vessel .p-content .select-p .row-o i {
    display: block;
    width: 12px;
    height: 12px;
    background: url("@/static/images/meeting/down.png") center no-repeat;
    background-size: cover;
    margin-left: 7px;
}

.p-main-vessel .p-content .select-p .row-t {
    display: flex;
    margin-bottom: 6px;
    align-items: center;
    gap: 8px;
}

.p-main-vessel .p-content .select-p .row-t i {
    display: block;
    width: 20px;
    height: 20px;
    margin-right: 7px;
}

.p-main-vessel .p-content .select-p .row-t .round {
    width: 34px;
    height: 34px;
    background: #4f7af6;
    border-radius: 17px;
    color: #ffffff;
    font-size: 14px;
    text-align: center;
    line-height: 34px;
}

.p-main-vessel .p-content .select-p .row-t .row-name {
    border-bottom: 1px solid rgba(153, 153, 153, 0.2);
    width: 74%;
    line-height: 34px;
    margin-left: 8px;
    color: #333333;
    font-size: 14px;
}

/* 底部通知按钮 */
.base-btn-footer {
    margin-bottom: 10px;
    padding-top: 10px;
    /* height: 40px; */
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 20px;
}

.n-button {
    width: 140px;
    height: 41px;
}

.add-more {
    position: relative;
}

.more-modal-container {
    position: absolute;
    top: 23px;
    right: -7px;
    z-index: 10;
    /* width: 134; */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.more-modal-container::before {
    /* 往上的小三角形 */
    position: absolute;
    top: -4px;
    right: 10px;
    content: '';
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #3E3E3E;
}

.modal-item {
    width: 134px;
    height: 44px;
    padding-left: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #3E3E3E;
    color: #fff;
    font-size: 14px;
}

.modal-item:hover {
    background-color: #4c4c4c;
}

.modal-item span {
    display: inline-block;
    flex: 1;
    display: flex;
    justify-content: flex-start;
    padding-left: 10px;
}

.modal-item.top {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.modal-item.bottom {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

.add-more {
    display: flex;
}
</style>