<template>
    <!-- <div class="base-top">
        <i id="goBack"></i>
        <span>会议提醒</span>
        <span></span>
    </div> -->
    <div class="remind-c inner-container">
        <div class="vessel">
            <span style="font-weight: bold;">会议提醒</span>
            <!-- <sl-radio v-model="notifyType" :value="0">立即通知</sl-radio>
            <sl-radio v-model="notifyType" :value="5">开始前5分钟</sl-radio>
            <sl-radio v-model="notifyType" :value="15">开始前15分钟</sl-radio>
            <sl-radio v-model="notifyType" :value="20">开始前20分钟</sl-radio> -->
            <div class="content" @click=" notifyType = notifyType == 0 ? null : 0">
                <span class="icon">
                    <SlSVgIcon name="components-selected" size="16" v-if="notifyType == 0" />
                    <SlSVgIcon name="components-unselected" size="16" v-else />
                    <!-- <svg class="icon-16" v-if="notifyType == 0">
                        <use xlink:href="#icon-components-selected"></use>
                    </svg>
                    <svg class="icon-16" v-else>
                        <use xlink:href="#icon-components-unselected"></use>
                    </svg> -->
                </span>
                <span>立即通知</span>
            </div>
            <div class="content" @click=" notifyType = notifyType == 5 ? null : 5">
                <span class="icon">
                    <SlSVgIcon name="components-selected" size="16" v-if="notifyType == 5" />
                    <SlSVgIcon name="components-unselected" size="16" v-else />
                    <!-- <svg class="icon-16" v-if="notifyType == 5">
                        <use xlink:href="#icon-components-selected"></use>
                    </svg>
                    <svg class="icon-16" v-else>
                        <use xlink:href="#icon-components-unselected"></use>
                    </svg> -->
                </span>
                <span>开始前5分钟</span>
            </div>
            <div class="content" @click=" notifyType = notifyType == 15 ? null : 15">
                <span class="icon">
                    <SlSVgIcon name="components-selected" size="16" v-if="notifyType == 15" />
                    <SlSVgIcon name="components-unselected" size="16" v-else />
                    <!-- <svg class="icon-16" v-if="notifyType == 15">
                        <use xlink:href="#icon-components-selected"></use>
                    </svg>
                    <svg class="icon-16" v-else>
                        <use xlink:href="#icon-components-unselected"></use>
                    </svg> -->
                </span>
                <span>开始前15分钟</span>
            </div>
            <div class="content" @click=" notifyType = notifyType == 20 ? null : 20">
                <span class="icon">
                    <SlSVgIcon name="components-selected" size="16" v-if="notifyType == 20" />
                    <SlSVgIcon name="components-unselected" size="16" v-else />
                    <!-- <svg class="icon-16" v-if="notifyType == 20">
                        <use xlink:href="#icon-components-selected"></use>
                    </svg>
                    <svg class="icon-16" v-else>
                        <use xlink:href="#icon-components-unselected"></use>
                    </svg> -->
                </span>
                <span>开始前20分钟</span>
            </div>
        </div>
        <div class="base-btn-footer">
            <up-button type="primary" text="完成" @click="save"></up-button>
            <up-button text="取消" @click="cancel"></up-button>
            <!-- <n-button class="medium" type="primary" @click="save">
                完成
            </n-button>
            <n-button class="medium" @click="cancel">
                取消
            </n-button> -->
        </div>
    </div>
</template>

<script setup lang="ts">
import SlSVgIcon from '@/components/SlSVgIcon.vue';
import useMettingHandleStore from '@/store/modules/metting-handle';
import { storeToRefs } from 'pinia';
import { onMounted, ref } from 'vue';
const notifyType = ref<number | null>(null)
const { mettingHandle } = storeToRefs(useMettingHandleStore())
onMounted(() => {
    // 选立即发送就传 `sendInstantNotification=1`和`notifyMinutesBefore=null` 
    // 选其他就穿 `sendInstantNotification=0`和`notifyMinutesBefore=15`
    const { sendInstantNotification, notifyMinutesBefore } = mettingHandle.value
    if (sendInstantNotification == 1 && notifyMinutesBefore == null) {
        notifyType.value = 0
    } else if (sendInstantNotification == 0) {
        notifyType.value = notifyMinutesBefore == null ? null : notifyMinutesBefore
    }
    // notifyType.value = mettingHandle.value?.notifyMinutesBefore == null ? null : mettingHandle.value?.notifyMinutesBefore
})
function save() {
    if (notifyType.value == null) { // 未选值，不通知
        mettingHandle.value.sendInstantNotification = 0
        mettingHandle.value.notifyMinutesBefore = null
    } else if (notifyType.value == 0) { // 立即通知
        mettingHandle.value.sendInstantNotification = 1
        mettingHandle.value.notifyMinutesBefore = null
    } else {
        mettingHandle.value.sendInstantNotification = 0
        mettingHandle.value.notifyMinutesBefore = notifyType.value
    }
    uni.navigateBack()
    // router.back()
}
function cancel() {
    uni.navigateBack()
    // router.back()
}
</script>

<style scoped>
@import "../../styles/metting-base.css";

.remind-c {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #ffffff;
    padding: 10px 15px;
    font-size: 14px;
}

.remind-c .vessel {
    margin-top: 10px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    /* 3.125vw → 10px (基于375px视窗) */
}

label {
    font-size: 14px !important;
}

.content {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    font-size: 14px;
}

.icon {
    display: inline-flex;
    align-items: center;
    margin-right: 8px;
}

.n-button {
    width: 140px;
    height: 41px;
}
</style>