.sl-mr-container {
  width: 100%;
  position: relative;
  /* 147 / 291 ≈ 0.50515 → 50.515% */
  padding-bottom: 50.515%;
}

.sl-mr-container .svg-el {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.sl-mr-container .svg-el .meeting-room:hover {
  cursor: pointer;
  transition: fill 0.2s, stroke 0.2s;
}

.sl-mr-container .svg-el .meeting-room.selected{
  /* 整个 g 里的所有形状都加高亮 */
  fill: #ffe6e4 !important;
  stroke: #ff3737 !important;
  stroke-width: 1 !important;
}

.sl-mr-container .route-line {
  fill: none;
  stroke: #f56c6c;
  stroke-width: 1;
  stroke-dasharray: 6 4;
}