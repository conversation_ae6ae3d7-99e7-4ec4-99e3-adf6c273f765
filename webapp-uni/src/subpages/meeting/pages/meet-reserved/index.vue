<template>
    <div class="res-container">
      <!-- 主体 -->
      <div class="mainBody"  :class="{ 'other-mainBody': currentIdx === 3 }">
        <div class="res-card-top">
              <span :class="{ active: currentIdx == 1 }" @click="changeIdx(1)" class="res-card-span">全部</span>
              <span :class="{ active: currentIdx == 2 }" @click="changeIdx(2)" class="res-card-span">待办</span>
              <span :class="{ active: currentIdx == 3 }" @click="changeIdx(3)" class="res-card-span">完结</span>
        </div>
        <div class="no-Data" v-if="meetings.length === 0">
          <i class="no-c"></i>
          <span>暂无预约</span>
        </div>
        <div class="res-card" v-else>
          <div
            class="card"
            v-for="(item, index) in meetings"
            :key="index"
            :class="getStatusClass(item.status)"
            @click="handleCardClick(item)"
          >
          <!-- 勾选图标 -->
          <span
              @click.stop="checked(item)"
              v-if="(item.status == 2 || item.status == 3)&&currentIdx==3"
              class="checked-icon"
            >
            <SlSvgIcon :name="`components-selected`" size="16" v-if="item.checked"/>
            <SlSvgIcon :name="`components-unselected`" size="16" v-else/>
            </span>
            <!-- 卡片内容 -->
          <div :style="currentIdx==3 ? { width: '87%' } : {}">
            <div class="card-o">
              <span>{{ item.title }}</span>
              <span class="status" :class="getStatusClass(item.status)">
                {{ getStatusStatus(item.status) }}
              </span>
            </div>
            <div class="card-t">
              <SlSvgIcon :name="`meeting-12-7`" size="12" />
                <span>{{ item.timeRange }}</span>
            </div>
            <div class="card-t">
              <SlSvgIcon :name="`meeting-12-5`" size="12" />
                <span>{{ item.name }}</span>
            </div>
          </div>
          </div>
        </div>
      </div>
      <!-- 全选按钮 -->
      <div class="delete" v-if="currentIdx==3">
        <div
          @click="allShowConfirmHandle"
          style="display: flex; align-items: center"
        >
        <SlSvgIcon :name="`components-selected`" size="16" v-if="allShowConfirm"/>
        <SlSvgIcon :name="`components-unselected`" size="16" v-else/>
          <span class="delete-t">全选</span>
        </div>
        <div class="delete-btn" @click="delete1" :class="{ 'disabled': selectedIds.length === 0 }">删除</div>
      </div>
      <!-- 底部导航 -->
      <div class="res-footer">
        <div
          :class="{ active: activeTab === 'schedule','footer-div': true }"
          @click="switchTab('schedule')"
        >
        <SlSvgIcon :name="getTabIcon('schedule')" size="22" />
          <span class="footer-span">日程</span>
        </div>
        <div
          :class="{ active: activeTab === 'reserve','footer-div': true }"
          @click="switchTab('reserve')"
        >
        <SlSvgIcon :name="getTabIcon('reserve')" size="22" />
          <span class="footer-span">预约</span>
        </div>
        <div :class="{ active: activeTab === 'room','footer-div': true }" @click="switchTab('room')">
          <SlSvgIcon :name="getTabIcon('room')" size="22" />
          <span class="footer-span">会议室</span>
        </div>
      </div>
    </div>
    <!-- 确认弹窗 -->
    <ConfirmDialog
      v-if="showConfirm"
      @close="showConfirm = false"
      @confirm="handleConfirm"
      >确定删除选中数据吗？</ConfirmDialog
    >
  </template>
  
  <script setup>
  import { ref, onMounted } from "vue";
  // import { useRouter } from "vue-router";
  import meetReservedService from "@/service/meet-assistant/meet-handle/meet-reserved.service";
//   import { useMettingHandleStore } from "@/stores/mettingHandle";
  // import { NButton, useMessage } from "naive-ui";
  import ConfirmDialog from "@/components/ConfirmDialog.vue";
  import SlSvgIcon from '@/components/SlSVgIcon.vue'
  // const router = useRouter();
  // const message = useMessage();
  const activeTab = ref("reserve");
  const showConfirm = ref(false);
  const allShowConfirm = ref(false);
  const meetings = ref([]);
  const currentIdx = ref(null)
  onMounted(() => {
    changeIdx(1)
  });
  function getList(statusList) {
    meetReservedService
      .getListReserved({ loadMeetingroom: true, onlyCreator: true,statusList })
      .then((res) => {
        const newArray = res.result.map((item) => ({
          ...item.meetingroom,
          timeRange: item.timeRange,
          title: item.title,
          id: item.id,
          status: item.status,
        }));
        console.log(newArray, "newArray");
        meetings.value = newArray;
      });
  }
  function batchDelete(idList) {
    meetReservedService
      .batchDelete(idList)
      .then((res) => {
        // message.success("删除成功");
      })
      .catch((err) => {
        // message.error("删除失败");
      })
      .finally(() => {
        changeIdx(currentIdx.value);
        allShowConfirm.value = false;
        showConfirm.value = false;
      });
  }
  const getStatusClass = (status) => {
    switch (status) {
      case 0:
        return "status-pending";
      case 1:
        return "status-in-progress";
      case 2:
        return "status-cancel";
      case 3:
        return "status-finished";
      default:
        return "";
    }
  };
  const getStatusStatus = (status) => {
    switch (status) {
      case 0:
        return "待开始";
      case 1:
        return "进行中";
      case 2:
        return "已取消";
      case 3:
        return "已结束";
      default:
        return "";
    }
  };
  const tabIcons = {
    schedule: {
      default: "home-日程(默认)",
      active: "home-日程(选中)",
    },
    reserve: {
      default: "home-预约(默认)",
      active: "home-预约(选中)",
    },
    room: {
      default: "home-会议室(默认)",
      active: "home-会议室(选中)",
    },
  };
  
  // 获取底部导航图标
  const getTabIcon = (tab) => {
    return activeTab.value === tab ? tabIcons[tab].active : tabIcons[tab].default;
  };
  
  const goBack = () => {
    uni.navigateTo({ url:"/meetSchedule"});
  };
//   const meetHandleStore = useMettingHandleStore();
  const goAdd = () => {
    // meetHandleStore.reset();
    uni.navigateTo({ url:"/meeting-handle"});
  };
  
  const handleCardClick = (item) => {
    if (item.id) {
      uni.navigateTo({ url:`/meeting-info/${item.id}`});
    }
    console.log("点击的卡片数据：", item.id);
  };
  
  const switchTab = (tab) => {
    activeTab.value = tab;
    if (tab === "schedule") {
      uni.navigateTo({ url:"/subpages/meeting/pages/meet-schedule/index"});
    } else if (tab === "room") {
      uni.navigateTo({ url:"/subpages/meeting/pages/meet-room/index"});
    }
  };
  // 选中的id数组
  const selectedIds = ref([]);
  const checked = (item) => {
    if (![2, 3].includes(item.status)) return;
    item.checked = !item.checked;
    if (item.checked) {
      if (!selectedIds.value.includes(item.id)) {
        selectedIds.value.push(item.id);
      }
    } else {
      selectedIds.value = selectedIds.value.filter((id) => id !== item.id);
    }
    const allQualifiedSelected = meetings.value
      .filter((meeting) => [2, 3].includes(meeting.status))
      .every((meeting) => meeting.checked);
      if(allQualifiedSelected){
        allShowConfirm.value = true
      }else{
        allShowConfirm.value = false
      }
    console.log("当前选中的ID:", selectedIds.value);
  };
  /**删除全选*/
  const deleteAllQualifiedMeetings = () => {
    // 判断是否已经全部选中了符合条件的会议
    const allQualifiedSelected = meetings.value
      .filter((meeting) => [2, 3].includes(meeting.status))
      .every((meeting) => meeting.checked);
  
    if (allQualifiedSelected) {
      // 如果已经全选，则取消所有符合条件的选中状态
      meetings.value.forEach((meeting) => {
        if ([2, 3].includes(meeting.status)) {
          meeting.checked = false;
        }
      });
    } else {
      // 否则选中所有符合条件的会议
      meetings.value.forEach((meeting) => {
        if ([2, 3].includes(meeting.status)) {
          meeting.checked = true;
        }
      });
    }
  
    selectedIds.value = meetings.value
      .filter((meeting) => meeting.checked)
      .map((meeting) => meeting.id);
    console.log("当前选中的会议 ID:", selectedIds.value);
  };
  const delete1 = async () => {
    if(selectedIds.value.length==0)return
    showConfirm.value = true;
  };
  
  const handleConfirm = async () => {
    batchDelete([...selectedIds.value]);
  };
  //勾选
  const allShowConfirmHandle = () => {
    allShowConfirm.value = !allShowConfirm.value;
    deleteAllQualifiedMeetings();
  };
  
  const changeIdx = (idx) => {
      currentIdx.value = idx
      meetings.value = []
      if(idx==1){ //全部
        getList()
      }else if(idx==2){//待开始 0,1
        getList([0,1])
      }else{ //完结 2,3
        getList([2,3])
      }
      allShowConfirm.value=false
      selectedIds.value = [];
  }
  </script>
  
  <style scoped>
  .res-container {
    background-color: #edeff3;
  }
  
  .res-container .top {
    text-align: center;
    color: white;
    /* background: linear-gradient(180deg, #084D9F 0%, #0066DF 100%); */
    background: var(--layout-header-bg);
    width: 100%;
    height: 44px;
    line-height: 64px;
    opacity: 1;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14.5px;
    font-size: 16px;
  }
  
  .res-container .top i {
    top: 50%;
    display: block;
    width: 9px;
    height: 16px;
    background-size: cover;
  }
  
  .res-container .goBack {
    /* left: 14.5px;
    font-size: 16px; */
  }
  
  .res-container .add {
    /* right: 14.5px;
    font-size: 16px; */
  }
  
  .res-container .mainBody {
    height: calc(100vh - 52px);
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
  }
  
  .res-container .mainBody .no-Data {
    width: 100%;
    height:calc(100% - 35px) ;
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center;
  }
  
  .res-container .mainBody .no-Data .no-c {
    display: block;
    width: 100%;
    height: 40px;
    background: url("@/static/images/meeting/noData.png") center no-repeat;
  }
  
  .res-container .mainBody .no-Data span {
    color: #999999;
    font-size: 12px;
  }
  
  .res-container .mainBody .res-card {
    width: 100%;
    height:calc(100% - 35px) ;
    overflow: auto;
    padding-bottom: 51px;
  }
  
  .res-container .mainBody .res-card .card {
    background: #ffffff;
    width: 100%;
    height: 90px;
    margin-bottom: 4px;
    padding: 11px 15px;
    position: relative;
  }
  
  .res-container .mainBody .res-card .card.status-cancel,
  .res-container .mainBody .res-card .card.status-finished {
    background: #e6e5e5;
  }
  
  .res-container .mainBody .res-card .card .card-o {
    display: flex;
    justify-content: space-between;
  }
  
  .res-container .mainBody .res-card .card .card-o span:nth-child(1) {
    color: #333333;
    font-size: 14px;
  }
  
  .res-container .mainBody .res-card .card .card-o span.status {
    opacity: 1;
    border-radius: 2px;
    width: 46px;
    height: 23px;
    font-size: 12px;
    text-align: center;
    line-height: 23px;
    display: inline-block;
  }
  
  .res-container .mainBody .res-card .card .card-o span.status.status-pending {
    color: #ff7605;
    background: #fff0e4;
    border: 1px solid #ffb06f;
  }
  
  .res-container
    .mainBody
    .res-card
    .card
    .card-o
    span.status.status-in-progress {
    color: #ff3333;
    background: #ffe3e3;
    border: 1px solid #ff6868;
  }
  
  .res-container .mainBody .res-card .card .card-o span.status.status-cancel {
    color: #666666;
    background: #e6e6e6;
    border: 1px solid #999999;
  }
  
  .res-container .mainBody .res-card .card .card-o span.status.status-finished {
    color: #ffffff;
    background: #b6b6b6;
    border: 1px solid #999999;
  }
  
  .res-container .mainBody .res-card .card .card-t {
    display: flex;
    align-items: center;
    margin-top: 6px;
  }
  
  .res-container .mainBody .res-card .card .card-t i {
    display: block;
    width: 12px;
    height: 12px;
    background: url("@/static/images/meeting/place1.png") center no-repeat;
    background-size: cover;
    margin-right: 6px;
  }
  
  .res-container .mainBody .res-card .card .card-t span {
    color: #666666;
    font-size: 12px;
    margin-left: 5px;
  }
  
  .res-container .mainBody .res-card .card .card-t .fas {
    background: url("@/static/images/meeting/time.png") center no-repeat !important;
    background-size: cover !important;
  }
  
  /* 底部导航 */
  .res-footer {
    width: 100%;
    height: 49px;
    border-radius: 10px 10px 0 0;
    background: rgba(255, 255, 255, 0.94);
    backdrop-filter: blur(20px);
    box-shadow: 0 -0.5px 6px rgba(93, 93, 93, 0.2);
    position: absolute;
    bottom: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
  
  .res-footer .footer-div {
    width: 34px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
  }
  
  .res-footer .footer-div i {
    display: block;
    width: 22px;
    height: 22px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  
  .res-footer .footer-div .footer-span {
    color: #666666;
    font-size: 10px;
    width: 100%;
    text-align: center;
  }
  
  .res-footer .footer-div.active .footer-span {
    color: #0066df;
  }
  /* .card:last-child {
    margin-bottom: 80px !important;
  } */
  
  /* 底部图标定义 */
  .delete {
    width: 100%;
    background: white;
    display: flex;
    /* position: absolute;
    bottom: 50px; */
    height: 42px;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
    z-index: 999;
  }
  .delete-btn {
    width: 110px;
    height: 38px;
    background: #0066df;
    font-size: 14px;
    color: #ffffff;
    text-align: center;
    line-height: 38px;
    border-radius: 5px;
  }
  .disabled {
    background: #999999;
  }
  .delete-t {
    font-size: 14px;
    margin-left: 5px;
  }
  .card {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }
  .res-card-top {
    width: 100%;
    height: 35px;
    background: #ffffff;
  }
  .res-card-top .res-card-span {
    position: relative;
    cursor: pointer;
    padding-bottom: 1.5625vw;
    /* 5px → 1.5625vw */
    margin-right: 5.625vw;
    /* 18px → 5.625vw */
  }
  
  /* 蓝色下划线（默认隐藏） */
  .res-card-top .res-card-span::after {
    content: "";
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 0;
    height: 0.625vw;
    transform: translateX(-50%);
    /* 2px → 0.625vw */
    background-color: #4f7af6;
    transition: width 0.3s ease;
  }
  
  /* 当前选中项的下划线（显示） */
  .res-card-top .res-card-span.active::after {
    width: 50%;
  }
  
  .res-card-top .res-card-span.active {
    color: #333333;
    font-weight: bold;
  }
  .res-card-top {
    height: 35px;
    border-bottom: 0.3125vw solid #99999933;
    padding: 0 5.3125vw;
    padding-top: 3.125vw;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .card{
    border-radius: 0px;
  }
  .checked-icon{
    width: 16px !important;
    height: 16px !important;
  }
  .other-mainBody{
    height: calc(100vh - 102px) !important;
  }
  </style>
  