<template>
    <div class="container1">
      <!-- Search -->
      <div class="search">
        <input
          v-model="searchText"
          type="text"
          placeholder="请输入会议室名"
        />
        <SlSvgIcon :name="'car-search'" size="16" class="search-icon"/>
        <div class="nav">
          <span
            v-for="item in navItems"
            :key="item.type"
            :class="['nav-item', { active: activeType === item.type }]"
            :data-type="item.type"
            @click="handleNavClick(item.type)"
            :style="{ marginLeft: item.type === 'all' ? '0' : '' }"
          >
            {{ item.label }}
          </span>
        </div>
      </div>
  
      <!-- 主体 -->
      <div class="mainPart">
        <div class="subject">
          <div class="card" v-for="(item, index) in filteredData" :key="index">
            <div class="c-line1">
              <span class="line1-span">{{ item.floor }}F</span>
              <span class="line1-span">{{ item.name }}</span>
            </div>
            <div class="c-line2">
              <!-- <i></i> -->
              <SlSvgIcon :name="'meeting-14-2'" size="14" />
              <!-- <span v-for="(d, i) in item.device" :key="i">{{ d }}</span> -->
              <span class="line2-span">{{item.deviceInfo}}</span>
            </div>
            <div class="c-line3">
              <SlSvgIcon :name="'meeting-14-1'" size="14" />
              <!-- <span v-for="(c, i) in item.capacity" :key="i">{{ c }}</span> -->
              <span class="line3-span">{{item.capacity}}</span>
            </div>
            <div class="c-line4">
              <span class="line4-t">预约时间</span>
              <div class="line4-div">
                <span v-for="(t, i) in item.times" :key="i" class="line4-span">{{ t.timeRange }}</span>
                <span v-if="!item.times || item.times.length === 0" class="line4-span">无</span>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Footer -->
      <div class="met-footer3">
      <div
        class="met-footer3-div"
        v-for="(item, index) in footerNav"
        :key="index"
        :class="{ active: activeFooter === item.label }"
        @click="handleFooterClick(item)"
      >
      <SlSvgIcon :name="getFooterIcon(item.label)" size="22" />
        <span class="met-footer3-span">{{ item.label }}</span>
      </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed,onMounted  } from "vue";
  import getMeetingroom from "@/service/meet-assistant/meet-handle/meet-reserved.service";
  import SlSvgIcon from '@/components/SlSVgIcon.vue'
  const searchText = ref("");
  const activeType = ref("all");
  const activeFooter = ref("会议室");
  // 组件挂载后立即调用
  onMounted(() => {
    getList();
  });
  // 样式分类项
  const navItems = [
    { label: "全部", type: "all" },
    { label: "5F", type: "5" },
    { label: "8F", type: "8" },
    { label: "9F", type: "9" },
    { label: "13F", type: "13" },
  ];
  
  const footerNav = [
    { label: "日程", route: "/subpages/meeting/pages/meet-schedule/index" },
    { label: "预约", route: "/subpages/meeting/pages/meet-reserved/index" },
    { label: "会议室", route: "/subpages/meeting/pages/meet-room/index" },
  ];
  
  // 模拟数据
  const meetRoomData = ref([])
  const footerIcons = {
    '日程': {
      default: "home-日程(默认)",
      active: "home-日程(选中)"
    },
    '预约': {
      default: "home-预约(默认)",
      active: "home-预约(选中)"
    },
    '会议室': {
      default: "home-会议室(默认)",
      active: "home-会议室(选中)"
    }
  };
  const goBack = () => {
    uni.navigateTo({ url:"/home"});
  };
  // 获取底部导航图标
  const getFooterIcon = (label) => {
    return activeFooter.value === label 
      ? footerIcons[label].active 
      : footerIcons[label].default;
  };
  
  // 筛选数据
  const filteredData = computed(() => {
    const keyword = searchText.value.trim();
    let result = meetRoomData.value;
    if (activeType.value !== "all") {
      result = result.filter((item) =>
        item.floor.startsWith(activeType.value)
      );
    }
  
    if (keyword) {
      result = result.filter(
        (item) =>  item.name.includes(keyword)
      );
    }
  
    return result;
  });
  
  // 处理导航点击
  const handleNavClick = (type) => {
    activeType.value = type;
    searchText.value = "";
  };
  
  // 搜索功能
  const handleSearch = () => {
    console.log("搜索关键词:", searchText.value);
  };
  
  // 底部导航点击
  const handleFooterClick = (item) => {
    activeFooter.value = item.label;
    uni.navigateTo({ url:item.route});
  };
  
  function getList() {
    getMeetingroom.getMeetingroom({ loadMeetingList: true,onlyActiveAndFutureMeetings:true }).then((res) => {
      meetRoomData.value = res.result.map((item) => ({
        name:item.name,
        floor:String(item.floor),
        capacity:item.capacity,
        deviceInfo:item.deviceInfo,
        times:item.meetingList
      }))
    });
  }
  </script>
  
  <style scoped >
  .container {
      display: flex;
      flex-direction: column;
      height: 100vh;
      position: relative;
    }
    /* 标题区域 */
    .top {
        text-align: center;
        color: white;
        background: linear-gradient(180deg, #084D9F 0%, #0066DF 100%);
        width: 100%;
        height: 44px;
        line-height: 44px;
        opacity: 1;
        flex-shrink: 0;
      }
      .top span {
        color: #ffffff;
        font-size: 16px;
      }
      
      .search {
        width: 100%;
        height: 74px;
        flex-shrink: 0;
        padding: 0 15px;
        background: #ffffff;
        padding-top: 9px;
        position: relative;
        box-sizing: border-box;
      }
      .search input {
        outline: none;
        height: 30px;
        /* width: 290px; */
        width: 100%;
        padding: 0px 10px;
        opacity: 1;
        border-radius: 3px;
        border: 1px solid #9999997f;
        color: #999999;
        font-size: 14px;
        box-sizing: border-box;
      }
      .search .search-icon {
        position: absolute;
        right: 25px;
        top: 15px;
      }
      
      .search .nav {
        width: 100%;
        margin-top: 10px;
        display: flex;
        align-items: center;
      }
      .search .nav  span {
        padding-bottom: 4px;
      }
      .nav-item {
        margin-left: 20px;
        color: #333333;
        font-size: 14px;
        cursor: pointer;
        position: relative;
      }
      .nav-item::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background-color: transparent;
        transition: width 0.3s ease, background-color 0.3s ease;
      }
      .nav-item.active {
        /* color: #455bff; */
        font-size: 16px;
        font-weight: bold;
      }
      .nav-item.active::after {
        width: 100%;
        background: #4355FC;
      }
      
      .mainPart {
        width: 100%;
        /* height: 380px; */
        height: calc(100vh - 167px);
        flex: 1;
        overflow: auto;
        padding-bottom: 51.2px; /* 略大于 .met-footer3 的高度 49px */
      }
      
      .subject {
        width: 100%;
        padding: 0 15px;
        box-sizing: border-box;
      }
      .subject .card {
        width: 100%;
        min-height: 136px;
        border-radius: 10px;
        background: #ffffff;
        box-shadow: 0 0 6px 0 #92929233;
        margin-top: 10px;
        padding: 10px;
        box-sizing: border-box;
      }
      .subject .card:last-child {
        margin-bottom: 10px;
      }
      .subject .card .c-line1 {
        display: flex;
      }
      .subject .card .c-line1 .line1-span:nth-child(1) {
        color: #ffffff;
        font-weight: bold;
        font-size: 14px;
        display: block;
        width: 38px;
        height: 20px;
        opacity: 1;
        border-radius: 2px;
        background: #0066DF;
        text-align: center;
        line-height: 20px;
      }
      .subject .card .c-line1 .line1-span:nth-child(2) {
        margin-left: 8px;
        opacity: 1;
        color: #3d3d3d;
        font-family: Source Han Sans;
        font-weight: bold;
        font-size: 14px;
      }
      .subject .card .c-line2 {
        display: flex;
        margin-top: 11px;
      }
      .subject .card .c-line2 i {
        display: block;
        width: 14px;
        height: 14px;
        background-size: cover;
        margin-left: 8px;
      }
      .subject .card .c-line2 .line2-span {
        color: #999999;
        font-size: 12px;
        margin-left: 8px;
      }
      .subject .card .c-line3 {
        display: flex;
      }
      .subject .card .c-line3 i {
        display: block;
        width: 14px;
        height: 14px;
        background-size: cover;
        margin-left: 8px;
      }
      .subject .card .c-line3 .line3-span {
        color: #999999;
        font-size: 12px;
        margin-left: 8px;
      }
      .subject .card .c-line4 {
        display: flex;
        /* margin-top: 10px; */
            margin-top: 3px;
      }
      .subject .card .c-line4 .line4-t {
        color: #333333;
        font-size: 14px;
        width: 70px;
        display: block;
      }
      .subject .card .c-line4 .line4-div {
        display: flex;
        flex-wrap: wrap;
        width: 204px;
      }
      .subject .card .c-line4 .line4-div .line4-span {
        color: #666666;
        font-size: 12px;
        width: 100%;
        position: relative;
        white-space: nowrap;
      }
      .subject .card .c-line4 .line4-div .line4-span::after {
        content: "";
        position: absolute;
        left: -7px;
        top: 41%;
        width: 3px;
        height: 3px;
        background-color: red;
      }
    
    
    /* 底部导航 */
    .met-footer3 {
      width: 100%;
      height: 49px;
      border-radius: 10px 10px 0 0;
      background: rgba(255, 255, 255, 0.94);
      backdrop-filter: blur(20px);
      box-shadow: 0 -0.5px 6px rgba(93, 93, 93, 0.2);
      /* position: absolute; */
      bottom: 0;
      display: flex;
      justify-content: space-around;
      align-items: center;
    }
    
    .met-footer3 .met-footer3-div {
      width: 34px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      cursor: pointer;
      -webkit-tap-highlight-color: transparent;
    }
    
    .met-footer3 .met-footer3-div i {
      display: block;
      width: 22px;
      height: 22px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }
    
    .met-footer3 .met-footer3-div .met-footer3-span {
      color: #666666;
      font-size: 10px;
      width: 100%;
      text-align: center;
    }
    
    .met-footer3 .met-footer3-div.active .met-footer3-span {
      color: #0066df;
    }
    
    /* 底部图标定义 */
   
  </style>
  