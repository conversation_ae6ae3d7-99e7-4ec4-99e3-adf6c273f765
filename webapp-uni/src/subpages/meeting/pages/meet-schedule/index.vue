<template>
    <div class="main">
      <!-- 顶部区域 -->
      <div class="tops">
        <!-- <div class="top">
         <svg  class="icon-16 goBack" @click="goBack"><use  xlink:href="#icon-home-前翻-2"></use></svg>
          <span>会议助手</span>
        </div> -->
        <div class="center">
        <SlSvgIcon :name="`meeting-36-3`" size="36" style="display: flex;"/>
          <span class="time-h">{{ greeting }} {{userInfo?.name||''}}</span>
        </div>
  
        <!-- 日历 -->
        <div class="calendar">
          <div class="calendar-plug-in" @click="showCalendar = true">
            <span class="calendar-m">{{ currentMonthLabel }}</span>
            <SlSvgIcon :name="`meeting-12-8`" size="12" />
          </div>
          <div class="calendar-show">
            <div class="week">
              <div
              class="week-item"
                v-for="(w, i) in weekLabels"
                :key="i"
                :class="{ active: activeIndex === i }"
              >
                {{ w }}
              </div>
            </div>
            <div class="day">
              <div
                class="day-item"
                v-for="(d, i) in daysOfWeek"
                :key="i"
                :class="{ active: activeIndex === i }"
                @click="selectDay(i)"
              >
                {{ d }}
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- 卡片区域 -->
      <div class="cards-container content" >
        <div class="cards-container-top">
          <div class="top-one">
            今天<span class="one-span">{{ selectedDayLabel }}</span>
          </div>
          <div class="top-tow">
            你还有<span class="top-span">{{ amount() }}</span
            >个会议任务
          </div>
        </div>
        <div class="cards-wrapper" ref="cardWrapper" v-if="meetingCards.length" :class="{ 'justify-center': meetingCards.length === 1 }">
          <div
            class="card"
            v-for="(card, index) in meetingCards"
            :key="index"
            :ref="
              (el) => {
                if (el) cardRefs[index] = el;
              }
            "
            :class="{ 'partially-visible': isFullyVisible[index] === false }"
            @click="goInfo(card)"
          >
            <div class="title-t" :style="{ color: getStatusColor(card.status) }">
              <i></i>{{ getStatusText(card.status) }}
            </div>
            <span class="title-r">{{
              card.meetingfacilityList.length ? "有会服" : "无会服"
            }}</span>
            <span class="title-t1">{{ card.title }}</span>
            <div class="card-footer">
              <span>{{ card.time }}</span>
              <i></i>
              <span>{{ card.time1 }}</span>
            </div>
            <div class="reserved">
              <span class="i">发起人</span>
              <span>{{ card.creatorName }}</span>
            </div>
            <div class="reserved">
              <span class="i">会议室</span>
              <span>{{ card.location }}</span>
            </div>
            <div class="reserved">
              <span class="i">参加人员</span>
              <div class="join">
                <span v-for="(name, index) in card.nameList" :key="index">{{
                  name
                }}</span>
                 <SlSvgIcon :name="`meeting-更多头像`" size="30" v-if="card.meetingpersonnelList.length > 2"/>
              </div>
            </div>
            <div class="go-to-meeting" @click.stop="handleGoToMeeting(card)">
              前往会议室
            </div>
          </div>
        </div>
        <div class="noData" v-else >
          <SlSvgIcon :name="`home-暂无`" size="30" />
          <span>暂无会议</span>
        </div>
      </div>
  
      <!-- 底部导航 -->
      <div class="met-footer">
      <div
      class="footer-item"
        v-for="(item, index) in footerItems"
        :key="index"
        :class="{ active: activeFooter === index }"
        @click="handleFooterClick(index)"
      >
      <SlSvgIcon :name="getFooterIcon(index)" size="22" />
        <span class="footer-span">{{ item }}</span>
      </div>
    </div>
  
      <!-- 日历弹窗 -->
      <CalendarPopup
        v-if="showCalendar"
        :initialDate="selectedDate || new Date()"
        :selectedDate="selectedDate"
        @close="showCalendar = false"
        @select-date="handleDateSelect"
      />
    </div>
  </template>
  
  <script  setup>
  import { ref, onMounted, onBeforeUnmount, nextTick, watch,computed } from "vue";
  // import { useRouter } from "vue-router";
  import CalendarPopup from "./CalendarPopup.vue";
  import meetReservedService from "@/service/meet-assistant/meet-handle/meet-reserved.service";
  import SlSvgIcon from '@/components/SlSVgIcon.vue'
  // import type { UserInfo } from "@/models/MeetReserved1"
  // 路由
  // const router = useRouter();
  const goBack = () => uni.navigateTo({ url:"/home"});
  
  // UI 状态相关
  const activeFooter = ref(0);
  const footerItems = ["日程", "预约", "会议室"];
  const showCalendar = ref(false);
  const userInfo = ref(null);
  // 日期和日历相关
  const weekLabels = ["日", "一", "二", "三", "四", "五", "六"];
  const currentDate = new Date();
  const years = ref(`${currentDate.getFullYear()}`);
  const months = ref(`${currentDate.getMonth() + 1}`);
  const activeIndex = ref(currentDate.getDay());
  const currentMonthLabel = ref(`${months.value}月`);
  const selectedDayLabel = ref(`${months.value}月${currentDate.getDate()}日`);
  const selectedDate = ref(new Date(
      years.value,
      months.value - 1,
      currentDate.getDate()
    ));
  
  const getCurrentWeekDates = () => {
    const now = new Date();
    const currentDay = now.getDay();
    return Array.from({ length: 7 }, (_, i) => {
      const date = new Date(now);
      date.setDate(now.getDate() - currentDay + i);
      return date.getDate();
    });
  };
  
  const daysOfWeek = ref(getCurrentWeekDates());
  
  // 会议卡片数据
  const meetingCards = ref([]);
  const cardRefs = ref([]);
  const isFullyVisible = ref([]);
  const cardsWrapper = ref(null);
  let observer = null;
  
  // 获取用户
  const getUser = () => {
    meetReservedService.getCurrentPersonnel().then((res) => {
      userInfo.value = res;
    })
  }
  
  // 数据请求逻辑
  const getList = (date) => {
    meetReservedService
      .getListReserved({
        date,
        loadMeetingpersonnelList: true,
        loadMeetingroom: true,
        onlyParticipant:true,
      })
      .then((res) => {
        meetingCards.value = res.result.map((item) => {
          const nameList =
            item.meetingpersonnelList?.map((p) => p.personnel?.name?.charAt(0)) ||
            [];
          return {
            ...item,
            time: item.startTime?.slice(11, 16),
            time1: item.endTime?.slice(11, 16),
            location: item.meetingroom?.name,
            nameList: nameList.length >= 3 ? nameList.slice(0, 2) : nameList,
          };
        });
        console.log(meetingCards.value,'meetingCards.value')
      });
  };
  const goInfo = (item)=>{
    // if(item.ifCreator){
    //   uni.navigateTo({ `/meeting-handle-edit/${item.id}`);
    // }else{
    //   uni.navigateTo({ `/meeting-info/${item.id}`);
    // }
    uni.navigateTo({ url:`/meeting-info/${item.id}`});
  }
  
  // 卡片可视性监测
  const initIntersectionObserver = () => {
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const index = cardRefs.value.indexOf(entry.target);
          if (index !== -1) {
            isFullyVisible.value[index] =
              entry.isIntersecting && entry.intersectionRatio === 1;
          }
        });
      },
      {
        root: cardsWrapper.value,
        threshold: 1.0,
        rootMargin: "0px",
      }
    );
  
    cardRefs.value.forEach((card, index) => {
      if (card) {
        isFullyVisible.value[index] = false;
        observer.observe(card);
      }
    });
  };
  
  // 方法 - UI交互
  const handleFooterClick = (index) => {
    activeFooter.value = index;
    const routeMap = ["/meetSchedule", "/subpages/meeting/pages/meet-reserved/index", "/subpages/meeting/pages/meet-room/index"];
    uni.navigateTo({ url:routeMap[index]});
  };
  
  const handleGoToMeeting = (card) => {
    // uni.navigateTo({ {
    //   path: '/goMeetRoom',
    //   query: card.meetingroom||{}
    // })
  };
  
  const selectDay = (index) => {
    activeIndex.value = index;
    const day = daysOfWeek.value[index];
    selectedDayLabel.value = `${months.value}月${daysOfWeek.value[index]}日`;
    getList(`${years.value}-${months.value}-${day}`);
    selectedDate.value = new Date(
      years.value,
      months.value - 1,
      day
    );
  };
  
  const handleDateSelect = (week, selectedIndex) => {
    const selected = week[selectedIndex];
    daysOfWeek.value = week.map((d) => d.date);
    years.value = selected.year;
    months.value = selected.month;
    activeIndex.value = selectedIndex;
    selectedDate.value = new Date(
      selected.year,
      selected.month - 1,
      selected.date
    );
    currentMonthLabel.value = `${selected.month}月`;
    selectedDayLabel.value = `${selected.month}月${selected.date}日`;
    getList(`${selected.year}-${selected.month}-${selected.date}`);
  };
  
  // 状态显示
  const getStatusText = (status) => {
    switch (status) {
      case 0:
        return "会议即将开始";
      case 1:
        return "会议进行中";
      case 2:
        return "会议已取消";
      case 3:
        return "会议已结束";
      default:
        return "未知状态";
    }
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case 0:
        return "#ff7605";
      case 1:
        return "#ff5b5b";
      case 2:
      case 3:
        return "#b6b6b6";
      default:
        return "#000";
    }
  };
  const greeting = computed(() => {
    const hour = new Date().getHours();
    if (hour < 6) return '凌晨好！';
    if (hour < 12) return '上午好！';
    if (hour < 14) return '中午好！';
    if (hour < 18) return '下午好！';
    return '晚上好！';
  });
  const footerIcons = {
    0: {
      default: "home-日程(默认)",
      active: "home-日程(选中)"
    },
    1: {
      default: "home-预约(默认)",
      active: "home-预约(选中)"
    },
    2: {
      default: "home-会议室(默认)",
      active: "home-会议室(选中)"
    }
  };
  
  // 获取底部导航图标
  const getFooterIcon = (index) => {
    return activeFooter.value === index 
      ? footerIcons[index].active 
      : footerIcons[index].default;
  };
  
  // 生命周期钩子
  onMounted(() => {
    const today = new Date().getDate();
    getUser();
    getList(`${years.value}-${months.value}-${today}`);
    nextTick(() => {
      initIntersectionObserver();
    });
  });
  
  onBeforeUnmount(() => {
    if (observer) observer.disconnect();
  });
  
  watch(
    meetingCards,
    () => {
      nextTick(() => {
        if (observer) observer.disconnect();
        cardRefs.value = [];
        isFullyVisible.value = [];
        nextTick(() => initIntersectionObserver());
      });
    },
    { deep: true }
  );
  const amount = ()=>{
    const dataAmount = meetingCards.value.length;
    const countTotal =  meetingCards.value.reduce((total, item) => {
    return total + (item.status === 0 || item.status === 1 ? 1 : 0);
  }, 0);
   
    return countTotal +'/'+dataAmount
  }
  </script>
  
  <style scoped>
.main {
  position: relative;
  min-height: 100vh;
  /* min-height: calc(100vh - 44px); */
}

.tops {
  width: 100%;
  height: 190px;
  opacity: 1;
  /* background: linear-gradient(180deg, #084d9f 0%, #0066df 100%); */
  background: #045abf;
}

.centre {
  width: 100%;
  height: 378px;
}

.top {
  text-align: center;
  /* padding: 14.5px; */
  /* padding-top: 15px; */
  height: 44px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.top .goBack {
  position: absolute;
  left: 14.5px;
  top: 13px;
  /* display: block; */
  /* width: 9px; */
  /* height: 16px; */
  /* background: url("@/assets/image/meeting/back.png") center no-repeat; */
  /* background-size: cover; */
}

.top span {
  color: #ffffff;
  font-size: 16px;
}

.center {
  width: 100%;
  height: 62px;
  line-height: 62px;
  padding: 0 15px;
  display: flex;
  align-items: center;
}

.center i {
  display: block;
  width: 36px;
  height: 36px;
  background-size: cover;
}

.center .time-h {
  color: #ffffff;
  font-size: 14px;
  margin-left: 10px;
}

.calendar {
  padding: 0 15px;
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.calendar .calendar-plug-in {
  text-align: center;
  width: 35px;
  height: 56px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
}

.calendar .calendar-plug-in .calendar-m {
  color: #ffffff;
  font-size: 18px;
}

.calendar .calendar-plug-in i {
  display: block;
  width: 12px;
  height: 12px;
  background: url("@/static/images/meeting/bottom.png") center no-repeat;
  background-size: cover;
}

.calendar .calendar-show {
  width: 100%;
  height: 56px;
  color: #ffffff;
  font-size: 14px;
}

.calendar .calendar-show .week {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.calendar .calendar-show .week .week-item {
  width: 27px;
  height: 31px;
  text-align: center;
  line-height: 31px;
}

.calendar .calendar-show .day {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.calendar .calendar-show .day .day-item {
  width: 27px;
  height: 31px;
  line-height: 31px;
  text-align: center;
}

.week .week-item.active {
  background-color: #61eaff;
  border-radius: 14px 14px 0 0;
  color: #0557b7;
}

.day .day-item.active {
  background-color: #61eaff;
  border-radius: 0 0 14px 14px;
  color: #0557b7;
}

.content {
  width: 100%;
  height: 378px;
  border-radius: 10px 10px 0 0;
  background: linear-gradient(180deg, #ffffff 0%, #f4f6fa 100%);
  position: relative;
}

.cards-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 239px);
  margin: 0 auto;
  overflow: hidden;
}

.cards-container-top {
  display: flex;
  height: 53px;
  line-height: 53px;
  padding: 0 15px;
  border-bottom: 1px solid #dadae4;
  justify-content: space-between;
}

.top-one,
.top-tow {
  font-size: 14px;
  font-weight: normal;
}

.top-one .one-span {
  margin-left: 10px;
  font-size: 12px;
  color: #333333;
}

.top-tow .top-span {
  color: red;
}

.cards-wrapper {
  display: flex;
  gap: 20px;
  padding: 20px;
  transition: transform 0.3s ease;
  overflow-x: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  /* justify-content: space-around; */
}

.cards-wrapper::-webkit-scrollbar {
  display: none;
}

.card {
  flex: 0 0 200px;
  scroll-snap-align: start; /* 添加这个属性 */

  height: 230px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  opacity: 1;
  border-radius: 10px;
  border: 1px solid #ffffff;
  background: url("@/static/images/meeting/会议即将开始-2.png") center no-repeat;
  background-size: 110% 109%;
  position: relative;
  background-color: #f0f7ff;
}
.cards-wrapper .partially-visible {
  background: url("@/static/images/meeting/会议即将开始-3.png") center no-repeat;
  background-size: 110% 109%;
}

.card .title-t {
  color: #ff7605;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.card .title-t i {
  opacity: 1;
  border-radius: 0;
  background: #0066DF;
  width: 4px;
  height: 12px;
  margin-right: 3px;
}

.card .title-t1 {
  opacity: 1;
  color: #333333;
  font-weight: bold;
  font-size: 12px;
  line-height: 1.2;
  margin: 5px 0;
}

.card .title-r {
  position: absolute;
  right: 6px;
  top: 2px;
  font-size: 10px;
  color: #ffffff;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  color: #333333;
  font-size: 18px;
  align-items: center;
  font-weight: 500;
}

.card-footer i {
  display: block;
  width: 60px;
  height: 4px;
  background: url("@/static/images/meeting/箭头.png") center no-repeat;
  background-size: cover;
}

.reserved {
  display: flex;
  align-items: center;
  color: rgba(51, 51, 51, 0.7);
  font-size: 12px;
  justify-content: space-between;
  margin-bottom: 5px;
}

.join {
  display: flex;
}

.join span {
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  background: #4f7af6;
  border-radius: 17px;
  color: #ffffff;
  font-size: 12px;
}
.join i:nth-child(3) {
  background: #cbcdd0;
}

.go-to-meeting {
  width: 180px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  opacity: 1;
  border-radius: 3px;
  background: #0066df;
  color: #ffffff;
  font-size: 14px;
  margin-top: 10px;
}

.go-to-meeting.active {
  background: #4589ff !important;
}
.partially-visible .title-r {
  color: #d7d9dd;
}
.partially-visible .go-to-meeting {
  background: #4991e7;
  color: #d7d9dd;
}
.partially-visible .join i {
  background: #4991e7;
}
.scroll-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 20px;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.left-btn {
  left: 5px;
}

.right-btn {
  right: 5px;
}

.met-footer {
  width: 100%;
  height: 49px;
  border-radius: 10px 10px 0 0;
  background: rgba(255, 255, 255, 0.94);
  backdrop-filter: blur(20px);
  box-shadow: 0 -0.5px 6px rgba(93, 93, 93, 0.2);
  position: absolute;
  bottom: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.met-footer .footer-item {
  width: 34px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.met-footer .footer-item i {
  display: block;
  width: 22px;
  height: 22px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.met-footer .footer-item .footer-span {
  color: #666666;
  font-size: 10px;
  width: 100%;
  text-align: center;
  /* margin-top: 5px; */
}

.met-footer .footer-item.active .footer-span {
  color: #0066df;
}
.noData {
  height: 57px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #afb1b3;
  width: 50px;
  flex-wrap: wrap;
  position: absolute;
  top: 50%;
  left: 42%;
}
.noData span {
  display: block;
  margin-top: 10px;
}

  </style>
  