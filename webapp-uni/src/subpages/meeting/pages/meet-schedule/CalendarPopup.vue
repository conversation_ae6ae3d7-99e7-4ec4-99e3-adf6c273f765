<template>
    <div class="popup-overlay">
      <div class="popup">
        <div class="calendar-header">
          <div @click="changeYear(-1)" class="time-btn">«</div>
          <picker mode="selector" :range="years" :value="yearIndex" @change="onYearChange">
            <div>{{ currentYear }}年</div>
          </picker>
          <picker mode="selector" :range="monthLabels" :value="currentMonth" @change="onMonthChange">
            <div>{{ currentMonth + 1 }}月</div>
          </picker>
          <div @click="changeYear(1)" class="time-btn">»</div>
        </div>
        <div class="calendar-week">
          <div v-for="(w, i) in weekLabels" :key="i">{{ w }}</div>
        </div>
        <div class="calendar-grid">
          <div v-for="n in firstDay" :key="'empty-' + n" class="calendar-day"></div>
          <div
          class="calendar-day"
            v-for="d in daysInMonth"
            :key="d"
            :class="{
              'active-day': isSelected(d),
              today: isToday(d),
            }"
            @click="selectDate(d)"
          >
            {{ d }}
          </div>
        </div>
        <div style="padding: 20px"></div>
        <button class="close" @click="$emit('close')">取消</button>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, watch } from "vue";
  
  const props = defineProps({
    initialDate: {
      type: Date,
      default: () => new Date(),
    },
    selectedDate: Date,
  });
  
  const emit = defineEmits(["close", "select-date"]);
  
  const weekLabels = ["日", "一", "二", "三", "四", "五", "六"];
  const today = new Date();
  const currentYear = ref(props.initialDate.getFullYear());
  const currentMonth = ref(props.initialDate.getMonth());
  const years = Array.from({ length: 2100 - 1970 + 1 }, (_, i) => i + 1970);
  const monthLabels = Array.from({ length: 12 }, (_, i) => `${i + 1}月`);
  
  // 计算当前年份在years数组中的索引
  const yearIndex = computed(() => years.indexOf(currentYear.value));
  
  const firstDay = computed(() => 
    new Date(currentYear.value, currentMonth.value, 1).getDay()
  );
  
  const daysInMonth = computed(() =>
    new Date(currentYear.value, currentMonth.value + 1, 0).getDate()
  );
  
  const onYearChange = (e) => {
    currentYear.value = years[e.detail.value];
  };
  
  const onMonthChange = (e) => {
    currentMonth.value = e.detail.value;
  };
  
  // 其余方法保持不变...
  const changeYear = (delta) => {
    currentYear.value += delta;
  };
  
  const isSelected = (d) => {
    if (!props.selectedDate) return false;
    return (
      props.selectedDate.getFullYear() === currentYear.value &&
      props.selectedDate.getMonth() === currentMonth.value &&
      props.selectedDate.getDate() === d
    );
  };
  
  const selectDate = (d) => {
    const selectedDate = new Date(currentYear.value, currentMonth.value, d);
    const dayOfWeek = selectedDate.getDay();
    const weekArray = [];
    
    for (let i = 0; i < 7; i++) {
      const offset = i - dayOfWeek;
      const date = new Date(selectedDate);
      date.setDate(selectedDate.getDate() + offset);
      weekArray.push({
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        date: date.getDate(),
      });
    }
  
    emit("select-date", weekArray, dayOfWeek);
    emit("close");
  };
  
  const isToday = (d) => {
    const date = new Date(currentYear.value, currentMonth.value, d);
    return (
      date.getFullYear() === today.getFullYear() &&
      date.getMonth() === today.getMonth() &&
      date.getDate() === today.getDate()
    );
  };
  </script>
  <style scoped>
.popup-overlay {
  position: fixed;
  top: 33%;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.popup {
  background: white;
  padding: 20px;
  border-radius: 10px;
  position: relative;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  text-align: center;
}

.calendar-grid .calendar-day {
  padding: 11px;
  background: #f0f0f0;
  border-radius: 19px;
  cursor: pointer;
  font-size: 12.8px;
}

.calendar-grid .calendar-day:hover {
  background: #9effd7;
}

.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 14px;
}
.time-btn{
    width: 30px;
}
button,.time-btn {
  cursor: pointer;
  border: 0px !important;
  font-size: 26px !important;
}

.btn-r,
.btn-l,
.close {
  background: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  line-height: 32px;
  font-size: 14px !important;
}

.close {
  background: #f0f0f0;
  border-radius: 4px;
  position: absolute;
  right: 20px;
  bottom: 4.8px;
  font-size: 14px;
  color: #4589ff;
  width: 66px;
}

select {
  width: 70.4px;
  height: 32px;
  border-radius: 12.8px;
  font-size: 16px;
  outline: none;
  text-align: center;
}
.active-day {
  background-color: #1890ff !important;
  color: white;
}
.today {
  background-color: #d9e8fa !important; /* 浅青色背景 */
}
.justify-center {
  justify-content: center;
}
  </style>
  