import { usePrincipalStore } from '@/store/principal';
import type { Directive, DirectiveBinding } from 'vue';

export const hasAnyAuthority: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding<string>) {
    const targetCode = binding.value;
    const store = usePrincipalStore();
    if (targetCode) {
      if (store.account?.username != targetCode) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    }
  },
};
