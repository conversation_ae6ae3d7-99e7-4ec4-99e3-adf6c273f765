/**
 * 消息提示
 */
export default function useMessage() {
  const showMessage = (title: string, icon: 'success' | 'loading' | 'error' | 'none' | 'fail' | 'exception') => {
    uni.showToast({
      title,
      icon,
      duration: 1500, // 默认延迟 1500毫秒
      mask: true,
    });
  };
  const error = (title: string) => showMessage(title, 'error');
  const success = (title: string) => showMessage(title, 'success');
  return {
    error,
    success,
  };
}
