import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import postcsspxtorem from "postcss-pxtorem";
import path from "path";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import { fileURLToPath, URL } from "node:url";
import viteCompression from "vite-plugin-compression";
import strip from "@rollup/plugin-strip";
import imagemin from "vite-plugin-imagemin";
import { visualizer } from "rollup-plugin-visualizer";
const isProd = process.env.NODE_ENV === "production";

// CJS 默认导出挂在 default
export default defineConfig({
  cacheDir: "node_modules/.vite_cache",
  define: {
    // https://github.com/vuejs/core/tree/main/packages/vue#bundler-build-feature-flags
    // fix: Feature flag VUE_PROD_HYDRATION_MISMATCH_DETAILS is not explicitly defined; vite4需显式声明
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false,
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
  },
  plugins: [
    vue(),
    createSvgIconsPlugin({
      iconDirs: [path.resolve(__dirname, "src/assets/icons")],
      symbolId: "icon-[dir]-[name]",
      svgoOptions: true,
    }),
    isProd &&
      viteCompression({
        verbose: true, // 控制台输出压缩结果
        disable: false, // 开启压缩
        threshold: 10240, // 超过 10kb 的文件才压缩
        algorithm: "gzip", // 使用 gzip 压缩
        ext: ".gz", // 压缩文件扩展名
      }),
    isProd &&
      strip({
        include: ["**/*.(js|ts|vue)"],
        functions: ["console.log", "console.debug", "console.info"],
      }),
    isProd &&
      imagemin({
        gifsicle: { optimizationLevel: 7 },
        mozjpeg: { quality: 70 },
        webp: { quality: 75 },
        svgo: {
          plugins: [
            {
              name: "removeViewBox",
            },
            {
              name: "removeEmptyAttrs",
              active: false,
            },
          ],
        },
      }),
    visualizer({ open: true, filename: "stats.html" }),
  ].filter(Boolean),
  css: {
    postcss: {
      plugins: [
        postcsspxtorem({
          rootValue: 32, // 设计稿宽度(320) / 10
          propList: ["*"], // 转换所有属性
          selectorBlackList: [".no-rem"], // 忽略 .no-rem 类
          minPixelValue: 2, // 小于等于 2px 不转换
          unitPrecision: 5, // rem 小数位精度
        }),
      ],
    },
  },
  envPrefix: "APP_",

  resolve: {
    alias: [
      { find: /^lodash-es\/(.*)$/, replacement: "lodash/$1" },
      { find: /^lodash-es$/, replacement: "lodash" },
      {
        find: "@",
        replacement: fileURLToPath(new URL("./src", import.meta.url)),
      },
    ],
  },

  build: {
    chunkSizeWarningLimit: 500,
    minify: "esbuild",
    emptyOutDir: true,
    outDir: "../www",
    target: "esnext",
    sourcemap: false, // 生产关掉
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes("virtual:svg-icons-register"))
            return "icons-register";
          if (id.includes("node_modules/lodash/")) return "vendor-lodash";
          if (id.includes("node_modules/naive-ui")) return "vendor-naive";
          if (id.includes("node_modules/vue")) return "vendor-vue";
          if (id.includes("node_modules")) return "vendor";
        },
      },
    },
  },
  server: {
    host: "0.0.0.0", // 绑定到所有网卡，局域网/外网都能访问
    port: 5713, // 指定端口
    strictPort: true, // 如果端口被占用则直接报错，不自动换端口
    open: false, // 启动后是否自动打开浏览器
    proxy: {
      "/api": {
        target: "http://************:8045",
        changeOrigin: true,
      },
    },
  },
});
