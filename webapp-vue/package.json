{"name": "webapp-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"starttest": "npm run dev", "dev": "vite --mode development", "gitupdate": "git pull origin", "build": "vue-tsc -b && vite build --mode production", "前端生产模式打包": "", "fprod": " npm run gitupdate && npm run build", "preview": "vite preview"}, "dependencies": {"axios": "^1.8.3", "fast-glob": "^3.3.3", "lib-flexible": "^0.3.2", "nprogress": "^0.2.0", "pinia": "^3.0.1", "showdown": "^2.1.0", "sm-crypto": "^0.3.13", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@rollup/plugin-strip": "^3.0.4", "@types/node": "^16.18.59", "@types/nprogress": "^0.2.3", "@types/postcss-pxtorem": "^6.1.0", "@types/showdown": "^2.0.6", "@types/sm-crypto": "^0.3.4", "@vitejs/plugin-vue": "^4.3.4", "@vue/tsconfig": "^0.7.0", "naive-ui": "^2.41.0", "postcss-pxtorem": "^6.1.0", "rollup-plugin-visualizer": "^5.14.0", "typescript": "~5.7.2", "vite": "^4.5.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.2.8"}}