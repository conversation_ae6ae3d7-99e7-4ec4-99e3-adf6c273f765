.home-ser {
  opacity: 1;
  border-radius: 0px;
  background: #fdfdfd;
  box-shadow: 0px 0px 4px 0px #92929233;
  width: 100%;
  height: 101px;
  padding: 12px 15px;
}

.home-ser .home-ser-t {
  color: #333333;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 14px;
  display: block;
}

.home-ser .home-ser-div::-webkit-scrollbar {
  display: none;
}

.home-ser .home-ser-div div {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 42px;
  height: 42px;
  margin-right: 15px;
  flex-shrink: 0;
}

.home-ser .home-ser-div span {
  font-size: 10px;
  white-space: nowrap;
}

.home-ser-card {
  width: 100%;
  /* height: calc(100vh - 239px); */
  padding: 10px 17px;
}

.home-ser-card .ser-card {
  padding: 12px 15px;
  height: 101px;
  opacity: 1;
  border-radius: 10px;
  background: linear-gradient(180deg, #e7f3ff 0%, #ffffff 100%);
  border: 1px solid #ffffff;
  box-shadow: 0px 0px 4px 0px #92929233;
  margin-bottom: 9px;
}

.home-ser-card .ser-card .home-ser-t {
  color: #333333;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 14px;
  display: block;
}

.home-ser-card .ser-card .card-item {
  margin-right: 5px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  width: 70px;
}

.home-ser-card .ser-card .card-item i {
  display: block;
}

.home-ser-card .ser-card .card-item span {
  display: block;
  width: 100%;
  text-align: center;
  font-size: 12px;
}

.home-ser-div {
  width: 100%;
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  padding-bottom: 10px;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
}
