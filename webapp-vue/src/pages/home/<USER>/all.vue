<template>
    <div>
      <!-- 顶部返回按钮（如果需要） -->
      <!-- <div class="base-top">
        <i id="goBack" @click="goBack"></i>
        <span>全部</span>
        <span></span>
      </div> -->
  
      <!-- 首页服务 -->
      <div class="home-ser">
        <span class="home-ser-t">首页服务</span>
        <div class="home-ser-div">
          <div v-for="(item, index) in mainServices" :key="index" @click="skipHtml(item.label)">
            <svg class="icon-27">
              <use :xlink:href="`#${item.icon}`"></use>
            </svg>
            <span>{{ item.label }}</span>
          </div>
        </div>
      </div>
  
      <!-- 服务卡片 -->
      <div class="home-ser-card">
        <div class="ser-card" v-for="(card, index) in serviceCards" :key="index">
          <span class="home-ser-t">{{ card.title }}</span>
          <div class="home-ser-div">
            <div class="card-item" v-for="(item, idx) in card.items" :key="idx" @click="skipHtml(item.label)">
              <svg class="icon-27">
                <use v-if="item.icon" :xlink:href="`#${item.icon}`"></use>
              </svg>
              <span>{{ item.label }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { useRouter } from 'vue-router'
  
  const router = useRouter()
  
  const skipHtml = (value) => {
    if(value == '会议助手'){
      router.push('/meetSchedule') 
    }else if(value == '挪车服务'){
      router.push('/car-move') 
    }
  }
  
  // 首页服务数据
  const mainServices = [
    { icon: 'icon-home-通勤服务', label: '通勤服务' },
    { icon: 'icon-home-会议管理', label: '会议助手' },
    { icon: 'icon-home-用餐服务', label: '用餐服务' },
    { icon: 'icon-home-物业服务', label: '物业服务' },
    { icon: 'icon-home-理发', label: '理发预约' },
    { icon: 'icon-home-安全', label: '安全管理' },
    { icon: 'icon-home-固定资产', label: '固定资产管理' }
  ]
  
  // 卡片服务数据
  const serviceCards = [
    {
      title: '办公服务',
      items: [
        { icon: 'icon-home-会议管理', label: '会议助手' },
        { icon: 'icon-home-固定资产', label: '固定资产管理' },
        { icon: 'icon-home-人事管理', label: '人事管理' },
        { icon: '', label: '' }
      ]
    },
    {
      title: '出行服务',
      items: [
        { icon: 'icon-home-通勤服务', label: '通勤服务' },
        { icon: 'icon-home-派车', label: '派车管理' },
        { icon: 'icon-home-挪车', label: '挪车服务' },
        { icon: '', label: '' }
      ]
    },
    {
      title: '生活服务',
      items: [
        { icon: 'icon-home-用餐服务', label: '用餐服务' },
        { icon: 'icon-home-外卖服务', label: '外卖服务' },
        { icon: 'icon-home-理发', label: '理发预约' },
        { icon: 'icon-home-日用品', label: '日用品库' }
      ]
    },
    {
      title: '更多服务',
      items: [
        { icon: 'icon-home-物业服务', label: '物业服务' },
        { icon: 'icon-home-安全', label: '安全管理' },
        { icon: 'icon-home-消息推送1', label: '消息推送' },
        { icon: '', label: '' }
      ]
    }
  ]
  </script>
  
  <style scoped >
    @import "./all.css";
  </style>
  