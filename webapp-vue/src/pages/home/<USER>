<template>
  <!-- 启动页 -->
  <!-- <div v-if="showStartPage" class="start">
    <div @click="openHome">手机号快捷登录</div>
  </div> -->
  <div class="home-vessel">
    <div class="bg"></div>
    <div class="home-function">
      <!-- 搜索栏 -->
      <div class="search">
        <svg class="icon-16 search-icon">
          <use xlink:href="#icon-meeting-14-3"></use>
        </svg>
        <input type="text" v-model="searchText" placeholder="一站式公务派车" />
        <span>搜索</span>
      </div>

      <!-- 主要功能区 -->
      <div class="main-function">
        <div class="one-row">
          <div v-for="item in mainFunctionRow1" :key="item.name" @click="handleMainClick(item.name)">
            <svg class="icon-27">
              <use :xlink:href="`#icon-home-${item.icon}`" />
            </svg>
            <span>{{ item.name }}</span>
          </div>
        </div>
        <div class="two-row">
          <div v-for="item in mainFunctionRow2" :key="item.name" @click="handleMainClick(item.name)">
            <svg class="icon-27">
              <use :xlink:href="`#icon-home-${item.icon}`" />
            </svg>
            <span>{{ item.name }}</span>
          </div>
        </div>
      </div>

      <!-- 热门服务 / 我的常用 -->
      <div class="cut-function" :class="{ 'frequent-bg': currentTab === 'frequent' }">
        <div class="cut-oneRow">
          <span class="tab-item" :class="{ active: currentTab === 'popular' }" data-tab="popular"
            @click="switchTab('popular')">热门服务</span>
          <span class="tab-item" :class="{ active: currentTab === 'frequent' }" data-tab="frequent"
            @click="switchTab('frequent')">我的常用</span>
        </div>
        <div class="cut-twoRow">
          <div v-for="(row, rowIndex) in tabData[currentTab]" :key="rowIndex"
            :class="`cut-${rowIndex === 0 ? 'one' : 'two'}`">
            <div v-for="item in row" :key="item.name" @click="handleTabClick(item.name)">
              <svg class="icon-20">
                <use :xlink:href="`#icon-home-${item.iconClass}`" />
              </svg>
              <span style="margin-left: 6px">{{ item.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="home-footer">
      <div :class="{ active: currentFooter === 0 }" @click="handleFooterClick(0)">
        <i></i>
        <span>工作台</span>
      </div>
      <div :class="{ active: currentFooter === 1 }" @click="handleFooterClick(1)">
        <i></i>
        <span>AI</span>
      </div>
      <div :class="{ active: currentFooter === 2 }" @click="handleFooterClick(2)">
        <i></i>
        <span>我的</span>
      </div>
    </div>
  </div>
  <NoticeWarn />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import NoticeWarn from '@/pages/car-move/notice/NoticeWarn.vue'
// const showStartPage = ref(false)
const router = useRouter()
const searchText = ref('一站式公务派车')
const currentTab = ref<'popular' | 'frequent'>('popular')
const currentFooter = ref(0)
// const showNoticeModal = ref(true)

const tabData = {
  popular: [
    [
      { iconClass: '挪车1', name: '挪车服务' },
      { iconClass: '会议管理1', name: '会议助手' }
    ],
    [
      { iconClass: '用餐服务1', name: '用餐服务' },
      { iconClass: '物业服务1', name: '物业服务' }
    ]
  ],
  frequent: [
    [
      { iconClass: '派车1', name: '派车管理' },
      { iconClass: '通勤服务', name: '通勤服务' }
    ],
    [
      { iconClass: '消息推送1', name: '消息推送' },
      { iconClass: '人事管理1', name: '人事管理' }
    ]
  ]
}

const mainFunctionRow1 = [
  { icon: '通勤服务', name: '通勤服务' },
  { icon: '会议管理', name: '会议助手' },
  { icon: '用餐服务', name: '用餐服务' },
  { icon: '物业服务', name: '物业服务' }
]

const mainFunctionRow2 = [
  { icon: '理发', name: '理发预约' },
  { icon: '安全', name: '安全管理' },
  { icon: '固定资产', name: '固定资产管理' },
  { icon: '全部', name: '全部' }
]
// onMounted(() => {
//   const hasOpened = localStorage.getItem('hasOpenedHome')
//   if (hasOpened === 'true') {
//     showStartPage.value = false
//   }
// })

// function openHome() {
//   showStartPage.value = false
//   localStorage.setItem('hasOpenedHome', 'true')
// }
function switchTab(type: 'popular' | 'frequent') {
  currentTab.value = type
}

function handleTabClick(name: string) {
  if (name === '挪车服务') {
    // Do something
    router.push({
      path: '/car-move'
    })
  } else if (name === '会议助手') {
    router.push('/meetSchedule');
  }
}

function handleMainClick(name: string) {
  if (name === '会议助手') {
    router.push('/meetSchedule');
  } else if (name === '全部') {
    router.push('/all')
  }
}

function handleFooterClick(index: number) {
  currentFooter.value = index
  switch (index) {
    case 0:
      console.log('工作台被点击')
      break
    case 1:
      console.log('AI被点击')
      router.push('/ai');
      break
    case 2:
      console.log('我的被点击')
      break
  }
}
</script>
<style scoped>
@import "./home.css";
</style>
