body {
  padding: 0px;
}



.home-vessel {
  position: relative;
  height: 100vh;
}

.home-vessel .bg {
  width: 100%;
  height: 310px;
  background: url("@/assets/icons/home/<USER>") center no-repeat;
  background-size: cover;
}

.home-vessel .home-function {
  position: absolute;
  top: 143px;
  width: 100%;
  padding: 0 15px;
  height: 369px;
}

.home-vessel .home-function .search {
  display: flex;
  position: relative;
}
.home-vessel .home-function .search .search-icon {
  left: 13px;
  top: 10px;
  z-index: 10;
  position: absolute;
}


.home-vessel .home-function .search input {
  width: 100%;
  height: 36px;
  opacity: 1;
  border-radius: 20px;
  background: #2034497f;
  border: 1px solid #ffffff;
  backdrop-filter: blur(10px);
  color: #ffffff;
  outline: none;
  padding: 0 34px;
  margin-bottom: 8px;
  font-size: 14px;
}

.home-vessel .home-function .search span {
  position: absolute;
  right: 8px;
  width: 35px;
  height: 17px;
  line-height: 17px;
  border-left: 2px solid #9c9687;
  text-align: center;
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  top: 8px;
}

.home-vessel .home-function .main-function {
  padding: 15px;
  width: 100%;
  height: 146px;
  opacity: 1;
  border-radius: 10px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px #92929219;
  font-size: 12px;
  color: #3b465d;
  margin-bottom: 10px;
}

.home-vessel .home-function .main-function .one-row,
.home-vessel .home-function .main-function .two-row {
  display: flex;
  justify-content: space-between;
}

.home-vessel .home-function .main-function .one-row div,
.home-vessel .home-function .main-function .two-row div {
  min-width: 48px;
  height: 47px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.home-vessel .home-function .main-function .one-row div i,
.home-vessel .home-function .main-function .two-row div i {
  display: block;
  width: 27px;
  height: 27px;
  background-size: cover;
}

.home-vessel .home-function .main-function .one-row div span,
.home-vessel .home-function .main-function .two-row div span {
  display: block;
  font-size: 11px;
  min-width: 47px;
  text-align: center;
}

.home-vessel .home-function .main-function .one-row {
  margin-bottom: 23px;
}

.home-vessel .cut-function {
  width: 100%;
  height: 169px;
  background-image: url("@/assets/icons/home/<USER>");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 110% 113%;
  margin-bottom: 10px;
}

.home-vessel .cut-function.frequent-bg {
  background-image: url("@/assets/icons/home/<USER>");
}

.home-vessel .cut-function .cut-oneRow {
  width: 100%;
  height: 38px;
  padding: 10px 15px;
  display: flex;
}

.home-vessel .cut-function .cut-oneRow .tab-item {
  font-size: 14px;
  color: #3b465dcc;
  position: relative;
  cursor: pointer;
}

.home-vessel .cut-function .cut-oneRow .tab-item:nth-child(1) {
  width: 74px;
  text-align: center;
  line-height: 28px;
  height: 28px;
}

.home-vessel .cut-function .cut-oneRow .tab-item:nth-child(2) {
  line-height: 28px;
  height: 28px;
  width: 185px;
  text-align: left;
  margin-left: 42px;
  /* padding-left: 60px; */
}

.home-vessel .cut-function .cut-oneRow .tab-item.active {
  font-weight: bold;
  color: #3b465d;
}

.home-vessel .cut-function .cut-oneRow .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: #005ac5;
}
.home-vessel .cut-function .cut-oneRow .tab-item:nth-child(2).active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 17%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: #005ac5;
}

.home-vessel .cut-function .cut-twoRow {
  width: 100%;
  height: 123px;
  margin-top: 4px;
  padding: 0 15px;
}

.home-vessel .cut-function .cut-twoRow .cut-one,
.home-vessel .cut-function .cut-twoRow .cut-two {
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.home-vessel .cut-function .cut-twoRow .cut-one div,
.home-vessel .cut-function .cut-twoRow .cut-two div {
  width: 125px;
  height: 100%;
  opacity: 1;
  border-radius: 8px;
  background: #f6f8fd;
  border: 1px solid #2e67e20c;
  box-shadow: 0px 0px 5px 0px #e0e2e84c;
  display: flex;
  padding: 12px;
}

.home-vessel .cut-function .cut-twoRow .cut-one div i,
.home-vessel .cut-function .cut-twoRow .cut-two div i {
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
  background-repeat: no-repeat;
  margin-right: 6px;
}

.home-vessel .cut-function .cut-twoRow .cut-one div span,
.home-vessel .cut-function .cut-twoRow .cut-two div span {
  color: #3b465d;
  font-size: 12px;
}

.home-vessel .home-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  height: 49px;
  background: #ffffffef;
  backdrop-filter: blur(20px);
  border-radius: 10px 10px 0 0;
  box-shadow: 0 -0.5px 6px 0 #5d5d5d33;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.home-vessel .home-footer div {
  width: 57px;
  height: 41px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.home-vessel .home-footer div i {
  display: block;
  width: 22px;
  height: 22px;
  position: absolute;
  top: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.home-vessel .home-footer div span {
  color: #666;
  font-size: 10px;
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: 0;
}

.home-vessel .home-footer div.active span {
  color: #005ac5;
}

.home-vessel .home-footer div.active i {
  width: 48px;
  height: 48px;
  top: -17px;
}

/* 默认图标 */
.home-vessel .home-footer div:nth-child(1) i {
  background-image: url("@/assets/icons/home/<USER>");
}

.home-vessel .home-footer div:nth-child(2) i {
  background-image: url("@/assets/icons/home/<USER>");
}

.home-vessel .home-footer div:nth-child(3) i {
  background-image: url("@/assets/icons/home/<USER>");
}

/* 激活状态图标 */
.home-vessel .home-footer div:nth-child(1).active i {
  background-image: url("@/assets/icons/home/<USER>");
}

.home-vessel .home-footer div:nth-child(2).active i {
  background-image: url("@/assets/icons/home/<USER>");
}

.home-vessel .home-footer div:nth-child(3).active i {
  background-image: url("@/assets/icons/home/<USER>");
}
