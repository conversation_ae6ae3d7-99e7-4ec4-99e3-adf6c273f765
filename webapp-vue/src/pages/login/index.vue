<template>
    <div class="login-start">
        <div v-if="resLogin" class="reslogin">
            <i :class="resLogin == 1 ? 'sucess' : 'fail'"></i><span>{{ resLogin === 1 ? '登录成功！' : resLogin === 2 ? '登录失败！' : resLogin
                === 3 ? '非内部人员！' : '' }}</span>
        </div>
        <!-- <a href="">手机号快捷登录</a> -->
        <div class="login-input">
            <input type="text" maxlength="11" oninput="this.value = this.value.replace(/[^0-9]/g, '').slice(0, 11);"
                inputmode="numeric" v-model="phone" placeholder="请输入手机号" />
            <i v-if="phone && !flagLogin" class="clear" @click="onClearPhone"></i>
        </div>
        <div :class="flagLogin ? 'login ing' : 'login'" @click="login">{{ flagLogin ? '正在登录...' : '登录' }}</div>
    </div>
</template>
<script setup lang="ts">
import { authorization } from '@/service/login.service';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router'
let phone = ref('');
const router = useRouter();
/**结果--登录 */
let resLogin = ref(0);
/**状态--登录 */
let flagLogin = ref(false);
/**登录 */
async function login() {
    let username = phone.value || '';
    phone.value = username = username.replace(/[^0-9]/g, '').slice(0, 11);
    if (!username) return;
    flagLogin.value = true;
    await authorization(username).then((res) => {
        resLogin.value = 1;
        sessionStorage.setItem('user', res.id)
        router.push('/')
    }).catch(e => {
        if (e === '用户名或密码错误!') {
            resLogin.value = 2
        } else if (e === '非内部人员！') {
            resLogin.value = 3
        }
    }).finally(() => {
        flagLogin.value = false;
        setTimeout(() => {
            resLogin.value = 0
        }, 2000);
    });
}
/**清空 */
function onClearPhone() {
    phone.value = '';
}

onMounted(() => {

})

</script>
<style lang="css" scoped>
.login-start {
    background: url("@/assets/icons/home/<USER>") center no-repeat;
    background-size: cover;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.login-start .reslogin {
    min-width: 90px;
    height: 25px;
    border-radius: 3px;
    background: #FFF;
    color: #333;
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translate(-50%, 0);
    display: flex;
    justify-content: space-between;
    padding: 0px 11px;
    align-items: center;
}

.reslogin span {
    font-size: 10px;
    margin-left: 5px;
}

i {
    width: 10px;
    height: 10px;
    display: inline-block;
}

.reslogin .sucess {
    background: url("@/assets/icons/login/成功.svg") center no-repeat;
    background-size: cover;
}

.reslogin .fail {
    background: url("@/assets/icons/login/错误.svg") center no-repeat;
    background-size: cover;
}

.clear {
    background: url("@/assets/icons/login/删除.svg") center no-repeat;
    background-size: contain;
    position: absolute;
    width: 16px;
    height: 16px;
    right: 20px;
    top: 12px;
}

.login-input {
    display: flex;
    align-items: center;
    position: relative;
}

input {
    font-size: 14px;
    width: 260px;
    height: 40px;
    opacity: 1;
    border-radius: 20px;
    background: #33333399;
    color: #fff;
    outline: none;
    margin-bottom: 25px;
    border: none;
    padding: 0px 1em;
}

input::placeholder {
    color: #FFFFFF99;
}

.login-start .login {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 260px;
    height: 40px;
    opacity: 1;
    border-radius: 20px;
    background: linear-gradient(270deg, #475fff 0%, #0bcbff 100%);
    color: #ffffff;
    font-size: 14px;
}

.login.ing {
    background: linear-gradient(270deg, #328EFF 0%, #0BE7FF 100%);
}
</style>
