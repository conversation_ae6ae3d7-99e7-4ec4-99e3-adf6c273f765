import type { Meetingroom, Personnel } from '@/models/MettingHandle';
import type { PersonnelAndOrganization2 } from '@/models/Personnel';
import { uuidv4 } from '@/utils/uuid';

export const RoomData = [
  // {
  //   title: '5F',
  //   // 坐标范围
  //   topLeftX: 85.6875,
  //   topLeftY: 82,
  //   topRightX: 115.6875,
  //   topRightY: 83,
  //   bottomLeftX: 85.6875,
  //   bottomLeftY: 141,
  //   bottomRightX: 115.6875,
  //   bottomRightY: 141,
  // },
  {
    title: '5F',
    children: [
      {
        title: '5F 507洽谈室',
        // 坐标范围
        topLeftX: 175.4375,
        topLeftY: 175.59375,
        topRightX: 210.4375,
        topRightY: 175.59375,
        bottomLeftX: 179.4375,
        bottomLeftY: 244.59375,
        bottomRightX: 210.4375,
        bottomRightY: 246.59375,
      },
      {
        title: '5F 504/505会议室',
        // 坐标范围
        topLeftX: 210.4375,
        topLeftY: 176.59375,
        topRightX: 271.4375,
        topRightY: 176.59375,
        bottomLeftX: 210.4375,
        bottomLeftY: 248.59375,
        bottomRightX: 270.4375,
        bottomRightY: 246.59375,
      },
    ],
    // 坐标范围
  },
];
/** 会议服务options */
// value 对应传给后端字段
export const MeetingServiceOptions = [
  {
    title: '用餐',
    checked: false,
    key: 'useFood',
    // value 对应传给后端字段
    value: 0,
    children: [
      {
        title: '人数',
        key: 'personNum',
        unit: '份',
        type: 'select',
        options: Array.from({ length: 30 }, (_, index) => {
          return {
            label: `${index + 1}人`,
            value: index + 1,
          };
        }),
      },
      { title: '时间', type: 'input', key: 'time' },
    ],
  },
  {
    title: '桌牌',
    checked: false,
    key: 'tableCard',
    value: 1,
    children: [
      {
        title: '数量',
        key: 'num',
        unit: '个',
        type: 'select',
        options: Array.from({ length: 30 }, (_, index) => {
          return {
            label: `${index + 1}人`,
            value: index + 1,
          };
        }),
      },
      { title: '内容', type: 'input', key: 'content' },
    ],
  },
  {
    title: '纸笔',
    checked: false,
    key: 'pencil',
    value: 2,
    children: [
      {
        title: '数量',
        type: 'select',
        unit: '份',
        key: 'num',
        options: Array.from({ length: 30 }, (_, index) => {
          return {
            label: `${index + 1}人`,
            value: index + 1,
          };
        }),
      },
    ],
  },
  {
    title: '茶水',
    checked: false,
    key: 'teaWater',
    value: 3,
    children: [
      {
        title: '数量',
        type: 'select',
        unit: '份',
        key: 'num',
        options: Array.from({ length: 30 }, (_, index) => {
          return {
            label: `${index + 1}人`,
            value: index + 1,
          };
        }),
      },
    ],
  },
  {
    title: '摄影',
    checked: false,
    key: 'fruitPlate',
    value: 4,
    // children: [
    //   {
    //     title: '数量',
    //     type: 'select',
    //     unit: '份',
    //     key: 'num',
    //     options: Array.from({ length: 30 }, (_, index) => {
    //       return {
    //         label: `${index + 1}人`,
    //         value: index + 1,
    //       };
    //     }),
    //   },
    // ],
  },
];
export const MeetingRoomNameMap = new Map([
  ['507', '5F507洽谈室'],
  ['5045', '5F 504/505会议室'],
  ['806', '8F806会议室'],
  ['902', '9F902会议室'],
  ['1301', '13F1301会议室'],
  ['130234', '13F 1302/1303/1304会议室'],
]);
/** 806会议室 504/505会议室 1301会议室 1302/1303/1304会议室 902会议室 507洽谈室 */
export const MeetingRoomNameMapForService = new Map([
  ['507洽谈室', '507'],
  ['504/505会议室', '5045'],
  ['806会议室', '806'],
  ['902会议室', '902'],
  ['1301会议室', '1301'],
  ['1302/1303/1304会议室', '130234'],
]);
/** 会议室已有平面图楼层 */
export type MeetingRoomFloor = 5 | 8 | 9 | 13 | null;
export interface SelectedMeetingRoom {
  id?: any; // 会议室id；因会议室组件里设置了特定的id；所以这里设any
  roomF: MeetingRoomFloor;
  roomName: string;
}
export const generateSelectedMeetingRoom = (roomInfo: Meetingroom): SelectedMeetingRoom => {
  const selectedRoom: SelectedMeetingRoom = {
    id: undefined,
    roomF: null,
    roomName: '',
  };
  selectedRoom.roomF = roomInfo.floor as number as MeetingRoomFloor;
  const resMettingroomName = roomInfo.name || '';
  if (resMettingroomName) {
    let nameStr = '';
    const floor = roomInfo.floor as number as MeetingRoomFloor;
    const names = resMettingroomName.split('/') || [];
    if (floor && floor > 9) {
      nameStr = names[0]?.substring(0, 4);
    } else {
      nameStr = names[0]?.substring(0, 3);
    }
    if (!nameStr) nameStr = resMettingroomName.substring(0, 3);
    if (nameStr == '504') {
      selectedRoom.id = '5045';
    } else if (nameStr == '1302') {
      selectedRoom.id = '130234';
    } else {
      selectedRoom.id = nameStr;
    }
    selectedRoom.roomName = MeetingRoomNameMap.get(selectedRoom.id) || roomInfo.name || '';
  }
  return selectedRoom;
};

/** 含 0 组织机构、1工作组、2外部人员的集合 */
export interface PersonnelAndOrganizationGroup {
  name: string;
  /** 0组织机构 1工作组 2外部人员 */
  _type: 0 | 1 | 2;
  id: string;
  _init?: Personnel;
  _uuid?: string;
  _open?: boolean;
  _isChecked?: boolean;
  children?: PersonnelAndOrganizationGroup[];
}
/** 拼接人员List */
export const generatePersonnelList = (data: PersonnelAndOrganization2): PersonnelAndOrganizationGroup[] => {
  if (data == null) return [];
  const { workgroupGroupList = [], organizationGroupList = [], externalPersonnelList = [] } = data || {};
  const organiArr = organizationGroupList
    ?.filter(item => item.members?.length)
    ?.map(item => {
      return <PersonnelAndOrganizationGroup>{
        name: item.organizationName,
        id: uuidv4(), // 上级没id
        _uuid: uuidv4(),
        _init: item,
        _open: true,
        _isChecked: false,
        _type: 0,
        children: item.members?.map(member => {
          return {
            name: member.name,
            id: member.id,
            _uuid: uuidv4(),
            _init: member,
            _open: true,
            _isChecked: false,
          };
        }),
      };
    });
  const workArr = workgroupGroupList
    ?.filter(item => item.members?.length)
    ?.map(item => {
      return <PersonnelAndOrganizationGroup>{
        name: item.workgroupName,
        id: uuidv4(), // 上级没id
        _uuid: uuidv4(),
        _init: item,
        _open: true,
        _isChecked: false,
        _type: 1,
        children: item.members?.map(member => {
          return {
            name: member.name,
            id: member.id,
            _uuid: uuidv4(),
            _init: member,
            _open: true,
            _isChecked: false,
          };
        }),
      };
    });
  let externalData: PersonnelAndOrganizationGroup | null = null;
  const externalChildren = externalPersonnelList?.map(item => {
    return <PersonnelAndOrganizationGroup>{
      name: item.name,
      id: item.id,
      _uuid: uuidv4(),
      _init: item,
      _open: true,
      _isChecked: false,
      _type: 2,
    };
  });
  if (externalChildren?.length) {
    externalData = {
      name: '外部成员',
      id: uuidv4(), // 上级没id
      _uuid: uuidv4(),
      _open: true,
      _isChecked: false,
      _type: 2,
      children: externalChildren,
    };
  }
  return externalData == null ? [...organiArr, ...workArr] : [...organiArr, ...workArr, externalData];
};
