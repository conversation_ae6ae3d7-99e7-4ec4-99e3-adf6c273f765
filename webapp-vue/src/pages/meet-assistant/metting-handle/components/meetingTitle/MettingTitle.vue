<template>
    <div class="inner-container">
        <div class="input-c">
            <span :class="{ require: isRequire }">{{ title }}</span>
            <textarea id="content" v-model="content" :placeholder="'请输入' + title"></textarea>
        </div>
        <div class="base-btn-footer">
            <n-button type="primary" @click="save">
                完成
            </n-button>
            <n-button @click="cancel">
                取消
            </n-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useMettingHandleStore } from '@/stores/mettingHandle';
import { NButton } from 'naive-ui';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
const { title = '会议名称', isRequire = false } = defineProps<{ title: string, isRequire: boolean }>()
const router = useRouter()
const content = ref('')
// 计算属性优化条件判断
const targetKey = computed(() => {
    switch (title) {
        case '会议主题': return 'title';
        case '会议内容': return 'content';
        default: return null; // 处理未知情况
    }
});
const store = useMettingHandleStore()
onMounted(() => {
    const key = targetKey.value;
    content.value = store.mettingHandle[key as 'title' | 'content'] || '';
})
function save() {
    const key = targetKey.value;
    if (content.value) {
        // 类型断言确保类型安全
        store.mettingHandle[key as 'title' | 'content'] = content.value;
        router.back()
        console.log("🚀 ~ save ~ store.mettingHandle:", store.mettingHandle)
    }
}
function cancel() {
    router.back()
    // const key = targetKey.value;
    // content.value = store.mettingHandle[key as 'title' | 'content'] || '';
}
</script>

<style scoped>
@import "@/pages/meet-assistant/metting-handle/styles/metting-base.css";

.input-c {
    flex: 1;
}

.input-c textarea {
    margin-top: 10px;
    width: 100%;
    display: block;
    outline: none;
    border: 1px solid rgba(153, 153, 153, 0.2); /* Converted #99999933 to rgba */
    min-height: 163px;
    border-radius: 5px;
    padding: 8px;
    color: black;
    font-size: 14px;
}
.input-c span{
    font-size: 14px;
}
.require {
    position: relative;
    padding-left: 2px;
}

.require::before {
    content: '*';
    position: absolute;
    color: #e50101;
    top: 0;
    left: -7px;
}
/* 底部通知按钮 */
.base-btn-footer {
    margin-bottom: 10px;
    height: 40px;
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 20px;
}
.n-button {
  width: 140px;
  height: 41px;
}
</style>