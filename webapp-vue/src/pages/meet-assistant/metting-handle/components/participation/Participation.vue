<template>
    <div class="par-c">
        <div class="c-top">
            <span :class="{ active: currentIdx == 1 }" @click="changeIdx(1)">参加{{ countData.join }}</span>
            <span :class="{ active: currentIdx == 2 }" @click="changeIdx(2)">建议延迟{{ countData.delay }}</span>
            <span :class="{ active: currentIdx == 3 }" @click="changeIdx(3)">不参加{{ countData.notJoin }}</span>
            <span :class="{ active: currentIdx == 0 }" @click="changeIdx(0)">无反馈{{ countData.noFeedback }}</span>
        </div>
        <div class="c-vessel" v-for="item in currentList" :key="item.id">
            <!-- <div class="round">{{ item.personnel?.name?.substring(0, 1) }}</div> -->
            <div class="circle circle-large"
                :class="{ 'circle-blue': item.personnel?.type == 0, 'circle-out-icon': item.personnel?.type == 1 }">
                {{ item.personnel?.name?.substring(0, 1) }}</div>
            <div class="content">
                <span class="content-title" style="color: #333;">{{ item.personnel?.name }}</span>
                <div class="content-reason" v-if="item.feedback != 0 && item.feedback != 1">
                    <span style="color: #333;">原因</span>
                    <span style="color: #999;">{{ item.reason }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Meetingpersonnel } from '@/models/MettingHandle';
import meetHandleService from '@/service/meet-assistant/meet-handle/meet-handle.service';
import { onMounted, reactive, ref } from 'vue';
const { id } = defineProps<{ id?: string }>();
const personnelList = ref<Meetingpersonnel[]>([])
// 参会反馈(0-未反馈,1-参加,2-建议延期,3-不参加)
const currentIdx = ref<number | null>(null)
const currentList = ref<Meetingpersonnel[]>()
const countData = reactive({
    // 参加
    join: 0,
    // 延迟
    delay: 0,
    // 不参加
    notJoin: 0,
    // 无反馈
    noFeedback: 0
})
onMounted(() => {
    getInfo()
})
const getInfo = () => {
    if (!id) return
    meetHandleService.getInfoMeeting(id).then(res => {
        console.log("🚀 ~ meetHandleService.getInfoMeeting ~ res:", res)
        personnelList.value = res.meetingpersonnelList || []
        console.log("🚀 ~ meetHandleService.getInfoMeeting ~ personnelList.value:", personnelList.value)
        countData.join = personnelList.value.filter(ele => ele.feedback == 1).length
        countData.delay = personnelList.value.filter(ele => ele.feedback == 2).length
        countData.notJoin = personnelList.value.filter(ele => ele.feedback == 3).length
        countData.noFeedback = personnelList.value.filter(ele => ele.feedback == 0).length
        changeIdx(1)
    })
}
const changeIdx = (idx: number) => {
    if (idx == currentIdx.value) return
    currentIdx.value = idx
    // 参会反馈(0-未反馈,1-参加,2-建议延期,3-不参加)
    currentList.value = personnelList.value.filter(ele => {
        return ele.feedback == currentIdx.value
    })
}
</script>

<style scoped>
.par-c .c-top {
    height: 12.1875vw;
    /* 39px → 39 * 0.3125 = 12.1875vw */
    border-bottom: 0.3125vw solid #99999933;
    /* 1px → 0.3125vw */
    padding: 0 5.3125vw;
    /* 17px → 5.3125vw */
    padding-top: 3.125vw;
    font-size: 14px;
    /* 10px → 3.125vw */
}

.par-c {
    width: 100%;
    background: #ffffff;

}

.round {
    width: 10.625vw;
    height: 10.625vw;
    background: #4f7af6;
    border-radius: 5.3125vw;
    color: #ffffff;
    font-size: 4.375vw;
    text-align: center;
    line-height: 10.625vw;
    margin: 3.125vw 0;
    /* 10px → 3.125vw */
    margin-right: 2.5vw;
    /* 8px → 2.5vw */
}

.c-vessel {
    display: flex;
    align-items: center;
    padding: 12px 5.3125vw;
    position: relative;

}

.c-vessel i {
    position: absolute;
    left: 12.5vw;
    /* 40px → 12.5vw */
    top: 1.5625vw;
    /* 5px → 1.5625vw */
}

.c-top span {
    position: relative;
    cursor: pointer;
    padding-bottom: 1.5625vw;
    /* 5px → 1.5625vw */
    margin-right: 5.625vw;
    /* 18px → 5.625vw */
}

/* 蓝色下划线（默认隐藏） */
.c-top span::after {
    content: "";
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 0;
    height: 0.625vw;
    transform: translateX(-50%);
    /* 2px → 0.625vw */
    background-color: #4f7af6;
    transition: width 0.3s ease;
}

/* 当前选中项的下划线（显示） */
.c-top span.active::after {
    width: 50%;
}

.c-top span.active {
    color: #333333;
    font-weight: bold;
}

.content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.content-reason {
    display: flex;
    align-items: center;
    gap: 4px;
}

.content-title {
    font-size: 14px;
    color: #333;
}

.circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 34px;
    height: 34px;
    border-radius: 50%;
    margin-right: 8px;
    background-color: #999;
    color: #fff;
    font-size: 14px;
}

.circle.circle-blue {
    background-color: #4f7af6;
}

.circle-out-icon {
    position: relative;
}

.circle-out-icon::after {
    position: absolute;
    content: '外';
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    line-height: 16px;
    border: 1px solid #3e75fe;
    border-radius: 50%;
    color: #3e75fe;
    text-align: center;
}
</style>