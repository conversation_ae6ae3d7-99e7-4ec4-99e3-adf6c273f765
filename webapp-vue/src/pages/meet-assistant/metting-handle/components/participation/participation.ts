// import $ from 'jquery';
// export default async function participation() {
//     $(document).ready(function() {
//         $('.c-top span').click(function() {
//           // 移除所有按钮的 active 类
//           $('.c-top span').removeClass('active');
//           // 当前按钮添加 active 类
//           $(this).addClass('active');
//           var fullText = $(this).text().trim(); // 例如 "参与5"
//           // 只取前面中文部分（去掉最后的数字）
//           var matchedText = fullText.replace(/\d+$/, ''); // 去掉结尾数字
//           console.log('你点击的是：' + matchedText); // 输出：参与 / 建议延迟 / 不参与
//           // 控制样式（比如是否参与）
//           if (matchedText === '参与') {
//             $('.par-c').addClass('is-participate');
//           } else {
//             $('.par-c').removeClass('is-participate');
//           }
//         });
//         $('#goBack').on('click',function(){ //返回
//             // window.location.hash = "/meeting-handle.html"
//         })
//       });
// }
