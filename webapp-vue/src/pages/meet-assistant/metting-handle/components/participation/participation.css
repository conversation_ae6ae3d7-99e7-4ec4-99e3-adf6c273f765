.par-c {
  width: 100%;
  background: #ffffff;
}

.par-c .c-top {
  height: 12.1875vw; /* 39px → 39 * 0.3125 = 12.1875vw */
  border-bottom: 0.3125vw solid rgba(153, 153, 153, 0.2); /* 1px → 0.3125vw */
  padding: 0 5.3125vw; /* 17px → 5.3125vw */
  padding-top: 3.125vw; /* 10px → 3.125vw */
}

.par-c .c-vessel {
  display: flex;
  align-items: center;
  padding: 0 5.3125vw; /* 17px → 5.3125vw */
  position: relative;
}

.par-c .c-vessel .round {
  width: 10.625vw;
  height: 10.625vw;
  background: #4f7af6;
  border-radius: 5.3125vw;
  color: #ffffff;
  font-size: 4.375vw;
  text-align: center;
  line-height: 10.625vw;
  margin: 3.125vw 0; /* 10px → 3.125vw */
  margin-right: 2.5vw; /* 8px → 2.5vw */
}

.par-c .c-vessel i {
  position: absolute;
  left: 12.5vw; /* 40px → 12.5vw */
  top: 1.5625vw; /* 5px → 1.5625vw */
}

.c-top span {
  position: relative;
  cursor: pointer;
  padding-bottom: 1.5625vw; /* 5px → 1.5625vw */
  margin-right: 5.625vw; /* 18px → 5.625vw */
}

/* 蓝色下划线（默认隐藏） */
.c-top span::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 0.625vw; /* 2px → 0.625vw */
  background-color: #4f7af6;
  transition: width 0.3s ease;
}

/* 当前选中项的下划线（显示） */
.c-top span.active::after {
  width: 100%;
}

.c-top span.active {
  color: #333333;
  font-weight: bold;
}
