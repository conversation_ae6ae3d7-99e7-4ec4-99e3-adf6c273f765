.input-c {
  padding: 0 17px;
  flex: 1;
}
.input-c .start,
.input-c .end {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  position: relative;
}
.input-c .start span,
.input-c .end span {
  font-size: 14px;
}
.input-c .start input,
.input-c .end input {
  height: 36px;
  width: 76%;
  border: 1px solid #99999933;
  border-radius: 5px;
  padding: 8px;
  outline: none;
}
.input-c .start i,
.input-c .end i {
  position: absolute;
  right: 10px;
}

.require {
  position: relative;
  font-size: 14px;
  font-weight: 550;
}
.require::before {
  content: "*";
  position: absolute;
  color: #e50101;
  top: 0;
  left: -0.5rem;
}
.popup-overlay{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4); /* 半透明遮罩 */
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 999; 
}
.popup {
  background: white;
  padding: 20px;
  border-radius: 10px 10px 0px 0px;
  width: 100%;
  position: relative;
  position: absolute;
  bottom: 0px;
  left: 0px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  text-align: center;
}

.calendar-grid div {
  padding: 8px;
  background: #f0f0f0;
  border-radius: 16.8px;
  cursor: pointer;
  font-size: 12.8px;
}

.calendar-grid div:hover {
  background: #9effd7;
}

.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 9.6px;
}

button {
  cursor: pointer;
}

.close {
  border-radius: 26.4px;
  position: absolute;
  right: 20px;
  bottom: 4.8px;
  font-size: 12.8px;
  color: #4589ff;
}

.btn-r,
.btn-l {
  background: none;
  border-radius: 13.6px;
  font-size: 23px !important;
  border: 0px;
  width: 20px !important;
}

select {
  width: 70.4px;
  height: 32px;
  border-radius: 12.8px;
  font-size: 16px;
  outline: none;
  text-align: center;
}

.time-quantum {
  height: 41px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  span{
    font-size: 14px;
    margin-bottom: 10px;
    margin-top: 10px;
    display: block;
  }
}
.time-quantum input {
  float: right;
  height: 80%;
  width: 80px;
  font-size: 14px;
  margin-bottom: 10px;
  margin-top: 10px;
}

.time-btn-footer {
  font-size: 14px;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
/* .time-btn-footer div {
  height: 41px;
  width: 133.6px;
  text-align: center;
  line-height: 41px;
}
.time-btn-footer div:nth-child(1) {
  color: #ffffff;
  opacity: 1;
  border-radius: 6.2px;
  background: #b3b3b3;
  border: 1px solid #b3b3b3;
}
.time-btn-footer div:nth-child(2) {
  border-radius: 6.2px;
  background: #0066DF;
  color: #ffffff;
} */

.calendar-grid div.selected {
  background-color: #1890ff;
  color: white;
  border-radius: 50%;
}
/* .start,.end  */
.ti-icon{
  position: absolute;
  right: 9px;
}
input{
  font-size: 14px;
}
.disabled {
  color: #ccc;
  pointer-events: none;
  cursor: not-allowed;
  background-color: #f5f5f5;
}
.n-button {
  width: 140px;
  height: 41px;
}
/* 底部通知按钮 */
.base-btn-footer {
    margin-bottom: 10px;
    height: 40px;
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 20px;
}