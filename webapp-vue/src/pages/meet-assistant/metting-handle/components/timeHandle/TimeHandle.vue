<template>
  <div class="inner-container">
    <div class="input-c">
      <span class="require">预约时间</span>
      <div class="start">
        <span>开始时间</span>
        <input type="text" placeholder="请选择开始时间" readonly :value="startTime" @click="openCalendar('start')" />
        <svg class="icon-16 ti-icon">
          <use :xlink:href="`#icon-meeting-12-10`" />
        </svg>
      </div>
      <div class="end">
        <span>结束时间</span>
        <input type="text" placeholder="请选择结束时间" readonly :value="endTime" @click="openCalendar('end')" />
        <svg class="icon-16 ti-icon">
          <use :xlink:href="`#icon-meeting-12-10`" />
        </svg>
      </div>
    </div>
    <div class="base-btn-footer">
      <n-button class="medium" type="primary" @click="handleConfirm">
        完成
      </n-button>
      <n-button class="medium" @click="handleCancel"> 取消 </n-button>
    </div>
    <!-- <div class="base-btn-footer">
      <div @click="handleConfirm">完成</div>
      <div @click="handleCancel">取消</div>
    </div> -->

    <!-- 日历弹窗 -->
    <div v-if="showPopup" class="popup-overlay">
      <div class="popup">
        <div class="calendar-header">
          <button class="btn-l" @click="prevYear">«</button>
          <select v-model="currentYear">
            <option v-for="y in years" :key="y" :value="y">{{ y }}</option>
          </select>
          <select v-model="currentMonth">
            <option v-for="m in months" :key="m.value" :value="m.value">
              {{ m.label }}
            </option>
          </select>
          <button class="btn-r" @click="nextYear">»</button>
        </div>

        <div class="calendar-week">
          <div v-for="d in weekDays" :key="d">{{ d }}</div>
        </div>

        <div class="calendar-grid">
          <div v-for="(_, i) in blankDays" :key="'b' + i"></div>
          <!-- <div v-for="day in daysInMonth" :key="day" :class="{ selected: isSelected(day) }" @click="selectDay(day)">
            {{ day }}
          </div> -->
          <div v-for="day in daysInMonth" :key="day" :class="[
            { selected: isSelected(day) },
            { disabled: isPastDate(day) },
          ]" @click="!isPastDate(day) && selectDay(day)">
            {{ day }}
          </div>
        </div>

        <div class="time-quantum">
          <span>{{ selectedTime }}</span>
          <input type="time" v-model="selectedTime" step="60" />
        </div>
        <div class="base-btn-footer">
          <n-button @click="closePopup">
            取消
          </n-button>
          <n-button type="primary" @click="confirmDate">
            确定
          </n-button>
        </div>
        <!-- <div class="time-btn-footer">
          <div @click="closePopup">取消</div>
          <div @click="confirmDate">确定</div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMettingHandleStore } from "@/stores/mettingHandle";
import { NButton } from "naive-ui";
import { storeToRefs } from "pinia";
import { computed, onMounted, ref } from "vue";
import { useRouter } from "vue-router";

const startTime = ref("");
const endTime = ref("");
const showPopup = ref(false);
const activeField = ref("start");
const today = new Date();
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth());
const selectedDay = ref(new Date().getDate());
const selectedTime = ref("09:00");
const { mettingHandle, timeChanged } = storeToRefs(useMettingHandleStore());
const weekDays = ["日", "一", "二", "三", "四", "五", "六"];
// const months = Array.from({ length: 12 }, (_, i) => `${i + 1}月`);
// const years = Array.from({ length: 2100 - 1970 + 1 }, (_, i) => 1970 + i);
const years = computed(() => {
  const nowYear = today.getFullYear();
  return Array.from({ length: 2100 - nowYear + 1 }, (_, i) => nowYear + i);
});
const months = computed(() => {
  const startMonth =
    currentYear.value === today.getFullYear() ? today.getMonth() : 0;
  return Array.from({ length: 12 - startMonth }, (_, i) => ({
    label: `${startMonth + i + 1}月`,
    value: startMonth + i,
  }));
});
onMounted(() => {
  startTime.value = mettingHandle.value.startTime || "";
  endTime.value = mettingHandle.value.endTime || "";
  timeChanged.value = false;
});
const blankDays = computed(() => {
  const firstDay = new Date(currentYear.value, currentMonth.value, 1).getDay();
  return Array.from({ length: firstDay });
});

const daysInMonth = computed(() => {
  return new Date(currentYear.value, currentMonth.value + 1, 0).getDate();
});

const selectedDate = computed(() => {
  return new Date(currentYear.value, currentMonth.value, selectedDay.value);
});

function openCalendar(field: string) {
  activeField.value = field;
  const targetValue = field === "start" ? startTime.value : endTime.value;

  if (targetValue) {
    const [datePart, timePart] = targetValue.split(" ");
    const [y, m, d] = datePart.split("-").map(Number);
    currentYear.value = y;
    currentMonth.value = m - 1;
    selectedDay.value = d;
    selectedTime.value = timePart || "09:00";
  } else {
    const now = new Date();
    currentYear.value = now.getFullYear();
    currentMonth.value = now.getMonth();
    selectedDay.value = now.getDate();
    selectedTime.value = `${String(now.getHours()).padStart(2, "0")}:${String(
      now.getMinutes()
    ).padStart(2, "0")}`;
  }

  showPopup.value = true;
}

function isSelected(day: number) {
  return (
    selectedDay.value === day &&
    selectedDate.value.getMonth() === currentMonth.value &&
    selectedDate.value.getFullYear() === currentYear.value
  );
}

function selectDay(day: number) {
  selectedDay.value = day;
}

function closePopup() {
  showPopup.value = false;
}

function confirmDate() {
  const formatted = `${currentYear.value}-${String(
    currentMonth.value + 1
  ).padStart(2, "0")}-${String(selectedDay.value).padStart(2, "0")} ${selectedTime.value
    }`;

  const targetRef = activeField.value === "start" ? startTime : endTime;
  const otherRef = activeField.value === "start" ? endTime : startTime;

  const thisDate = new Date(formatted.replace(" ", "T"));
  const otherDate = new Date(otherRef.value.replace(" ", "T"));

  if (
    startTime.value &&
    endTime.value &&
    startTime.value !== "" &&
    endTime.value !== ""
  ) {
    if (activeField.value === "start" && thisDate > otherDate) {
      alert("开始时间不能晚于结束时间");
      return;
    }
    if (activeField.value === "end" && thisDate < otherDate) {
      alert("结束时间不能早于开始时间");
      return;
    }
  }

  targetRef.value = formatted;
  closePopup();
}

function prevYear() {
  if (currentYear.value > today.getFullYear()) {
    currentYear.value--;
  }
}
function nextYear() {
  currentYear.value++;
}
const router = useRouter();
function handleCancel() {
  // 跳转或清空
  router.back();
  // window.location.hash = '/meeting-handle.html';
}

function handleConfirm() {
  mettingHandle.value.startTime = startTime.value || "";
  mettingHandle.value.endTime = endTime.value || "";
  timeChanged.value = true;
  router.back();
  console.log("开始时间:", startTime.value);
  console.log("结束时间:", endTime.value);
}
function isPastDate(day: number) {
  const today = new Date();
  const date = new Date(currentYear.value, currentMonth.value, day);
  today.setHours(0, 0, 0, 0); // 去掉时分秒精度，避免比较误差
  return date < today;
}
</script>

<style scoped>
@import "./timeHandle.css";
@import "../../styles/metting-base.css";
</style>
