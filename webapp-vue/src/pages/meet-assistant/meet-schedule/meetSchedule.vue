<template>
  <div class="main">
    <!-- 顶部区域 -->
    <div class="tops">
      <div class="top">
       <svg  class="icon-16 goBack" @click="goBack"><use  xlink:href="#icon-home-前翻-2"></use></svg>
        <span>会议助手</span>
      </div>
      <div class="center">
        <svg class="icon-36" >
                <use xlink:href="#icon-meeting-36-3"></use>
          </svg>
        <span>{{ greeting }} {{userInfo?.name||''}}</span>
      </div>

      <!-- 日历 -->
      <div class="calendar">
        <div class="calendar-plug-in" @click="showCalendar = true">
          <span>{{ currentMonthLabel }}</span>
          <svg class="icon-12" >
                <use xlink:href="#icon-meeting-12-8"></use>
          </svg>
        </div>
        <div class="calendar-show">
          <div class="week">
            <div
              v-for="(w, i) in weekLabels"
              :key="i"
              :class="{ active: activeIndex === i }"
            >
              {{ w }}
            </div>
          </div>
          <div class="day">
            <div
              v-for="(d, i) in daysOfWeek"
              :key="i"
              :class="{ active: activeIndex === i }"
              @click="selectDay(i)"
            >
              {{ d }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 卡片区域 -->
    <div class="cards-container content" >
      <div class="cards-container-top">
        <div class="top-one">
          今天<span>{{ selectedDayLabel }}</span>
        </div>
        <div class="top-tow">
          你还有<span>{{ amount() }}</span
          >个会议任务
        </div>
      </div>
      <div class="cards-wrapper" ref="cardWrapper" v-if="meetingCards.length" :class="{ 'justify-center': meetingCards.length === 1 }">
        <div
          class="card"
          v-for="(card, index) in meetingCards"
          :key="index"
          :ref="
            (el) => {
              if (el) cardRefs[index] = el;
            }
          "
          :class="{ 'partially-visible': isFullyVisible[index] === false }"
          @click="goInfo(card)"
        >
          <div class="title-t" :style="{ color: getStatusColor(card.status) }">
            <i></i>{{ getStatusText(card.status) }}
          </div>
          <span class="title-r">{{
            card.meetingfacilityList.length ? "有会服" : "无会服"
          }}</span>
          <span class="title-t1">{{ card.title }}</span>
          <div class="card-footer">
            <span>{{ card.time }}</span>
            <i></i>
            <span>{{ card.time1 }}</span>
          </div>
          <div class="reserved">
            <span class="i">发起人</span>
            <span>{{ card.creatorName }}</span>
          </div>
          <div class="reserved">
            <span class="i">会议室</span>
            <span>{{ card.location }}</span>
          </div>
          <div class="reserved">
            <span class="i">参加人员</span>
            <div class="join">
              <span v-for="(name, index) in card.nameList" :key="index">{{
                name
              }}</span>
              <svg class="icon-30" v-if="card.meetingpersonnelList.length > 2">
                <use xlink:href="#icon-home-更多头像"></use>
              </svg>
            </div>
          </div>
          <div class="go-to-meeting" @click.stop="handleGoToMeeting(card)">
            前往会议室
          </div>
        </div>
      </div>
      <div class="noData" v-else >
        <svg class="icon-30">
            <use xlink:href="#icon-home-暂无"></use>
        </svg>
        <span>暂无会议</span>
      </div>
    </div>

    <!-- 底部导航 -->
    <div class="met-footer">
    <div
      v-for="(item, index) in footerItems"
      :key="index"
      :class="{ active: activeFooter === index }"
      @click="handleFooterClick(index)"
    >
      <svg class="icon-22">
        <!-- 动态切换图标 -->
        <use :xlink:href="getFooterIcon(index)"></use>
      </svg>
      <span>{{ item }}</span>
    </div>
  </div>

    <!-- 日历弹窗 -->
    <CalendarPopup
      v-if="showCalendar"
      :initialDate="selectedDate || new Date()"
      :selectedDate="selectedDate"
      @close="showCalendar = false"
      @select-date="handleDateSelect"
    />
  </div>
</template>

<script  setup>
import { ref, onMounted, onBeforeUnmount, nextTick, watch,computed } from "vue";
import { useRouter } from "vue-router";
import CalendarPopup from "./CalendarPopup.vue";
import meetReservedService from "@/service/meet-assistant/meet-handle/meet-reserved.service";
// import type { UserInfo } from "@/models/MeetReserved1"
// 路由
const router = useRouter();
const goBack = () => router.push("/home");

// UI 状态相关
const activeFooter = ref(0);
const footerItems = ["日程", "预约", "会议室"];
const showCalendar = ref(false);
const userInfo = ref(null);
// 日期和日历相关
const weekLabels = ["日", "一", "二", "三", "四", "五", "六"];
const currentDate = new Date();
const years = ref(`${currentDate.getFullYear()}`);
const months = ref(`${currentDate.getMonth() + 1}`);
const activeIndex = ref(currentDate.getDay());
const currentMonthLabel = ref(`${months.value}月`);
const selectedDayLabel = ref(`${months.value}月${currentDate.getDate()}日`);
const selectedDate = ref(new Date(
    years.value,
    months.value - 1,
    currentDate.getDate()
  ));

const getCurrentWeekDates = () => {
  const now = new Date();
  const currentDay = now.getDay();
  return Array.from({ length: 7 }, (_, i) => {
    const date = new Date(now);
    date.setDate(now.getDate() - currentDay + i);
    return date.getDate();
  });
};

const daysOfWeek = ref(getCurrentWeekDates());

// 会议卡片数据
const meetingCards = ref([]);
const cardRefs = ref([]);
const isFullyVisible = ref([]);
const cardsWrapper = ref(null);
let observer = null;

// 获取用户
const getUser = () => {
  meetReservedService.getCurrentPersonnel().then((res) => {
    userInfo.value = res;
  })
}

// 数据请求逻辑
const getList = (date) => {
  meetReservedService
    .getListReserved({
      date,
      loadMeetingpersonnelList: true,
      loadMeetingroom: true,
      onlyParticipant:true,
    })
    .then((res) => {
      meetingCards.value = res.result.map((item) => {
        const nameList =
          item.meetingpersonnelList?.map((p) => p.personnel?.name?.charAt(0)) ||
          [];
        return {
          ...item,
          time: item.startTime?.slice(11, 16),
          time1: item.endTime?.slice(11, 16),
          location: item.meetingroom?.name,
          nameList: nameList.length >= 3 ? nameList.slice(0, 2) : nameList,
        };
      });
      console.log(meetingCards.value,'meetingCards.value')
    });
};
const goInfo = (item)=>{
  // if(item.ifCreator){
  //   router.push(`/meeting-handle-edit/${item.id}`);
  // }else{
  //   router.push(`/meeting-info/${item.id}`);
  // }
  router.push(`/meeting-info/${item.id}`);
}

// 卡片可视性监测
const initIntersectionObserver = () => {
  observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        const index = cardRefs.value.indexOf(entry.target);
        if (index !== -1) {
          isFullyVisible.value[index] =
            entry.isIntersecting && entry.intersectionRatio === 1;
        }
      });
    },
    {
      root: cardsWrapper.value,
      threshold: 1.0,
      rootMargin: "0px",
    }
  );

  cardRefs.value.forEach((card, index) => {
    if (card) {
      isFullyVisible.value[index] = false;
      observer.observe(card);
    }
  });
};

// 方法 - UI交互
const handleFooterClick = (index) => {
  activeFooter.value = index;
  const routeMap = ["/meetSchedule", "/meetReserved", "/meetRoom"];
  router.push(routeMap[index]);
};

const handleGoToMeeting = (card) => {
  router.push({
    path: '/goMeetRoom',
    query: card.meetingroom||{}
  })
};

const selectDay = (index) => {
  activeIndex.value = index;
  const day = daysOfWeek.value[index];
  selectedDayLabel.value = `${months.value}月${daysOfWeek.value[index]}日`;
  getList(`${years.value}-${months.value}-${day}`);
  selectedDate.value = new Date(
    years.value,
    months.value - 1,
    day
  );
};

const handleDateSelect = (week, selectedIndex) => {
  const selected = week[selectedIndex];
  daysOfWeek.value = week.map((d) => d.date);
  years.value = selected.year;
  months.value = selected.month;
  activeIndex.value = selectedIndex;
  selectedDate.value = new Date(
    selected.year,
    selected.month - 1,
    selected.date
  );
  currentMonthLabel.value = `${selected.month}月`;
  selectedDayLabel.value = `${selected.month}月${selected.date}日`;
  getList(`${selected.year}-${selected.month}-${selected.date}`);
};

// 状态显示
const getStatusText = (status) => {
  switch (status) {
    case 0:
      return "会议即将开始";
    case 1:
      return "会议进行中";
    case 2:
      return "会议已取消";
    case 3:
      return "会议已结束";
    default:
      return "未知状态";
  }
};

const getStatusColor = (status) => {
  switch (status) {
    case 0:
      return "#ff7605";
    case 1:
      return "#ff5b5b";
    case 2:
    case 3:
      return "#b6b6b6";
    default:
      return "#000";
  }
};
const greeting = computed(() => {
  const hour = new Date().getHours();
  if (hour < 6) return '凌晨好！';
  if (hour < 12) return '上午好！';
  if (hour < 14) return '中午好！';
  if (hour < 18) return '下午好！';
  return '晚上好！';
});
const footerIcons = {
  0: {
    default: "#icon-home-日程(默认)",
    active: "#icon-home-日程(选中)"
  },
  1: {
    default: "#icon-home-预约(默认)",
    active: "#icon-home-预约(选中)"
  },
  2: {
    default: "#icon-home-会议室(默认)",
    active: "#icon-home-会议室(选中)"
  }
};

// 获取底部导航图标
const getFooterIcon = (index) => {
  return activeFooter.value === index 
    ? footerIcons[index].active 
    : footerIcons[index].default;
};

// 生命周期钩子
onMounted(() => {
  const today = new Date().getDate();
  getUser();
  getList(`${years.value}-${months.value}-${today}`);
  nextTick(() => {
    initIntersectionObserver();
  });
});

onBeforeUnmount(() => {
  if (observer) observer.disconnect();
});

watch(
  meetingCards,
  () => {
    nextTick(() => {
      if (observer) observer.disconnect();
      cardRefs.value = [];
      isFullyVisible.value = [];
      nextTick(() => initIntersectionObserver());
    });
  },
  { deep: true }
);
const amount = ()=>{
  const dataAmount = meetingCards.value.length;
  const countTotal =  meetingCards.value.reduce((total, item) => {
  return total + (item.status === 0 || item.status === 1 ? 1 : 0);
}, 0);
 
  return countTotal +'/'+dataAmount
}
</script>

<style scoped>
@import "./meetSchedule.css";
</style>
