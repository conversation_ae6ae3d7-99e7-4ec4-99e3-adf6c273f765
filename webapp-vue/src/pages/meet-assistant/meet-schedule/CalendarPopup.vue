<template>
  <div class="popup-overlay">
    <div class="popup">
      <div class="calendar-header">
        <button @click="changeYear(-1)">«</button>
        <select v-model="currentYear">
          <option v-for="y in years" :key="y" :value="y">{{ y }}</option>
        </select>
        <select v-model="currentMonth">
          <option v-for="(m, i) in 12" :key="i" :value="i">
            {{ i + 1 }}月
          </option>
        </select>
        <button @click="changeYear(1)">»</button>
      </div>
      <div class="calendar-week">
        <div v-for="(w, i) in weekLabels" :key="i">{{ w }}</div>
      </div>
      <div class="calendar-grid">
        <div v-for="n in firstDay" :key="'empty-' + n"></div>
        <div
          v-for="d in daysInMonth"
          :key="d"
          :class="{
            'active-day': isSelected(d),
            today: isToday(d),
          }"
          @click="selectDate(d)"
        >
          {{ d }}
        </div>
      </div>
      <div style="padding: 20px"></div>
      <button class="close" @click="$emit('close')">取消</button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
// 定义 props 和 emits
const props = defineProps({
  initialDate: {
    type: Date,
    default: () => new Date(),
  },
  selectedDate: Date, // 新增：用于高亮选中日期
});
// 正确声明 emit
const emit = defineEmits(["close", "select-date"]);
const weekLabels = ["日", "一", "二", "三", "四", "五", "六"];
const today = new Date();
// const currentYear = ref(today.getFullYear())
// const currentMonth = ref(today.getMonth())
const currentYear = ref(props.initialDate.getFullYear());
const currentMonth = ref(props.initialDate.getMonth());
const years = Array.from({ length: 2100 - 1970 + 1 }, (_, i) => i + 1970);

const firstDay = computed(() =>
  new Date(currentYear.value, currentMonth.value, 1).getDay()
);
const daysInMonth = computed(() =>
  new Date(currentYear.value, currentMonth.value + 1, 0).getDate()
);

watch(
  () => props.initialDate,
  (newVal) => {
    if (newVal) {
      currentYear.value = newVal.getFullYear();
      currentMonth.value = newVal.getMonth();
    }
  }
);

const changeYear = (delta) => {
  currentYear.value += delta;
};
const isSelected = (d) => {
  if (!props.selectedDate) return false;
  return (
    props.selectedDate.getFullYear() === currentYear.value &&
    props.selectedDate.getMonth() === currentMonth.value &&
    props.selectedDate.getDate() === d
  );
};
const selectDate = (d) => {
  const selectedDate = new Date(currentYear.value, currentMonth.value, d);
  const dayOfWeek = selectedDate.getDay();
  console.log("选中日期：", selectedDate, d);
  const weekArray = [];
  for (let i = 0; i < 7; i++) {
    const offset = i - dayOfWeek;
    const date = new Date(selectedDate);
    date.setDate(selectedDate.getDate() + offset);
    weekArray.push({
      year: date.getFullYear(),
      month: date.getMonth() + 1,
      date: date.getDate(),
    });
  }

  // 传回父组件：一周数据和选中索引
  emit("select-date", weekArray, dayOfWeek);
  emit("close");
};
const isToday = (d) => {
  const date = new Date(currentYear.value, currentMonth.value, d);
  return (
    date.getFullYear() === today.getFullYear() &&
    date.getMonth() === today.getMonth() &&
    date.getDate() === today.getDate()
  );
};
</script>
<style scoped>
@import "./meetSchedule.css";
</style>
