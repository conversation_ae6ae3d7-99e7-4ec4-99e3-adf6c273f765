.main {
  position: relative;
  min-height: 100vh;
  /* min-height: calc(100vh - 44px); */
}

.tops {
  width: 100%;
  height: 190px;
  opacity: 1;
  /* background: linear-gradient(180deg, #084d9f 0%, #0066df 100%); */
  background: #045abf;
}

.centre {
  width: 100%;
  height: 378px;
}

.top {
  text-align: center;
  /* padding: 14.5px; */
  /* padding-top: 15px; */
  height: 44px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.top .goBack {
  position: absolute;
  left: 14.5px;
  top: 13px;
  /* display: block; */
  /* width: 9px; */
  /* height: 16px; */
  /* background: url("@/assets/image/meeting/back.png") center no-repeat; */
  /* background-size: cover; */
}

.top span {
  color: #ffffff;
  font-size: 16px;
}

.center {
  width: 100%;
  height: 62px;
  line-height: 62px;
  padding: 0 15px;
  display: flex;
  align-items: center;
}

.center i {
  display: block;
  width: 36px;
  height: 36px;
  background-size: cover;
}

.center span {
  color: #ffffff;
  font-size: 14px;
  margin-left: 10px;
}

.calendar {
  padding: 0 15px;
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.calendar .calendar-plug-in {
  text-align: center;
  width: 35px;
  height: 56px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
}

.calendar .calendar-plug-in span {
  color: #ffffff;
  font-size: 18px;
}

.calendar .calendar-plug-in i {
  display: block;
  width: 12px;
  height: 12px;
  background: url("@/assets/image/meeting/bottom.png") center no-repeat;
  background-size: cover;
}

.calendar .calendar-show {
  width: 100%;
  height: 56px;
  color: #ffffff;
  font-size: 14px;
}

.calendar .calendar-show .week {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.calendar .calendar-show .week div {
  width: 27px;
  height: 31px;
  text-align: center;
  line-height: 31px;
}

.calendar .calendar-show .day {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.calendar .calendar-show .day div {
  width: 27px;
  height: 31px;
  line-height: 31px;
  text-align: center;
}

.week div.active {
  background-color: #61eaff;
  border-radius: 14px 14px 0 0;
  color: #0557b7;
}

.day div.active {
  background-color: #61eaff;
  border-radius: 0 0 14px 14px;
  color: #0557b7;
}

.content {
  width: 100%;
  height: 378px;
  border-radius: 10px 10px 0 0;
  background: linear-gradient(180deg, #ffffff 0%, #f4f6fa 100%);
  position: relative;
}

.cards-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 239px);
  margin: 0 auto;
  overflow: hidden;
}

.cards-container-top {
  display: flex;
  height: 53px;
  line-height: 53px;
  padding: 0 15px;
  border-bottom: 1px solid #dadae4;
  justify-content: space-between;
}

.top-one,
.top-tow {
  font-size: 14px;
  font-weight: normal;
}

.top-one span {
  margin-left: 10px;
  font-size: 12px;
  color: #333333;
}

.top-tow span {
  color: red;
}

.cards-wrapper {
  display: flex;
  gap: 20px;
  padding: 20px;
  transition: transform 0.3s ease;
  overflow-x: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  /* justify-content: space-around; */
}

.cards-wrapper::-webkit-scrollbar {
  display: none;
}

.card {
  flex: 0 0 200px;
  scroll-snap-align: start; /* 添加这个属性 */

  height: 230px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  opacity: 1;
  border-radius: 10px;
  border: 1px solid #ffffff;
  background: url("@/assets/image/meeting/会议即将开始-2.png") center no-repeat;
  background-size: 110% 109%;
  position: relative;
  background-color: #f0f7ff;
}
.cards-wrapper .partially-visible {
  background: url("@/assets/image/meeting/会议即将开始-3.png") center no-repeat;
  background-size: 110% 109%;
}

.card .title-t {
  color: #ff7605;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.card .title-t i {
  opacity: 1;
  border-radius: 0;
  background: #0066DF;
  width: 4px;
  height: 12px;
  margin-right: 3px;
}

.card .title-t1 {
  opacity: 1;
  color: #333333;
  font-weight: bold;
  font-size: 12px;
  line-height: 1.2;
  margin: 5px 0;
}

.card .title-r {
  position: absolute;
  right: 6px;
  top: 2px;
  font-size: 10px;
  color: #ffffff;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  color: #333333;
  font-size: 18px;
  align-items: center;
  font-weight: 500;
}

.card-footer i {
  display: block;
  width: 60px;
  height: 4px;
  background: url("@/assets/image/meeting/箭头.png") center no-repeat;
  background-size: cover;
}

.reserved {
  display: flex;
  align-items: center;
  color: rgba(51, 51, 51, 0.7);
  font-size: 12px;
  justify-content: space-between;
  margin-bottom: 5px;
}

.join {
  display: flex;
}

.join span {
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  background: #4f7af6;
  border-radius: 17px;
  color: #ffffff;
  font-size: 12px;
}

/* .join i:nth-child(1) {
  background: url("../../../assets/image/meeting/1.png") center no-repeat;
  background-size: contain;
}

.join i:nth-child(2) {
  background: url("../../../assets/image/meeting/2.png") center no-repeat;
  background-size: contain;
} */

.join i:nth-child(3) {
  background: #cbcdd0;
  /* background: url("../../../assets/image/meeting/3.png") center no-repeat;
  background-size: contain; */
}

.go-to-meeting {
  width: 180px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  opacity: 1;
  border-radius: 3px;
  background: #0066df;
  color: #ffffff;
  font-size: 14px;
  margin-top: 10px;
}

.go-to-meeting.active {
  background: #4589ff !important;
}
.partially-visible .title-r {
  color: #d7d9dd;
}
.partially-visible .go-to-meeting {
  background: #4991e7;
  color: #d7d9dd;
}
.partially-visible .join i {
  background: #4991e7;
}
.scroll-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 20px;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.left-btn {
  left: 5px;
}

.right-btn {
  right: 5px;
}

.met-footer {
  width: 100%;
  height: 49px;
  border-radius: 10px 10px 0 0;
  background: rgba(255, 255, 255, 0.94);
  backdrop-filter: blur(20px);
  box-shadow: 0 -0.5px 6px rgba(93, 93, 93, 0.2);
  position: absolute;
  bottom: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.met-footer div {
  width: 34px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.met-footer div i {
  display: block;
  width: 22px;
  height: 22px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.met-footer div span {
  color: #666666;
  font-size: 10px;
  width: 100%;
  text-align: center;
  /* margin-top: 5px; */
}

.met-footer div.active span {
  color: #0066df;
}

.popup-overlay {
  position: fixed;
  top: 33%;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.popup {
  background: white;
  padding: 20px;
  border-radius: 10px;
  position: relative;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  text-align: center;
}

.calendar-grid div {
  padding: 8px;
  background: #f0f0f0;
  border-radius: 16.8px;
  cursor: pointer;
  font-size: 12.8px;
}

.calendar-grid div:hover {
  background: #9effd7;
}

.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 14px;
}

button {
  cursor: pointer;
  border: 0px;
  font-size: 26px !important;
}

.btn-r,
.btn-l,
.close {
  background: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  line-height: 32px;
  font-size: 14px !important;
}

.close {
  background: #f0f0f0;
  border-radius: 4px;
  position: absolute;
  right: 20px;
  bottom: 4.8px;
  font-size: 14px;
  color: #4589ff;
  width: 66px;
}

select {
  width: 70.4px;
  height: 32px;
  border-radius: 12.8px;
  font-size: 16px;
  outline: none;
  text-align: center;
}
.active-day {
  background-color: #1890ff !important;
  color: white;
}
.noData {
  height: 57px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #afb1b3;
  width: 50px;
  flex-wrap: wrap;
  position: absolute;
  top: 50%;
  left: 42%;
}
.noData span {
  display: block;
  margin-top: 10px;
}
.today {
  background-color: #d9e8fa !important; /* 浅青色背景 */
}
.justify-center {
  justify-content: center;
}