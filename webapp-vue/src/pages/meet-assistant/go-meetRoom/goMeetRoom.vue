<template>
  <div class="room-container">
    <div class="top-info">
      <svg class="icon-12" v-show="selectedRoom.roomName">
        <use xlink:href="#icon-meeting-12-4"></use>
      </svg>
      <span>{{ selectedRoom.roomName }}</span>
    </div>
    <!-- 主体内容区域 -->
    <div class="meeting-room-container">
      <div class="meeting-room-content" v-if="selectedRoom.roomF">
        <SlMeetingRoom5 v-if="selectedRoom.roomF == 5" :selectedId="selectedRoom.id" />
        <SlMeetingRoom8 v-if="selectedRoom.roomF == 8" :selectedId="selectedRoom.id" />
        <SlMeetingRoom9 v-if="selectedRoom.roomF == 9" :selectedId="selectedRoom.id" />
        <SlMeetingRoom13 v-if="selectedRoom.roomF == 13" :selectedId="selectedRoom.id" />
      </div>
      <div class="empty-container" v-else>
        <BaseEmpty>
          暂无图片
        </BaseEmpty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseEmpty from "@/components/BaseEmpty.vue";
import SlMeetingRoom13 from "@/components/SlMeetingRoom13.vue";
import SlMeetingRoom5 from "@/components/SlMeetingRoom5.vue";
import SlMeetingRoom8 from "@/components/SlMeetingRoom8.vue";
import SlMeetingRoom9 from "@/components/SlMeetingRoom9.vue";
import type { Meetingroom } from "@/models/MettingHandle";
import { onMounted, ref } from 'vue';
import { useRoute } from "vue-router";
import { generateSelectedMeetingRoom, type SelectedMeetingRoom } from "../metting-handle/meeting-handle.data";
const route = useRoute();
const selectedRoom = ref<SelectedMeetingRoom>({
  id: undefined,
  roomF: null,
  roomName: '',
})
onMounted(() => {
  const meetingroom = route.query as Meetingroom;
  // 已选中的会议室
  if (meetingroom?.id) {
    const selected = generateSelectedMeetingRoom(meetingroom)
    selectedRoom.value = selected
  }
})
</script>

<style scoped>
@import "./goMeetRoom.css";

.meeting-room-container {
  width: 100%;
  padding: 15px;
  background-color: #fff;
}

.meeting-room-content {
  width: 290px;
  height: 146px;
  display: flex;
  justify-content: center;
  position: relative;
  pointer-events: none;
}
</style>
