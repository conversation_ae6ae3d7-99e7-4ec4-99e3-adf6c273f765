.room-container .top {
  text-align: center;
  color: white;
  background: linear-gradient(180deg, #0221e7 0%, #4c5cff 100%);
  width: 100%;
  height: 64px; /* 20vw → 64px */
  line-height: 64px; /* 20vw → 64px */
  opacity: 1;
  flex-shrink: 0;
  position: relative;
}

.room-container .top i {
  position: absolute;
  left: 14.5px; /* 4.53125vw → 14.5px */
  top: 50%;
  display: block;
  width: 9px; /* 2.8125vw → 9px */
  height: 16px; /* 5vw → 16px */
  background: url("@/assets/image/meeting/back.png") center no-repeat;
  background-size: cover;
}

.room-container .top-info {
  display: flex;
  height: 45px; /* 14.0625vw → 45px */
  background: #f4f6fa;
  align-items: center;
  justify-content: center;
}

.room-container .top-info i {
  display: block;
  width: 9px; /* 2.8125vw → 9px */
  height: 10px; /* 3.125vw → 10px */
  background-size: cover;
}

.room-container .top-info span {
  opacity: 1;
  color: #333333;
  font-size: 14px; /* 4.375vw → 14px */
  margin-left: 4px; /* 1.25vw → 4px */
}

.room-container .mainBody {
  height: calc(100vh - 89px); /* 34.0625vw → 109px */
  width: 100%;
  opacity: 1;
  border-radius: 0;
  background: #fdfdfd;
  box-shadow: 0 0 4px #92929233; /* 1.25vw → 4px */
}
