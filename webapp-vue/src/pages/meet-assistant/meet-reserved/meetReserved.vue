<template>
  <div class="res-container">
    <!-- 主体 -->
    <div class="mainBody"  :class="{ 'other-mainBody': currentIdx === 3 }">
      <div class="res-card-top">
            <span :class="{ active: currentIdx == 1 }" @click="changeIdx(1)">全部</span>
            <span :class="{ active: currentIdx == 2 }" @click="changeIdx(2)">待办</span>
            <span :class="{ active: currentIdx == 3 }" @click="changeIdx(3)">完结</span>
      </div>
      <div class="no-Data" v-if="meetings.length === 0">
        <i></i>
        <span>暂无预约</span>
      </div>
      <div class="res-card" v-else>
        <div
          class="card"
          v-for="(item, index) in meetings"
          :key="index"
          :class="getStatusClass(item.status)"
          @click="handleCardClick(item)"
        >
        <!-- 勾选图标 -->
        <span
            @click.stop="checked(item)"
            v-if="(item.status == 2 || item.status == 3)&&currentIdx==3"
            class="checked-icon"
          >
            <svg class="icon-16" v-if="item.checked">
              <use xlink:href="#icon-components-selected"></use>
            </svg>
            <svg class="icon-16" v-else>
              <use xlink:href="#icon-components-unselected"></use>
            </svg>
          </span>
          <!-- 卡片内容 -->
        <div :style="currentIdx==3 ? { width: '87%' } : {}">
          <div class="card-o">
            <span>{{ item.title }}</span>
            <span class="status" :class="getStatusClass(item.status)">
              {{ getStatusStatus(item.status) }}
            </span>
          </div>
          <div class="card-t">
              <svg class="icon-12">
                <use xlink:href="#icon-meeting-12-7"></use>
              </svg>
              <span>{{ item.timeRange }}</span>
          </div>
          <div class="card-t">
              <svg class="icon-12">
                <use xlink:href="#icon-meeting-12-5"></use>
              </svg>
              <span>{{ item.name }}</span>
          </div>
        </div>
        </div>
      </div>
    </div>
    <!-- 全选按钮 -->
    <div class="delete" v-if="currentIdx==3">
      <div
        @click="allShowConfirmHandle"
        style="display: flex; align-items: center"
      >
        <svg class="icon-16" v-if="allShowConfirm">
          <use xlink:href="#icon-components-selected"></use>
        </svg>
        <svg class="icon-16" v-else>
          <use xlink:href="#icon-components-unselected"></use>
        </svg>
        <span class="delete-t">全选</span>
      </div>
      <div class="delete-btn" @click="delete1" :class="{ 'disabled': selectedIds.length === 0 }">删除</div>
    </div>
    <!-- 底部导航 -->
    <div class="res-footer">
      <div
        :class="{ active: activeTab === 'schedule' }"
        @click="switchTab('schedule')"
      >
        <svg class="icon-22">
          <use :xlink:href="getTabIcon('schedule')"></use>
        </svg>
        <span>日程</span>
      </div>
      <div
        :class="{ active: activeTab === 'reserve' }"
        @click="switchTab('reserve')"
      >
        <svg class="icon-22">
          <use :xlink:href="getTabIcon('reserve')"></use>
        </svg>
        <span>预约</span>
      </div>
      <div :class="{ active: activeTab === 'room' }" @click="switchTab('room')">
        <svg class="icon-22">
          <use :xlink:href="getTabIcon('room')"></use>
        </svg>
        <span>会议室</span>
      </div>
    </div>
  </div>
  <!-- 确认弹窗 -->
  <ConfirmDialog
    v-if="showConfirm"
    @close="showConfirm = false"
    @confirm="handleConfirm"
    >确定删除选中数据吗？</ConfirmDialog
  >
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import meetReservedService from "@/service/meet-assistant/meet-handle/meet-reserved.service";
import { useMettingHandleStore } from "@/stores/mettingHandle";
import { NButton, useMessage } from "naive-ui";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
const router = useRouter();
const message = useMessage();
const activeTab = ref("reserve");
const showConfirm = ref(false);
const allShowConfirm = ref(false);
const meetings = ref([]);
const currentIdx = ref(null)
onMounted(() => {
  changeIdx(1)
});
function getList(statusList) {
  meetReservedService
    .getListReserved({ loadMeetingroom: true, onlyCreator: true,statusList })
    .then((res) => {
      const newArray = res.result.map((item) => ({
        ...item.meetingroom,
        timeRange: item.timeRange,
        title: item.title,
        id: item.id,
        status: item.status,
      }));
      console.log(newArray, "newArray");
      meetings.value = newArray;
    });
}
function batchDelete(idList) {
  meetReservedService
    .batchDelete(idList)
    .then((res) => {
      message.success("删除成功");
    })
    .catch((err) => {
      message.error("删除失败");
    })
    .finally(() => {
      changeIdx(currentIdx.value);
      allShowConfirm.value = false;
      showConfirm.value = false;
    });
}
const getStatusClass = (status) => {
  switch (status) {
    case 0:
      return "status-pending";
    case 1:
      return "status-in-progress";
    case 2:
      return "status-cancel";
    case 3:
      return "status-finished";
    default:
      return "";
  }
};
const getStatusStatus = (status) => {
  switch (status) {
    case 0:
      return "待开始";
    case 1:
      return "进行中";
    case 2:
      return "已取消";
    case 3:
      return "已结束";
    default:
      return "";
  }
};
const tabIcons = {
  schedule: {
    default: "#icon-home-日程(默认)",
    active: "#icon-home-日程(选中)",
  },
  reserve: {
    default: "#icon-home-预约(默认)",
    active: "#icon-home-预约(选中)",
  },
  room: {
    default: "#icon-home-会议室(默认)",
    active: "#icon-home-会议室(选中)",
  },
};

// 获取底部导航图标
const getTabIcon = (tab) => {
  return activeTab.value === tab ? tabIcons[tab].active : tabIcons[tab].default;
};

const goBack = () => {
  router.push("/meetSchedule");
};
const meetHandleStore = useMettingHandleStore();
const goAdd = () => {
  meetHandleStore.reset();
  router.push("/meeting-handle");
};

const handleCardClick = (item) => {
  if (item.id) {
    router.push(`/meeting-info/${item.id}`);
  }
  console.log("点击的卡片数据：", item.id);
};

const switchTab = (tab) => {
  activeTab.value = tab;
  if (tab === "schedule") {
    router.push("/meetSchedule");
  } else if (tab === "room") {
    router.push("/meetRoom");
  }
};
// 选中的id数组
const selectedIds = ref([]);
const checked = (item) => {
  if (![2, 3].includes(item.status)) return;
  item.checked = !item.checked;
  if (item.checked) {
    if (!selectedIds.value.includes(item.id)) {
      selectedIds.value.push(item.id);
    }
  } else {
    selectedIds.value = selectedIds.value.filter((id) => id !== item.id);
  }
  const allQualifiedSelected = meetings.value
    .filter((meeting) => [2, 3].includes(meeting.status))
    .every((meeting) => meeting.checked);
    if(allQualifiedSelected){
      allShowConfirm.value = true
    }else{
      allShowConfirm.value = false
    }
  console.log("当前选中的ID:", selectedIds.value);
};
/**删除全选*/
const deleteAllQualifiedMeetings = () => {
  // 判断是否已经全部选中了符合条件的会议
  const allQualifiedSelected = meetings.value
    .filter((meeting) => [2, 3].includes(meeting.status))
    .every((meeting) => meeting.checked);

  if (allQualifiedSelected) {
    // 如果已经全选，则取消所有符合条件的选中状态
    meetings.value.forEach((meeting) => {
      if ([2, 3].includes(meeting.status)) {
        meeting.checked = false;
      }
    });
  } else {
    // 否则选中所有符合条件的会议
    meetings.value.forEach((meeting) => {
      if ([2, 3].includes(meeting.status)) {
        meeting.checked = true;
      }
    });
  }

  selectedIds.value = meetings.value
    .filter((meeting) => meeting.checked)
    .map((meeting) => meeting.id);
  console.log("当前选中的会议 ID:", selectedIds.value);
};
const delete1 = async () => {
  if(selectedIds.value.length==0)return
  showConfirm.value = true;
};

const handleConfirm = async () => {
  batchDelete([...selectedIds.value]);
};
//勾选
const allShowConfirmHandle = () => {
  allShowConfirm.value = !allShowConfirm.value;
  deleteAllQualifiedMeetings();
};

const changeIdx = (idx) => {
    currentIdx.value = idx
    meetings.value = []
    if(idx==1){ //全部
      getList()
    }else if(idx==2){//待开始 0,1
      getList([0,1])
    }else{ //完结 2,3
      getList([2,3])
    }
    allShowConfirm.value=false
    selectedIds.value = [];
}
</script>

<style scoped>
@import "./meetReserved.css";
</style>
