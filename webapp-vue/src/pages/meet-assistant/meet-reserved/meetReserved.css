.res-container {
  background-color: #edeff3;
}

.res-container .top {
  text-align: center;
  color: white;
  /* background: linear-gradient(180deg, #084D9F 0%, #0066DF 100%); */
  background: var(--layout-header-bg);
  width: 100%;
  height: 44px;
  line-height: 64px;
  opacity: 1;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14.5px;
  font-size: 16px;
}

.res-container .top i {
  top: 50%;
  display: block;
  width: 9px;
  height: 16px;
  background-size: cover;
}

.res-container .goBack {
  /* left: 14.5px;
  font-size: 16px; */
}

.res-container .add {
  /* right: 14.5px;
  font-size: 16px; */
}

.res-container .mainBody {
  height: calc(100vh - 93px);
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.res-container .mainBody .no-Data {
  width: 100%;
  height:calc(100% - 35px) ;
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  justify-content: center;
}

.res-container .mainBody .no-Data i {
  display: block;
  width: 100%;
  height: 40px;
  background: url("@/assets/image/meeting/noData.png") center no-repeat;
}

.res-container .mainBody .no-Data span {
  color: #999999;
  font-size: 12px;
}

.res-container .mainBody .res-card {
  width: 100%;
  height:calc(100% - 35px) ;
  overflow: auto;
  padding-bottom: 51px;
}

.res-container .mainBody .res-card .card {
  background: #ffffff;
  width: 100%;
  height: 90px;
  margin-bottom: 4px;
  padding: 11px 15px;
  position: relative;
}

.res-container .mainBody .res-card .card.status-cancel,
.res-container .mainBody .res-card .card.status-finished {
  background: #e6e5e5;
}

.res-container .mainBody .res-card .card .card-o {
  display: flex;
  justify-content: space-between;
}

.res-container .mainBody .res-card .card .card-o span:nth-child(1) {
  color: #333333;
  font-size: 14px;
}

.res-container .mainBody .res-card .card .card-o span.status {
  opacity: 1;
  border-radius: 2px;
  width: 46px;
  height: 23px;
  font-size: 12px;
  text-align: center;
  line-height: 23px;
  display: inline-block;
}

.res-container .mainBody .res-card .card .card-o span.status.status-pending {
  color: #ff7605;
  background: #fff0e4;
  border: 1px solid #ffb06f;
}

.res-container
  .mainBody
  .res-card
  .card
  .card-o
  span.status.status-in-progress {
  color: #ff3333;
  background: #ffe3e3;
  border: 1px solid #ff6868;
}

.res-container .mainBody .res-card .card .card-o span.status.status-cancel {
  color: #666666;
  background: #e6e6e6;
  border: 1px solid #999999;
}

.res-container .mainBody .res-card .card .card-o span.status.status-finished {
  color: #ffffff;
  background: #b6b6b6;
  border: 1px solid #999999;
}

.res-container .mainBody .res-card .card .card-t {
  display: flex;
  align-items: center;
  margin-top: 6px;
}

.res-container .mainBody .res-card .card .card-t i {
  display: block;
  width: 12px;
  height: 12px;
  background: url("@/assets/image/meeting/place1.png") center no-repeat;
  background-size: cover;
  margin-right: 6px;
}

.res-container .mainBody .res-card .card .card-t span {
  color: #666666;
  font-size: 12px;
  margin-left: 5px;
}

.res-container .mainBody .res-card .card .card-t .fas {
  background: url("@/assets/image/meeting/time.png") center no-repeat !important;
  background-size: cover !important;
}

/* 底部导航 */
.res-footer {
  width: 100%;
  height: 49px;
  border-radius: 10px 10px 0 0;
  background: rgba(255, 255, 255, 0.94);
  backdrop-filter: blur(20px);
  box-shadow: 0 -0.5px 6px rgba(93, 93, 93, 0.2);
  /* position: absolute; */
  bottom: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.res-footer div {
  width: 34px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.res-footer div i {
  display: block;
  width: 22px;
  height: 22px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.res-footer div span {
  color: #666666;
  font-size: 10px;
  width: 100%;
  text-align: center;
}

.res-footer div.active span {
  color: #0066df;
}
/* .card:last-child {
  margin-bottom: 80px !important;
} */

/* 底部图标定义 */
.delete {
  width: 100%;
  background: white;
  display: flex;
  /* position: absolute;
  bottom: 50px; */
  height: 42px;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
  z-index: 999;
}
.delete-btn {
  width: 110px;
  height: 38px;
  background: #0066df;
  font-size: 14px;
  color: #ffffff;
  text-align: center;
  line-height: 38px;
  border-radius: 5px;
}
.disabled {
  background: #999999;
}
.delete-t {
  font-size: 14px;
  margin-left: 5px;
}
.card {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.res-card-top {
  width: 100%;
  height: 35px;
  background: #ffffff;
}
.res-card-top span {
  position: relative;
  cursor: pointer;
  padding-bottom: 1.5625vw;
  /* 5px → 1.5625vw */
  margin-right: 5.625vw;
  /* 18px → 5.625vw */
}

/* 蓝色下划线（默认隐藏） */
.res-card-top span::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: 0;
  width: 0;
  height: 0.625vw;
  transform: translateX(-50%);
  /* 2px → 0.625vw */
  background-color: #4f7af6;
  transition: width 0.3s ease;
}

/* 当前选中项的下划线（显示） */
.res-card-top span.active::after {
  width: 50%;
}

.res-card-top span.active {
  color: #333333;
  font-weight: bold;
}
.res-card-top {
  height: 35px;
  border-bottom: 0.3125vw solid #99999933;
  padding: 0 5.3125vw;
  padding-top: 3.125vw;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card{
  border-radius: 0px;
}
.checked-icon{
  width: 16px !important;
  height: 16px !important;
}
.other-mainBody{
  height: calc(100vh - 135px) !important;
}