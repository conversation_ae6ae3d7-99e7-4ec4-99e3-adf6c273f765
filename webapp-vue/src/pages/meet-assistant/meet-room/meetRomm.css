.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    position: relative;
  }
  /* 标题区域 */
  .top {
      text-align: center;
      color: white;
      background: linear-gradient(180deg, #084D9F 0%, #0066DF 100%);
      width: 100%;
      height: 44px;
      line-height: 44px;
      opacity: 1;
      flex-shrink: 0;
    }
    .top span {
      color: #ffffff;
      font-size: 16px;
    }
    
    .search {
      width: 100%;
      height: 74px;
      flex-shrink: 0;
      padding: 0 15px;
      background: #ffffff;
      padding-top: 9px;
      position: relative;
    }
    .search input {
      outline: none;
      height: 30px;
      /* width: 290px; */
      width: 100%;
      padding: 6px 10px;
      opacity: 1;
      border-radius: 3px;
      border: 1px solid #9999997f;
      color: #999999;
      font-size: 14px;
    }
    .search .search-icon {
      position: absolute;
      right: 25px;
      top: 15px;
    }
    
    .search .nav {
      width: 100%;
      margin-top: 10px;
      display: flex;
      align-items: center;
    }
    .search .nav  span {
      padding-bottom: 4px;
    }
    .nav-item {
      margin-left: 20px;
      color: #333333;
      font-size: 14px;
      cursor: pointer;
      position: relative;
    }
    .nav-item::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 2px;
      background-color: transparent;
      transition: width 0.3s ease, background-color 0.3s ease;
    }
    .nav-item.active {
      /* color: #455bff; */
      font-size: 16px;
      font-weight: bold;
    }
    .nav-item.active::after {
      width: 100%;
      background: #4355FC;
    }
    
    .mainPart {
      width: 100%;
      /* height: 380px; */
      height: calc(100vh - 167px);
      flex: 1;
      overflow: auto;
      padding-bottom: 51.2px; /* 略大于 .met-footer3 的高度 49px */
    }
    
    .subject {
      width: 100%;
      padding: 0 15px;
    }
    .subject .card {
      width: 100%;
      min-height: 136px;
      border-radius: 10px;
      background: #ffffff;
      box-shadow: 0 0 6px 0 #92929233;
      margin-top: 10px;
      padding: 10px;
    }
    .subject .card:last-child {
      margin-bottom: 10px;
    }
    .subject .card .c-line1 {
      display: flex;
    }
    .subject .card .c-line1 span:nth-child(1) {
      color: #ffffff;
      font-weight: bold;
      font-size: 14px;
      display: block;
      width: 38px;
      height: 20px;
      opacity: 1;
      border-radius: 2px;
      background: #0066DF;
      text-align: center;
      line-height: 20px;
    }
    .subject .card .c-line1 span:nth-child(2) {
      margin-left: 8px;
      opacity: 1;
      color: #3d3d3d;
      font-family: Source Han Sans;
      font-weight: bold;
      font-size: 14px;
    }
    .subject .card .c-line2 {
      display: flex;
      margin-top: 11px;
    }
    .subject .card .c-line2 i {
      display: block;
      width: 14px;
      height: 14px;
      background-size: cover;
      margin-left: 8px;
    }
    .subject .card .c-line2 span {
      color: #999999;
      font-size: 12px;
      margin-left: 8px;
    }
    .subject .card .c-line3 {
      display: flex;
    }
    .subject .card .c-line3 i {
      display: block;
      width: 14px;
      height: 14px;
      background-size: cover;
      margin-left: 8px;
    }
    .subject .card .c-line3 span {
      color: #999999;
      font-size: 12px;
      margin-left: 8px;
    }
    .subject .card .c-line4 {
      display: flex;
      /* margin-top: 10px; */
          margin-top: 3px;
    }
    .subject .card .c-line4 .line4-t {
      color: #333333;
      font-size: 14px;
      width: 70px;
      display: block;
    }
    .subject .card .c-line4 div {
      display: flex;
      flex-wrap: wrap;
      width: 204px;
    }
    .subject .card .c-line4 div span {
      color: #666666;
      font-size: 12px;
      width: 100%;
      position: relative;
      white-space: nowrap;
    }
    .subject .card .c-line4 div span::after {
      content: "";
      position: absolute;
      left: -7px;
      top: 41%;
      width: 3px;
      height: 3px;
      background-color: red;
    }
  
  
  /* 底部导航 */
  .met-footer3 {
    width: 100%;
    height: 49px;
    border-radius: 10px 10px 0 0;
    background: rgba(255, 255, 255, 0.94);
    backdrop-filter: blur(20px);
    box-shadow: 0 -0.5px 6px rgba(93, 93, 93, 0.2);
    /* position: absolute; */
    bottom: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
  
  .met-footer3 div {
    width: 34px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
  }
  
  .met-footer3 div i {
    display: block;
    width: 22px;
    height: 22px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  
  .met-footer3 div span {
    color: #666666;
    font-size: 10px;
    width: 100%;
    text-align: center;
  }
  
  .met-footer3 div.active span {
    color: #0066df;
  }
  
  /* 底部图标定义 */
 