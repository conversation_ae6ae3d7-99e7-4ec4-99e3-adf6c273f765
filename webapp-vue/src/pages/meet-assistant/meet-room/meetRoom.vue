<template>
  <div class="container1">
    <!-- Top -->
   
    <!-- <div class="top">
      <svg  class="icon-16 goBack" @click="goBack"><use  xlink:href="#icon-home-前翻-2"></use></svg>
      <span>会议助手</span>
    </div> -->
    <!-- Search -->
    <div class="search">
      <input
        v-model="searchText"
        type="text"
        placeholder="请输入会议室名"
      />
      <svg class="icon-16 search-icon" @click="handleSearch()">
                <use xlink:href="#icon-car-search"></use>
            </svg>
      <div class="nav">
        <span
          v-for="item in navItems"
          :key="item.type"
          :class="['nav-item', { active: activeType === item.type }]"
          :data-type="item.type"
          @click="handleNavClick(item.type)"
          :style="{ marginLeft: item.type === 'all' ? '0' : '' }"
        >
          {{ item.label }}
        </span>
      </div>
    </div>

    <!-- 主体 -->
    <div class="mainPart">
      <div class="subject">
        <div class="card" v-for="(item, index) in filteredData" :key="index">
          <div class="c-line1">
            <span>{{ item.floor }}F</span>
            <span>{{ item.name }}</span>
          </div>
          <div class="c-line2">
            <!-- <i></i> -->
            <svg class="icon-14" >
                <use xlink:href="#icon-meeting-14-2"></use>
            </svg>
            <!-- <span v-for="(d, i) in item.device" :key="i">{{ d }}</span> -->
            <span>{{item.deviceInfo}}</span>
          </div>
          <div class="c-line3">
            <svg class="icon-14" >
                <use xlink:href="#icon-meeting-14-1"></use>
            </svg>
            <!-- <span v-for="(c, i) in item.capacity" :key="i">{{ c }}</span> -->
            <span>{{item.capacity}}</span>
          </div>
          <div class="c-line4">
            <span class="line4-t">预约时间</span>
            <div>
              <span v-for="(t, i) in item.times" :key="i">{{ t.timeRange }}</span>
              <span v-if="!item.times || item.times.length === 0">无</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="met-footer3">
    <div
      v-for="(item, index) in footerNav"
      :key="index"
      :class="{ active: activeFooter === item.label }"
      @click="handleFooterClick(item)"
    >
      <svg class="icon-22">
        <use :xlink:href="getFooterIcon(item.label)"></use>
      </svg>
      <span>{{ item.label }}</span>
    </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed,onMounted  } from "vue";
import { useRouter } from "vue-router";
import getMeetingroom from "@/service/meet-assistant/meet-handle/meet-reserved.service";
const router = useRouter();

const searchText = ref("");
const activeType = ref("all");
const activeFooter = ref("会议室");
// 组件挂载后立即调用
onMounted(() => {
  getList();
});
// 样式分类项
const navItems = [
  { label: "全部", type: "all" },
  { label: "5F", type: "5" },
  { label: "8F", type: "8" },
  { label: "9F", type: "9" },
  { label: "13F", type: "13" },
];

const footerNav = [
  { label: "日程", route: "/meetSchedule" },
  { label: "预约", route: "/meetReserved" },
  { label: "会议室", route: "/meetRoom" },
];

// 模拟数据
const meetRoomData = ref([])
const footerIcons = {
  '日程': {
    default: "#icon-home-日程(默认)",
    active: "#icon-home-日程(选中)"
  },
  '预约': {
    default: "#icon-home-预约(默认)",
    active: "#icon-home-预约(选中)"
  },
  '会议室': {
    default: "#icon-home-会议室(默认)",
    active: "#icon-home-会议室(选中)"
  }
};
const goBack = () => {
  router.push("/home");
};
// 获取底部导航图标
const getFooterIcon = (label) => {
  return activeFooter.value === label 
    ? footerIcons[label].active 
    : footerIcons[label].default;
};

// 筛选数据
const filteredData = computed(() => {
  const keyword = searchText.value.trim();
  let result = meetRoomData.value;
  if (activeType.value !== "all") {
    result = result.filter((item) =>
      item.floor.startsWith(activeType.value)
    );
  }

  if (keyword) {
    result = result.filter(
      (item) =>  item.name.includes(keyword)
    );
  }

  return result;
});

// 处理导航点击
const handleNavClick = (type) => {
  activeType.value = type;
  searchText.value = "";
};

// 搜索功能
const handleSearch = () => {
  console.log("搜索关键词:", searchText.value);
};

// 底部导航点击
const handleFooterClick = (item) => {
  activeFooter.value = item.label;
  router.push(item.route);
};

function getList() {
  getMeetingroom.getMeetingroom({ loadMeetingList: true,onlyActiveAndFutureMeetings:true }).then((res) => {
    meetRoomData.value = res.result.map((item) => ({
      name:item.name,
      floor:String(item.floor),
      capacity:item.capacity,
      deviceInfo:item.deviceInfo,
      times:item.meetingList
    }))
  });
}
</script>

<style scoped >
@import "./meetRomm.css";
</style>
