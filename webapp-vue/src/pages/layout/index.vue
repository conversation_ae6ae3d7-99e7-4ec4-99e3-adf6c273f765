<template>
  <div class="container container2" :class="{ 'spin-main': rebootSpin }">
    <!--背景音乐-->
    <MusicPlayer></MusicPlayer>
    <!--头部-->
    <header class="header" :class="{ banner: showBanner }" v-if="!hideHeader" :style="headerStyle">
      <nav class="navbar" v-if="showNavbar || showGoback || extra.length">
        <a v-if="showGoback" class="goback" @click="goBack" href="javascript:void(0)">
          <svg class="icon-16">
            <use xlink:href="#icon-home-前翻-2"></use>
          </svg>
        </a>
        <span class="title">{{ pageTitle }}</span>
        <!--其他操作-->
        <div class="extra" v-if="extra.length">
          <a v-for="(item, idx) in extra" :key="idx" href="javascript:void(0)" @click="handleExtra(item)">
            <svg class="icon-16" v-if="item.icon">
              <use :xlink:href="item.icon"></use>
            </svg>
          </a>
        </div>
      </nav>
    </header>
    <!--主体加载区域-->
    <main class="content" :class="{ 'car-move': hasCarMoveInUrl }">
      <router-view />
    </main>
    <!--底部区域-->
    <footer class="footer" v-if="showFooterNavbar && footerNavbars.length">
      <nav>
        <RouterLink v-for="item of footerNavbars" :to="item.path">
          <svg class="icon-22 active">
            <use :xlink:href="'#icon-car-' + item.icon + '0'"></use>
          </svg>
          <svg class="icon-22 inactive">
            <use :xlink:href="'#icon-car-' + item.icon + '1'"></use>
          </svg>
          <span>{{ item.lable }}</span>
        </RouterLink>
      </nav>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { isNotEmpty } from "@/utils";
import { ref, watch, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { LayoutFooterNavbars } from "./layout-navbar";
import { eventBus } from "@/utils/eventBus";
import MusicPlayer from "@/components/MusicPlayer.vue";
interface ExtraItem {
  path?: string;
  icon: string;
  cb?: () => void;
  eventName?: string; // 通过eventBus 传递
}
const route = useRoute();
const router = useRouter();
// 根据 meta 直接用 computed，自动响应路由变化
const pageTitle = computed(() => (route.meta.title as string) || "");
const showBanner = computed(() => isNotEmpty(route.meta.banner));
const showNavbar = computed(() => route.meta.showNavbar !== false);
const showGoback = computed(() => route.meta.showGoback !== false);
const extra = computed(() => (route.meta.extra as Array<ExtraItem>) || []);
const hideHeader = computed(() => Boolean(route.meta.hideHeader));
// 挪车模块设置背景色
const hasCarMoveInUrl = computed(() => {
  return route.fullPath.includes("car-move") && !route.fullPath.includes('car-move/enter') && !route.fullPath.includes('car-move/notice/list');
});
const showFooterNavbar = computed(() => route.meta?.showFooterNavbar === true)

// 底部导航栏
const footerNavbars = computed(() => {
  if (route.meta && route.meta.showFooterNavbar !== true) {
    return []
  }
  const path = route.fullPath;
  const navbar = LayoutFooterNavbars.find((ele) => path.includes(ele.module));
  return navbar?.navbars || [];
});

const rebootSpin = ref(false);
const routeData = computed(() => route.meta);
watch(
  routeData,
  (newData) => {
    console.log("routeData", newData);
  },
  {
    immediate: true,
    deep: true,
  }
);

const headerStyle = computed(() => {
  const banner = route.meta.banner as string;
  return banner ? { backgroundImage: `url(${banner})` } : {};
});

const handleExtra = (item: ExtraItem) => {
  console.log("handleExtra", item);
  if (item.path) {
    router.push({
      path: item.path,
    });
  } else if (item.eventName) {
    eventBus.emit(item.eventName);
  }
};

const goBack = () => {
  const routeMeta = route.meta;
  if (routeMeta.goBackTo) {
    router.push(routeMeta.goBackTo)
  } else {
    router.back()
  }
}

</script>

<style scoped>
@import "@/styles/layout/index.css";
</style>
