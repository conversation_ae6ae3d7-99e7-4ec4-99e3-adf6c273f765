.car-move-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.car-move-container .search-bg {
  width: 100%;
  /* background: linear-gradient(180deg, #035CC5 0%, #FFFFFF 31%); */
  height: 209px;
  padding: 15px 15px 0 15px;
  flex: 0 210px;
}
.car-move-container .search-bg button{
  height: 40px;
  background-color: #0066DF;
}

.car-move-container .search {
  width: 100%;
  height: 185px;
  border-radius: 10px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px #9292924c;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px 15px 25px 15px;
}
.car-move-container .search .title {
  color: #333333;
  font-weight: bold;
  font-size: 18px;
  line-height: normal;
  letter-spacing: 0px;
  text-align: center;
}
.car-move-container .search .input-group {
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #dddddd;
  border-bottom: 1px solid #dddddd;
  position: relative;
  width: 100%;
  padding-right: 3px;
}
.car-move-container .search .input-group input {
  border: none;
  flex: 1;
  outline: none;
  text-align: right;
}

.car-move-container .search .input-group input:focus,
.car-move-container .search .input-group input:hover {
  border: none;
}

.car-move-container .search .input-group .label {
  flex: 0 0 auto;
  font-size: 12px;
  font-weight: bold;
}
.car-move-container > .car-move-result {
  width: 100%;
  padding: 0 15px;
  padding-top: 14px;
  background: #fff;
  flex: 1;
  overflow-y: auto;
}
.car-move-container .car-move-result .r-title {
  font-weight: bold;
  font-size: 14px;
  text-align: left;
}
.car-move-container .car-move-result .r-info {
  border-bottom: 1px dashed rgba(153, 153, 153, 0.2);
  padding-bottom: 12px;
}
.car-move-container .car-move-result .r-info:not(:first-of-type) {
  padding-top: 12px;
}
.car-move-container .car-move-result .r-info li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30px;
  padding-left: 25px;
  padding-right: 15px;
  position: relative;
}
.car-move-container .car-move-result .r-info li .radio {
  position: absolute;
  left: 0;
}

.car-move-container .car-move-result .r-info li > span.label,
.car-move-container .car-move-result .r-info li > span.val {
  font-size: 14px;
}

.car-move-container .car-move-result .r-info li > span.val {
  color: #666666;
}

.car-move-container .no-data {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.car-move-container .no-data span.tip {
  font-size: 14px;
  color: #999999;
}
