<template>
  <div class="notice-container">
    <div class="content">
      <n-space vertical>
        <n-space>
          <span class="title span-title">通知挪车</span>
        </n-space>
        <n-space style="padding-top: 16px; padding-bottom: 16px">
          <n-radio :checked="ifNotice === '0'" value="0" size="large" name="ifNotice" @change="ifNoticeChange">
            是
          </n-radio>
          <n-radio :checked="ifNotice === '1'" value="1" name="ifNotice" @change="ifNoticeChange" size="large">
            否
          </n-radio>
        </n-space>
        <n-space vertical v-if="ifNotice == '0'">
          <n-checkbox v-model:checked="sms"> 短信通知 </n-checkbox>
          <!-- <n-checkbox v-model:checked="roboot"> 机器人语音电话通知 </n-checkbox> -->
        </n-space>
      </n-space>
    </div>
    <div class="footer">
      <n-button style="width: 50%" type="primary" @click="saveData" v-if="!show">
        通知
      </n-button>
      <n-button style="margin-left: 10px; width: 50%" @click="goBack"> 取消 </n-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { NSpace, NRadio, NCheckbox, NButton, useMessage } from "naive-ui";
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { save } from "@/service/noticefeedback.service";
// 在你的组件中
const route = useRoute();
const data = route.query;
const ifNotice = ref(<string>"0");
const sms = ref(<boolean>false);
// const roboot = ref(<boolean>true);
const message = useMessage();
const router = useRouter();
const show = ref<boolean>(false);
const ifNoticeChange = (e: Event) => {
  ifNotice.value = (e.target as HTMLInputElement).value;
  if (ifNotice.value == '1') {
    show.value = true;
  } else {
    show.value = false;
  }
};
const saveData = async () => {
  // const methods: Array<string> = [];
  if (!sms.value) {
    message.error("请选择通知方式");
    return
  }
  // if (sms.value) methods.push("短信通知");
  // if (roboot.value) methods.push("机器人语音电话通知");
  // data.noticeMethod = methods.join("、");
  data.noticeMethod = "短信通知"
  data.vehicleInfoId = data.id;
  delete data.id;
  delete data.licensePlateNumber;
  try {
    await save(data);
    message.success("通知成功");
    router.push("/car-move");
  } catch (e) {
    message.error("通知失败");
  }
};
const goBack = async () => {
  router.push("/car-move");
}

onMounted(async () => { });
</script>
<style scoped>
.notice-container {
  width: 100%;
  height: 100%;
  padding: 15px 15px 26px 15px;
  display: flex;
  flex-direction: column;
  background: #fff;
  font-size: 14px;
}
.n-checkbox,
.n-radio {
  font-size: 14px;
}

.notice-container .content {
  flex: 1;
}

.notice-container .footer {
  flex: 0;
  display: flex;
  justify-content: center;

}

.notice-container .footer [n-button] {
  height: 40px !important;
  width: 50%;
}
</style>
