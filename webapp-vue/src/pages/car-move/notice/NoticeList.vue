<template>
  <div class="notice-list-container">
    <div class="item" v-for="item of list" :key="item.id">
      <div class="title">
        <div style="display: inline-flex; align-items: center">
          <svg class="icon-16">
            <use xlink:href="#icon-car-notice"></use>
          </svg>
          <span class="label" style="margin-left: 8px">通知反馈</span>
        </div>
        <a
          href="javascript:void(0)"
          style="display: inline-flex; align-items: center"
          @click="deleteItem(item)"
        >
          <svg class="icon-16">
            <use xlink:href="#icon-car-delete"></use>
          </svg>
        </a>
      </div>
      <div class="content">
        <ul>
          <li>
            <span class="label">姓名</span
            ><span class="val">{{ item.name }}</span>
          </li>
          <li>
            <span class="label">职务</span
            ><span class="val">{{ item.position }}</span>
          </li>
          <li>
            <span class="label">手机号码</span
            ><span class="val">{{ item.phone }}</span>
          </li>
          <li>
            <span class="label">车牌号</span
            ><span class="val">{{ item.licensePlateNumber }}</span>
          </li>
          <li>
            <span class="label">通知方式</span
            ><span class="val">{{ item.noticeMethod }}</span>
          </li>
          <li>
            <span class="label">通知时间</span
            ><span class="val">{{ item.noticeTime }}</span>
          </li>
          <li>
            <span class="label">反馈</span
            ><span class="val danger-text">{{ item.feedbackContent }}</span>
          </li>
        </ul>
      </div>
      <div class="actions">
        <n-button
          style="width: 100%; height: 38px"
          type="primary"
          @click="infoItem(item)"
          >再次通知</n-button
        >
      </div>
    </div>
    <div v-if="!list.length" class="noData">
      <BaseEmpty>
        <span style="color: #999; font-size: 14px"> 暂无反馈数据 </span>
      </BaseEmpty>
    </div>
    
  </div>

  <!-- 统一的确认对话框 -->
  <ConfirmDialog
    v-if="showConfirm"
    @close="showConfirm = false"
    @confirm="handleConfirm"
    >{{ confirmMessage }}</ConfirmDialog
  >
</template>

<script lang="ts" setup>
import BaseEmpty from "@/components/BaseEmpty.vue";
import type { NoticeFeedback } from "@/models/Noticefeedback";
import {
  deleteByAdminDisplayAndId,
  getListAll,
  deleteListByAdminDisplay,
} from "@/service/noticefeedback.service";
import { NButton, useMessage } from "naive-ui";
import { onMounted, onUnmounted, ref } from "vue";
import { eventBus } from "@/utils/eventBus";
import { useRouter } from "vue-router";
import ConfirmDialog from "@/components/ConfirmDialog.vue";

const showConfirm = ref(false);
const currentId = ref(""); // 点击的id
const confirmType = ref<"delete" | "clear">(); // 确认类型
const confirmMessage = ref(""); // 确认消息
const message = useMessage();
const list = ref(<NoticeFeedback[]>[]);
const router = useRouter();

onMounted(async () => {
  await getList();
  eventBus.on("clearNoticeList", () => {
    showConfirmDialog("clear", "您确定要清空所有通知反馈记录吗?");
  });
});

onUnmounted(() => {
  eventBus.off("clearNoticeList");
});

const getList = async () => {
  try {
    list.value = await getListAll({ ifPage: false });
  } catch (e) {}
};

const deleteItem = async (item: NoticeFeedback) => {
  showConfirmDialog("delete", "您确定要删除当前通知反馈记录吗?", item.id);
};

const infoItem = async (item: NoticeFeedback) => {
  router.push({
    path: "/car-move/notice/way",
    query: {
      ...item,
    },
  });
};

// 显示确认对话框
const showConfirmDialog = (
  type: "delete" | "clear",
  message: string,
  id?: string
) => {
  confirmType.value = type;
  confirmMessage.value = message;
  if (id) currentId.value = id;
  showConfirm.value = true;
};

// 处理确认操作
const handleConfirm = async () => {
  if (!showConfirm.value) return;

  try {
    if (confirmType.value === "delete") {
      if (!currentId.value) return;
      await deleteByAdminDisplayAndId(currentId.value);
      message.success("删除成功");
    } else if (confirmType.value === "clear") {
      await deleteListByAdminDisplay();
      message.success("清除成功");
    }
    await getList();
  } catch (e) {
    message.error(confirmType.value === "delete" ? "删除失败" : "清除失败");
  } finally {
    showConfirm.value = false;
  }
};
</script>

<style scoped>
@import "./notice-list.css";
</style>
