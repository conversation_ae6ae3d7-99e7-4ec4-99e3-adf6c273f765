<template>
    <div class="notice-container" v-show="showModal">
        <div class="notice-inner">
            <div class="notice-warn-container">
                <svg class="icon-16 close-icon" @click="showModal = false">
                    <use xlink:href="#icon-meeting-16-7"></use>
                </svg>
                <template v-for="data in dataList" :key="data.id">
                    <n-space vertical>
                        <span class="title">{{ data.licensePlateNumber }}车主您好</span>
                        <p class="content_time">{{ data.noticeTime }}</p>
                        <p class="content" style="color: #666666;">
                            您的车目前停放位置造成他人车辆出行不便，请您尽快前往挪车，感谢您的配合。
                        </p>
                    </n-space>
                    <div class="card-box">
                        <div class="card-content">
                            <n-space vertical>
                                <span class="title">您的处理</span>
                                <div class="card-content_main">
                                    <!-- <n-space vertical :size="[0, 15]"> -->
                                        <sl-radio v-model="data.handleWay" :value="0">
                                            <span class="content">
                                                立即挪车
                                            </span>
                                        </sl-radio>
                                        <sl-radio v-model="data.handleWay" :value="1">
                                            <span class="content">
                                                稍后挪车
                                            </span>
                                        </sl-radio>
                                        <div v-if="data.handleWay === 1"
                                            style="display: flex;align-items: center;padding-left: 35px;">
                                            <span class="text">时间：</span>
                                            <n-select style="flex:1" v-model:value="data.afterTimes"
                                                :options="timesOptions" />
                                        </div>
                                        <sl-radio v-model="data.handleWay" :value="2">
                                            <span class="content">
                                                暂不方便挪车
                                            </span>
                                        </sl-radio>
                                    <!-- </n-space> -->
                                </div>
                            </n-space>
                        </div>
                        <div class="card-footer">
                            <n-button type="primary" style="width: 100%;" @click="onConfirm(data)">
                                确定
                            </n-button>
                        </div>
                    </div>
                </template>
                <div class="empty-container" v-if="dataList.length === 0">
                    <BaseEmpty>
                        <span style="color: #999;font-size: 14px;">
                            暂无挪车提醒
                        </span>
                    </BaseEmpty>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import BaseEmpty from '@/components/BaseEmpty.vue';
import SlRadio from '@/components/SlRadio.vue';
import type { NoticeFeedback } from '@/models/Noticefeedback';
import { getListTip, saveFeedback } from '@/service/noticefeedback.service';
import { NButton, NSelect, NSpace, useMessage } from 'naive-ui';
import { onMounted, ref } from 'vue';
// 方式
// const handleWay = ref(<number>0)
// 方式2 稍后挪车
// const afterTimes = ref(<number>10)
const dataList = ref<(NoticeFeedback & { handleWay: number, afterTimes: number })[]>([])
const timesOptions = ref([
    {
        label: '10 分钟',
        value: 10,
    },
    {
        label: '20 分钟',
        value: 20,
    },
    {
        label: '30 分钟',
        value: 30,
    },
    {
        label: '60 分钟',
        value: 60,
    }
])
const showModal = ref(false)
onMounted(() => {
    getList()
})
function getList() {
    getListTip().then(res => {
        const datas: (NoticeFeedback & { handleWay: number, afterTimes: number })[] = res.map(ele => {
            return {
                ...ele,
                handleWay: 0,
                afterTimes: 10
            }
        }) || []
        if (datas?.length) {
            showModal.value = true
            dataList.value = [datas[0]] // 2025年5月22日 只显示第一条，反馈成功后重新getList再显示下一条
        } else showModal.value = false
    }).catch(err => {
        message.error(err || '系统异常！')
        showModal.value = false
    })
}
const message = useMessage()
function onConfirm(data: NoticeFeedback & { handleWay: number, afterTimes: number }) {
    const { handleWay, afterTimes } = data
    saveFeedback({
        // personnelId: data.value.personnelId,
        // vehicleInfoId: data.value.vehicleInfoId,
        id: data.id,
        feedbackContent: handleWay == 0 ? '立即挪车' : handleWay == 1 ? `稍后挪车${afterTimes}分钟` : `暂不方便挪车`,
    }).then(_ => {
        message.success('反馈成功！')
        // 刷新当前列表
        getList()
        // router.back()
    })
}
</script>
<style scoped>
.notice-warn-container {
    padding: 15px 15px 0 15px;
    background: #F4F6FA;
    /* height: 100vh; */
    width: 100%;
    height: 100%;
    border-radius: 10px;
    background: linear-gradient(180deg, #D8E2FF 0%, #fff 20%, #FFFFFF 100%);
    box-shadow: 0px 0px 10px 0px #9292924C;
    display: flex;
    flex-direction: column;
    position: relative;
}

.notice-warn-container .title {
    color: #333333;
    font-weight: bold;
    font-size: 16px;
}
.n-button {
    height: 38px;
}
/* .notice-warn-container .card { */
/* margin: 20px 0; */
/* } */

.notice-warn-container .card .title {
    font-size: 14px;
}

.empty-container {
    height: calc(100vh - 60px);
}

.content_time {
    font-size: 12px;
    color: #999;
}

.content {
    font-size: 14px;
}

.notice-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* background-color: rgb(204, 204, 204, 0.5); */
    background-color: rgba(0, 0, 0, 0.4);
    /* 半透明遮罩 */
    z-index: 10;
    /* border-radius: 10px; */
}

.notice-inner {
    position: absolute;
    width: 290px;
    height: 400px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.card-box {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    margin-top: 5px;
}

.close-icon {
    position: absolute;
    top: 18px;
    right: 15px;
}

.text {
    font-size: 12px;
}
.card-content {
    padding-top: 15px;
    height: 210px;
}

.card-content_main {
    padding-left: 80px;
    display: flex;
    flex-direction: column;
    row-gap: 20px;
}
:deep(.n-base-selection-input){
    font-size: 12px !important;
}
</style>