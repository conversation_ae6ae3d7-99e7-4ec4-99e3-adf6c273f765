.notice-list-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 15px;
}

.notice-list-container .item {
  width: 100%;
  height: 326px;
  border-radius: 10px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px #1d18184c;
  display: flex;
  flex-direction: column;
  padding-bottom: 25px;
  margin-bottom: 10px;
}
.notice-list-container .item .title {
  height: 40px;
  width: 100%;
  padding: 0 15px;
  display: flex;
  align-items: center;
  border-bottom: var(--card-bottom-line-style);
  font-size: 14px;
  justify-content: space-between;
}

.notice-list-container .item .title .label {
  color: var(--text-primary);
}
.notice-list-container .item .content {
  margin-top: 10px;
  padding: 0 15px;
  flex: 1;
}
.notice-list-container .item .content ul {
  display: flex;
  flex-direction: column;
}
.notice-list-container .item .content ul li {
  display: flex;
  height: 27px;
  align-items: center;
}
.notice-list-container .item .content ul li .label,
.notice-list-container .item .content ul li .val {
  font-size: 12px;
}
.notice-list-container .item .content ul li .label,
.notice-list-container .item .content ul li .val:not(.danger-text) {
  color: #666666;
}


.notice-list-container .item .content ul li .label {
  flex: 0 70px;
}
.notice-list-container .item .content ul li .val {
  flex: 1;
}
.notice-list-container .item .actions {
  height: 40px;
  padding: 0 15px;
  flex: 0 40px;
  width: 100%;
}

.notice-list-container .item .actions [n-button] {
  width: 100%;
}
.noData{
  width: 100%;
  height:calc( 100vh - 74px);
  display: flex;
  justify-content: center;
  align-items: center;
}