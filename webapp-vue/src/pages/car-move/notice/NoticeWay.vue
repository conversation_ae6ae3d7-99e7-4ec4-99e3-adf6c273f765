<template>
  <div class="notice-way-container">
    <n-space vertical>
      <span class="title">通知方式</span>
      <n-checkbox v-model:checked="sms"> 短信通知 </n-checkbox>
      <!-- <n-checkbox v-model:checked="roboot"> 机器人语音电话通知 </n-checkbox> -->
    </n-space>
    <div class="footer">
        <n-button type="primary" @click="onConfirmCancel()">
          通知
        </n-button>
        <n-button @click="cancel"> 取消 </n-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { NCheckbox, NSpace, useMessage, NButton } from "naive-ui";
import { ref, watch, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { save } from "@/service/noticefeedback.service";
const route = useRoute();
const router = useRouter();
const query = route.query;
const sms = ref(true);
const roboot = ref(false);
const message = useMessage();

// 处理方法
const handleNoticeMethod = () => {
  sms.value = false;
  roboot.value = false;
  if (query.noticeMethod && typeof query.noticeMethod === "string") {
    const methods = query.noticeMethod.split("、"); // 按逗号分隔
    // 检查是否包含 "短信通知"
    if (methods.includes("短信通知")) {
      sms.value = true;
    }
    // 检查是否包含 "机器人语音电话通知"
    // if (methods.includes("机器人语音电话通知")) {
    //   roboot.value = true;
    // }
  }
};

// 监听路由变化
watch(
  () => route.query,
  () => {
    handleNoticeMethod();
  },
  { immediate: true }
);
// 监听变化，确保至少一个选中
watch([sms, roboot], ([newSms, newRoboot]) => {
  if (!newSms && !newRoboot) {
    // sms.value = true; // 自动重新选中短信通知
  }
});
// 初始化时处理
onMounted(() => {
  handleNoticeMethod();
});

async function onConfirmCancel() {
  if (!sms.value){
    message.error('请选择通知方式')
    return
  }
  const data = { ...query };
  delete data.noticeTime;
  delete data.feedbackContent;
  // const methods = [];
  // if (sms.value) methods.push("短信通知");
  // if (roboot.value) methods.push("机器人语音电话通知");
  // data.noticeMethod = methods.join("、");
  data.noticeMethod="短信通知"
  try {
    await save(data);
    message.success('通知成功')
    router.push("/car-move/notice/list");
  } catch (e) {
    message.error('通知失败')
  }
}
const cancel = () => {
  router.push("/car-move/notice/list");
}
</script>
<style scoped>
.notice-way-container {
  padding: 10px 15px 0;
  height: 100%;
  background: #F4F6FA;
  position: relative;
  font-size: 14px;
}
.n-checkbox{
  font-size: 14px;
}

.notice-way-container .title {
  color: #333333;
  font-weight: bold;
  font-size: 14px;
}

.notice-way-container .footer {
  width: 100%;
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: 0;
  left: 0;
  margin-bottom: 26px;
}

.notice-way-container .footer .n-button {
   width: 140px;
}

.notice-way-container .footer .n-button+.n-button {
  margin-left:10px;
}

/* n-button {
  width: 140px;
  height: 40px;
  border-radius: 3px;
  background: #0066df;
  text-align: center;
  line-height: 40px;
  color: #ffffff;
  font-size: 14px;
} */
/* 
[n-button]:nth-child(2) {
  border-radius: 3px;
  background: #ffffff;
  border: 1px solid #0066df;
  color: #0066df;
} */
</style>