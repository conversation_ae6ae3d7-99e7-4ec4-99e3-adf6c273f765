<template>
    <div class="car-add-container">
        <form>
            <div class="content">
                <div class="input-group">
                    <span class="label">姓名：</span>
                    <input readonly type="text" v-model="person.name" />
                </div>
                <div class="input-group">
                    <span class="label">职务：</span>
                    <input readonly type="text" v-model="person.position" />
                </div>
                <div class="input-group">
                    <span class="label">手机号码：</span>
                    <input readonly type="text" v-model="person.phone" />
                </div>
                <template v-if="vehicleinfoList.length">
                    <div class="input-group" v-for="(item, index) of vehicleinfoList" :key="index">
                        <span class="label">车牌号{{ vehicleinfoList.length > 1 ? index + 1 : '' }}：</span>
                        <input type="text" v-model="item.licensePlateNumber" placeholder="请输入车牌号" />
                    </div>
                </template>
                <div class="input-group" v-else>
                    <span class="label">车牌号：</span>
                    <input readonly type="text" :value="'无'" />
                </div>
            </div>
            <!--  -->
            <div class="actions">
                <n-button class="actions_add" @click.stop.prevent="addVehicleinfo"> 新增车牌号 </n-button>
                <!-- 
                <n-button type="default" class="add" @click="addVehicleinfo">

                </n-button> -->
            </div>
            <div class="footer">
                <n-button type="primary" @click="save">
                    确定
                </n-button>
                <n-button style="margin-left: 10px;" @click="cancel">
                    取消
                </n-button>
            </div>
        </form>
    </div>
</template>
<script setup lang="ts">
import type { Personnel, Vehicle } from '@/models/CarMove';
import { getInfoPersonnel, saveVehicleinfo } from '@/service/car-move.service';
import { isEmpty } from '@/utils';
import { NButton, useMessage } from 'naive-ui'
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
const props = defineProps<{ id?: string }>()
const vehicleinfoList = ref(<Vehicle[]>[])
const person = ref(<Personnel>{
    name: '',
    position: '',
    phone: '',
    vehicleinfoList: []
})
const router = useRouter()
onMounted(() => {
    console.log("🚀 ~ onMounted ~ props:", props.id)
    if (props.id) getInfo()
})
const getInfo = () => {
    getInfoPersonnel(props.id!).then(res => {
        person.value = res
        vehicleinfoList.value = res.vehicleinfoList || []
    })
}
const cancel = () => {
    router.back()
}
const message = useMessage()
const save = async () => {
    try {
        const isEmptyArr = vehicleinfoList.value.filter(ele => isEmpty(ele.licensePlateNumber))
        if (isEmptyArr.length) {
            message.warning('请填写车牌号')
            return;
        }
        vehicleinfoList.value.forEach(async (item) => {
            if (!item.id) {
                saveVehicleinfo(item).then(() => {
                    message.success('保存成功')
                    router.back()
                }).catch((err) => {
                    message.error(err||'保存失败')
                    console.log(err,'res') 
                })
            //    const info = await saveVehicleinfo(item)
            }
        })
        // message.success('保存成功')
        // router.back()
    } catch (error) {
        // message.success('保存失败')
    }
}
const addVehicleinfo = () => {
    vehicleinfoList.value.push({
        licensePlateNumber: '',
        id: '',
        personnelId: person.value.id! // 车主 id
    })
}

</script>
<style scoped>
.car-add-container {
    width: 100%;
    height: 100%;
}

form {
    display: flex;
    height: 100%;
    flex-direction: column;
}

form .content {
    display: flex;
    flex-direction: column;
    padding: 0 15px;
    border: 1px dashed #ddd;
    background-color: #fff;
}

form .actions {
    display: flex;
    justify-content: center;
    width: 100%;
    padding: 30px 15px;
    box-shadow: 0px 0px 4px 0px #92929233;
    background: #fff;
    flex: 0;
}

.actions_add {
    height: 40px;
    width: 290px;
    background-color: rgba(69, 91, 255, 0.1);
    border-color: rgba(69, 91, 255, 0.6);
    color: #0066DF
}

form .footer {
    display: flex;
    width: 100%;
    justify-content: center;
    flex: 1;
    align-items: flex-end;
    padding-bottom: 26px;
}

form .footer button {
    width: 140px;
    height: 41px;
}

.add {
    width: 290px;
    height: 40px;
    background: rgba(69, 91, 255, 0.1);
    border: 1px solid rgba(69, 91, 255, 0.6);
    color: rgba(69, 91, 255, 1);
}
</style>