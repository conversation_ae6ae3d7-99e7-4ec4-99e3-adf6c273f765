<template>
    <div class="car-move-container">
        <div class="search-bg">
            <div class="search">
                <div class="title">
                    <span>信息查询</span>
                </div>
                <div class="input-group">
                    <span class="label">车牌号/姓名：</span>
                    <input style="width: 100%;" type="text" v-model="keyWord" placeholder="请输入" />
                </div>
                <button type="button" @click="search" class="btn btn-primary lg">开始查询</button>
            </div>
        </div>

        <div class="car-move-result" v-if="personList.length">
            <div class="r-title">
                <span>查询结果</span>
            </div>
            <ul class="r-info" v-for="person of personList">
                <li><span class="label">姓名：</span><span class="val">{{ person.name }}</span></li>
                <li><span class="label">职务：</span><span class="val">{{ person.position }}</span></li>
                <li>
                    <span class="label">手机号码：</span><span class="val">{{ person.phone }}</span>
                </li>
                <li v-for="(item) of person.vehicleinfoList" :key="item.id">
                    <sl-radio class="radio" v-model="selectedId" :value="item.id"
                        @change="lpnChange(item)"></sl-radio>
                    <span class="label">车牌号：</span>
                    <span class="val">{{ item.licensePlateNumber }}</span>
                </li>
            </ul>
        </div>
        <div class="no-data" v-else>
            <svg class="icon-40">
                <use xlink:href="#icon-car-no-data"></use>
            </svg>
            <span class="tip">{{ ifInit ? '暂无查询' : '暂无数据' }}</span>
        </div>
    </div>
</template>
<script setup lang="ts">
import SlRadio from '@/components/SlRadio.vue';
import type { Personnel } from '@/models/CarMove';
import { getListByKeyWord } from '@/service/car-move.service';
import { useCarMoveStore } from '@/stores/car-move.store';
import { isEmpty } from '@/utils';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter()
const store = useCarMoveStore()
const selectedId = ref(<string>'')
const spin = ref(<boolean>false)
// 查询结果
// const personList = ref(<Personnel[]>[])
// 关键字查询（姓名、车牌号）
// const keyWord = ref(<string>(""))

const keyWord = ref<string>(store.carmoveKeyword)
const personList = ref<Personnel[]>([...store.personList])

const ifInit = ref(<boolean>true)
onMounted(() => {
    if (store.personList.length > 0) {
        ifInit.value = false
    }
})

const search = async () => {
    await getList(keyWord.value)
}
const getList = async (keyWord: string) => {
    if (isEmpty(keyWord) && personList.value.length == 0) return
    spin.value = true
    try {
        const list = await getListByKeyWord(keyWord)
        personList.value = list
        spin.value = false
        ifInit.value = false
        // 把结果写回 store
        store.setKeyword(keyWord)
        store.setPersonList(list)
    } catch (msg) {
        ifInit.value = false
        spin.value = false
        personList.value = []
        store.setKeyword(keyWord)
        store.setPersonList([])
    }
}

const lpnChange = async (val: any) => {
    console.log('va', val)
    router.push({
        name: 'CarMoveNotice',
        query: val
    })
}

</script>
<style scoped>
@import "./car-move.css";
</style>