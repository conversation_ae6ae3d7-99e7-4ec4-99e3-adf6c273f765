<template>
    <div class="p-main-vessel">
        <div class="search">
            <input type="text" v-model="search.keyword" placeholder="请输入姓名/首字母" />
            <svg class="icon-16 icon-search" @click="toGetList()">
                <use xlink:href="#icon-car-search"></use>
            </svg>
        </div>
        <div class="p-content">
            <div v-for="item in personnelDatas" class="select-p" :key="item._uuid">
                <div class="row-0">
                    <span class="dept-icon" v-if="item._type == 2">外</span>
                    {{ item.name }}  
                    <svg class="icon-12" @click="item._open = !item._open">
                        <use xlink:href="#icon-meeting-12-9" v-if="item._open"></use>
                        <use xlink:href="#icon-meeting-12-1" v-else></use>
                    </svg>
                </div>
                <!-- <template v-for="(_,subIdx) in item.members" :key="subIdx"> -->
                <n-virtual-list v-if="item._open" item-resizable :item-size="42" :items="item.children" key-field="id">
                    <template #default="{ item: vItem }">
                        <div class="row-t" @click="onChangeItem(vItem)">
                            <div class="round" :style="{ background: item._type == 2 ? '#999999' : '#4f7af6' }">
                                {{ vItem.name![0] }}</div>
                            <div
                                style="flex:1;border-bottom: 1px solid rgba(153, 153, 153, 0.2);display: flex;align-items: center;justify-content: space-between;">
                                <div class="row-name">
                                    {{ vItem.name }}
                                </div>
                                <svg class="icon-12">
                                    <use xlink:href="#icon-meeting-12-1"></use>
                                </svg>
                            </div>
                        </div>
                    </template>
                </n-virtual-list>
                <!-- </template> -->
            </div>
            <BaseEmpty v-if="!personnelDatas.length" />
        </div>
    </div>
</template>

<script setup lang="ts">
import BaseEmpty from '@/components/BaseEmpty.vue';
import type { Personnel } from '@/models/MettingHandle';
import { PersonnelSearch, type PersonnelAndOrganization2 } from '@/models/Personnel';
import { getList } from '@/service/car-move.service';
import { NVirtualList, useMessage, type MessageReactive } from 'naive-ui';
import { onMounted, onUnmounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { generatePersonnelList, type PersonnelAndOrganizationGroup } from '../meet-assistant/metting-handle/meeting-handle.data';
const personnelDatas = ref<PersonnelAndOrganizationGroup[]>([])
const router = useRouter()
onMounted(() => {
    toGetList()
})
onUnmounted(() => {
    loadingMsg?.destroy()
})
function onChangeItem(item: Personnel) {
    router.push({ name: 'car-move-add', params: { id: item.id } })
}
const search = ref<PersonnelSearch>(new PersonnelSearch())
const message = useMessage()
let loadingMsg: MessageReactive | null = null
function toGetList() {
    if (loadingMsg != null) {
        loadingMsg?.destroy()
        loadingMsg = null
    }
    loadingMsg = message.loading('数据加载中...')
    search.value.ifPage = false
    search.value.groupByOrganization = true
    // search.value.excludeCurrentUser = true
    search.value.groupByWorkgroup = false
    search.value.ifGrouped = true
    getList(search.value).then(res => {
        const datas = res as PersonnelAndOrganization2
        const dataList = generatePersonnelList(datas)
        personnelDatas.value = dataList || []
    }).finally(() => {
        loadingMsg?.destroy()
        loadingMsg = null
    })
}
</script>

<style scoped>
.row-0 {
    color: #666;
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;
    font-size: 14px;
}

.p-main-vessel .p-top {
    width: 100%;
    height: 64px;
    text-align: center;
    color: white;
    background: #0558bb;
    opacity: 1;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    font-size: 16px;
}

.p-main-vessel .p-top i {
    top: 50%;
    display: block;
    width: 9px;
    height: 16px;
    background-size: cover;
}

.p-main-vessel .p-top i:nth-child(1) {
    background-size: cover !important;
    background: url("@/assets/image/meeting/back.png") center no-repeat;
    left: 15px;
}

.p-main-vessel .p-top i:last-child {
    background-size: cover !important;
    background: url("@/assets/image/meeting/add.png") center no-repeat;
    right: 15px;
    width: 16px;
    height: 16px;
}

.p-main-vessel .search {
    width: 100%;
    height: 51px;
    flex-shrink: 0;
    padding: 0 15px;
    background: #035bc3;
    padding-top: 9px;
    position: relative;
    display: flex;
    align-items: center;
}

.p-main-vessel .search input {
    outline: none;
    height: 30px;
    width: 100%;
    padding: 6px 10px;
    opacity: 1;
    border-radius: 3px;
    border: 1px solid rgba(153, 153, 153, 0.5);
    color: #999999;
    font-size: 14px;
}

.icon-search {
    position: absolute;
    right: 25px;
    top: 22px;
}

.p-main-vessel .p-content {
    width: 100%;
    height: calc(100vh - 145px);
    padding: 10px 15px;
    overflow-y: auto;
    background-color: #fff;
}

.p-main-vessel .p-content .dept-icon {
    color: #3e75fe;
    font-size: 10px;
    width: 16px;
    height: 16px;
    line-height: 16px;
    border: 1px solid #3e75fe;
    text-align: center;
    border-radius: 9px;
    margin-right: 9px;
}

.p-main-vessel .p-content .select-p {
    margin-bottom: 14px;
}

.p-main-vessel .p-content .select-p .row-o {
    color: #666666;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.p-main-vessel .p-content .select-p .row-o i {
    display: block;
    width: 12px;
    height: 12px;
    background: url("@/assets/image/meeting/down.png") center no-repeat;
    background-size: cover;
    margin-left: 7px;
}

.p-main-vessel .p-content .select-p .row-t {
    display: flex;
    margin-bottom: 6px;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.row-t:hover {
    background-color: rgba(125, 159, 255, 0.2);
}

.p-main-vessel .p-content .select-p .row-t i {
    display: block;
    width: 20px;
    height: 20px;
    margin-right: 7px;
}

.p-main-vessel .p-content .select-p .row-t .round {
    width: 34px;
    height: 34px;
    background: #4f7af6;
    border-radius: 17px;
    color: #ffffff;
    font-size: 14px;
    text-align: center;
    line-height: 34px;
}

.p-main-vessel .p-content .select-p .row-t .row-name {

    width: 74%;
    line-height: 34px;
    margin-left: 8px;
    color: #333333;
    font-size: 14px;
}
</style>