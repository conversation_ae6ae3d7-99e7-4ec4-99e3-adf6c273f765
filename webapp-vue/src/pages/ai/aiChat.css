/**定义变量*/
:root {
    --icon-size-16: 16px;
    --icon-size-14: 14px;
    --font-size-14: 14px;
    --history-input-hight: 2.5em;
    --history-input-radius: 1.25em;
    --input-height: 3em;
    --input-radius: 1.5em;
}

.all {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

.chat-main {
    width: 100%;
    height: 100%;
    padding-bottom: 49px;
    flex-grow: 1;
    flex-shrink: 1;
    display: flex;
    font-size: var(--font-size-14);
    touch-action: none;
    overflow: hidden;
}

.chat-copy {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 132px;
    height: 44px;
    transform: translate(-50%, -50%);
    opacity: 1;
    border-radius: 3px;
    background: #33333399;
    color: #78E542;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px 1em;
}

.chat-copy i {
    display: block;
    width: 2em;
    height: 2em;
    margin-right: 1em;
    background-repeat: no-repeat;
    background-position: center;
    background-image: url('/src/assets/image/chat/17.svg');
}

input {
    /* outline-style: none; */
    border: none;
    outline-width: 1px;
    outline-color: #7D9FFF;
    font-size: 1em;
}

/**---历史记录部分---*/
.chat-history {
    display: none;
    width: 0%;
    height: 100%;
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    position: relative;
    justify-content: center;
    border-radius: 0px;
    background: #FFF;
}

.chat-history.show {
    animation: show 0.3s linear 100ms 1 normal forwards;
}

.chat-history.none {
    width: 75%;
    animation: hidden 0.3s linear 100ms 1 normal forwards;
}

@keyframes show {
    0% {
        width: 0%;
    }

    100% {
        width: 75%;
    }
}

@keyframes hidden {
    0% {
        width: 75%;
    }

    100% {
        width: 0%;
    }
}

.chat-history.show::after {
    content: '';
    display: flex;
    position: absolute;
    right: 0px;
    transform: translateX(100%);
    z-index: 1;
    width: 100vw;
    height: 100%;
    opacity: 1;
    border-radius: 0px;
    background: #33333399;
}


.history-search {
    width: 100%;
    height: 5em;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    flex-grow: 0;
    padding: 0em 1em;
}

.history-input {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    height: var(--history-input-hight);
    border-radius: var(--history-input-radius);
    background: #E7EAF0;
}

.history-search input {
    width: 100%;
    height: 100%;
    background-color: transparent;
    border-radius: var(--history-input-radius);
    padding: 0em 3em 0em 1em;
}

.history-search #history-send {
    position: absolute;
    right: 1em;
    width: var(--icon-size-16);
    height: var(--icon-size-16);
    background: url('/src/assets/image/chat/search.svg') no-repeat center center;
}

.history-list {
    width: 100%;
    height: 50%;
    flex-grow: 1;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 0em 1em;
    overflow-y: scroll;
    touch-action: pan-y !important;
}

.history-list div {
    width: 100%;
    height: 1.5em;
    display: flex;
    align-items: center;
}

.list-time {
    opacity: 1;
    color: #999999;
    font-family: Source Han Sans;
    font-weight: regular;
    font-size: 1em;
    line-height: normal;
    letter-spacing: 0px;
    text-align: left;
}

.history-item {
    opacity: 1;
    color: #333333;
    font-size: 1em;
    line-height: normal;
    letter-spacing: 0px;
    padding: 1.2em 1em;
}

.history-item span {
    overflow: hidden;
    white-space: nowrap;
    word-break: break-all;
    text-overflow: ellipsis;
}

.history-item.active {
    opacity: 1;
    border-radius: 8px;
    background: #7D9FFF33;
    color: #455BFF;
}


/**主chat部分*/
.aichat {
    flex-grow: 0;
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    font-size: 12px;
    position: relative;
    justify-content: center;
    border-radius: 0px;
    background: url("/src/assets/image/chat/back.svg");
    background-repeat: no-repeat;
    background-size: cover;
}

.chat-head {
    height: 3em;
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 1em;
}

.history {
    width: var(--icon-size-16);
    height: var(--icon-size-16);
    background: url('/src/assets/image/chat/history.svg') no-repeat center center;
    background-size: cover;
}

.newchat {
    width: var(--icon-size-16);
    height: var(--icon-size-16);
    background: url('/src/assets/image/chat/newchat.svg') no-repeat center center;
    background-size: cover;
}

.chat-reply {
    height: 50%;
    width: 100%;
    flex-grow: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    position: relative;
}

.chat-reply-robot {
    background: url('/src/assets/image/chat/robot.png') no-repeat center center;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.chat-reply-robot span {
    position: absolute;
    width: 100%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) translateY(80px);
    text-align: center;
    color: #999999;

}

.chat-input {
    width: 100%;
    height: 6em;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    flex-grow: 0;
}

#replyed {
    display: flex;
    flex-direction: column;
}

.result {
    background-color: #FFF;
    color: #333;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: justified;
    border-radius: 0.5em;
    width: fit-content;
    max-width: 90%;
    padding: 0.5em 1.2em;
    margin: 1em 2em;
}

.result.ai {
    border-top-left-radius: 0em;
    align-self: flex-start;
    position: relative;
    margin-bottom: 2em;
}

.result.ai::before {
    display: flex;
    content: "";
    width: 20px;
    height: 20px;
    background: url('/src/assets/image/chat/4.svg') no-repeat center center;
    background-size: cover;
    position: absolute;
    top: 0px;
    transform: translate(-100%, -100%);
}

.result.ai .marks {
    position: absolute;
    padding: 5px 7px 5px 15px;
    bottom: 0px;
    transform: translate(-1.2em, 2.5em);
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    background-color: #FFFFFFCC;
    ;
    border-radius: 1em;
}

.marks i {
    display: block;
    width: var(--icon-size-14);
    height: var(--icon-size-14);
    margin-right: 8px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    background-color: #FFF;
}

.marks i:nth-child(1) {
    background-image: url('/src/assets/image/chat/21.svg');
}

.marks i:nth-child(2) {
    background-image: url('/src/assets/image/chat/22.svg');
}

.marks i:nth-child(3) {
    background-image: url('/src/assets/image/chat/24.svg');
}

.marks i:nth-child(3).active {
    background-image: url('/src/assets/image/chat/23.svg');
}

.marks i:nth-child(4) {
    background-image: url('/src/assets/image/chat/25.svg');
}

.marks i:nth-child(4).active {
    background-image: url('/src/assets/image/chat/26.svg');
}

.hidden {
    display: none;
}

.result.user {
    background-color: #4F7AF6;
    border-top-right-radius: 0em;
    align-self: flex-end;
}

.result.user .chat {
    color: #FFF;
}

.ai .think {
    background-color: #EAEAEA;
    border-radius: 0.3em;
}


.enter {
    width: 100%;
    height: 4em;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    flex-grow: 0;
}

.enter .user-input {
    width: 90%;
    height: var(--input-height);
    border-radius: var(--input-radius);
    background: #E7EAF0;
    position: relative;
}

.user-input input {
    width: 100%;
    height: 100%;
    background-color: transparent;
    border-radius: var(--input-radius);
    padding: 0em 7em 0em 1em;
}

#ai-send {
    height: 100%;
    border-radius: var(--input-radius);
    width: 6em;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #99999999;
    position: absolute;
    top: 0;
    right: 0;
    color: #FFF;
}

#ai-send.active {
    background-color: #455BFF;
}















.markdown-body {
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    margin: 0;
    color: #24292f;
    background-color: transparent;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
}


.markdown-body .octicon {
    display: inline-block;
    fill: currentColor;
    vertical-align: text-bottom;
}

.markdown-body h1:hover .anchor .octicon-link:before,
.markdown-body h2:hover .anchor .octicon-link:before,
.markdown-body h3:hover .anchor .octicon-link:before,
.markdown-body h4:hover .anchor .octicon-link:before,
.markdown-body h5:hover .anchor .octicon-link:before,
.markdown-body h6:hover .anchor .octicon-link:before {
    width: 16px;
    height: 16px;
    content: " ";
    display: inline-block;
    background-color: currentColor;
    -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
    mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
}

.markdown-body details,
.markdown-body figcaption,
.markdown-body figure {
    display: block;
}

.markdown-body summary {
    display: list-item;
}

.markdown-body [hidden] {
    display: none !important;
}

.markdown-body a {
    background-color: transparent;
    color: #0969da;
    text-decoration: none;
}

.markdown-body abbr[title] {
    border-bottom: none;
    text-decoration: underline dotted;
}

.markdown-body b,
.markdown-body strong {
    font-weight: --base-text-weight-semibold, 600;
}

.markdown-body dfn {
    font-style: italic;
}

.markdown-body h1 {
    margin: 0.67em 0;
    font-weight: --base-text-weight-semibold, 600;
    padding-bottom: 0.3em;
    font-size: 2em;
    border-bottom: 1px solid hsla(210, 18%, 87%, 1);
}

.markdown-body mark {
    background-color: #fff8c5;
    color: #24292f;
}

.markdown-body small {
    font-size: 90%;
}

.markdown-body sub,
.markdown-body sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

.markdown-body sub {
    bottom: -0.25em;
}

.markdown-body sup {
    top: -0.5em;
}

.markdown-body img {
    border-style: none;
    max-width: 100%;
    box-sizing: content-box;
    background-color: transparent;
}

.markdown-body code,
.markdown-body kbd,
.markdown-body pre,
.markdown-body samp {
    font-family: monospace;
    font-size: 1em;
}

.markdown-body figure {
    margin: 1em 40px;
}

.markdown-body hr {
    box-sizing: content-box;
    overflow: hidden;
    background: transparent;
    border-bottom: 1px solid hsla(210, 18%, 87%, 1);
    height: 0.25em;
    padding: 0;
    margin: 24px 0;
    background-color: #d0d7de;
    border: 0;
}

.markdown-body input {
    font: inherit;
    margin: 0;
    overflow: visible;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

.markdown-body [type="button"],
.markdown-body [type="reset"],
.markdown-body [type="submit"] {
    -webkit-appearance: button;
}

.markdown-body [type="checkbox"],
.markdown-body [type="radio"] {
    box-sizing: border-box;
    padding: 0;
}

.markdown-body [type="number"]::-webkit-inner-spin-button,
.markdown-body [type="number"]::-webkit-outer-spin-button {
    height: auto;
}

.markdown-body [type="search"]::-webkit-search-cancel-button,
.markdown-body [type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}

.markdown-body ::-webkit-input-placeholder {
    color: inherit;
    opacity: 0.54;
}

.markdown-body ::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit;
}

.markdown-body a:hover {
    text-decoration: underline;
}

.markdown-body ::placeholder {
    color: #6e7781;
    opacity: 1;
}

.markdown-body hr::before {
    display: table;
    content: "";
}

.markdown-body hr::after {
    display: table;
    clear: both;
    content: "";
}

.markdown-body table {
    border-spacing: 0;
    border-collapse: collapse;
    display: block;
    width: max-content;
    max-width: 100%;
    overflow: auto;
}

.markdown-body td,
.markdown-body th {
    padding: 0;
}

.markdown-body details summary {
    cursor: pointer;
}

.markdown-body details:not([open])>*:not(summary) {
    display: none !important;
}

.markdown-body a:focus,
.markdown-body [role="button"]:focus,
.markdown-body input[type="radio"]:focus,
.markdown-body input[type="checkbox"]:focus {
    outline: 2px solid #0969da;
    outline-offset: -2px;
    box-shadow: none;
}

.markdown-body a:focus:not(:focus-visible),
.markdown-body [role="button"]:focus:not(:focus-visible),
.markdown-body input[type="radio"]:focus:not(:focus-visible),
.markdown-body input[type="checkbox"]:focus:not(:focus-visible) {
    outline: solid 1px transparent;
}

.markdown-body a:focus-visible,
.markdown-body [role="button"]:focus-visible,
.markdown-body input[type="radio"]:focus-visible,
.markdown-body input[type="checkbox"]:focus-visible {
    outline: 2px solid #0969da;
    outline-offset: -2px;
    box-shadow: none;
}

.markdown-body a:not([class]):focus,
.markdown-body a:not([class]):focus-visible,
.markdown-body input[type="radio"]:focus,
.markdown-body input[type="radio"]:focus-visible,
.markdown-body input[type="checkbox"]:focus,
.markdown-body input[type="checkbox"]:focus-visible {
    outline-offset: 0;
}

.markdown-body kbd {
    display: inline-block;
    padding: 3px 5px;
    font: 11px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas,
        Liberation Mono, monospace;
    line-height: 10px;
    color: #24292f;
    vertical-align: middle;
    background-color: #f6f8fa;
    border: solid 1px rgba(175, 184, 193, 0.2);
    border-bottom-color: rgba(175, 184, 193, 0.2);
    border-radius: 6px;
    box-shadow: inset 0 -1px 0 rgba(175, 184, 193, 0.2);
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
    margin-top: 0.2em;
    margin-bottom: 0.2em;
    font-weight: --base-text-weight-semibold, 600;
    line-height: 1.2;
}

.markdown-body h2 {
    font-weight: --base-text-weight-semibold, 600;
    padding-bottom: 0.3em;
    font-size: 1.25em;
    border-bottom: 1px solid hsla(210, 18%, 87%, 1);
}

.markdown-body h3 {
    font-weight: --base-text-weight-semibold, 600;
    font-size: 1.15em;
}

.markdown-body h4 {
    font-weight: --base-text-weight-semibold, 600;
    font-size: 1em;
}

.markdown-body h5 {
    font-weight: --base-text-weight-semibold, 600;
    font-size: 0.875em;
}

.markdown-body h6 {
    font-weight: --base-text-weight-semibold, 600;
    font-size: 0.85em;
    color: #57606a;
}

.markdown-body p {
    margin-top: 0;
}

.markdown-body blockquote {
    margin: 0;
    padding: 0 1em;
    color: #57606a;
    border-left: 0.25em solid #d0d7de;
}

.markdown-body ul,
.markdown-body ol {
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 2em;
}

.markdown-body ol ol,
.markdown-body ul ol {
    list-style-type: lower-roman;
}

.markdown-body ul ul ol,
.markdown-body ul ol ol,
.markdown-body ol ul ol,
.markdown-body ol ol ol {
    list-style-type: lower-alpha;
}

.markdown-body dd {
    margin-left: 0;
}

.markdown-body tt,
.markdown-body code,
.markdown-body samp {
    font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas,
        Liberation Mono, monospace;
    font-size: 12px;
}

.markdown-body pre {
    margin-top: 0;
    margin-bottom: 0;
    font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas,
        Liberation Mono, monospace;
    font-size: 12px;
    word-wrap: normal;
}

.markdown-body .octicon {
    display: inline-block;
    overflow: visible !important;
    vertical-align: text-bottom;
    fill: currentColor;
}

.markdown-body input::-webkit-outer-spin-button,
.markdown-body input::-webkit-inner-spin-button {
    margin: 0;
    -webkit-appearance: none;
    appearance: none;
}

.markdown-body::before {
    display: table;
    content: "";
}

.markdown-body::after {
    display: table;
    clear: both;
    content: "";
}

.markdown-body>*:first-child {
    margin-top: 0 !important;
}

.markdown-body>*:last-child {
    margin-bottom: 0 !important;
}

.markdown-body a:not([href]) {
    color: inherit;
    text-decoration: none;
}

.markdown-body .absent {
    color: #cf222e;
}

.markdown-body .anchor {
    float: left;
    padding-right: 4px;
    margin-left: -20px;
    line-height: 1;
}

.markdown-body .anchor:focus {
    outline: none;
}

.markdown-body p,
.markdown-body blockquote,
.markdown-body ul,
.markdown-body ol,
.markdown-body dl,
.markdown-body table,
.markdown-body pre,
.markdown-body details {
    margin-top: 0;
    margin-bottom: 8px;
}

.markdown-body blockquote> :first-child {
    margin-top: 0;
}

.markdown-body blockquote> :last-child {
    margin-bottom: 0;
}

.markdown-body h1 .octicon-link,
.markdown-body h2 .octicon-link,
.markdown-body h3 .octicon-link,
.markdown-body h4 .octicon-link,
.markdown-body h5 .octicon-link,
.markdown-body h6 .octicon-link {
    color: #24292f;
    vertical-align: middle;
    visibility: hidden;
}

.markdown-body h1:hover .anchor,
.markdown-body h2:hover .anchor,
.markdown-body h3:hover .anchor,
.markdown-body h4:hover .anchor,
.markdown-body h5:hover .anchor,
.markdown-body h6:hover .anchor {
    text-decoration: none;
}

.markdown-body h1:hover .anchor .octicon-link,
.markdown-body h2:hover .anchor .octicon-link,
.markdown-body h3:hover .anchor .octicon-link,
.markdown-body h4:hover .anchor .octicon-link,
.markdown-body h5:hover .anchor .octicon-link,
.markdown-body h6:hover .anchor .octicon-link {
    visibility: visible;
}

.markdown-body h1 tt,
.markdown-body h1 code,
.markdown-body h2 tt,
.markdown-body h2 code,
.markdown-body h3 tt,
.markdown-body h3 code,
.markdown-body h4 tt,
.markdown-body h4 code,
.markdown-body h5 tt,
.markdown-body h5 code,
.markdown-body h6 tt,
.markdown-body h6 code {
    padding: 0 0.2em;
    font-size: inherit;
}

.markdown-body summary h1,
.markdown-body summary h2,
.markdown-body summary h3,
.markdown-body summary h4,
.markdown-body summary h5,
.markdown-body summary h6 {
    display: inline-block;
}

.markdown-body summary h1 .anchor,
.markdown-body summary h2 .anchor,
.markdown-body summary h3 .anchor,
.markdown-body summary h4 .anchor,
.markdown-body summary h5 .anchor,
.markdown-body summary h6 .anchor {
    margin-left: -40px;
}

.markdown-body summary h1,
.markdown-body summary h2 {
    padding-bottom: 0;
    border-bottom: 0;
}

.markdown-body ul.no-list,
.markdown-body ol.no-list {
    padding: 0;
    list-style-type: none;
}

.markdown-body ol[type="a"] {
    list-style-type: lower-alpha;
}

.markdown-body ol[type="A"] {
    list-style-type: upper-alpha;
}

.markdown-body ol[type="i"] {
    list-style-type: lower-roman;
}

.markdown-body ol[type="I"] {
    list-style-type: upper-roman;
}

.markdown-body ol[type="1"] {
    list-style-type: decimal;
}

.markdown-body div>ol:not([type]) {
    list-style-type: decimal;
}

.markdown-body ul ul,
.markdown-body ul ol,
.markdown-body ol ol,
.markdown-body ol ul {
    margin-top: 0;
    margin-bottom: 0;
}

.markdown-body li>p {
    margin-top: 16px;
}

.markdown-body li+li {
    margin-top: 0.25em;
}

.markdown-body dl {
    padding: 0;
}

.markdown-body dl dt {
    padding: 0;
    margin-top: 16px;
    font-size: 1em;
    font-style: italic;
    font-weight: --base-text-weight-semibold, 600;
}

.markdown-body dl dd {
    padding: 0 16px;
    margin-bottom: 8px;
}

.markdown-body table th {
    font-weight: --base-text-weight-semibold, 600;
}

.markdown-body table th,
.markdown-body table td {
    padding: 6px 13px;
    border: 1px solid #d0d7de;
}

.markdown-body table tr {
    background-color: transparent;
    border-top: 1px solid hsla(210, 18%, 87%, 1);
}

.markdown-body table tr:nth-child(2n) {
    background-color: #f6f8fa;
}

.markdown-body table img {
    background-color: transparent;
}

.markdown-body img[align="right"] {
    padding-left: 20px;
}

.markdown-body img[align="left"] {
    padding-right: 20px;
}

.markdown-body .emoji {
    max-width: none;
    vertical-align: text-top;
    background-color: transparent;
}

.markdown-body span.frame {
    display: block;
    overflow: hidden;
}

.markdown-body span.frame>span {
    display: block;
    float: left;
    width: auto;
    padding: 7px;
    margin: 13px 0 0;
    overflow: hidden;
    border: 1px solid #d0d7de;
}

.markdown-body span.frame span img {
    display: block;
    float: left;
}

.markdown-body span.frame span span {
    display: block;
    padding: 5px 0 0;
    clear: both;
    color: #24292f;
}

.markdown-body span.align-center {
    display: block;
    overflow: hidden;
    clear: both;
}

.markdown-body span.align-center>span {
    display: block;
    margin: 13px auto 0;
    overflow: hidden;
    text-align: center;
}

.markdown-body span.align-center span img {
    margin: 0 auto;
    text-align: center;
}

.markdown-body span.align-right {
    display: block;
    overflow: hidden;
    clear: both;
}

.markdown-body span.align-right>span {
    display: block;
    margin: 13px 0 0;
    overflow: hidden;
    text-align: right;
}

.markdown-body span.align-right span img {
    margin: 0;
    text-align: right;
}

.markdown-body span.float-left {
    display: block;
    float: left;
    margin-right: 13px;
    overflow: hidden;
}

.markdown-body span.float-left span {
    margin: 13px 0 0;
}

.markdown-body span.float-right {
    display: block;
    float: right;
    margin-left: 13px;
    overflow: hidden;
}

.markdown-body span.float-right>span {
    display: block;
    margin: 13px auto 0;
    overflow: hidden;
    text-align: right;
}

.markdown-body code,
.markdown-body tt {
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    white-space: break-spaces;
    background-color: rgba(175, 184, 193, 0.2);
    border-radius: 6px;
}

.markdown-body code br,
.markdown-body tt br {
    display: none;
}

.markdown-body del code {
    text-decoration: inherit;
}

.markdown-body samp {
    font-size: 85%;
}

.markdown-body pre code {
    font-size: 100%;
}

.markdown-body pre>code {
    padding: 0;
    margin: 0;
    word-break: normal;
    white-space: pre;
    background: transparent;
    border: 0;
}

.markdown-body .highlight {
    margin-bottom: 8px;
}

.markdown-body .highlight pre {
    margin-bottom: 0;
    word-break: normal;
}

.markdown-body .highlight pre,
.markdown-body pre {
    padding: 16px 16px 8px 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    border-radius: 6px;
    direction: ltr;
}

.markdown-body pre code,
.markdown-body pre tt {
    display: inline-block;
    max-width: 100%;
    padding: 0;
    margin: 0;
    overflow-x: scroll;
    line-height: inherit;
    word-wrap: normal;
    background-color: transparent;
    border: 0;
}

.markdown-body .csv-data td,
.markdown-body .csv-data th {
    padding: 5px;
    overflow: hidden;
    font-size: 12px;
    line-height: 1;
    text-align: left;
    white-space: nowrap;
}

.markdown-body .csv-data .blob-num {
    padding: 10px 8px 9px;
    text-align: right;
    background: transparent;
    border: 0;
}

.markdown-body .csv-data tr {
    border-top: 0;
}

.markdown-body .csv-data th {
    font-weight: --base-text-weight-semibold, 600;
    background: #f6f8fa;
    border-top: 0;
}

.markdown-body [data-footnote-ref]::before {
    content: "[";
}

.markdown-body [data-footnote-ref]::after {
    content: "]";
}

.markdown-body .footnotes {
    font-size: 12px;
    color: #57606a;
    border-top: 1px solid #d0d7de;
}

.markdown-body .footnotes ol {
    padding-left: 16px;
}

.markdown-body .footnotes ol ul {
    display: inline-block;
    padding-left: 16px;
    margin-top: 16px;
}

.markdown-body .footnotes li {
    position: relative;
}

.markdown-body .footnotes li:target::before {
    position: absolute;
    top: -8px;
    right: -8px;
    bottom: -8px;
    left: -24px;
    pointer-events: none;
    content: "";
    border: 2px solid #0969da;
    border-radius: 6px;
}

.markdown-body .footnotes li:target {
    color: #24292f;
}

.markdown-body .footnotes .data-footnote-backref g-emoji {
    font-family: monospace;
}

.markdown-body .pl-c {
    color: #6e7781;
}

.markdown-body .pl-c1,
.markdown-body .pl-s .pl-v {
    color: #0550ae;
}

.markdown-body .pl-e,
.markdown-body .pl-en {
    color: #8250df;
}

.markdown-body .pl-smi,
.markdown-body .pl-s .pl-s1 {
    color: #24292f;
}

.markdown-body .pl-ent {
    color: #116329;
}

.markdown-body .pl-k {
    color: #cf222e;
}

.markdown-body .pl-s,
.markdown-body .pl-pds,
.markdown-body .pl-s .pl-pse .pl-s1,
.markdown-body .pl-sr,
.markdown-body .pl-sr .pl-cce,
.markdown-body .pl-sr .pl-sre,
.markdown-body .pl-sr .pl-sra {
    color: #0a3069;
}

.markdown-body .pl-v,
.markdown-body .pl-smw {
    color: #953800;
}

.markdown-body .pl-bu {
    color: #f85149;
}

.markdown-body .pl-ii {
    color: #f0f6fc;
    background-color: #82071e;
}

.markdown-body .pl-c2 {
    color: #f6f8fa;
    background-color: #cf222e;
}

.markdown-body .pl-sr .pl-cce {
    font-weight: bold;
    color: #116329;
}

.markdown-body .pl-ml {
    color: #3b2300;
}

.markdown-body .pl-mh,
.markdown-body .pl-mh .pl-en,
.markdown-body .pl-ms {
    font-weight: bold;
    color: #0550ae;
}

.markdown-body .pl-mi {
    font-style: italic;
    color: #24292f;
}

.markdown-body .pl-mb {
    font-weight: bold;
    color: #24292f;
}

.markdown-body .pl-md {
    color: #82071e;
    background-color: #67060c;
}

.markdown-body .pl-mi1 {
    color: #116329;
    background-color: #033a16;
}

.markdown-body .pl-mc {
    color: #953800;
    background-color: #ffd8b5;
}

.markdown-body .pl-mi2 {
    color: #eaeef2;
    background-color: #0550ae;
}

.markdown-body .pl-mdr {
    font-weight: bold;
    color: #8250df;
}

.markdown-body .pl-ba {
    color: #57606a;
}

.markdown-body .pl-sg {
    color: #8c959f;
}

.markdown-body .pl-corl {
    text-decoration: underline;
    color: #0a3069;
}

.markdown-body g-emoji {
    display: inline-block;
    min-width: 1ch;
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    font-size: 1em;
    font-style: normal !important;
    font-weight: --base-text-weight-normal, 400;
    line-height: 1;
    vertical-align: -0.075em;
}

.markdown-body g-emoji img {
    width: 1em;
    height: 1em;
}

.markdown-body .task-list-item {
    list-style-type: none;
}

.markdown-body .task-list-item label {
    font-weight: --base-text-weight-normal, 400;
}

.markdown-body .task-list-item.enabled label {
    cursor: pointer;
}

.markdown-body .task-list-item+.task-list-item {
    margin-top: 4px;
}

.markdown-body .task-list-item .handle {
    display: none;
}

.markdown-body .task-list-item-checkbox {
    margin: 0 0.2em 0.25em -1.4em;
    vertical-align: middle;
}

.markdown-body .contains-task-list:dir(rtl) .task-list-item-checkbox {
    margin: 0 -1.6em 0.25em 0.2em;
}

.markdown-body .contains-task-list {
    position: relative;
}

.markdown-body .contains-task-list:hover .task-list-item-convert-container,
.markdown-body .contains-task-list:focus-within .task-list-item-convert-container {
    display: block;
    width: auto;
    height: 24px;
    overflow: visible;
    clip: auto;
}

.markdown-body ::-webkit-calendar-picker-indicator {
    filter: invert(50%);
}

.markdown-body .mermaid {
    border: --border-in-light;
    margin-bottom: 5px;
    border-radius: 4px;
    padding: 10px;
    background-color: --white;
}

#dmermaid {
    display: none;
}


.all .home-footer {
    width: 100%;
    position: absolute;
    bottom: 0px;
    flex-grow: 0px;
    flex-shrink: 0px;
    height: 49px;
    background: #ffffffef;
    backdrop-filter: blur(20px);
    border-radius: 10px 10px 0 0;
    box-shadow: 0 -0.5px 6px 0 #5d5d5d33;
    display: flex;
    justify-content: space-around;
    align-items: center;

}

.all .home-footer div {
    width: 57px;
    height: 41px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;

}

.all .home-footer div i {
    display: block;
    width: 22px;
    height: 22px;
    position: absolute;
    top: 0;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.all .home-footer div span {
    color: #666;
    font-size: 10px;
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: 0;
}

.all .home-footer div.active span {
    color: #005ac5;
}

.all .home-footer div.active i {
    width: 48px;
    height: 48px;
    top: -17px;
}

/* 默认图标 */
.all .home-footer div:nth-child(1) i {
    background-image: url("/src/assets/icons/home/<USER>");
}

.all .home-footer div:nth-child(2) i {
    background-image: url("/src/assets/icons/home/<USER>");
}

.all .home-footer div:nth-child(3) i {
    background-image: url("/src/assets/icons/home/<USER>");
}

/* 激活状态图标 */
.all .home-footer div:nth-child(1).active i {
    background-image: url("/src/assets/icons/home/<USER>");
}

.all .home-footer div:nth-child(2).active i {
    background-image: url("/src/assets/icons/home/<USER>");
}

.all .home-footer div:nth-child(3).active i {
    background-image: url("/src/assets/icons/home/<USER>");
}