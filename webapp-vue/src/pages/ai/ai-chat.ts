import { AiChatData, type ChatSessionAll } from "./utils/AI-Data";
import { AiChat } from './utils/AI-Chat';
import showdown from 'showdown';
const md = new showdown.Converter();
export class Elements {
  constructor() {
    this.elReply = document.getElementById('chat-reply')!;
    this.elReplyed = document.getElementById('replyed')!;
    this.elReplying = document.getElementById('replying')!;
    this.elChatNew = document.getElementById('newchat')!;
    this.elHistory = document.getElementById('history')!;
    this.elList = document.getElementById('history-list')!;
    this.elRobot = document.getElementById('robot')!;
    this.elCopyResult = document.getElementById('copy-result')!;
  }
  private cbs: { [K in string]: Function[] } = Object.create(null);
  /** 空白机器人提示*/
  public elRobot: HTMLElement;
  public elReply: HTMLElement;
  public elReplyed: HTMLElement;
  public elReplying: HTMLElement;
  /**历史按钮 */
  public elHistory: HTMLElement;
  public elChatNew: HTMLElement;
  public elList: HTMLElement;
  /**copy结果 */
  public elCopyResult: HTMLElement;
  public on(name: string, cb: Function) {
    this.cbs[name] = this.cbs[name] || [];
    this.cbs[name].push(cb);
  }
  public trigger(name: string, _args: any[] = []) {
    const cbs = this.cbs[name] || [];
    const args = !(_args instanceof Array) ? [_args] : _args
    cbs.forEach(cb => {
      cb.apply(this, args);
    });
  }


}
export class ChatReplyComponent {
  constructor(eles: Elements, abort: { abort?: AbortController }) {
    this.eles = eles;
    this.abort = abort;
    this.user = sessionStorage.getItem('user') || 'none_' + Date.now().toString();
    this.init();
  }
  private user: string;
  private abort;
  private md = new showdown.Converter();
  /**同一天创建多个会话时用于区分 */
  private session!: string;//|| Date.now().toString();
  /**用户发送的话 */
  public words: string = '';
  public results: ChatReply[] = [];
  /**历史数据 */
  private historys: ChatSessionAll[] = []
  public eles: Elements;
  /**数据库操作 */
  private db = new AiChatData();
  /**新建会话 */
  public onNew() {
    this.session = Date.now().toString();
    localStorage.setItem('session' + this.user, this.session);
    this.init();
  }
  /**发送消息 */
  public async onSend(words: string) {
    if (words == '' || words == undefined) return;
    return await this.getStreamResponse(words);
  }
  /**搜索历史消息 */
  public onSearch(words: string) {
    this.getHistoryByWord(words);
  }
  /**初始化包括页面节点 */
  private async init(session?: string | null) {
    const { elRobot, elReplyed, elCopyResult } = this.eles;
    this.session = session = session || localStorage.getItem('session' + this.user)!;
    if (!session) { this.onNew(); return; }
    elReplyed.innerHTML = '';
    elRobot.classList.remove('hidden');
    this.results = new Proxy([], {
      set(target, propKey, value: ChatReply, receiver) {
        elRobot.classList.add('hidden')
        const { innerHTML, innerHTMLThink, role, id, A, B } = value;
        if (role === 'user') {
          elReplyed.innerHTML += `<div class="result user"><div class="chat markdown-body">${innerHTML}</div></div>`
        } else if (role === 'ai') {
          elReplyed.innerHTML += `<div class="result ai">
                    ${innerHTMLThink ? '<div class="chat think markdown-body">' + innerHTMLThink + '</div>' : ''}
                    <div class="chat markdown-body">${innerHTML}</div><div class='marks'>
                    <i class='mark' data-type='mark0' data-id =${id}></i>
                    <i class='mark' data-type='mark1' data-id =${id}></i>
                    <i class='mark ${A ? 'active' : ''}' data-type='mark2' data-id =${id} data-mark=${A}></i>
                    <i class='mark ${B ? 'active' : ''}' data-type='mark3' data-id =${id} data-mark=${B}></i></div>
                    </div>
                    `
        }
        return Reflect.set(target, propKey, value, receiver);
      }
    })
    this.getAllHistory();
    let datas = await this.db.onLoadMessageHistory(session);
    const md = this.md;
    datas.forEach(e => {
      this.results.push({ role: e.role, innerHTMLThink: md.makeHtml(e.think), innerHTML: md.makeHtml(e.content), id: e.id, A: e.A, B: e.B })
    })
    setTimeout(() => {
      document.querySelectorAll('.mark').forEach(e => {
        e.addEventListener('click', () => {
          const data_id = e.getAttribute('data-id')!, id = parseInt(data_id), type = e.getAttribute('data-type'), mark = e.getAttribute('data-mark');
          let data = datas.find(e => e.id == id);
          if (!data) return;
          if (type == 'mark2' || type == 'mark3') {
            console.log(mark)
            const key = type === 'mark2' ? 'A' : 'B', data = mark === '1' ? 0 : 1;
            this.db.onChangeMessageById(id, key, data);
            e.classList.toggle('active');
          } else if (type === 'mark0') {
            copyStringToClipboard(data?.content || '');
            elCopyResult.classList.remove('hidden')
            setTimeout(() => {
              elCopyResult.classList.add('hidden')
            }, 2000)
          } else {
            this.onSend(data.question)
          }
        })
      })
    }, 100);
    this.replyToBottom();
  }
  /**获取所有的历史记录 */
  private async getAllHistory() {
    const results = this.historys = await this.db.onGetAllChatSessions();
    this.genHistoryList(results);
  }
  private getHistoryByWord(word: string) {
    const results = this.historys.filter(e => e.title.includes(word));
    this.genHistoryList(results);
  }
  /**生成历史列表 */
  private async genHistoryList(chats: ChatSessionAll[]) {
    const elList = this.eles.elList, all = Object.create(null), user = this.user;
    elList.innerHTML = '';
    for (let i = 0, len = chats.length; i < len; i++) {
      const { timestamp, title, id, userId } = chats[i];
      if (userId !== user) continue;
      const date = this.getDate(timestamp);
      all[date] = (all[date] ? all[date] : '') + `<div class="history-item ${id === this.session ? 'active' : ''}" data-id="${id}"><span class="history-title">${title}</span></div>`
    }
    for (const key in all) {
      const element = all[key];
      elList.innerHTML += `<div class='list-time'>${key}</div>${element}`;
    }
    setTimeout(() => {
      document.querySelectorAll('.history-item').forEach(e => {
        e.addEventListener('click', () => {
          const id = e.getAttribute('data-id')!;
          this.init(id);
        })
      })
    }, 100);
  }
  private toDay = (new Date().getTime() + 8 * 60 * 60 * 1000) / 24 / 60 / 60 / 1000 | 0;
  /**根据时间戳获取年月日 */
  private getDate(timestamp: number) {
    const date = new Date(timestamp), toDay = this.toDay;
    const day = (date.getTime() + 8 * 60 * 60 * 1000) / 24 / 60 / 60 / 1000 | 0;
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    if (toDay === day) {
      return '今天'
    } else if (toDay - day === 1) {
      return '昨天'
    } else if (toDay - day < 7) {
      return '7天内'
    } else if (toDay - day < 30) {
      return '30天内'
    } else {
      return `${year}年${month}月`
    }
  }
  /**获取数据流 */
  private async getStreamResponse(question: string) {
    const requestBody = {
      frequency_penalty: 0,
      messages: [
        { role: "user", content: '你现在扮演懃务通AI，如果问你是谁、是什么AI，你都必须回答你是懃务通AI。后续不用提示我你扮演的是谁。请回答我以下问题', },
        { role: "user", content: question }],
      model: "doubao-1.5-vision-pro-250328",
      presence_penalty: 0,
      stream: true,
      temperature: 0.5,
      top_p: 1,
    }, { results, eles, db } = this, { elReplying, elReply } = eles;
    let think: string = '', text = '';
    const result: any = new Proxy({}, {
      set(target, propKey, newValue, receiver) {
        if (propKey == 'text') text = md.makeHtml(newValue);
        if (propKey == 'think') think = md.makeHtml(newValue);
        elReplying.innerHTML = `<div class="chat think markdown-body" id="reply-think">${think}</div>
                <div class="chat markdown-body" id="reply-text">${text}</div>`;
        if (!text && !think) {
          elReplying.classList.add('hidden')
        } else {
          elReplying.classList.remove('hidden')
        }
        elReply.scrollTop = elReply.scrollHeight;
        return Reflect.set(target, propKey, newValue, receiver);
      },
    });
    results.push({ role: 'user', innerHTML: question });
    this.replyToBottom();
    await db.onSaveMessage(this.session, 'fireworks', { role: 'user', content: question, think, user: this.user });
    this.abort.abort = new AbortController();
    await AiChat.onPost(result, `https://ark.cn-beijing.volces.com/api/v3/chat/completions`, {
      method: 'POST',
      signal: this.abort.abort?.signal,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer 60c3e16d-22ab-424c-877d-239c974568ae'
      },
      body: JSON.stringify(requestBody)
    });
    await db.onSaveMessage(this.session, 'fireworks', { role: 'ai', question, think: result.think, content: result.text, user: this.user })
    results.push({ role: 'ai', innerHTMLThink: think, innerHTML: text });
    this.init(this.session)
    result.text = '';
    result.think = '';
    return result;
  }
  /**  */
  private replyToBottom() {
    const { elReply } = this.eles;
    requestAnimationFrame(() => {
      elReply.scrollTop = elReply.scrollHeight;
    })
  }
}

/**聊天回复 */
export interface ChatReply {
  role: string;
  /**数据库存储生成 */
  id?: number
  /**html */
  innerHTMLThink?: string;
  /**html */
  innerHTML: string;
  /**点赞 */
  A?: 0 | 1;
  /**踩 */
  B?: 0 | 1;
}

function copyStringToClipboard(text: string) {
  // 检查浏览器是否支持Clipboard API
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(function () {
      console.log('文本成功复制到剪切板');
    }, function (err) {
      console.error('复制文本到剪切板时发生错误', err);
    });
  } else {
    // 对于不支持Clipboard API的浏览器，可以使用document.execCommand()方法（但不推荐在现代浏览器中使用）
    var dummy = document.createElement("textarea");
    document.body.appendChild(dummy);
    dummy.value = text;
    dummy.select();
    document.execCommand("copy");
    document.body.removeChild(dummy);
    console.log('文本成功复制到剪切板');
  }
}