<template>
  <div class="all">
    <div ref="chat-main" @touchstart="onTouchstartMain" @touchmove="onTouchmoveMain" @touchcancel="onTouchcancel"
      @touchend="onTouchendMain" class="chat-main" id="chat-main">
      <div ref="chat-history" class="chat-history hidden" id="history">
        <div class="history-search">
          <div class="history-input">
            <input type="text" id="history-input" v-model="historyIn" @keydown.enter="onHistorySend" placeholder="搜索" />
            <div class="iocn" id="history-send" @click="onHistorySend"></div>
          </div>
        </div>
        <div ref="history-list" class="history-list" id="history-list"></div>
      </div>
      <div class="aichat">
        <div class="chat-head">
          <div class="history" id="history-btn" @click="onOpenHistory"></div>
          <h2>懃务通AI</h2>
          <div class="newchat" id="newchat" @click="onNewChat"></div>
        </div>
        <div ref="chat-reply" class="chat-reply" id="chat-reply">
          <div class="chat-reply-robot" id="robot">
            <span>您好，我是您的AI助手，欢迎向我提问!</span>
          </div>
          <!--已经回复的历史结果-->
          <div id="replyed"></div>
          <!--当前回复-->
          <div class="result ai hidden" id="replying">
            <div class="chat think markdown-body" id="reply-think"></div>
            <div class="chat markdown-body" id="reply-text"></div>
          </div>
        </div>
        <div class="chat-input">
          <div class="enter">
            <div class="user-input">
              <input type="text" id="ai-input" v-model="userIn" @keydown.enter="onSessionSend(true)" @focus="onUserInFocus"
                placeholder="发消息" />
              <div id="ai-send" :class="userIn || !replyed ? 'active' : ''" @click="onSessionSend()">{{ replyed ? '发送' : '停止' }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="chat-copy hidden" id="copy-result">
        <i></i><span>复制成功</span>
      </div>
    </div>
    <!-- 底部导航栏 -->
    <div class="home-footer">
      <div :class="{ active: currentFooter === 0 }" @click="handleFooterClick(0)">
        <i></i>
        <span>工作台</span>
      </div>
      <div :class="{ active: currentFooter === 1 }" @click="handleFooterClick(1)">
        <i></i>
        <span>AI</span>
      </div>
      <div :class="{ active: currentFooter === 2 }" @click="handleFooterClick(2)">
        <i></i>
        <span>我的</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, useTemplateRef } from "vue";
import { ChatReplyComponent, Elements } from "./ai-chat";
import { useRouter } from "vue-router";
const router = useRouter();
/**历史查询输入 */
const historyIn = ref();
/**用户输入 */
const userIn = ref();
/** */
// const userModel = defineModel();
/**聊天回复节点 */
const elChatReply = useTemplateRef('chat-reply')
/**历史记录 */
const elChatHistory = useTemplateRef('chat-history')
/**历史记录列 */
const elHistoryList = useTemplateRef('history-list')
/** */
let chat!: ChatReplyComponent;
/**触摸滑动返回需要 */
let X: number = 0, Y: number = 0;
function onTouchstartMain(ev: TouchEvent) {
  console.log(ev.targetTouches[0])
  const touche = ev.targetTouches[0];
  X = touche.clientX, Y = touche.clientY;
  if (touche.target === elChatHistory.value && X > elChatHistory.value.clientWidth) historyHidden()
}
function onTouchmoveMain(ev: TouchEvent) {
  if (!X) return;
  const { clientX, clientY } = ev.targetTouches[0];
  const delateX = clientX - X, delateY = clientY - Y, el = elHistoryList.value;
  if (delateX < -10 && (Math.abs(delateY) < 10 || el?.clientHeight == el?.scrollHeight)) historyHidden()
}
function onTouchcancel(ev: TouchEvent) {
  ev.preventDefault();
}
function onTouchendMain() {
  X = 0;
}
function historyHidden() {
  elChatHistory.value?.classList.add('none');
  elChatHistory.value?.classList.remove('show');
}
/**历史查询 */
function onHistorySend() {
  chat.onSearch(historyIn.value);
}
let replyed = ref(true);
/**当前会话发送消息 */
async function onSessionSend(flag: boolean = false) {
  const value = userIn.value;
  if (!replyed.value && flag) {
    return;
    // abort.abort?.abort();
    // userIn.value = ""
    // await chat.onSend(value);
  } else if (!replyed.value) {
    abort.abort?.abort();
  } else {
    replyed.value = false;
    userIn.value = ""
    await chat.onSend(value);
  }
  replyed.value = true;
}
/**用户输入获得焦点(聊天结果界面缓慢滚动到底部) */
function onUserInFocus() {
  elChatReply.value?.scrollTo({
    top: elChatReply.value.scrollHeight,
    behavior: "smooth"
  });
}
/**打开历史记录列表*/
function onOpenHistory() {
  elChatHistory.value?.classList.remove('hidden')
  elChatHistory.value?.classList.remove('none')
  elChatHistory.value?.classList.add('show')
}
/**开启新的会话 */
function onNewChat() {
  chat.onNew();
}

const abort: { abort?: AbortController } = {}

onMounted(() => {
  const eles = new Elements();
  chat = new ChatReplyComponent(eles, abort);
});


const currentFooter = ref(1);
function handleFooterClick(index: number) {
  currentFooter.value = index;
  switch (index) {
    case 0:
      router.push("home");
      break;
    case 1:
      router.push("/ai");
      break;
    case 2:
      console.log("我的被点击");
      break;
  }
}
</script>

<style lang="css">
@import "./aiChat.css";
</style>
