export class AiChat {
    /**请求数据流 
     * @param result 结果实时存储对象
     * @param input 请求路径
     * @param init 请求设置
     * @returns 请求结束后再次将结果返回
    */
    public static async onPost(result: AiResult, input: string | URL | Request, init?: RequestInit): Promise<AiResult> {
        const response = await fetch(input, init);
        /**默认将结果输出到text主文本 */
        result.key = 'text';
        result.think = '';
        result.text = '';
        return await AiChat.processStreamResponse(response, result);
    }

    private static async processStreamResponse(response: Response, result: AiResult): Promise<AiResult> {
        const reader = response.body?.getReader();
        const decoder = new TextDecoder('utf-8');
        let eventName = '';
        let eventData = '';
        // 用于处理跨chunks的数据
        let buffer = '';
        try {
            while (true && reader) {
                const { done, value } = await reader.read();
                if (done) {
                    // 如果有内容，将AI回复保存到历史记录中
                    if (result) {
                        console.log(result)
                        return result
                    }
                    break;
                }
                // 解码二进制数据
                const chunk = decoder.decode(value, { stream: true });
                // 添加到缓冲区并处理完整行
                buffer += chunk;
                const lines = buffer.split('\n');
                // 处理所有完整行，保留最后一行（可能不完整）
                if (lines.length > 1) {
                    buffer = lines.pop() || '';
                    for (const line of lines) {
                        if (line.startsWith('event:')) {
                            eventName = line.substring(6).trim();
                        } else if (line.startsWith('data:')) {
                            eventData = line.substring(5).trim();
                            // 在这里处理事件数据
                            await AiChat.handleEventData(eventName, eventData, result);
                        } else if (line.trim() === '') {
                            // 空行，重置事件数据
                            eventName = '';
                            eventData = '';
                        }
                    }
                }
            }
        } catch (error) {
            console.error('读取流出错:', error);
        }
        return result;
    }
    // 处理事件数据
    private static async handleEventData(eventName: string, eventData: string, result: AiResult) {
        // 处理使用情况数据
        if (eventName === 'begin') {
            window.parent.postMessage({ type: 'ai_tool_call_begin' }, '*');
        }
        else if (eventName === 'usage' && eventData) {
            try {
                const usageData = JSON.parse(eventData);
                console.log('Usage数据:', usageData); // 调试用
                // 更新全局时间信息 - 直接使用服务器返回的elapsedTime
                if (usageData.elapsedTime) {

                }

                return result;
            } catch (e) {
                console.error('解析usage数据出错:', e, '原始数据:', eventData);
            }
        }
        // 处理完成事件
        else if (eventName === 'done') {
            window.parent.postMessage({ type: 'ai_tool_call_done' }, '*');
        }
        // 处理内容事件
        else if (eventData) {
            try {
                const jsonData = JSON.parse(eventData);
                /**---!---fireworks返回的内容处理---!--- */
                const choices = jsonData?.choices;
                if (choices) {
                    for (let i = 0, len = choices.length; i < len; i++) {
                        const choice = choices[i];
                        let text = choice.delta?.content;
                        if (text == undefined) break;
                        if (text.includes('<think>')) {
                            result.key = 'think'
                            text = text.replace('<think>', '');
                        } else if (text.includes('</think>')) {
                            result.key = 'text'
                            text = text.replace('</think>', '');
                        };
                        if (result.key === 'think') {
                            result.think = result.think + text;
                        } else {
                            result.text = result.text + text;
                        }
                    }
                }
            } catch (e) {
                // 非JSON数据作为普通文本处理
                const newFullText = eventData;
                return newFullText;
            }
        }
        return result;
    }
}


/**AI返回的结果 */
interface AiResult {
    /**当前内容key */
    key: string;
    /**思考文本 */
    think: string;
    /**正式文本 */
    text: string;
}