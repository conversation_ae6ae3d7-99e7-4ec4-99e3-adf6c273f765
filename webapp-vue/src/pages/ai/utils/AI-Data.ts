/**
 * AI聊天应用 - 数据管理模块
 * 包含数据库操作和会话管理功能
 */
type Role = 'user' | 'ai';
/**保存的数据对象 */
interface DBChatMessage {
    /**对象(用户、AI) */
    role: Role,
    /**主要文本(结论、问题) */
    content: string,
    /**哪个用户存储 */
    user: string,
    /**AI回答结果存储用户问题 */
    question?: string,
    /**AI思考过程 */
    think?: string,
    /**时间戳(保存时自动生成) */
    timestamp?: number,
    /**会话id(保存时提供) */
    sessionId?: string,
    /**会话供应商(保存时提供) */
    provider?: string,
    /**信息标识id(点赞) */
    mark?: number,
    /**好评 */
    A?: 0 | 1,
    /**差评 */
    B?: 0 | 1,
}
/**数据库查询出的结果 */
interface DBRequestData {
    /**角色 */
    role: Role;
    /**内容 */
    content: string;
    /**思考过程 */
    think: string;
    /**会话id */
    sessionId: string;
    /**存储时间戳 */
    timestamp: number;
    /**会话供应商 */
    provider: string;
    /**AI回答结果存储用户问题 */
    question: string,
    /**存储自动生成(Key) */
    id: number;
    /**好评 */
    A?: 0 | 1,
    /**差评 */
    B?: 0 | 1,
}
/**查询出所有的会话记录 */
export interface ChatSessionAll {
    /**会话的id */
    id: string;
    /**用户id */
    userId: string;
    /**会话标题 */
    title: string;
    /**上次回答预览 */
    preview: string;
    /**消息数量 */
    messageCount: number;
    /**会话最后一次时间戳 */
    timestamp: number;
    /**供应商 */
    provider: string;
}
/**数据库查询所有会话后进行MAP分类使用 */
interface DBRequestDataMap {
    /**会话id */
    sessionId: string,
    /**用户id */
    userId: string,
    /**会话下的所有消息 */
    messages: {  /**角色 */
        role: Role;
        /**内容 */
        content: string;
    }[],
    /**最后的时间戳 */
    lastTimestamp: number,
    /**会话数量 */
    messageCount: number,
    /**会话提供商 */
    provider: string
}
/**聊天数据库 */
interface ChatDB {
    instance: IDBDatabase | undefined,
    name: string,
    version: number,
    isReady: boolean
}
/**数据库相关配置*/
export const DB: ChatDB = {
    instance: undefined, // 数据库实例
    name: 'AIDatabase',
    version: 1,
    isReady: false
}
// 数据管理全局对象
export class AiChatData {
    constructor() { }
    /** 初始化数据库 */
    public async onInitDatabase(): Promise<ChatDB> {
        if (DB.isReady) return DB;
        await new Promise((resolve, _) => {
            const request = indexedDB.open(DB.name, DB.version);
            request.onerror = () => {
                DB.isReady = false;
            };
            request.onsuccess = () => {
                DB.instance = request.result;
                DB.isReady = true;
                resolve(DB);
            };
            request.onupgradeneeded = () => {
                const db = DB.instance = request.result;
                // 检查messages表是否存在
                if (!db.objectStoreNames.contains('messages')) {
                    // 创建messages表，使用自增ID作为主键
                    const store = db.createObjectStore('messages', { keyPath: 'id', autoIncrement: true });
                    // 创建索引
                    store.createIndex('sessionId', 'sessionId', { unique: false });
                    store.createIndex('timestamp', 'timestamp', { unique: false });
                    store.createIndex('provider', 'provider', { unique: false });
                } else {
                    // 检查是否需要添加provider索引
                    const transaction = request.transaction!;
                    const store = transaction.objectStore('messages');
                    // 检查provider索引是否存在
                    if (!store.indexNames.contains('provider')) {
                        // 添加provider索引
                        store.createIndex('provider', 'provider', { unique: false });
                    }
                }
            };
        })
        return DB;
    }
    /**获取指定会话的历史记录
     * @param sessionId 会话的id
     * @param limit 限制数量
     */
    public async onGetSessionMessages(sessionId: string, limit = 0): Promise<DBRequestData[]> {
        if (!DB.isReady || !DB.instance) await this.onInitDatabase();
        const result: DBRequestData[] = await new Promise((resolve, reject) => {
            try {
                const transaction = DB.instance!.transaction(['messages'], 'readonly');
                const store = transaction.objectStore('messages');
                const index = store.index('sessionId');
                // 使用sessionId查询
                const request = index.openCursor(IDBKeyRange.only(sessionId));
                const messages: any[] = [];
                request.onsuccess = () => {
                    const cursor = request.result;
                    if (cursor) {
                        messages.push(cursor.value);
                        cursor.continue();
                    } else {
                        // 按时间戳排序
                        messages.sort((a, b) => a.timestamp - b.timestamp);
                        // 如果有限制，则只返回最新的N条
                        const result = limit > 0 ? messages.slice(-limit) : messages;
                        resolve(result);
                    }
                };
                request.onerror = (event) => {
                    console.error('查询会话消息失败:', event.target);
                    reject(event.target);
                };
            } catch (error) {
                console.error('获取会话消息出错:', error);
                reject(error);
            }
        })
        console.log(result)
        return result;
    }
    /**获取所有聊天会话信息*/
    public async onGetAllChatSessions(provider = null): Promise<ChatSessionAll[]> {
        if (!DB.isReady || !DB.instance) await this.onInitDatabase();
        const result: ChatSessionAll[] = await new Promise((resolve, reject) => {
            try {
                const store = DB.instance!.transaction(['messages'], 'readonly').objectStore('messages');
                const map: { [K in string]: DBRequestDataMap } = Object.create(null);
                let request: IDBRequest<IDBCursorWithValue | null>;
                // 如果提供了供应商且数据库有provider索引，则使用索引直接过滤
                if (provider && store.indexNames.contains('provider')) {
                    const index = store.index('provider');
                    const range = IDBKeyRange.only(provider);
                    request = index.openCursor(range);
                } else {
                    // 否则获取所有会话，需要在代码中手动过滤
                    request = store.openCursor();
                }
                request.onsuccess = () => {
                    const cursor = request.result;
                    if (cursor) {
                        const message: DBChatMessage = cursor.value;
                        const key = message.sessionId!;
                        const messageProvider = message.provider || 'unknown';
                        // 如果指定了provider，且不匹配当前消息的provider，则跳过
                        if (provider && messageProvider !== provider) {
                            cursor.continue();
                            return;
                        }
                        // 更新会话信息
                        const session = map[key] = map[key] || {
                            sessionId: key,
                            messages: [],
                            userId: message.user,
                            lastTimestamp: 0,
                            messageCount: 0,
                            provider: messageProvider
                        };
                        // 更新会话信息
                        session.messageCount++;
                        session.lastTimestamp = Math.max(session.lastTimestamp, message.timestamp!);
                        // 保存一些消息内容用于显示预览（最多保存前2条用户消息和第一条AI回复）
                        const userMessagesCount = session.messages.filter(m => m.role === 'user').length;
                        const aiMessagesCount = session.messages.filter(m => m.role === 'ai').length;
                        if ((message.role === 'user' && userMessagesCount < 2) || (message.role === 'ai' && aiMessagesCount < 1)) {
                            session.messages.push({
                                role: message.role,
                                content: message.content
                            });
                        }
                        cursor.continue();
                    } else {
                        /**遍历结束 */
                        // 转换映射为数组并按最后活跃时间排序
                        const sessions = Object.values(map).sort((a, b) => b.lastTimestamp - a.lastTimestamp);
                        // 格式化会话信息
                        const formattedSessions: ChatSessionAll[] = sessions.map(session => {
                            // 提取会话的首条用户消息作为会话标题
                            const firstUserMessage = session.messages.find(m => m.role === 'user');
                            const firstAIMessage = session.messages.find(m => m.role === 'ai');
                            return {
                                id: session.sessionId,
                                userId: session.userId,
                                title: firstUserMessage ? firstUserMessage.content.substring(0, 50) : '无标题会话',
                                preview: firstAIMessage ? firstAIMessage.content.substring(0, 100) : '无预览内容',
                                messageCount: session.messageCount,
                                timestamp: session.lastTimestamp,
                                provider: session.provider
                            };
                        });
                        resolve(formattedSessions);
                    }
                };
                request.onerror = (event) => {
                    console.error('获取会话列表失败:', event.target);
                    reject(event.target);
                };
            } catch (error) {
                console.error('获取会话列表时出错:', error);
                reject(error);
            }
        });
        return result;
    }
    /**根据id获取并修改消息 */
    public async onChangeMessageById(id: number, key: string, data: any) {
        if (!DB.isReady || !DB.instance) await this.onInitDatabase();
        const result: DBRequestData[] = await new Promise((resolve, reject) => {
            try {
                const transaction = DB.instance!.transaction(['messages'], 'readwrite');
                const store = transaction.objectStore('messages');
                const request = store.get(id);
                request.onsuccess = () => {
                    const result: any = request.result;
                    if (result) {
                        result[key] = data;
                        store.put(result)
                        // cursor.continue();
                    } else {
                        resolve(result);
                    }
                };
                request.onerror = (event) => {
                    console.error('查询会话消息失败:', event.target);
                    reject(event.target);
                };
            } catch (error) {
                console.error('获取会话消息出错:', error);
                reject(error);
            }
        })
        console.log(result)
        return result;
    }
    /**
     * 保存消息历史
     * @param session 会话id
     * @param provider AI供应商名称
     * @param messages 消息数组（用户问题、AI回复）
     * @returns 
     */
    public async onSaveMessage(session: string, provider: string, messages: DBChatMessage) {
        // 保存每条消息到数据库
        if (messages.role && messages.content) return await this.saveChatMessage(session, provider, messages)
            .catch(error => console.error('保存消息失败:', error));
        return null
    }
    /**修改消息历史 (增加好评)*/
    public async onSaveMessageMark(messages: DBChatMessage) {
        if (!DB.isReady || !DB.instance) await this.onInitDatabase();
        // 保存每条消息到数据库
        this.saveChatMessageMark(messages)
    }
    /**加载消息历史 
     * @param session 会话id
     */
    public async onLoadMessageHistory(session: string): Promise<DBRequestData[]> {
        if (!DB.isReady || !DB.instance) await this.onInitDatabase();
        const messages = await this.loadChatHistory(session, 50);
        return messages;
    }
    // 保存聊天消息到数据库
    private async saveChatMessage(session: string, provider: string, message: DBChatMessage) {
        if (!DB.isReady || !DB.instance) await this.onInitDatabase();
        if (!message || !message.content || !session) {
            console.error('保存消息失败: 无效的消息或会话ID', {
                hasMessage: !!message,
                hasContent: message && !!message.content,
                sessionId: session
            });
            return new Error('无效的消息或会话ID');
        }
        const result = await new Promise((resolve, reject) => {
            try {
                const transaction = DB.instance!.transaction(['messages'], 'readwrite');
                const store = transaction.objectStore('messages');
                // 准备要存储的消息对象
                const messageToStore: DBChatMessage = {
                    ...message,
                    sessionId: session,
                    timestamp: Date.now(),
                    provider: provider
                };
                // 添加到消息存储
                const request = store.add(messageToStore);
                request.onsuccess = () => {
                    console.log('消息已保存到数据库');
                    resolve(messageToStore);
                };

                request.onerror = (event) => {
                    console.error('保存消息到数据库失败:', event.target);
                    reject(event.target);
                };
            } catch (error) {
                console.error('保存消息到数据库时出错:', error);
                reject(error);
            }
        });
        console.log(result)
        return result;
    }
    // 保存回复评价到数据库
    private async saveChatMessageMark(message: DBChatMessage) {
        if (!DB.isReady || !DB.instance) await this.onInitDatabase();
        const result = await new Promise((resolve, reject) => {
            try {
                const transaction = DB.instance!.transaction(['messages'], 'readwrite');
                const store = transaction.objectStore('messages');
                // 准备要存储的消息对象
                const messageToStore: DBChatMessage = {
                    ...message,
                };
                // 修改到消息存储
                const request = store.put(messageToStore);
                request.onsuccess = () => {
                    console.log('消息已修改到数据库');
                    resolve(messageToStore);
                };

                request.onerror = (event) => {
                    console.error('修改消息到数据库失败:', event.target);
                    reject(event.target);
                };
            } catch (error) {
                console.error('修改消息到数据库时出错:', error);
                reject(error);
            }
        });
        console.log(result)
        return result;
    }
    // 从数据库加载会话消息
    private async loadChatHistory(session: string, limit = 50): Promise<DBRequestData[]> {
        const result: DBRequestData[] = await new Promise((resolve, reject) => {
            try {
                const transaction = DB.instance!.transaction(['messages'], 'readonly');
                const store = transaction.objectStore('messages');
                const index = store.index('sessionId');
                // 查询当前会话的消息，并按时间排序
                const range = IDBKeyRange.only(session);
                const request = index.openCursor(range, 'prev'); // 倒序，最新的消息优先
                const messages: any[] = [];
                let counter = 0;
                request.onsuccess = () => {
                    const cursor = request.result;
                    if (cursor && counter < limit) {
                        messages.push(cursor.value);
                        counter++;
                        cursor.continue();
                    } else {
                        // 按时间正序排列
                        messages.sort((a, b) => a.timestamp - b.timestamp);
                        console.log(`从数据库加载了 ${messages.length} 条消息`);
                        resolve(messages);
                    }
                };
                request.onerror = () => {
                    console.error('加载消息历史失败:', request.error);
                    reject(request.error);
                };
            } catch (error) {
                console.error('加载消息历史过程中出错:', error);
                reject(error);
            }
        });
        return result;
    }
}; 