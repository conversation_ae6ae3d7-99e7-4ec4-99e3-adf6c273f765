<template>
  <div>
    <SlMeetingRoom5 :selectedId="'507'" @update:selected-id="selectedChange" />
    <SlMeetingRoom8 @update:selected-id="selectedChange"/>
    <SlMeetingRoom9 :selectedId="'902'" @update:selected-id="selectedChange"/>
  </div>
</template>
<script lang="ts" setup>
import SlMeetingRoom5 from "@/components/SlMeetingRoom5.vue";
import SlMeetingRoom8 from "@/components/SlMeetingRoom8.vue";
import SlMeetingRoom9 from "@/components/SlMeetingRoom9.vue";
const selectedChange = (roomId: string|undefined) => {
  console.log('roomId', roomId)
}
// import { ref } from "vue";
// // 5F 路线
// const route5FPoints = ref<{ x: number; y: number }[]>([
//   { x: 84, y: 34 },
//   { x: 88, y: 34 },
//   { x: 88, y: 75 },
//   { x: 198, y: 75 },
//   { x: 198, y: 34 },
// ]);
// // 5F 507 定位点
// const position507 = ref<{ x: number; y: number }>({ x: 94, y: 88 });
// // 5F 507 门
// const doors507 = ref<{ x: number; y: number }[]>([{ x: 109, y: 76 }]);
// // 5F 5045 定位点
// const position5045 = ref<{ x: number; y: number }>({ x: 135, y: 88 });
// // 5F 5045 门
// const doors5045 = ref<{ x: number; y: number }[]>([{ x: 126, y: 76 }, { x: 161, y: 76 }]);

// // 8F 路线
// const route8FPoints = ref<{ x: number; y: number }[]>([
//   { x: 84, y: 34 },
//   { x: 88, y: 34 },
//   { x: 88, y: 75 },
//   { x: 198, y: 75 },
//   { x: 198, y: 34 },
// ]);
// // 8F 507 定位点
// const position806 = ref<{ x: number; y: number }>({ x: 94, y: 88 });
// // 8F 806 门
// const doors806 = ref<{ x: number; y: number }[]>([{ x: 109, y: 76 }]);

// // 9F 路线
// const route9FPoints = ref<{ x: number; y: number }[]>([
//   { x: 84, y: 34 },
//   { x: 88, y: 34 },
//   { x: 88, y: 75 },
//   { x: 198, y: 75 },
//   { x: 224, y: 75 },
// ]);
// // 9F 902 定位点
// const position902 = ref<{ x: number; y: number }>({ x: 94, y: 88 });
// // 9F 902 门
// const doors902 = ref<{ x: number; y: number }[]>([{ x: 222, y: 76 }]);


</script>
<style lang="css" scoped>
div {
  width: 291px;
  margin: 0 auto;
}
</style>
