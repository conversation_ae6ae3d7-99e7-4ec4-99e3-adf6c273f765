<template>
    <div>
        <h2>Mock</h2>
        <h3>download/upload</h3>
        <div>
            <div style="display: flex;">
                <n-input v-model:value="downloadUrl" style="width: 300px;margin-right: 20px;" :status>
                    <template #prefix>
                        URL：
                    </template>
                </n-input>
                <n-button type="primary" @click="download()">下载文件</n-button>
            </div>
            <div style="display: flex;">
                <n-button type="primary" @click="downloadAisLog()">AIS下载日志文件</n-button>
                <n-button type="primary" @click="downloadRunningLog()">下载运行日志文件</n-button>
            </div>
            <div style="display: flex;margin-top: 20px;">
                <n-upload action="/api/v1/flow/uploadTplFile" accept=".xlsx, .xls" :custom-request="customUpload"
                @before-upload="beforeUpload"  :show-file-list="false">
                    <n-button type="primary">上传省流模板</n-button>
                </n-upload>
            </div>
        </div>
        <h3>内部协议</h3>
        <div>
            <div style="display: flex; align-items: center; gap:5%;width: 100%;">
                <n-input v-model:value="mockData.url" style="width: 300px;" :status>
                    <template #prefix>
                        URL：
                    </template>
                </n-input>
                <n-radio-group v-model:value="mockData.method" name="radiogroup">
                    <n-radio value="GET">
                        GET
                    </n-radio>
                    <n-radio value="POST">
                        POST
                    </n-radio>
                </n-radio-group>
                <n-button type="primary" @click="onRequest()">Request</n-button>
            </div>
            <div style="margin: 10px 0;" v-show="mockData.method === 'POST'">
                <h4>Params</h4>
                <textarea v-model="mockData.data" />
            </div>
            <div>
                <h4>Response</h4>
                <textarea v-model="mockData.res" />
            </div>
        </div>
        <h3>ip输入</h3>
        <div>
            <sl-ip-input style="width: 300px" v-model="ipValue" @update:modelValue="handleIpChange"></sl-ip-input>
        </div>
        <h3>雪碧图</h3>
        <div style="margin-top: 10px;">
            <img src="@/assets/img/sprite.png" alt="" style="border: 1px solid #999;">
        </div>
        <div style="margin-top: 10px; display: flex;align-items: center; gap: 8px;">
            <span>即时查看</span>
            <n-input v-model:value="icon.size" style="width: 100px;">
                <template #prefix>尺寸</template>
            </n-input>
            <n-input style="width: 100px;" v-model:value="icon.idx">
                <template #prefix>索引</template>
            </n-input>
            <a href="javascript:;" v-show="iconClass" @click="copyIcon" title="复制class">
                <i :class="iconClass"></i>
            </a>
            {{ iconClass }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { get, post } from '@/service/http.service';
import { NInput, NRadio, NRadioGroup, useModal, NUpload, NButton, type UploadCustomRequestOptions, useMessage, type UploadSettledFileInfo } from 'naive-ui';
import { computed, reactive, ref } from 'vue';
import { downloadFile, downloadLogByTime, isEmpty } from '../../utils';
import { getToken } from '../../service/storage.service';
import axios from 'axios';
import SlIpInput from '@/components/SlIpInput.vue'

const modal = useModal();
const status = ref()
const downloadUrl = ref('')
const ipValue = ref('')
const icon = reactive({ size: '', idx: '' })
const iconClass = computed(() => icon.size && icon.idx ? `app-icon icon-${icon.size}-${icon.idx}` : '')
const message = useMessage()
const mockData = reactive({
    method: 'GET',
    url: '',
    data: '',
    res: ''
})
const onRequest = async () => {
    if (!mockData.url) {
        status.value = 'error'
        return
    }
    status.value = undefined
    mockData.res = '';
    if (mockData.method == 'GET') {
        mockData.data = ''
    }
    try {
        const res = mockData.method == 'POST' ? await post(mockData.url, mockData.data) : await get(mockData.url)
        mockData.res = JSON.stringify(res)
    } catch (err) {
        mockData.res = JSON.stringify(err || '')
    }
}


const customUpload = async ({
    file,
    data,
    headers,
    withCredentials,
    action,
    onFinish,
    onError,
}: UploadCustomRequestOptions) => {
    const uploadErrorTip = () => {
        modal.create({
            title: '提示',
            preset: 'dialog',
            content: '上传失败！',
        })
        onError()
    }
    const formData = new FormData()
    if (data) {
        Object.keys(data).forEach((key) => {
            formData.append(
                key,
                data[key as keyof UploadCustomRequestOptions['data']]
            )
        })
    }
    formData.append(file.name, file.file as File)
    // showProgressModal.value = true // 显示进度条
    // uploading.value = true // 显示上传中提示
    // upgrading.value = false // 隐藏固件升级中提示
    axios.post(action as string, formData, {
        headers: {
            ...headers,
            Authorization: 'Bearer ' + getToken(),
        },
        withCredentials,
        onUploadProgress: (e) => {
            const percent = Math.round((e.loaded * 100) / (e.total || 1))
            console.log('percent', percent)
        },
    }).then((res) => {
        const data = res?.data
        if (res && res.status == 200 && data && data.rlt === 0) {
            onFinish()
            console.log(data)
            modal.create({
                title: '提示',
                preset: 'dialog',
                content: '上传成功！',
            });
            // progressStatus.value = 'success' // 上传成功
            // uploadPercentage.value = 100 // 设置进度条为100%
        } else {
            uploadErrorTip()
        }
    }).catch(() => {
        // showProgressModal.value = false
        modal.create({
            title: '上传失败',
            preset: 'dialog',
            type: 'error',
            content: '请检查网络连接或文件格式',
            closable: true,
            positiveText: '确定',
        })
        onError()
    })
}
const downloadAisLog = async () => {
    const startTime = '2025-04-28 12:00:00';
    const endTime = '2025-04-28 19:00:00';
    await downloadLogByTime('ais/download', { startTime, endTime })
    modal.create({
        title: '提示',
        preset: 'dialog',
        content: 'AIS日志下载成功！',
    });
}

const downloadRunningLog = async () => {
    const startTime = '2025-04-28 12:01:01';
    const endTime = '2025-04-28 19:01:01';
    await downloadLogByTime('log/download', { startTime, endTime })
    modal.create({
        title: '提示',
        preset: 'dialog',
        content: '运行日志下载成功！',
    });
}
const download = async () => {
    try {
        if (isEmpty(downloadUrl.value)) {
            modal.create({
                title: '提示',
                preset: 'dialog',
                content: '请输入url',
            });
            return
        }

        await downloadFile(downloadUrl.value)
        modal.create({
            title: '提示',
            preset: 'dialog',
            content: '下载成功！',
        });
    } catch (e) {
        modal.create({
            title: '提示',
            preset: 'dialog',
            content: '下载失败！',
        });
    }
}
function handleIpChange(newIp: string) {
    console.log('IP改变了:', newIp)
}

function copyIcon() {
    if (!iconClass.value) return;
    navigator.clipboard.writeText(`<i class="${iconClass.value}"></i>`).then(() => {
        message.success(`复制成功`)
    }).catch(err => {
        message.success(err || '复制失败')
    })
}
const beforeUpload = ({ file }: {
    file: UploadSettledFileInfo;
    fileList: UploadSettledFileInfo[];
}) => {
    if (file.file && file.file?.size > 10 * 1024 * 1024) { // 10M
        message.error('文件不能超过10MB！');
        return false
    }
    return true
}
</script>

<style scoped>
textarea {
    width: 100%;
    height: 250px;
    padding: 8px;
}
</style>