// 单辆车的信息
export interface Vehicle {
  /** 车辆 ID */
  id: string;
  /** 关联的人员 ID */
  personnelId: string;
  /** 车牌号 */
  licensePlateNumber: string;
}

export interface PersonnelPayload {
  id?: string; //可选
  personnelId: string; //车主id 必须
  licensePlateNumber: string; // 车牌号 必需
}

// 单个员工（或人员）的信息
export interface Personnel {
  /** 人员 ID */
  id?: string;
  /** 姓名 */
  name: string;
  /** 电话 */
  phone: string;
  /** 部门 */
  department?: string;
  /** 职位 */
  position: string;
  /** 邮箱 */
  email?: string;
  /** 类型（0=普通、1=其他…） */
  type?: number;
  /** 状态（1=在职、0=离职…） */
  status?: number;
  /** 该人员名下的车辆列表 */
  vehicleinfoList: Vehicle[];
}
