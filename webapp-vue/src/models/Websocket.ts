/** ws 动作类型 */
export enum WS_ACTION {
  /** 重启 */
  REBOOT = 'reboot',
  /** 恢复出厂设置（重置） */
  UPGRADE = 'upgrade',
}
/** ws 请求频道类型 */
export enum WS_REQ_CHANNEL {
  CMD = 'cmd',
  PING = 'ping',
  LOG = 'log',
  AIS_START = 'ais_start',
  AIS_STOP = 'ais_stop',
}

/** ws 响应频道类型 */
export enum WS_RES_CHANNEL {
  PONG = 'pong',
  AIS = 'ais',
  LOG = 'log',
  /** 状态更新数据 */
  UPDATE = 'update',
  ERROR = 'error',
  /** 升级数据 */
  UPGRADE = 'upgrade',
  /** 提示 */
  ALERT = 'alert', //
}

/** ws 响应体 */
export interface WsMessage<T = any> {
  /** 请求频道类型 */
  channel: WS_RES_CHANNEL;
  message: string;
  /** utc 时间 */
  timestamp: string;
  /** 请求具体动作 */
  action?: WS_ACTION;
  error?: string;
  payload?: T;
}

/** ws 请求体 */
export interface WsReqMessage {
  /** 请求频道类型 */
  channel: WS_REQ_CHANNEL;
  /** 请求具体动作 */
  action?: WS_ACTION;
  params?: any;
}
