import type { SelectedMeetingRoom } from '@/pages/meet-assistant/metting-handle/meeting-handle.data';
import type { Content } from './Content';

/**
 * Meeting
 */
export interface Meeting {
  /**
   * 会议内容
   */
  content?: string;
  /**
   * 创建人ID
   */
  creatorId?: string;
  /**
   * 创建人姓名
   */
  creatorName?: string;
  /**
   * 结束时间
   */
  endTime?: string;
  /** info 时间段 */
  timeRange?: string;
  id?: string;
  /**
   * 关联的会议室信息
   */
  meetingroom?: Meetingroom;
  /**
   * 会议室ID
   */
  meetingroomId?: string;
  /**
   * 关联的参会人员列表
   */
  meetingpersonnelList?: Meetingpersonnel[];
  /**
   * 开始时间
   */
  startTime?: string;
  /**
   * 会议状态(0-待开始,1-进行中,2-已取消,3-已结束)
   */
  status?: number;
  /**
   * 会议主题
   */
  title?: string;
  /** 关联的会议服务列表 */
  meetingfacilityList?: Meetingfacility[];
  /** 关联的会议通知 */
  notification?: Notification;
  /** 会议开始前多少分钟发送通知，null表示不发送通知 */
  notifyMinutesBefore?: number | null;
  /** 通知方式(0-短信通知,1-机器人语音电话通知) */
  // notificationType?: number;
  // notificationMethod?: number;
  /** 是否短信通知(0-否,1-是) */
  notifyBySms?: number;
  /** 是否机器人语音电话通知(0-否,1-是) */
  notifyByVoiceCall?: number;
  /** 关联的附件列表 */
  contentList?: Content[];
  /** 是否发送即时通知(创建/修改会议时)(0-否,1-是) */
  // 选立即发送就传`sendInstantNotification=1`和`notifyMinutesBefore=null` 选其他就穿`sendInstantNotification=0`和`notifyMinutesBefore=15`
  sendInstantNotification?: number;
  // [property: string]: any;
  /** 会议服务开关 */
  _isServiceOpen?: boolean;
  /** 更多人员全选开关 */
  _isAllCheckedPerson?: boolean;
  /** 0 智能推荐 1 手动推荐 */
  // _roomCheckType?: number;
  /** 1 智能推荐 0 手动推荐 */
  roomSelectionType?: number;
  _meetingroom?: SelectedMeetingRoom;
}

/**
 * 关联的会议室信息
 *
 * Meetingroom
 */
export interface Meetingroom {
  /**
   * 所在栋号(A/B/C)
   */
  building?: string;
  /**
   * 容纳人数
   */
  capacity?: number;
  /**
   * 会议室描述
   */
  description?: string;
  /**
   * 所在楼层
   */
  floor?: number;
  /**
   * 是否有麦克风(0-无,1-有)
   */
  hasMicrophone?: number;
  /**
   * 是否有笔记本(0-无,1-有)
   */
  hasNotebook?: number;
  /**
   * 是否有投影仪(0-无,1-有)
   */
  hasProjector?: number;
  /**
   * 是否有白板(0-无,1-有)
   */
  hasWhiteboard?: number;
  id?: string;
  /**
   * 会议室名称
   */
  name?: string;
  /**
   * 状态(0-禁用,1-启用)
   */
  status?: number;
  [property: string]: any;
}

/**
 * 会议参会人员关系实体类
 *
 * Meetingpersonnel
 */
export interface Meetingpersonnel {
  /**
   * 参会反馈(0-未反馈,1-参加,2-建议延期,3-不参加)
   */
  feedback?: number;
  id?: string;
  /**
   * 会议ID
   */
  meetingId?: string;
  /**
   * 关联的人员信息
   */
  personnel?: Personnel;
  /**
   * 人员ID
   */
  personnelId?: string;
  /**
   * 反馈原因
   */
  reason?: string;
  /**
   * 参会角色(0-普通参会人,1-发起人)
   */
  role?: number;
  /**
   * 人员类型(0-内部成员,1-外部成员)
   */
  type?: number;
  name?: string;
  // [property: string]: any;
}

/**
 * 关联的人员信息
 *
 * Personnel
 */
export interface Personnel {
  /**
   * 部门
   */
  department?: string;
  organizationId?: string;
  organization?: any;
  /**
   * 邮箱
   */
  email?: string;
  id?: string;
  /**
   * 姓名
   */
  name?: string;
  /**
   * 手机号码
   */
  phone?: string;
  /**
   * 职务
   */
  position?: string;
  /**
   * 状态(0-禁用,1-启用)
   */
  status?: number;
  /**
   * 人员类型(0-内部成员,1-外部成员)
   */
  type?: number | null;
  // 部门人员
  members?: Personnel[];
  /**
   * 车辆信息列表
   */
  vehicleList?: Vehicleinfo[];
  _isChecked?: boolean;
}

/**
 * 车辆信息实体类
 *
 * Vehicleinfo
 */
export interface Vehicleinfo {
  id?: string;
  /**
   * 车牌号
   */
  licensePlateNumber?: string;
  /**
   * 车主id
   */
  personnelId?: string;
  [property: string]: any;
}
/** 会议服务列表 */
export interface Meetingfacility {
  id?: string;
  meetingId?: string;
  /** 服务类型(0-用餐,1-桌牌,2-纸笔,3-茶水,4-果盘) */
  type: number;
  /** 数量 */
  quantity?: number;
  /** 服务内容(如桌牌内容、用餐时间等) */
  content?: string;
  _selectType?: 'select' | 'input';
}
/** 会议通知 */
export interface Notification {
  id?: string;
  meetingId?: string;
  /** 通知方式(0-短信通知,1-机器人语音电话通知) */
  type: number;
  /** 通知状态(0-未发送,1-已发送,2-已取消) */
  status?: number;
  /** 通知时间 */
  time?: number;
  /** 通知结果记录 */
  result?: number;
}

/**
 * 工作组实体类
 */
export interface IWorkGroup {
    /**
     * 工作组描述
     */
    description?: string;
    id?: string;
    /**
     * 负责人信息
     */
    leader?: Personnel;
    /**
     * 负责人ID
     */
    leaderId?: string;
    /**
     * 工作组名称
     */
    name?: string;
    /**
     * 工作组成员列表
     */
    personnelList?: Personnel[];
    /**
     * 排序号
     */
    sortOrder?: number;
    /**
     * 状态(0-禁用,1-启用)
     */
    status?: number;
}