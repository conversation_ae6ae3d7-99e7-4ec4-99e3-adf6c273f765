export interface MeetReserved {
  id?: string;
  title?: string;
  content?: string;
  meetingroomId?: string;
  startTime?: string;
  endTime?: string;
  creatorId?: string;
  creatorName?: string;
  loadMeetingroom?: boolean;
  status?: number;
  date?: string;
  meetingfacilityList?: any[];
  meetingpersonnelList?: any[];
}

export interface UserInfo {
  /**
   * 用户的唯一标识符。
   */
  id: string;
  /**
   * 用户的姓名。
   */
  name: string;
  /**
   * 用户的联系电话。
   */
  phone: string;
  /**
   * 用户所在的部门。
   */
  department: string;
  /**
   * 用户的职位。
   */
  position:  string;
  /**
   * 用户的电子邮箱地址。
   */
  email:  string;
  /**
   * 用户类型，使用数字表示不同类型。
   */
  type: number;
  /**
   * 用户状态，使用数字表示不同状态。
   */
  status:  number;
  /**
   * 用户关联的车辆信息列表，该字段为可选字段。
   */
  vehicleinfoList?: any[];
}

