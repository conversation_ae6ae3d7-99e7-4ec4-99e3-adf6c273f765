import NotFoundComponent from "@/components/NotFoundComponent.vue";
// import { usePrincipalStore } from '@/stores/principal';
import {
  createRouter,
  createWebHistory,
  type RouteRecordRaw,
} from "vue-router";
import Layout from "@/pages/layout/index.vue";
import { CarMoveRoutes } from "./car-move.router";
import MettingRoutes from "./metting.router";
import { AIRoutes } from "./ai.router";
import { MockRoutes } from "./mock.router";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
NProgress.configure({ showSpinner: false, trickleSpeed: 200 });
// 检查登录状态
const checkLogin = () => {
  const hasOpened = sessionStorage.getItem("user");
  return !!hasOpened;
  // const principal = usePrincipalStore();
  // const account = await principal.identity();
  // if (account != null && typeof account == 'object' && account.id) {
  //   return true;
  // } else {
  //   return false;
  // }
};
// 全局路由配置
const routes: RouteRecordRaw[] = [
  {
    path: "/",
    component: Layout,
    redirect: "/home",
    children: [
      {
        path: "/home",
        name: "home",
        component: () => import("@/pages/home/<USER>"),
        meta: {
          keepAlive: true,
          title: "首页", // title
          showNavbar: false, // 导航栏
          // banner: "/src/assets/image/home/<USER>", // 如 首页banner
          showGoback: false, // 返回上一级
          extra: [], // 导航栏额外操作，没有不配置
        },
      },
      {
        path: "/all",
        component: () => import("@/pages/home/<USER>/all.vue"),
        meta: { title: "全部", showNavbar: true, showGoback: true },
      },
      // 会议模块
      ...MettingRoutes,
      // 挪车服务
      ...CarMoveRoutes,
      // AI 助手
      ...AIRoutes,
      // 模拟测试页面
      ...MockRoutes,
    ],
  },
  {
    path: "/login",
    name: "login",
    component: () => import("@/pages/login/index.vue"),
    meta: {
      title: "首页", // title
      showNavbar: false, // 导航栏
      showGoback: false, // 返回上一级
    },
  },
  {
    path: "/erorr",
    component: () => import("@/pages/error/index.vue"),
    meta: {
      title: "首页", // title
      showNavbar: false, // 导航栏
      showGoback: false, // 返回上一级
    },
  },
  { path: "/:pathMatch(.*)", component: NotFoundComponent },
];
const router = createRouter({
  history: createWebHistory(),
  routes,
});
// 全局前置守卫
router.beforeEach((to) => {
  NProgress.start();
  const isLogin = checkLogin();
  // 目标路由是 login
  if (to.name === "login") {
    NProgress.done();
    return true;
  }
  // 否则，如果没登录，就重定向到 login
  if (!isLogin) {
    NProgress.done();
    return { name: "login" };
  }
  return true;
});

// 全局后置钩子
router.afterEach(() => {
  NProgress.done();
});
// 捕获路由错误
router.onError(() => {
  NProgress.done();
});
export default router;
