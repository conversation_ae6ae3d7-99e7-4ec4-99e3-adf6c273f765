import type { RouteRecordRaw } from "vue-router";

export const CarMoveRoutes: RouteRecordRaw[] = [
  {
    path: "/car-move",
    component: () => import("@/pages/car-move/index.vue"),
    meta: {
      title: "挪车服务",
      showFooterNavbar: true,
      goBackTo: { path: "/" }, // 也支持 {name:'CarMoveNotice'}
      extra: [
        // {
        //   // 挪车提醒
        //   path: "/car-move/notice/warn",
        //   icon: "#icon-car-remind",
        // },
        { path: "/car-move/notice/list", icon: "#icon-car-feedback" }, // 通知反馈
      ],
    },
  },
  {
    path: "/car-move/notice",
    name: "CarMoveNotice",
    component: () => import("@/pages/car-move/notice/index.vue"),
    meta: { title: "通知挪车", showFooterNavbar: false },
  },
  {
    path: "/car-move/notice/list",
    component: () => import("@/pages/car-move/notice/NoticeList.vue"),
    meta: {
      title: "通知反馈",
      goBackTo: { path: "/car-move" },
      extra: [
        {
          showFooterNavbar: false,
          eventName: "clearNoticeList",
          icon: "#icon-car-clear",
        }, // 清除列表
      ],
    },
  },
  {
    path: "/car-move/notice/way",
    component: () => import("@/pages/car-move/notice/NoticeWay.vue"),
    meta: { title: "通知方式", showFooterNavbar: false },
  },
  {
    path: "/car-move/notice/warn",
    component: () => import("@/pages/car-move/notice/NoticeWarn.vue"),
    meta: { title: "挪车提醒", showFooterNavbar: false },
  },
  {
    name: "car-move-add",
    path: "/car-move/add/:id?",
    component: () => import("@/pages/car-move/CarAdd.vue"),
    props: true,
    meta: { title: "个人车辆信息", showFooterNavbar: false },
  },
  // 录入
  {
    path: "/car-move/enter",
    component: () => import("@/pages/car-move/CarEnter.vue"),
    meta: { title: "挪车服务", showFooterNavbar: true },
  },
];
