import type { RouteRecordRaw } from 'vue-router';

const MettingRoutes: RouteRecordRaw[] = [
  {
    name: 'meeting-handle',
    path: '/meeting-handle',
    component: () => import('@/pages/meet-assistant/metting-handle/MettingHandle.vue'),
    meta: {
      title: '会议助手', // title
      showNavbar: true, // 导航栏
      // banner: '/src/assets/image/home/<USER>', // 如 首页banner
      showGoback: true, // 返回上一级
      hideHeader: false,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  // 后续title支持动态修改后，可以放到上面id为可选
  {
    name: 'meeting-handle-edit',
    path: '/meeting-handle-edit/:id',
    component: () => import('@/pages/meet-assistant/metting-handle/MettingHandle.vue'),
    props: true,
    meta: {
      title: '修改会议', // title
      showNavbar: true, // 导航栏
      // banner: '/src/assets/image/home/<USER>', // 如 首页banner
      showGoback: true, // 返回上一级
      hideHeader: false,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  {
    name: 'meeting-info',
    path: '/meeting-info/:id',
    component: () => import('@/pages/meet-assistant/metting-handle/MettingInfo.vue'),
    meta: {
      title: '会议详情', // title
      showNavbar: true, // 导航栏
      // banner: '/src/assets/image/home/<USER>', // 如 首页banner
      showGoback: true, // 返回上一级
      hideHeader: false,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  // 会议主题
  {
    name: 'meeting-title',
    path: '/meeting-title',
    component: () => import('@/pages/meet-assistant/metting-handle/components/meetingTitle/MettingTitle.vue'),
    props: { title: '会议主题', isRequire: true },
    meta: {
      title: '会议主题', // title
      showNavbar: true, // 导航栏
      showGoback: true, // 返回上一级
      hideHeader: false,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  // 会议内容
  {
    name: 'meeting-content',
    path: '/meeting-content',
    component: () => import('@/pages/meet-assistant/metting-handle/components/meetingTitle/MettingTitle.vue'),
    props: { title: '会议内容', isRequire: false },
    meta: {
      title: '会议内容', // title
      showNavbar: true, // 导航栏
      showGoback: true, // 返回上一级
      hideHeader: false,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  // 更多人员
  {
    name: 'meeting-handle-add-personnel',
    path: '/meeting-handle-add-personnel',
    component: () => import('@/pages/meet-assistant/metting-handle/components/personnel/AddPersonnel.vue'),
    meta: {
      title: '添加人员', // title
      showNavbar: false, // 导航栏
      showGoback: false, // 返回上一级
      hideHeader: true,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  //  '新增人员'
  {
    name: 'meeting-handle-add-external',
    path: '/meeting-handle-add-external',
    component: () => import('@/pages/meet-assistant/metting-handle/components/personnel/AddExternalPerson.vue'),
    meta: {
      title: '新增人员', // title
      showNavbar: true, // 导航栏
      showGoback: true, // 返回上一级
      hideHeader: false,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  // 会议 添加人员 新建工作组
  {
    name: 'meeting-handle-add-group',
    path: '/meeting-handle-add-group',
    component: () => import('@/pages/meet-assistant/metting-handle/components/personnel/AddPersonnelGroup.vue'),
    meta: {
      title: '新建工作组', // title
      showNavbar: false, // 导航栏
      showGoback: false, // 返回上一级
      hideHeader: true,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  // 会议 添加人员 新建工作组
  {
    name: 'meeting-handle-add-group-personnel',
    path: '/meeting-handle-add-group-personnel',
    component: () => import('@/pages/meet-assistant/metting-handle/components/personnel/AddPersonnelGroupPersonnel.vue'),
    meta: {
      title: '添加人员', // title
      showNavbar: true, // 导航栏
      showGoback: true, // 返回上一级
      hideHeader: false,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  // 会议提醒
  {
    name: 'meeting-handle-remind',
    path: '/meeting-handle-remind',
    component: () => import('@/pages/meet-assistant/metting-handle/components/meetingRemind/MeetingRemind.vue'),
    meta: {
      title: '会议提醒', // title
      showNavbar: true, // 导航栏
      showGoback: true, // 返回上一级
      hideHeader: false,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  // 预定时间 Notification
  {
    name: 'meeting-handle-time',
    path: '/meeting-handle-time',
    component: () => import('@/pages/meet-assistant/metting-handle/components/timeHandle/TimeHandle.vue'),
    meta: {
      title: '预定时间', // title
      showNavbar: true, // 导航栏
      showGoback: true, // 返回上一级
      hideHeader: false,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  // 通知方式
  {
    name: 'meeting-handle-notification',
    path: '/meeting-handle-notification',
    component: () => import('@/pages/meet-assistant/metting-handle/components/notification/Notification.vue'),
    meta: {
      title: '通知方式', // title
      showNavbar: true, // 导航栏
      showGoback: true, // 返回上一级
      hideHeader: false,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  // 参与详情
  {
    name: 'meeting-info-participation',
    path: '/meeting-info-participation/:id',
    component: () => import('@/pages/meet-assistant/metting-handle/components/participation/Participation.vue'),
    props: true,
    meta: {
      title: '参与详情', // title
      showNavbar: true, // 导航栏
      showGoback: true, // 返回上一级
      hideHeader: false,
      extra: [], // 导航栏额外操作，没有不配置
    },
  },
  {
    name: 'meetSchedule',
    path: '/meetSchedule',
    component: () => import('@/pages/meet-assistant/meet-schedule/meetSchedule.vue'),
    meta: {
      hideHeader: true,
    },
  },
  {
    name: 'meetReserved',
    path: '/meetReserved',
    component: () => import('@/pages/meet-assistant/meet-reserved/meetReserved.vue'),
    meta: {
      title: '会议助手',
      showFooterNavbar: true,
      goBackTo: { path: '/meetSchedule' },
      extra: [
        {
          // 挪车提醒
          path: '/meeting-handle',
          icon: '#icon-meeting-16-6',
        },
      ],
    },
  },
  {
    name: 'meetRoom',
    path: '/meetRoom',
    component: () => import('@/pages/meet-assistant/meet-room/meetRoom.vue'),
    meta: {
      // hideHeader: true,
      title: '会议助手',
      goBackTo: { path: '/home' },
    },
  },
  // 前往会议室
  {
    name: 'goMeetRoom',
    path: '/goMeetRoom',
    component: () => import('@/pages/meet-assistant/go-meetRoom/goMeetRoom.vue'),
  },
];
export default MettingRoutes;
