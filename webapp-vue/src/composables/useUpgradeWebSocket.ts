import { ref, onUnmounted } from "vue";
import type { WsMessage } from "../models/Websocket";

export function useUpgradeWebSocket() {
  const status = ref(0); // 0 未连接 1 连接中 2 已连接 3 错误 4 连接断开
  const progress = ref(5);
  const isDone = ref(false);
  // const logs = ref<string[]>([]);
  let socket: WebSocket | null = null;

  const isProd = import.meta.env.APP_NODE_ENV === "production";
  // ✅ 自动重连控制
  const reconnectAttempts = ref(0);
  const maxReconnectAttempts = 15;
  const reconnectDelay = 1000;
  let reconnectTimer: ReturnType<typeof setTimeout> | null = null;
  const host = isProd
    ? window.location.host // 生产环境直接用当前域名+端口
    : "127.0.0.1:5000"; // 开发环境固定指向 5000

  // 根据协议决定 ws 还是 wss
  const wsProto = window.location.protocol === "https:" ? "wss" : "ws";

  // 最终 URL
  const wsUrl = `${wsProto}://${host}/ws`;
  const connect = () => {
    if (socket) socket.close(); // 清理旧连接
    // socket = new WebSocket(
    //   `ws://${isProd ? window.location.host : "127.0.0.1:5000"}/ws`
    // );
    socket = new WebSocket(wsUrl);
    isDone.value = false;
    status.value = 1;
    socket.onopen = () => {
      status.value = 2;
      // logs.value.push("✅ 升级服务连接成功");
      console.log("[Upgrade] ✅ 升级服务连接成功");
      reconnectAttempts.value = 0;
    };
    socket.onmessage = (e) => {
      try {
        const data: WsMessage<{
          status: string;
          message: string;
          progress: number | null;
        }> = JSON.parse(e.data);
        const { channel, payload } = data;
        if (channel === "upgrade") {
          // console.log("[升级服务data]==============", timestamp);
          // console.log(data);
          progress.value = payload?.progress ?? 5;
          // logs.value.push(`[${timestamp}] ${payload?.message}`);
          // console.log(`[${timestamp}] ${payload?.message}`);
          if (payload?.status === "done") {
            isDone.value = true;
            progress.value = 100;
            disconnect();
          }
        }
      } catch {
        console.log(`[升级服务格式错误] ${e.data}`);
        // logs.value.push(`[格式错误] ${e.data}`);
      }
    };

    socket.onerror = (e) => {
      status.value = 3;
      // logs.value.push("❌ WebSocket 错误");
      console.log("[Upgrade] ❌升级服务错误", e);
    };

    socket.onclose = () => {
      status.value = 4;
      // logs.value.push("🔌 升级服务已关闭");
      console.log("[Upgrade] 🔌 升级服务已关闭");
      if (isDone.value === true) {
        if (reconnectTimer) clearTimeout(reconnectTimer);
      } else {
        tryReconnect();
      }
    };
  };

  // ✅ 重连逻辑
  const tryReconnect = () => {
    if (reconnectAttempts.value >= maxReconnectAttempts) {
      // logs.value.push("❌ 已超过最大重连次数，停止尝试");
      console.log("[Upgrade] ❌ 已超过最大重连次数，停止尝试");
      return;
    }

    reconnectAttempts.value++;
    // logs.value.push(`🔁 第 ${reconnectAttempts.value} 次尝试重连...`);
    console.log(`[Upgrade] 🔁 第 ${reconnectAttempts.value} 次尝试重连...`);
    reconnectTimer = setTimeout(() => {
      connect();
    }, reconnectDelay);
  };

  const disconnect = () => {
    if (reconnectTimer) clearTimeout(reconnectTimer);
    if (socket) {
      socket.close();
      socket = null;
    }
  };

  onUnmounted(disconnect);

  return { connect, disconnect, status, progress };
}
