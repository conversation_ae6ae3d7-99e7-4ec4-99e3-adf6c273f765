<template>
    <n-input-group :class="[
        'sl-ip-input-group',
        { error: isError }
    ]">
        <template v-for="(_, index) in segments" :key="index">
            <n-input-number v-model:value="segments[index]" :bordered="false" :min="0" :max="255" :show-button="false"
                :disabled="disabled" :readonly="readonly" :style="{ width: '25%' }" placeholder="">
                <template #suffix v-if="index < 3">
                    <span class="dot">.</span>
                </template>
            </n-input-number>
        </template>
    </n-input-group>
</template>

<script setup lang="ts">
import { ref, watch, computed, toRefs } from 'vue'
import { NInputGroup, NInputNumber } from 'naive-ui'

interface Props {
    modelValue: string
    required?: boolean
    disabled?: boolean
    readonly?: boolean
}

const props = withDefaults(
    defineProps<Props>(),
    {
        required: false,
        disabled: false,
        readonly: false
    }
)

const { required, disabled, readonly } = toRefs(props)

const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void
}>()

// 四段 IP，可为 null 表示该段空
const segments = ref<(number | null)[]>([null, null, null, null])

// 外部 v-model 同步进来
watch(
    () => props.modelValue,
    (val) => {
        const parts = val ? val.split('.') : []
        segments.value = [0, 1, 2, 3].map((_, i) => {
            const p = parts[i]
            if (!p) return null
            const num = Number(p)
            return /^\d{1,3}$/.test(p) && num >= 0 && num <= 255
                ? num
                : null
        })
    },
    { immediate: true }
)

// 是否用户有任何输入
const hasAny = computed(() =>
    segments.value.some(v => v !== null)
)
// 是否存在空段
const hasEmpty = computed(() =>
    segments.value.some(v => v === null)
)

// 最终是否要高亮报错
const isError = computed(() => {
    if (required.value) {
        // 必填：任何空都报错
        return hasEmpty.value
    } else {
        // 非必填：只有「部分输入但不完整」才报错
        return hasAny.value && hasEmpty.value
    }
})

// 任何输入变化都拼回 string 并 emit
watch(
    segments,
    (segs) => {
        const hasAny = segs.some(v => v !== null)
        const allEmpty = !hasAny
        const ip = allEmpty
            ? ''
            : segs.map(v => (v === null ? '' : String(v))).join('.')
        emit('update:modelValue', ip)
    },
    { deep: true }
    // segments,
    // (segs) => {
    //     const ip = segs
    //         .map(v => (v === null ? '' : String(v)))
    //         .join('.')
    //     emit('update:modelValue', ip)
    // },
    // { deep: true }
)
</script>

<style scoped>
.sl-ip-input-group {
    display: flex;
    border: 1px solid rgb(224, 224, 230);
    border-radius: 3px;
}

.sl-ip-input-group.error {
    border-color: #d03050;
}

.sl-ip-input-group:hover {
    border-color: #4f7af6;
}

.dot {
    font-weight: bold;
    margin: 0 4px;
}
</style>