<template>
  <div class="sl-mr-container">
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1"
      width="291" height="146.999755859375" viewBox="0 0 291 146.999755859375" @click.self="clearSelection" ref="svgEl">
      <defs>
        <clipPath id="master_svg0_53_8732">
          <rect x="92.5" y="124.5" width="6" height="6" rx="0" />
        </clipPath>
        <clipPath id="master_svg1_53_8741">
          <rect x="133.5" y="124.5" width="6" height="6" rx="0" />
        </clipPath>
        <clipPath id="master_svg0_32_03912">
          <rect x="0" y="0" width="16" height="16" rx="0" />
        </clipPath>
        <linearGradient x1="0.4583958089351654" y1="-0.1322835236787796" x2="1.2632941989403146" y2="0.5322777142255726"
          id="master_svg1_1_5010">
          <stop offset="0%" stop-color="#3E73FF" stop-opacity="1" />
          <stop offset="100%" stop-color="#3EF9FF" stop-opacity="1" />
        </linearGradient>
        <filter id="master_svg2_32_03912/1_08328" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB"
          x="-0.6666666666666666" y="-0.5" width="2.3333333333333335" height="2">
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
          <feOffset dy="0" dx="0" />
          <feGaussianBlur stdDeviation="1" />
          <feColorMatrix type="matrix"
            values="0 0 0 0 0.23893804848194122 0 0 0 0 1 0 0 0 0 0.9238938093185425 0 0 0 0.30000001192092896 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape" />
        </filter>
        <linearGradient x1="0.8333751559257507" y1="0.7578933238983154" x2="0.4687796567421075"
          y2="-0.052560190527749434" id="master_svg3_1_5013">
          <stop offset="0%" stop-color="#3E5BFF" stop-opacity="1" />
          <stop offset="100%" stop-color="#3EFFFF" stop-opacity="1" />
        </linearGradient>
        <marker id="arrow" viewBox="0 0 10 10" refX="5" refY="5" markerWidth="6" markerHeight="6" orient="auto">
          <path d="M0,0 L10,5 L0,10 Z" fill="#f56c6c" />
        </marker>
      </defs>
      <g>
        <!--女卫-->
        <g id="ladysRoom">
          <rect x="0.5" y="0.5" width="21" height="66" rx="0" fill="#E4E4E4" fill-opacity="1" />
          <rect x="0.6500000059604645" y="0.6500000059604645" width="20.69999998807907" height="65.69999998807907"
            rx="0" fill-opacity="0" stroke-opacity="1" stroke="#999999" fill="none"
            stroke-width="0.30000001192092896" />
        </g>
        <g transform="matrix(1,0,0,-1,0,147)">
          <path
            d="M14.4942,80.8C14.4981,80.70041,14.5,80.60041,14.5,80.5L8,80.5L8,87.5C8.10045,87.5,8.20045,87.4976,8.3,87.4928C9.05475,87.4562,9.7839,87.281,10.48744,86.9672C11.28378,86.6119,11.9867,86.1061,12.5962,85.4497Q13.5104,84.4652,14.0052,83.17878C14.2994,82.41396,14.4624,81.62103,14.4942,80.8ZM14.194,80.8Q14.1465,81.97581,13.7252,83.07109Q13.2512,84.3035,12.3764,85.2456Q11.5038,86.1853,10.36523,86.6932Q9.36836,87.1379,8.3,87.19239999999999L8.3,80.8L14.194,80.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <!--办公室-->
        <g>
          <rect x="0.5" y="81.5" width="25" height="40" rx="0" fill="#E4E4E4" fill-opacity="1" />
          <rect x="0.6500000059604645" y="81.65000000596046" width="24.69999998807907" height="39.69999998807907" rx="0"
            fill-opacity="0" stroke-opacity="1" stroke="#999999" fill="none" stroke-width="0.30000001192092896" />
        </g>
        <g>
          <path
            d="M14.4942,81.8C14.4981,81.70041,14.5,81.60041,14.5,81.5L8,81.5L8,88.5C8.10045,88.5,8.20045,88.4976,8.3,88.4928C9.05475,88.4562,9.7839,88.281,10.48744,87.9672C11.28378,87.6119,11.9867,87.1061,12.5962,86.4497Q13.5104,85.4652,14.0052,84.17878C14.2994,83.41396,14.4624,82.62103,14.4942,81.8ZM14.194,81.8Q14.1465,82.97581,13.7252,84.07109Q13.2512,85.3035,12.3764,86.2456Q11.5038,87.1853,10.36523,87.6932Q9.36836,88.1379,8.3,88.19239999999999L8.3,81.8L14.194,81.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <!--值班室-->
        <g>
          <path d="M25.5,121.5L32,121.5L32,117.4755L55.5,117.4755L55.5,81.5L25.5,81.5L25.5,121.5Z" fill="#E4E4E4"
            fill-opacity="1" />
          <path
            d="M55.5,117.4755L32,117.4755L32,121.5L25.5,121.5L25.5,81.5L55.5,81.5L55.5,117.4755ZM31.7,117.1755L31.7,121.2L25.8,121.2L25.8,81.8L55.2,81.8L55.2,117.1755L31.7,117.1755Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g transform="matrix(-1,0,0,1,121,0)">
          <path
            d="M73.4942,81.8C73.4981,81.70041,73.5,81.60041,73.5,81.5L67,81.5L67,88.5C67.10045,88.5,67.20045,88.4976,67.3,88.4928C68.05475,88.4562,68.7839,88.281,69.48743999999999,87.9672C70.28378000000001,87.6119,70.9867,87.1061,71.5962,86.4497Q72.5104,85.4652,73.0052,84.17878C73.2994,83.41396,73.4624,82.62103,73.4942,81.8ZM73.194,81.8Q73.1465,82.97581,72.7252,84.07109Q72.2512,85.3035,71.3764,86.2456Q70.5038,87.1853,69.36523,87.6932Q68.36836,88.1379,67.3,88.19239999999999L67.3,81.8L73.194,81.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g transform="matrix(1,0,0,-1,0,267)">
          <path d="M29.5,145.4998L32,145.4998L32,149.5L55.5,149.5L55.5,133.5L29.5,133.5L29.5,145.4998Z" fill="#E4E4E4"
            fill-opacity="1" />
          <path
            d="M32,149.5L32,145.4998L29.5,145.4998L29.5,133.5L55.5,133.5L55.5,149.5L32,149.5ZM32.3,145.1998L29.8,145.1998L29.8,133.8L55.2,133.8L55.2,149.2L32.3,149.2L32.3,145.1998Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g transform="matrix(-1,0,0,1,121,0)">
          <path
            d="M73.4942,117.8C73.4981,117.70041,73.5,117.60041,73.5,117.5L67,117.5L67,124.5C67.10045,124.5,67.20045,124.4976,67.3,124.4928C68.05475,124.4562,68.7839,124.281,69.48743999999999,123.9672C70.28378000000001,123.6119,70.9867,123.1061,71.5962,122.4497Q72.5104,121.4652,73.0052,120.17878C73.2994,119.41396,73.4624,118.62103,73.4942,117.8ZM73.194,117.8Q73.1465,118.97581,72.7252,120.07109Q72.2512,121.3035,71.3764,122.2456Q70.5038,123.1853,69.36523,123.6932Q68.36836,124.1379,67.3,124.19239999999999L67.3,117.8L73.194,117.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <!--男卫-->
        <g>
          <rect x="21.5" y="0.5" width="21" height="66" rx="0" fill="#E4E4E4" fill-opacity="1" />
          <rect x="21.650000005960464" y="0.6500000059604645" width="20.69999998807907" height="65.69999998807907"
            rx="0" fill-opacity="0" stroke-opacity="1" stroke="#999999" fill="none"
            stroke-width="0.30000001192092896" />
        </g>
        <g transform="matrix(-1,0,0,-1,87,147)">
          <path
            d="M56.4942,80.8C56.4981,80.70041,56.5,80.60041,56.5,80.5L50,80.5L50,87.5C50.10045,87.5,50.200450000000004,87.4976,50.3,87.4928C51.05475,87.4562,51.7839,87.281,52.48744,86.9672C53.28378,86.6119,53.9867,86.1061,54.596199999999996,85.4497Q55.510400000000004,84.4652,56.0052,83.17878C56.2994,82.41396,56.4624,81.62103,56.4942,80.8ZM56.194,80.8Q56.1465,81.97581,55.7252,83.07109Q55.2512,84.3035,54.376400000000004,85.2456Q53.5038,86.1853,52.36523,86.6932Q51.36836,87.1379,50.3,87.19239999999999L50.3,80.8L56.194,80.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <!--510/511办公室-->
        <g>
          <path
            d="M96.5,11.61475L99.02688,11.61475L99.02688,58.3689L114.7098,58.3689L114.7098,66.5L168.7688,66.5L168.7688,52.7787L190.5,52.7787L190.5,4.5L96.5,4.5L96.5,11.61475Z"
            fill="#E4E4E4" fill-opacity="1" />
          <path
            d="M190.5,52.7787L168.7688,52.7787L168.7688,66.5L114.7098,66.5L114.7098,58.3689L99.02688,58.3689L99.02688,11.61475L96.5,11.61475L96.5,4.5L190.5,4.5L190.5,52.7787ZM168.4688,52.4787L168.4688,66.2L115.0098,66.2L115.0098,58.0689L99.32688,58.0689L99.32688,11.31475L96.8,11.31475L96.8,4.8L190.2,4.8L190.2,52.4787L168.4688,52.4787Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g>
          <path d="M232.5,133.5L259.5,133.5L259.5,119.5L232.5,119.5L232.5,133.5Z" fill="#E4E4E4" fill-opacity="1" />
          <path
            d="M232.5,133.5L259.5,133.5L259.5,119.5L232.5,119.5L232.5,133.5ZM232.8,119.8L259.2,119.8L259.2,133.2L232.8,133.2L232.8,119.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g transform="matrix(-1,0,0,1,525,0)">
          <path
            d="M275.4942,119.8C275.4981,119.70041,275.5,119.60041,275.5,119.5L269,119.5L269,126.5C269.10045,126.5,269.20045,126.4976,269.3,126.4928C270.05475,126.4562,270.7839,126.281,271.48744,125.9672C272.28378,125.6119,272.9867,125.1061,273.5962,124.4497Q274.5104,123.4652,275.0052,122.17878C275.2994,121.41396,275.4624,120.62103,275.4942,119.8ZM275.194,119.8Q275.1465,120.97581,274.7252,122.07109Q274.2512,123.3035,273.3764,124.2456Q272.5038,125.1853,271.36523,125.6932Q270.36836,126.1379,269.3,126.19239999999999L269.3,119.8L275.194,119.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g>
          <path d="M61.5,66.5L80.5,66.5L80.5,60.5L61.5,60.5L61.5,66.5Z" fill="#E4E4E4" fill-opacity="1" />
          <path
            d="M61.5,66.5L80.5,66.5L80.5,60.5L61.5,60.5L61.5,66.5ZM61.8,60.8L80.2,60.8L80.2,66.2L61.8,66.2L61.8,60.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g>
          <path d="M98.5,66.5L114.5,66.5L114.5,58.5L98.5,58.5L98.5,66.5Z" fill="#E4E4E4" fill-opacity="1" />
          <path
            d="M98.5,66.5L114.5,66.5L114.5,58.5L98.5,58.5L98.5,66.5ZM98.8,58.8L114.2,58.8L114.2,66.2L98.8,66.2L98.8,58.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g>
          <path d="M168.5,52.5L168.5,63.5L190.5,63.5L190.5,52.5L168.5,52.5Z" fill="#E4E4E4" fill-opacity="1" />
          <path
            d="M168.5,52.5L168.5,63.5L190.5,63.5L190.5,52.5L168.5,52.5ZM168.8,52.8L190.2,52.8L190.2,63.2L168.8,63.2L168.8,52.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g transform="matrix(0,1,-1,0,142,-19)">
          <g transform="matrix(0,-0.9999452829360962,0.9999452829360962,0,-0.9955405592918396,161.99559527635574)">
            <path
              d="M90.48988,86.8C90.49572,86.70058,90.49864,86.60058,90.49864,86.5L85.49932,86.5L85.49932,91.5C85.5999,91.5,85.6999,91.49708,85.79932,91.49124C86.3552,91.45858,86.89291,91.33464000000001,87.41248,91.1194C88.02496,90.86566,88.5656,90.50438,89.03437,90.03553Q89.73754,89.33227,90.11809,88.41342C90.3333,87.89377,90.45724,87.35597,90.48988,86.8ZM90.18932,86.8Q90.14089,87.57433,89.84092,88.29863Q89.4832,89.16235,88.82223,89.82342Q88.16126,90.48448,87.29766,90.84224Q86.57351,91.14223,85.79932,91.19067L85.79932,86.8L90.18932,86.8Z"
              fill-rule="evenodd" fill="#999999" fill-opacity="1" />
          </g>
          <g transform="matrix(0,0.9999452829360962,0.9999452829360962,0,19.003365099430084,-18.995595276355743)">
            <path
              d="M90.48934,66.79973Q90.49809,66.65059,90.49809,66.49973L85.49905,66.49973L85.49905,71.49945C85.59963,71.49945,85.69963,71.49653,85.79905,71.49069C86.35489,71.45804,86.89257,71.3341,87.4121,71.11887Q88.33078,70.73829,89.03391,70.03507Q89.73703,69.33184,90.11756,68.41304Q90.44036,67.63362,90.48934,66.79973ZM90.18877,66.79973Q90.14034,67.57401,89.84039,68.29825Q89.4827,69.16192,88.82176,69.82295Q88.16083,70.48398,87.29728,70.84171Q86.57318,71.14169,85.79905,71.19013L85.79905,66.79973L90.18877,66.79973Z"
              fill-rule="evenodd" fill="#999999" fill-opacity="1" />
          </g>
        </g>
        <g transform="matrix(0,1,-1,0,177,-54)">
          <g transform="matrix(0,-0.9999452829360962,0.9999452829360962,0,34.00445944070816,196.9936801791191)">
            <path
              d="M125.48988,86.8C125.49572,86.70058,125.49864,86.60058,125.49864,86.5L120.49932,86.5L120.49932,91.5C120.5999,91.5,120.6999,91.49708,120.79932,91.49124C121.3552,91.45858,121.89291,91.33464000000001,122.41248,91.1194C123.02496,90.86566,123.5656,90.50438,124.03437,90.03553Q124.73754,89.33227,125.11809,88.41342C125.3333,87.89377,125.45724,87.35597,125.48988,86.8ZM125.18932,86.8Q125.14089,87.57433,124.84092,88.29863Q124.4832,89.16235,123.82223,89.82342Q123.16126,90.48448,122.29766,90.84224Q121.57351,91.14223,120.79932,91.19067L120.79932,86.8L125.18932,86.8Z"
              fill-rule="evenodd" fill="#999999" fill-opacity="1" />
          </g>
          <g transform="matrix(0,0.9999452829360962,0.9999452829360962,0,54.003365099430084,-53.99368017911911)">
            <path
              d="M125.48934,66.79973Q125.49809,66.65059,125.49809,66.49973L120.49905,66.49973L120.49905,71.49945C120.59963,71.49945,120.69963,71.49653,120.79905,71.49069C121.35489,71.45804,121.89257,71.3341,122.4121,71.11887Q123.33078,70.73829,124.03391,70.03507Q124.73703,69.33184,125.11756,68.41304Q125.44036,67.63362,125.48934,66.79973ZM125.18877,66.79973Q125.14034,67.57401,124.84039,68.29825Q124.4827,69.16192,123.82176,69.82295Q123.16083,70.48398,122.29728,70.84171Q121.57318,71.14169,120.79905,71.19013L120.79905,66.79973L125.18877,66.79973Z"
              fill-rule="evenodd" fill="#999999" fill-opacity="1" />
          </g>
        </g>
        <g transform="matrix(0,1,-1,0,251,-134)">
          <g transform="matrix(0,-0.9999452829360962,0.9999452829360962,0,114.00429528951645,270.9894669651985)">
            <path
              d="M202.48988,83.8C202.49572,83.70058,202.49864,83.60058,202.49864,83.5L197.49932,83.5L197.49932,88.5C197.5999,88.5,197.6999,88.49708,197.79932,88.49124C198.3552,88.45858,198.89291,88.33464000000001,199.41248,88.1194C200.02496,87.86566,200.5656,87.50438,201.03437,87.03553Q201.73754,86.33227,202.11809,85.41342C202.3333,84.89377,202.45724,84.35597,202.48988,83.8ZM202.18932,83.8Q202.14089,84.57433,201.84092,85.29863Q201.4832,86.16235,200.82223,86.82342Q200.16126,87.48448,199.29766,87.84224Q198.57351,88.14223,197.79932,88.19067L197.79932,83.8L202.18932,83.8Z"
              fill-rule="evenodd" fill="#999999" fill-opacity="1" />
          </g>
          <g transform="matrix(0,0.9999452829360962,0.9999452829360962,0,134.00320094823837,-133.98946696519852)">
            <path
              d="M202.48934,63.79973Q202.49809,63.65059,202.49809,63.49973L197.49905,63.49973L197.49905,68.49945C197.59963,68.49945,197.69963,68.49653,197.79905,68.49069C198.35489,68.45804,198.89257,68.3341,199.4121,68.11887Q200.33078,67.73829,201.03391,67.03507Q201.73703,66.33184,202.11756,65.41304Q202.44036,64.63362,202.48934,63.79973ZM202.18877,63.79973Q202.14034,64.57401,201.84039,65.29825Q201.4827,66.16192,200.82176,66.82295Q200.16083,67.48398,199.29728,67.84171Q198.57318,68.14169,197.79905,68.19013L197.79905,63.79973L202.18877,63.79973Z"
              fill-rule="evenodd" fill="#999999" fill-opacity="1" />
          </g>
        </g>
        <!--510/511与楼梯间靠墙区域-->
        <g>
          <path d="M190.5,22.5L205.5,22.5L205.5,4.5L190.5,4.5L190.5,22.5Z" fill="#E4E4E4" fill-opacity="1" />
          <path
            d="M190.5,22.5L205.5,22.5L205.5,4.5L190.5,4.5L190.5,22.5ZM190.8,4.8L205.2,4.8L205.2,22.2L190.8,22.2L190.8,4.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <!--501办公室-->
        <g>
          <path
            d="M225.5,81.67L231.59375,81.67L231.74352,119.673L259.0156,119.673L259.0156,125.5L290.5,125.5L290.5,0.5L225.5,0.5L225.5,66.0242L225.5,81.67Z"
            fill="#E4E4E4" fill-opacity="1" />
          <path
            d="M259.0156,125.5L259.0156,119.673L231.74352,119.673L231.59375,81.67L225.5,81.67L225.5,0.5L290.5,0.5L290.5,125.5L259.0156,125.5ZM259.3156,119.373L232.04234,119.373L231.89257,81.37L225.8,81.37L225.8,0.8L290.2,0.8L290.2,125.2L259.3156,125.2L259.3156,119.373Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g>
          <g transform="matrix(0,-0.9999452829360962,0.9999452829360962,0,131.0055201248906,305.987311899662)">
            <path
              d="M231.4942,94.799267578125C231.4981,94.699677578125,231.5,94.599677578125,231.5,94.499267578125L225,94.499267578125L225,101.499267578125C225.10045,101.499267578125,225.20045,101.496867578125,225.3,101.492067578125C226.05475,101.455467578125,226.7839,101.280267578125,227.48744,100.966467578125C228.28378,100.611167578125,228.9867,100.105367578125,229.5962,99.448967578125Q230.5104,98.464467578125,231.0052,97.178047578125C231.2994,96.413227578125,231.4624,95.620297578125,231.4942,94.799267578125ZM231.194,94.799267578125Q231.1465,95.975077578125,230.7252,97.070357578125Q230.2512,98.302767578125,229.3764,99.244867578125Q228.5038,100.184567578125,227.36523,100.692467578125Q226.36836,101.137167578125,225.3,101.19166757812499L225.3,94.799267578125L231.194,94.799267578125Z"
              fill-rule="evenodd" fill="#999999" fill-opacity="1" />
          </g>
          <g transform="matrix(0,0.9999452829360962,0.9999452829360962,0,157.00336509943008,-156.98804432153702)">
            <path
              d="M231.4993,68.49964L231.4993,68.49962L224.99964,68.49962L224.99964,75.4992C225.10009,75.4992,225.20009,75.49680000000001,225.29964,75.492C226.05435,75.4554,226.78345,75.28020000000001,227.48695,74.9664C228.28325,74.6112,228.9861,74.1054,229.5956,73.4491Q230.5098,72.4646,231.0045,71.17826C231.2987,70.41347,231.4617,69.62058999999999,231.4935,68.79962C231.4974,68.70004,231.4993,68.60005,231.4993,68.49964ZM231.1933,68.79962Q231.1458,69.97534,230.7245,71.07056Q230.2506,72.3029,229.3757,73.245Q228.5032,74.1846,227.36474,74.69239999999999Q226.36794,75.1371,225.29964,75.1916L225.29964,68.79962L231.1933,68.79962Z"
              fill-rule="evenodd" fill="#999999" fill-opacity="1" />
          </g>
        </g>
        <g transform="matrix(0,-0.9999452829360962,0.9999452829360962,0,156.00150471925735,210.98995941877365)">
          <path
            d="M196.4942,34.8C196.4981,34.70041,196.5,34.60041,196.5,34.5L190,34.5L190,41.5C190.10045,41.5,190.20045,41.4976,190.3,41.4928C191.05475,41.4562,191.7839,41.281,192.48744,40.9672C193.28378,40.6119,193.9867,40.1061,194.5962,39.4497Q195.5104,38.465199999999996,196.0052,37.17878C196.2994,36.41396,196.4624,35.62103,196.4942,34.8ZM196.194,34.8Q196.1465,35.975809999999996,195.7252,37.07109Q195.2512,38.3035,194.3764,39.245599999999996Q193.5038,40.1853,192.36523,40.6932Q191.36836,41.1379,190.3,41.1924L190.3,34.8L196.194,34.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g>
          <path
            d="M183.4942,81.8C183.4981,81.70041,183.5,81.60041,183.5,81.5L177,81.5L177,88.5C177.10045,88.5,177.20045,88.4976,177.3,88.4928C178.05475,88.4562,178.7839,88.281,179.48744,87.9672C180.28378,87.6119,180.9867,87.1061,181.5962,86.4497Q182.5104,85.4652,183.0052,84.17878C183.2994,83.41396,183.4624,82.62103,183.4942,81.8ZM183.194,81.8Q183.1465,82.97581,182.7252,84.07109Q182.2512,85.3035,181.3764,86.2456Q180.5038,87.1853,179.36523,87.6932Q178.36836,88.1379,177.3,88.19239999999999L177.3,81.8L183.194,81.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g transform="matrix(-1,0,0,1,465,0)">
          <path
            d="M245.4942,81.8C245.4981,81.70041,245.5,81.60041,245.5,81.5L239,81.5L239,88.5C239.10045,88.5,239.20045,88.4976,239.3,88.4928C240.05475,88.4562,240.7839,88.281,241.48744,87.9672C242.28378,87.6119,242.9867,87.1061,243.5962,86.4497Q244.5104,85.4652,245.0052,84.17878C245.2994,83.41396,245.4624,82.62103,245.4942,81.8ZM245.194,81.8Q245.1465,82.97581,244.7252,84.07109Q244.2512,85.3035,243.3764,86.2456Q242.5038,87.1853,241.36523,87.6932Q240.36836,88.1379,239.3,88.19239999999999L239.3,81.8L245.194,81.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <!--右楼梯间-->
        <g>
          <rect x="205.5" y="4.5" width="20" height="44" rx="0" fill="#E4E4E4" fill-opacity="1" />
          <rect x="205.65000000596046" y="4.6500000059604645" width="19.69999998807907" height="43.69999998807907"
            rx="0" fill-opacity="0" stroke-opacity="1" stroke="#999999" fill="none"
            stroke-width="0.30000001192092896" />
        </g>
        <!--右电梯间-->
        <g id="right-elevator">
          <rect x="205.5" y="48.5" width="20" height="18" rx="0" fill="#E4E4E4" fill-opacity="1" />
          <rect x="205.65000000596046" y="48.650000005960464" width="19.69999998807907" height="17.69999998807907"
            rx="0" fill-opacity="0" stroke-opacity="1" stroke="#999999" fill="none"
            stroke-width="0.30000001192092896" />
        </g>
        <!--左楼梯间-->
        <g>
          <rect x="42.5" y="0.5" width="20" height="66" rx="0" fill="#E4E4E4" fill-opacity="1" />
          <rect x="42.650000005960464" y="0.6500000059604645" width="19.69999998807907" height="65.69999998807907"
            rx="0" fill-opacity="0" stroke-opacity="1" stroke="#999999" fill="none"
            stroke-width="0.30000001192092896" />
        </g>
        <g>
          <g transform="matrix(1,0,0,-1,0,97)">
            <path
              d="M53.4927,54.8Q53.5,54.65073,53.5,54.5L47.5,54.5L47.5,60.5C47.60048,60.5,47.70048,60.4976,47.8,60.4927C48.48848,60.4591,49.15385,60.3093,49.796099999999996,60.0433C50.53119,59.7388,51.18003,59.3053,51.742599999999996,58.742599999999996C52.3053,58.18003,52.7388,57.53119,53.0433,56.796099999999996Q53.4423,55.83272,53.4927,54.8ZM53.1923,54.8Q53.1423,55.773089999999996,52.7661,56.6813Q52.3322,57.728790000000004,51.5305,58.5305Q50.7288,59.3322,49.6813,59.7661Q48.773089999999996,60.1423,47.8,60.1923L47.8,54.8L53.1923,54.8Z"
              fill-rule="evenodd" fill="#999999" fill-opacity="1" />
          </g>
          <g transform="matrix(-1,0,0,-1,123,93)">
            <path
              d="M69.48902,50.8Q69.5,50.651089999999996,69.5,50.5L65.5,50.5L65.5,54.5C65.60073,54.5,65.70073,54.496340000000004,65.8,54.48902C66.22349,54.45779,66.63374,54.35996,67.03073,54.19552Q67.76582,53.891040000000004,68.32843,53.32843Q68.89104,52.76582,69.19552,52.03073Q69.44218,51.43524,69.48902,50.8ZM69.18812,50.8Q69.14214,51.37565,68.91835,51.91593Q68.63671,52.59588,68.1163,53.1163Q67.59588,53.63671,66.91593,53.918350000000004Q66.37565000000001,54.14215,65.8,54.18812L65.8,50.8L69.18812,50.8Z"
              fill-rule="evenodd" fill="#999999" fill-opacity="1" />
          </g>
        </g>
        <g>
          <g transform="matrix(1,0,0,-1,0,145)">
            <path
              d="M53.4927,78.8Q53.5,78.65073,53.5,78.5L47.5,78.5L47.5,84.5C47.60048,84.5,47.70048,84.4976,47.8,84.4927C48.48848,84.4591,49.15385,84.30930000000001,49.796099999999996,84.0433C50.53119,83.7388,51.18003,83.3053,51.742599999999996,82.7426C52.3053,82.18003,52.7388,81.53119,53.0433,80.7961Q53.4423,79.83272,53.4927,78.8ZM53.1923,78.8Q53.1423,79.77309,52.7661,80.6813Q52.3322,81.72879,51.5305,82.5305Q50.7288,83.3322,49.6813,83.7661Q48.773089999999996,84.1423,47.8,84.1923L47.8,78.8L53.1923,78.8Z"
              fill-rule="evenodd" fill="#999999" fill-opacity="1" />
          </g>
          <g transform="matrix(-1,0,0,-1,123,141)">
            <path
              d="M69.48902,74.8Q69.5,74.65109,69.5,74.5L65.5,74.5L65.5,78.5C65.60073,78.5,65.70073,78.49634,65.8,78.48902C66.22349,78.45779,66.63374,78.35996,67.03073,78.19552Q67.76582,77.89104,68.32843,77.32843Q68.89104,76.76582,69.19552,76.03073Q69.44218,75.43524,69.48902,74.8ZM69.18812,74.8Q69.14214,75.37565000000001,68.91835,75.91593Q68.63671,76.59588,68.1163,77.1163Q67.59588,77.63671,66.91593,77.91835Q66.37565000000001,78.14215,65.8,78.18812L65.8,74.8L69.18812,74.8Z"
              fill-rule="evenodd" fill="#999999" fill-opacity="1" />
          </g>
        </g>
        <!--左电梯间-->
        <g id="left-elevator">
          <rect x="62.5" y="4.5" width="18" height="56" rx="0" fill="#E4E4E4" fill-opacity="1" />
          <rect x="62.650000005960464" y="4.6500000059604645" width="17.69999998807907" height="55.69999998807907"
            rx="0" fill-opacity="0" stroke-opacity="1" stroke="#999999" fill="none"
            stroke-width="0.30000001192092896" />
        </g>
        <g>
          <line x1="42.5" y1="42.349999994039536" x2="61.5" y2="42.349999994039536" fill-opacity="0" stroke-opacity="1"
            stroke="#999999" fill="none" stroke-width="0.30000001192092896" />
        </g>
        <g>
          <line x1="62.5" y1="42.349999994039536" x2="80.5" y2="42.349999994039536" fill-opacity="0" stroke-opacity="1"
            stroke="#999999" fill="none" stroke-width="0.30000001192092896" />
        </g>
        <g>
          <line x1="62.5" y1="23.349999994039536" x2="80.5" y2="23.349999994039536" fill-opacity="0" stroke-opacity="1"
            stroke="#999999" fill="none" stroke-width="0.30000001192092896" />
        </g>
        <g>
          <path
            d="M62.393402,5.605532Q62.35,5.561691,62.35,5.5Q62.35,5.4701631,62.361418,5.4425975Q62.372836,5.4150318,62.393934,5.393934Q62.4150318,5.372836,62.4425975,5.361418Q62.4701631,5.35,62.5,5.35Q62.562574,5.35,62.606598,5.394468L80.6066,23.5763Q80.6271,23.597,80.6384,23.6239Q80.64959999999999,23.6508,80.65,23.68Q80.6503,23.7092,80.6397,23.7363Q80.6291,23.7635,80.6091,23.7847L62.709824,42.7688L80.6049,60.3928L80.6053,60.3931Q80.65,60.4372,80.65,60.5Q80.65,60.5298,80.6386,60.5574Q80.6272,60.585,80.6061,60.6061Q80.58500000000001,60.6272,80.5574,60.6386Q80.5298,60.65,80.5,60.65Q80.4385,60.65,80.3947,60.6069L80.3944,60.6066L62.394747,42.8796Q62.373791,42.859,62.36218,42.8319Q62.35057,42.8049,62.350026,42.7755Q62.349481,42.7461,62.360083,42.7187Q62.370684,42.6912,62.390862,42.6698L80.2914,23.6844L62.393402,5.605532Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g transform="matrix(-1,0,0,1,161,0)">
          <path
            d="M80.393402,5.605532Q80.35,5.561691,80.35,5.5Q80.35,5.4701631,80.361418,5.4425975Q80.372836,5.4150318,80.393934,5.393934Q80.4150318,5.372836,80.4425975,5.361418Q80.4701631,5.35,80.5,5.35Q80.562574,5.35,80.606598,5.394468L98.6066,23.5763Q98.6271,23.597,98.6384,23.6239Q98.64959999999999,23.6508,98.65,23.68Q98.6503,23.7092,98.6397,23.7363Q98.6291,23.7635,98.6091,23.7847L80.709824,42.7688L98.6049,60.3928L98.6053,60.3931Q98.65,60.4372,98.65,60.5Q98.65,60.5298,98.6386,60.5574Q98.6272,60.585,98.6061,60.6061Q98.58500000000001,60.6272,98.5574,60.6386Q98.5298,60.65,98.5,60.65Q98.4385,60.65,98.3947,60.6069L98.3944,60.6066L80.394747,42.8796Q80.373791,42.859,80.36218,42.8319Q80.35057,42.8049,80.350026,42.7755Q80.349481,42.7461,80.360083,42.7187Q80.370684,42.6912,80.390862,42.6698L98.2914,23.6844L80.393402,5.605532Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <!--更衣室-->
        <g>
          <path d="M86.5,143.5L86.5,81.5L55.5,81.5L55.5,139.8039Q70.8673,142.0554,86.5,143.5Z" fill-rule="evenodd"
            fill="#E4E4E4" fill-opacity="1" />
          <path
            d="M55.35,139.8039Q55.35,139.83100000000002,55.359423,139.8563Q55.368845,139.8816,55.386506,139.902Q55.4041677,139.9225,55.4278489,139.9355Q55.4515301,139.9484,55.478256,139.9524Q70.8494,142.2047,86.4862,143.6494Q86.5481,143.6551,86.5959,143.6154Q86.64359999999999,143.57569999999998,86.6494,143.5138Q86.65,143.5069,86.65,143.5L86.65,81.5Q86.65,81.4701631,86.6386,81.4425975Q86.6272,81.4150318,86.6061,81.393934Q86.58500000000001,81.372836,86.5574,81.361418Q86.5298,81.35,86.5,81.35L55.5,81.35Q55.4701631,81.35,55.4425975,81.361418Q55.4150318,81.372836,55.393934,81.393934Q55.372836,81.4150318,55.361418,81.4425975Q55.35,81.4701631,55.35,81.5L55.35,139.8039ZM55.65,139.67430000000002Q70.8696,141.9009,86.35,143.3355L86.35,81.65L55.65,81.65L55.65,139.67430000000002Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g>
          <path
            d="M69.4942,81.8C69.4981,81.70041,69.5,81.60041,69.5,81.5L63,81.5L63,88.5C63.10045,88.5,63.200450000000004,88.4976,63.3,88.4928C64.05475,88.4562,64.7839,88.281,65.48743999999999,87.9672C66.28378000000001,87.6119,66.9867,87.1061,67.5962,86.4497Q68.5104,85.4652,69.0052,84.17878C69.2994,83.41396,69.4624,82.62103,69.4942,81.8ZM69.194,81.8Q69.1465,82.97581,68.7252,84.07109Q68.2512,85.3035,67.3764,86.2456Q66.5038,87.1853,65.36523,87.6932Q64.36836,88.1379,63.3,88.19239999999999L63.3,81.8L69.194,81.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <!--洽谈室-->
        <g id="room-507" class="meeting-room" @click.stop="selectRoom('507')" :class="{ selected: selected === '507' }">
          <path d="M116.5,145.5L116.5,81.5L86.5,81.5L86.5,143.441Q101.628,144.8602,116.5,145.5Z" fill-rule="evenodd"
            fill="#FFFFFF" fill-opacity="1" />
          <path
            d="M86.35,143.441Q86.35,143.4691,86.360114,143.4952Q86.370228,143.5213,86.389091,143.542Q86.407955,143.5627,86.4330248,143.5753Q86.4580947,143.58780000000002,86.48599,143.5904Q101.6265,145.0105,116.4936,145.6499Q116.5556,145.6525,116.6014,145.6105Q116.6472,145.5685,116.6499,145.50650000000002L116.65,145.5L116.65,81.5Q116.65,81.4701631,116.6386,81.4425975Q116.6272,81.4150318,116.6061,81.393934Q116.58500000000001,81.372836,116.5574,81.361418Q116.5298,81.35,116.5,81.35L86.5,81.35Q86.4701631,81.35,86.4425975,81.361418Q86.4150318,81.372836,86.393934,81.393934Q86.372836,81.4150318,86.361418,81.4425975Q86.35,81.4701631,86.35,81.5L86.35,143.441ZM86.65,143.3044Q101.6343,144.7067,116.35,145.3434L116.35,81.65L86.65,81.65L86.65,143.3044Z"
            fill-rule="evenodd" fill="#333333" fill-opacity="1" />
        </g>
        <g>
          <path
            d="M102.54,130.5L103.056,130.5L103.056,129.288L103.644,129.288L103.644,128.85L103.056,128.85L103.056,126.102L102.45,126.102L100.62,128.928L100.62,129.288L102.54,129.288L102.54,130.5ZM102.54,128.85L101.19,128.85L102.192,127.35C102.318,127.134,102.438,126.912,102.546,126.702L102.57,126.702C102.55799999999999,126.924,102.54,127.284,102.54,127.5L102.54,128.85ZM106.572,125.478C106.554,126.402,106.59,129.336,104.088,130.602C104.226,130.698,104.37,130.842,104.454,130.956C105.924,130.17,106.56,128.826,106.842,127.62C107.136,128.742,107.784,130.224,109.28999999999999,130.932C109.362,130.806,109.494,130.65,109.62,130.554C107.496,129.6,107.124,127.086,107.034,126.366C107.064,126.006,107.07,125.7,107.076,125.478L106.572,125.478Z"
            fill="#999999" fill-opacity="1" />
        </g>

        <g clip-path="url(#master_svg0_53_8732)">
          <g>
            <path
              d="M96.24733341253281,127.4533989577961L96.24733341253281,127.3597989577961C96.9056734125328,126.8853389577961,97.0069734125328,125.9446389577961,96.4647534125328,125.3408919577961C95.92252341253281,124.7371409577961,94.9763934125328,124.7371409577961,94.43416341253281,125.3408929577961C93.89194241253281,125.9446389577961,93.99324241253281,126.8853389577961,94.65157341253281,127.3597989577961L94.65157341253281,127.44043895779609C93.7596364125328,127.8003489577961,93.1789788425328,128.6696289577961,93.18805824053281,129.6313989577961C93.2154044125328,129.8570389577961,93.41783041253281,130.0196189577961,93.6440584125328,129.99763895779608L97.22173341253281,129.99763895779608C97.4479634125328,130.0196189577961,97.65039341253281,129.8570389577961,97.6777334125328,129.6313989577961C97.6868634125328,128.6826089577961,97.12202341253281,127.8222589577961,96.2478134125328,127.4533989577961L96.24733341253281,127.4533989577961Z"
              fill="#CBCDD0" fill-opacity="1" style="mix-blend-mode: passthrough" />
          </g>
          <g>
            <path
              d="M98.17819073660506,129.1404106890297L98.15755073660506,129.1404106890297C98.15901073660507,129.1295106890297,98.15973073660507,129.11853068902968,98.15971073660506,129.1075306890297C98.19744073660506,128.4100606890297,97.82135073660507,127.7556606890297,97.19971073660507,127.43713068902969L97.19971073660507,127.36609068902969C97.45437073660506,127.16291068902969,97.60160073660506,126.8540206890297,97.59907073660506,126.52825068902969C97.61126073660506,126.06212968902969,97.30443073660507,125.6476136890297,96.85507073660506,125.5231307890297C96.92690073660506,125.5052404790297,97.00065073660507,125.4962133813297,97.07467073660507,125.49625080347069C97.62505073660506,125.5165748890297,98.05558073660507,125.9777766890297,98.03803073660507,126.52825068902969C98.04031073660506,126.8540606890297,97.89281073660507,127.16287068902969,97.63795073660506,127.3658506890297C97.92573073660506,127.5107706890297,98.14909073660506,127.75784068902969,98.26435073660507,128.0587306890297C98.37695073660507,128.3155606890297,98.45616073660506,128.58576068902968,98.50003073660507,128.8627306890297C98.48687073660507,129.0278006890297,98.34340073660506,129.1515806890297,98.17819073660506,129.1404106890297ZM92.82163073660506,129.1408906890297L92.84227073660506,129.1408906890297C92.84080773660506,129.12999068902968,92.84008573660506,129.1190106890297,92.84011073660507,129.1080106890297C92.80238173660507,128.4105406890297,93.17847573660507,127.75614068902969,93.80011073660506,127.4376106890297L93.80011073660506,127.36681068902969C93.54552073660507,127.16368068902969,93.39829173660506,126.85490068902969,93.40075073660506,126.5292106890297C93.38834173660507,126.0629136890297,93.69522073660507,125.6481376890297,94.14475073660506,125.5236111890297C94.07292073660507,125.50572101902969,93.99917073660507,125.49669380402969,93.92515073660506,125.4967312260297C93.37477173660507,125.51705548902969,92.94424573660507,125.97825668902969,92.96179073660507,126.5287306890297C92.95950673660506,126.85454068902969,93.10700773660507,127.1633506890297,93.36187073660507,127.3663306890297C93.07409273660507,127.5112506890297,92.85072973660506,127.75832068902969,92.73547073660507,128.0592106890297C92.62287073660507,128.3160406890297,92.54366463660506,128.5862406890297,92.49979073660506,128.8632106890297C92.51294993660507,129.02828068902969,92.65641873660506,129.1520606890297,92.82163073660506,129.1408906890297Z"
              fill="#CBCDD0" fill-opacity="1" style="mix-blend-mode: passthrough" />
          </g>
        </g>
        <g transform="matrix(-1,0,0,1,237,0)" id="door-507">
          <path
            d="M131.4942,81.8C131.4981,81.70041,131.5,81.60041,131.5,81.5L125,81.5L125,88.5C125.10045,88.5,125.20045,88.4976,125.3,88.4928C126.05475,88.4562,126.7839,88.281,127.48743999999999,87.9672C128.28378,87.6119,128.9867,87.1061,129.5962,86.4497Q130.5104,85.4652,131.0052,84.17878C131.2994,83.41396,131.4624,82.62103,131.4942,81.8ZM131.194,81.8Q131.1465,82.97581,130.7252,84.07109Q130.2512,85.3035,129.3764,86.2456Q128.5038,87.1853,127.36523,87.6932Q126.36836,88.1379,125.3,88.19239999999999L125.3,81.8L131.194,81.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <!--办公室503-->
        <g>
          <path d="M232.5,134.6731L232.5,81.5L171.5,81.5L171.5,145.5Q201.5447,144.0852,232.5,140L232.5,134.6731Z"
            fill-rule="evenodd" fill="#E4E4E4" fill-opacity="1" />
          <path
            d="M232.5196,140.1487Q232.5467,140.1451,232.5707,140.1323Q232.5948,140.11939999999998,232.6128,140.09890000000001Q232.6308,140.0784,232.6404,140.0528Q232.65,140.0273,232.65,140L232.65,81.5Q232.65,81.4701631,232.6386,81.4425975Q232.62720000000002,81.4150318,232.6061,81.393934Q232.585,81.372836,232.5574,81.361418Q232.5298,81.35,232.5,81.35L171.5,81.35Q171.4701631,81.35,171.4425975,81.361418Q171.4150318,81.372836,171.393934,81.393934Q171.372836,81.4150318,171.361418,81.4425975Q171.35,81.4701631,171.35,81.5L171.35,145.5Q171.35,145.5298,171.361418,145.5574Q171.372836,145.58499999999998,171.393934,145.6061Q171.4150318,145.62720000000002,171.4425975,145.6386Q171.4701631,145.65,171.5,145.65L171.50705574,145.6498Q201.551,144.2352,232.5196,140.1487ZM232.35,139.86849999999998Q201.542,143.9282,171.65,145.3428L171.65,81.65L232.35,81.65L232.35,139.86849999999998Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g>
          <path
            d="M183.4942,81.8C183.4981,81.70041,183.5,81.60041,183.5,81.5L177,81.5L177,88.5C177.10045,88.5,177.20045,88.4976,177.3,88.4928C178.05475,88.4562,178.7839,88.281,179.48744,87.9672C180.28378,87.6119,180.9867,87.1061,181.5962,86.4497Q182.5104,85.4652,183.0052,84.17878C183.2994,83.41396,183.4624,82.62103,183.4942,81.8ZM183.194,81.8Q183.1465,82.97581,182.7252,84.07109Q182.2512,85.3035,181.3764,86.2456Q180.5038,87.1853,179.36523,87.6932Q178.36836,88.1379,177.3,88.19239999999999L177.3,81.8L183.194,81.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g transform="matrix(-1,0,0,1,465,0)">
          <path
            d="M245.4942,81.8C245.4981,81.70041,245.5,81.60041,245.5,81.5L239,81.5L239,88.5C239.10045,88.5,239.20045,88.4976,239.3,88.4928C240.05475,88.4562,240.7839,88.281,241.48744,87.9672C242.28378,87.6119,242.9867,87.1061,243.5962,86.4497Q244.5104,85.4652,245.0052,84.17878C245.2994,83.41396,245.4624,82.62103,245.4942,81.8ZM245.194,81.8Q245.1465,82.97581,244.7252,84.07109Q244.2512,85.3035,243.3764,86.2456Q242.5038,87.1853,241.36523,87.6932Q240.36836,88.1379,239.3,88.19239999999999L239.3,81.8L245.194,81.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <!--会议室504/505-->
        <g id="room-5045" class="meeting-room" @click.stop="selectRoom('5045')"
          :class="{ selected: selected === '5045' }">
          <path
            d="M171.5,146.0592L171.5,81.5L116.5,81.5L116.5,146.2227Q120.43942,146.38670000000002,124.39533,146.5L160.171,146.5Q166.0749,146.3339,171.5,146.0592Z"
            fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1" />
          <path
            d="M160.171,146.65L160.1752,146.6499Q166.08089999999999,146.4838,171.5076,146.209Q171.53640000000001,146.20749999999998,171.5626,146.19549999999998Q171.5888,146.18349999999998,171.6087,146.1626Q171.6286,146.14159999999998,171.6393,146.1148Q171.65,146.088,171.65,146.0592L171.65,81.5Q171.65,81.4701631,171.6386,81.4425975Q171.62720000000002,81.4150318,171.6061,81.393934Q171.585,81.372836,171.5574,81.361418Q171.5298,81.35,171.5,81.35L116.5,81.35Q116.4701631,81.35,116.4425975,81.361418Q116.4150318,81.372836,116.393934,81.393934Q116.372836,81.4150318,116.361418,81.4425975Q116.35,81.4701631,116.35,81.5L116.35,146.2227Q116.35,146.2517,116.360829,146.27870000000001Q116.371657,146.3056,116.391751,146.32659999999998Q116.4118454,146.3475,116.4383034,146.3594Q116.4647614,146.3714,116.49376333,146.3726Q120.43037,146.5365,124.39103,146.6499L124.39533,146.65L160.171,146.65ZM171.35,145.91660000000002Q165.9932,146.1861,160.171,146.3499L160.16899999999998,146.35L124.39533,146.35Q120.51102,146.2387,116.65,146.0788L116.65,81.65L171.35,81.65L171.35,145.91660000000002Z"
            fill-rule="evenodd" fill="#333333" fill-opacity="1" />
        </g>
        <g>
          <path
            d="M141.764,130.5L144.53,130.5L144.53,130.026L143.312,130.026C143.09,130.026,142.82,130.05,142.592,130.068C143.624,129.09,144.32,128.196,144.32,127.314C144.32,126.534,143.822,126.024,143.036,126.024C142.478,126.024,142.094,126.276,141.74,126.666L142.058,126.978C142.304,126.684,142.61,126.468,142.97,126.468C143.516,126.468,143.78,126.834,143.78,127.338C143.78,128.094,143.144,128.97,141.764,130.176L141.764,130.5ZM146.498,130.578C147.332,130.578,147.86599999999999,129.822,147.86599999999999,128.286C147.86599999999999,126.762,147.332,126.024,146.498,126.024C145.658,126.024,145.13,126.762,145.13,128.286C145.13,129.822,145.658,130.578,146.498,130.578ZM146.498,130.13400000000001C146,130.13400000000001,145.658,129.576,145.658,128.286C145.658,127.002,146,126.456,146.498,126.456C146.996,126.456,147.338,127.002,147.338,128.286C147.338,129.576,146.996,130.13400000000001,146.498,130.13400000000001ZM150.902,125.478C150.88400000000001,126.402,150.92,129.336,148.418,130.602C148.556,130.698,148.7,130.842,148.784,130.956C150.254,130.17,150.89,128.826,151.172,127.62C151.466,128.742,152.114,130.224,153.62,130.932C153.692,130.806,153.824,130.65,153.95,130.554C151.826,129.6,151.454,127.086,151.364,126.366C151.394,126.006,151.4,125.7,151.406,125.478L150.902,125.478Z"
            fill="#999999" fill-opacity="1" />
        </g>
        <!--会议室504/505人图标-->
        <g clip-path="url(#master_svg1_53_8741)">
          <g>
            <path
              d="M137.2473334125328,127.4533989577961L137.2473334125328,127.3597989577961C137.9056734125328,126.8853389577961,138.0069734125328,125.9446389577961,137.46475341253281,125.3408919577961C136.9225234125328,124.7371409577961,135.97639341253281,124.7371409577961,135.4341634125328,125.3408929577961C134.8919424125328,125.9446389577961,134.9932424125328,126.8853389577961,135.6515734125328,127.3597989577961L135.6515734125328,127.44043895779609C134.7596364125328,127.8003489577961,134.1789788425328,128.6696289577961,134.1880582405328,129.6313989577961C134.21540441253282,129.8570389577961,134.4178304125328,130.0196189577961,134.64405841253281,129.99763895779608L138.2217334125328,129.99763895779608C138.4479634125328,130.0196189577961,138.6503934125328,129.8570389577961,138.6777334125328,129.6313989577961C138.6868634125328,128.6826089577961,138.1220234125328,127.8222589577961,137.2478134125328,127.4533989577961L137.2473334125328,127.4533989577961Z"
              fill="#CBCDD0" fill-opacity="1" style="mix-blend-mode: passthrough" />
          </g>
          <g>
            <path
              d="M139.17819073660507,129.1404106890297L139.15755073660506,129.1404106890297C139.15901073660507,129.1295106890297,139.15973073660507,129.11853068902968,139.15971073660506,129.1075306890297C139.19744073660507,128.4100606890297,138.82135073660507,127.7556606890297,138.19971073660506,127.43713068902969L138.19971073660506,127.36609068902969C138.45437073660506,127.16291068902969,138.60160073660506,126.8540206890297,138.59907073660506,126.52825068902969C138.61126073660506,126.06212968902969,138.30443073660507,125.6476136890297,137.85507073660506,125.5231307890297C137.92690073660506,125.5052404790297,138.00065073660505,125.4962133813297,138.07467073660507,125.49625080347069C138.62505073660506,125.5165748890297,139.05558073660507,125.9777766890297,139.03803073660507,126.52825068902969C139.04031073660505,126.8540606890297,138.89281073660507,127.16287068902969,138.63795073660506,127.3658506890297C138.92573073660506,127.5107706890297,139.14909073660507,127.75784068902969,139.26435073660505,128.0587306890297C139.37695073660507,128.3155606890297,139.45616073660506,128.58576068902968,139.50003073660505,128.8627306890297C139.48687073660506,129.0278006890297,139.34340073660508,129.1515806890297,139.17819073660507,129.1404106890297ZM133.82163073660507,129.1408906890297L133.84227073660506,129.1408906890297C133.84080773660506,129.12999068902968,133.84008573660506,129.1190106890297,133.84011073660506,129.1080106890297C133.80238173660507,128.4105406890297,134.17847573660507,127.75614068902969,134.80011073660506,127.4376106890297L134.80011073660506,127.36681068902969C134.54552073660506,127.16368068902969,134.39829173660507,126.85490068902969,134.40075073660506,126.5292106890297C134.38834173660507,126.0629136890297,134.69522073660505,125.6481376890297,135.14475073660506,125.5236111890297C135.07292073660506,125.50572101902969,134.99917073660507,125.49669380402969,134.92515073660508,125.4967312260297C134.37477173660506,125.51705548902969,133.94424573660507,125.97825668902969,133.96179073660505,126.5287306890297C133.95950673660505,126.85454068902969,134.10700773660506,127.1633506890297,134.36187073660506,127.3663306890297C134.07409273660505,127.5112506890297,133.85072973660508,127.75832068902969,133.73547073660507,128.0592106890297C133.62287073660505,128.3160406890297,133.54366463660506,128.5862406890297,133.49979073660506,128.8632106890297C133.51294993660505,129.02828068902969,133.65641873660508,129.1520606890297,133.82163073660507,129.1408906890297Z"
              fill="#CBCDD0" fill-opacity="1" style="mix-blend-mode: passthrough" />
          </g>
        </g>
        <!--会议室504/505 左边门-->
        <g id="door-5045-l">
          <path
            d="M129.4942,81.8C129.4981,81.70041,129.5,81.60041,129.5,81.5L123,81.5L123,88.5C123.10045,88.5,123.20045,88.4976,123.3,88.4928C124.05475,88.4562,124.7839,88.281,125.48743999999999,87.9672C126.28378000000001,87.6119,126.9867,87.1061,127.5962,86.4497Q128.5104,85.4652,129.0052,84.17878C129.2994,83.41396,129.4624,82.62103,129.4942,81.8ZM129.194,81.8Q129.1465,82.97581,128.7252,84.07109Q128.2512,85.3035,127.3764,86.2456Q126.5038,87.1853,125.36523,87.6932Q124.36836,88.1379,123.3,88.19239999999999L123.3,81.8L129.194,81.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <!--会议室504/505 右边门-->
        <g transform="matrix(-1,0,0,1,343,0)" id="door-5045-r">
          <path
            d="M184.4942,81.8C184.4981,81.70041,184.5,81.60041,184.5,81.5L178,81.5L178,88.5C178.10045,88.5,178.20045,88.4976,178.3,88.4928C179.05475,88.4562,179.7839,88.281,180.48744,87.9672C181.28378,87.6119,181.9867,87.1061,182.5962,86.4497Q183.5104,85.4652,184.0052,84.17878C184.2994,83.41396,184.4624,82.62103,184.4942,81.8ZM184.194,81.8Q184.1465,82.97581,183.7252,84.07109Q183.2512,85.3035,182.3764,86.2456Q181.5038,87.1853,180.36523,87.6932Q179.36836,88.1379,178.3,88.19239999999999L178.3,81.8L184.194,81.8Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g>
          <path
            d="M205.600384,48.388541L215.5,57.2982L225.3994,48.388767L225.3997,48.388506Q225.4424,48.35,225.5,48.35Q225.5298,48.35,225.5574,48.361418Q225.585,48.372836,225.6061,48.393934Q225.6272,48.4150318,225.6386,48.4425975Q225.65,48.4701631,225.65,48.5Q225.65,48.5668038,225.6003,48.611494L215.7242,57.5L225.6003,66.3885Q225.65,66.4332,225.65,66.5Q225.65,66.5298,225.6386,66.5574Q225.6272,66.58500000000001,225.6061,66.6061Q225.585,66.6272,225.5574,66.6386Q225.5298,66.65,225.5,66.65Q225.4424,66.65,225.3997,66.6115L225.3994,66.6113L215.5,57.7018L205.600345,66.6115Q205.5575603,66.65,205.5,66.65Q205.4701631,66.65,205.4425975,66.6386Q205.4150318,66.6272,205.393934,66.6061Q205.372836,66.58500000000001,205.361418,66.5574Q205.35,66.5298,205.35,66.5Q205.35,66.4332,205.399655,66.3885L215.27577,57.5L205.399695,48.61153L205.399655,48.611494Q205.35,48.5668044,205.35,48.5Q205.35,48.4701631,205.361418,48.4425975Q205.372836,48.4150318,205.393934,48.393934Q205.4150318,48.372836,205.4425975,48.361418Q205.4701631,48.35,205.5,48.35Q205.5575604,48.35,205.600345,48.388506L205.600384,48.388541Z"
            fill-rule="evenodd" fill="#999999" fill-opacity="1" />
        </g>
        <g>
          <path
            d="M63.512,113.072L63.128,113.228C63.332,113.576,63.584,113.852,63.878,114.074C63.512,114.284,62.996,114.458,62.282,114.59C62.378,114.69200000000001,62.498,114.884,62.552,114.986C63.332,114.818,63.89,114.596,64.292,114.332C65.12,114.77,66.224,114.908,67.622,114.962C67.646,114.812,67.73,114.62,67.814,114.518C66.47,114.482,65.432,114.392,64.658,114.044C64.97,113.738,65.132,113.39,65.204,113.018L67.238,113.018L67.238,110.696L65.27,110.696L65.27,110.186L67.61,110.186L67.61,109.778L62.39,109.778L62.39,110.186L64.802,110.186L64.802,110.696L62.936,110.696L62.936,113.018L64.73,113.018C64.658,113.306,64.52,113.576,64.244,113.816C63.956,113.624,63.71,113.384,63.512,113.072ZM63.368,112.034L64.802,112.034L64.802,112.274C64.802,112.4,64.802,112.526,64.79,112.646L63.368,112.646L63.368,112.034ZM65.258,112.646C65.264,112.526,65.27,112.406,65.27,112.28L65.27,112.034L66.788,112.034L66.788,112.646L65.258,112.646ZM63.368,111.074L64.802,111.074L64.802,111.674L63.368,111.674L63.368,111.074ZM65.27,111.074L66.788,111.074L66.788,111.674L65.27,111.674L65.27,111.074ZM70.58,109.568C70.73,109.838,70.892,110.19200000000001,70.952,110.432L68.366,110.432L68.366,110.87L70.574,110.87C70.034,111.59,69.134,112.28,68.204,112.694C68.276,112.79,68.402,112.97,68.456,113.084C68.84,112.904,69.206,112.688,69.554,112.436L69.554,114.08C69.554,114.362,69.35,114.53,69.23,114.608C69.30799999999999,114.69200000000001,69.434,114.866,69.476,114.962C69.632,114.854,69.86,114.764,71.75,114.164C71.72,114.068,71.666,113.882,71.648,113.756L70.01,114.254L70.01,112.088C70.394,111.764,70.736,111.416,71.012,111.044C71.33,112.7,71.912,113.84,73.478,114.824C73.532,114.686,73.682,114.524,73.79599999999999,114.434C73.034,113.99,72.512,113.504,72.14,112.922C72.584,112.568,73.106,112.082,73.502,111.656L73.118,111.38C72.818,111.752,72.35,112.208,71.936,112.556C71.69,112.064,71.528,111.512,71.414,110.87L73.64,110.87L73.64,110.432L71.048,110.432L71.438,110.3C71.378,110.072,71.20400000000001,109.706,71.03,109.436L70.58,109.568ZM74.894,113.204L74.894,113.6L76.766,113.6L76.766,114.404L74.354,114.404L74.354,114.812L79.67,114.812L79.67,114.404L77.228,114.404L77.228,113.6L79.136,113.6L79.136,113.204L77.228,113.204L77.228,112.574L76.766,112.574L76.766,113.204L74.894,113.204ZM75.14,112.682C75.326,112.61,75.608,112.586,78.476,112.364C78.614,112.502,78.73400000000001,112.64,78.818,112.748L79.166,112.502C78.92,112.19,78.404,111.728,77.98400000000001,111.404L77.654,111.626C77.81,111.752,77.97800000000001,111.89,78.14,112.04L75.818,112.202C76.16,111.95,76.502,111.65,76.82,111.332L79.01,111.332L79.01,110.94200000000001L75.038,110.94200000000001L75.038,111.332L76.238,111.332C75.902,111.674,75.548,111.962,75.416,112.05199999999999C75.26,112.172,75.122,112.25,75.008,112.268C75.056,112.382,75.116,112.592,75.14,112.682ZM76.61,109.526C76.694,109.664,76.778,109.838,76.844,109.994L74.42,109.994L74.42,111.056L74.858,111.056L74.858,110.402L79.13,110.402L79.13,111.056L79.586,111.056L79.586,109.994L77.348,109.994C77.282,109.814,77.156,109.58,77.042,109.4L76.61,109.526Z"
            fill="#333333" fill-opacity="0.6000000238418579" />
        </g>
        <g>
          <path
            d="M209.502,24.784C209.664,25.042,209.85,25.396,209.946,25.6L210.306,25.414C210.216,25.216,210.018,24.88,209.85,24.628L209.502,24.784ZM212.01,24.568C211.896,24.832,211.68,25.216,211.512,25.45L211.824,25.6C212.004,25.378,212.214,25.042,212.412,24.736L212.01,24.568ZM210.708,24.46L210.708,25.63L209.28,25.63L209.28,26.008L210.426,26.008C210.066,26.38,209.556,26.734,209.112,26.926000000000002C209.208,27.003999999999998,209.334,27.148,209.4,27.25C209.832,27.028,210.336,26.644,210.708,26.236L210.708,27.208L211.13400000000001,27.208L211.13400000000001,26.218C211.512,26.614,212.028,27.003999999999998,212.454,27.22C212.514,27.118000000000002,212.646,26.962,212.748,26.884C212.31,26.704,211.782,26.362000000000002,211.416,26.008L212.604,26.008L212.604,25.63L211.13400000000001,25.63L211.13400000000001,24.46L210.708,24.46ZM211.56,28.114C211.44,28.462,211.254,28.732,210.99,28.948C210.726,28.852,210.45,28.75,210.18,28.66C210.288,28.503999999999998,210.402,28.312,210.516,28.114L211.56,28.114ZM209.556,28.84C209.904,28.954,210.252,29.073999999999998,210.582,29.2C210.192,29.392,209.688,29.512,209.064,29.59C209.13,29.68,209.208,29.848,209.244,29.968C210.012,29.848,210.612,29.668,211.062,29.392C211.536,29.584,211.956,29.782,212.274,29.956L212.586,29.631999999999998C212.274,29.476,211.872,29.296,211.422,29.116C211.698,28.852,211.896,28.522,212.016,28.114L212.664,28.114L212.664,27.724L210.726,27.724C210.798,27.58,210.864,27.436,210.924,27.298000000000002L210.486,27.214C210.42,27.376,210.342,27.55,210.252,27.724L209.154,27.724L209.154,28.114L210.036,28.114C209.874,28.384,209.706,28.636,209.556,28.84ZM208.068,24.46L208.068,25.618L207.336,25.618L207.336,26.038L208.044,26.038C207.882,26.854,207.54,27.814,207.192,28.317999999999998C207.27,28.426000000000002,207.378,28.624,207.432,28.756C207.666,28.39,207.888,27.808,208.068,27.195999999999998L208.068,29.974L208.482,29.974L208.482,26.836C208.638,27.124,208.812,27.472,208.89,27.658L209.166,27.334C209.07,27.16,208.632,26.482,208.482,26.278L208.482,26.038L209.07,26.038L209.07,25.618L208.482,25.618L208.482,24.46L208.068,24.46ZM214.158,24.46L214.158,25.618L213.3,25.618L213.3,26.038L214.122,26.038C213.93,26.86,213.564,27.814,213.186,28.317999999999998C213.27,28.426000000000002,213.378,28.624,213.426,28.756C213.696,28.36,213.96,27.724,214.158,27.058L214.158,29.974L214.572,29.974L214.572,26.836C214.734,27.13,214.926,27.484,215.004,27.676000000000002L215.28,27.352C215.178,27.178,214.722,26.482,214.572,26.278L214.572,26.038L215.214,26.038L215.214,25.618L214.572,25.618L214.572,24.46L214.158,24.46ZM216.738,26.944L216.738,27.604L215.832,27.604L215.916,26.944L216.738,26.944ZM215.562,26.56C215.526,27.016,215.454,27.61,215.382,27.988L216.54,27.988C216.168,28.558,215.562,29.086,214.986,29.35C215.076,29.434,215.208,29.59,215.274,29.692C215.808,29.41,216.342,28.924,216.738,28.366L216.738,29.98L217.164,29.98L217.164,27.988L218.262,27.988C218.22,28.654,218.172,28.918,218.112,28.990000000000002C218.07,29.038,218.028,29.05,217.95,29.05C217.878,29.05,217.704,29.044,217.506,29.026C217.572,29.14,217.608,29.32,217.62,29.445999999999998C217.83,29.451999999999998,218.034,29.451999999999998,218.148,29.44C218.28,29.422,218.37,29.386,218.454,29.29C218.574,29.146,218.63400000000001,28.75,218.682,27.784C218.688,27.724,218.694,27.604,218.694,27.604L217.164,27.604L217.164,26.944L218.502,26.944L218.502,25.438L217.788,25.438C217.944,25.186,218.106,24.874,218.25,24.592L217.818,24.46C217.704,24.748,217.512,25.162,217.344,25.438L216.38400000000001,25.438L216.564,25.36C216.486,25.114,216.3,24.748,216.102,24.478L215.748,24.622C215.916,24.868,216.078,25.192,216.162,25.438L215.346,25.438L215.346,25.822L216.738,25.822L216.738,26.56L215.562,26.56ZM217.164,25.822L218.082,25.822L218.082,26.56L217.164,26.56L217.164,25.822ZM219.546,25.81L219.546,29.98L220.008,29.98L220.008,25.81L219.546,25.81ZM219.636,24.754C219.912,25.018,220.224,25.396,220.362,25.636L220.734,25.396C220.59,25.144,220.266,24.79,219.984,24.538L219.636,24.754ZM221.274,27.73L222.714,27.73L222.714,28.54L221.274,28.54L221.274,27.73ZM221.274,26.554000000000002L222.714,26.554000000000002L222.714,27.352L221.274,27.352L221.274,26.554000000000002ZM220.86599999999999,26.176000000000002L220.86599999999999,28.912L223.14,28.912L223.14,26.176000000000002L220.86599999999999,26.176000000000002ZM221.112,24.796L221.112,25.222L224.016,25.222L224.016,29.434C224.016,29.512,223.992,29.536,223.914,29.542C223.836,29.542,223.59,29.548000000000002,223.338,29.536C223.398,29.65,223.458,29.842,223.482,29.95C223.848,29.95,224.106,29.95,224.268,29.878C224.424,29.8,224.478,29.686,224.478,29.434L224.478,24.796L221.112,24.796Z"
            fill="#333333" fill-opacity="0.6000000238418579" />
        </g>
        <g>
          <path
            d="M3.19,29.892L3.19,30.348L5.002,30.348L5.002,34.308L2.812,34.308L2.812,34.758L8.206,34.758L8.206,34.308L5.482,34.308L5.482,30.348L7.264,30.348L7.264,32.43C7.264,32.525999999999996,7.234,32.556,7.114,32.562C6.988,32.568,6.568,32.568,6.106,32.556C6.178,32.676,6.2620000000000005,32.874,6.286,33C6.838,33,7.216,32.994,7.438,32.922C7.66,32.844,7.726,32.706,7.726,32.442L7.726,29.892L3.19,29.892ZM9.934000000000001,29.556C9.706,30.414,9.315999999999999,31.248,8.824,31.782C8.937999999999999,31.842,9.136,31.974,9.225999999999999,32.052C9.454,31.782,9.664,31.44,9.856,31.062L11.278,31.062L11.278,32.388L9.49,32.388L9.49,32.82L11.278,32.82L11.278,34.35L8.83,34.35L8.83,34.788L14.194,34.788L14.194,34.35L11.746,34.35L11.746,32.82L13.69,32.82L13.69,32.388L11.746,32.388L11.746,31.062L13.906,31.062L13.906,30.624L11.746,30.624L11.746,29.46L11.278,29.46L11.278,30.624L10.054,30.624C10.186,30.318,10.3,29.988,10.39,29.658L9.934000000000001,29.556ZM15.046,30.81L15.046,34.980000000000004L15.508,34.980000000000004L15.508,30.81L15.046,30.81ZM15.136,29.754C15.412,30.018,15.724,30.396,15.862,30.636L16.234,30.396C16.09,30.144,15.766,29.79,15.484,29.538L15.136,29.754ZM16.774,32.730000000000004L18.214,32.730000000000004L18.214,33.54L16.774,33.54L16.774,32.730000000000004ZM16.774,31.554000000000002L18.214,31.554000000000002L18.214,32.352000000000004L16.774,32.352000000000004L16.774,31.554000000000002ZM16.366,31.176000000000002L16.366,33.912L18.64,33.912L18.64,31.176000000000002L16.366,31.176000000000002ZM16.612000000000002,29.796L16.612000000000002,30.222L19.516,30.222L19.516,34.434C19.516,34.512,19.492,34.536,19.414,34.542C19.336,34.542,19.09,34.548,18.838,34.536C18.898,34.65,18.958,34.842,18.982,34.95C19.348,34.95,19.606,34.95,19.768,34.878C19.924,34.8,19.978,34.686,19.978,34.434L19.978,29.796L16.612000000000002,29.796ZM7.906,44.676L8.242,44.525999999999996C7.726,43.674,7.48,42.653999999999996,7.48,41.634C7.48,40.62,7.726,39.606,8.242,38.748L7.906,38.592C7.354,39.492000000000004,7.024,40.458,7.024,41.634C7.024,42.816,7.354,43.782,7.906,44.676ZM12.514,40.374C12.328,41.166,12.046,41.784,11.608,42.252C11.164,42.048,10.702,41.85,10.246,41.67C10.432,41.298,10.636,40.848,10.834,40.374L12.514,40.374ZM9.562000000000001,41.88C10.132,42.096000000000004,10.696,42.342,11.23,42.594C10.648,43.038,9.862,43.314,8.776,43.47C8.878,43.59,8.98,43.782,9.027999999999999,43.926C10.228,43.722,11.092,43.38,11.722,42.834C12.49,43.224000000000004,13.174,43.620000000000005,13.666,43.974000000000004L14.038,43.572C13.54,43.230000000000004,12.844,42.846000000000004,12.076,42.474000000000004C12.532,41.94,12.826,41.25,13.018,40.374L14.164,40.374L14.164,39.894L11.026,39.894C11.212,39.408,11.38,38.916,11.5,38.466L11.014,38.4C10.888,38.862,10.708,39.378,10.504,39.894L8.86,39.894L8.86,40.374L10.3,40.374C10.054,40.944,9.796,41.478,9.562000000000001,41.88ZM15.094,44.676C15.646,43.782,15.976,42.816,15.976,41.634C15.976,40.458,15.646,39.492000000000004,15.094,38.592L14.752,38.748C15.268,39.606,15.526,40.62,15.526,41.634C15.526,42.653999999999996,15.268,43.674,14.752,44.525999999999996L15.094,44.676Z"
            fill="#333333" fill-opacity="0.6000000238418579" />
        </g>
        <g>
          <path
            d="M10.077,99.578C10.815000000000001,99.578,11.517,99.032,11.517,98.072C11.517,97.1,10.917,96.668,10.190999999999999,96.668C9.927,96.668,9.729,96.734,9.530999999999999,96.842L9.645,95.57L11.301,95.57L11.301,95.102L9.165,95.102L9.021,97.154L9.315000000000001,97.34C9.567,97.172,9.753,97.082,10.047,97.082C10.599,97.082,10.959,97.454,10.959,98.084C10.959,98.726,10.545,99.122,10.023,99.122C9.513,99.122,9.189,98.888,8.943,98.636L8.667,98.996C8.966999999999999,99.29,9.387,99.578,10.077,99.578ZM13.503,99.578C14.337,99.578,14.871,98.822,14.871,97.286C14.871,95.762,14.337,95.024,13.503,95.024C12.663,95.024,12.135,95.762,12.135,97.286C12.135,98.822,12.663,99.578,13.503,99.578ZM13.503,99.134C13.005,99.134,12.663,98.576,12.663,97.286C12.663,96.002,13.005,95.456,13.503,95.456C14.001,95.456,14.343,96.002,14.343,97.286C14.343,98.576,14.001,99.134,13.503,99.134ZM16.575,99.578C17.397,99.578,18.171,98.894,18.171,97.112C18.171,95.714,17.535,95.024,16.689,95.024C16.005000000000003,95.024,15.429,95.594,15.429,96.452C15.429,97.358,15.909,97.832,16.641,97.832C17.006999999999998,97.832,17.384999999999998,97.622,17.655,97.298C17.613,98.66,17.121000000000002,99.122,16.557000000000002,99.122C16.269,99.122,16.005000000000003,98.996,15.813,98.786L15.513,99.128C15.759,99.386,16.095,99.578,16.575,99.578ZM17.649,96.836C17.355,97.256,17.025,97.424,16.731,97.424C16.209,97.424,15.945,97.04,15.945,96.452C15.945,95.846,16.269,95.45,16.695,95.45C17.253,95.45,17.589,95.93,17.649,96.836ZM5.598,105.53C5.43,106.05799999999999,5.13,106.724,4.77,107.15L5.184,107.39C5.532,106.934,5.8260000000000005,106.232,6.006,105.69800000000001L5.598,105.53ZM9.168,105.614C9.443999999999999,106.22,9.725999999999999,107.012,9.815999999999999,107.498L10.26,107.336C10.158000000000001,106.85,9.864,106.07,9.582,105.476L9.168,105.614ZM6.834,103.466L6.834,104.51L6.834,104.564L5.022,104.564L5.022,105.014L6.822,105.014C6.768,106.184,6.438,107.606,4.752,108.644C4.866,108.72200000000001,5.04,108.896,5.118,109.004C6.912,107.876,7.248,106.304,7.302,105.014L8.526,105.014C8.442,107.258,8.346,108.128,8.154,108.326C8.088000000000001,108.404,8.022,108.422,7.896,108.416C7.746,108.416,7.3740000000000006,108.416,6.9719999999999995,108.38C7.056,108.512,7.116,108.71600000000001,7.128,108.86C7.494,108.872,7.878,108.89,8.094,108.866C8.315999999999999,108.842,8.46,108.788,8.597999999999999,108.608C8.838000000000001,108.32,8.928,107.408,9.024000000000001,104.816C9.024000000000001,104.744,9.030000000000001,104.564,9.030000000000001,104.564L7.314,104.564L7.314,104.516L7.314,103.466L6.834,103.466ZM12.443999999999999,103.634C12.09,104.534,11.484,105.398,10.806000000000001,105.932C10.926,106.004,11.129999999999999,106.166,11.219999999999999,106.256C11.886,105.662,12.522,104.75,12.924,103.766L12.443999999999999,103.634ZM14.49,103.586L14.052,103.766C14.508,104.672,15.276,105.68,15.906,106.256C15.996,106.136,16.164,105.962,16.284,105.872C15.66,105.374,14.892,104.414,14.49,103.586ZM11.466000000000001,108.584C11.693999999999999,108.5,12.018,108.476,15.186,108.266C15.348,108.512,15.486,108.746,15.588,108.938L16.032,108.69800000000001C15.732,108.152,15.114,107.306,14.586,106.664L14.166,106.856C14.406,107.156,14.664,107.504,14.904,107.846L12.096,108.008C12.696,107.312,13.284,106.412,13.782,105.5L13.29,105.28999999999999C12.81,106.286,12.078,107.336,11.838000000000001,107.606C11.616,107.888,11.454,108.068,11.292,108.11C11.358,108.242,11.442,108.482,11.466000000000001,108.584ZM17.394,107.20400000000001L17.394,107.6L19.266,107.6L19.266,108.404L16.854,108.404L16.854,108.812L22.17,108.812L22.17,108.404L19.728,108.404L19.728,107.6L21.636,107.6L21.636,107.20400000000001L19.728,107.20400000000001L19.728,106.574L19.266,106.574L19.266,107.20400000000001L17.394,107.20400000000001ZM17.64,106.682C17.826,106.61,18.108,106.586,20.976,106.364C21.114,106.502,21.234,106.64,21.318,106.748L21.666,106.502C21.42,106.19,20.904,105.728,20.484,105.404L20.154,105.626C20.310000000000002,105.752,20.478,105.89,20.64,106.03999999999999L18.317999999999998,106.202C18.66,105.95,19.002000000000002,105.65,19.32,105.332L21.51,105.332L21.51,104.94200000000001L17.538,104.94200000000001L17.538,105.332L18.738,105.332C18.402,105.674,18.048000000000002,105.962,17.916,106.05199999999999C17.759999999999998,106.172,17.622,106.25,17.508,106.268C17.555999999999997,106.382,17.616,106.592,17.64,106.682ZM19.11,103.526C19.194000000000003,103.664,19.278,103.838,19.344,103.994L16.92,103.994L16.92,105.056L17.358,105.056L17.358,104.402L21.63,104.402L21.63,105.056L22.086,105.056L22.086,103.994L19.848,103.994C19.782,103.814,19.656,103.58,19.542,103.4L19.11,103.526Z"
            fill="#333333" fill-opacity="0.6000000238418579" />
        </g>
        <g>
          <path
            d="M35.077,99.578C35.815,99.578,36.517,99.032,36.517,98.072C36.517,97.1,35.917,96.668,35.191,96.668C34.927,96.668,34.729,96.734,34.531,96.842L34.644999999999996,95.57L36.301,95.57L36.301,95.102L34.165,95.102L34.021,97.154L34.315,97.34C34.567,97.172,34.753,97.082,35.047,97.082C35.599000000000004,97.082,35.959,97.454,35.959,98.084C35.959,98.726,35.545,99.122,35.022999999999996,99.122C34.513,99.122,34.189,98.888,33.943,98.636L33.667,98.996C33.967,99.29,34.387,99.578,35.077,99.578ZM38.503,99.578C39.337,99.578,39.871,98.822,39.871,97.286C39.871,95.762,39.337,95.024,38.503,95.024C37.663,95.024,37.135,95.762,37.135,97.286C37.135,98.822,37.663,99.578,38.503,99.578ZM38.503,99.134C38.005,99.134,37.663,98.576,37.663,97.286C37.663,96.002,38.005,95.456,38.503,95.456C39.001,95.456,39.343,96.002,39.343,97.286C39.343,98.576,39.001,99.134,38.503,99.134ZM41.845,99.578C42.667,99.578,43.219,99.08,43.219,98.444C43.219,97.838,42.865,97.508,42.481,97.286L42.481,97.256C42.739000000000004,97.05199999999999,43.063,96.656,43.063,96.194C43.063,95.516,42.607,95.036,41.857,95.036C41.173,95.036,40.650999999999996,95.486,40.650999999999996,96.152C40.650999999999996,96.614,40.927,96.944,41.245,97.166L41.245,97.19C40.843,97.406,40.441,97.82,40.441,98.408C40.441,99.086,41.028999999999996,99.578,41.845,99.578ZM42.144999999999996,97.112C41.623,96.908,41.149,96.674,41.149,96.152C41.149,95.726,41.443,95.444,41.851,95.444C42.319,95.444,42.595,95.786,42.595,96.224C42.595,96.548,42.439,96.848,42.144999999999996,97.112ZM41.851,99.17C41.323,99.17,40.927,98.828,40.927,98.36C40.927,97.94,41.179,97.592,41.533,97.364C42.157,97.616,42.697,97.832,42.697,98.426C42.697,98.864,42.361000000000004,99.17,41.851,99.17ZM33.094,103.46000000000001C33.076,103.64,33.046,103.856,33.016,104.072L31.474,104.072L31.474,104.474L32.944,104.474C32.908,104.678,32.872,104.87,32.83,105.032L31.792,105.032L31.792,108.416L31.216,108.416L31.216,108.806L35.248,108.806L35.248,108.416L34.714,108.416L34.714,105.032L33.238,105.032C33.286,104.87,33.334,104.678,33.376,104.474L35.068,104.474L35.068,104.072L33.466,104.072L33.574,103.49L33.094,103.46000000000001ZM32.2,108.416L32.2,107.918L34.294,107.918L34.294,108.416L32.2,108.416ZM32.2,106.226L34.294,106.226L34.294,106.742L32.2,106.742L32.2,106.226ZM32.2,105.89L32.2,105.386L34.294,105.386L34.294,105.89L32.2,105.89ZM32.2,107.066L34.294,107.066L34.294,107.588L32.2,107.588L32.2,107.066ZM31.084,103.466C30.766,104.378,30.244,105.272,29.692,105.86C29.77,105.968,29.896,106.202,29.944,106.304C30.118,106.112,30.292,105.89,30.454,105.65L30.454,108.98L30.874,108.98L30.874,104.966C31.114,104.534,31.324,104.066,31.498,103.598L31.084,103.466ZM38.626,103.46000000000001L38.626,106.022C38.626,107.096,38.494,108.026,37.45,108.662C37.534,108.74,37.672,108.89,37.732,108.986C38.878,108.278,39.034,107.24,39.034,106.022L39.034,103.46000000000001L38.626,103.46000000000001ZM37.756,104.702C37.75,105.476,37.714,106.244,37.474000000000004,106.688L37.804,106.922C38.086,106.406,38.11,105.56,38.122,104.744L37.756,104.702ZM39.268,106.07L39.268,106.478L39.928,106.478L39.928,108.344L38.763999999999996,108.344L38.763999999999996,108.764L41.26,108.764L41.26,108.344L40.354,108.344L40.354,106.478L41.05,106.478L41.05,106.07L40.354,106.07L40.354,104.288L41.146,104.288L41.146,103.874L39.166,103.874L39.166,104.288L39.928,104.288L39.928,106.07L39.268,106.07ZM35.686,108.056L35.769999999999996,108.482C36.28,108.356,36.94,108.188,37.576,108.026L37.528,107.618L36.844,107.786L36.844,106.244L37.426,106.244L37.426,105.836L36.844,105.836L36.844,104.312L37.516,104.312L37.516,103.904L35.752,103.904L35.752,104.312L36.43,104.312L36.43,105.836L35.836,105.836L35.836,106.244L36.43,106.244L36.43,107.888L35.686,108.056ZM42.394,107.20400000000001L42.394,107.6L44.266,107.6L44.266,108.404L41.854,108.404L41.854,108.812L47.17,108.812L47.17,108.404L44.728,108.404L44.728,107.6L46.635999999999996,107.6L46.635999999999996,107.20400000000001L44.728,107.20400000000001L44.728,106.574L44.266,106.574L44.266,107.20400000000001L42.394,107.20400000000001ZM42.64,106.682C42.826,106.61,43.108000000000004,106.586,45.976,106.364C46.114000000000004,106.502,46.234,106.64,46.318,106.748L46.666,106.502C46.42,106.19,45.903999999999996,105.728,45.484,105.404L45.153999999999996,105.626C45.31,105.752,45.478,105.89,45.64,106.03999999999999L43.318,106.202C43.66,105.95,44.002,105.65,44.32,105.332L46.510000000000005,105.332L46.510000000000005,104.94200000000001L42.538,104.94200000000001L42.538,105.332L43.738,105.332C43.402,105.674,43.048,105.962,42.916,106.05199999999999C42.76,106.172,42.622,106.25,42.507999999999996,106.268C42.556,106.382,42.616,106.592,42.64,106.682ZM44.11,103.526C44.194,103.664,44.278,103.838,44.344,103.994L41.92,103.994L41.92,105.056L42.358000000000004,105.056L42.358000000000004,104.402L46.629999999999995,104.402L46.629999999999995,105.056L47.086,105.056L47.086,103.994L44.848,103.994C44.782,103.814,44.656,103.58,44.542,103.4L44.11,103.526Z"
            fill="#333333" fill-opacity="0.6000000238418579" />
        </g>
        <g>
          <path
            d="M24.19,29.892L24.19,30.348L26.002,30.348L26.002,34.308L23.812,34.308L23.812,34.758L29.206,34.758L29.206,34.308L26.482,34.308L26.482,30.348L28.264,30.348L28.264,32.43C28.264,32.525999999999996,28.234,32.556,28.114,32.562C27.988,32.568,27.567999999999998,32.568,27.106,32.556C27.178,32.676,27.262,32.874,27.286,33C27.838,33,28.216,32.994,28.438,32.922C28.66,32.844,28.726,32.706,28.726,32.442L28.726,29.892L24.19,29.892ZM30.934,29.556C30.706,30.414,30.316,31.248,29.823999999999998,31.782C29.938,31.842,30.136,31.974,30.226,32.052C30.454,31.782,30.664,31.44,30.856,31.062L32.278,31.062L32.278,32.388L30.490000000000002,32.388L30.490000000000002,32.82L32.278,32.82L32.278,34.35L29.83,34.35L29.83,34.788L35.194,34.788L35.194,34.35L32.746,34.35L32.746,32.82L34.69,32.82L34.69,32.388L32.746,32.388L32.746,31.062L34.906,31.062L34.906,30.624L32.746,30.624L32.746,29.46L32.278,29.46L32.278,30.624L31.054000000000002,30.624C31.186,30.318,31.3,29.988,31.39,29.658L30.934,29.556ZM36.046,30.81L36.046,34.980000000000004L36.507999999999996,34.980000000000004L36.507999999999996,30.81L36.046,30.81ZM36.135999999999996,29.754C36.412,30.018,36.724000000000004,30.396,36.862,30.636L37.234,30.396C37.09,30.144,36.766,29.79,36.484,29.538L36.135999999999996,29.754ZM37.774,32.730000000000004L39.214,32.730000000000004L39.214,33.54L37.774,33.54L37.774,32.730000000000004ZM37.774,31.554000000000002L39.214,31.554000000000002L39.214,32.352000000000004L37.774,32.352000000000004L37.774,31.554000000000002ZM37.366,31.176000000000002L37.366,33.912L39.64,33.912L39.64,31.176000000000002L37.366,31.176000000000002ZM37.612,29.796L37.612,30.222L40.516,30.222L40.516,34.434C40.516,34.512,40.492000000000004,34.536,40.414,34.542C40.336,34.542,40.09,34.548,39.838,34.536C39.897999999999996,34.65,39.958,34.842,39.982,34.95C40.348,34.95,40.606,34.95,40.768,34.878C40.924,34.8,40.978,34.686,40.978,34.434L40.978,29.796L37.612,29.796ZM28.906,44.676L29.242,44.525999999999996C28.726,43.674,28.48,42.653999999999996,28.48,41.634C28.48,40.62,28.726,39.606,29.242,38.748L28.906,38.592C28.354,39.492000000000004,28.024,40.458,28.024,41.634C28.024,42.816,28.354,43.782,28.906,44.676ZM30.862000000000002,40.164L32.254,40.164L32.254,40.812L30.862000000000002,40.812L30.862000000000002,40.164ZM32.704,40.164L34.12,40.164L34.12,40.812L32.704,40.812L32.704,40.164ZM30.862000000000002,39.162L32.254,39.162L32.254,39.804L30.862000000000002,39.804L30.862000000000002,39.162ZM32.704,39.162L34.12,39.162L34.12,39.804L32.704,39.804L32.704,39.162ZM29.932000000000002,41.784L29.932000000000002,42.198L31.906,42.198C31.624000000000002,42.84,31.048000000000002,43.32,29.758,43.59C29.848,43.686,29.962,43.866,29.998,43.980000000000004C31.468,43.65,32.098,43.025999999999996,32.397999999999996,42.198L34.294,42.198C34.21,43.025999999999996,34.108000000000004,43.391999999999996,33.976,43.506C33.916,43.56,33.844,43.566,33.712,43.566C33.574,43.566,33.178,43.56,32.788,43.524C32.86,43.638000000000005,32.92,43.812,32.926,43.938C33.316,43.956,33.682,43.962,33.874,43.956C34.084,43.938,34.222,43.908,34.354,43.788C34.546,43.596000000000004,34.66,43.128,34.774,41.982C34.78,41.922,34.792,41.784,34.792,41.784L32.524,41.784C32.566,41.598,32.602000000000004,41.406,32.626,41.202L34.588,41.202L34.588,38.778L30.418,38.778L30.418,41.202L32.158,41.202C32.134,41.406,32.098,41.598,32.05,41.784L29.932000000000002,41.784ZM36.094,44.676C36.646,43.782,36.976,42.816,36.976,41.634C36.976,40.458,36.646,39.492000000000004,36.094,38.592L35.752,38.748C36.268,39.606,36.525999999999996,40.62,36.525999999999996,41.634C36.525999999999996,42.653999999999996,36.268,43.674,35.752,44.525999999999996L36.094,44.676Z"
            fill="#333333" fill-opacity="0.6000000238418579" />
        </g>
        <g>
          <path
            d="M45.002,18.784C45.164,19.042,45.35,19.396,45.446,19.6L45.806,19.414C45.716,19.216,45.518,18.88,45.35,18.628L45.002,18.784ZM47.51,18.568C47.396,18.832,47.18,19.216,47.012,19.45L47.324,19.6C47.504,19.378,47.714,19.042,47.912,18.736L47.51,18.568ZM46.208,18.46L46.208,19.63L44.78,19.63L44.78,20.008L45.926,20.008C45.566,20.38,45.056,20.734,44.612,20.926000000000002C44.708,21.003999999999998,44.834,21.148,44.9,21.25C45.332,21.028,45.836,20.644,46.208,20.236L46.208,21.208L46.634,21.208L46.634,20.218C47.012,20.614,47.528,21.003999999999998,47.954,21.22C48.014,21.118000000000002,48.146,20.962,48.248,20.884C47.81,20.704,47.282,20.362000000000002,46.916,20.008L48.104,20.008L48.104,19.63L46.634,19.63L46.634,18.46L46.208,18.46ZM47.06,22.114C46.94,22.462,46.754,22.732,46.49,22.948C46.226,22.852,45.95,22.75,45.68,22.66C45.788,22.503999999999998,45.902,22.312,46.016,22.114L47.06,22.114ZM45.056,22.84C45.403999999999996,22.954,45.752,23.073999999999998,46.082,23.2C45.692,23.392,45.188,23.512,44.564,23.59C44.63,23.68,44.708,23.848,44.744,23.968C45.512,23.848,46.112,23.668,46.562,23.392C47.036,23.584,47.456,23.782,47.774,23.956L48.086,23.631999999999998C47.774,23.476,47.372,23.296,46.922,23.116C47.198,22.852,47.396,22.522,47.516,22.114L48.164,22.114L48.164,21.724L46.226,21.724C46.298,21.58,46.364,21.436,46.424,21.298000000000002L45.986,21.214C45.92,21.376,45.842,21.55,45.752,21.724L44.653999999999996,21.724L44.653999999999996,22.114L45.536,22.114C45.374,22.384,45.206,22.636,45.056,22.84ZM43.568,18.46L43.568,19.618L42.836,19.618L42.836,20.038L43.544,20.038C43.382,20.854,43.04,21.814,42.692,22.317999999999998C42.77,22.426000000000002,42.878,22.624,42.932,22.756C43.166,22.39,43.388,21.808,43.568,21.195999999999998L43.568,23.974L43.982,23.974L43.982,20.836C44.138,21.124,44.312,21.472,44.39,21.658L44.666,21.334C44.57,21.16,44.132,20.482,43.982,20.278L43.982,20.038L44.57,20.038L44.57,19.618L43.982,19.618L43.982,18.46L43.568,18.46ZM49.658,18.46L49.658,19.618L48.8,19.618L48.8,20.038L49.622,20.038C49.43,20.86,49.064,21.814,48.686,22.317999999999998C48.769999999999996,22.426000000000002,48.878,22.624,48.926,22.756C49.196,22.36,49.46,21.724,49.658,21.058L49.658,23.974L50.072,23.974L50.072,20.836C50.234,21.13,50.426,21.484,50.504,21.676000000000002L50.78,21.352C50.678,21.178,50.222,20.482,50.072,20.278L50.072,20.038L50.714,20.038L50.714,19.618L50.072,19.618L50.072,18.46L49.658,18.46ZM52.238,20.944L52.238,21.604L51.332,21.604L51.416,20.944L52.238,20.944ZM51.062,20.56C51.025999999999996,21.016,50.954,21.61,50.882,21.988L52.04,21.988C51.668,22.558,51.062,23.086,50.486,23.35C50.576,23.434,50.708,23.59,50.774,23.692C51.308,23.41,51.842,22.924,52.238,22.366L52.238,23.98L52.664,23.98L52.664,21.988L53.762,21.988C53.72,22.654,53.672,22.918,53.612,22.990000000000002C53.57,23.038,53.528,23.05,53.45,23.05C53.378,23.05,53.204,23.044,53.006,23.026C53.072,23.14,53.108000000000004,23.32,53.12,23.445999999999998C53.33,23.451999999999998,53.534,23.451999999999998,53.647999999999996,23.44C53.78,23.422,53.87,23.386,53.954,23.29C54.074,23.146,54.134,22.75,54.182,21.784C54.188,21.724,54.194,21.604,54.194,21.604L52.664,21.604L52.664,20.944L54.002,20.944L54.002,19.438L53.288,19.438C53.444,19.186,53.606,18.874,53.75,18.592L53.318,18.46C53.204,18.748,53.012,19.162,52.844,19.438L51.884,19.438L52.064,19.36C51.986000000000004,19.114,51.8,18.748,51.602000000000004,18.478L51.248,18.622C51.416,18.868,51.578,19.192,51.662,19.438L50.846000000000004,19.438L50.846000000000004,19.822L52.238,19.822L52.238,20.56L51.062,20.56ZM52.664,19.822L53.582,19.822L53.582,20.56L52.664,20.56L52.664,19.822ZM55.046,19.81L55.046,23.98L55.507999999999996,23.98L55.507999999999996,19.81L55.046,19.81ZM55.135999999999996,18.754C55.412,19.018,55.724000000000004,19.396,55.862,19.636L56.234,19.396C56.09,19.144,55.766,18.79,55.484,18.538L55.135999999999996,18.754ZM56.774,21.73L58.214,21.73L58.214,22.54L56.774,22.54L56.774,21.73ZM56.774,20.554000000000002L58.214,20.554000000000002L58.214,21.352L56.774,21.352L56.774,20.554000000000002ZM56.366,20.176000000000002L56.366,22.912L58.64,22.912L58.64,20.176000000000002L56.366,20.176000000000002ZM56.612,18.796L56.612,19.222L59.516,19.222L59.516,23.434C59.516,23.512,59.492000000000004,23.536,59.414,23.542C59.336,23.542,59.09,23.548000000000002,58.838,23.536C58.897999999999996,23.65,58.958,23.842,58.982,23.95C59.348,23.95,59.606,23.95,59.768,23.878C59.924,23.8,59.978,23.686,59.978,23.434L59.978,18.796L56.612,18.796Z"
            fill="#333333" fill-opacity="0.6000000238418579" />
        </g>
        <g>
          <path
            d="M64.712,35.052L64.712,35.916L63.224,35.916L63.224,35.052L64.712,35.052ZM65.186,35.052L66.728,35.052L66.728,35.916L65.186,35.916L65.186,35.052ZM64.712,34.632L63.224,34.632L63.224,33.774L64.712,33.774L64.712,34.632ZM65.186,34.632L65.186,33.774L66.728,33.774L66.728,34.632L65.186,34.632ZM62.756,33.33L62.756,36.726L63.224,36.726L63.224,36.354L64.712,36.354L64.712,36.99C64.712,37.692,64.91,37.878,65.582,37.878C65.732,37.878,66.746,37.878,66.908,37.878C67.55,37.878,67.694,37.56,67.772,36.647999999999996C67.634,36.612,67.44200000000001,36.528,67.322,36.444C67.28,37.224000000000004,67.22,37.422,66.884,37.422C66.668,37.422,65.792,37.422,65.612,37.422C65.252,37.422,65.186,37.35,65.186,37.002L65.186,36.354L67.19,36.354L67.19,33.33L65.186,33.33L65.186,32.472L64.712,32.472L64.712,33.33L62.756,33.33ZM69.158,32.46L69.158,33.618L68.3,33.618L68.3,34.038L69.122,34.038C68.93,34.86,68.564,35.814,68.186,36.318C68.27,36.426,68.378,36.624,68.426,36.756C68.696,36.36,68.96,35.724000000000004,69.158,35.058L69.158,37.974000000000004L69.572,37.974000000000004L69.572,34.836C69.734,35.13,69.926,35.484,70.004,35.676L70.28,35.352000000000004C70.178,35.178,69.722,34.482,69.572,34.278L69.572,34.038L70.214,34.038L70.214,33.618L69.572,33.618L69.572,32.46L69.158,32.46ZM71.738,34.944L71.738,35.604L70.832,35.604L70.916,34.944L71.738,34.944ZM70.562,34.56C70.526,35.016,70.45400000000001,35.61,70.382,35.988L71.53999999999999,35.988C71.168,36.558,70.562,37.086,69.986,37.35C70.076,37.434,70.208,37.59,70.274,37.692C70.80799999999999,37.41,71.342,36.924,71.738,36.366L71.738,37.980000000000004L72.164,37.980000000000004L72.164,35.988L73.262,35.988C73.22,36.653999999999996,73.172,36.918,73.112,36.99C73.07,37.038,73.028,37.05,72.95,37.05C72.878,37.05,72.70400000000001,37.044,72.506,37.025999999999996C72.572,37.14,72.608,37.32,72.62,37.446C72.83,37.452,73.034,37.452,73.148,37.44C73.28,37.422,73.37,37.386,73.45400000000001,37.29C73.574,37.146,73.634,36.75,73.682,35.784C73.688,35.724000000000004,73.694,35.604,73.694,35.604L72.164,35.604L72.164,34.944L73.502,34.944L73.502,33.438L72.788,33.438C72.944,33.186,73.106,32.874,73.25,32.592L72.818,32.46C72.70400000000001,32.748,72.512,33.162,72.344,33.438L71.384,33.438L71.564,33.36C71.486,33.114,71.3,32.748,71.102,32.478L70.748,32.622C70.916,32.868,71.078,33.192,71.162,33.438L70.346,33.438L70.346,33.822L71.738,33.822L71.738,34.56L70.562,34.56ZM72.164,33.822L73.082,33.822L73.082,34.56L72.164,34.56L72.164,33.822ZM74.54599999999999,33.81L74.54599999999999,37.980000000000004L75.008,37.980000000000004L75.008,33.81L74.54599999999999,33.81ZM74.636,32.754C74.912,33.018,75.224,33.396,75.362,33.636L75.734,33.396C75.59,33.144,75.266,32.79,74.984,32.538L74.636,32.754ZM76.274,35.730000000000004L77.714,35.730000000000004L77.714,36.54L76.274,36.54L76.274,35.730000000000004ZM76.274,34.554L77.714,34.554L77.714,35.352000000000004L76.274,35.352000000000004L76.274,34.554ZM75.866,34.176L75.866,36.912L78.14,36.912L78.14,34.176L75.866,34.176ZM76.112,32.796L76.112,33.222L79.01599999999999,33.222L79.01599999999999,37.434C79.01599999999999,37.512,78.992,37.536,78.914,37.542C78.836,37.542,78.59,37.548,78.338,37.536C78.398,37.65,78.458,37.842,78.482,37.95C78.848,37.95,79.106,37.95,79.268,37.878C79.424,37.8,79.47800000000001,37.686,79.47800000000001,37.434L79.47800000000001,32.796L76.112,32.796Z"
            fill="#333333" fill-opacity="0.6000000238418579" />
        </g>
        <g>
          <path
            d="M209.712,57.052L209.712,57.916L208.224,57.916L208.224,57.052L209.712,57.052ZM210.186,57.052L211.728,57.052L211.728,57.916L210.186,57.916L210.186,57.052ZM209.712,56.632L208.224,56.632L208.224,55.774L209.712,55.774L209.712,56.632ZM210.186,56.632L210.186,55.774L211.728,55.774L211.728,56.632L210.186,56.632ZM207.756,55.33L207.756,58.726L208.224,58.726L208.224,58.354L209.712,58.354L209.712,58.99C209.712,59.692,209.91,59.878,210.582,59.878C210.732,59.878,211.746,59.878,211.908,59.878C212.55,59.878,212.694,59.56,212.772,58.647999999999996C212.63400000000001,58.612,212.442,58.528,212.322,58.444C212.28,59.224000000000004,212.22,59.422,211.88400000000001,59.422C211.668,59.422,210.792,59.422,210.612,59.422C210.252,59.422,210.186,59.35,210.186,59.002L210.186,58.354L212.19,58.354L212.19,55.33L210.186,55.33L210.186,54.472L209.712,54.472L209.712,55.33L207.756,55.33ZM214.158,54.46L214.158,55.618L213.3,55.618L213.3,56.038L214.122,56.038C213.93,56.86,213.564,57.814,213.186,58.318C213.27,58.426,213.378,58.624,213.426,58.756C213.696,58.36,213.96,57.724000000000004,214.158,57.058L214.158,59.974000000000004L214.572,59.974000000000004L214.572,56.836C214.734,57.13,214.926,57.484,215.004,57.676L215.28,57.352000000000004C215.178,57.178,214.722,56.482,214.572,56.278L214.572,56.038L215.214,56.038L215.214,55.618L214.572,55.618L214.572,54.46L214.158,54.46ZM216.738,56.944L216.738,57.604L215.832,57.604L215.916,56.944L216.738,56.944ZM215.562,56.56C215.526,57.016,215.454,57.61,215.382,57.988L216.54,57.988C216.168,58.558,215.562,59.086,214.986,59.35C215.076,59.434,215.208,59.59,215.274,59.692C215.808,59.41,216.342,58.924,216.738,58.366L216.738,59.980000000000004L217.164,59.980000000000004L217.164,57.988L218.262,57.988C218.22,58.653999999999996,218.172,58.918,218.112,58.99C218.07,59.038,218.028,59.05,217.95,59.05C217.878,59.05,217.704,59.044,217.506,59.025999999999996C217.572,59.14,217.608,59.32,217.62,59.446C217.83,59.452,218.034,59.452,218.148,59.44C218.28,59.422,218.37,59.386,218.454,59.29C218.574,59.146,218.63400000000001,58.75,218.682,57.784C218.688,57.724000000000004,218.694,57.604,218.694,57.604L217.164,57.604L217.164,56.944L218.502,56.944L218.502,55.438L217.788,55.438C217.944,55.186,218.106,54.874,218.25,54.592L217.818,54.46C217.704,54.748,217.512,55.162,217.344,55.438L216.38400000000001,55.438L216.564,55.36C216.486,55.114,216.3,54.748,216.102,54.478L215.748,54.622C215.916,54.868,216.078,55.192,216.162,55.438L215.346,55.438L215.346,55.822L216.738,55.822L216.738,56.56L215.562,56.56ZM217.164,55.822L218.082,55.822L218.082,56.56L217.164,56.56L217.164,55.822ZM219.546,55.81L219.546,59.980000000000004L220.008,59.980000000000004L220.008,55.81L219.546,55.81ZM219.636,54.754C219.912,55.018,220.224,55.396,220.362,55.636L220.734,55.396C220.59,55.144,220.266,54.79,219.984,54.538L219.636,54.754ZM221.274,57.730000000000004L222.714,57.730000000000004L222.714,58.54L221.274,58.54L221.274,57.730000000000004ZM221.274,56.554L222.714,56.554L222.714,57.352000000000004L221.274,57.352000000000004L221.274,56.554ZM220.86599999999999,56.176L220.86599999999999,58.912L223.14,58.912L223.14,56.176L220.86599999999999,56.176ZM221.112,54.796L221.112,55.222L224.016,55.222L224.016,59.434C224.016,59.512,223.992,59.536,223.914,59.542C223.836,59.542,223.59,59.548,223.338,59.536C223.398,59.65,223.458,59.842,223.482,59.95C223.848,59.95,224.106,59.95,224.268,59.878C224.424,59.8,224.478,59.686,224.478,59.434L224.478,54.796L221.112,54.796Z"
            fill="#333333" fill-opacity="0.6000000238418579" />
        </g>
        <g>
          <path
            d="M98.077,111.578C98.815,111.578,99.517,111.032,99.517,110.072C99.517,109.1,98.917,108.668,98.191,108.668C97.92699999999999,108.668,97.729,108.734,97.531,108.842L97.645,107.57L99.301,107.57L99.301,107.102L97.165,107.102L97.021,109.154L97.315,109.34C97.56700000000001,109.172,97.753,109.082,98.047,109.082C98.599,109.082,98.959,109.454,98.959,110.084C98.959,110.726,98.545,111.122,98.023,111.122C97.513,111.122,97.189,110.888,96.943,110.636L96.667,110.996C96.967,111.29,97.387,111.578,98.077,111.578ZM101.503,111.578C102.337,111.578,102.871,110.822,102.871,109.286C102.871,107.762,102.337,107.024,101.503,107.024C100.663,107.024,100.135,107.762,100.135,109.286C100.135,110.822,100.663,111.578,101.503,111.578ZM101.503,111.134C101.005,111.134,100.663,110.576,100.663,109.286C100.663,108.002,101.005,107.456,101.503,107.456C102.001,107.456,102.343,108.002,102.343,109.286C102.343,110.576,102.001,111.134,101.503,111.134ZM104.353,111.5L104.923,111.5C104.995,109.778,105.181,108.752,106.213,107.432L106.213,107.102L103.459,107.102L103.459,107.57L105.595,107.57C104.731,108.77,104.431,109.832,104.353,111.5ZM93.076,115.826C93.478,116.024,93.976,116.33,94.222,116.55199999999999L94.504,116.19200000000001C94.246,115.976,93.736,115.688,93.34,115.508L93.076,115.826ZM92.752,117.506C93.13,117.686,93.598,117.98,93.832,118.184L94.096,117.824C93.862,117.626,93.376,117.35,93.01,117.188L92.752,117.506ZM92.956,120.596L93.334,120.902C93.688,120.344,94.108,119.594,94.426,118.958L94.096,118.664C93.748,119.342,93.274,120.134,92.956,120.596ZM96.19,115.472C95.86,116.312,95.182,117.14,94.33,117.662C94.432,117.74,94.594,117.902,94.66,117.998C94.888,117.848,95.104,117.68,95.30799999999999,117.494L95.30799999999999,117.842L97.372,117.842L97.372,117.428L95.38,117.428C95.77,117.062,96.1,116.636,96.358,116.18C96.80199999999999,116.87,97.426,117.542,98.002,117.92C98.074,117.8,98.23,117.632,98.338,117.548C97.696,117.188,96.976,116.468,96.568,115.766L96.64,115.598L96.19,115.472ZM94.99,118.52L94.99,120.998L95.446,120.998L95.446,120.674L97.204,120.674L97.204,120.98L97.672,120.98L97.672,118.52L94.99,118.52ZM95.446,120.266L95.446,118.928L97.204,118.928L97.204,120.266L95.446,120.266ZM101.176,115.88C101.068,116.264,100.864,116.684,100.63,116.93L101.008,117.086C101.254,116.81,101.458,116.354,101.56,115.97L101.176,115.88ZM101.152,118.44800000000001C101.05,118.856,100.852,119.312,100.618,119.564L101.002,119.75C101.254,119.444,101.452,118.952,101.554,118.526L101.152,118.44800000000001ZM103.54599999999999,115.832C103.402,116.132,103.144,116.576,102.94,116.846L103.282,116.99C103.498,116.738,103.768,116.342,103.99,115.994L103.54599999999999,115.832ZM103.618,118.424C103.462,118.772,103.162,119.264,102.928,119.564L103.288,119.714C103.528,119.426,103.834,118.976,104.08,118.586L103.618,118.424ZM99.232,115.91C99.538,116.168,99.904,116.53999999999999,100.078,116.78L100.402,116.498C100.228,116.27,99.844,115.91,99.538,115.664L99.232,115.91ZM102.148,115.46000000000001C102.1,116.876,101.938,117.566,100.57,117.932C100.66,118.016,100.774,118.184,100.822,118.292C101.626,118.05799999999999,102.064,117.71000000000001,102.304,117.188C102.898,117.53,103.564,117.974,103.912,118.274L104.2,117.932C103.804,117.608,103.054,117.14,102.436,116.81C102.538,116.438,102.574,115.988,102.598,115.46000000000001L102.148,115.46000000000001ZM102.148,117.956C102.094,119.48,101.914,120.212,100.312,120.59C100.408,120.68,100.528,120.86,100.57,120.974C101.626,120.69800000000001,102.124,120.26,102.364,119.57C102.676,120.28999999999999,103.19800000000001,120.77,104.062,120.968C104.122,120.848,104.242,120.674,104.332,120.578C103.294,120.404,102.748,119.762,102.526,118.85C102.562,118.58,102.586,118.286,102.598,117.956L102.148,117.956ZM98.776,117.344L98.776,117.776L99.694,117.776L99.694,119.96000000000001C99.694,120.254,99.514,120.458,99.406,120.542C99.484,120.614,99.604,120.776,99.652,120.866C99.724,120.758,99.868,120.644,100.654,120.044C100.606,119.96000000000001,100.534,119.792,100.498,119.672L100.12,119.94200000000001L100.12,117.344L98.776,117.344ZM105.394,119.20400000000001L105.394,119.6L107.266,119.6L107.266,120.404L104.854,120.404L104.854,120.812L110.17,120.812L110.17,120.404L107.728,120.404L107.728,119.6L109.636,119.6L109.636,119.20400000000001L107.728,119.20400000000001L107.728,118.574L107.266,118.574L107.266,119.20400000000001L105.394,119.20400000000001ZM105.64,118.682C105.826,118.61,106.108,118.586,108.976,118.364C109.114,118.502,109.23400000000001,118.64,109.318,118.748L109.666,118.502C109.42,118.19,108.904,117.728,108.484,117.404L108.154,117.626C108.31,117.752,108.478,117.89,108.64,118.03999999999999L106.318,118.202C106.66,117.95,107.002,117.65,107.32,117.332L109.51,117.332L109.51,116.94200000000001L105.538,116.94200000000001L105.538,117.332L106.738,117.332C106.402,117.674,106.048,117.962,105.916,118.05199999999999C105.76,118.172,105.622,118.25,105.508,118.268C105.556,118.382,105.616,118.592,105.64,118.682ZM107.11,115.526C107.194,115.664,107.278,115.838,107.344,115.994L104.92,115.994L104.92,117.056L105.358,117.056L105.358,116.402L109.63,116.402L109.63,117.056L110.086,117.056L110.086,115.994L107.848,115.994C107.782,115.814,107.656,115.58,107.542,115.4L107.11,115.526Z"
            fill="#333333" fill-opacity="1" />
        </g>
        <g>
          <path
            d="M134.906,111.578C135.644,111.578,136.346,111.032,136.346,110.072C136.346,109.1,135.746,108.668,135.02,108.668C134.756,108.668,134.558,108.734,134.36,108.842L134.474,107.57L136.13,107.57L136.13,107.102L133.994,107.102L133.85,109.154L134.144,109.34C134.396,109.172,134.582,109.082,134.876,109.082C135.428,109.082,135.788,109.454,135.788,110.084C135.788,110.726,135.374,111.122,134.852,111.122C134.342,111.122,134.018,110.888,133.772,110.636L133.496001,110.996C133.796,111.29,134.216,111.578,134.906,111.578ZM138.332,111.578C139.166,111.578,139.7,110.822,139.7,109.286C139.7,107.762,139.166,107.024,138.332,107.024C137.492,107.024,136.964,107.762,136.964,109.286C136.964,110.822,137.492,111.578,138.332,111.578ZM138.332,111.134C137.834,111.134,137.492,110.576,137.492,109.286C137.492,108.002,137.834,107.456,138.332,107.456C138.83,107.456,139.172,108.002,139.172,109.286C139.172,110.576,138.83,111.134,138.332,111.134ZM142.034,111.5L142.55,111.5L142.55,110.288L143.138,110.288L143.138,109.85L142.55,109.85L142.55,107.102L141.944,107.102L140.114,109.928L140.114,110.288L142.034,110.288L142.034,111.5ZM142.034,109.85L140.684,109.85L141.686,108.35C141.812,108.134,141.932,107.912,142.04,107.702L142.064,107.702C142.052,107.924,142.034,108.284,142.034,108.5L142.034,109.85ZM143.39,112.574L143.792,112.574L145.586,106.736L145.19,106.736L143.39,112.574ZM147.248,111.578C147.986,111.578,148.688,111.032,148.688,110.072C148.688,109.1,148.088,108.668,147.362,108.668C147.098,108.668,146.9,108.734,146.702,108.842L146.816,107.57L148.472,107.57L148.472,107.102L146.336,107.102L146.192,109.154L146.486,109.34C146.738,109.172,146.924,109.082,147.218,109.082C147.77,109.082,148.13,109.454,148.13,110.084C148.13,110.726,147.716,111.122,147.194,111.122C146.684,111.122,146.36,110.888,146.114,110.636L145.838,110.996C146.138,111.29,146.558,111.578,147.248,111.578ZM150.674,111.578C151.508,111.578,152.042,110.822,152.042,109.286C152.042,107.762,151.508,107.024,150.674,107.024C149.834,107.024,149.306,107.762,149.306,109.286C149.306,110.822,149.834,111.578,150.674,111.578ZM150.674,111.134C150.176,111.134,149.834,110.576,149.834,109.286C149.834,108.002,150.176,107.456,150.674,107.456C151.172,107.456,151.514,108.002,151.514,109.286C151.514,110.576,151.172,111.134,150.674,111.134ZM153.90800000000002,111.578C154.64600000000002,111.578,155.348,111.032,155.348,110.072C155.348,109.1,154.748,108.668,154.022,108.668C153.758,108.668,153.56,108.734,153.362,108.842L153.476,107.57L155.132,107.57L155.132,107.102L152.996,107.102L152.852,109.154L153.14600000000002,109.34C153.398,109.172,153.584,109.082,153.878,109.082C154.43,109.082,154.79,109.454,154.79,110.084C154.79,110.726,154.376,111.122,153.85399999999998,111.122C153.344,111.122,153.02,110.888,152.774,110.636L152.498,110.996C152.798,111.29,153.218,111.578,153.90800000000002,111.578ZM136.442,120.848C136.67,120.764,137.006,120.74,140.186,120.47C140.324,120.65,140.444,120.824,140.528,120.974L140.93,120.72800000000001C140.666,120.278,140.096,119.63,139.556,119.15L139.178,119.354C139.412,119.57,139.652,119.822,139.868,120.074L137.138,120.284C137.564,119.888,137.99,119.408,138.362,118.916L141.008,118.916L141.008,118.478L136.034,118.478L136.034,118.916L137.75,118.916C137.36,119.45,136.904,119.924,136.742,120.068C136.556,120.242,136.418,120.356,136.286,120.386C136.34,120.506,136.418,120.746,136.442,120.848ZM138.524,115.46000000000001C137.984,116.264,136.928,117.026,135.752,117.524C135.86,117.608,136.016,117.8,136.082,117.914C136.43,117.752,136.766,117.572,137.084,117.374L137.084,117.74L139.946,117.74L139.946,117.32L137.162,117.32C137.678,116.984,138.14,116.606,138.518,116.19200000000001C138.878,116.564,139.382,116.972,139.946,117.32C140.27,117.524,140.618,117.70400000000001,140.96,117.842C141.032,117.722,141.182,117.536,141.278,117.446C140.306,117.11,139.328,116.456,138.776,115.886L138.956,115.646L138.524,115.46000000000001ZM144.752,115.742C144.992,116.144,145.244,116.678,145.34,117.008L145.748,116.822C145.652,116.492,145.382,115.976,145.13,115.58L144.752,115.742ZM142.178,115.874C142.448,116.156,142.772,116.55199999999999,142.928,116.804L143.27,116.522C143.114,116.276,142.778,115.904,142.502,115.628L142.178,115.874ZM146.492,115.832C146.294,117.08,145.982,118.202,145.34,119.102C144.734,118.262,144.368,117.176,144.152,115.904L143.726,115.976C143.984,117.398,144.374,118.58,145.034,119.48C144.614,119.95400000000001,144.068,120.35,143.36599999999999,120.65C143.45,120.746,143.576,120.914,143.636,121.02199999999999C144.332,120.71000000000001,144.88400000000001,120.30199999999999,145.322,119.828C145.772,120.332,146.336,120.72200000000001,147.032,120.998C147.10399999999998,120.878,147.248,120.69800000000001,147.356,120.608C146.648,120.356,146.084,119.966,145.628,119.462C146.35399999999998,118.49,146.714,117.272,146.954,115.904L146.492,115.832ZM141.776,117.338L141.776,117.776L142.622,117.776L142.622,119.894C142.622,120.206,142.46,120.41,142.364,120.506C142.442,120.572,142.574,120.72800000000001,142.622,120.824C142.718,120.70400000000001,142.874,120.584,143.93,119.834C143.882,119.744,143.816,119.57,143.78,119.45L143.06,119.94800000000001L143.06,117.338L141.776,117.338ZM148.394,119.20400000000001L148.394,119.6L150.266,119.6L150.266,120.404L147.85399999999998,120.404L147.85399999999998,120.812L153.17000000000002,120.812L153.17000000000002,120.404L150.728,120.404L150.728,119.6L152.636,119.6L152.636,119.20400000000001L150.728,119.20400000000001L150.728,118.574L150.266,118.574L150.266,119.20400000000001L148.394,119.20400000000001ZM148.64,118.682C148.826,118.61,149.108,118.586,151.976,118.364C152.114,118.502,152.234,118.64,152.318,118.748L152.666,118.502C152.42000000000002,118.19,151.904,117.728,151.484,117.404L151.154,117.626C151.31,117.752,151.478,117.89,151.64,118.03999999999999L149.318,118.202C149.66,117.95,150.002,117.65,150.32,117.332L152.51,117.332L152.51,116.94200000000001L148.538,116.94200000000001L148.538,117.332L149.738,117.332C149.402,117.674,149.048,117.962,148.916,118.05199999999999C148.76,118.172,148.622,118.25,148.508,118.268C148.556,118.382,148.61599999999999,118.592,148.64,118.682ZM150.11,115.526C150.194,115.664,150.278,115.838,150.344,115.994L147.92,115.994L147.92,117.056L148.358,117.056L148.358,116.402L152.63,116.402L152.63,117.056L153.086,117.056L153.086,115.994L150.848,115.994C150.782,115.814,150.656,115.58,150.542,115.4L150.11,115.526Z"
            fill="#333333" fill-opacity="1" />
        </g>
        <g>
          <path
            d="M133.906,33.578C134.644,33.578,135.346,33.032,135.346,32.072C135.346,31.1,134.746,30.668,134.02,30.668C133.756,30.668,133.558,30.734,133.36,30.842L133.474,29.57L135.13,29.57L135.13,29.102L132.994,29.102L132.85,31.154L133.144,31.34C133.396,31.172,133.582,31.082,133.876,31.082C134.428,31.082,134.788,31.454,134.788,32.084C134.788,32.726,134.374,33.122,133.852,33.122C133.342,33.122,133.018,32.888,132.772,32.636L132.496001,32.996C132.796,33.29,133.216,33.578,133.906,33.578ZM136.192,33.5L138.604,33.5L138.604,33.044L137.722,33.044L137.722,29.102L137.302,29.102C137.062,29.240000000000002,136.78,29.342,136.39,29.414L136.39,29.762L137.176,29.762L137.176,33.044L136.192,33.044L136.192,33.5ZM140.662,33.578C141.496,33.578,142.03,32.822,142.03,31.286C142.03,29.762,141.496,29.024,140.662,29.024C139.822,29.024,139.294,29.762,139.294,31.286C139.294,32.822,139.822,33.578,140.662,33.578ZM140.662,33.134C140.164,33.134,139.822,32.576,139.822,31.286C139.822,30.002,140.164,29.456,140.662,29.456C141.16,29.456,141.502,30.002,141.502,31.286C141.502,32.576,141.16,33.134,140.662,33.134ZM142.39,34.574L142.792,34.574L144.586,28.736L144.19,28.736L142.39,34.574ZM146.248,33.578C146.986,33.578,147.688,33.032,147.688,32.072C147.688,31.1,147.088,30.668,146.362,30.668C146.098,30.668,145.9,30.734,145.702,30.842L145.816,29.57L147.472,29.57L147.472,29.102L145.336,29.102L145.192,31.154L145.486,31.34C145.738,31.172,145.924,31.082,146.218,31.082C146.77,31.082,147.13,31.454,147.13,32.084C147.13,32.726,146.716,33.122,146.194,33.122C145.684,33.122,145.36,32.888,145.114,32.636L144.838,32.996C145.138,33.29,145.558,33.578,146.248,33.578ZM148.534,33.5L150.946,33.5L150.946,33.044L150.064,33.044L150.064,29.102L149.644,29.102C149.404,29.240000000000002,149.122,29.342,148.732,29.414L148.732,29.762L149.518,29.762L149.518,33.044L148.534,33.044L148.534,33.5ZM151.864,33.5L154.276,33.5L154.276,33.044L153.394,33.044L153.394,29.102L152.974,29.102C152.734,29.240000000000002,152.452,29.342,152.062,29.414L152.062,29.762L152.848,29.762L152.848,33.044L151.864,33.044L151.864,33.5ZM135.598,39.53C135.43,40.058,135.13,40.724000000000004,134.77,41.15L135.184,41.39C135.532,40.934,135.826,40.232,136.006,39.698L135.598,39.53ZM139.168,39.614000000000004C139.444,40.22,139.726,41.012,139.816,41.498L140.26,41.336C140.158,40.85,139.864,40.07,139.582,39.476L139.168,39.614000000000004ZM136.834,37.466L136.834,38.51L136.834,38.564L135.022,38.564L135.022,39.013999999999996L136.822,39.013999999999996C136.768,40.184,136.438,41.606,134.752,42.644C134.866,42.722,135.04,42.896,135.118,43.004000000000005C136.912,41.876,137.248,40.304,137.302,39.013999999999996L138.526,39.013999999999996C138.442,41.257999999999996,138.346,42.128,138.154,42.326C138.088,42.403999999999996,138.022,42.422,137.896,42.416C137.746,42.416,137.374,42.416,136.972,42.38C137.056,42.512,137.11599999999999,42.716,137.128,42.86C137.494,42.872,137.878,42.89,138.094,42.866C138.316,42.842,138.46,42.788,138.598,42.608000000000004C138.838,42.32,138.928,41.408,139.024,38.816C139.024,38.744,139.03,38.564,139.03,38.564L137.314,38.564L137.314,38.516L137.314,37.466L136.834,37.466ZM142.444,37.634C142.09,38.534,141.484,39.397999999999996,140.806,39.932C140.926,40.004,141.13,40.166,141.22,40.256C141.886,39.662,142.522,38.75,142.924,37.766L142.444,37.634ZM144.49,37.586L144.052,37.766C144.508,38.672,145.276,39.68,145.906,40.256C145.996,40.135999999999996,146.164,39.962,146.284,39.872C145.66,39.374,144.892,38.414,144.49,37.586ZM141.466,42.584C141.694,42.5,142.018,42.476,145.186,42.266C145.348,42.512,145.486,42.745999999999995,145.588,42.938L146.032,42.698C145.732,42.152,145.114,41.306,144.586,40.664L144.166,40.856C144.406,41.156,144.664,41.504,144.904,41.846000000000004L142.096,42.007999999999996C142.696,41.312,143.284,40.412,143.782,39.5L143.29,39.29C142.81,40.286,142.078,41.336,141.838,41.606C141.61599999999999,41.888,141.454,42.068,141.292,42.11C141.358,42.242000000000004,141.442,42.482,141.466,42.584ZM147.394,41.204L147.394,41.6L149.266,41.6L149.266,42.403999999999996L146.85399999999998,42.403999999999996L146.85399999999998,42.812L152.17000000000002,42.812L152.17000000000002,42.403999999999996L149.728,42.403999999999996L149.728,41.6L151.636,41.6L151.636,41.204L149.728,41.204L149.728,40.574L149.266,40.574L149.266,41.204L147.394,41.204ZM147.64,40.682C147.826,40.61,148.108,40.586,150.976,40.364000000000004C151.114,40.502,151.234,40.64,151.318,40.748L151.666,40.502C151.42000000000002,40.19,150.904,39.728,150.484,39.403999999999996L150.154,39.626C150.31,39.752,150.478,39.89,150.64,40.04L148.318,40.202C148.66,39.95,149.002,39.65,149.32,39.332L151.51,39.332L151.51,38.942L147.538,38.942L147.538,39.332L148.738,39.332C148.402,39.674,148.048,39.962,147.916,40.052C147.76,40.172,147.622,40.25,147.508,40.268C147.556,40.382,147.61599999999999,40.592,147.64,40.682ZM149.11,37.525999999999996C149.194,37.664,149.278,37.838,149.344,37.994L146.92,37.994L146.92,39.056L147.358,39.056L147.358,38.402L151.63,38.402L151.63,39.056L152.086,39.056L152.086,37.994L149.848,37.994C149.782,37.814,149.656,37.58,149.542,37.4L149.11,37.525999999999996Z"
            fill="#333333" fill-opacity="0.6000000238418579" />
        </g>
        <g>
          <path
            d="M198.077,111.578C198.815,111.578,199.517,111.032,199.517,110.072C199.517,109.1,198.917,108.668,198.191,108.668C197.927,108.668,197.729,108.734,197.531,108.842L197.645,107.57L199.301,107.57L199.301,107.102L197.165,107.102L197.021,109.154L197.315,109.34C197.567,109.172,197.753,109.082,198.047,109.082C198.599,109.082,198.959,109.454,198.959,110.084C198.959,110.726,198.545,111.122,198.023,111.122C197.513,111.122,197.189,110.888,196.943,110.636L196.667,110.996C196.967,111.29,197.387,111.578,198.077,111.578ZM201.503,111.578C202.337,111.578,202.871,110.822,202.871,109.286C202.871,107.762,202.337,107.024,201.503,107.024C200.663,107.024,200.135,107.762,200.135,109.286C200.135,110.822,200.663,111.578,201.503,111.578ZM201.503,111.134C201.005,111.134,200.663,110.576,200.663,109.286C200.663,108.002,201.005,107.456,201.503,107.456C202.001,107.456,202.343,108.002,202.343,109.286C202.343,110.576,202.001,111.134,201.503,111.134ZM204.743,111.578C205.529,111.578,206.159,111.11,206.159,110.324C206.159,109.718,205.745,109.334,205.22899999999998,109.208L205.22899999999998,109.178C205.697,109.016,206.00900000000001,108.656,206.00900000000001,108.122C206.00900000000001,107.426,205.469,107.024,204.725,107.024C204.221,107.024,203.831,107.246,203.501,107.546L203.795,107.894C204.047,107.642,204.353,107.468,204.707,107.468C205.169,107.468,205.451,107.744,205.451,108.164C205.451,108.638,205.145,109.004,204.233,109.004L204.233,109.424C205.253,109.424,205.601,109.772,205.601,110.306C205.601,110.81,205.235,111.122,204.707,111.122C204.209,111.122,203.879,110.882,203.621,110.618L203.339,110.972C203.627,111.29,204.059,111.578,204.743,111.578ZM193.598,117.53C193.43,118.05799999999999,193.13,118.724,192.77,119.15L193.184,119.39C193.532,118.934,193.826,118.232,194.006,117.69800000000001L193.598,117.53ZM197.168,117.614C197.444,118.22,197.726,119.012,197.816,119.498L198.26,119.336C198.158,118.85,197.864,118.07,197.582,117.476L197.168,117.614ZM194.834,115.466L194.834,116.51L194.834,116.564L193.022,116.564L193.022,117.014L194.822,117.014C194.768,118.184,194.438,119.606,192.752,120.644C192.866,120.72200000000001,193.04,120.896,193.118,121.004C194.912,119.876,195.248,118.304,195.302,117.014L196.526,117.014C196.442,119.258,196.346,120.128,196.154,120.326C196.088,120.404,196.022,120.422,195.896,120.416C195.746,120.416,195.374,120.416,194.972,120.38C195.056,120.512,195.116,120.71600000000001,195.128,120.86C195.494,120.872,195.878,120.89,196.094,120.866C196.316,120.842,196.46,120.788,196.598,120.608C196.838,120.32,196.928,119.408,197.024,116.816C197.024,116.744,197.03,116.564,197.03,116.564L195.314,116.564L195.314,116.516L195.314,115.466L194.834,115.466ZM200.444,115.634C200.09,116.534,199.484,117.398,198.806,117.932C198.926,118.004,199.13,118.166,199.22,118.256C199.886,117.662,200.522,116.75,200.924,115.766L200.444,115.634ZM202.49,115.586L202.052,115.766C202.508,116.672,203.276,117.68,203.906,118.256C203.996,118.136,204.164,117.962,204.284,117.872C203.66,117.374,202.892,116.414,202.49,115.586ZM199.466,120.584C199.694,120.5,200.018,120.476,203.186,120.266C203.348,120.512,203.486,120.746,203.588,120.938L204.032,120.69800000000001C203.732,120.152,203.114,119.306,202.586,118.664L202.166,118.856C202.406,119.156,202.664,119.504,202.904,119.846L200.096,120.008C200.696,119.312,201.284,118.412,201.782,117.5L201.29,117.28999999999999C200.81,118.286,200.078,119.336,199.838,119.606C199.61599999999999,119.888,199.454,120.068,199.292,120.11C199.358,120.242,199.442,120.482,199.466,120.584ZM205.394,119.20400000000001L205.394,119.6L207.266,119.6L207.266,120.404L204.85399999999998,120.404L204.85399999999998,120.812L210.17000000000002,120.812L210.17000000000002,120.404L207.728,120.404L207.728,119.6L209.636,119.6L209.636,119.20400000000001L207.728,119.20400000000001L207.728,118.574L207.266,118.574L207.266,119.20400000000001L205.394,119.20400000000001ZM205.64,118.682C205.826,118.61,206.108,118.586,208.976,118.364C209.114,118.502,209.234,118.64,209.318,118.748L209.666,118.502C209.42000000000002,118.19,208.904,117.728,208.484,117.404L208.154,117.626C208.31,117.752,208.478,117.89,208.64,118.03999999999999L206.318,118.202C206.66,117.95,207.002,117.65,207.32,117.332L209.51,117.332L209.51,116.94200000000001L205.538,116.94200000000001L205.538,117.332L206.738,117.332C206.402,117.674,206.048,117.962,205.916,118.05199999999999C205.76,118.172,205.622,118.25,205.508,118.268C205.556,118.382,205.61599999999999,118.592,205.64,118.682ZM207.11,115.526C207.194,115.664,207.278,115.838,207.344,115.994L204.92,115.994L204.92,117.056L205.358,117.056L205.358,116.402L209.63,116.402L209.63,117.056L210.086,117.056L210.086,115.994L207.848,115.994C207.782,115.814,207.656,115.58,207.542,115.4L207.11,115.526Z"
            fill="#333333" fill-opacity="0.6000000238418579" />
        </g>
        <g>
          <path
            d="M255.077,61.578C255.815,61.578,256.517,61.032,256.517,60.072C256.517,59.1,255.917,58.668,255.191,58.668C254.927,58.668,254.729,58.734,254.531,58.842L254.645,57.57L256.301,57.57L256.301,57.102L254.165,57.102L254.021,59.153999999999996L254.315,59.34C254.567,59.172,254.753,59.082,255.047,59.082C255.599,59.082,255.959,59.454,255.959,60.084C255.959,60.726,255.545,61.122,255.023,61.122C254.513,61.122,254.189,60.888,253.943,60.636L253.667,60.996C253.967,61.29,254.387,61.578,255.077,61.578ZM258.503,61.578C259.337,61.578,259.871,60.822,259.871,59.286C259.871,57.762,259.337,57.024,258.503,57.024C257.663,57.024,257.135,57.762,257.135,59.286C257.135,60.822,257.663,61.578,258.503,61.578ZM258.503,61.134C258.005,61.134,257.663,60.576,257.663,59.286C257.663,58.002,258.005,57.456,258.503,57.456C259.001,57.456,259.343,58.002,259.343,59.286C259.343,60.576,259.001,61.134,258.503,61.134ZM260.693,61.5L263.105,61.5L263.105,61.044L262.223,61.044L262.223,57.102L261.803,57.102C261.563,57.24,261.281,57.342,260.891,57.414L260.891,57.762L261.677,57.762L261.677,61.044L260.693,61.044L260.693,61.5ZM250.598,67.53C250.43,68.05799999999999,250.13,68.724,249.77,69.15L250.184,69.39C250.532,68.934,250.826,68.232,251.006,67.69800000000001L250.598,67.53ZM254.168,67.614C254.444,68.22,254.726,69.012,254.816,69.498L255.26,69.336C255.158,68.85,254.864,68.07,254.582,67.476L254.168,67.614ZM251.834,65.466L251.834,66.51L251.834,66.564L250.022,66.564L250.022,67.014L251.822,67.014C251.768,68.184,251.438,69.606,249.752,70.644C249.866,70.72200000000001,250.04,70.896,250.118,71.004C251.912,69.876,252.248,68.304,252.302,67.014L253.526,67.014C253.442,69.258,253.346,70.128,253.154,70.326C253.088,70.404,253.022,70.422,252.896,70.416C252.746,70.416,252.374,70.416,251.972,70.38C252.056,70.512,252.116,70.71600000000001,252.128,70.86C252.494,70.872,252.878,70.89,253.094,70.866C253.316,70.842,253.46,70.788,253.598,70.608C253.838,70.32,253.928,69.408,254.024,66.816C254.024,66.744,254.03,66.564,254.03,66.564L252.314,66.564L252.314,66.516L252.314,65.466L251.834,65.466ZM257.444,65.634C257.09,66.534,256.484,67.398,255.806,67.932C255.926,68.004,256.13,68.166,256.22,68.256C256.886,67.662,257.522,66.75,257.924,65.766L257.444,65.634ZM259.49,65.586L259.052,65.766C259.508,66.672,260.276,67.68,260.906,68.256C260.996,68.136,261.164,67.962,261.284,67.872C260.66,67.374,259.892,66.414,259.49,65.586ZM256.466,70.584C256.694,70.5,257.018,70.476,260.186,70.266C260.348,70.512,260.486,70.746,260.588,70.938L261.032,70.69800000000001C260.73199999999997,70.152,260.114,69.306,259.586,68.664L259.166,68.856C259.406,69.156,259.664,69.504,259.904,69.846L257.096,70.008C257.696,69.312,258.284,68.412,258.782,67.5L258.29,67.28999999999999C257.81,68.286,257.078,69.336,256.838,69.606C256.616,69.888,256.454,70.068,256.292,70.11C256.358,70.242,256.442,70.482,256.466,70.584ZM262.394,69.20400000000001L262.394,69.6L264.266,69.6L264.266,70.404L261.854,70.404L261.854,70.812L267.17,70.812L267.17,70.404L264.728,70.404L264.728,69.6L266.636,69.6L266.636,69.20400000000001L264.728,69.20400000000001L264.728,68.574L264.266,68.574L264.266,69.20400000000001L262.394,69.20400000000001ZM262.64,68.682C262.826,68.61,263.108,68.586,265.976,68.364C266.114,68.502,266.234,68.64,266.318,68.748L266.666,68.502C266.42,68.19,265.904,67.728,265.484,67.404L265.154,67.626C265.31,67.752,265.478,67.89,265.64,68.03999999999999L263.318,68.202C263.66,67.95,264.002,67.65,264.32,67.332L266.51,67.332L266.51,66.94200000000001L262.538,66.94200000000001L262.538,67.332L263.738,67.332C263.402,67.674,263.048,67.962,262.916,68.05199999999999C262.76,68.172,262.622,68.25,262.508,68.268C262.556,68.382,262.616,68.592,262.64,68.682ZM264.11,65.526C264.194,65.664,264.278,65.838,264.344,65.994L261.92,65.994L261.92,67.056L262.358,67.056L262.358,66.402L266.63,66.402L266.63,67.056L267.086,67.056L267.086,65.994L264.848,65.994C264.782,65.814,264.656,65.58,264.542,65.4L264.11,65.526Z"
            fill="#333333" fill-opacity="0.6000000238418579" />
        </g>
        <g>
          <path
            d="M63.1429,0.5Q63.1428,0.4507543,63.1332,0.4024549Q63.1236,0.354155,63.1048,0.308658Q63.0859,0.263161,63.0586,0.222215Q63.0312,0.181269,62.9964,0.146447Q62.9616,0.11162499999999997,62.9206,0.08426499999999998Q62.8797,0.05690600000000001,62.8342,0.03805999999999998Q62.7887,0.019214999999999982,62.7404,0.009606999999999977Q62.6921,0,62.6429,0L0.5,0Q0.4507543,0,0.4024549,0.009606999999999977Q0.354155,0.019214999999999982,0.308658,0.03805999999999998Q0.263161,0.05690600000000001,0.222215,0.08426499999999998Q0.181269,0.11162499999999997,0.146447,0.146447Q0.11162499999999997,0.181269,0.08426499999999998,0.222215Q0.05690600000000001,0.263161,0.03805999999999998,0.308658Q0.019214999999999982,0.354155,0.009606999999999977,0.4024549Q0,0.4507543,0,0.5L0,121.976Q0,122.025,0.009606999999999977,122.073Q0.019214999999999982,122.122,0.03805999999999998,122.167Q0.05690600000000001,122.213,0.08426499999999998,122.253Q0.11162499999999997,122.294,0.146447,122.329Q0.181269,122.364,0.222215,122.391Q0.263161,122.419,0.308658,122.438Q0.354155,122.456,0.4024549,122.466Q0.4507543,122.476,0.5,122.476L29.3031,122.476L29.3031,134.111Q29.3031,134.161,29.3127,134.209Q29.3223,134.257,29.3412,134.303Q29.36,134.348,29.3874,134.389Q29.4148,134.43,29.4496,134.465Q29.4844,134.5,29.5253,134.527Q29.5663,134.555,29.6118,134.573Q29.6573,134.592,29.7056,134.602Q29.7539,134.611,29.8031,134.611L55.5749,134.611L55.5749,140.179Q55.5749,140.224,55.5828,140.268Q55.5906,140.311,55.6061,140.353Q55.6216,140.395,55.6442,140.433Q55.6669,140.472,55.6959,140.505Q55.725,140.539,55.7595,140.567Q55.7941,140.595,55.833,140.617Q55.872,140.638,55.9142,140.653Q55.9564,140.667,56.0004,140.674Q139.936,153.326,231.962,140.675Q232.006,140.669,232.049,140.655Q232.092,140.641,232.131,140.619Q232.171,140.598,232.206,140.57Q232.241,140.542,232.271,140.508Q232.3,140.474,232.323,140.435Q232.346,140.397,232.362,140.355Q232.378,140.313,232.386,140.268Q232.394,140.224,232.394,140.179L232.394,134.611L259.681,134.611Q259.73,134.611,259.779,134.602Q259.827,134.592,259.872,134.573Q259.918,134.555,259.959,134.527Q260,134.5,260.035,134.465Q260.07,134.43,260.097,134.389Q260.124,134.348,260.143,134.303Q260.162,134.257,260.172,134.209Q260.181,134.161,260.181,134.111L260.181,126.521L290.5,126.521Q290.549,126.521,290.598,126.511Q290.646,126.502,290.691,126.483Q290.737,126.464,290.778,126.437Q290.819,126.409,290.854,126.374Q290.888,126.34,290.916,126.299Q290.943,126.258,290.962,126.212Q290.981,126.167,290.99,126.118Q291,126.07,291,126.021L291,0.5Q291,0.4507543,290.99,0.4024549Q290.981,0.354155,290.962,0.308658Q290.943,0.263161,290.916,0.222215Q290.888,0.181269,290.854,0.146447Q290.819,0.11162499999999997,290.778,0.08426499999999998Q290.737,0.05690600000000001,290.691,0.03805999999999998Q290.646,0.019214999999999982,290.598,0.009606999999999977Q290.549,0,290.5,0L225.326,0Q225.277,0,225.228,0.009606999999999977Q225.18,0.019214999999999982,225.134,0.03805999999999998Q225.089,0.05690600000000001,225.048,0.08426499999999998Q225.007,0.11162499999999997,224.972,0.146447Q224.937,0.181269,224.91,0.222215Q224.883,0.263161,224.864,0.308658Q224.845,0.354155,224.835,0.4024549Q224.826,0.4507543,224.826,0.5L224.826,3.65792L63.1429,3.65792L63.1429,0.5ZM62.1429,1L1,1L1,121.476L29.8031,121.476Q29.8524,121.476,29.9007,121.485Q29.949,121.495,29.9945,121.514Q30.04,121.533,30.0809,121.56Q30.1219,121.587,30.1567,121.622Q30.1915,121.657,30.2189,121.698Q30.2462,121.739,30.2651,121.784Q30.2839,121.83,30.2935,121.878Q30.3031,121.926,30.3031,121.976L30.3031,133.611L56.0749,133.611Q56.1242,133.611,56.1725,133.621Q56.2207,133.631,56.2662,133.649Q56.3117,133.668,56.3527,133.696Q56.3936,133.723,56.4285,133.758Q56.4633,133.793,56.4906,133.834Q56.518,133.875,56.5368,133.92Q56.5557,133.966,56.5653,134.014Q56.5749,134.062,56.5749,134.111L56.5749,139.749Q139.979,152.254,231.394,139.743L231.394,134.111Q231.394,134.062,231.403,134.014Q231.413,133.966,231.432,133.92Q231.451,133.875,231.478,133.834Q231.505,133.793,231.54,133.758Q231.575,133.723,231.616,133.696Q231.657,133.668,231.702,133.649Q231.748,133.631,231.796,133.621Q231.844,133.611,231.894,133.611L259.181,133.611L259.181,126.021Q259.181,125.972,259.191,125.923Q259.2,125.875,259.219,125.83Q259.238,125.784,259.265,125.743Q259.293,125.702,259.328,125.667Q259.362,125.633,259.403,125.605Q259.444,125.578,259.49,125.559Q259.535,125.54,259.584,125.531Q259.632,125.521,259.681,125.521L290,125.521L290,1L225.826,1L225.826,4.15792Q225.826,4.20716,225.816,4.255459999999999Q225.807,4.3037600000000005,225.788,4.34926Q225.769,4.39476,225.741,4.435700000000001Q225.714,4.476649999999999,225.679,4.51147Q225.644,4.54629,225.604,4.57365Q225.563,4.60101,225.517,4.61986Q225.472,4.6387,225.423,4.64831Q225.375,4.65792,225.326,4.65792L62.6429,4.65792Q62.5936,4.65792,62.5453,4.64831Q62.497,4.6387,62.4515,4.61986Q62.406,4.60101,62.3651,4.57365Q62.3241,4.54629,62.2893,4.51147Q62.2545,4.476649999999999,62.2271,4.435700000000001Q62.1997,4.39476,62.1809,4.34926Q62.1621,4.3037600000000005,62.1525,4.255459999999999Q62.1428,4.20716,62.1429,4.15792L62.1429,1Z"
            fill-rule="evenodd" fill="#333333" fill-opacity="1" />
        </g>
      </g>
      <!-- 路线层 -->
      <polyline v-if="internalPoints.length" :points="routeString" class="route-line" stroke-linecap="round"
        stroke-linejoin="round" />
      <!--定位-->
      <g v-if="positionStyle"  :style="positionStyle" clip-path="url(#master_svg0_32_03912)" >
        <g>
          <path
            d="M8,13Q8.17184,13,8.34347,12.998190000000001Q8.51511,12.99639,8.686119999999999,12.99278Q8.85713,12.98917,9.02711,12.98376Q9.19709,12.97836,9.36563,12.97118Q9.53417,12.963989999999999,9.70086,12.95505Q9.86755,12.9461,10.03199,12.935410000000001Q10.19643,12.92472,10.35823,12.91232Q10.52002,12.89991,10.67878,12.885819999999999Q10.83754,12.87173,10.99288,12.85598Q11.1482,12.84024,11.2998,12.82288Q11.4513,12.80552,11.5987,12.78659Q11.7461,12.76766,11.889,12.7472Q12.0319,12.72675,12.1699,12.70481Q12.3079,12.68288,12.4408,12.65952Q12.5736,12.63615,12.7009,12.61143Q12.8282,12.5867,12.9497,12.56066Q13.0713,12.53462,13.1867,12.50734Q13.3021,12.48005,13.4111,12.45159Q13.5201,12.42313,13.6225,12.39355Q13.7248,12.36397,13.8203,12.33335Q13.9158,12.30274,14.0041,12.27115Q14.0924,12.23957,14.1734,12.2071Q14.2545,12.17462,14.3279,12.14133Q14.4014,12.108039999999999,14.4672,12.07402Q14.5329,12.04,14.5908,12.00533Q14.6487,11.97066,14.6986,11.93543Q14.7485,11.90019,14.7902,11.86447Q14.832,11.82875,14.8655,11.79264Q14.899,11.75652,14.9242,11.7201Q14.9494,11.68367,14.9663,11.64703Q14.9831,11.61038,14.9916,11.5736Q15,11.53682,15,11.5Q15,11.46318,14.9916,11.4264Q14.9831,11.38962,14.9663,11.35297Q14.9494,11.31633,14.9242,11.2799Q14.899,11.24348,14.8655,11.20736Q14.832,11.17125,14.7902,11.13553Q14.7485,11.09981,14.6986,11.06457Q14.6487,11.02934,14.5908,10.994665Q14.5329,10.959995,14.4672,10.925975Q14.4014,10.891955,14.3279,10.858667Q14.2545,10.82538,14.1734,10.792905Q14.0924,10.76043,14.0041,10.728846Q13.9158,10.697262,13.8203,10.666645Q13.7248,10.636027,13.6225,10.606451Q13.5201,10.576875,13.4111,10.54841Q13.3021,10.519946000000001,13.1867,10.492662Q13.0713,10.465378,12.9497,10.43934Q12.8282,10.413302,12.7009,10.388573Q12.5736,10.363845,12.4408,10.340484Q12.3079,10.317124,12.1699,10.295189Q12.0319,10.273253,11.889,10.252796Q11.7461,10.232338,11.5987,10.213407Q11.4513,10.194476,11.2998,10.177118Q11.1482,10.15976,10.99288,10.144016Q10.83754,10.128272,10.67878,10.114181Q10.52002,10.100089,10.35823,10.0876839Q10.19643,10.0752786,10.03199,10.0645895Q9.86755,10.0539004,9.70086,10.0449531Q9.53417,10.0360059,9.36563,10.0288221Q9.19709,10.0216383,9.02711,10.0162352Q8.85713,10.0108322,8.686119999999999,10.00722291Q8.51511,10.00361363,8.34347,10.00180682Q8.17184,10,8,10Q7.82816,10,7.65653,10.00180682Q7.48489,10.00361363,7.31388,10.00722291Q7.14287,10.0108322,6.97289,10.0162352Q6.80291,10.0216383,6.63437,10.0288221Q6.46583,10.0360059,6.29914,10.0449531Q6.13245,10.0539004,5.96801,10.0645895Q5.80357,10.0752786,5.64177,10.0876839Q5.47998,10.100089,5.32122,10.114181Q5.16246,10.128272,5.00711,10.144016Q4.85177,10.15976,4.70022,10.177118Q4.5486699999999995,10.194476,4.40128,10.213407Q4.25389,10.232338,4.11101,10.252796Q3.96813,10.273253,3.8301,10.295189Q3.69208,10.317124,3.55925,10.340484Q3.42641,10.363845,3.29909,10.388573Q3.17176,10.413302,3.05025,10.43934Q2.92874,10.465378,2.81334,10.492662Q2.69794,10.519946000000001,2.58893,10.54841Q2.4799100000000003,10.576875,2.3775500000000003,10.606451Q2.2751799999999998,10.636027,2.17971,10.666645Q2.0842400000000003,10.697262,1.9959,10.728846Q1.907556,10.76043,1.826551,10.792905Q1.745546,10.82538,1.672075,10.858667Q1.598604,10.891955,1.532843,10.925975Q1.4670830000000001,10.959995,1.409191,10.994665Q1.3513,11.02934,1.301418,11.06457Q1.251535,11.09981,1.209781,11.13553Q1.168027,11.17125,1.134503,11.20736Q1.100979,11.24348,1.0757644,11.2799Q1.0505502,11.31633,1.0337069,11.35297Q1.0168636,11.38962,1.0084318,11.4264Q1,11.46318,1,11.5Q1,11.53682,1.0084318,11.5736Q1.0168636,11.61038,1.0337069,11.64703Q1.0505502,11.68367,1.0757644,11.7201Q1.100979,11.75652,1.134503,11.79264Q1.168027,11.82875,1.209781,11.86447Q1.251535,11.90019,1.301418,11.93543Q1.3513,11.97066,1.409191,12.00533Q1.4670830000000001,12.04,1.532843,12.07402Q1.598604,12.108039999999999,1.672075,12.14133Q1.745546,12.17462,1.826551,12.2071Q1.907556,12.23957,1.995899,12.27115Q2.0842400000000003,12.30274,2.17971,12.333359999999999Q2.2751799999999998,12.36397,2.3775500000000003,12.39355Q2.4799100000000003,12.42313,2.58893,12.45159Q2.69794,12.48005,2.81334,12.50734Q2.92874,12.53462,3.05025,12.56066Q3.17176,12.5867,3.29909,12.61143Q3.42641,12.63616,3.55925,12.65952Q3.69208,12.68288,3.8301,12.70481Q3.96813,12.72675,4.11101,12.7472Q4.25389,12.76766,4.40128,12.78659Q4.5486699999999995,12.80552,4.70022,12.82288Q4.85177,12.84024,5.00711,12.85598Q5.16246,12.87173,5.32122,12.885819999999999Q5.47998,12.89991,5.64177,12.91232Q5.80357,12.92472,5.96801,12.935410000000001Q6.13245,12.9461,6.29914,12.95505Q6.46583,12.963989999999999,6.63437,12.97118Q6.80291,12.97836,6.97289,12.98376Q7.14287,12.98917,7.31388,12.99278Q7.48489,12.99639,7.65653,12.998190000000001Q7.82816,13,8,13ZM14.4472,11.5Q14.3814,11.55241,14.2705,11.61242Q13.8,11.86712,12.845,12.07176Q10.84653,12.5,8,12.5Q5.15348,12.5,3.15502,12.07176Q2.20004,11.86712,1.72949,11.61242Q1.6186120000000002,11.55241,1.552785,11.5Q1.6186120000000002,11.44759,1.72949,11.38758Q2.20004,11.13288,3.15502,10.928241Q5.15347,10.5,8,10.5Q10.84653,10.5,12.845,10.928241Q13.8,11.13288,14.2705,11.38758Q14.3814,11.44759,14.4472,11.5Z"
            fill-rule="evenodd" fill="url(#master_svg1_1_5010)" fill-opacity="0.6000000238418579" />
        </g>
        <g filter="url(#master_svg2_32_03912/1_08328)">
          <path
            d="M8,3C6.34302,3,5,4.43262,5,6.19994Q5,8.99977,8,11Q11,9.05002,11,6.19994C11,4.43262,9.65681,3.000000262817,8,3ZM8,7.57142C7.22021,7.57142,6.58821,6.88912,6.58821,6.04758C6.58821,5.20595,7.22021,4.52375,8,4.52375C8.77966,4.52375,9.41174,5.20595,9.41174,6.04758C9.41174,6.88908,8.77966,7.57142,8,7.57142Z"
            fill="url(#master_svg3_1_5013)" fill-opacity="1" style="mix-blend-mode: passthrough" />
        </g>
      </g>
      <!--门-->
      <g v-for="(doorStyle, i) in doorsStyles" :key="`door-${i}`" :style="doorStyle">
        <path
          d="M0,0.5Q0,0.4507543,0.009606999999999977,0.4024549Q0.019214999999999982,0.354155,0.03805999999999998,0.308658Q0.05690600000000001,0.263161,0.08426499999999998,0.222215Q0.11162499999999997,0.181269,0.146447,0.146447Q0.181269,0.11162499999999997,0.222215,0.08426499999999998Q0.263161,0.05690600000000001,0.308658,0.03805999999999998Q0.354155,0.019214999999999982,0.4024549,0.009606999999999977Q0.4507543,0,0.5,0Q0.5492457,0,0.5975451,0.009606999999999977Q0.645845,0.019214999999999982,0.691342,0.03805999999999998Q0.736839,0.05690600000000001,0.777785,0.08426499999999998Q0.818731,0.11162499999999997,0.853553,0.146447Q0.888375,0.181269,0.915735,0.222215Q0.943094,0.263161,0.96194,0.308658Q0.980785,0.354155,0.9903930000000001,0.4024549Q1,0.4507543,1,0.5L1,1.333904L0,1.333904L0,0.5ZM1,3.31267L3.4,3.31267L0.5,8.335619999999999L-2.4,3.31267L0,3.31267L0,3.00171L1,3.00171L1,3.31267Z"
          fill-rule="evenodd" fill="#FF592B" fill-opacity="1" />
      </g>
    </svg>
    <div class="debugger-pannel" style="position: fixed; top: 0; right: 0">
      <button type="button" class="btn btn-primary" @click="clear">清空</button>
      <div v-for="item of internalPoints" :key="item.x + item.y">
        <input type="text" v-model="item.x" />
        <input type="text" v-model="item.y" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from "vue";
const props = defineProps<{
  /** 线路坐标 */
  modelValue: { x: number; y: number }[];
  /** 默认选中的房间 ID */
  selectedId?: "507" | "5045";
  /** 门的位置 */
  doorsPosition?: { x: number; y: number }[];
  /** 定位图标 */
  position?: { x: number; y: number };
}>();

const emit = defineEmits<{
  (e: "update:modelValue", val: { x: number; y: number }[]): void;
  (e: "update:selectedId", v: string | undefined): void;
}>();
const selected = ref<string | null>(null);
const internalPoints = ref<{ x: number; y: number }[]>([]);
const svgEl = ref<SVGSVGElement>();
watch(
  () => props.modelValue,
  (v) => {
    internalPoints.value = v.slice();
  },
  { immediate: true }
);
watch(
  () => props.selectedId,
  (v) => {
    selected.value = v ?? null;
  },
  { immediate: true }
);


// 这个函数会把屏幕坐标转成 SVG 坐标并打印
// TODO:remove
function logSvgPoint(evt: MouseEvent) {
  if (!svgEl.value) return;
  const svg = svgEl.value;
  // 创建一个 SVGPoint
  const pt = svg.createSVGPoint();
  pt.x = evt.clientX;
  pt.y = evt.clientY;
  // 转换到 SVG 坐标系
  const svgP = pt.matrixTransform(svg.getScreenCTM()!.inverse());
  addPoint(svgP)
}

function addPoint(p: { x: number; y: number }) {
  internalPoints.value.push(p);
  // 只有在用户真正添加点的时候才 emit
  emit("update:modelValue", internalPoints.value.slice());
}
onMounted(() => {
  // 绑定全 SVG 的点击事件
  svgEl.value?.addEventListener("click", logSvgPoint);
});

onBeforeUnmount(() => {
  svgEl.value?.removeEventListener("click", logSvgPoint);
});

const selectRoom = (roomId: string) => {
  selected.value = roomId;
  emit("update:selectedId", selected.value);
};
function clear() {
  console.log(internalPoints.value);
  internalPoints.value = [];
  emit("update:modelValue", []);
  emit("update:selectedId", undefined);
}
const clearSelection = (_: MouseEvent) => {
  // selected.value = null;
  // internalPoints.value = [];
};

const routeString = computed(() =>
  internalPoints.value.map((p) => `${p.x},${p.y}`).join(" ")
);

const positionStyle = computed(() =>
  props.position
    ? `transform: translate(${props.position.x}px, ${props.position.y}px)`
    : ""
);
const doorsStyles = computed(() =>
  (props.doorsPosition || []).map(
    (d) => `transform: translate(${d.x}px, ${d.y}px)`
  )
);


// 监听选中房间或 SVG 初始化
// watch([svgEl, selected], drawRoute, { immediate: true });
</script>
<style lang="css" scoped>
.sl-mr-container {
  width: 100%;
  position: relative;
  /* 147 / 291 ≈ 0.50515 → 50.515% */
  padding-bottom: 50.515%;
}

.sl-mr-container svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.sl-mr-container svg g.meeting-room:hover {
  cursor: pointer;
  transition: fill 0.2s, stroke 0.2s;
}

.sl-mr-container svg g.meeting-room.selected * {
  /* 整个 g 里的所有形状都加高亮 */
  fill: #ffe6e4 !important;
  stroke: #ff3737 !important;
  stroke-width: 1 !important;
}

.sl-mr-container .route-line {
  fill: none;
  stroke: #f56c6c;
  stroke-width: 1;
  stroke-dasharray: 6 4;
}
</style>
