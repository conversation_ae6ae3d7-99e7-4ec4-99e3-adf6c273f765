<template>
    <n-modal v-model:show="show" :style="{ 'width': isDialog ? '400px' : `${width}px` }" :mask-closable="false"
        to="#app">
        <div class="modal-wrapper" :class="{ 'dialog': isDialog }">
            <!-- 头部标题 -->
            <div class="modal-header">
                <div class="modal-header-title" :class="titleClass">
                    <span>{{ title || (isDialog ? '提示' : title) }}</span>
                </div>
                <span @click="onCancel" style="cursor: pointer;" v-if="ifCanClose">
                    <i class="app-icon icon-14-1"></i>
                </span>
            </div>
            <!-- 内容插槽 -->
            <div class="modal-content">
                <n-config-provider :theme-overrides="lightThemeOverrides">
                    <slot />
                </n-config-provider>
            </div>
            <!-- 底部按钮 -->
            <div class="modal-footer" v-if="showFooter">
                <slot name="footer">
                    <button type="button" class="btn-primary" @click="onConfirm">{{ confirmText }}</button>
                    <button type="button" @click="onCancel">{{ cancelText }}</button>
                </slot>
            </div>
        </div>
    </n-modal>
</template>

<script setup lang="ts">
import { NModal, NConfigProvider, type GlobalThemeOverrides } from 'naive-ui';
import { ref, type VNode, type VNodeArrayChildren } from 'vue';
const { confirmText = '确定', cancelText = '取消', width = 600, onConfirmCallback, showFooter = true, ifCanClose = true } = defineProps<ModalOptions>()
const emit = defineEmits(['confirm', 'cancel'])
const show = defineModel('show', { required: true, type: Boolean })
const slots = defineSlots()
const isLoadingConfirm = ref(false)
const onCancel = () => {
    show.value = false
    // onCancelCallback?.()
    emit('cancel')
}
const onConfirm = async () => {
    isLoadingConfirm.value = true
    emit('confirm')
    if (onConfirmCallback && typeof onConfirmCallback === 'function') {
        const isSuccess = await onConfirmCallback()
        show.value = !isSuccess
        isSuccess && emit('cancel') // mock destroy
    } else {
        show.value = false
        emit('cancel')
    }
    isLoadingConfirm.value = false
}
// 主题配置：需同APP.vue配置保持一致。可提取到config里。
const lightThemeOverrides: GlobalThemeOverrides = {
    common: {
        primaryColor: '#4F7AF6',
        primaryColorHover: '#4F7AF6',
        primaryColorPressed: '#4F7AF6',
        primaryColorSuppl: '#4F7AF6',
        heightMedium: '40px'
    },
    Modal: {
        padding: '0',
        common: {
            primaryColor: '#4F7AF6',
            primaryColorHover: '#4F7AF6',
            primaryColorPressed: '#4F7AF6',
            primaryColorSuppl: '#4F7AF6',
        },
        peers: {
            Dialog: {
                padding: '0',
            }
        }
    },
    Input: {
        borderHover: '1px solid #4F7AF6',
        borderFocus: '1px solid #4F7AF6',
        groupLabelBorder: '1px solid #4F7AF6',
        heightMedium: '40px'
    },
    DataTable: {
        thColor: '#F2F3F7',
        borderColor: '#E5E6EA'
    },
    Table: {
        thColor: '#F2F3F7',
        borderColor: '#E5E6EA'
    },
    Dropdown: {
        optionTextColor: '#fff',
        color: '#1C2340',
        optionColorHover: '#4F7AF6',
        optionTextColorHover: '#fff',
    },
    Dialog: {
        borderRadius: '8px',
        padding: '0',
    },
}
type SlotContent =
    | (() => VNode | VNodeArrayChildren | string) // 函数返回单个 VNode 或数组
    | VNode // 直接传递静态 VNode
    | string;
export interface ModalOptions {
    show?: boolean;
    /** dialog 默认为 “提示” */
    title?: string;
    titleClass?: string;
    loading?: boolean;
    width?: number;
    cancelText?: string;
    confirmText?: string;
    // onCancelCallback?: () => void;
    /** 
     * modal控制权丢给外部处理。若涉及接口请求请使用onConfirmCallback；否则请用confirm；
     * return true | false弹窗控制权、
     * // return string错误提示
     * */
    onConfirmCallback?: () => Promise<boolean>;
    /** 简单的确认操作 */
    onConfirm?: () => void
    footerSlot?: SlotContent;
    content?: SlotContent;
    /** 是否提示框 */
    isDialog?: boolean;
    onCancel?: () => void
    showFooter?: boolean
    /** 是否可以关闭 */
    ifCanClose?: boolean
}
</script>

<style scoped>
/* @import '@/styles/component/button.css'; */
.modal-wrapper.dialog {
    min-height: 212px;
}

.modal-wrapper.dialog .modal-content {
    display: flex;
    justify-content: center;
    align-items: center;
}

.center {
    justify-content: center;
}

.modal-content {
    padding: 20px;
    flex: 1;
}

/* modal peers button 与 common button样式不共用，需单独另起 */
.primary:hover {
    background-color: #4F7AF6;
    border-color: #4F7AF6;
}

.primary {
    background-color: #4F7AF6;
    color: #fff;
}

:slotted(.form-item) {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
}

:slotted(.form-item:not(:last-child)) {
    margin-bottom: 8px;
}

:slotted(.label) {
    display: inline-block;
    color: #333;
    min-width: 100px;
    white-space: nowrap;
    margin-right: 8px;
}


:slotted(.label.required) {
    position: relative;
    color: #333;
    width: 100px;
    white-space: nowrap;
    margin-right: 8px;
}

:slotted(.label.required::after) {
    position: absolute;
    content: '*';
    top: 50%;
    width: 10px;
    color: red;
    transform: translateY(-50%);
    left: -10px;
}

:deep(--n-border-hover) {
    border-color: #4F7AF6;
}
</style>