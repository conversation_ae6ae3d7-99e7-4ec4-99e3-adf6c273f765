/** 挪车服务反馈 */

/** 挪车服务反馈 */
import { get, post } from "./http.service";
import type {
  NoticeFeedback,
  NoticefeedbackSearch,
} from "@/models/Noticefeedback";
/**
 * 保存通知反馈信息
 * POST /api/noticefeedback/save :contentReference[oaicite:0]{index=0}:contentReference[oaicite:1]{index=1}
 */
const save = async (data: NoticeFeedback): Promise<string> => {
  return post<NoticeFeedback, string>(`/noticefeedback/save`, data);
};

/**
 * 清空所有通知反馈记录（管理员视图）
 * GET /api/noticefeedback/deleteListByAdminDisplay :contentReference[oaicite:2]{index=2}:contentReference[oaicite:3]{index=3}
 */
const deleteListByAdminDisplay = async (): Promise<number> => {
  return get<number>(`/noticefeedback/deleteListByAdminDisplay`);
};

/**
 * 查询所有的通知反馈记录（分页/不分页）
 * POST /api/noticefeedback/getListAll :contentReference[oaicite:4]{index=4}:contentReference[oaicite:5]{index=5}
 */
const getListAll = async (
  search: NoticefeedbackSearch
): Promise<NoticeFeedback[]> => {
  return post<NoticefeedbackSearch, NoticeFeedback[]>(
    `/noticefeedback/getListAll`,
    search
  );
};

/**
 * 根据车主 ID 获取通知反馈列表
 * GET /api/noticefeedback/getListByPersonnelId/{personnelId} :contentReference[oaicite:6]{index=6}:contentReference[oaicite:7]{index=7}
 */
const getListByPersonnelId = async (
  personnelId: string
): Promise<NoticeFeedback[]> => {
  return get<NoticeFeedback[]>(
    `/noticefeedback/getListByPersonnelId/${personnelId}`
  );
};

/**
 * 根据 ID 删除某条通知反馈记录（管理员视图）
 * GET /api/noticefeedback/deleteByAdminDisplayAndId/{id} :contentReference[oaicite:8]{index=8}:contentReference[oaicite:9]{index=9}
 */
const deleteByAdminDisplayAndId = async (id: string): Promise<number> => {
  return get<number>(`/noticefeedback/deleteByAdminDisplayAndId/${id}`);
};

/**
 * 车主根据自身情况反馈挪车信息
 * POST /api/noticefeedback/feedBack :contentReference[oaicite:10]{index=10}:contentReference[oaicite:11]{index=11}
 */
const feedBack = async (data: NoticeFeedback): Promise<number> => {
  return post<NoticeFeedback, number>(`/noticefeedback/feedBack`, data);
};
/** 获取通知反馈 */
const getListTip = ()=>{
  return post<{},NoticeFeedback[]>('/noticefeedback/getListTip',{})
}
const saveFeedback = (data:NoticeFeedback) => {
  return post('/noticefeedback/feedBack',data)
};
export {
  save,
  deleteListByAdminDisplay,
  getListAll,
  getListByPersonnelId,
  deleteByAdminDisplayAndId,
  feedBack,
  getListTip,
  saveFeedback
};
