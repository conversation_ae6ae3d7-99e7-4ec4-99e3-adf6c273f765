// services/http.service.ts
// import { closeWebSockerMain } from "@/composables/useWebSocket";
import axios, { AxiosError, type AxiosRequestConfig } from "axios";
import { getToken, removeToken } from "./storage.service";
import router from "@/router";
// import { removeToken } from "./storage.service";
// const isEncrypt = import.meta.env.APP_X_ENCRYPTED === "true";
// 1. 定义后端的通用响应结构
interface ApiResponse<T = unknown> {
  rlt: number; // 0 表示成功
  datas?: T; // 成功时返回的数据
  info?: string; // 失败时的提示
}
const axiosInstance = axios.create({ baseURL: "/api" });
// 添加请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    const token = getToken();
    config.headers.Authorization = `Bearer ${token}`;
    config.headers["Content-Type"] = config.headers["Content-Type"] || "application/json"; // get请求会被忽略
    if (!config.responseType) config.responseType = "json";
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
// 添加响应拦截器
axiosInstance.interceptors.response.use(
  (response) => {
    const res = response.data;
    const responseType = response.config.responseType;
    // 响应非普通的返回 json格式，如 Blob
    if (responseType == "blob") {
      // 处理 blob 内容
      const contentDisposition = response.headers["content-disposition"];
      let filename = "downloaded_file";
      if (contentDisposition) {
        const matchUtf8 = contentDisposition.match(
          /filename\*=UTF-8''(.+?)(?:;|$)/
        );
        if (matchUtf8 && matchUtf8[1]) {
          filename = decodeURIComponent(matchUtf8[1]);
        } else {
          const matchNormal = contentDisposition.match(/filename="?([^"]+)"?/);
          if (matchNormal && matchNormal[1]) {
            filename = matchNormal[1];
          }
        }
      }
      // 防止blob是json的错误
      let blob = res;
      if (blob.type.includes("application/json")) {
        if (response.config.params?.type == "json") {
          return Promise.resolve({
            blob: new Blob([res], { type: "application/json" }),
            filename,
          });
        }
        return new Promise((_, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const json = JSON.parse(reader.result as string);
              reject(json.message || "下载失败");
            } catch (e) {
              reject("下载失败");
            }
          };
          reader.onerror = () => reject("下载失败");
          reader.readAsText(blob);
        });
      }
      // 正常返回文件
      return Promise.resolve({ blob, filename });
    } else {
      if (res && res.rlt === 0) {
        return Promise.resolve(res.datas);
      }
    }
    const msg = res.info || "请求失败";
    return Promise.reject(msg);
  },
  (error) => {
    if (axios.isAxiosError(error)) {
      const axiosErr = error as AxiosError<ApiResponse, any>;
      const msg =
        axiosErr.response?.data?.info ?? axiosErr.message ?? "请稍后再试";
      console.error("[AxiosError]", msg);
      switch (axiosErr.status) {
        case 401:
          // const isSuper = window.location.pathname.includes("super");
          removeToken();
          router.push({ name: "login" });
          break;
        default:
          break;
      }
      return Promise.reject(msg);
    }
    return Promise.reject("请求失败");
  }
);
// const handleError = (error: unknown): string => {
//   if (axios.isAxiosError(error)) {
//     const axiosErr = error as AxiosError<ApiResponse, any>;
//     const msg = axiosErr.response?.data?.info ?? axiosErr.message ?? '请稍后再试';
//     console.error('[AxiosError]', msg);
//     return msg;
//   }
//   console.error('[Unknown error]', error);
//   return '发生未知错误，请稍后再试';
// };

export const downloadRaw = async (
  url: string,
  onProgress?: (percent: number) => void
): Promise<{ blob: Blob; filename: string }> => {
  return axiosInstance.get(url, {
    responseType: "blob",
    onDownloadProgress: (evt) => {
      const percent = evt.total
        ? Math.round((evt.loaded * 100) / evt.total)
        : -1;
      onProgress?.(percent);
    },
  });
};

export const downloadJSON = async (
  url: string,
  param: any
): Promise<{ blob: Blob; filename: string }> => {
  return axiosInstance.post(url, param, {
    responseType: "blob",
    params: { type: "json" },
  });
};

export const downloadCSV = async (
  url: string,
  param: any
): Promise<{ blob: Blob; filename: string }> => {
  return axiosInstance.post(url, param, {
    responseType: "blob",
    params: { type: "json" },
  });
};

const get = async <T>(
  url: string,
  controller?: AbortController
): Promise<T> => {
  return axiosInstance.get(url, { signal: controller?.signal });
};
const post = async <T, S = any>(
  url: string,
  data: T,
  controller?: AbortController
): Promise<S> => {
  const headers: AxiosRequestConfig["headers"] = {};
  // if (isEncrypt) {
  //   const stringifyData = JSON.stringify(data, null, 2);
  //   data = { datas: await encryptData(stringifyData) };
  //   headers["X-Encrypted"] = "true";
  // }
  return axiosInstance.post(url, data, {
    headers,
    signal: controller?.signal,
  });
};
const postHeader = async <T, S = any>(
  url: string,
  data: T,
  config: AxiosRequestConfig
): Promise<S> => {
  return axiosInstance.post(url, data, config);
};
export { get, post, postHeader };
