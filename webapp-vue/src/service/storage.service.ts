const localStroageKey = "MT_STORAGE_KEY";
const set = (key: string, value: any) => {
  const storage = localStorage.getItem(localStroageKey);
  if (storage) {
    const storageObj = JSON.parse(storage);
    storageObj[key] = value;
    localStorage.setItem(localStroageKey, JSON.stringify(storageObj));
  } else {
    localStorage.setItem(localStroageKey, JSON.stringify({ [key]: value }));
  }
};
const get = (key: string) => {
  const storage = localStorage.getItem(localStroageKey);
  if (storage) {
    const storageObj = JSON.parse(storage);
    return storageObj[key];
  }
  return null;
};

const remove = (key: string) => {
  const storage = localStorage.getItem(localStroageKey);
  if (storage) {
    const storageObj = JSON.parse(storage);
    delete storageObj[key];
    localStorage.setItem(localStroageKey, JSON.stringify(storageObj));
  }
};

const clear = () => {
  localStorage.removeItem(localStroageKey);
};

const setToken = (token: string) => {
  remove("access_token");
  set("access_token", token);
};

const getToken = () => {
  return get("access_token");
};

const removeToken = () => {
  remove("access_token");
};

export { set, get, remove, clear, getToken, setToken, removeToken };
