import type { Account } from "../models/Account";
import { get, post } from "./http.service";

/**
 * @description 登录
 * @param username
 * @param password
 * @returns
 */
const login = async (
  username: string,
  password: string,
  type: "super" | "common"
) => {
  // const login = async (username: string, password: string) => {
  return post<
    { username: string; password: string; type: "super" | "common" },
    { accessToken: string }
  >("/auth/login", { username, password, type });
};

/**
 * @description 登出
 * @returns
 */
const logout = async () => {
  return get("/auth/logout");
};

/**
 * @description 获取账户信息
 * @returns
 */
const getAccount = async (): Promise<Account> => {
  return get("/auth/account");
};

/**
 * @description 获取公钥
 * @returns
 */
const getPublicKeyHex = async (): Promise<string> => {
  return get("/pkh");
};

/**
 * 获取日志下载的文件名称
 * @returns
 */
const getLogDownloadFile = async () => {
  return get<{ fileName: string }>("/log/getFileName");
};

/**
 * 获取配置下载的文件名称
 * @returns
 */
const getConfDownloadFile = async () => {
  return get<{ filename: string }>("/configuration/filename");
};

/**
 * web 服务升级
 * @param id
 * @returns
 */
const handleWebUpgrade = async (id: string) => {
  return get(`/upgrade/web/${id}`);
};

/**
 * main 主控服务升级
 * @param id
 * @returns
 */
const handleMainUpgrade = async (id: string) => {
  return get(`/upgrade/main/${id}`);
};

/** 恢复默认配置 */
const getResetConfig = async () => {
  return get("configuration/resetConf");
};
/** 是否支持远程升级 */
const getCheckUpgrade = async () => {
  return get<{ ifRemoteUpgrade: boolean }>(`/configuration/checkUpgrade`);
};

/**
 * @description API服务
 * @description 提供登录、登出、获取账户信息、获取公钥等功能
 * @description 主要用于与后端进行交互
 * @description 通过HTTP请求获取数据
 * <AUTHOR>
 * @date 2025-03-17 14:03
 */
export default {
  login, // 登录
  logout, // 登出
  getAccount, // 获取账户信息
  getPublicKeyHex, // 获取公钥
  getLogDownloadFile, // 获取日志下载的文件名称
  getConfDownloadFile, // 获取配置下载的文件名称
  handleWebUpgrade, //上传完成后，web服务升级流程
  handleMainUpgrade, // 主控服务升级
  /** 恢复默认配置 */
  getResetConfig,
  /** 是否支持远程升级 */
  getCheckUpgrade,
};
