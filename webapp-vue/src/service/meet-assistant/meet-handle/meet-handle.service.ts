import type { IWorkGroup, Meeting, Meetingpersonnel, Meetingroom, Personnel } from '@/models/MettingHandle';
import type { PageSearch } from '@/models/Page';
import type { PersonnelSearch } from '@/models/Personnel';
import { get, post } from '@/service/http.service';

/** 获取人员列表 */
const getListPersonnel = (search: PersonnelSearch) => {
  return post<PersonnelSearch, Personnel[]>('/Personnel/getList', search);
};
/** 获取可添加的会议参会人员列表 */
const getListWithoutOptions = () => {
  return get<{ department: string; members: Personnel[]; id: string; open: boolean }[]>('/Personnel/getMeetingAttendeeOptions');
};
/** 新增参会人员 */
const savePersonnel = (data: Personnel) => {
  return post('/Personnel/save', data);
};
/** 新增会议 */
const saveMeeting = (data: Meeting) => {
  return post('/Meeting/save', data);
};
/** 获取会议室房间列表 */
const getMeetRoomList = (search: PageSearch) => {
  return post<PageSearch, Meetingroom[]>('/Meetingroom/getList', search);
};
const getInfoMeeting = (id: string) => {
  return get<Meeting>(`/Meeting/getInfo/${id}`);
};
/** 取消已预约会议 */
const cancelMeeting = (id: string) => {
  return get(`/Meeting/cancelMeeting/${id}`);
};
/** 删除附件 */
const deleteContent = (id: string) => {
  return get(`/Content/delete/${id}`);
};
/** 预览附件 */
const previewContent = (id: string) => {
  return get(`/Content/preview/${id}`);
};
/** 智能推荐会议室 */
const recommendMeetingRoom = ({ startTime, endTime, attendeeCount }: { startTime: string; endTime: string; attendeeCount: number }) => {
  return get<Meetingroom>(`/Meetingroom/recommend?startTime=${startTime}&endTime=${endTime}&attendeeCount=${attendeeCount}`);
};
/** 判断当前会议是发起人还是参会人 */
const getCurrentMeetingPersonnel = (meetingId: string) => {
  return get<Meetingpersonnel>(`/Meetingpersonnel/getCurrentUserMeetingPersonnel/${meetingId}`);
};
const saveMeetingpersonnel = (data: Meetingpersonnel) => {
  return post('/Meetingpersonnel/save', data);
};
const deletePersonnel = (id: string) => {
  return get(`/Personnel/delete/${id}`);
};
/**
 * 保存机构部门
 * status 状态(0-禁用,1-启用)
 * */
const saveOrganization = (data: { name: string; status: number; personnelList: Personnel[] }) => {
  return post('/Organization/save', data);
};
/** 获取机构部门 */
const getListOrganization = (data: {
  currentPage?: number;
  ifPage?: boolean;
  /**
   * 是否加载人员列表
   */
  loadPersonnelList?: boolean;
  /**
   * 组织机构名称模糊匹配
   */
  nameLike?: string;
  pageRecord?: number;
  /**
   * 状态(0-禁用,1-启用)
   */
  status?: number;
}) => {
  return post('/Organization/getList', data);
};

/** ---工作组--- */
const saveWorkGoup = (data: IWorkGroup) => {
  return post('/Workgroup/save', data);
};
export default {
  getListPersonnel,
  getCurrentMeetingPersonnel,
  getListWithoutOptions,
  savePersonnel,
  saveMeeting,
  getMeetRoomList,
  previewContent,
  getInfoMeeting,
  deleteContent,
  cancelMeeting,
  recommendMeetingRoom,
  saveMeetingpersonnel,
  deletePersonnel,
  saveOrganization,
  getListOrganization,
  saveWorkGoup,
};
