import type { MeetReserved } from "@/models/MeetReserved1"

import { post,get } from '@/service/http.service';

const getListReserved = async (search: MeetReserved) => {
  return post<MeetReserved, any>('/Meeting/getList', search);
};
const getMeetingroom = async (search: MeetReserved) => {
  return post<MeetReserved, any>('/Meetingroom/getList', search);
};
const getCurrentPersonnel = async () => {
  return get('/Personnel/getCurrentPersonnel');
};
const batchDelete = async (search: Array<string>) => {
  return post<Array<string>, any>('/Meeting/batchDelete', search);
};

export default { getListReserved,getMeetingroom,getCurrentPersonnel,batchDelete };
