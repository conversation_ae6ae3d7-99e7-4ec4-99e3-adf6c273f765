<script lang="ts">
import {
  NConfigProvider,
  NMessageProvider,
  NModalProvider,
  NNotificationProvider,
  type GlobalThemeOverrides,
} from "naive-ui";
import { defineComponent, onMounted } from "vue";
// locale & dateLocale
import { dateZhCN, zhCN } from "naive-ui";

export default defineComponent({
  components: {
    NConfigProvider,
    NModalProvider,
    NMessageProvider,
    // NThemeEditor,
    // NDialogProvider,
    NNotificationProvider,
  },
  setup() {
    onMounted(async () => {
      await import('virtual:svg-icons-register')
    })
    // 明亮主题
    const lightThemeOverrides: GlobalThemeOverrides = {
      common: {
        primaryColor: "#0066DF",
        primaryColorHover: "#0066DF",
        primaryColorPressed: "#0066DF",
        primaryColorSuppl: "#0066DF",
        heightMedium: "40px",
      },
      Button: {
        textColor: "#0066DF",
        colorPrimary:'#0066DF',
        border: "1px solid #0066DF",
      },
      Checkbox: {
        fontSizeMedium: '15px',
      },
      Radio: {
        fontSizeMedium: '15px',
      },
      Modal: {
        padding: "0",
        common: {
          primaryColor: "#0066DF",
          primaryColorHover: "#0066DF",
          primaryColorPressed: "#0066DF",
          primaryColorSuppl: "#0066DF",
        },
        peers: {
          Dialog: {
            padding: "0",
          },
        },
      },
      Input: {
        borderHover: "1px solid #0066DF",
        borderFocus: "1px solid #0066DF",
        groupLabelBorder: "1px solid #0066DF",
        heightMedium: "40px",
      },
      DataTable: {
        thColor: "#F2F3F7",
        borderColor: "#E5E6EA",
      },
      Table: {
        thColor: "#F2F3F7",
        borderColor: "#E5E6EA",
      },
      Dropdown: {
        optionTextColor: "#fff",
        color: "#1C2340",
        optionColorHover: "#0066DF",
        optionTextColorHover: "#fff",
      },
      Dialog: {
        borderRadius: "8px",
        padding: "0",
      },
    };
    return {
      zhCN,
      dateZhCN,
      lightThemeOverrides,
    };
  },
});
</script>

<template>
  <n-config-provider inline-theme-disabled :locale="zhCN" :date-locale="dateZhCN" style="width: 100%; height: 100%"
    :theme-overrides="lightThemeOverrides">
    <n-modal-provider>
      <n-message-provider>
        <!-- 全局通知 -->
        <n-notification-provider placement="bottom-right">
          <router-view />
        </n-notification-provider>
      </n-message-provider>
    </n-modal-provider>
  </n-config-provider>
</template>
