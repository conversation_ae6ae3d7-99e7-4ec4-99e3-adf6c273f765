/** 格式化时间; return 格式为YYYY-MM-DD HH:mm:ss */
export const formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};
// 计算一小时前的时间
export const getOneHourAgo = () => {
  const now = new Date();
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
  return formatDate(oneHourAgo);
};

// 计算一天前的时间
export const getOneDayAgo = () => {
  const now = new Date();
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  return formatDate(oneDayAgo);
};
/**
 * 根据秒数计算时间
 * @returns {string} -h-m-s
 * */
export const convertSeconds = (seconds: number): string => {
  const secondsInDay = 24 * 60 * 60;
  const secondsInHour = 60 * 60;
  const secondsInMinute = 60;

  const days = Math.floor(seconds / secondsInDay);
  seconds %= secondsInDay;

  const hours = Math.floor(seconds / secondsInHour);
  seconds %= secondsInHour;

  const minutes = Math.floor(seconds / secondsInMinute);
  const remainingSeconds = seconds % secondsInMinute;

  let result = '';
  if (days > 0) {
    result += `${days}d`;
  }
  if (hours > 0) {
    result += `${hours}h`;
  }
  if (minutes > 0) {
    result += `${minutes}m`;
  }
  if (remainingSeconds > 0 || result === '') {
    result += `${remainingSeconds}s`;
  }

  return result;
};

/**
 * 格式化UTC时间
 * @param {string} utc -UTC时间 "2025-04-28_18:38:02.232"
 * @returns {string} -格式化后的时间 "2025-04-28 18:38:02"
 * */
export const formatUTC = (utc: string): string => {
  if (!utc) return '';
  const [date = '', time = ''] = utc.split('_');
  const [formatTime] = time.split('.');
  return `${date} ${formatTime}`;
};

/**
 * 格式化UTC时间
 * @param {string} utcMs - 毫秒数 1746686832791
 * @returns {string} -格式化后的本地时间 "2025-04-28 18:38:02"
 * */
export const formatLocalTime = (utcMs: number | null): string => {
  if (utcMs == null) return '';
  return new Date(utcMs)
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    })
    .replace(/\//g, '-')
    .replace(/\s+/, ' ');
};

/**
 * 将时间戳转成UTC字符串，只取时分秒
 * @param {string} time - 123456789
 * @returns {string} - HH:mm:ss
 * */
export const getUTCHoursMinutesSeconds = (time: number | null): string => {
  if (time == null) return '';
  const date = new Date(time);
  const isoString = date.toISOString(); // YYYY-MM-DDTHH:mm:ss.sssZ
  return isoString.slice(11, 19);
};
