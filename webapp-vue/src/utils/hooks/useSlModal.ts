import type { ModalOptions } from '@/components/BaseModal.vue';
import { cloneDeep } from '@/utils/clone';
import { defineAsyncComponent, h, onBeforeUnmount, ref, render, type Component, type VNode } from 'vue';
interface IModalInstance {
  destroy$: () => void;
}
export const useSlModal = () => {
  const vnode = ref<VNode | null>();
  const hostContainer = ref<HTMLDivElement>();
  let modalInstance: Component<ModalOptions> | null = null;
  const create = (newProps: ModalOptions): IModalInstance => {
    if (modalInstance == null) modalInstance = defineAsyncComponent(() => import('@/components/BaseModal.vue'));
    if (hostContainer.value == null) hostContainer.value = document.createElement('div');
    const props = cloneDeep(newProps);
    // const newMergeProps = mergeProps({ show: true, onCancelCallback: toDestroy, titleClass: '111' } as Record<string, any>, { ...props }) as ModalOptions;
    const cancelCallback = props.onCancel;
    props.show = true;
    props.onCancel = () => (cancelCallback?.(), _destroy());
    // h更友好的语法糖; createVNode偏底层
    vnode.value = h(modalInstance, props, {
      default: typeof props.content === 'function' ? props.content : () => props.content ?? null,
      footer: typeof props.footerSlot === 'function' ? props.footerSlot : () => props.footerSlot ?? null,
    });
    render(vnode.value, hostContainer.value);
    return {
      destroy$: props.onCancel,
    }; // return destroy function to out side
  };
  /** 提示框：统一的删除模态框 */
  const createDeleteModal = (onConfirmCallback: ModalOptions['onConfirmCallback'], content: string = '') => {
    create({
      title: '提示',
      isDialog: true,
      content: `确定要删除${content}吗？`,
      onConfirmCallback,
    });
  };
  const createConfirmModal = ({
    content,
    onConfirmCallback,
    title,
    footerSlot,
    ifCanClose,
    confirmText,
    onCancel,
  }: Pick<ModalOptions, 'onConfirmCallback' | 'content' | 'title' | 'footerSlot' | 'ifCanClose' | 'ifCanClose' | 'confirmText' | 'onCancel'> & { content: string }) => {
    create({
      title: title || '提示',
      isDialog: true,
      content,
      onConfirmCallback,
      footerSlot,
      ifCanClose,
      confirmText,
      onCancel,
    });
  };
  /** 仅确定的提示框，不带取消（如 用于保存后的提示） */
  const createConfirmModalOnly = ({ content, confirmText = '确定', onConfirmAfter = () => {} }: { content: string; confirmText?: string; onConfirmAfter?: () => void }) => {
    create({
      title: '提示',
      isDialog: true,
      content,
      onCancel: onConfirmAfter, // 关闭后回调
      footerSlot: () => {
        return h(
          'button',
          {
            type: 'button',
            class: 'btn-primary',
            onClick: () => {
              onConfirmAfter(), _destroy();
            },
          },
          confirmText
        );
      },
    });
  };
  const _destroy = () => {
    if (hostContainer.value) {
      render(null, hostContainer.value);
      hostContainer.value?.remove();
      hostContainer.value = vnode.value = undefined;
      modalInstance = null;
    }
  };
  const destroyAll = () => {
    _destroy;
  };
  onBeforeUnmount(() => {
    _destroy();
    modalInstance = null;
  });
  return { create, createDeleteModal, createConfirmModal, createConfirmModalOnly, destroyAll };
};
