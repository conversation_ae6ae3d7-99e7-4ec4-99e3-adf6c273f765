type Handler = (...args: any[]) => void;

class EventBus {
  private events: Record<string, Handler[]> = {};

  /** 订阅事件 */
  on(event: string, handler: Handler) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(handler);
  }

  /** 取消订阅 */
  off(event: string, handler?: Handler) {
    const handlers = this.events[event];
    if (!handlers) return;
    if (!handler) {
      // 全部移除
      delete this.events[event];
    } else {
      this.events[event] = handlers.filter((h) => h !== handler);
    }
  }

  /** 触发事件 */
  emit(event: string, ...args: any[]) {
    const handlers = this.events[event];
    if (!handlers) return;
    // 拷贝一份，避免中途修改数组导致遍历出错
    handlers.slice().forEach((h) => h(...args));
  }
}

export const eventBus = new EventBus();
