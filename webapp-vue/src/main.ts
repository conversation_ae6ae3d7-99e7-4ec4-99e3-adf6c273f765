import "lib-flexible/flexible.js";
import { createPinia } from 'pinia';
import { createApp } from 'vue';
import App from './App.vue';
// import { loadDirectives } from './directives';
import router from './router/index';
import './style.css';
const pinia = createPinia();
const app = createApp(App);
app.config.globalProperties.getEnv = import.meta.env;
/** 加载全局自定义指令 */
// loadDirectives(app);
app.use(pinia);
app.use(router);
app.mount('#app');
