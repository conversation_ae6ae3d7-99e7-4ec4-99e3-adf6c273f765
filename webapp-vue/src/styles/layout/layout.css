/* 布局容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* Main Content */
main {
  overflow-y: auto;
  flex: 1;
  background: var(--layout-content-bg);
}
main.car-move {
  background: linear-gradient(180deg, #035cc5 0%, #ffffff 31%);
  margin-top: -1px;
}
/* Footer */
footer {
  align-items: center;
  flex: 0 49px;
  height: 49px;
  display: flex;
  border-radius: 10px 10px 0px 0px;
  background: #ffffff;
  backdrop-filter: blur(20px);
  box-shadow: 0px -0.5px 6px 0px #5d5d5d33;
  width: 100%;
}

footer nav {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

footer nav a {
  display: inline-flex;
  flex-direction: column;
  /* 图标和文字有些紧凑，可以调整下间距 */
  row-gap: 2px;
  align-items: center;
  font-size: 10px;
}

footer nav a > span {
  color: #666666;
}

footer nav a.router-link-active span {
  color: #005ac5;
}
footer nav a.router-link-active .inactive {
  display: none;
}

footer nav a:not(.router-link-active) .active {
  display: none;
}
