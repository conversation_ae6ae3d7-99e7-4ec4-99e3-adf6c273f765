/* Header */
header.header {
  display: flex;
  position: relative;
  flex-direction: column;
}
header.header.banner {
  width: 100%;
  height: 310px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
header.header nav.navbar {
  height: 44px;
  background: var(--layout-header-bg);
  width: 100%;
  flex: 0 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  position: relative;
}
header.header nav.navbar.car-move {
  background: var(--layout-car-header-bg);
}
header.header nav.navbar span.title {
  font-size: 16px;
}
header.header nav.navbar .goback {
  position: absolute;
  left: 14.5px;
  color: #fff;
  font-size: 16px;
}
header.header nav.navbar .extra {
  position: absolute;
  right: 14.5px;
}

header.header nav.navbar .extra a + a {
  margin-left: 18px;
}
header.header nav.navbar .extra a.extra-btn {
  display: inline-flex;
  align-items: center;
}
