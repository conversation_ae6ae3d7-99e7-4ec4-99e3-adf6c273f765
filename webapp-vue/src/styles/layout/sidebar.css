/* Sidebar 样式 */
.sidebar {
  grid-area: sidebar;
  width: var(--mt--sidebar-width);
  background: var(--mt-sidebar-bg);
  display: flex;
  flex-direction: column;
  padding: 15px 0;
}

.sidebar a {
  text-decoration: none;
  color:  var(--mt-sidebar-text-color);
  background: var(--mt-sidebar-bg);
  font-size: 1rem;
  display: flex;
  align-items: center;
  border-radius: 5px;
  transition: background 0.3s;
}

.sidebar a i.icon {
  width: 18px;
  height: 18px;
  display: inline-block;
  border: 1px solid #fff;
  text-align: center;
  line-height: 18px;
  flex: 0 0 18px;
}

.sidebar a .text {
  flex: 1;
  padding-left: 10px;
}

/* 当前选中项高亮 */
.sidebar a.active {
  background-color: #666666;
}

/* 菜单 hover 效果 */
.sidebar a:hover {
  background-color: #4b5563;
}

/* 子菜单样式 */
.dropdown-menu {
  list-style: none;
  margin: 0;
  background: #3a3f4b;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}
.dropdown-menu li a {
  padding: 8px 12px 8px 20px;
  display: block;
  color: #fff;
  border-radius: 5px;
}

.dropdown-menu li a:hover {
  background-color: #555b6a;
}

/* caret 箭头 */
.caret {
  margin-left: auto;
  transition: transform 0.3s;
}

.caret.rotated {
  transform: rotate(180deg);
}
.menu a:hover{
  color:  var(--mt-sidebar-text-color);
  background: var(--mt-sidebar-bg);
}
