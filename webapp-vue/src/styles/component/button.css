button {
  border-radius: 3px;
  border: 1px solid #e0e0e6;
  height: 36px;
  cursor: pointer;
  transition: border-color 0.25s;
  /* min-width: 100px; */
  background-color: #fff;
  color: #333;
  padding: 0 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 14px !important;
  gap: 4px;
}
button:hover {
  border-color: #0066df;
  color: #0066df;
}
/* 两边圆弧的按钮样式 */
button.btn-rounded {
  border-radius: 20px;
  padding: 0 15px;
}
button.small {
  min-width: 80px;
}
button.medium {
  min-width: 140px;
}
/* primary背景 如 save-btn */
button.btn-primary {
  background-color: #0066df;
  color: #fff;
}
/* primary边框， */
button.btn-border-primary {
  border-color: #0066df;
  color: #0066df;
}
button.btn-border-primary:hover {
  background-color: #f1f5ff;
}
button.disabled {
  cursor: default;
  pointer-events: none;
  border-color: #e0e0e6;
  color: #333;
  background-color: #e0e0e6;
}
button.btn-del {
  border-color: #666;
  color: #666;
}
button.btn-del:hover {
  background-color: #ffeaea;
  color: #ea0000;
  border-color: #ea0000;
}

/* hover 底下 */
button i.hover {
  display: none;
}

button:hover i.alway {
  display: none;
}

button:hover i.hover {
  display: inline-block;
}
