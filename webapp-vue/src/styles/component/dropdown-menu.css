/* 一级菜单列表样式 */
ul.menu {
  list-style: none;
  padding-left: 0;
  margin: 0;
  font-size: 1rem;
}

/* 二级菜单 */
.dropdown-menu {
  list-style: none;
  padding-left: 0;
  margin: 0;
  background-color: #333;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
  border-left: 3px solid #4b5563;
}

/* 二级菜单项 */
.dropdown-menu li {
  margin: 5px 0;
}

.dropdown-menu li a {
  font-size: 0.875rem;
  padding: 8px 8px 8px 40px;
  display: flex;
  align-items: center;
  color: white;
  text-decoration: none;
  transition: background 0.3s, color 0.3s;
}

/* 子菜单 hover 效果 */
.dropdown-menu li a:hover {
  background-color: #444;
  color: #ddd;
}

/* 父级菜单项的展开状态 */
.dropdown.open {
  background-color: #999999;
}

/* 当展开时显示子菜单 */
.dropdown.open .dropdown-menu {
  max-height: 500px; /* 设定一个足够大的值，确保子菜单展开 */
  opacity: 1;
}

/* 默认箭头方向（向下） */
.dropdown-toggle .caret {
  margin-left: 5px;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #fff;
  transition: transform 0.3s ease-in-out;
}

/* 旋转箭头（向上） */
.dropdown.open .dropdown-toggle .caret {
  transform: rotate(180deg);
}
