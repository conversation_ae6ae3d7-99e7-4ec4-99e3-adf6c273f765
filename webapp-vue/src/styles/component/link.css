a {
  font-weight: 500;
  text-decoration: none;
}
.header-nav-text {
  position: relative;
  color: #4f7af6;
  font-size: 24px;
  width: 100%;
  padding-left: 1rem;
  margin: 8px 0;
  display: flex;
  align-items: center;
  font-weight: bold;
}

.header-nav-text::before {
  position: absolute;
  content: '';
  background: #4f7af6;
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: 10px;
  height: 10px;
}
.header-nav-right {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
  gap: 8px;
}
.header-nav-second-text {
  font-size: 20px;
  color: #333;
  font-weight: bold;
}
