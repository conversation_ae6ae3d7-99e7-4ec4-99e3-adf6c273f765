.modal {
  width: 560px;
}
.n-modal {
  min-width: 400px;
  padding: 14px;
  background-color: #f4f4f4;
}
.modal-wrapper {
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 0;
  display: flex;
  flex-direction: column;
}
.modal-footer_button {
  display: flex;
  align-items: center;
  margin: 4px 0;
  justify-content: space-around;
}
.modal-footer {
  height: 36px;
  line-height: 36px;
  margin-bottom: 28px;
  display: flex;
  justify-content: center;
  gap: 10%;
}
/* 模态框标题 */
.modal-header {
  height: 50px;
  background: #4f7af6;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 20px;
  padding-left: 30px;
  padding-right: 26px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
/* 模态框标题 文字部分 */
.modal-header-title {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
}
.modal-header-title.center{
  justify-content: center;
}
/* 模态框标题 */
.modal-footer {
  height: 36px;
  line-height: 36px;
  margin-bottom: 28px;
  display: flex;
  justify-content: center;
  gap: 10%;
}
