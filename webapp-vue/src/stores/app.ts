import { defineStore } from "pinia";
import apiService from "../service/api.service";
export const useAppStore = defineStore("appinfo", {
  state: () => ({
    publicKey: "",
  }),
  actions: {
    async getPk(): Promise<string> {
      if (this.publicKey) {
        return Promise.resolve(this.publicKey);
      }
      return apiService
        .getPublicKeyHex()
        .then((pk) => {
          this.publicKey = pk;
          return this.publicKey;
        })
        .catch(() => {
          this.publicKey = "";
          return "";
        });
    },
  },
});
