package com.shenlan.smartlogixmini.util

import okhttp3.Headers
import okhttp3.OkHttpClient
import org.springframework.stereotype.Component

@Component
object HttpUtil {
    val client = OkHttpClient()

    // get接口，返回body通过泛型进行序列化
    inline fun <reified T> getJsp(url: String, headers: Headers = Headers.of()): T {
        val request = okhttp3.Request.Builder()
            .url(url)
            .headers(headers)
            .build()
        val response = client.newCall(request).execute()
        val json = response.body()?.bytes()?.toString(Charsets.UTF_8)?.trim() ?: "{}"
        return customObjectMapper.readValue(json, T::class.java)
    }
}