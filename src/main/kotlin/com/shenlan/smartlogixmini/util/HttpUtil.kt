package com.shenlan.smartlogixmini.util

import com.fasterxml.jackson.core.type.TypeReference
import okhttp3.Headers
import okhttp3.MediaType
import okhttp3.OkHttpClient
import okhttp3.RequestBody
import org.springframework.stereotype.Component

@Component
object HttpUtil {
    val client = OkHttpClient()

    // get接口，返回body通过泛型进行序列化
    inline fun <reified T> getJsp(url: String, headers: Headers = Headers.of()): T {
        val request = okhttp3.Request.Builder()
            .url(url)
            .headers(headers)
            .build()
        val response = client.newCall(request).execute()
        val json = response.body()?.bytes()?.toString(Charsets.UTF_8)?.trim() ?: "{}"
        val type = object : TypeReference<T>() {}
        return customObjectMapper.readValue(json, type)
    }

    fun get(url: String, headers: Headers = Headers.of()): String {
        val request = okhttp3.Request.Builder()
            .url(url)
            .headers(headers)
            .build()
        val response = client.newCall(request).execute()
        return response.body()?.bytes()?.toString(Charsets.UTF_8)?.trim() ?: ""
    }

    // post接口，返回body通过泛型进行序列化
    inline fun <reified T> postJsp(url: String, json: String, headers: Headers = Headers.of()): T {
        val mediaType = MediaType.parse("application/json; charset=utf-8")
        val requestBody = RequestBody.create(mediaType, json)
        val request = okhttp3.Request.Builder()
            .url(url)
            .headers(headers)
            .post(requestBody)
            .build()
        val response = client.newCall(request).execute()
        val responseJson = response.body()?.bytes()?.toString(Charsets.UTF_8)?.trim() ?: "{}"
        val type = object : TypeReference<T>() {}
        return customObjectMapper.readValue(responseJson, type)
    }

    fun post(url: String, json: String, headers: Headers = Headers.of()): String {
        val mediaType = MediaType.parse("application/json; charset=utf-8")
        val requestBody = RequestBody.create(mediaType, json)
        val request = okhttp3.Request.Builder()
            .url(url)
            .headers(headers)
            .post(requestBody)
            .build()
        val response = client.newCall(request).execute()
        return response.body()?.bytes()?.toString(Charsets.UTF_8)?.trim() ?: ""
    }
}