package com.shenlan.smartlogixmini.util

import com.fasterxml.jackson.annotation.JsonProperty
import org.springframework.stereotype.Component
import javax.validation.Constraint
import javax.validation.ConstraintValidator
import javax.validation.ConstraintValidatorContext
import javax.validation.Payload
import kotlin.reflect.KClass

/**
 * 内容安全检测注解
 * 使用微信内容安全检测API对文本内容进行安全性验证
 */
@Target(AnnotationTarget.FIELD, AnnotationTarget.PROPERTY, AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [ContentSecurityValidator::class])
@MustBeDocumented
annotation class ContentSecurity(
    /** 错误消息 */
    val message: String = "内容包含违规信息",
    /** 分组验证(框架需要) */
    val groups: Array<KClass<*>> = [],
    /** 负载信息(框架需要) */
    val payload: Array<KClass<out Payload>> = [],
    /** 检测场景 */
    val scene: WechatMsgSecCheckUtil.Scene,
    /** 是否在异常时跳过验证 */
    val skipOnException: Boolean = true
)

/**
 * 内容安全检测验证器
 * 使用微信内容安全检测API对文本内容进行验证
 */
class ContentSecurityValidator(
    private val wechatMsgSecCheckUtil: WechatMsgSecCheckUtil
) : ConstraintValidator<ContentSecurity, String> {

    private lateinit var annotation: ContentSecurity

    override fun initialize(constraintAnnotation: ContentSecurity) {
        this.annotation = constraintAnnotation
    }

    override fun isValid(value: String?, context: ConstraintValidatorContext?): Boolean {
        log.info("ContentSecurityValidator.isValid called with value: $value")
        try {
            // 处理空值和空白字符串
            if (value.isNullOrBlank()) {
                return true
            }

            // 获取当前用户信息
            val currentUser = getUser()
            if (currentUser == null) {
                log.warn("Cannot get current user for content security check")
                return true
            }

            // 获取微信openid
            val openid = currentUser.wechatOpenId
            if (openid.isBlank()) {
                return true
            }

            // 调用微信内容安全检测API
            @Suppress("UNNECESSARY_NOT_NULL_ASSERTION")
            val response = wechatMsgSecCheckUtil.checkText(
                content = value!!,
                scene = annotation.scene,
                openid = openid
            )

            // 检查检测结果
            return when (response.result?.suggest) {
                WechatMsgSecCheckUtil.Suggest.PASS.value -> {
                    log.debug("Content security check passed for user: $openid")
                    true
                }
                WechatMsgSecCheckUtil.Suggest.REVIEW.value -> {
                    log.info("Content needs manual review for user: $openid, label: ${response.result.label}")
                    false
                }
                WechatMsgSecCheckUtil.Suggest.RISKY.value -> {
                    log.info("Content contains risky content for user: $openid, label: ${response.result.label}")
                    false
                }
                else -> {
                    log.warn("Unknown suggest result: ${response.result?.suggest} for user: $openid")
                    annotation.skipOnException
                }
            }
        } catch (e: Exception) {
            // 网络异常或其他错误
            log.error("Error during content security check", e)
            return annotation.skipOnException
        }
    }
}

/**
 * 微信文本内容安全识别工具类
 * 负责检查文本是否含有违法违规内容
 * 官方文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/sec-center/sec-check/msgSecCheck.html
 */
@Component
class WechatMsgSecCheckUtil(
    private val wechatAccessTokenUtil: WechatAccessTokenUtil
) {

    /** 场景枚举值 */
    enum class Scene(val value: Int) {
        PROFILE(1), // 资料
        COMMENT(2), // 评论
        FORUM(3), // 论坛
        SOCIAL_LOG(4) // 社交日志
    }

    /** 建议枚举值 */
    enum class Suggest(val value: String) {
        PASS("pass"), // 通过
        REVIEW("review"), // 需要人工审核
        RISKY("risky") // 风险内容
    }

    /** 标签枚举值 */
    enum class Label(val value: Int) {
        NORMAL(100), // 正常
        AD(10001), // 广告
        POLITICS(20001), // 时政
        PORN(20002), // 色情
        ABUSE(20003), // 辱骂
        ILLEGAL(20006), // 违法犯罪
        FRAUD(20008), // 欺诈
        VULGAR(20012), // 低俗
        COPYRIGHT(20013), // 版权
        OTHER(21000) // 其他
    }

    /**
     * 文本安全检测请求数据类
     */
    data class MsgSecCheckRequest(
        @JsonProperty("content")
        val content: String,
        @JsonProperty("version")
        val version: Int = 2,
        @JsonProperty("scene")
        val scene: Int,
        @JsonProperty("openid")
        val openid: String,
        @JsonProperty("title")
        val title: String? = null,
        @JsonProperty("nickname")
        val nickname: String? = null,
        @JsonProperty("signature")
        val signature: String? = null
    )

    /**
     * 详细检测结果
     */
    data class DetailResult(
        @JsonProperty("strategy")
        val strategy: String,
        @JsonProperty("errcode")
        val errcode: Int,
        @JsonProperty("suggest")
        val suggest: String? = null,
        @JsonProperty("label")
        val label: Int? = null,
        @JsonProperty("prob")
        val prob: Int? = null,
        @JsonProperty("keyword")
        val keyword: String? = null
    )

    /**
     * 综合结果
     */
    data class ResultInfo(
        @JsonProperty("suggest")
        val suggest: String,
        @JsonProperty("label")
        val label: Int
    )

    /**
     * 文本安全检测响应数据类
     */
    data class MsgSecCheckResponse(
        @JsonProperty("errcode")
        val errcode: Int,
        @JsonProperty("errmsg")
        val errmsg: String,
        @JsonProperty("detail")
        val detail: List<DetailResult>? = null,
        @JsonProperty("trace_id")
        val traceId: String? = null,
        @JsonProperty("result")
        val result: ResultInfo? = null
    )

    /**
     * 检测文本内容安全性
     *
     * @param content 需检测的文本内容，文本字数的上限为2500字
     * @param scene 场景枚举值
     * @param openid 用户的openid（用户需在近两小时访问过小程序）
     * @param title 文本标题（可选）
     * @param nickname 用户昵称（可选）
     * @param signature 个性签名（可选，仅在资料类场景有效）
     * @return 检测结果，包含是否通过和详细信息
     * @throws RuntimeException 当检测失败时抛出异常
     */
    fun checkText(
        content: String,
        scene: Scene,
        openid: String,
        title: String = "",
        nickname: String = "",
        signature: String = ""
    ): MsgSecCheckResponse {
        try {
            // 参数验证
            if (content.isBlank()) {
                throw IllegalArgumentException("content cannot be blank")
            }
            if (content.length > 2500) {
                throw IllegalArgumentException("content length cannot exceed 2500 characters")
            }
            if (openid.isBlank()) {
                throw IllegalArgumentException("openid cannot be blank")
            }

            log.info("Checking text content security for user: $openid, scene: $scene")

            // 获取access_token
            val accessToken = wechatAccessTokenUtil.getAccessToken()

            // 构建请求URL
            val url = "https://api.weixin.qq.com/wxa/msg_sec_check?access_token=$accessToken"

            // 构建请求体
            val request = MsgSecCheckRequest(
                content = content,
                version = 2,
                scene = scene.value,
                openid = openid,
                title = if (title.isBlank()) null else title,
                nickname = if (nickname.isBlank()) null else nickname,
                signature = if (signature.isBlank()) null else signature
            )

            // 转换为JSON
            val requestJson = customObjectMapper.writeValueAsString(request)
            log.debug("Msg sec check request: $requestJson")

            // 发送HTTP请求
            val response = HttpUtil.postJsp<MsgSecCheckResponse>(url, requestJson)

            // 检查响应结果
            if (response.errcode == 0) {
                log.info("Text content security check completed successfully for user: $openid, result: ${response.result?.suggest}")
                return response
            } else {
                val errorMsg = "Failed to check text content security: ${response.errcode} - ${response.errmsg}"
                log.error(errorMsg)
                throw RuntimeException(errorMsg)
            }

        } catch (e: Exception) {
            log.error("Error checking text content security for user: $openid", e)
            throw RuntimeException("Failed to check text content security", e)
        }
    }
}
