package com.shenlan.smartlogixmini.util

import com.fasterxml.jackson.annotation.JsonProperty
import org.springframework.stereotype.Component

/**
 * 微信小程序scheme码生成工具
 * 用于生成可以拉起小程序的URL scheme，适用于短信、邮件、外部网页等场景
 * 官方文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/url-scheme/generateScheme.html
 */
@Component
class WechatSchemeUtil(
    private val wechatAccessTokenUtil: WechatAccessTokenUtil
) {

    /**
     * 跳转小程序配置
     */
    data class JumpWxa(
        /** 通过scheme码进入的小程序页面路径，必须是已发布的小程序存在的页面，不可携带query */
        @JsonProperty("path")
        val path: String? = null,
        /** 通过scheme码进入小程序时的query，最大1024个字符 */
        @JsonProperty("query")
        val query: String? = null,
        /** 要打开的小程序版本：release-正式版，trial-体验版，develop-开发版 */
        @JsonProperty("env_version")
        val envVersion: String = "release"
    )

    /**
     * 生成scheme请求参数
     */
    data class GenerateSchemeRequest(
        /** 跳转到的目标小程序信息 */
        @JsonProperty("jump_wxa")
        val jumpWxa: JumpWxa? = null,
        /** 是否设置过期时间 */
        @JsonProperty("is_expire")
        val isExpire: Boolean = false,
        /** 过期失效的scheme码失效时间，Unix时间戳 */
        @JsonProperty("expire_time")
        val expireTime: Long? = null,
        /** 失效类型：0-失效时间，1-失效间隔天数 */
        @JsonProperty("expire_type")
        val expireType: Int = 0,
        /** 失效间隔天数，最长30天 */
        @JsonProperty("expire_interval")
        val expireInterval: Int? = null
    )

    /**
     * 生成scheme响应结果
     */
    data class GenerateSchemeResponse(
        /** 错误码 */
        val errcode: Int = 0,
        /** 错误信息 */
        val errmsg: String = "",
        /** 生成的小程序scheme码 */
        val openlink: String? = null
    )

    /**
     * 生成小程序scheme码
     * 官方文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/url-scheme/generateScheme.html
     *
     * @param path 小程序页面路径
     * @param query 页面参数
     * @param envVersion 小程序版本，默认正式版
     * @param expireTime 过期时间戳（可选）
     * @param expireInterval 过期间隔天数（可选）
     * @return 生成的scheme码
     * @throws RuntimeException 当生成失败时抛出异常
     */
    fun generateScheme(
        path: String? = null,
        query: String? = null, 
        envVersion: String = "release",
        expireTime: Long? = null,
        expireInterval: Int? = null
    ): String {
        val accessToken = wechatAccessTokenUtil.getAccessToken()
        val url = "https://api.weixin.qq.com/wxa/generatescheme?access_token=$accessToken"

        // 构建请求参数
        val jumpWxa = JumpWxa(
            path = path,
            query = query,
            envVersion = envVersion
        )

        val request = GenerateSchemeRequest(
            jumpWxa = jumpWxa,
            isExpire = expireTime != null || expireInterval != null,
            expireTime = expireTime,
            expireType = if (expireInterval != null) 1 else 0,
            expireInterval = expireInterval
        )

        try {
            val requestJson = customObjectMapper.writeValueAsString(request)
            log.info("Generating WeChat scheme with params: path=$path, query=$query, envVersion=$envVersion")

            val response = HttpUtil.postJsp<GenerateSchemeResponse>(url, requestJson)

            // 检查响应结果
            if (response.errcode != 0) {
                val errorMsg = "Failed to generate WeChat scheme: ${response.errcode} - ${response.errmsg}"
                log.error(errorMsg)
                throw RuntimeException(errorMsg)
            }

            if (response.openlink.isNullOrEmpty()) {
                val errorMsg = "WeChat scheme generation returned empty openlink"
                log.error(errorMsg)
                throw RuntimeException(errorMsg)
            }

            log.info("WeChat scheme generated successfully")
            @Suppress("UNNECESSARY_NOT_NULL_ASSERTION")
            return response.openlink!!

        } catch (e: Exception) {
            log.error("Error generating WeChat scheme", e)
            throw RuntimeException("Failed to generate WeChat scheme", e)
        }
    }
}