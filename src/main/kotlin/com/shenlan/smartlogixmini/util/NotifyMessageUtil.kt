package com.shenlan.smartlogixmini.util

import com.shenlan.smartlogixmini.auto.Notifymessage
import com.shenlan.smartlogixmini.auto.NotifymessageMapper
import com.shenlan.smartlogixmini.auto.Notifyusermessage
import com.shenlan.smartlogixmini.auto.NotifyusermessageMapper

object NotifyMessageUtil {
    /**
     * 根据用户id列表、消息module、type保存推送消息
     */
    fun saveNotifyMessage(userIds: List<String>, module: String, type: String, title: String, content: String, extra: String) {
        // 保存通知信息
        val notifyMessage = Notifymessage().apply {
            id = uuid()
            this.module = module
            this.type = type
            this.title = title
            this.content = content
            this.extra = extra
        }
        getBean(NotifymessageMapper::class.java).insert(notifyMessage)

        // 生成通知
        getBean(NotifyusermessageMapper::class.java).insertList(userIds.map { userId ->
            Notifyusermessage().apply {
                id = uuid()
                this.userId = userId
                messageId = notifyMessage.id
                pushStatus = 0
            }
        })
    }
}