package com.shenlan.smartlogixmini.util

import com.shenlan.smartlogixmini.config.AppPro
import java.nio.charset.Charset
import java.util.*
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.spec.SecretKeySpec

object AesUtil {
    const val PARAMS = "params"
    // 算法
    private const val ALGORITHMSTR = "AES/ECB/PKCS5Padding"
    // 密钥
    private const val AES_KEY = "bWFaeHB9ZA==WNST"

    /**
     * aes 解密
     *
     * @param encryptStr
     * @return
     */
    fun aesDecrypt(encryptStr: String): String? {
        return try {
            if (encryptStr.isEmpty()) {
                null
            } else {
                val keyGenerator = KeyGenerator.getInstance("AES")
                keyGenerator.init(128)

                val cipher = Cipher.getInstance(ALGORITHMSTR)
                cipher.init(Cipher.DECRYPT_MODE, SecretKeySpec(AES_KEY.toByteArray(), "AES"))

                val encryptBytes = Base64.getDecoder().decode(encryptStr)
                val decryptBytes = cipher.doFinal(encryptBytes)
                String(decryptBytes, Charset.forName("utf-8"))
            }
        } catch (e: Exception) {
            encryptStr
        }
    }

    /**
     * aes 加密
     *
     * @param content
     * @return
     */
    fun aesEncrypt(content: String): String {
        return if (AppPro.aes) {
            try {
                val kgen = KeyGenerator.getInstance("AES")
                kgen.init(128)

                val cipher = Cipher.getInstance(ALGORITHMSTR)
                cipher.init(Cipher.ENCRYPT_MODE, SecretKeySpec(AES_KEY.toByteArray(), "AES"))

                val encryptBytes = cipher.doFinal(content.toByteArray(charset("utf-8")))
                Base64.getEncoder().encodeToString(encryptBytes)
            } catch (e: Exception) {
                e.printStackTrace()
                ""
            }
        } else {
            content
        }
    }
}
