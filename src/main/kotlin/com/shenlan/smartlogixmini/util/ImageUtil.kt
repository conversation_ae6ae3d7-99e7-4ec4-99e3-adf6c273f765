package com.shenlan.smartlogixmini.util

import com.shenlan.smartlogixmini.auto.Content
import com.shenlan.smartlogixmini.auto.ContentMapper
import com.shenlan.smartlogixmini.auto.Dish
import com.shenlan.smartlogixmini.auto.Dishimage
import com.shenlan.smartlogixmini.auto.DishimageMapper
import com.shenlan.smartlogixmini.auto.DishimageService
import com.shenlan.smartlogixmini.auto.ImageType
import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.config.OpenaiConfig
import com.zhipu.oapi.ClientV4
import com.zhipu.oapi.Constants
import com.zhipu.oapi.service.v4.image.CreateImageRequest
import org.springframework.stereotype.Component
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL

@Component
object ImageUtil {
    var client: ClientV4? = null

    data class SimpleImageApiResponse(
        val code: Int,
        val data: SimpleImageApiResponseData,
        val msg: String,
        val success: Boolean
    )

    data class SimpleImageApiResponseData(
        val data: List<SimpleApiImage>
    )

    data class SimpleApiImage(
        val url: String
    )

    init {
        client = ClientV4.Builder(OpenaiConfig.zhipuApiSecretKey).build()
    }

    fun dishNameToImage(dishName: String, dishId: String): String {
        val createImageRequest: CreateImageRequest = CreateImageRequest().apply {
            prompt = """
                美食摄影，一道叫做“${dishName}”的菜，摆盘精致，色香俱全
            """.trimIndent()
//            model = Constants.ModelCogView
            model = "cogview-4"
        }
        var imageId = ""

        try {
            client?.createImage(createImageRequest)?.let { imageApiResponse ->
                imageApiResponse.data.data.take(1).forEach {
                    log.info("$dishName ai image url: ${it.url}")
                    imageId = downloadImage(it.url, dishId)
                }
            }
        } catch (e: Exception) {
            log.error("Failed to create image: ${e.message}")
        }

        return imageId
    }

    fun downloadImage(url: String, dishId: String): String {
        val dishImage = Dishimage(dishId, "", ImageType.AI.toString())
        val dir = AppPro.uploadDir + File.separator + "dishimage" + File.separator + "ai"

        File(System.getProperty("user.dir") + File.separator + dir).apply {
            if (!this.exists())
                this.mkdirs()
        }


        val fileName = System.currentTimeMillis().toString() + "." + url.substringAfterLast(".")

        dishImage.imageUrl = (dir + File.separator + fileName).replace("\\", "/")


        val url = URL(url)
        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = "GET"
        connection.connectTimeout = 5000
        connection.readTimeout = 5000

        if (connection.responseCode == HttpURLConnection.HTTP_OK) {
            val inputStream: InputStream = connection.inputStream
            val file = File(System.getProperty("user.dir") + File.separator + dir + File.separator + fileName)
            val outputStream = FileOutputStream(file)

            inputStream.use { input ->
                outputStream.use { output ->
                    input.copyTo(output)
                }
            }

            dishImage.imageUrl = (dir + File.separator + fileName).replace("\\", "/")

            log.info("图片下载完成: $url")
        } else {
            log.info("下载失败，响应码: ${connection.responseCode}")
        }

        connection.disconnect()

        return getBean(DishimageService::class.java).save(dishImage).datas as String
    }

    fun syncContent(image: Dishimage) {
        try {
            val context = Content()
            val imageUrl = image.imageUrl.replace("/", File.separator)

            context.id = uuid()
            context.storeName = imageUrl.substringAfterLast(File.separator)
            context.storeDir = imageUrl.substringAfter(AppPro.uploadDir + File.separator).substringBeforeLast(File.separator + context.storeName)
            context.fileType = "." + context.storeName.substringAfterLast(".")
            context.fileSize = File( System.getProperty("user.dir") + File.separator + imageUrl).length().toInt()

            getBean(ContentMapper::class.java).insert(context)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun updateDishImages(dish: Dish) {
        getBean(DishimageMapper::class.java).clearDishId(dish.id)
        updateImageDishId(dish.imageId, dish.id)
        dish.images.forEach { image ->
            updateImageDishId(image.id, dish.id)
        }
    }

    fun updateImageDishId(id: String, dishId: String) {
        try {
            getBean(DishimageMapper::class.java).updateDishId(id, dishId)
        } catch (e: Exception) {
            log.error("更换关联菜品失败id: $id, dishId: $dishId, ${e.message}")
        }
    }
}