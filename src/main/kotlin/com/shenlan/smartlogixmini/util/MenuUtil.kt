package com.shenlan.smartlogixmini.util

import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters

object MenuUtil {
    /**
     * 获取菜谱日期列表
     */
    fun getMenuDateList(localDate: LocalDate = LocalDate.now()): List<String> {
        val sunday = getLatestMenuSunday(localDate).minusWeeks(1)

        // 返回从周一到周五的MenuDate
        return List(5) { sunday.plusDays(it.toLong() + 1).format(localDateFormatter) }
    }

    /**
     * 获取最新一周的周日
     */
    fun getLatestMenuSunday(localDate: LocalDate = LocalDate.now()): LocalDate = localDate.plusDays(2).with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY)).plusWeeks(1)
}