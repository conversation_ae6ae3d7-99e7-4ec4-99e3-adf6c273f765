package com.shenlan.smartlogixmini.util

import com.shenlan.smartlogixmini.auto.Dish
import com.shenlan.smartlogixmini.auto.DishMapper
import com.shenlan.smartlogixmini.auto.DishSearch
import com.shenlan.smartlogixmini.auto.MealTime
import com.shenlan.smartlogixmini.auto.Takeoutdish
import com.shenlan.smartlogixmini.auto.TakeoutdishMapper
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters

object MenuUtil {
    /**
     * 获取菜谱日期列表
     */
    fun getMenuDateList(localDate: LocalDate = LocalDate.now()): List<String> {
        val sunday = getLatestMenuSunday(localDate).minusWeeks(1)
        val list = List(7) { sunday.plusDays(it.toLong() + 1) }

        // 返回从周一到周五的MenuDate
        return List(5) { sunday.plusDays(it.toLong() + 1).format(localDateFormatter) }
    }

    /**
     * 获取最新一周的周日
     */
    fun getLatestMenuSunday(localDate: LocalDate = LocalDate.now()): LocalDate = localDate.plusDays(2).with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY)).plusWeeks(1)


    // 同步菜品库
    fun syncTakeoutDish(takeDish: Takeoutdish) {
        val mapper = getBean(TakeoutdishMapper::class.java)
        mapper.delete(takeDish.id)
        mapper.insert(takeDish)
    }

    fun syncDish(takeDish: Takeoutdish) {
        val mapper = getBean(DishMapper::class.java)
        val dish = mapper.getInfo(takeDish.id)
        if (dish == null) return
        mapper.update(dish.apply {
            name = takeDish.name
            price = takeDish.price
            portionUnit = takeDish.portionUnit
            limitPerPerson = takeDish.limitPerPerson
            imageId = takeDish.imageId
        })
    }

    /**
     * 校验菜品名称是否重复
     */
    fun checkDishNameExists(dish: Any): String {
        val dishMapper = getBean(DishMapper::class.java)
        val takeoutDishMapper = getBean(TakeoutdishMapper::class.java)

        when (dish) {
            is Dish -> {
                // 菜谱菜品存在同类菜谱菜谱同名、外卖菜品同名两种情况
                val dishes = dishMapper.getList(DishSearch().apply { name = dish.name }).filter { it.id != dish.id }
                val dishNameExisted = dishes.any { it.name == dish.name && it.timePeriod == dish.timePeriod }

                if (dish.synced) {
                    val takeoutDishes = takeoutDishMapper.getList(DishSearch().apply { name = dish.name }).filter { it.id != dish.id }
                    val takeoutDishNameExisted = takeoutDishes.any { it.name == dish.name }

                    if (dishNameExisted && takeoutDishNameExisted)
                        return "均已存在同名菜品"

                    if (takeoutDishNameExisted)
                        return "已存在同名订餐菜品"
                }

                if (dishNameExisted)
                    return "已存在同名早餐/午餐菜品"
            }
            is Takeoutdish -> {
                val takeoutDishes = takeoutDishMapper.getList(DishSearch().apply { name = dish.name }).filter { it.id != dish.id }
                val takeoutDishNameExisted = takeoutDishes.any { it.name == dish.name }
                val syncedDish = dishMapper.getInfo(dish.id)

                if (syncedDish != null) {
                    val dishes = dishMapper.getList(DishSearch().apply { name = dish.name }).filter { it.id != dish.id }
                    val dishNameExisted = dishes.any { it.name == dish.name && it.timePeriod == syncedDish.timePeriod }

                    if (dishNameExisted && takeoutDishNameExisted)
                        return "均已存在同名菜品"

                    if (dishNameExisted)
                        return "已存在同名早餐/午餐菜品"
                }

                if (takeoutDishNameExisted)
                    return "已存在同名订餐菜品"
            }
        }

        return ""
    }
}