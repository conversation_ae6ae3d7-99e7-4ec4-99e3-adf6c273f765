package com.shenlan.smartlogixmini.util

import com.aliyun.dyvmsapi20170525.Client
import com.aliyun.dyvmsapi20170525.models.*
import com.aliyun.teaopenapi.models.Config
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import javax.annotation.PostConstruct
import javax.annotation.PreDestroy

/**
 * 阿里云语音通话工具类
 */
@Component
class AliyunVmsUtil {

    @Value("\${aliyun.vms.enabled:false}")
    private var vmsEnabled: Boolean = false

    @Value("\${aliyun.vms.access-key-id:}")
    private var accessKeyId: String = ""

    @Value("\${aliyun.vms.access-key-secret:}")
    private var accessKeySecret: String = ""

    @Value("\${aliyun.vms.endpoint:dyvmsapi.aliyuncs.com}")
    private var endpoint: String = "dyvmsapi.aliyuncs.com"

    @Value("\${aliyun.vms.called-show-number:}")
    private var defaultCalledShowNumber: String = ""

    private var client: Client? = null

    @PostConstruct
    fun init() {
        if (vmsEnabled && accessKeyId.isNotEmpty() && accessKeySecret.isNotEmpty()) {
            try {
                log.info("Initializing Aliyun Vms client")
                val config = Config()
                    .setAccessKeyId(accessKeyId)
                    .setAccessKeySecret(accessKeySecret)
                config.endpoint = endpoint
                client = Client(config)
                log.info("Aliyun Vms client initialized successfully")
            } catch (e: Exception) {
                log.error("Failed to initialize Aliyun Vms client: ${e.message}")
                errorlog(e)
            }
        } else {
            log.info("Aliyun Vms service is disabled or missing required configuration")
        }
    }

    @PreDestroy
    fun destroy() {
        log.info("Aliyun Vms client closed successfully")
    }

    /**
     * 检查通话服务是否可用并验证基本参数
     *
     * @param calledNumber 被叫号码
     * @param codeParam 模板代码（TTS模板ID或IVR模板ID）
     * @param calledShowNumber 主叫号码
     * @return 如果检查通过则返回true，否则返回false
     */
    private fun checkCallServiceAvailable(
        calledNumber: String,
        codeParam: String,
        calledShowNumber: String
    ): Boolean {
        if (!vmsEnabled || client == null) {
            log.warn("Vms service is disabled or client not initialized, call not sent to $calledNumber")
            return false
        }
        if (calledShowNumber.isEmpty()) {
            log.error("CalledShowNumber is empty, call not sent to $calledNumber")
            return false
        }
        if (codeParam.isEmpty()) {
            log.error("Code parameter is empty, call not sent to $calledNumber")
            return false
        }
        return true
    }

    /**
     * 统一呼叫方法，根据menuKeyMapList是否为空决定使用IVR呼叫或TTS呼叫
     *
     * @param calledNumber 被叫号码
     * @param code 呼叫模板代码（当menuKeyMapList为空时作为ttsCode使用，否则作为startCode使用）
     * @param ttsParams TTS参数（JSON字符串，当menuKeyMapList为空时作为ttsParam使用，否则作为startTtsParams使用）
     * @param calledShowNumber 主叫号码（可选，默认取配置）
     * @param menuKeyMapList 菜单按键TTS配置列表（可选，为空时使用TTS呼叫，否则使用IVR呼叫）
     * @return Pair<是否成功, callId>
     */
    fun call(
        calledNumber: String,
        code: String,
        ttsParams: String,
        calledShowNumber: String = defaultCalledShowNumber,
        menuKeyMapList: List<Map<String, Any>> = emptyList()
    ): Pair<Boolean, String> {
        if (!checkCallServiceAvailable(calledNumber, code, calledShowNumber)) {
            return false to ""
        }

        return if (menuKeyMapList.isEmpty()) {
            // 使用TTS呼叫
            singleCallByTts(calledNumber, code, ttsParams, calledShowNumber)
        } else {
            // 使用IVR呼叫
            ivrCall(calledNumber, code, calledShowNumber, ttsParams, menuKeyMapList)
        }
    }

    /**
     * 发起交互式语音通话（支持TTS参数和菜单按键配置）
     *
     * @param calledNumber 被叫号码
     * @param startCode 呼叫开始时播放的提示音（模板ID）
     * @param calledShowNumber 主叫号码（可选，默认取配置）
     * @param startTtsParams 开始TTS参数（JSON字符串，可选）
     * @param menuKeyMapList 菜单按键TTS配置列表（可选）
     * @return Pair<是否成功, callId>
     */
    fun ivrCall(
        calledNumber: String,
        startCode: String,
        calledShowNumber: String = defaultCalledShowNumber,
        startTtsParams: String? = null,
        menuKeyMapList: List<Map<String, Any>>
    ): Pair<Boolean, String> {
        if (!checkCallServiceAvailable(calledNumber, startCode, calledShowNumber)) {
            return false to ""
        }

        if (menuKeyMapList.isEmpty()) {
            log.error("menuKeyMapList cannot be empty, call not sent to $calledNumber")
            return false to ""
        }

        try {
            log.info("Sending IVR call to $calledNumber with startCode $startCode")
            val request = IvrCallRequest()
                .setCalledShowNumber(calledShowNumber)
                .setCalledNumber(calledNumber)
                .setStartCode(startCode)
            if (!startTtsParams.isNullOrBlank()) {
                request.startTtsParams = startTtsParams
            }

            // 将 List<Map<String, Any>> 转换为 List<IvrCallRequest.IvrCallRequestMenuKeyMap>
            val keyMapList = menuKeyMapList.map { map ->
                val keyMap = IvrCallRequest.IvrCallRequestMenuKeyMap()
                map["key"]?.let { keyMap.key = it.toString() }
                map["ttsParams"]?.let { keyMap.ttsParams = it.toString() }
                map["code"]?.let { keyMap.code = it.toString() }
                keyMap
            }
            request.menuKeyMap = keyMapList

            // 打印request对象的JSON
            log.info("IVR call request JSON: ${request.toJsonString}")

            val response: IvrCallResponse = client!!.ivrCall(request)
            val isSuccess = "OK" == response.body.code
            val callId = response.body.callId ?: ""
            if (isSuccess) {
                log.info("IVR call sent successfully to $calledNumber, callId: $callId")
            } else {
                log.error("Failed to send IVR call to $calledNumber, code: ${response.body.code}, message: ${response.body.message}")
            }
            return isSuccess to callId
        } catch (e: Exception) {
            log.error("Error sending IVR call to $calledNumber: ${e.message}")
            errorlog(e)
            return false to ""
        }
    }

    /**
     * 发送TTS语音通知
     *
     * @param calledNumber 被叫号码
     * @param ttsCode TTS模板ID
     * @param ttsParam TTS参数（JSON字符串）
     * @param calledShowNumber 主叫号码（可选，默认取配置）
     * @return Pair<是否成功, callId>
     */
    fun singleCallByTts(
        calledNumber: String,
        ttsCode: String,
        ttsParam: String,
        calledShowNumber: String = defaultCalledShowNumber
    ): Pair<Boolean, String> {
        if (!checkCallServiceAvailable(calledNumber, ttsCode, calledShowNumber)) {
            return false to ""
        }

        try {
            log.info("Sending TTS call to $calledNumber with ttsCode $ttsCode")
            val request = SingleCallByTtsRequest()
                .setCalledShowNumber(calledShowNumber)
                .setCalledNumber(calledNumber)
                .setTtsCode(ttsCode)
                .setTtsParam(ttsParam)

            log.info("TTS call request: CalledNumber=$calledNumber, TtsCode=$ttsCode, TtsParam=$ttsParam")

            val response: SingleCallByTtsResponse = client!!.singleCallByTts(request)
            val isSuccess = "OK" == response.body.code
            val callId = response.body.callId ?: ""
            if (isSuccess) {
                log.info("TTS call sent successfully to $calledNumber, callId: $callId")
            } else {
                log.error("Failed to send TTS call to $calledNumber, code: ${response.body.code}, message: ${response.body.message}")
            }
            return isSuccess to callId
        } catch (e: Exception) {
            log.error("Error sending TTS call to $calledNumber: ${e.message}")
            errorlog(e)
            return false to ""
        }
    }

    /**
     * 查询通话详情
     *
     * @param callId 通话ID
     * @param queryTime 查询时间（Unix时间戳，毫秒）
     * @return 通话详情响应对象，失败返回null
     */
    fun queryCallDetailByCallId(
        callId: String,
        queryTime: Long
    ): QueryCallDetailByCallIdResponse? {
        if (!vmsEnabled || client == null) {
            log.warn("Vms service is disabled or client not initialized, cannot query call detail for $callId")
            return null
        }
        try {
            log.info("Querying call detail for callId $callId at $queryTime")
            val request = QueryCallDetailByCallIdRequest()
                .setCallId(callId)
                .setProdId(11000000300006L)
                .setQueryDate(queryTime)
            val response = client!!.queryCallDetailByCallId(request)
            log.info("Query call detail success for callId $callId")
            return response
        } catch (e: Exception) {
            log.error("Error querying call detail for callId $callId: ${e.message}")
            errorlog(e)
            return null
        }
    }
}
