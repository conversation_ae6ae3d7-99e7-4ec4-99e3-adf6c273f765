package com.shenlan.smartlogixmini.util

import com.alibaba.fastjson.JSON
import com.aliyun.dysmsapi20170525.Client
import com.aliyun.dysmsapi20170525.models.SendSmsRequest
import com.aliyun.dysmsapi20170525.models.SendSmsResponse
import com.aliyun.teaopenapi.models.Config
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import javax.annotation.PostConstruct
import javax.annotation.PreDestroy

/**
 * 阿里云短信服务工具类
 */
@Component
class AliyunSmsUtil {

    @Value("\${aliyun.sms.enabled:false}")
    private var smsEnabled: Boolean = false

    @Value("\${aliyun.sms.access-key-id:}")
    private var accessKeyId: String = ""

    @Value("\${aliyun.sms.access-key-secret:}")
    private var accessKeySecret: String = ""

    @Value("\${aliyun.sms.endpoint:dysmsapi.aliyuncs.com}")
    private var endpoint: String = "dysmsapi.aliyuncs.com"

    @Value("\${aliyun.sms.sign-name:智慧后勤}")
    private var defaultSignName: String = "智慧后勤"

    private var client: Client? = null

    @PostConstruct
    fun init() {
        if (smsEnabled && accessKeyId.isNotEmpty() && accessKeySecret.isNotEmpty()) {
            try {
                log.info("Initializing Aliyun SMS client")
                // 配置客户端
                val config = Config()
                    .setAccessKeyId(accessKeyId)
                    .setAccessKeySecret(accessKeySecret)
                config.endpoint = endpoint

                // 创建客户端实例
                client = Client(config)
                log.info("Aliyun SMS client initialized successfully")
            } catch (e: Exception) {
                log.error("Failed to initialize Aliyun SMS client: ${e.message}")
                errorlog(e)
            }
        } else {
            log.info("Aliyun SMS service is disabled or missing required configuration")
        }
    }

    @PreDestroy
    fun destroy() {
        log.info("Aliyun SMS client closed successfully")
    }

    /**
     * 发送短信
     *
     * @param phoneNumber 手机号码
     * @param params 短信模板参数，JSON格式字符串
     * @param templateCode 短信模板ID
     * @param signName 短信签名，不传则使用默认签名
     * @return 发送结果，成功返回 true，失败返回 false
     */
    fun sendSms(
        phoneNumber: String,
        params: Map<String, String>,
        templateCode: String,
        signName: String = defaultSignName
    ): Boolean {
        if (!smsEnabled || client == null) {
            log.warn("SMS service is disabled or client not initialized, SMS not sent to $phoneNumber")
            return false
        }

        if (templateCode.isEmpty()) {
            log.error("Template code is empty, SMS not sent to $phoneNumber")
            return false
        }

        try {
            log.info("Sending SMS to $phoneNumber with template $templateCode")

            // 将参数转换为JSON字符串
            val paramsJson = JSON.toJSONString(params)

            // 创建短信发送请求
            val request = SendSmsRequest()
                .setPhoneNumbers(phoneNumber)
                .setSignName(signName)
                .setTemplateCode(templateCode)
                .setTemplateParam(paramsJson)
                
            // 打印request对象的JSON
            log.info("SMS request JSON: ${JSON.toJSONString(request)}")

            // 发送短信
            val response: SendSmsResponse = client!!.sendSms(request)

            // 判断是否发送成功
            val isSuccess = "OK" == response.body.code
            if (isSuccess) {
                log.info("SMS sent successfully to $phoneNumber, bizId: ${response.body.bizId}")
            } else {
                log.error("Failed to send SMS to $phoneNumber, code: ${response.body.code}, message: ${response.body.message}")
            }

            return isSuccess
        } catch (e: Exception) {
            log.error("Error sending SMS to $phoneNumber: ${e.message}")
            errorlog(e)
            return false
        }
    }

    /**
     * 发送短信并返回BizId
     *
     * @param phoneNumber 手机号码
     * @param params 短信模板参数，JSON格式字符串
     * @param templateCode 短信模板ID
     * @param signName 短信签名，不传则使用默认签名
     * @return Pair<发送结果, BizId>，成功时BizId为回执ID，失败为null
     */
    fun sendSmsWithBizId(
        phoneNumber: String,
        params: Map<String, String>,
        templateCode: String,
        signName: String = defaultSignName
    ): Pair<Boolean, String> {
        if (!smsEnabled || client == null) {
            log.warn("SMS service is disabled or client not initialized, SMS not sent to $phoneNumber")
            return false to ""
        }

        if (templateCode.isEmpty()) {
            log.error("Template code is empty, SMS not sent to $phoneNumber")
            return false to ""
        }

        try {
            log.info("Sending SMS to $phoneNumber with template $templateCode")

            // 将参数转换为JSON字符串
            val paramsJson = JSON.toJSONString(params)

            // 创建短信发送请求
            val request = SendSmsRequest()
                .setPhoneNumbers(phoneNumber)
                .setSignName(signName)
                .setTemplateCode(templateCode)
                .setTemplateParam(paramsJson)
                
            // 打印request对象的JSON
            log.info("SMS request JSON: ${JSON.toJSONString(request)}")

            // 发送短信
            val response: SendSmsResponse = client!!.sendSms(request)

            // 判断是否发送成功
            val isSuccess = "OK" == response.body.code
            val bizId = response.body.bizId ?: ""
            if (isSuccess) {
                log.info("SMS sent successfully to $phoneNumber, bizId: $bizId")
            } else {
                log.error("Failed to send SMS to $phoneNumber, code: ${response.body.code}, message: ${response.body.message}")
            }

            return isSuccess to bizId
        } catch (e: Exception) {
            log.error("Error sending SMS to $phoneNumber: ${e.message}")
            errorlog(e)
            return false to ""
        }
    }
}
