package com.shenlan.smartlogixmini.util

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.github.promeg.pinyinhelper.Pinyin
import com.shenlan.smartlogixmini.auto.BaseModel
import com.shenlan.smartlogixmini.auto.Personnel
import com.shenlan.smartlogixmini.config.AppUser
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationContext
import org.springframework.context.ApplicationContextAware
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Service
import java.io.*
import java.text.SimpleDateFormat
import java.time.format.DateTimeFormatter
import java.util.*


val customObjectMapper = ObjectMapper().apply {
    configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
    configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
    setSerializationInclusion(JsonInclude.Include.NON_NULL)
    dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm")
    registerModule(JavaTimeModule())
}

val Any.log: Logger
    get() = LoggerFactory.getLogger(this.javaClass)

val Any.toJsonString: String
    get() = customObjectMapper.writeValueAsString(this)

fun <T> String?.toObject(cla: Class<T>): T = customObjectMapper.readValue(this.toString(), cla)

fun Any?.println() {
    println(this)
}

fun Any?.pj() {
    if (this == null) return
    println(this.toJsonString)
}

fun Any?.fpj() {
    if (this == null) return
    customObjectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(this).println()
}

fun String?.notEmpty(): Boolean {
    return !this.isNullOrEmpty()
}

/**
 * 当字符串为空时返回提供的默认值，否则返回原字符串
 */
fun String?.ifEmpty(defaultValue: () -> String): String {
    return if (this == null || this.isEmpty()) defaultValue() else this
}

fun String?.firstLower(): String {
    if (this != null) {
        return this[0].toLowerCase() + this.substring(1 until this.length)
    }
    return ""
}

fun String?.firstUpper(): String {
    if (this != null) {
        return this[0].toUpperCase() + this.substring(1 until this.length)
    }
    return ""
}

//返回sql 中的 in 格式串
fun List<String>?.getInStr(): String {
    if (this.isNullOrEmpty()) {
        return ""
    }
    return "'" + this!!.joinToString("','") + "'"
}

val BaseModel.tableName: String
    get() = "TBL_" + this::class.simpleName?.toUpperCase()

fun <T> List<T>?.notempty(): Boolean {
    if (this == null) return false
    if (this.isEmpty()) return false
    return true
}

fun <T> List<T>?.isNullOrEmpty(): Boolean {
    if (this == null) return true
    if (this.isEmpty()) return true
    return false
}

fun uuid(): String {
    return UUID.randomUUID().toString().replace("-", "")
}

fun errorlog(e: Throwable) {
    val trace = StringWriter()
    e.printStackTrace(PrintWriter(trace))
    "".log.error(trace.toString())
}

fun <T> getBean(requiredType: Class<T>): T {
    return SpringContext.context.getBean(requiredType)
}

@Service
class SpringContext : ApplicationContextAware {
    override fun setApplicationContext(applicationContext: ApplicationContext) {
        context = applicationContext
    }

    companion object {
        lateinit var context: ApplicationContext
    }
}

fun getUser(): Personnel? {
    return (SecurityContextHolder.getContext().authentication.principal as? AppUser)?.personnel
}

val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
val simpleDateFormat2 = SimpleDateFormat("yyyy-MM-dd")
fun strToDate(str: String): Date = SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(str)
fun intToDate(date: Int) = Date(date * 1000L)
fun intToStr(date: Int) = dateFormat(intToDate(date))
fun dateFormat(date: Date): String = SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date)
fun dateFormat2(date: Date): String = SimpleDateFormat("yyyy-MM-dd").format(date)
fun dateToInt(date: Date) = (date.time / 1000L).toInt()

/**
 * 深拷贝对象
 * 使用Jackson进行序列化和反序列化，比Java原生序列化更安全、更高效
 *
 * @param obj 要克隆的对象
 * @return 深拷贝后的对象，如果失败则抛出异常
 */
inline fun <reified T> deepClone(obj: T): T {
    try {
        // 先序列化为JSON字符串
        val jsonString = customObjectMapper.writeValueAsString(obj)
        // 再反序列化为对象
        return customObjectMapper.readValue(jsonString, T::class.java)
    } catch (e: Exception) {
        "".log.error("Failed to deep clone object of type ${T::class.simpleName}: ${e.message}")
        errorlog(e)
        throw IllegalStateException("Deep clone failed for object of type ${T::class.simpleName}", e)
    }
}

/**
 * 提取姓名的首字母
 * 支持中英文混合，英文字符转为大写，中文字符提取拼音首字母
 *
 * @param text 输入的文本（通常是姓名）
 * @return 姓名首字母字符串，全部大写
 */
fun getChineseFirstLetters(text: String): String {
    if (text.isEmpty()) {
        return ""
    }

    val result = StringBuilder()

    for (char in text) {
        when {
            // 中文字符
            Pinyin.isChinese(char) -> {
                try {
                    // 使用tinypinyin获取拼音首字母
                    val pinyin = Pinyin.toPinyin(char)
                    if (pinyin.isNotEmpty()) {
                        result.append(pinyin[0].toUpperCase())
                    } else {
                        // 如果无法转换，保留原字符
                        result.append(char)
                    }
                } catch (e: Exception) {
                    // 转换失败时保留原字符
                    result.append(char)
                }
            }
            // 英文字符
            char.isLetter() -> {
                result.append(char.toUpperCase())
            }
            // 数字保留
            char.isDigit() -> {
                result.append(char)
            }
            // 其他字符忽略
        }
    }

    return result.toString()
}

/**
 * 检查数组是否为null或空
 */
fun <T> Array<T>?.isNullOrEmpty(): Boolean {
    if (this == null) {
        return true
    }
    if (this.isEmpty()) {
        return true
    }
    return false
}

val localDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd")
