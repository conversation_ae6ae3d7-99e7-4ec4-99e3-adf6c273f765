package com.shenlan.smartlogixmini.util

import kotlin.math.abs

class QrCodeUtil {
    companion object {
        private val B = arrayOf(
            intArrayOf(-2, -3, -1, 1, 1, 3),
            intArrayOf(2, 3, 0, -1, -1, -2),
            intArrayOf(2, 2, 1, -1, -1, -2),
            intArrayOf(-1, -1, 0, 1, 0, 1),
            intArrayOf(1, 1, 0, 0, 0, -1),
            intArrayOf(-1, -1, 0, 0, 1, 1)
        )

        fun decode(encode: IntArray): String {
            val decode = IntArray(6)
            if (encode.size != 6) {
                throw IllegalArgumentException("The encode length must be 6!!!")
            }

            for (i in 0 until 6) {
                for (j in 0 until 6) {
                    decode[i] += encode[j] * B[i][j]
                }
                decode[i] = abs(decode[i]) % 100
            }

            return String.format(
                "20%02d-%02d-%02d %02d:%02d:%02d",
                decode[0], decode[1], decode[2], decode[3], decode[4], decode[5]
            )
        }

        fun decode(text: String): String {
            if (!text.contains('@')) {
                throw IllegalArgumentException("The text must be include @!!!")
            }
            val texts = text.split("@")
            val arr = texts[1].chunked(2)
            val encode = IntArray(arr.size)
            for (i in arr.indices) {
                encode[i] = arr[i].toInt(16)
            }

            return texts[0] + '@' + decode(encode)
        }
    }
}