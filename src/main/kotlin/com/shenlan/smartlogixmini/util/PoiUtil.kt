package com.shenlan.smartlogixmini.util

import org.apache.poi.ss.usermodel.*
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.io.ByteArrayOutputStream
import java.lang.reflect.Field
import java.util.*
import kotlin.reflect.KClass
import kotlin.reflect.KProperty1
import kotlin.reflect.full.memberProperties

/**
 * Excel列注解，支持自定义转换器
 */
@Target(AnnotationTarget.FIELD)
annotation class ExcelColumn(
    val name: String = "",               // 表头名称
    val order: Int = Int.MAX_VALUE,      // 列排序
    val dateFormat: String = "",         // 日期格式化
    val numberFormat: String = "",       // 数字格式化(未实现)
    val width: Int = -1,                 // 列宽(未实现)
    val converter: KClass<out ExcelValueConverter<*>> = DefaultConverter::class // 值转换器，在PoiClass里定义
)

object PoiUtil {
    private const val HEADER_ROW_HEIGHT = 20f
    private const val MIN_COLUMN_WIDTH = 10
    private const val MAX_COLUMN_WIDTH = 120
    private const val CHINESE_CHAR_WIDTH = 2.4 // 中文字符宽度系数
    private const val ENGLISH_CHAR_WIDTH = 1.2 // 英文字符宽度系数

    /**
     * list转为excel，注解形式
     * @param data 要转换的数据列表
     * @param excludeColumns 要排除的列
     * @param sheetName 页表名字
     * @param customConverters 自定义转换器(通常用注解不用这个)
     */
    fun <T : Any> exportExcel(
        data: List<T>,
        excludeColumns: Set<String> = emptySet(),  
        sheetName: String = "Sheet1",
        customConverters: Map<String, ExcelValueConverter<*>> = emptyMap()
    ): ByteArray {
        if (data.isEmpty()) {
            throw IllegalArgumentException("数据列表不能为空")
        }

        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet(sheetName)

        // 获取类的所有字段及其注解信息
        val fields = getExportFields(data.first()::class.java,excludeColumns)

        val styles = createStyles(workbook)

        // 写入表头并返回每列最大宽度
        val headerWidths = writeHeader(sheet, fields, styles.headerStyle)

        // 写入数据并更新列宽
        val dataWidths = writeData(sheet, data, fields, styles.dataStyle, customConverters)

        // 设置最终列宽
        setOptimalColumnWidths(sheet, headerWidths, dataWidths)

        return workbook.use { wb ->
            ByteArrayOutputStream().use { os ->
                wb.write(os)
                os.toByteArray()
            }
        }
    }

    private fun getExportFields(clazz: Class<*>,excludeColumns: Set<String>): List<Field> {
        return clazz.declaredFields
            .filter { it.isAnnotationPresent(ExcelColumn::class.java) }
            .filter { field -> field.name !in excludeColumns }  // 排除不需要导出的列
            .sortedBy { it.getAnnotation(ExcelColumn::class.java)?.order }
            .onEach { it.isAccessible = true }
    }
    
    private fun createStyles(workbook: XSSFWorkbook): ExcelStyles {
        // 表头样式
        val headerStyle = workbook.createCellStyle().apply {
            setFillForegroundColor(IndexedColors.GREY_25_PERCENT.index)
            fillPattern = FillPatternType.SOLID_FOREGROUND
            alignment = HorizontalAlignment.CENTER
            verticalAlignment = VerticalAlignment.CENTER

            // 设置边框
            borderTop = BorderStyle.THIN
            borderBottom = BorderStyle.THIN
            borderLeft = BorderStyle.THIN
            borderRight = BorderStyle.THIN

            // 自动换行
            wrapText = true
        }

        // 数据样式
        val dataStyle = workbook.createCellStyle().apply {
            alignment = HorizontalAlignment.LEFT
            verticalAlignment = VerticalAlignment.CENTER
            wrapText = true
        }

        return ExcelStyles(headerStyle, dataStyle)
    }

    private fun writeHeader(
        sheet: Sheet,
        fields: List<Field>,
        headerStyle: CellStyle
    ): List<Int> {
        val headerRow = sheet.createRow(0).apply {
            heightInPoints = HEADER_ROW_HEIGHT
        }

        return fields.mapIndexed { index, field ->
            val column = field.getAnnotation(ExcelColumn::class.java)
            val headerName = column?.name?.takeIf { it.isNotEmpty() } ?: field.name

            headerRow.createCell(index).apply {
                setCellValue(headerName)
                cellStyle = headerStyle
            }

            // 返回表头文字长度
            headerName.length * 2
        }
    }

    private fun writeData(
        sheet: Sheet,
        data: List<Any>,
        fields: List<Field>,
        dataStyle: CellStyle,
        customConverters: Map<String, ExcelValueConverter<*>>
    ): List<List<Int>> {
        val columnWidths = List(fields.size) { mutableListOf<Int>() }

        data.forEachIndexed { rowIndex, item ->
            val row = sheet.createRow(rowIndex + 1)
            fields.forEachIndexed { colIndex, field ->
                val value = getPropertyValue(item, field)
                val converter = getFieldConverter(field, customConverters)
                val cellValue = converter.convert(value)

                row.createCell(colIndex).apply {
                    setCellValue(cellValue)
                    cellStyle = dataStyle
                }

                columnWidths[colIndex].add(calculateOptimalWidth(cellValue))
            }
        }

        return columnWidths
    }

    private fun getPropertyValue(item: Any, field: Field): Any? {
        return try {
            // 使用 KCallable<*> 而不是具体的属性类型
            val property = item::class.memberProperties
                .find { it.name == field.name } as? KProperty1<Any, *>
            // 如果找到属性，调用get
            if (property != null) {
                property.get(item)
            } else {
                // 降级到直接字段访问
                field.get(item)
            }
        } catch (e: Exception) {
            // 如果获取失败，返回null并打印错误信息
            e.printStackTrace()
            null
        }
    }

    @Suppress("UNCHECKED_CAST")
    private fun getFieldConverter(
        field: Field,
        customConverters: Map<String, ExcelValueConverter<*>>
    ): ExcelValueConverter<Any> {
        // 优先使用自定义转换器
        customConverters[field.name]?.let { return it as ExcelValueConverter<Any> }

        // 使用注解指定的转换器
        val column = field.getAnnotation(ExcelColumn::class.java)
        return try {
            when {
                field.type == Date::class.java -> {
                    val dateFormat = column?.dateFormat?.takeIf { it.isNotEmpty() } ?: "yyyy-MM-dd HH:mm:ss"
                    DateConverter(dateFormat) as ExcelValueConverter<Any>
                }
                else -> {
                    column?.converter?.java?.getDeclaredConstructor()?.newInstance()
                            as? ExcelValueConverter<Any>
                        ?: DefaultConverter()
                }
            }
        } catch (e: Exception) {
            DefaultConverter()
        }
    }

    private fun setOptimalColumnWidths(
        sheet: Sheet,
        headerWidths: List<Int>,
        dataWidths: List<List<Int>>
    ) {
        headerWidths.forEachIndexed { index, headerWidth ->
            // 获取数据列最大宽度
            val maxDataWidth = dataWidths[index].maxBy { it } ?: 0

            // 计算最优宽度（考虑表头和数据）
            val optimalWidth = maxOf(
                calculateOptimalWidth(sheet.getRow(0).getCell(index).stringCellValue),
                maxDataWidth
            )

            sheet.setColumnWidth(index, optimalWidth * 256)
        }
    }

    private fun calculateOptimalWidth(text: String): Int {
        var width = 0.0

        text.forEach { char ->
            width += if (isChinese(char)) {
                CHINESE_CHAR_WIDTH  // 中文字符
            } else {
                ENGLISH_CHAR_WIDTH  // 英文字符
            }
        }

        return width.toInt().coerceIn(MIN_COLUMN_WIDTH, MAX_COLUMN_WIDTH)
    }

    // 判断是否为中文字符
    private fun isChinese(c: Char): Boolean {
        val ub = Character.UnicodeBlock.of(c)
        return ub === Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS ||
                ub === Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS ||
                ub === Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A ||
                ub === Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B ||
                ub === Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION ||
                ub === Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS ||
                ub === Character.UnicodeBlock.GENERAL_PUNCTUATION
    }

    private data class ExcelStyles(
        val headerStyle: CellStyle,
        val dataStyle: CellStyle
    )
}