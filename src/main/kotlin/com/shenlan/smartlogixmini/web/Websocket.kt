package com.shenlan.smartlogixmini.web

import com.shenlan.smartlogixmini.auto.CommuteVehicleStatus
import com.shenlan.smartlogixmini.auto.CommutevehicleMapper
import com.shenlan.smartlogixmini.auto.CommutevehicleSearch
import com.shenlan.smartlogixmini.auto.SimpleCommuteVehicle
import com.shenlan.smartlogixmini.util.errorlog
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.localDateTimeFormatter
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import com.shenlan.smartlogixmini.util.toObject
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.util.concurrent.CopyOnWriteArraySet
import javax.websocket.*
import javax.websocket.server.ServerEndpoint

/**
 * Created by Administrator on 2021/9/6.
 */
object WebSocketManager {
    //concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
    var webSocketSet = CopyOnWriteArraySet<WebSocket>()

    fun add(websocket: WebSocket) {
        webSocketSet.add(websocket)
    }

    fun remove(websocket: WebSocket) {
        webSocketSet.remove(websocket)
    }

    fun size(): Int {
        return webSocketSet.size
    }
}

class WebsocketModel {
    var type = "0"//0 数据流的推送
    var filterString = ""//过滤类型
}

class WebsocketInfo {
    var type: Int = 0
    val typeCn: String
        get() {
            return when(type) {
                0 -> "班车位置信息"
                else -> "未知"
            }
        }
    var date = ""
    var data: Any? = null

    constructor()

    constructor(type: Int, date: String, data: Any?) {
        this.type = type
        this.date = date
        this.data = data
    }
}

enum class WebSocketType (val value: Int) {
    COMMUTE_VEHICLE_LOCATION(0)     // 通勤班车位置信息
}

@ServerEndpoint(value = "/websocket-smartlogixmini")
@Component
class WebSocket {
    // 与某个客户端的连接会话，需要通过它来给客户端发送数据
    private var session: Session? = null
    var model: WebsocketModel = WebsocketModel()

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    fun onOpen(session: Session) {
        this.session = session
        WebSocketManager.add(this)

        // 新连接加入立即发送班车位置信息
        val vehicles = getBean(CommutevehicleMapper::class.java).getList(CommutevehicleSearch().apply {
            status = CommuteVehicleStatus.ONLINE.value
        })
        session.basicRemote.sendText(WebsocketInfo(WebSocketType.COMMUTE_VEHICLE_LOCATION.value, localDateTimeFormatter.format(
            LocalDateTime.now()), vehicles.map {
            SimpleCommuteVehicle(it.id, it.carName, it.routeId, it.lat, it.lng)
        }).toJsonString)

        log.info("有新连接加入！当前在线人数为:{}", WebSocketManager.size())
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    fun onClose() {
        WebSocketManager.remove(this)
        log.info("有一连接关闭！当前在线人数为:{}", WebSocketManager.size())
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message
     * 客户端发送过来的消息
     */
    @OnMessage
    fun onMessage(message: String, session: Session) {
        try {
//            model = message.toObject(WebsocketModel::class.java)
            log.info("receiveMessage: $message")
//            when (model.type.toInt()) {
//                0 -> {
//
//                }
//            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 发生错误时调用
     */
    @OnError
    fun onError(session: Session, error: Throwable) {
        errorlog(error)
    }

    fun sendMessage(data: Any) {
        session!!.basicRemote.sendText(data.toJsonString)
    }

    companion object {
        fun sendInfo(data: Any) {
            for (websocket in WebSocketManager.webSocketSet) {
                websocket.sendMessage(data)
            }
        }
    }
}