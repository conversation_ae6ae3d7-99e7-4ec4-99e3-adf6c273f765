package com.shenlan.smartlogixmini.web

import com.shenlan.smartlogixmini.auto.Result
import org.springframework.dao.DataAccessException
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.http.converter.HttpMessageConversionException
import org.springframework.jdbc.BadSqlGrammarException
import org.springframework.security.access.AccessDeniedException
import org.springframework.security.core.AuthenticationException
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.MissingServletRequestParameterException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException
import javax.validation.ConstraintViolationException

@ControllerAdvice
class ExceptionTranslator {

    @ExceptionHandler(MethodArgumentNotValidException::class)
    @ResponseBody
    fun processValidationError(ex: MethodArgumentNotValidException): Result {
        val list = ex.bindingResult.fieldErrors.map { it.defaultMessage }.distinct()
        return Result.getError(list.subList(0, if (list.size > 5) 5 else list.size).joinToString("<br/>"))
    }

    @ExceptionHandler(ConstraintViolationException::class)
    @ResponseBody
    fun processConstraintViolationException(ex: ConstraintViolationException): Result {
        val messages = ex.constraintViolations.map { it.message }.distinct()
        return Result.getError(messages.take(5).joinToString("<br/>"))
    }

    @ExceptionHandler(HttpMessageConversionException::class)
    @ResponseBody
    fun processHttpMessageConversionException(ex: HttpMessageConversionException): Result {
        return Result.getError("数据格式错误!")
    }

    @ExceptionHandler(BadSqlGrammarException::class)
    @ResponseBody
    fun processBadSqlGrammarException(ex: BadSqlGrammarException): Result {
        return Result.getError("数据查询异常，请检查查询条件!")
    }

    @ExceptionHandler(DataIntegrityViolationException::class)
    @ResponseBody
    fun processDataIntegrityViolationException(ex: DataIntegrityViolationException): Result {
        return Result.getError("数据完整性约束异常，请检查输入数据!")
    }

    @ExceptionHandler(DataAccessException::class)
    @ResponseBody
    fun processDataAccessException(ex: DataAccessException): Result {
        return Result.getError("数据访问异常!")
    }

    @ExceptionHandler(AccessDeniedException::class)
    @ResponseBody
    fun processAccessDeniedException(ex: AccessDeniedException): Result {
        return Result.getError("访问被拒绝，权限不足!")
    }

    @ExceptionHandler(AuthenticationException::class)
    @ResponseBody
    fun processAuthenticationException(ex: AuthenticationException): Result {
        return Result.getError("认证失败，请重新登录!")
    }

    @ExceptionHandler(MissingServletRequestParameterException::class)
    @ResponseBody
    fun processMissingServletRequestParameterException(ex: MissingServletRequestParameterException): Result {
        return Result.getError("缺少必要的请求参数: " + ex.parameterName)
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException::class)
    @ResponseBody
    fun processMethodArgumentTypeMismatchException(ex: MethodArgumentTypeMismatchException): Result {
        return Result.getError("请求参数类型不匹配: " + ex.name)
    }

    @ExceptionHandler(Exception::class)
    @ResponseBody
    fun processException(ex: Exception): Result {
        return Result.getError("系统异常!")
    }
}
