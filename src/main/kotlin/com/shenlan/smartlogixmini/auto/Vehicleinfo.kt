package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * 车辆信息实体类
 */
class Vehicleinfo : BaseModel() {
    /** 关联车主id */
    var personnelId: String = ""

    /** 车牌号 */
    var licensePlateNumber: String = ""


}

/**
 * 车辆信息查询条件类
 */
class VehicleinfoSearch : BaseSearch() {
    /** 车主id */
    var personnelId: String = ""

    /** 车牌号 */
    var licensePlateNumber: String = ""

    /** 车牌号模糊查询 */
    var keyword: String = ""
}

/**
 * 车辆信息Mapper接口
 */
@Mapper
interface VehicleinfoMapper : BaseMapper<Vehicleinfo> {
    /**
     * 根据车牌号查询车主id（一个车牌号只对应一个车主信息）
     */
    @Select("SELECT personnelId FROM tbl_vehicleinfo WHERE licensePlateNumber=#{licensePlateNumber} AND sysDeleted = 0")
    fun getPersonnelIdByLicensePlateNumber(licensePlateNumber: String): String

    /**
     * 根据车牌号模糊查询车主id集合
     */
    @Select("SELECT personnelId FROM tbl_vehicleinfo WHERE licensePlateNumber LIKE CONCAT('%', #{licensePlateNumber}, '%') AND sysDeleted = 0")
    fun getPersonnelIdsByLicensePlateNumber(licensePlateNumber: String): List<String>

    /**
     * 根据查询条件获取车辆列表
     */
    @Select(
        """
        <script>
            SELECT * FROM tbl_vehicleinfo 
            <where>
                sysDeleted = 0
                <if test="personnelId != ''">
                    AND personnelId = #{personnelId}
                </if>
                <if test="licensePlateNumber != ''">
                    AND licensePlateNumber LIKE CONCAT('%', #{licensePlateNumber}, '%')
                </if>
                <if test="keyword != ''">
                    AND licensePlateNumber LIKE CONCAT('%', #{keyword}, '%')
                </if>
                
            </where>
            ORDER BY sysCreated ASC
        </script>
    """
    )
    override fun getList(search: BaseSearch): List<Vehicleinfo>
}

/**
 * 车辆信息服务类
 */
@Service
class VehicleinfoService(
    private val personnelService: PersonnelService,
    private val personnelMapper: PersonnelMapper,
    mapper: VehicleinfoMapper
) : BaseService<Vehicleinfo, VehicleinfoMapper>(mapper) {

    /**
     * 根据输入的车主姓名或者是车牌号模糊查询对应信息
     */
    fun getListByKeyWord(keyWord: String): Result {
        val personnelSearch = PersonnelSearch()
        if (keyWord != "") {
            // 对输入的条件先进行姓名的模糊查询获取到符合要求的车主id
            personnelSearch.nameLike = keyWord
            personnelSearch.ifPage = false
            val idList1 = personnelService.getDataList(personnelSearch).map { it.id }
            // 对输入的条件进行车牌号的模糊查询获取到符合要求的车主id
            val idList2 = mapper.getPersonnelIdsByLicensePlateNumber(keyWord)
            val idList = idList1.plus(idList2).distinct()//去重id
            log.info("姓名模糊查询符合要求id集合$idList1")
            log.info("车牌模糊查询符合要求id集合$idList2")
            log.info("id结果集$idList")
            // 创建的是一个空的可变列表而不是null
            val personnelList = mutableListOf<Personnel?>()
            for (id in idList) {
                val personnel = personnelService.getInfo(id).datas as? Personnel
                if (personnel != null && personnel.vehicleinfoList.isNotEmpty()) {
                    personnelList.add(personnel)
                }
            }
            println("符合要求的查询结果$personnelList")
            return if (personnelList.isNotEmpty()) {
                Result.getSuccess(personnelList)
            } else
                Result.getError("未找到符合条件的车辆信息")
        }
        return Result.getError("未识别到查询条件")
    }

    /**
     * 根据车牌号获取车主信息
     */
    fun getInfoByLicensePlateNumber(licensePlateNumber: String): Result {
        val personnelId = mapper.getPersonnelIdByLicensePlateNumber(licensePlateNumber)
        if (personnelId.isEmpty()) {
            return Result.getError("未找到该车牌号对应的车主信息")
        }
        return Result.getSuccess(personnelMapper.getInfo(personnelId))
    }

    /**
     * 一个车主可以有多台车辆，一个车辆只能存在一个车主。在新增操作前进行判断
     */
    @Transactional
    override fun save(model: Vehicleinfo): Result {
        // 检查车牌号是否已存在
        val existingPersonnelId = mapper.getPersonnelIdByLicensePlateNumber(model.licensePlateNumber)
        if (existingPersonnelId.isNotEmpty()) {
            return Result.getError("该车牌号已被其他车主使用")
        }

        // 检查车主是否存在
        val personnel = personnelMapper.getInfo(model.personnelId)
        if (personnel == null) {
            return Result.getError("车主信息不存在")
        }

        if (model.id.isEmpty()) {
            model.id = uuid()
            mapper.insert(model)
        } else {
            delete(model.id)
            mapper.insert(model)
        }

        return Result.getSuccess(model.id)
    }
}

/**
 * 车辆信息控制器
 */
@RestController
@RequestMapping("/api/Vehicleinfo")
class VehicleinfoResource(service: VehicleinfoService) :
    BaseResource<VehicleinfoSearch, Vehicleinfo, VehicleinfoMapper, VehicleinfoService>(service) {

    /**
     * 根据姓名或者车牌号模糊查询对应信息
     */
    @GetMapping("/getListByKeyWord")
    fun getListByKeyWord(@RequestParam("keyWord") keyWord: String): Result {
        return service.getListByKeyWord(keyWord)
    }
}
