package com.shenlan.smartlogixmini.auto

import com.github.pagehelper.PageHelper
import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.mybatis.paginationInfo
import com.shenlan.smartlogixmini.util.SpringContext
import com.shenlan.smartlogixmini.util.errorlog
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.TaskScheduler
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ScheduledFuture
import javax.annotation.PostConstruct

/**
 * 会议实体类
 */
class Meeting : BaseEntity() {
    // 表字段

    /** 会议主题 */
    var title: String = ""
    /** 会议内容 */
    var content: String = ""
    /** 会议室ID */
    var meetingroomId: String = ""
    /** 开始时间 */
    var startTime: Date? = null
    /** 结束时间 */
    var endTime: Date? = null
    /** 创建人ID */
    var creatorId: String = ""
    /** 创建人姓名 */
    var creatorName: String = ""
    /** 会议状态(0-待开始,1-进行中,2-已取消,3-已结束) */
    var status: Int = 0
    /** 是否发送即时通知(创建/修改会议时)(0-否,1-是) */
    var sendInstantNotification: Int = 0
    /** 会议开始前多少分钟发送通知，null表示不发送通知 */
    var notifyMinutesBefore: Int? = null
    /** 是否短信通知(0-否,1-是) */
    var notifyBySms: Int = 0
    /** 是否机器人语音电话通知(0-否,1-是) */
    var notifyByVoiceCall: Int = 0
    /** 会议室选择方式(0-手动选择,1-智能推荐) */
    var roomSelectionType: Int = 0

    // 关联字段

    /** 关联的会议室信息 */
    var meetingroom: Meetingroom? = null
    /** 关联的会议服务列表 */
    var meetingfacilityList: List<Meetingfacility> = listOf()
    /** 参会人员关系列表 */
    var meetingpersonnelList: List<Meetingpersonnel> = listOf()
    /** 关联的附件列表 */
    var contentList: List<Content> = listOf()

    // 其他字段

    /** 展示会议时间范围，格式：2025.5.17 14:00 ~ 2025.5.17 15:00 */
    val timeRange: String
        get() {
            val format = SimpleDateFormat("yyyy.MM.dd HH:mm")
            return if (startTime != null && endTime != null) {
                "${format.format(startTime)} ~ ${format.format(endTime)}"
            } else {
                ""
            }
        }
    /** 当前登录用户是否是会议发起人 */
    var ifCreator: Boolean = false
}

/**
 * 会议查询条件类
 */
class MeetingSearch : BaseSearch() {
    /** 关键词(会议主题) */
    var keyword: String = ""
    /** 会议室ID */
    var meetingroomId: String = ""
    /** 创建人ID */
    var creatorId: String = ""
    /** 会议状态(0-待开始,1-进行中,2-已取消,3-已结束) */
    var status: Int? = null
    /** 会议状态列表，可同时过滤多个状态值 */
    var statusList: List<Int> = listOf()
    /** 开始时间范围-起始 */
    var startTimeBegin: String = ""
    /** 开始时间范围-结束 */
    var startTimeEnd: String = ""
    /** 结束时间范围-起始 */
    var endTimeBegin: String = ""
    /** 结束时间范围-结束 */
    var endTimeEnd: String = ""
    /** 指定日期(格式为yyyy-MM-dd) */
    var date: String = ""
    /** 参会人员ID */
    var personnelId: String = ""
    /** 服务类型(0-用餐,1-桌牌,2-纸笔,3-茶水,4-果盘) */
    var meetingfacilityType: Int? = null
    /** 通知方式(0-短信通知,1-机器人语音电话通知) */
    var notificationMethod: Int? = null
    /** 提前通知分钟数 */
    var notifyMinutesBefore: Int? = null
    /** 是否发送即时通知(创建/修改会议时)(0-否,1-是) */
    var sendInstantNotification: Int? = null
    /** 是否只查询当前登录用户作为会议发起人的会议 */
    var onlyCreator: Boolean = false
    /** 是否只查询当前登录用户参与的会议 */
    var onlyParticipant: Boolean = false
    /** 是否加载会议室信息 */
    var loadMeetingroom: Boolean = false
    /** 是否加载会议服务列表 */
    var loadMeetingfacilityList: Boolean = false
    /** 是否加载参会人员关系列表 */
    var loadMeetingpersonnelList: Boolean = false
    /** 是否加载附件列表 */
    var loadContentList: Boolean = false
}

/**
 * 会议Mapper接口
 */
@Mapper
interface MeetingMapper : BaseMapper<Meeting> {

    @Select("""
        <script>
            SELECT m.* FROM tbl_meeting m
            <where>
                m.sysDeleted = 0
                <if test="keyword != ''">
                    AND m.title LIKE CONCAT('%', #{keyword}, '%')
                </if>
                <if test="meetingroomId != ''">
                    AND m.meetingroomId = #{meetingroomId}
                </if>
                <if test="creatorId != ''">
                    AND m.creatorId = #{creatorId}
                </if>
                <if test="status != null">
                    AND m.status = #{status}
                </if>
                <if test="statusList.size > 0">
                    AND m.status IN
                    <foreach collection="statusList" item="statusItem" open="(" separator="," close=")">
                        #{statusItem}
                    </foreach>
                </if>
                <if test="startTimeBegin != ''">
                    AND m.startTime >= #{startTimeBegin}
                </if>
                <if test="startTimeEnd != ''">
                    AND m.startTime &lt;= #{startTimeEnd}
                </if>
                <if test="endTimeBegin != ''">
                    AND m.endTime >= #{endTimeBegin}
                </if>
                <if test="endTimeEnd != ''">
                    AND m.endTime &lt;= #{endTimeEnd}
                </if>
                <if test="date != ''">
                    AND DATE(m.startTime) = #{date}
                </if>
                <if test="personnelId != ''">
                    AND EXISTS (
                        SELECT 1 FROM tbl_meetingpersonnel mp 
                        WHERE mp.meetingId = m.id AND mp.personnelId = #{personnelId} AND mp.sysDeleted = 0
                    )
                </if>
                <if test="meetingfacilityType != null">
                    AND EXISTS (
                        SELECT 1 FROM tbl_meetingfacility mf 
                        WHERE mf.meetingId = m.id AND mf.type = #{meetingfacilityType} AND mf.sysDeleted = 0
                    )
                </if>
                <if test="notificationMethod != null">
                    AND m.notificationMethod = #{notificationMethod}
                </if>
                <if test="notifyMinutesBefore != null">
                    AND m.notifyMinutesBefore = #{notifyMinutesBefore}
                </if>
                <if test="sendInstantNotification != null">
                    AND m.sendInstantNotification = #{sendInstantNotification}
                </if>
            </where>
            ORDER BY 
            <choose>
                <when test="status != null">
                    m.startTime ASC
                </when>
                <otherwise>
                    CASE m.status
                        WHEN 1 THEN 1  /* 进行中 */
                        WHEN 0 THEN 2  /* 待开始 */
                        WHEN 3 THEN 3  /* 已结束 */
                        WHEN 2 THEN 4  /* 已取消 */
                        ELSE 5
                    END,
                    m.startTime ASC
                </otherwise>
            </choose>
        </script>
    """)
    override fun getList(search: BaseSearch): List<Meeting>

    @Select("""
        SELECT m.* FROM tbl_meeting m
        WHERE m.id = #{id} AND m.sysDeleted = 0
    """)
    override fun getInfo(id: String): Meeting?

    @Select("""
        <script>
            SELECT COUNT(*) FROM tbl_meeting
            WHERE meetingroomId = #{meetingroomId} 
            AND sysDeleted = 0 
            AND status IN (0, 1)
            AND (
                (startTime &lt;= #{startTime} AND endTime > #{startTime})
                OR (startTime &lt; #{endTime} AND endTime >= #{endTime})
                OR (startTime >= #{startTime} AND endTime &lt;= #{endTime})
            )
            <if test="meetingId != ''">
                AND id != #{meetingId}
            </if>
        </script>
    """)
    fun checkTimeConflict(meetingroomId: String, startTime: Date, endTime: Date, meetingId: String = ""): Int

    @Select("""
        SELECT m.* FROM tbl_meeting m
        WHERE m.sysDeleted = 0 AND m.status = 0 AND m.startTime <= NOW() AND m.endTime > NOW()
    """)
    fun getStartingMeetings(): List<Meeting>

    @Select("""
        SELECT m.* FROM tbl_meeting m
        WHERE m.sysDeleted = 0 AND m.status = 1 AND m.endTime <= NOW()
    """)
    fun getEndingMeetings(): List<Meeting>

    /**
     * 查询所有状态异常的会议
     * 包括：
     * 1. 状态为待开始(0)，但开始时间已过的会议
     * 2. 状态为进行中(1)，但结束时间已过的会议
     */
    @Select("""
        SELECT m.* FROM tbl_meeting m
        WHERE m.sysDeleted = 0 AND (
            (m.status = 0 AND m.startTime < NOW()) OR
            (m.status = 1 AND m.endTime < NOW())
        )
        ORDER BY m.startTime ASC
    """)
    fun getAbnormalStatusMeetings(): List<Meeting>

    /**
     * 查询指定时间段内的所有会议
     * 用于定期检查和状态恢复
     */
    @Select("""
        SELECT m.* FROM tbl_meeting m
        WHERE m.sysDeleted = 0 
        AND m.status IN (0, 1)
        AND (
            (m.startTime BETWEEN #{startTime} AND #{endTime}) OR
            (m.endTime BETWEEN #{startTime} AND #{endTime}) OR
            (m.startTime <= #{startTime} AND m.endTime >= #{endTime})
        )
        ORDER BY m.startTime ASC
    """)
    fun getActiveOrUpcomingMeetingsInTimeRange(startTime: Date, endTime: Date): List<Meeting>

    /**
     * 查询所有需要立即开始的会议
     * 状态为待开始(0)，且当前时间已经超过开始时间但未超过结束时间
     */
    @Select("""
        SELECT m.* FROM tbl_meeting m
        WHERE m.sysDeleted = 0 
        AND m.status = 0
        AND m.startTime <= NOW()
        AND m.endTime > NOW()
    """)
    fun getMeetingsNeedToStart(): List<Meeting>

    /**
     * 查询所有需要立即结束的会议
     * 状态为进行中(1)，且当前时间已经超过结束时间
     */
    @Select("""
        SELECT m.* FROM tbl_meeting m
        WHERE m.sysDeleted = 0 
        AND m.status = 1
        AND m.endTime <= NOW()
    """)
    fun getMeetingsNeedToEnd(): List<Meeting>

    /**
     * 查询所有需要直接标记为已结束的会议
     * 状态为待开始(0)，但开始时间和结束时间都已过
     */
    @Select("""
        SELECT m.* FROM tbl_meeting m
        WHERE m.sysDeleted = 0 
        AND m.status = 0
        AND m.startTime < NOW()
        AND m.endTime < NOW()
    """)
    fun getMeetingsNeedToMarkAsEnded(): List<Meeting>

    /**
     * 直接更新会议状态
     * 绕过会议室验证和其他业务逻辑检查
     */
    @Update("""
        UPDATE tbl_meeting 
        SET status = #{status}
        WHERE id = #{id} 
        AND sysDeleted = 0
    """)
    fun updateMeetingStatusDirectly(id: String, status: Int): Int
}

/**
 * 会议Service类
 */
@Service
class MeetingService(
    mapper: MeetingMapper,
    private val meetingroomMapper: MeetingroomMapper,
    private val meetingpersonnelMapper: MeetingpersonnelMapper,
    private val meetingfacilityMapper: MeetingfacilityMapper,
    private val meetingnotificationMapper: MeetingnotificationMapper,
    private val meetingnotificationService: MeetingnotificationService,
    private val meetingpersonnelService: MeetingpersonnelService,
    private val meetingfacilityService: MeetingfacilityService,
    private val contentService: ContentService,
    private val personnelService: PersonnelService
) : BaseService<Meeting, MeetingMapper, MeetingSearch>(mapper) {

    /**
     * 重写getInfo方法，加载关联的会议室、参会人员、会议服务和会议通知信息
     */
    override fun getEntity(id: String): Meeting? {
        val meeting = mapper.getInfo(id)
        if (meeting != null) {
            // 获取会议室详情
            meeting.meetingroom = meetingroomMapper.getInfo(meeting.meetingroomId)

            // 获取参会人员关系列表（包含人员详情）
            val meetingpersonnelSearch = MeetingpersonnelSearch()
            meetingpersonnelSearch.meetingId = id
            meetingpersonnelSearch.ifPage = false
            meetingpersonnelSearch.loadPersonnel = true
            meeting.meetingpersonnelList =meetingpersonnelService.getEntityList(meetingpersonnelSearch)

            // 获取会议服务列表
            val facilitySearch = MeetingfacilitySearch()
            facilitySearch.meetingId = id
            facilitySearch.ifPage = false
            meeting.meetingfacilityList = meetingfacilityService.getEntityList(facilitySearch)

            // 获取会议附件列表
            val contentSearch = ContentSearch()
            contentSearch.meetingId = id
            contentSearch.ifPage = false
            meeting.contentList = contentService.getEntityList(contentSearch)

            // 检查当前登录用户是否是会议发起人
            val currentPersonnel = personnelService.getCurrentPersonnel()
            if (currentPersonnel != null) {
                meeting.ifCreator = currentPersonnel.id == meeting.creatorId
            }
        }
        return meeting
    }

    /**
     * 重写方法，根据查询条件决定是否加载关联的会议室、参会人员和会议服务信息
     */
    override fun getEntityPage(search: MeetingSearch): PaginationInfo<Meeting> {
        // 新增：只查当前用户发起的会议
        if (search.onlyCreator) {
            val currentPersonnel = personnelService.getCurrentPersonnel()
            if (currentPersonnel != null) {
                search.creatorId = currentPersonnel.id
            }
        }

        // 新增：只查当前用户参与的会议
        if (search.onlyParticipant) {
            val currentPersonnel = personnelService.getCurrentPersonnel()
            if (currentPersonnel != null) {
                search.personnelId = currentPersonnel.id
            }
        }

        // 判断是否需要分页
        return if (!search.ifPage) {
            // 不分页，直接查询
            val list = mapper.getList(search)
            // 根据查询条件决定是否加载关联信息
            loadRelatedData(list, search.loadMeetingroom, search.loadMeetingfacilityList, search.loadMeetingpersonnelList, search.loadContentList)
            PaginationInfo.ofList(list)
        } else {
            // 使用分页查询
            val pageResult = PageHelper.startPage<Meeting>(search.currentPage, search.pageRecord)
                .doSelectPageInfo<Meeting> {
                    mapper.getList(search)
                }
                .paginationInfo

            // 根据查询条件决定是否加载关联信息
            val list = pageResult.result
            loadRelatedData(list, search.loadMeetingroom, search.loadMeetingfacilityList, search.loadMeetingpersonnelList, search.loadContentList)

            pageResult
        }
    }

    /**
     * 加载会议相关数据（会议室、参会人员、会议服务、附件）
     */
    private fun loadRelatedData(meetings: List<Meeting>, loadMeetingroom: Boolean, loadfacilityList: Boolean, loadMeetingpersonnelList: Boolean = false, loadContentList: Boolean = false) {
        // 获取当前登录用户对应的人员信息
        val currentPersonnel = personnelService.getCurrentPersonnel()

        for (meeting in meetings) {
            // 加载会议室信息
            if (loadMeetingroom) {
                meeting.meetingroom = meetingroomMapper.getInfo(meeting.meetingroomId)
            }

            // 加载参会人员关系列表
            if (loadMeetingpersonnelList) {
                val meetingpersonnelSearch = MeetingpersonnelSearch()
                meetingpersonnelSearch.meetingId = meeting.id
                meetingpersonnelSearch.ifPage = false
                meetingpersonnelSearch.loadPersonnel = true

                meeting.meetingpersonnelList = meetingpersonnelService.getEntityList(meetingpersonnelSearch)
            }

            // 加载会议服务列表
            if (loadfacilityList) {
                val facilitySearch = MeetingfacilitySearch()
                facilitySearch.meetingId = meeting.id
                facilitySearch.ifPage = false

                meeting.meetingfacilityList = meetingfacilityService.getEntityList(facilitySearch)
            }

            // 加载会议附件列表
            if (loadContentList) {
                val contentSearch = ContentSearch()
                contentSearch.meetingId = meeting.id
                contentSearch.ifPage = false

                meeting.contentList = contentService.getEntityList(contentSearch)
            }

            // 设置当前用户是否是创建者
            if (currentPersonnel != null) {
                meeting.ifCreator = currentPersonnel.id == meeting.creatorId
            } else {
                meeting.ifCreator = false
            }
        }
    }

    /**
     * 重写方法，实现会议创建和更新功能
     */
    @Transactional
    override fun saveEntity(entity: Meeting): String {
        // 设置发起人信息（使用当前登录用户对应的人员信息）
        val currentPersonnel = personnelService.getCurrentPersonnel()
        if (currentPersonnel == null) {
            throw BusinessException("未找到当前用户对应的人员信息，无法创建会议")
        }

        entity.creatorId = currentPersonnel.id
        entity.creatorName = currentPersonnel.name

        // 验证会议室是否存在
        val meetingroom = meetingroomMapper.getInfo(entity.meetingroomId)
        if (meetingroom == null) {
            throw BusinessException("会议室不存在")
        }

        // 验证会议时间是否有效
        if (entity.startTime == null || entity.endTime == null) {
            throw BusinessException("会议开始时间和结束时间不能为空")
        }

        if (entity.startTime!!.after(entity.endTime)) {
            throw BusinessException("会议开始时间不能晚于结束时间")
        }

        // 检查会议室时间冲突
        val conflictCount = mapper.checkTimeConflict(
            entity.meetingroomId,
            entity.startTime!!,
            entity.endTime!!,
            entity.id
        )

        if (conflictCount > 0) {
            throw BusinessException("所选时间段内会议室已被预约")
        }

        // 判断是新建会议还是修改会议
        val isNewMeeting = entity.id.isEmpty()

        // 保存当前的参会人员情况，用于后续发送不同类型的通知
        val oldPersonnelList = if (!isNewMeeting) {
            val personnelSearch = MeetingpersonnelSearch()
            personnelSearch.meetingId = entity.id
            personnelSearch.ifPage = false
            meetingpersonnelMapper.getList(personnelSearch)
        } else {
            listOf()
        }

        // 保存原有参会人员ID，用于判断人员变化
        val oldPersonnelIdSet = oldPersonnelList.map { it.personnelId }.toSet()

        // 保存会议信息
        // 手动实现BaseService.save方法的逻辑
        val id: String
        if (!isNewMeeting) {
            // 对于已存在的会议，使用forceDelete删除所有关联数据
            forceDelete(entity.id)
            // 重新设置ID为原ID
            entity.id = entity.id
        } else {
            // 新会议分配UUID
            entity.id = uuid()
        }

        // 插入会议数据
        if (1 == mapper.insert(entity)) {
            id = entity.id
        } else {
            throw BusinessException("保存会议信息失败")
        }

        // 如果保存成功，处理会议服务和参会人员
        if (entity.meetingfacilityList.isNotEmpty()) {
            // 保存新的会议服务
            for (facility in entity.meetingfacilityList) {
                facility.meetingId = entity.id
                facility.id = uuid()
                meetingfacilityMapper.insert(facility)
            }
        }

        // 处理参会人员列表（包括确保发起人已在参会人员列表中）
        // 检查发起人是否已在参会人员列表中
        val creatorExists = entity.meetingpersonnelList.any { it.personnelId == currentPersonnel.id }

        // 如果发起人不在列表中，添加到列表
        if (!creatorExists) {
            val creatorPersonnel = Meetingpersonnel()
            creatorPersonnel.id = uuid()
            creatorPersonnel.meetingId = entity.id
            creatorPersonnel.personnelId = currentPersonnel.id
            creatorPersonnel.role = 1  // 发起人角色
            creatorPersonnel.feedback = 1  // 设置反馈为"参加"
            entity.meetingpersonnelList += creatorPersonnel
        }

        // 确保所有参会人员中，发起人的角色为发起人
        entity.meetingpersonnelList.forEach {
            if (it.personnelId == currentPersonnel.id) {
                it.role = 1 // 确保角色为发起人
                it.feedback = 1 // 确保反馈为"参加"
            }
        }

        // 保存新的参会人员关系
        if (entity.meetingpersonnelList.isNotEmpty()) {
            for (personnel in entity.meetingpersonnelList) {
                // 只使用id、personnelId和role三个字段
                val newPersonnel = Meetingpersonnel()
                newPersonnel.id = uuid() // 总是生成新ID
                newPersonnel.personnelId = personnel.personnelId
                newPersonnel.role = personnel.role
                newPersonnel.meetingId = entity.id
                // 添加feedback字段的复制
                newPersonnel.feedback = personnel.feedback
                newPersonnel.reason = personnel.reason

                meetingpersonnelMapper.insert(newPersonnel)
            }
        }

        // 处理会议附件关联
        if (entity.contentList.isNotEmpty()) {
            // 更新新的附件关联
            for (content in entity.contentList) {
                // 如果附件ID存在且不为空
                if (content.id.isNotEmpty()) {
                    // 获取附件信息
                    val contentInfo = contentService.getEntity(content.id)
                    if (contentInfo != null) {
                        // 更新附件的meetingId字段
                        contentInfo.meetingId = entity.id
                        contentService.saveEntity(contentInfo)
                    }
                }
            }
        }

        // 处理会议通知
        // 先删除所有旧的通知记录
        val meetingnotificationSearch = MeetingnotificationSearch()
        meetingnotificationSearch.meetingId = entity.id
        meetingnotificationSearch.ifPage = false
        val oldMeetingnotifications = meetingnotificationMapper.getList(meetingnotificationSearch)
        for (oldMeetingnotification in oldMeetingnotifications) {
            meetingnotificationMapper.delete(oldMeetingnotification.id)

            // 如果有对应的通知任务，也需要取消
            try {
                val meetingnotificationScheduler = SpringContext.context.getBean(MeetingnotificationScheduler::class.java)
                meetingnotificationScheduler.cancelMeetingnotificationTask(oldMeetingnotification.id)
            } catch (e: Exception) {
                errorlog(e)
            }
        }

        // 如果开启了即时通知，根据参会人员状态发送不同类型的通知
        if (entity.sendInstantNotification == 1) {
            if (isNewMeeting) {
                // 新建会议：所有人都是新邀请的，发送会议创建通知
                for (personnel in entity.meetingpersonnelList) {
                    sendNotificationsToPersonnel(entity, personnel.personnelId, 0, "create")
                }
                log.info("Created and sent ${entity.meetingpersonnelList.size} immediate creation notifications for new meeting [${entity.id}]")
            } else {
                // 会议修改：需要区分三种情况
                // 1. 获取新的参会人员ID集合
                val newPersonnelIdSet = entity.meetingpersonnelList.map { it.personnelId }.toSet()

                // 2. 区分三类人员：新邀请的、仍被邀请的、被删除的
                val newlyInvitedPersonnelIds = newPersonnelIdSet.filter { !oldPersonnelIdSet.contains(it) }
                val stillInvitedPersonnelIds = newPersonnelIdSet.filter { oldPersonnelIdSet.contains(it) }
                val removedPersonnelIds = oldPersonnelIdSet.filter { !newPersonnelIdSet.contains(it) }

                // 3. 向不同类型的人员发送对应的通知
                // 新邀请的人：发送会议创建通知(type=0)
                for (personnelId in newlyInvitedPersonnelIds) {
                    sendNotificationsToPersonnel(entity, personnelId, 0, "create")
                }
                log.info("Created and sent ${newlyInvitedPersonnelIds.size} creation notifications for newly invited personnel to meeting [${entity.id}]")

                // 仍被邀请的人：发送会议调整通知(type=3)
                for (personnelId in stillInvitedPersonnelIds) {
                    sendNotificationsToPersonnel(entity, personnelId, 3, "adjust")
                }
                log.info("Created and sent ${stillInvitedPersonnelIds.size} adjustment notifications for still invited personnel to meeting [${entity.id}]")

                // 被删除的人：发送会议取消通知(type=2)
                for (personnelId in removedPersonnelIds) {
                    sendNotificationsToPersonnel(entity, personnelId, 2, "cancel")
                }
                log.info("Created and sent ${removedPersonnelIds.size} cancellation notifications for removed personnel from meeting [${entity.id}]")
            }
        } else {
            log.info("Immediate notifications for meeting [${entity.id}] are disabled (sendInstantNotification=0)")
        }

        // 如果设置了提前通知时间，则为每个参会人员创建提前通知
        if (entity.notifyMinutesBefore != null && entity.startTime != null) {
            val calendar = Calendar.getInstance()
            calendar.time = entity.startTime!!
            calendar.add(Calendar.MINUTE, -entity.notifyMinutesBefore!!)
            val notificationTime = calendar.time
            val now = Date()
            if (notificationTime.after(now)) {
                for (personnel in entity.meetingpersonnelList) {
                    // 跳过会议发起人，不为其创建提前通知
                    if (personnel.personnelId == entity.creatorId) {
                        log.info("Skipping scheduled notification for meeting [${entity.id}] to creator [${entity.creatorId}]")
                        continue
                    }

                    if (entity.notifyBySms == 1) {
                        val meetingnotification = Meetingnotification()
                        meetingnotification.id = uuid()
                        meetingnotification.meetingId = entity.id
                        meetingnotification.personnelId = personnel.personnelId
                        meetingnotification.method = 0 // 短信
                        meetingnotification.status = 0
                        meetingnotification.time = notificationTime
                        meetingnotification.type = 1 // 会议开始通知
                        meetingnotificationMapper.insert(meetingnotification)
                        try {
                            val meetingnotificationScheduler = SpringContext.context.getBean(MeetingnotificationScheduler::class.java)
                            meetingnotificationScheduler.scheduleMeetingnotificationTask(meetingnotification)
                        } catch (e: Exception) {
                            errorlog(e)
                        }
                    }
                    if (entity.notifyByVoiceCall == 1) {
                        val meetingnotification = Meetingnotification()
                        meetingnotification.id = uuid()
                        meetingnotification.meetingId = entity.id
                        meetingnotification.personnelId = personnel.personnelId
                        meetingnotification.method = 1 // 语音电话
                        meetingnotification.status = 0
                        meetingnotification.time = notificationTime
                        meetingnotification.type = 1 // 会议开始通知
                        meetingnotificationMapper.insert(meetingnotification)
                        try {
                            val meetingnotificationScheduler = SpringContext.context.getBean(MeetingnotificationScheduler::class.java)
                            meetingnotificationScheduler.scheduleMeetingnotificationTask(meetingnotification)
                        } catch (e: Exception) {
                            errorlog(e)
                        }
                    }
                }
                log.info("Created scheduled notifications for meeting [${entity.id}], scheduled at $notificationTime")
            } else {
                log.warn("Scheduled notification time for meeting [${entity.id}] is in the past, no notifications created")
            }
        }

        // 注册会议状态定时任务
        if (entity.status == 0) {
            try {
                // 获取MeetingStatusScheduler实例
                val statusScheduler = SpringContext.context.getBean(MeetingStatusScheduler::class.java)
                // 注册会议开始状态变更任务
                statusScheduler.scheduleStartTask(entity)
            } catch (e: Exception) {
                errorlog(e)
            }
        }

        return id
    }

    /**
     * 向指定人员发送指定类型的通知
     */
    private fun sendNotificationsToPersonnel(model: Meeting, personnelId: String, notificationType: Int, typeDesc: String) {
        // 如果是会议发起人，不发送通知
        if (personnelId == model.creatorId) {
            log.info("Skipping $typeDesc notification for meeting [${model.id}] to creator [${personnelId}]")
            return
        }

        // 发送短信通知
        if (model.notifyBySms == 1) {
            val smsMeetingnotification = Meetingnotification()
            smsMeetingnotification.id = uuid()
            smsMeetingnotification.meetingId = model.id
            smsMeetingnotification.personnelId = personnelId
            smsMeetingnotification.method = 0 // 短信
            smsMeetingnotification.status = 0
            smsMeetingnotification.time = Date()
            smsMeetingnotification.type = notificationType
            meetingnotificationMapper.insert(smsMeetingnotification)
            try {
                meetingnotificationService.sendMeetingnotification(smsMeetingnotification.id)
                log.info("Immediate $typeDesc SMS meetingnotification for meeting [${model.id}] sent to personnel [$personnelId]")
            } catch (e: Exception) {
                log.error("Failed to send immediate $typeDesc SMS meetingnotification for meeting [${model.id}] to personnel [$personnelId]: ${e.message}")
                errorlog(e)
            }
        }

        // 发送语音电话通知
        if (model.notifyByVoiceCall == 1) {
            val voiceMeetingnotification = Meetingnotification()
            voiceMeetingnotification.id = uuid()
            voiceMeetingnotification.meetingId = model.id
            voiceMeetingnotification.personnelId = personnelId
            voiceMeetingnotification.method = 1 // 语音电话
            voiceMeetingnotification.status = 0
            voiceMeetingnotification.time = Date()
            voiceMeetingnotification.type = notificationType
            meetingnotificationMapper.insert(voiceMeetingnotification)
            try {
                meetingnotificationService.sendMeetingnotification(voiceMeetingnotification.id)
                log.info("Immediate $typeDesc VoiceCall meetingnotification for meeting [${model.id}] sent to personnel [$personnelId]")
            } catch (e: Exception) {
                log.error("Failed to send immediate $typeDesc VoiceCall meetingnotification for meeting [${model.id}] to personnel [$personnelId]: ${e.message}")
                errorlog(e)
            }
        }
    }

    /**
     * 内部实现会议及关联数据的删除
     * @param id 会议ID
     * @param logPrefix 日志前缀，用于区分普通删除和强制删除
     * @return 删除结果
     */
    private fun deleteMeetingAndRelatedData(id: String, logPrefix: String): Int {
        try {
            // 1. 删除会议关联的设施服务
            val facilitySearch = MeetingfacilitySearch()
            facilitySearch.meetingId = id
            facilitySearch.ifPage = false
            val facilities = meetingfacilityService.getEntityList(facilitySearch)
            for (facility in facilities) {
                meetingfacilityService.deleteEntity(facility.id)
            }
            log.info("$logPrefix deleted ${facilities.size} facilities for meeting [$id]")

            // 2. 删除会议关联的参会人员关系
            val personnelSearch = MeetingpersonnelSearch()
            personnelSearch.meetingId = id
            personnelSearch.ifPage = false
            val personnels = meetingpersonnelService.getEntityList(personnelSearch)
            for (personnel in personnels) {
                meetingpersonnelService.deleteEntity(personnel.id)
            }
            log.info("$logPrefix deleted ${personnels.size} personnel relationships for meeting [$id]")

            // 3. 删除会议关联的附件
            val contentSearch = ContentSearch()
            contentSearch.meetingId = id
            contentSearch.ifPage = false
            val contents = contentService.getEntityList(contentSearch)
            for (content in contents) {
                contentService.deleteEntity(content.id)
            }
            log.info("$logPrefix deleted ${contents.size} content attachments for meeting [$id]")

            // 4. 删除会议关联的通知
            val meetingnotificationSearch2 = MeetingnotificationSearch()
            meetingnotificationSearch2.meetingId = id
            meetingnotificationSearch2.ifPage = false
            val meetingnotifications = meetingnotificationService.getEntityList(meetingnotificationSearch2)
            for (meetingnotification in meetingnotifications) {
                meetingnotificationService.deleteEntity(meetingnotification.id)
            }
            log.info("$logPrefix deleted ${meetingnotifications.size} meetingnotifications for meeting [$id]")

            // 5. 最后删除会议本身
            val result = mapper.delete(id)
            log.info("$logPrefix deleted meeting [$id]")

            return result
        } catch (e: Exception) {
            errorlog(e)
            return 0
        }
    }

    /**
     * 取消会议相关的定时任务
     * @param id 会议ID
     */
    private fun cancelMeetingScheduledTasks(id: String) {
        try {
            // 获取MeetingStatusScheduler实例
            val statusScheduler = SpringContext.context.getBean(MeetingStatusScheduler::class.java)
            // 取消会议开始状态变更任务
            statusScheduler.cancelStartTask(id)

            // 获取MeetingnotificationScheduler实例
            val meetingnotificationScheduler = SpringContext.context.getBean(MeetingnotificationScheduler::class.java)
            // 取消所有通知任务
            val meetingnotificationSearch = MeetingnotificationSearch()
            meetingnotificationSearch.meetingId = id
            meetingnotificationSearch.ifPage = false
            val meetingnotifications = meetingnotificationMapper.getList(meetingnotificationSearch)
            for (meetingnotification in meetingnotifications) {
                meetingnotificationScheduler.cancelMeetingnotificationTask(meetingnotification.id)
            }
        } catch (e: Exception) {
            // 记录异常但不中断删除流程
            errorlog(e)
            log.warn("Failed to cancel scheduled tasks for meeting [$id]: ${e.message}")
        }
    }

    /**
     * 重写delete方法，只允许删除已结束或已取消的会议
     */
    @Transactional
    override fun deleteEntity(id: String): Int {
        // 查询会议信息
        val meeting = mapper.getInfo(id)
        if (meeting == null) {
            throw BusinessException("会议不存在")
        }

        // 检查会议状态，只有已结束(3)或已取消(2)的会议才能删除
        if (meeting.status != 2 && meeting.status != 3) {
            throw BusinessException("只能删除已结束或已取消的会议")
        }

        return deleteMeetingAndRelatedData(id, "Normal")
    }

    /**
     * 强制删除会议
     * 无论会议处于何种状态，都直接删除它
     */
    @Transactional
    fun forceDelete(id: String): Int {
        // 查询会议信息
        val meeting = mapper.getInfo(id)
        if (meeting == null) {
            throw BusinessException("会议不存在")
        }

        // 如果会议状态为待开始或进行中，尝试取消相关定时任务
        if (meeting.status == 0 || meeting.status == 1) {
            cancelMeetingScheduledTasks(id)
        }

        return deleteMeetingAndRelatedData(id, "Force")
    }

    /**
     * 检查并更新会议状态
     */
    @Transactional
    fun updateMeetingStatus() {
        val now = Date()

        // 1. 更新待开始→进行中
        // 查找所有应该开始但未开始的会议
        val startingMeetings = mapper.getStartingMeetings()
        for (meeting in startingMeetings) {
            meeting.status = 1 // 进行中
            super.saveEntity(meeting)
            log.info("Meeting [${meeting.id}] ${meeting.title} status updated: Waiting -> In Progress")
        }

        // 2. 更新进行中→已结束
        // 查找所有应该结束但未结束的会议
        val endingMeetings = mapper.getEndingMeetings()
        for (meeting in endingMeetings) {
            meeting.status = 3 // 已结束
            super.saveEntity(meeting)
            log.info("Meeting [${meeting.id}] ${meeting.title} status updated: In Progress -> Ended")
        }

        // 3. 处理异常状态：待开始但开始时间已过
        val search1 = MeetingSearch()
        search1.status = 0 // 待开始状态
        search1.ifPage = false
        val abnormalMeetings1 = mapper.getList(search1)

        for (meeting in abnormalMeetings1) {
            if (meeting.startTime != null && meeting.startTime!!.before(now) &&
                meeting.endTime != null && meeting.endTime!!.before(now)) {
                // 会议应该已经结束，但状态仍为待开始
                meeting.status = 3 // 直接设置为已结束
                super.saveEntity(meeting)
                log.info("Meeting [${meeting.id}] ${meeting.title} status fixed: Waiting -> Ended (abnormal state)")
            }
        }

        // 4. 处理异常状态：进行中但结束时间已过
        val search2 = MeetingSearch()
        search2.status = 1 // 进行中状态
        search2.ifPage = false
        val abnormalMeetings2 = mapper.getList(search2)

        for (meeting in abnormalMeetings2) {
            if (meeting.endTime != null && meeting.endTime!!.before(now)) {
                // 会议应该已经结束，但状态仍为进行中
                meeting.status = 3 // 设置为已结束
                super.saveEntity(meeting)
                log.info("Meeting [${meeting.id}] ${meeting.title} status fixed: In Progress -> Ended (abnormal state)")
            }
        }
    }

    /**
     * 检查会议室在指定时间段是否可用
     */
    fun checkMeetingroomAvailable(meetingroomId: String, startTime: Date, endTime: Date, meetingId: String = ""): Result {
        val conflictCount = mapper.checkTimeConflict(meetingroomId, startTime, endTime, meetingId)
        return Result.getSuccess(conflictCount == 0)
    }

    /**
     * 直接更新会议状态方法，绕过会议室验证
     * 用于系统自动状态更新，特别是当会议室不存在时
     */
    @Transactional
    fun updateMeetingStatusOnly(id: String, newStatus: Int): Result {
        val meeting = mapper.getInfo(id)
        if (meeting == null) {
            return Result.getError("会议不存在")
        }

        try {
            // 使用Mapper中的方法直接更新状态
            val updateCount = mapper.updateMeetingStatusDirectly(id, newStatus)

            if (updateCount > 0) {
                log.info("Meeting [${meeting.id}] ${meeting.title} status directly updated to: ${getStatusText(newStatus)}")
                return Result.getSuccess(true)
            } else {
                log.error("Failed to update meeting status: No rows affected")
                return Result.getError("更新会议状态失败：未影响任何行")
            }
        } catch (e: Exception) {
            log.error("Failed to update meeting status directly: ${e.message}")
            errorlog(e)
            return Result.getError("更新会议状态失败：${e.message}")
        }
    }

    /**
     * 获取状态文本描述
     */
    private fun getStatusText(status: Int): String {
        return when(status) {
            0 -> "Waiting"
            1 -> "In Progress"
            2 -> "Cancelled"
            3 -> "Ended"
            else -> "Unknown($status)"
        }
    }

    /**
     * 批量删除会议
     * 只能删除已结束或已取消的会议
     */
    @Transactional
    fun batchDelete(idList: List<String>): Result {
        if (idList.isEmpty()) {
            return Result.getError("没有要删除的会议")
        }

        val successIdList = mutableListOf<String>()
        val failureMessages = mutableListOf<String>()

        // 逐个处理每个会议ID
        for (id in idList) {
            val result = deleteEntity(id)
            if (result == 1) {
                successIdList.add(id)
            } else {
                failureMessages.add("会议ID $id")
            }
        }

        // 返回处理结果
        return if (failureMessages.isEmpty()) {
            Result.getSuccessInfo("成功删除${successIdList.size}个会议")
        } else {
            Result.getError("部分会议删除失败: ${failureMessages.joinToString("; ")}",
                mapOf("successIdList" to successIdList, "failureMessages" to failureMessages))
        }
    }

    /**
     * 取消会议
     */
    @Transactional
    fun cancelMeeting(id: String): Result {
        val meeting = mapper.getInfo(id)
        if (meeting == null) {
            return Result.getError("会议不存在")
        }

        if (meeting.status != 0) {
            return Result.getError("只能取消尚未开始的会议")
        }

        // 获取会议参会人员列表
        val meetingpersonnelSearch = MeetingpersonnelSearch()
        meetingpersonnelSearch.meetingId = id
        meetingpersonnelSearch.ifPage = false
        meetingpersonnelSearch.loadPersonnel = true
        val participantsList = meetingpersonnelService.getEntityList(meetingpersonnelSearch)

        // 直接更新会议状态为已取消，而不是通过save方法删除再重建
        val updateResult = mapper.updateMeetingStatusDirectly(id, 2)

        val result = if (updateResult > 0) {
            Result.getSuccess(true)
        } else {
            return Result.getError("更新会议状态失败")
        }

        // 如果更新成功，同时取消相关定时任务
        if (result.rlt == Result.SUCCESS) {
            try {
                // 取消会议相关的定时任务
                cancelMeetingScheduledTasks(id)

                // 取消会议通知任务，更新通知状态为已取消
                val meetingnotificationSearch3 = MeetingnotificationSearch()
                meetingnotificationSearch3.meetingId = id
                meetingnotificationSearch3.ifPage = false
                val meetingnotifications = meetingnotificationMapper.getList(meetingnotificationSearch3)
                for (meetingnotification in meetingnotifications) {
                    // 更新通知状态为已取消
                    meetingnotificationService.cancelMeetingnotification(meetingnotification.id, "会议已取消")
                }

                // 为每个参会人员创建并发送会议取消通知（使用查询到的参会人员列表）
                for (personnel in participantsList) {
                    // 跳过会议发起人，不发送取消通知
                    if (personnel.personnelId == meeting.creatorId) {
                        log.info("Skipping cancel notification for meeting [${meeting.id}] to creator [${meeting.creatorId}]")
                        continue
                    }

                    if (meeting.notifyBySms == 1) {
                        val cancelMeetingnotification = Meetingnotification()
                        cancelMeetingnotification.id = uuid()
                        cancelMeetingnotification.meetingId = id
                        cancelMeetingnotification.personnelId = personnel.personnelId
                        cancelMeetingnotification.method = 0 // 短信
                        cancelMeetingnotification.status = 0
                        cancelMeetingnotification.time = Date()
                        cancelMeetingnotification.type = 2 // 会议取消通知
                        meetingnotificationMapper.insert(cancelMeetingnotification)
                        try {
                            meetingnotificationService.sendMeetingnotification(cancelMeetingnotification.id)
                            log.info("Cancel SMS meetingnotification for meeting [${meeting.id}] sent to personnel [${personnel.personnelId}]")
                        } catch (e: Exception) {
                            log.error("Failed to send cancel SMS meetingnotification for meeting [${meeting.id}] to personnel [${personnel.personnelId}]: ${e.message}")
                            errorlog(e)
                        }
                    }
                    if (meeting.notifyByVoiceCall == 1) {
                        val cancelMeetingnotification = Meetingnotification()
                        cancelMeetingnotification.id = uuid()
                        cancelMeetingnotification.meetingId = id
                        cancelMeetingnotification.personnelId = personnel.personnelId
                        cancelMeetingnotification.method = 1 // 语音电话
                        cancelMeetingnotification.status = 0
                        cancelMeetingnotification.time = Date()
                        cancelMeetingnotification.type = 2 // 会议取消通知
                        meetingnotificationMapper.insert(cancelMeetingnotification)
                        try {
                            meetingnotificationService.sendMeetingnotification(cancelMeetingnotification.id)
                            log.info("Cancel VoiceCall meetingnotification for meeting [${meeting.id}] sent to personnel [${personnel.personnelId}]")
                        } catch (e: Exception) {
                            log.error("Failed to send cancel VoiceCall meetingnotification for meeting [${meeting.id}] to personnel [${personnel.personnelId}]: ${e.message}")
                            errorlog(e)
                        }
                    }
                }

                log.info("Created and sent ${participantsList.size - 1} cancel meetingnotifications for meeting [${meeting.id}]")

                // 记录日志
                log.info("Meeting [${meeting.id}] ${meeting.title} has been cancelled")
            } catch (e: Exception) {
                errorlog(e)
            }
        }

        return result
    }
}

/**
 * 会议Controller类
 */
@RestController
@RequestMapping("/api/Meeting")
class MeetingResource(
    service: MeetingService
) : BaseResource<MeetingSearch, Meeting, MeetingMapper, MeetingService>(service) {

    @GetMapping("/cancelMeeting/{id}")
    fun cancelMeeting(@PathVariable id: String): Result {
        return service.cancelMeeting(id)
    }

    @GetMapping("/checkMeetingroomAvailable/{meetingroomId}/{startTime}/{endTime}")
    fun checkMeetingroomAvailable(
        @PathVariable meetingroomId: String,
        @PathVariable startTime: Date,
        @PathVariable endTime: Date
    ): Result {
        return service.checkMeetingroomAvailable(meetingroomId, startTime, endTime)
    }

    @PostMapping("/batchDelete")
    fun batchDelete(@RequestBody idList: List<String>): Result {
        return service.batchDelete(idList)
    }

    @GetMapping("/forceDelete/{id}")
    fun forceDelete(@PathVariable id: String): Result {
        return Result.getSuccess(service.forceDelete(id))
    }
}

/**
 * 会议状态自动更新调度器
 * 实现两种机制结合的方案：
 * 1. 定时任务：低频率全表扫描，作为兜底机制
 * 2. 事件驱动：在会议创建和修改时，动态注册定时任务，准确触发状态变更
 */
@Configuration
@EnableScheduling
class MeetingStatusScheduler {

    @Autowired
    private lateinit var meetingService: MeetingService

    @Autowired
    private lateinit var meetingMapper: MeetingMapper

    @Autowired
    private lateinit var taskScheduler: TaskScheduler

    // 存储会议ID和对应的定时任务
    private val startTaskMap = ConcurrentHashMap<String, ScheduledFuture<*>>()
    private val endTaskMap = ConcurrentHashMap<String, ScheduledFuture<*>>()

    /**
     * 系统启动时初始化所有未开始和进行中的会议的定时任务
     * 同时修复可能存在的状态错误
     */
    @PostConstruct
    fun initScheduledTasks() {
        try {
            log.info("System startup, initializing meeting status scheduler...")

            // 首先修复可能存在的状态错误
            fixMeetingStatus()

            // 再次验证有没有遗漏的异常状态会议
            val additionalFixRequired = verifyMeetingStatus()

            if (additionalFixRequired) {
                log.info("Abnormal status meetings detected, performing secondary fix...")
                fixMeetingStatus()
            }

            // 查询所有待开始的会议
            val waitingSearch = MeetingSearch()
            waitingSearch.status = 0
            waitingSearch.ifPage = false
            val waitingMeetings = meetingMapper.getList(waitingSearch)
            for (meeting in waitingMeetings) {
                if (meeting.startTime != null && meeting.startTime!!.after(Date())) {
                    scheduleStartTask(meeting)
                    log.info("Registered start task for waiting meeting [${meeting.id}] ${meeting.title}, scheduled start time: ${meeting.startTime}")
                } else if (meeting.startTime != null && meeting.startTime!!.before(Date())) {
                    // 开始时间已过但仍为待开始状态，尝试再次修复
                    log.warn("Found abnormal meeting [${meeting.id}] ${meeting.title}, start time passed but status still waiting, fixing...")
                    if (meeting.endTime != null && meeting.endTime!!.before(Date())) {
                        // 结束时间也已过，直接标记为已结束
                        val result = meetingService.updateMeetingStatusOnly(meeting.id, 3)
                        if (result.rlt == Result.SUCCESS) {
                            log.info("Meeting [${meeting.id}] ${meeting.title} status fixed: Waiting -> Ended")
                        } else {
                            log.error("Failed to fix meeting [${meeting.id}] status: ${result.info}")
                        }
                    } else if (meeting.endTime != null && meeting.endTime!!.after(Date())) {
                        // 结束时间未过，标记为进行中
                        val result = meetingService.updateMeetingStatusOnly(meeting.id, 1)
                        if (result.rlt == Result.SUCCESS) {
                            log.info("Meeting [${meeting.id}] ${meeting.title} status fixed: Waiting -> In Progress")
                            // 为其注册结束任务
                            scheduleEndTask(meeting)
                        } else {
                            log.error("Failed to fix meeting [${meeting.id}] status: ${result.info}")
                        }
                    }
                }
            }

            // 查询所有进行中的会议
            val ongoingSearch = MeetingSearch()
            ongoingSearch.status = 1
            ongoingSearch.ifPage = false
            val ongoingMeetings = meetingMapper.getList(ongoingSearch)
            for (meeting in ongoingMeetings) {
                if (meeting.endTime != null && meeting.endTime!!.after(Date())) {
                    scheduleEndTask(meeting)
                    log.info("Registered end task for ongoing meeting [${meeting.id}] ${meeting.title}, scheduled end time: ${meeting.endTime}")
                } else if (meeting.endTime != null && meeting.endTime!!.before(Date())) {
                    // 结束时间已过但仍为进行中状态，尝试再次修复
                    log.warn("Found abnormal meeting [${meeting.id}] ${meeting.title}, end time passed but status still in progress, fixing...")
                    val result = meetingService.updateMeetingStatusOnly(meeting.id, 3)
                    if (result.rlt == Result.SUCCESS) {
                        log.info("Meeting [${meeting.id}] ${meeting.title} status fixed: In Progress -> Ended")
                    } else {
                        log.error("Failed to fix meeting [${meeting.id}] status: ${result.info}")
                    }
                }
            }

            log.info("Meeting status scheduler initialization completed")
        } catch (e: Exception) {
            errorlog(e)
            log.error("Meeting status scheduler initialization failed: ${e.message}")
        }
    }

    /**
     * 修复可能存在的会议状态错误
     * 处理因系统故障或其他原因导致的状态未更新的会议
     */
    private fun fixMeetingStatus() {
        try {
            log.info("Starting meeting status recovery process...")

            // 1. 处理需要直接标记为已结束的会议
            // 状态为待开始(0)，但开始时间和结束时间都已过
            val directEndMeetings = meetingMapper.getMeetingsNeedToMarkAsEnded()

            if (directEndMeetings.isNotEmpty()) {
                log.info("Found ${directEndMeetings.size} meetings need to mark as ended directly")

                for (meeting in directEndMeetings) {
                    try {
                        val oldStatus = meeting.status
                        // 使用直接更新方法，绕过会议室验证
                        val result = meetingService.updateMeetingStatusOnly(meeting.id, 3)
                        if (result.rlt == Result.SUCCESS) {
                            log.info("Meeting [${meeting.id}] ${meeting.title} status fixed: ${getStatusText(oldStatus)} -> Ended")
                        } else {
                            log.error("Failed to fix meeting [${meeting.id}] status: ${result.info}")
                        }
                    } catch (e: Exception) {
                        log.error("Failed to fix meeting [${meeting.id}] status: ${e.message}")
                        errorlog(e)
                    }
                }
            }

            // 2. 处理需要立即开始的会议
            // 状态为待开始(0)，且当前时间已经超过开始时间但未超过结束时间
            val needStartMeetings = meetingMapper.getMeetingsNeedToStart()

            if (needStartMeetings.isNotEmpty()) {
                log.info("Found ${needStartMeetings.size} meetings need to start immediately")

                for (meeting in needStartMeetings) {
                    try {
                        val oldStatus = meeting.status
                        // 使用直接更新方法，绕过会议室验证
                        val result = meetingService.updateMeetingStatusOnly(meeting.id, 1)
                        if (result.rlt == Result.SUCCESS) {
                            log.info("Meeting [${meeting.id}] ${meeting.title} status fixed: ${getStatusText(oldStatus)} -> In Progress")

                            // 如果结束时间在未来，注册结束任务
                            if (meeting.endTime != null && meeting.endTime!!.after(Date())) {
                                scheduleEndTask(meeting)
                                log.info("Registered end task for meeting [${meeting.id}] ${meeting.title}")
                            }
                        } else {
                            log.error("Failed to fix meeting [${meeting.id}] status: ${result.info}")
                        }
                    } catch (e: Exception) {
                        log.error("Failed to fix meeting [${meeting.id}] status: ${e.message}")
                        errorlog(e)
                    }
                }
            }

            // 3. 处理需要立即结束的会议
            // 状态为进行中(1)，且当前时间已经超过结束时间
            val needEndMeetings = meetingMapper.getMeetingsNeedToEnd()

            if (needEndMeetings.isNotEmpty()) {
                log.info("Found ${needEndMeetings.size} meetings need to end immediately")

                for (meeting in needEndMeetings) {
                    try {
                        val oldStatus = meeting.status
                        // 使用直接更新方法，绕过会议室验证
                        val result = meetingService.updateMeetingStatusOnly(meeting.id, 3)
                        if (result.rlt == Result.SUCCESS) {
                            log.info("Meeting [${meeting.id}] ${meeting.title} status fixed: ${getStatusText(oldStatus)} -> Ended")
                        } else {
                            log.error("Failed to fix meeting [${meeting.id}] status: ${result.info}")
                        }
                    } catch (e: Exception) {
                        log.error("Failed to fix meeting [${meeting.id}] status: ${e.message}")
                        errorlog(e)
                    }
                }
            }

            // 记录总体状态修复情况
            val totalFixed = directEndMeetings.size + needStartMeetings.size + needEndMeetings.size
            if (totalFixed > 0) {
                log.info("Meeting status recovery completed, fixed $totalFixed abnormal status meetings")
            } else {
                log.info("No abnormal status meetings found, no fix needed")
            }
        } catch (e: Exception) {
            log.error("Error during meeting status recovery process: ${e.message}")
            errorlog(e)
        }
    }

    /**
     * 根据状态码返回对应的文本描述
     */
    private fun getStatusText(status: Int): String {
        return when(status) {
            0 -> "Waiting"
            1 -> "In Progress"
            2 -> "Cancelled"
            3 -> "Ended"
            else -> "Unknown($status)"
        }
    }

    /**
     * 每小时执行一次的全表扫描，作为兜底机制
     * 防止由于系统重启或其他原因导致的定时任务丢失
     */
    @Scheduled(cron = "0 0 * * * ?") // 每小时整点执行一次
    fun scanAndUpdateMeetingStatus() {
        try {
            log.info("Starting scheduled meeting status scan...")

            // 调用会议状态更新方法
            meetingService.updateMeetingStatus()

            // 额外检查并修复可能存在的状态错误
            fixMeetingStatus()

            // 检查修复后是否还有异常状态
            val additionalFixRequired = verifyMeetingStatus()
            if (additionalFixRequired) {
                log.warn("Abnormal status meetings still detected after first fix, performing secondary fix...")
                fixMeetingStatus()

                // 再次验证
                val stillHasAbnormal = verifyMeetingStatus()
                if (stillHasAbnormal) {
                    log.error("Abnormal status meetings still exist after multiple fixes, possible data consistency issue, please check database")
                }
            }

            // 检查是否有需要重新注册的定时任务
            recheckScheduledTasks()

            log.info("Scheduled meeting status scan completed")
        } catch (e: Exception) {
            errorlog(e)
            log.error("Error during scheduled meeting status scan: ${e.message}")
        }
    }

    /**
     * 重新检查并注册可能丢失的定时任务
     */
    private fun recheckScheduledTasks() {
        try {
            val now = Date()
            val oneHourLater = Date(now.time + 3600000) // 当前时间后一小时

            // 查询未来一小时内需要开始或结束的会议
            val upcomingMeetings = meetingMapper.getActiveOrUpcomingMeetingsInTimeRange(now, oneHourLater)

            for (meeting in upcomingMeetings) {
                // 对于待开始的会议，检查是否已注册开始任务
                if (meeting.status == 0 && meeting.startTime != null && meeting.startTime!!.after(now)) {
                    if (!startTaskMap.containsKey(meeting.id)) {
                        scheduleStartTask(meeting)
                        log.info("Re-registered start task for meeting [${meeting.id}] ${meeting.title}")
                    }
                }

                // 对于进行中的会议，检查是否已注册结束任务
                if (meeting.status == 1 && meeting.endTime != null && meeting.endTime!!.after(now)) {
                    if (!endTaskMap.containsKey(meeting.id)) {
                        scheduleEndTask(meeting)
                        log.info("Re-registered end task for meeting [${meeting.id}] ${meeting.title}")
                    }
                }
            }
        } catch (e: Exception) {
            errorlog(e)
        }
    }

    /**
     * 为会议注册开始状态变更的定时任务
     */
    fun scheduleStartTask(meeting: Meeting) {
        if (meeting.id.isEmpty() || meeting.startTime == null) {
            return
        }

        // 取消已存在的任务
        cancelStartTask(meeting.id)

        // 如果开始时间已过，则不需要注册任务
        if (meeting.startTime!!.before(Date())) {
            return
        }

        // 注册新任务
        val startInstant = meeting.startTime!!.toInstant()
        val task = taskScheduler.schedule({
            try {
                updateMeetingToOngoing(meeting.id)
            } catch (e: Exception) {
                errorlog(e)
            } finally {
                // 任务执行完成后从Map中移除
                startTaskMap.remove(meeting.id)
            }
        }, startInstant)

        // 将任务保存到Map中
        startTaskMap[meeting.id] = task
    }

    /**
     * 为会议注册结束状态变更的定时任务
     */
    fun scheduleEndTask(meeting: Meeting) {
        if (meeting.id.isEmpty() || meeting.endTime == null) {
            return
        }

        // 取消已存在的任务
        cancelEndTask(meeting.id)

        // 如果结束时间已过，则不需要注册任务
        if (meeting.endTime!!.before(Date())) {
            return
        }

        // 注册新任务
        val endInstant = meeting.endTime!!.toInstant()
        val task = taskScheduler.schedule({
            try {
                updateMeetingToEnded(meeting.id)
            } catch (e: Exception) {
                errorlog(e)
            } finally {
                // 任务执行完成后从Map中移除
                endTaskMap.remove(meeting.id)
            }
        }, endInstant)

        // 将任务保存到Map中
        endTaskMap[meeting.id] = task
    }

    /**
     * 取消会议开始状态变更的定时任务
     */
    fun cancelStartTask(meetingId: String) {
        val task = startTaskMap.remove(meetingId)
        task?.cancel(false)
    }

    /**
     * 取消会议结束状态变更的定时任务
     */
    fun cancelEndTask(meetingId: String) {
        val task = endTaskMap.remove(meetingId)
        task?.cancel(false)
    }

    /**
     * 更新会议状态为进行中
     */
    private fun updateMeetingToOngoing(meetingId: String) {
        val meeting = meetingMapper.getInfo(meetingId)
        if (meeting != null && meeting.status == 0) { // 确保会议仍处于待开始状态
            // 使用直接更新方法，绕过会议室验证
            val result = meetingService.updateMeetingStatusOnly(meetingId, 1)
            if (result.rlt == Result.SUCCESS) {
                // 记录日志
                log.info("Meeting [${meeting.id}] ${meeting.title} status updated: Waiting -> In Progress")

                // 如果会议有结束时间，注册结束任务
                if (meeting.endTime != null && meeting.endTime!!.after(Date())) {
                    scheduleEndTask(meeting)
                }
            } else {
                log.error("Failed to update meeting [${meeting.id}] status: ${result.info}")
            }
        }
    }

    /**
     * 更新会议状态为已结束
     */
    private fun updateMeetingToEnded(meetingId: String) {
        val meeting = meetingMapper.getInfo(meetingId)
        if (meeting != null && meeting.status == 1) { // 确保会议仍处于进行中状态
            // 使用直接更新方法，绕过会议室验证
            val result = meetingService.updateMeetingStatusOnly(meetingId, 3)
            if (result.rlt == Result.SUCCESS) {
                // 记录日志
                log.info("Meeting [${meeting.id}] ${meeting.title} status updated: In Progress -> Ended")
            } else {
                log.error("Failed to update meeting [${meeting.id}] status: ${result.info}")
            }
        }
    }

    /**
     * 验证会议状态是否有遗漏的异常状态
     * 返回是否需要再次执行修复逻辑
     */
    private fun verifyMeetingStatus(): Boolean {
        val abnormalCount = meetingMapper.getMeetingsNeedToMarkAsEnded().size +
                meetingMapper.getMeetingsNeedToStart().size +
                meetingMapper.getMeetingsNeedToEnd().size

        return abnormalCount > 0
    }
}
