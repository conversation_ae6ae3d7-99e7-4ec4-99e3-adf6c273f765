package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.errorlog
import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.text.SimpleDateFormat
import java.util.*

/**
 * 会议室实体类
 */
class Meetingroom : BaseModel() {
    /** 会议室名称 */
    var name: String = ""
    /** 所在楼层 */
    var floor: Int? = null
    /** 容纳人数 */
    var capacity: Int? = null
    /** 是否有投影仪(0-无,1-有) */
    var hasProjector: Int = 0
    /** 是否有麦克风(0-无,1-有) */
    var hasMicrophone: Int = 0
    /** 是否有白板(0-无,1-有) */
    var hasWhiteboard: Int = 0
    /** 是否有笔记本(0-无,1-有) */
    var hasNotebook: Int = 0
    /** 是否有饮水机(0-无,1-有) */
    var hasWaterDispenser: Int = 0
    /** 会议室描述 */
    var description: String = ""
    /** 状态(0-禁用,1-启用) */
    var status: Int = 1

    /** 关联的会议列表 */
    var meetingList: List<Meeting> = listOf()

    /**
     * 会议室设备信息，格式：投影仪 麦克风 白板 笔记本 饮水机
     */
    val deviceInfo: String
        get() {
            val devices = mutableListOf<String>()
            if (hasProjector == 1) devices.add("投影仪")
            if (hasMicrophone == 1) devices.add("麦克风")
            if (hasWhiteboard == 1) devices.add("白板")
            if (hasNotebook == 1) devices.add("笔记本")
            if (hasWaterDispenser == 1) devices.add("饮水机")
            return devices.joinToString(" ")
        }
}

/**
 * 会议室查询条件类
 */
class MeetingroomSearch : BaseSearch() {
    /** 搜索关键词 */
    var keyword: String = ""
    /** 所在楼层 */
    var floor: Int? = null
    /** 最小容纳人数 */
    var minCapacity: Int? = null
    /** 可用性检查-开始时间（yyyy-MM-dd HH:mm格式），用于检查会议室是否可预订 */
    var availabilityStartTime: String = ""
    /** 可用性检查-结束时间（yyyy-MM-dd HH:mm格式），用于检查会议室是否可预订 */
    var availabilityEndTime: String = ""
    /** 排除的会议ID，用于更新会议时避免与自身冲突 */
    var excludeMeetingId: String = ""
    /** 是否加载关联的会议列表 */
    var loadMeetingList: Boolean = false
    /** 是否只显示活跃和未来会议(待开始和进行中) */
    var onlyActiveAndFutureMeetings: Boolean = false
}

/**
 * 会议室Mapper接口
 */
@Mapper
interface MeetingroomMapper : BaseMapper<Meetingroom> {
    /**
     * 使用动态SQL查询会议室列表
     * 支持按楼层和名称关键词搜索
     * 支持按最小容量筛选
     * 支持按时间段可用性筛选
     */
    @Select("""
        <script>
            SELECT DISTINCT r.* FROM tbl_meetingroom r
            <where>
                r.sysDeleted = 0
                <if test="floor != null">
                    AND r.floor = #{floor}
                </if>
                <if test="keyword != ''">
                    AND r.name LIKE CONCAT('%', #{keyword}, '%')
                </if>
                <if test="minCapacity != null">
                    AND r.capacity >= #{minCapacity}
                </if>
                <if test="availabilityStartTime != '' and availabilityEndTime != ''">
                    AND NOT EXISTS (
                        SELECT 1 FROM tbl_meeting m
                        WHERE m.meetingroomId = r.id
                        AND m.sysDeleted = 0
                        AND m.status IN (0, 1) <!-- 只检查待开始和进行中的会议 -->
                        AND (
                            (m.startTime &lt;= #{availabilityStartTime} AND m.endTime > #{availabilityStartTime})
                            OR (m.startTime &lt; #{availabilityEndTime} AND m.endTime >= #{availabilityEndTime})
                            OR (m.startTime >= #{availabilityStartTime} AND m.endTime &lt;= #{availabilityEndTime})
                        )
                        <if test="excludeMeetingId != ''">
                            AND m.id != #{excludeMeetingId}
                        </if>
                    )
                </if>
            </where>
            ORDER BY r.floor, r.name
        </script>
    """)
    override fun getList(search: BaseSearch): List<Meetingroom>
}

/**
 * 会议室服务类
 */
@Service
class MeetingroomService(
    mapper: MeetingroomMapper,
    private val meetingService: MeetingService
) : BaseService<Meetingroom, MeetingroomMapper>(mapper) {

    override fun getInfo(id: String): Result {
        val meetingroom = mapper.getInfo(id)
        if (meetingroom != null) {
            val meetingSearch = MeetingSearch()
            meetingSearch.meetingroomId = id
            meetingSearch.ifPage = false

            meetingroom.meetingList = meetingService.getDataList(meetingSearch)
        }
        return Result.getSuccess(meetingroom)
    }

    override fun getList(page: BaseSearch): Result {
        val search = page as MeetingroomSearch
        val result = super.getList(page)

        if (search.loadMeetingList) {
            loadMeetingLists(result.toList(), search.onlyActiveAndFutureMeetings)
        }

        return result
    }

    private fun loadMeetingLists(meetingrooms: List<Meetingroom>, onlyActiveAndFutureMeetings: Boolean) {
        for (meetingroom in meetingrooms) {
            val meetingSearch = MeetingSearch()
            meetingSearch.meetingroomId = meetingroom.id
            meetingSearch.ifPage = false

            // 如果需要只显示活跃和未来会议，设置状态过滤条件
            if (onlyActiveAndFutureMeetings) {
                // 使用endTimeBegin而不是startTimeBegin来过滤
                // 只获取结束时间在当前时间之后的会议（即未结束的会议）
                meetingSearch.endTimeBegin = SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date())

                // 使用statusList过滤状态，只查询待开始(0)和进行中(1)的会议
                meetingSearch.statusList = listOf(0, 1)
            }

            meetingroom.meetingList = meetingService.getDataList(meetingSearch)
        }
    }

    /**
     * 智能推荐会议室
     * 根据会议开始时间、结束时间和参会人数，推荐适合的会议室
     * 暂时实现为随机选择一个符合条件的会议室
     */
    fun recommendMeetingroom(startTimeStr: String, endTimeStr: String, attendeeCount: Int): Result {
        try {
            // 解析时间字符串
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm")
            val startTime: Date
            val endTime: Date

            try {
                startTime = dateFormat.parse(startTimeStr)
                endTime = dateFormat.parse(endTimeStr)
            } catch (e: Exception) {
                return Result.getError("时间格式错误，请使用格式：yyyy-MM-dd HH:mm")
            }

            if (startTime.after(endTime)) {
                return Result.getError("开始时间不能晚于结束时间")
            }

            // 使用符合条件的会议室查询：
            // - 容量满足要求
            // - 指定时间段内无时间冲突
            val meetingroomSearch = MeetingroomSearch()
            meetingroomSearch.ifPage = false
            meetingroomSearch.minCapacity = attendeeCount

            // 使用相同的时间格式 yyyy-MM-dd HH:mm
            meetingroomSearch.availabilityStartTime = startTimeStr
            meetingroomSearch.availabilityEndTime = endTimeStr

            // 获取符合条件的会议室列表
            val meetingrooms: List<Meetingroom> = this.getDataList(meetingroomSearch)

            // 随机选择一个符合条件的会议室
            val random = Random()
            val recommendedMeetingroom = meetingrooms[random.nextInt(meetingrooms.size)]
            log.info("Recommended meetingroom: [${recommendedMeetingroom.id}] ${recommendedMeetingroom.name} for $attendeeCount attendees from $startTimeStr to $endTimeStr")

            return Result.getSuccess(recommendedMeetingroom)
        } catch (e: Exception) {
            errorlog(e)
            return Result.getError("推荐会议室失败：${e.message}")
        }
    }
}

/**
 * 会议室控制器
 */
@RestController
@RequestMapping("/api/Meetingroom")
class MeetingroomResource(service: MeetingroomService) : BaseResource<MeetingroomSearch, Meetingroom, MeetingroomMapper, MeetingroomService>(service) {

    /**
     * 智能推荐会议室API
     * 根据会议开始时间、结束时间和参会人数，推荐适合的会议室
     */
    @GetMapping("/recommend")
    fun recommendMeetingroom(
        @RequestParam startTime: String,
        @RequestParam endTime: String,
        @RequestParam attendeeCount: Int
    ): Result {
        return service.recommendMeetingroom(startTime, endTime, attendeeCount)
    }
}
