package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import kotlin.system.measureTimeMillis

class Menu: BaseModel {
    var menuDate: String = ""
    var timePeriod: String = ""
    var sysDeleted: Int = 0

    var menuDishList: MutableList<Menudish> = mutableListOf()

    constructor()

    constructor(menuDate: String, timePeriod: String) {
        this.id = uuid()
        this.menuDate = menuDate
        this.timePeriod = timePeriod
    }
}

enum class MealTime(val value: String) {
    MORNING("morning"),
    AFTERNOON("afternoon");

    override fun toString(): String = value
}

class MenuSearch: BaseSearch() {
    var menuId: String = ""
    var menuDate: String = ""
    var timePeriod: String = ""
    // 菜品列表
    var dishList: List<Dish> = listOf()
}

@Mapper
interface MenuMapper : BaseMapper<Menu> {
    @Select("""
        <script>
            SELECT * FROM tbl_menu
            <where>
                AND sysDeleted = 0
                <if test="menuDate != ''">
                    AND DATE(menuDate) = #{menuDate}
                </if>
                <if test="timePeriod != ''">
                    AND timePeriod = #{timePeriod}
                </if>
            </where>
            ORDER BY menuDate DESC, timePeriod ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Menu>

    // 校验menuDate是否存在
    @Select("SELECT COUNT(*) FROM tbl_menu WHERE menuDate = #{menuDate} AND timePeriod = #{timePeriod} AND sysDeleted = 0")
    fun checkMenuDateExists(menuDate: String, timePeriod: String): Int

    @Select("SELECT * FROM tbl_menu WHERE menuDate = #{menuDate} AND sysDeleted = 0")
    fun getMenuByDate(menuDate: String): List<Menu>
}

@Service
class MenuService(mapper: MenuMapper) : BaseService<Menu, MenuMapper>(mapper) {
    override fun save(model: Menu): Result {
        val menuDishMapper = getBean(MenudishMapper::class.java)
        val dishMapper = getBean(DishMapper::class.java)

        return try {
            val time = measureTimeMillis {
                // 获取当天菜谱
                val menu = mapper.getMenuByDate(model.menuDate).find {
                    it.timePeriod == model.timePeriod
                } ?: return Result.getError("当前日期未开放")

                // 保存菜谱详细
                menuDishMapper.insertList(model.menuDishList.mapIndexed {  index, menuDish ->
                    Menudish(menu.id, index, menuDish)
                })

                // 同步菜品库
                model.menuDishList.filter { it.isSynced }.apply {
                    log.info("Syncing $size dishes to dish library")
                }.forEach { menuDish ->
                    dishMapper.syncDishByMenuDish(menuDish)
                }
            }

            log.info("Menu saved successfully, cost $time ms")

            Result.getSuccess(model.id)
        } catch (e: Exception) {
            log.error("Failed to save menu: ${e.message}")
            Result.getError("菜谱发布失败: ${e.message}")
        }
    }

    fun getMenuByDate(menuDate: String): Result {
        val menu = mapper.getMenuByDate(menuDate).onEach { menu ->
            val menuDishList = getBean(MenudishMapper::class.java).getList(MenudishSearch().apply { menuId = menu.id })
            menu.menuDishList.addAll(menuDishList)
        }
        return Result.getSuccess(menu)
    }
}

@RestController
@RequestMapping("/api/menu")
class MenuResource(service: MenuService) : BaseResource<MenuSearch, Menu, MenuMapper, MenuService>(service) {
    /**
     * 发布菜谱
     */
    @PostMapping("/save")
    override fun save(@RequestBody model: Menu): Result {
        return try {
            service.save(model)
            Result.getSuccessInfo("保存菜谱成功")
        } catch (e: Exception) {
            log.error("保存菜谱失败: ${e.message}")
            Result.getError("保存菜谱失败: ${e.message}")
        }
    }

    /**
     * 根据日期获取菜谱
     */
    @GetMapping("/getMenuByDate/{menuDate}")
    fun getMenuByDate(@PathVariable menuDate: String): Result {
        return try {
            service.getMenuByDate(menuDate)
        } catch (e: Exception) {
            log.error("获取菜谱失败: ${e.message}")
            Result.getError("获取菜谱失败: ${e.message}")
        }
    }
}
