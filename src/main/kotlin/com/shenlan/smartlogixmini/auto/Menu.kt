package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Menu: BaseModel() {
    var menuDate: String = ""
    var timePeriod: String = ""
    var sysDeleted: Int = 0
}

class MenuSearch: BaseSearch() {
    var menuDate: String = ""
    var timePeriod: String = ""
}

@Mapper
interface MenuMapper : BaseMapper<Menu> {
    @Select("""
        <script>
            SELECT * FROM tbl_menu
            <where>
                AND sysDeleted = 0
                <if test="menuDate != ''">
                    AND DATE(menuDate) = #{menuDate}
                </if>
                <if test="timePeriod != ''">
                    AND timePeriod = #{timePeriod}
                </if>
            </where>
            ORDER BY menuDate DESC, timePeriod ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Menu>
}

@Service
class MenuService(mapper: MenuMapper) : BaseService<Menu, MenuMapper>(mapper)

@RestController
@RequestMapping("/api/menu")
class MenuResource(service: MenuService) : BaseResource<MenuSearch, Menu, MenuMapper, MenuService>(service)
