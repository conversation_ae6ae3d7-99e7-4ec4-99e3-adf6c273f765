package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.MenuUtil
import com.shenlan.smartlogixmini.util.NotifyMessageUtil.saveNotifyMessage
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDate
import kotlin.system.measureTimeMillis

class Menu: BaseModel {
    var menuDate: String = ""
    var timePeriod: String = ""
    var sysDeleted: Int = 0
    var published: Boolean = false

    var menuDishList: MutableList<Menudish> = mutableListOf()

    constructor()

    constructor(menuDate: String, timePeriod: String) {
        this.id = uuid()
        this.menuDate = menuDate
        this.timePeriod = timePeriod
    }
}

data class MenuResponse(
    val morningMenu: Menu?,
    val afternoonMenu: Menu?,
    val notifyMessage: Notifymessage?
)

enum class MealTime(val value: String) {
    MORNING("morning"),
    AFTERNOON("afternoon");

    override fun toString(): String = value
}

class MenuSearch: BaseSearch() {
    var menuId: String = ""
    var menuDate: String = ""
    var timePeriod: String = ""
    // 菜品列表
    var dishList: List<Dish> = listOf()
    var menuList: List<Menu> = listOf()
}

@Mapper
interface MenuMapper : BaseMapper<Menu> {
    @Select("""
        <script>
            SELECT * FROM tbl_menu
            <where>
                AND sysDeleted = 0
                <if test="menuDate != ''">
                    AND DATE(menuDate) = #{menuDate}
                </if>
                <if test="timePeriod != ''">
                    AND timePeriod = #{timePeriod}
                </if>
            </where>
            ORDER BY menuDate DESC, timePeriod ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Menu>

    // 校验menuDate是否存在
    @Select("SELECT COUNT(*) FROM tbl_menu WHERE menuDate = #{menuDate} AND timePeriod = #{timePeriod} AND sysDeleted = 0")
    fun checkMenuDateExists(menuDate: String, timePeriod: String): Int

    @Select("SELECT * FROM tbl_menu WHERE menuDate = #{menuDate} AND sysDeleted = 0")
    fun getMenuByDate(menuDate: String): List<Menu>

    // 更新published状态
    @Update("UPDATE tbl_menu SET published = #{published} WHERE id = #{id}")
    fun updatePublished(id: String, published: Boolean)
}

@Service
class MenuService(mapper: MenuMapper) : BaseService<Menu, MenuMapper>(mapper) {
    override fun save(model: Menu): Result {
        val menuDishMapper = getBean(MenudishMapper::class.java)
        val dishMapper = getBean(DishMapper::class.java)

        return try {
            // 1.1 保存菜谱信息
            val time = measureTimeMillis {
                // 获取当天菜谱
                val menu = mapper.getMenuByDate(model.menuDate).find {
                    it.timePeriod == model.timePeriod
                } ?: return Result.getError("当前日期未开放")

                // 保存菜谱详细
                menuDishMapper.insertList(model.menuDishList.mapIndexed {  index, menuDish ->
                    Menudish(menu.id, index, menuDish)
                })

                // 同步菜品库
                model.menuDishList.filter { it.isSynced }.apply {
                    log.info("Syncing $size dishes to dish library")
                }.forEach { menuDish ->
                    dishMapper.syncDishByMenuDish(menuDish)
                }

                mapper.updatePublished(menu.id, true)
            }

            log.info("Menu saved successfully, cost $time ms")
            Result.getSuccess(model.id)
        } catch (e: Exception) {
            log.error("Failed to save menu: ${e.message}")
            Result.getError("菜谱发布失败: ${e.message}")
        }
    }

    fun getMenuByDate(menuDate: String, userId: String): Result {
        val menu = mapper.getMenuByDate(menuDate).onEach { menu ->
            val menuDishList = getBean(MenudishMapper::class.java).getList(MenudishSearch().apply { menuId = menu.id })
            menu.menuDishList.addAll(menuDishList)
        }

        if (menu.isEmpty()) return Result.getError("当前日期未开放")

        val menuResponse = MenuResponse(
            menu.find { it.timePeriod == MealTime.MORNING.toString() },
            menu.find { it.timePeriod == MealTime.AFTERNOON.toString() },
            getBean(NotifymessageMapper::class.java).getListByUserIdAndModuleAndType(userId, NotifyModule.MENU.toString(), "management").elementAtOrNull(0)
        )

        return Result.getSuccess(menuResponse)
    }
}

@RestController
@RequestMapping("/api/menu")
class MenuResource(service: MenuService) : BaseResource<MenuSearch, Menu, MenuMapper, MenuService>(service) {
    /**
     * 发布菜谱
     */
    @PostMapping("/save")
    override fun save(@RequestBody model: Menu): Result {
        return try {
            service.save(model)
            Result.getSuccessInfo("保存菜谱成功")
        } catch (e: Exception) {
            log.error("保存菜谱失败: ${e.message}")
            Result.getError("保存菜谱失败: ${e.message}")
        }
    }

    /**
     * 批量发布菜谱
     */
    @PostMapping("/batchSave")
    fun batchSave(@RequestBody search: MenuSearch): Result {
        return try {
            search.menuList.forEach {
                try {
                    service.save(it)
                } catch (e: Exception) {
                    log.error("保存菜谱${it.toJsonString}失败")
                }
            }

            // 保存通知信息
            val userIds = getBean(PersonnelMapper::class.java).getAllPersonnelIds()
            saveNotifyMessage(userIds,
                NotifyModule.MENU.value,
                "normal",
                "每周菜谱通知",
                "下周的「菜谱盲盒」已解锁，快来查收吧！",
                search.menuList.joinToString(",") { it.menuDate })

            Result.getSuccessInfo("批量保存菜谱成功")
        } catch (e: Exception) {
            log.error("批量保存菜谱失败: ${e.message}")
            Result.getError("保存菜谱失败: ${e.message}")
        }
    }

    /**
     * 获取本周菜谱日期
     */
    @GetMapping("/getMenuDateList")
    fun getMenuDateList(): Result {
        return try {
            Result.getSuccess(MenuUtil.getMenuDateList(LocalDate.now()))
        } catch (e: Exception) {
            log.error("获取菜谱日期失败: ${e.message}")
            Result.getError("获取菜谱日期失败: ${e.message}")
        }
    }


    /**
     * 根据日期获取菜谱
     */
    @GetMapping("/getMenuByDate/{menuDate}/{userId}")
    fun getMenuByDate(@PathVariable menuDate: String, @PathVariable userId: String): Result {
        return try {
            service.getMenuByDate(menuDate, userId)
        } catch (e: Exception) {
            log.error("获取菜谱失败: ${e.message}")
            Result.getError("获取菜谱失败: ${e.message}")
        }
    }
}
