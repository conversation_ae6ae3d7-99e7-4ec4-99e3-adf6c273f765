package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.errorlog
import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * 数据库表管理服务
 */
@Mapper
interface DatabaseMapper {
    @Update("CREATE TABLE IF NOT EXISTS \${tableName}\${monthSuffix} like \${tableName}")
    fun createMonthlyTable(@Param("tableName") tableName: String, @Param("monthSuffix") monthSuffix: String)

    @Select("SELECT count(*) FROM information_schema.TABLES WHERE table_schema = schema() and table_name = #{tableName}")
    fun existsTable(@Param("tableName") tableName: String): Int
}

@Component
class DatabaseScheduler(
    private val databaseMapper: DatabaseMapper
) {

    // 需要按月创建的表名列表
    private val monthlyTableNameList = listOf<String>()
    // 日期格式化器
    private val dateFormatter = DateTimeFormatter.ofPattern("yyyyMM")

    /**
     * 批量创建表
     * 每隔10天创建新表，格式如 tbl_historical201911
     */
    @Scheduled(fixedDelay = (10 * 24 * 60 * 60 * 1000).toLong())
    fun batchCreateMonthlyTables(): Result {
        log.info("Starting to create tables")

        monthlyTableNameList.forEach { tableName ->
            createTablesForPeriod(tableName)
        }

        log.info("Tables creation completed")
        return Result.success
    }

    /**
     * 为指定表名创建前、当前、后三个月的表
     */
    private fun createTablesForPeriod(tableName: String) {
        try {
            val currentDate = LocalDate.now()

            // 创建上个月、当前月、下个月的表
            createTableForMonth(tableName, currentDate.minusMonths(1))
            createTableForMonth(tableName, currentDate)
            createTableForMonth(tableName, currentDate.plusMonths(1))

            log.info("Created tables for $tableName")
        } catch (e: Exception) {
            log.error("Failed to create tables for $tableName")
            errorlog(e)
        }
    }

    /**
     * 创建指定日期的表
     */
    private fun createTableForMonth(tableName: String, localDate: LocalDate) {
        val monthSuffix = localDate.format(dateFormatter)
        val fullTableName = "$tableName$monthSuffix"

        if (databaseMapper.existsTable(fullTableName) == 0) {
            databaseMapper.createMonthlyTable(tableName, monthSuffix)
            log.info("Created table: $fullTableName")
        } else {
            log.info("Table already exists: $fullTableName")
        }
    }
}
