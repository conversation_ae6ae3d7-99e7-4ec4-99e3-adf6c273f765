package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Commutevehiclelocation: BaseEntity() {
    var carId: String = ""
    var lat: Double = 0.0
    var lng: Double = 0.0
    var speed: Double = 0.0
    var drct: Int = 0
    var drctCn: String = ""
    var stateCn: String = ""
    var address: String = ""
    var time: String = ""
    var tmnKey: String = ""
    var runStopTime: String = ""
    var sysDeleted: Int = 0
}

class CommutevehiclelocationSearch: BaseSearch() {
    var carId: String = ""
    var stateCn: String = ""
    var address: String = ""
    var tmnKey: String = ""
}

@Mapper
interface CommutevehiclelocationMapper : BaseMapper<Commutevehiclelocation> {
    @Select("""
        <script>
            SELECT * FROM tbl_commutevehiclelocation
            <where>
                AND sysDeleted = 0
                <if test="carId != ''">
                    AND carId = #{carId}
                </if>
                <if test="stateCn != ''">
                    AND stateCn LIKE CONCAT('%', #{stateCn}, '%')
                </if>
                <if test="address != ''">
                    AND address LIKE CONCAT('%', #{address}, '%')
                </if>
                <if test="tmnKey != ''">
                    AND tmnKey = #{tmnKey}
                </if>
            </where>
            ORDER BY time DESC, sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutevehiclelocation>

    // 批量更新实时位置信息
    @Update("""
        <script>
        UPDATE tbl_commutevehiclelocation
        <set>
            lat = CASE id
            <foreach collection="locations" item="location" separator=",">
                WHEN #{location.id} THEN #{location.lat}
            </foreach>
            END,
            lng = CASE id
            <foreach collection="locations" item="location" separator=",">
                WHEN #{location.id} THEN #{location.lng}
            </foreach>
            END,
            speed = CASE id
            <foreach collection="locations" item="location" separator=",">
                WHEN #{location.id} THEN #{location.speed}
            </foreach>
            END,
            drct = CASE id
            <foreach collection="locations" item="location" separator=",">
                WHEN #{location.id} THEN #{location.drct}
            </foreach>
            END,
            drctCn = CASE id
            <foreach collection="locations" item="location" separator=",">
                WHEN #{location.id} THEN #{location.drctCn}
                </foreach>
            END,
            stateCn = CASE id
            <foreach collection="locations" item="location" separator=",">
                WHEN #{location.id} THEN #{location.stateCn}
            </foreach>
            END,
            address = CASE id
            <foreach collection="locations" item="location" separator=",">
                WHEN #{location.id} THEN #{location.address}
            </foreach>
            END,
            time = CASE id
            <foreach collection="locations" item="location" separator=",">
                WHEN #{location.id} THEN #{location.time}
            </foreach>
            END,
            tmnKey = CASE id
            <foreach collection="locations" item="location" separator=",">
                WHEN #{location.id} THEN #{location.tmnKey}
            </foreach>
            END,
            runStopTime = CASE id
            <foreach collection="locations" item="location" separator=",">
                WHEN #{location.id} THEN #{location.runStopTime}
            </foreach>
            END
        </set>
        WHERE id IN
        <foreach collection="locations" item="location" open="(" separator="," close=")">
            #{location.id}
        </foreach>
        </script>
    """)
    fun batchUpdateLocation(@Param("locations") list: List<Commutevehiclelocation>): Int

    // 获取所有班车id
    @Select("""
        SELECT DISTINCT id FROM tbl_commutevehiclelocation
    """)
    fun getAllCarId(): List<String>

    // 根据id更新经纬度
    @Update("""
        UPDATE tbl_commutevehiclelocation
        SET lat = #{lat}, lng = #{lng}
        WHERE id = #{id}
    """)
    fun updateLocation(id: String, lat: Double, lng: Double)
}

@Service
class CommutevehiclelocationService(mapper: CommutevehiclelocationMapper) : BaseService<Commutevehiclelocation, CommutevehiclelocationMapper, CommutevehiclelocationSearch>(mapper)

@RestController
@RequestMapping("/api/commutevehiclelocation")
class CommutevehiclelocationResource(service: CommutevehiclelocationService) : BaseResource<CommutevehiclelocationSearch, Commutevehiclelocation, CommutevehiclelocationMapper, CommutevehiclelocationService>(service)
