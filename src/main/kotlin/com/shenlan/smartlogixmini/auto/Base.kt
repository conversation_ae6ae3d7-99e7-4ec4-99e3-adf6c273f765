package com.shenlan.smartlogixmini.auto

import com.github.pagehelper.PageHelper
import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.mybatis.paginationInfo
import com.shenlan.smartlogixmini.util.notEmpty
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import java.io.Serializable
import java.util.*
import javax.validation.Valid

class Result {
    /** 0成功 1失败 */
    var rlt = 0
    var info = "success"
    var datas: Any? = null

    constructor()

    constructor(rlt: Int, info: String) {
        this.rlt = rlt
        this.info = info
    }

    constructor(rlt: Int, info: String, datas: Any) {
        this.rlt = rlt
        this.info = info
        this.datas = datas
    }

    constructor(datas: Any?) {
        this.datas = datas
    }

    fun <T> toList(): List<T> {
        return when (datas) {
            is PaginationInfo<*> -> {
                @Suppress("UNCHECKED_CAST")
                (datas as PaginationInfo<*>).result as? List<T> ?: listOf()
            }
            is List<*> -> {
                @Suppress("UNCHECKED_CAST")
                datas as? List<T> ?: listOf()
            }
            else -> listOf()
        }
    }

    companion object {
        const val SUCCESS = 0
        const val FAIL = 1
        val success = Result(0, "success")

        fun getSuccess(datas: Any?): Result {
            return Result(datas)
        }

        fun getError(info: String): Result {
            return Result(1, info)
        }

        fun getError(info: String, datas: Any): Result {
            return Result(1, info, datas)
        }

        fun getSuccessInfo(info: String) = Result(0, info)

        fun getError(status: ResultStatus): Result {
            return Result(status.code, status.description)
        }

        fun getError(status: ResultStatus, datas: Any): Result {
            return Result(status.code, status.description, datas)
        }
    }
}

/** 结果状态接口 */
interface ResultStatus {
    val code: Int
    val description: String
}

open class BaseEntity : Serializable {
    var id: String = ""
    var sysCreated: Date? = null
}

open class BaseSearch {
    var ifPage = true
    var currentPage: Int = 1
    var pageRecord: Int = 15
    var startTime: String = ""
    var endTime: String = ""
}

@Mapper
interface BaseMapper<EntityType : BaseEntity> {
    fun getList(search: BaseSearch = BaseSearch()): List<EntityType>

    fun getListByPid(pid: String): List<EntityType>

    fun getListByIds(ids: List<String>): List<EntityType>

    fun getInfo(id: String): EntityType?

    fun insert(model: BaseEntity): Int

    fun insertList(modelList: List<BaseEntity>): Int

    fun delete(id: String): Int

    fun deleteLogic(id: String): Int
}

/**
 * 新方法：getEntityPage、getEntityList、getEntity、saveEntity、deleteEntity、deleteEntityLogic
 * 老方法：getList、getInfo、save、delete、deleteLogic
 * 子类不能同时重写新方法和老方法，重写老方法时只能调用父类的老方法，重写新方法时只能调用父类的新方法
 */
open class BaseService<EntityType : BaseEntity, MapperType : BaseMapper<EntityType>, SearchType : BaseSearch>(var mapper: MapperType) {

    companion object {
        private val saveInProgress = ThreadLocal<Boolean>()
        private val getListInProgress = ThreadLocal<Boolean>()
        private val getInfoInProgress = ThreadLocal<Boolean>()
        private val deleteInProgress = ThreadLocal<Boolean>()
        private val deleteLogicInProgress = ThreadLocal<Boolean>()
    }

    /**
     * 实际执行查询的方法
     */
    private fun doGetEntityPage(search: SearchType): PaginationInfo<EntityType> {
        return if (!search.ifPage) {
            PaginationInfo.ofList(mapper.getList(search))
        } else {
            PageHelper.startPage<EntityType>(search.currentPage, search.pageRecord).doSelectPageInfo<EntityType> {
                mapper.getList(search)
            }.paginationInfo
        }
    }

    open fun getEntityPage(search: SearchType): PaginationInfo<EntityType> {
        // 如果不是通过getList调用的，尝试调用getList以执行可能的重写逻辑
        if (getListInProgress.get() != true) {
            getListInProgress.set(true)
            try {
                val result = getList(search)
                val data = result.datas
                return when (data) {
                    is PaginationInfo<*> -> {
                        @Suppress("UNCHECKED_CAST")
                        data as PaginationInfo<EntityType>
                    }
                    is List<*> -> {
                        @Suppress("UNCHECKED_CAST")
                        PaginationInfo.ofList(data as List<EntityType>)
                    }
                    else -> PaginationInfo.ofList(emptyList())
                }
            } finally {
                getListInProgress.remove()
            }
        }

        // 执行实际查询逻辑
        return doGetEntityPage(search)
    }

    fun getEntityList(search: SearchType): List<EntityType> {
        return getEntityPage(search).result
    }

    open fun getList(page: SearchType): Result {
        // 如果不是通过getEntityPage调用的，尝试调用getEntityPage以执行可能的重写逻辑
        if (getListInProgress.get() != true) {
            getListInProgress.set(true)
            try {
                val paginationInfo = getEntityPage(page)
                val result: Any = if (page.ifPage) {
                    paginationInfo
                } else {
                    paginationInfo.result
                }
                return Result.getSuccess(result)
            } finally {
                getListInProgress.remove()
            }
        }

        // 执行实际查询逻辑
        val paginationInfo = doGetEntityPage(page)
        val result: Any = if (page.ifPage) {
            paginationInfo
        } else {
            paginationInfo.result
        }
        return Result.getSuccess(result)
    }

    open fun getEntity(id: String): EntityType? {
        // 如果不是通过getInfo调用的，尝试调用getInfo以执行可能的重写逻辑
        if (getInfoInProgress.get() != true) {
            getInfoInProgress.set(true)
            try {
                val result = getInfo(id)
                @Suppress("UNCHECKED_CAST")
                return result.datas as? EntityType
            } finally {
                getInfoInProgress.remove()
            }
        }

        // 执行实际查询逻辑
        return mapper.getInfo(id)
    }

    open fun getInfo(id: String): Result {
        // 如果不是通过getEntity调用的，尝试调用getEntity以执行可能的重写逻辑
        if (getInfoInProgress.get() != true) {
            getInfoInProgress.set(true)
            try {
                return Result.getSuccess(getEntity(id))
            } finally {
                getInfoInProgress.remove()
            }
        }

        // 执行实际查询逻辑
        return Result.getSuccess(mapper.getInfo(id))
    }

    /**
     * 实际执行保存的方法
     */
    private fun doSave(entity: EntityType): String {
        if (entity.id.notEmpty()) {
            mapper.delete(entity.id)
        }
        if (entity.id.isEmpty()) {
            entity.id = uuid()
        }
        mapper.insert(entity)
        return entity.id
    }

    @Transactional
    open fun saveEntity(entity: EntityType): String {
        // 如果不是通过save调用的，尝试调用save以执行可能的重写逻辑
        if (saveInProgress.get() != true) {
            saveInProgress.set(true)
            try {
                val result = save(entity)
                return result.datas as String
            } finally {
                saveInProgress.remove()
            }
        }

        // 执行实际保存逻辑
        return doSave(entity)
    }

    @Transactional
    open fun save(model: EntityType): Result {
        // 如果不是通过saveEntity调用的，尝试调用saveEntity以执行可能的重写逻辑
        if (saveInProgress.get() != true) {
            saveInProgress.set(true)
            try {
                val id = saveEntity(model)
                return Result.getSuccess(id)
            } finally {
                saveInProgress.remove()
            }
        }

        // 执行实际保存逻辑
        val id = doSave(model)
        return Result.getSuccess(id)
    }

    @Transactional
    open fun deleteEntity(id: String): Int {
        // 如果不是通过delete调用的，尝试调用delete以执行可能的重写逻辑
        if (deleteInProgress.get() != true) {
            deleteInProgress.set(true)
            try {
                val result = delete(id)
                return result.datas as Int
            } finally {
                deleteInProgress.remove()
            }
        }

        // 执行实际删除逻辑
        return mapper.delete(id)
    }

    @Transactional
    open fun delete(id: String): Result {
        // 如果不是通过deleteEntity调用的，尝试调用deleteEntity以执行可能的重写逻辑
        if (deleteInProgress.get() != true) {
            deleteInProgress.set(true)
            try {
                return Result.getSuccess(deleteEntity(id))
            } finally {
                deleteInProgress.remove()
            }
        }

        // 执行实际删除逻辑
        return Result.getSuccess(mapper.delete(id))
    }

    @Transactional
    open fun deleteEntityLogic(id: String): Int {
        // 如果不是通过deleteLogic调用的，尝试调用deleteLogic以执行可能的重写逻辑
        if (deleteLogicInProgress.get() != true) {
            deleteLogicInProgress.set(true)
            try {
                val result = deleteLogic(id)
                return result.datas as Int
            } finally {
                deleteLogicInProgress.remove()
            }
        }

        // 执行实际逻辑删除
        return mapper.deleteLogic(id)
    }

    @Transactional
    open fun deleteLogic(id: String): Result {
        // 如果不是通过deleteEntityLogic调用的，尝试调用deleteEntityLogic以执行可能的重写逻辑
        if (deleteLogicInProgress.get() != true) {
            deleteLogicInProgress.set(true)
            try {
                return Result.getSuccess(deleteEntityLogic(id))
            } finally {
                deleteLogicInProgress.remove()
            }
        }

        // 执行实际逻辑删除
        return Result.getSuccess(mapper.deleteLogic(id))
    }
}

class BusinessException : RuntimeException {
    constructor() : super()
    constructor(message: String) : super(message)
    constructor(message: String, cause: Throwable) : super(message, cause)
    constructor(cause: Throwable) : super(cause)
}

/**
 * 更正:如果要使用ApiInfoCache则这四个方法必须是open的(子类不需要重写)
 */
open class BaseResource<SearchType : BaseSearch, EntityType : BaseEntity, MapperType : BaseMapper<EntityType>, F : BaseService<EntityType, MapperType, SearchType>>(var service: F) {

    @PostMapping("/getList")
    open fun getList(@Valid @RequestBody search: SearchType) = service.getList(search)

    @PostMapping("/save")
    open fun save(@Valid @RequestBody model: EntityType) = service.save(model)

    @GetMapping("/delete/{id}")
    open fun delete(@PathVariable id: String) = service.delete(id)

    @GetMapping("/getInfo/{id}")
    open fun getInfo(@PathVariable id: String) = service.getInfo(id)
}

