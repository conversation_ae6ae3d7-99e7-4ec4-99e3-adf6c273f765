package com.shenlan.smartlogixmini.auto

import com.github.pagehelper.PageHelper
import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.mybatis.paginationInfo
import com.shenlan.smartlogixmini.util.notEmpty
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.*
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import java.io.Serializable
import java.util.*
import javax.validation.Valid

open class BaseModel : Serializable {
    var id: String = ""
    var sysCreated: Date? = null
}

open class BaseSearch {
    var ifPage = true
    var currentPage: Int? = 1
    var pageRecord: Int? = 15
    var startTime: String = ""
    var endTime: String = ""
}

@Mapper
interface BaseMapper<L : BaseModel> {
    fun getList(search: BaseSearch = BaseSearch()): List<L>

    fun getListByPid(pid: String): List<L>

    fun getInfo(id: String): L?

    fun insert(model: BaseModel): Int

    fun insertList(modelList: List<BaseModel>): Int

    fun delete(id: String): Int

    fun deleteLogic(id: String): Int
}


class Result {
    /** 0成功 1失败 */
    var rlt = 0
    var info = "success"
    var datas: Any? = null

    constructor()

    constructor(rlt: Int, info: String) {
        this.rlt = rlt
        this.info = info
    }

    constructor(rlt: Int, info: String, datas: Any) {
        this.rlt = rlt
        this.info = info
        this.datas = datas
    }

    constructor(datas: Any?) {
        this.datas = datas
    }

    fun <T> toList(): List<T> {
        return when (datas) {
            is PaginationInfo -> {
                @Suppress("UNCHECKED_CAST")
                (datas as PaginationInfo).result as? List<T> ?: listOf()
            }
            is List<*> -> {
                @Suppress("UNCHECKED_CAST")
                datas as? List<T> ?: listOf()
            }
            else -> listOf()
        }
    }

    companion object {
        const val SUCCESS = 0
        const val FAIL = 1
        val success = Result(0, "success")

        fun getSuccess(datas: Any?): Result {
            return Result(datas)
        }

        fun getError(info: String): Result {
            return Result(1, info)
        }

        fun getError(info: String, datas: Any): Result {
            return Result(1, info, datas)
        }

        fun getSuccessInfo(info: String) = Result(0, info)
    }
}

open class BaseService<T : BaseModel, M : BaseMapper<T>>(var mapper: M) {

    open fun getList(page: BaseSearch): Result {
        val result: Any = if (page.currentPage == null || page.pageRecord == null || !page.ifPage) {
            mapper.getList(page)
        } else {
            PageHelper.startPage<T>(page.currentPage!!, page.pageRecord!!).doSelectPageInfo<T> {
                mapper.getList(page)
            }.paginationInfo
        }
        return Result.getSuccess(result)
    }

    open fun getDataList(search: BaseSearch): List<T> {
        val result =  getList(search)
        val data = result.datas
        return when (data) {
            is List<*> -> {
                @Suppress("UNCHECKED_CAST")
                data as List<T>
            }
            is PaginationInfo -> {
                @Suppress("UNCHECKED_CAST")
                data.result as List<T>
            }
            else -> listOf()
        }
    }

    open fun getInfo(id: String): Result {
        val model = mapper.getInfo(id)
        return Result.getSuccess(model)
    }

    @Transactional
    open fun save(model: T): Result {
        if (model.id.notEmpty()) {
            delete(model.id)
        }
        if (model.id.isEmpty()) {
            model.id = uuid()
        }
        mapper.insert(model)
        return Result.getSuccess(model.id)
    }

    open var deleteChildren = { _: T -> }
    @Transactional
    open fun delete(id: String): Result {
        val datas = getInfo(id).datas
        if (datas != null) {
            @Suppress("UNCHECKED_CAST")
            deleteChildren(datas as T)
        }
        return Result.getSuccess(mapper.delete(id))
    }

    @Transactional
    open fun deleteLogic(id: String): Result {
        return Result.getSuccess(mapper.deleteLogic(id))
    }
}

/**
 * 更正:如果要使用ApiInfoCache则这四个方法必须是open的(子类不需要重写)
 */
open class BaseResource<T : BaseSearch, G : BaseModel, M : BaseMapper<G>, F : BaseService<G, M>>(var service: F) {

    @PostMapping("/getList")
    open fun getList(@Valid @RequestBody search: T) = service.getList(search)

    @PostMapping("/save")
    open fun save(@Valid @RequestBody model: G) = service.save(model)

    @GetMapping("/delete/{id}")
    open fun delete(@PathVariable id: String) = service.delete(id)

    @GetMapping("/getInfo/{id}")
    open fun getInfo(@PathVariable id: String) = service.getInfo(id)
}

