package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 组织实体类
 *
 * 3层组织树架构：
 * Tier 1 - Administration
 * Tier 2 - Branch: Main Branch / Sub Branch
 * Tier 3 - Department
 */
class Organization : BaseEntity() {
    // 数据库表字段

    /** 父组织ID */
    var pid: String = ""
    /** 分支组织ID(祖先路径中层级为2的节点ID) */
    var branchOrganizationId: String = ""
    /** 组织名称 */
    var name: String = ""
    /** 排序序号 */
    var sortOrder: Int = 0
    /** 层级2类型(0-无,1-总中心,2-分中心) */
    var tier2Type: Int = 0
    /** 组织类型(0-无,1-总中心下的部门,2-分中心) */
    var mainBranchDepartmentOrSubBranch: Int = 0

    // 关联字段

    /** 组织下的人员列表 */
    var personnelList: List<Personnel> = listOf()
}

/**
 * 组织查询条件类
 */
class OrganizationSearch : BaseSearch() {
    /** 父组织ID */
    var pid: String? = null
    /** 分支组织ID */
    var branchOrganizationId: String? = null
    /** 组织名称模糊匹配 */
    var nameLike: String = ""
    /** 层级2类型(0-无,1-总中心,2-分中心) */
    var tier2Type: Int = 0
    /** 组织类型(0-无,1-总中心下的部门,2-分中心) */
    var mainBranchDepartmentOrSubBranch: Int = 0
    /** 是否只查询叶子节点（没有子组织的组织） */
    var onlyLeaf: Boolean = false
    /** 是否按当前用户所属分中心过滤组织 */
    var ifFilterByCurrentSubBranch: Boolean = false
    /** 是否加载人员列表 */
    var loadPersonnelList: Boolean = false
}

/**
 * 组织Mapper接口
 */
@Mapper
interface OrganizationMapper : BaseMapper<Organization> {

    @Select("""
        <script>
            SELECT * FROM tbl_organization
            <where>
                sysDeleted = 0
                <if test="pid != null">
                    AND pid = #{pid}
                </if>
                <if test="branchOrganizationId != null">
                    AND branchOrganizationId = #{branchOrganizationId}
                </if>
                <if test="nameLike != ''">
                    AND name LIKE CONCAT('%', #{nameLike}, '%')
                </if>
                <if test="tier2Type > 0">
                    AND tier2Type = #{tier2Type}
                </if>
                <if test="mainBranchDepartmentOrSubBranch > 0">
                    AND mainBranchDepartmentOrSubBranch = #{mainBranchDepartmentOrSubBranch}
                </if>
                <if test="onlyLeaf">
                    AND NOT EXISTS (
                        SELECT 1 FROM tbl_organization child 
                        WHERE child.pid = tbl_organization.id AND child.sysDeleted = 0
                    )
                </if>
            </where>
            ORDER BY sortOrder
        </script>
    """)
    override fun getList(search: BaseSearch): List<Organization>

    /**
     * 根据组织名称查询单个组织信息
     */
    @Select("SELECT * FROM tbl_organization WHERE sysDeleted = 0 AND name = #{name} LIMIT 1")
    fun getInfoByName(name: String): Organization?

    /**
     * 获取当前最大的排序号
     */
    @Select("SELECT COALESCE(MAX(sortOrder), 0) FROM tbl_organization WHERE sysDeleted = 0")
    fun getMaxSortOrder(): Int

    /**
     * 批量更新组织的排序序号
     */
    @Update("""
        <script>
            UPDATE tbl_organization SET sortOrder = CASE id
            <foreach collection="sortOrderMap" index="id" item="sortOrder">
                WHEN #{id} THEN #{sortOrder}
            </foreach>
            END
            WHERE id IN (
            <foreach collection="sortOrderMap" index="id" item="sortOrder" separator=",">
                #{id}
            </foreach>
            )
        </script>
    """)
    fun updateSortOrderBatch(@Param("sortOrderMap") sortOrderMap: Map<String, Int>): Int
}

/**
 * 组织Service类
 */
@Service
class OrganizationService(
    mapper: OrganizationMapper,
    private val personnelMapper: PersonnelMapper
) : BaseService<Organization, OrganizationMapper, OrganizationSearch>(mapper) {

    /**
     * 重写save方法，确保组织名称不重复和层级关系有效
     */
    @Transactional
    override fun saveEntity(entity: Organization): String {
        // 检查组织名称不能为空
        if (entity.name.isEmpty()) {
            throw BusinessException("组织名称不能为空")
        }

        // 查询是否存在相同名称的组织（除了当前编辑的组织）
        val existingOrganizationByName = mapper.getInfoByName(entity.name)
        if (existingOrganizationByName != null && existingOrganizationByName.id != entity.id) {
            throw BusinessException("组织名称已存在，请使用其他名称")
        }

        // 验证父组织关系
        if (entity.pid.isNotEmpty()) {
            // 检查父组织是否存在
            val parentOrganization = mapper.getInfo(entity.pid)
            if (parentOrganization == null) {
                throw BusinessException("指定的父组织不存在")
            }

            // 检查不能设置自己为父组织
            if (entity.pid == entity.id) {
                throw BusinessException("不能设置自己为父组织")
            }

            // 检查是否会形成循环引用
            if (isCircularReference(entity.id, entity.pid)) {
                throw BusinessException("不能设置该父组织，会形成循环引用")
            }
        }

        // 根据组织类型自动设置pid和tier2Type
        when (entity.mainBranchDepartmentOrSubBranch) {
            1 -> {
                // 总中心下的部门：找到总中心作为父组织
                val mainBranchId = getMainBranchId()
                if (mainBranchId.isEmpty()) {
                    throw BusinessException("系统中不存在总中心，无法创建总中心下的部门")
                }
                entity.pid = mainBranchId
                entity.tier2Type = 0  // 部门层级
                log.info("Set organization as mainBranchDepartment. name={}, mainBranchId={}",
                        entity.name, mainBranchId)
            }
            2 -> {
                // 分中心：设为Administration的子组织
                val administrationId = getAdministrationId()
                if (administrationId.isEmpty()) {
                    throw BusinessException("系统中不存在Administration，无法创建分中心")
                }
                entity.pid = administrationId
                entity.tier2Type = 2  // 分中心层级
                log.info("Set organization as subBranch under Administration. name={}, administrationId={}",
                        entity.name, administrationId)
            }
            0 -> {
                // 默认值，不做自动处理，使用手动设置的pid和tier2Type
                log.info("Organization type not specified, using manual settings. name={}", entity.name)
            }
            else -> {
                throw BusinessException("无效的组织类型：${entity.mainBranchDepartmentOrSubBranch}")
            }
        }

        // 验证通过，调用父类的save方法完成保存
        val result = super.saveEntity(entity)

        // 保存后设置branchOrganizationId
        val branchOrganization = getBranchOrganization(entity.id)
        entity.branchOrganizationId = branchOrganization?.id ?: ""
        super.saveEntity(entity)

        // 调整所有组织的排序序号
        adjustAllOrganizationSortOrder()

        return result
    }

    /**
     * 检查是否会形成循环引用
     */
    private fun isCircularReference(organizationId: String, parentId: String): Boolean {
        var currentParentId = parentId

        // 最多检查10层，防止无限循环
        repeat(10) {
            if (currentParentId == organizationId) {
                return true
            }

            val parent = mapper.getInfo(currentParentId)
            if (parent == null || parent.pid.isEmpty()) {
                return false
            }

            currentParentId = parent.pid
        }

        return false
    }

    /**
     * 获取Administration组织的ID
     */
    private fun getAdministrationId(): String {
        val search = OrganizationSearch()
        search.pid = "" // 查询顶级组织
        search.ifPage = false
        val topOrganizations = mapper.getList(search)
        return topOrganizations.firstOrNull()?.id ?: ""
    }

    /**
     * 获取总中心组织的ID（tier2Type = 1）
     */
    private fun getMainBranchId(): String {
        val search = OrganizationSearch()
        search.tier2Type = 1 // 总中心
        search.ifPage = false
        val mainBranches = mapper.getList(search)
        return mainBranches.firstOrNull()?.id ?: ""
    }

    /**
     * 重写方法，根据需要加载人员列表
     */
    override fun getEntityPage(search: OrganizationSearch): PaginationInfo<Organization> {
        // 处理按当前用户分中心过滤的逻辑
        if (search.ifFilterByCurrentSubBranch) {
            val currentUser = getUser()
            if (currentUser?.branchOrganizationId?.isNotEmpty() == true) {
                // 检查用户所属分支组织是否为分中心（tier2Type = 2）
                val userBranchOrganization = mapper.getInfo(currentUser.branchOrganizationId)
                if (userBranchOrganization?.tier2Type == 2) {
                    search.branchOrganizationId = currentUser.branchOrganizationId
                    log.info("Filter organizations by current user sub-branch. userId={}, branchOrganizationId={}",
                            currentUser.id, currentUser.branchOrganizationId)
                } else {
                    log.info("Current user branch organization is not sub-branch, skipping filter. userId={}, branchOrganizationId={}, tier2Type={}",
                            currentUser.id, currentUser.branchOrganizationId, userBranchOrganization?.tier2Type)
                }
            } else {
                log.info("Current user has no branch organization, skipping sub-branch filter. userId={}",
                        currentUser?.id ?: "null")
            }
        }

        val paginationInfo = super.getEntityPage(search)
        if (search.loadPersonnelList) {
            val organizationList = paginationInfo.result
            organizationList.forEach { loadPersonnel(it) }
        }
        return paginationInfo
    }

    /**
     * 重写getInfo方法，加载组织的人员信息
     */
    override fun getEntity(id: String): Organization? {
        val organization = mapper.getInfo(id)
        if (organization != null) {
            // 加载组织的人员信息
            loadPersonnel(organization)
        }
        return organization
    }

    /**
     * 加载组织的人员信息
     */
    private fun loadPersonnel(organization: Organization) {
        val search = PersonnelSearch()
        search.organizationId = organization.id
        search.ifPage = false
        val personnelList = personnelMapper.getList(search)
        organization.personnelList = personnelList
    }

    /**
     * 获取组织的祖先路径（从根组织到当前组织）
     * @param organizationId 组织ID
     * @return 祖先路径列表，从根组织到当前组织的顺序
     */
    fun getAncestorPath(organizationId: String): List<Organization> {
        if (organizationId.isEmpty()) {
            log.info("Empty organizationId provided for ancestor path")
            return emptyList()
        }

        val path = mutableListOf<Organization>()
        var currentId = organizationId

        // 防止无限循环，最多查找10层
        repeat(10) {
            if (currentId.isEmpty()) {
                return@repeat
            }

            val organization = mapper.getInfo(currentId)
            if (organization == null) {
                log.info("Organization not found during ancestor path traversal. organizationId={}", currentId)
                return@repeat
            }

            path.add(organization)
            currentId = organization.pid
        }

        if (path.isEmpty()) {
            log.info("No organizations found in ancestor path. organizationId={}", organizationId)
            return emptyList()
        }

        // 反转列表，使其从根组织到当前组织的顺序
        val ancestorPath = path.reversed()
        log.info("Retrieved ancestor path. organizationId={}, pathLength={}", organizationId, ancestorPath.size)

        return ancestorPath
    }

    /**
     * 获取组织祖先路径中指定层级的组织
     * @param organizationId 组织ID
     * @param tier 层级，从1开始（1=根组织，2=第二层，以此类推）
     * @return 指定层级的组织，如果不存在则返回null
     */
    fun getAncestorAtTier(organizationId: String, tier: Int): Organization? {
        if (tier <= 0) {
            log.info("Invalid tier provided. tier={}", tier)
            return null
        }

        val ancestorPath = getAncestorPath(organizationId)

        if (ancestorPath.isEmpty()) {
            log.info("No ancestor path found. organizationId={}", organizationId)
            return null
        }

        if (tier > ancestorPath.size) {
            log.info("Tier exceeds path length. organizationId={}, tier={}, pathLength={}",
                    organizationId, tier, ancestorPath.size)
            return null
        }

        val targetOrganization = ancestorPath[tier - 1]
        log.info("Retrieved organization at tier. organizationId={}, tier={}, targetOrgId={}, targetOrgName={}",
                organizationId, tier, targetOrganization.id, targetOrganization.name)

        return targetOrganization
    }

    /**
     * 获取组织祖先路径中的Branch组织（第2层）
     * @param organizationId 组织ID
     * @return Branch组织（Main Branch或Sub Branch），如果不存在则返回null
     */
    fun getBranchOrganization(organizationId: String): Organization? {
        val branchOrganization = getAncestorAtTier(organizationId, 2)

        if (branchOrganization != null) {
            log.info("Retrieved branch organization. organizationId={}, branchId={}, branchName={}",
                    organizationId, branchOrganization.id, branchOrganization.name)
        } else {
            log.info("No branch organization found. organizationId={}", organizationId)
        }

        return branchOrganization
    }

    /**
     * 调整所有组织的排序序号
     * 根据mainBranchDepartmentOrSubBranch值设置优先级排序：
     * 1. mainBranchDepartmentOrSubBranch == 1 (总中心下的部门) - 优先级最高
     * 2. mainBranchDepartmentOrSubBranch == 2 (分中心) - 优先级中等
     * 3. mainBranchDepartmentOrSubBranch == 0 (其他) - 优先级最低
     * 同类型内按原先的sortOrder排序
     */
    @Transactional
    fun adjustAllOrganizationSortOrder() {
        // 获取所有组织
        val search = OrganizationSearch()
        search.ifPage = false
        val allOrganizations = getEntityList(search)

        if (allOrganizations.isEmpty()) {
            log.info("No organizations found, skipping sort order adjustment")
            return
        }

        // 按规则排序：先按类型优先级，再按原先的sortOrder
        val sorted = allOrganizations.sortedWith(
            compareBy<Organization> {
                when(it.mainBranchDepartmentOrSubBranch) {
                    1 -> 1  // 总中心下的部门优先
                    2 -> 2  // 分中心次之
                    else -> 3  // 其他最后
                }
            }.thenBy { it.sortOrder }
        )

        // 构建排序映射：组织ID -> 新的sortOrder
        val sortOrderMap = sorted.mapIndexed { index, org ->
            org.id to (index + 1) * 10
        }.toMap()

        if (sortOrderMap.isNotEmpty()) {
            mapper.updateSortOrderBatch(sortOrderMap)
            log.info("Adjusted sort order for organizations. totalCount={}", sortOrderMap.size)
        }
    }

    /**
     * 重写delete方法，检查是否有关联的人员和子组织
     */
    @Transactional
    override fun deleteEntity(id: String): Int {
        val organization = mapper.getInfo(id)
        if (organization != null) {
            // 检查是否有子组织
            val childSearch = OrganizationSearch()
            childSearch.pid = organization.id
            childSearch.ifPage = false
            val childOrganizations = mapper.getList(childSearch)

            if (childOrganizations.isNotEmpty()) {
                throw BusinessException("该组织下还有${childOrganizations.size}个子组织，无法删除")
            }

            // 检查是否有关联的人员
            val personnelSearch = PersonnelSearch()
            personnelSearch.organizationId = organization.id
            personnelSearch.ifPage = false
            val personnelList = personnelMapper.getList(personnelSearch)

            if (personnelList.isNotEmpty()) {
                throw BusinessException("该机构下已有职工，不可删除")
            }
        }

        val result = super.deleteEntity(id)

        return result
    }
}

/**
 * 组织Controller类
 */
@RestController
@RequestMapping("/api/Organization")
class OrganizationResource(service: OrganizationService) : BaseResource<OrganizationSearch, Organization, OrganizationMapper, OrganizationService>(service)
