package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 组织机构实体类
 */
class Organization : BaseModel() {
    // 数据库表字段

    /** 组织机构名称 */
    var name: String = ""
    /** 组织机构描述 */
    var description: String = ""
    /** 排序序号 */
    var sortOrder: Int = 0
    /** 状态(0-禁用,1-启用) */
    var status: Int = 1

    // 关联字段

    /** 组织机构下的人员列表 */
    var personnelList: List<Personnel> = listOf()
}

/**
 * 组织机构查询条件类
 */
class OrganizationSearch : BaseSearch() {
    /** 组织机构名称模糊匹配 */
    var nameLike: String = ""
    /** 状态(0-禁用,1-启用) */
    var status: Int? = null
    /** 是否加载人员列表 */
    var loadPersonnelList: Boolean = false
}

/**
 * 组织机构Mapper接口
 */
@Mapper
interface OrganizationMapper : BaseMapper<Organization> {

    @Select("""
        <script>
            SELECT * FROM tbl_organization
            <where>
                sysDeleted = 0
                <if test="nameLike != ''">
                    AND name LIKE CONCAT('%', #{nameLike}, '%')
                </if>
                <if test="status != null">
                    AND status = #{status}
                </if>
            </where>
            ORDER BY sortOrder, name
        </script>
    """)
    override fun getList(search: BaseSearch): List<Organization>

    /**
     * 根据组织机构名称查询单个组织机构信息
     */
    @Select("SELECT * FROM tbl_organization WHERE sysDeleted = 0 AND name = #{name} LIMIT 1")
    fun getInfoByName(name: String): Organization?

    /**
     * 获取当前最大的排序号
     */
    @Select("SELECT COALESCE(MAX(sortOrder), 0) FROM tbl_organization WHERE sysDeleted = 0")
    fun getMaxSortOrder(): Int
}

/**
 * 组织机构Service类
 */
@Service
class OrganizationService(
    mapper: OrganizationMapper,
    private val personnelMapper: PersonnelMapper
) : BaseService<Organization, OrganizationMapper>(mapper) {

    /**
     * 重写save方法，确保组织机构名称不重复
     */
    override fun save(model: Organization): Result {
        // 检查组织机构名称不能为空
        if (model.name.isEmpty()) {
            return Result.getError("组织机构名称不能为空")
        }

        // 查询是否存在相同名称的组织机构（除了当前编辑的组织机构）
        val existingOrganizationByName = mapper.getInfoByName(model.name)
        if (existingOrganizationByName != null && existingOrganizationByName.id != model.id) {
            return Result.getError("组织机构名称已存在，请使用其他名称")
        }

        // 处理排序号设置
        if (model.sortOrder <= 0) {
            val maxSortOrder = mapper.getMaxSortOrder()
            model.sortOrder = maxSortOrder + 1
            log.info("Set sortOrder for organization '{}' to {}", model.name, model.sortOrder)
        }

        // 验证通过，调用父类的save方法完成保存
        return super.save(model)
    }

    /**
     * 重写getList方法，根据需要加载人员列表
     */
    override fun getList(page: BaseSearch): Result {
        val result = super.getList(page)
        if (page is OrganizationSearch && page.loadPersonnelList) {
            val organizationList = result.toList<Organization>()
            organizationList.forEach { loadPersonnel(it) }
        }
        return result
    }

    /**
     * 重写getInfo方法，加载组织机构的人员信息
     */
    override fun getInfo(id: String): Result {
        val organization = mapper.getInfo(id)
        if (organization != null) {
            // 加载组织机构的人员信息
            loadPersonnel(organization)
        }
        return Result.getSuccess(organization)
    }

    /**
     * 加载组织机构的人员信息
     */
    private fun loadPersonnel(organization: Organization) {
        val search = PersonnelSearch()
        search.organizationId = organization.id
        search.ifPage = false
        val personnelList = personnelMapper.getList(search)
        organization.personnelList = personnelList
    }

    /**
     * 重写delete方法，检查是否有关联的人员
     */
    override fun delete(id: String): Result {
        val organization = mapper.getInfo(id)
        if (organization != null) {
            // 检查是否有关联的人员
            val search = PersonnelSearch()
            search.organizationId = organization.id
            search.ifPage = false
            val personnelList = personnelMapper.getList(search)

            if (personnelList.isNotEmpty()) {
                return Result.getError("该组织机构下还有${personnelList.size}名人员，无法删除")
            }
        }

        return super.delete(id)
    }
}

/**
 * 组织机构Controller类
 */
@RestController
@RequestMapping("/api/Organization")
class OrganizationResource(service: OrganizationService) : BaseResource<OrganizationSearch, Organization, OrganizationMapper, OrganizationService>(service)
