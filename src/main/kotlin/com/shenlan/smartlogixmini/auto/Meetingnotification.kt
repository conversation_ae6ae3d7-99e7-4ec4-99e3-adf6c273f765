package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.AliyunSmsUtil
import com.shenlan.smartlogixmini.util.AliyunVmsUtil
import com.shenlan.smartlogixmini.util.errorlog
import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.TaskScheduler
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.text.SimpleDateFormat
import java.time.ZoneId
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ScheduledFuture
import javax.annotation.PostConstruct

/**
 * 转换会议室名称
 * 将阿拉伯数字转换为中文数字，将"/"替换为空格
 */
private fun formatMeetingRoomName(name: String): String {
    val arabicToChinese = mapOf(
        "0" to "零",
        "1" to "一",
        "2" to "二",
        "3" to "三",
        "4" to "四",
        "5" to "五",
        "6" to "六",
        "7" to "七",
        "8" to "八",
        "9" to "九"
    )

    var formattedName = name
    // 替换所有阿拉伯数字为中文数字
    arabicToChinese.forEach { (arabic, chinese) ->
        formattedName = formattedName.replace(arabic, chinese)
    }

    // 将"/"替换为空格
    formattedName = formattedName.replace("/", " ")

    return formattedName
}

/**
 * VMS语音电话菜单按键配置
 */
data class VmsMenuKeyMap(
    var key: String = "",
    var code: String = ""
)

/**
 * 会议创建通知VMS模板配置类
 */
@Component
@ConfigurationProperties(prefix = "aliyun.vms.template.meeting.create")
class MeetingCreateVmsTemplateConfig {
    var startCode: String = ""
    var menuKeyMapList: List<VmsMenuKeyMap> = listOf()
}

/**
 * 会议开始通知VMS模板配置类
 */
@Component
@ConfigurationProperties(prefix = "aliyun.vms.template.meeting.start")
class MeetingStartVmsTemplateConfig {
    var startCode: String = ""
    var menuKeyMapList: List<VmsMenuKeyMap> = listOf()
}

/**
 * 会议取消通知VMS模板配置类
 */
@Component
@ConfigurationProperties(prefix = "aliyun.vms.template.meeting.cancel")
class MeetingCancelVmsTemplateConfig {
    var startCode: String = ""
    var menuKeyMapList: List<VmsMenuKeyMap> = listOf()
}

/**
 * 会议调整通知VMS模板配置类
 */
@Component
@ConfigurationProperties(prefix = "aliyun.vms.template.meeting.change")
class MeetingChangeVmsTemplateConfig {
    var startCode: String = ""
    var menuKeyMapList: List<VmsMenuKeyMap> = listOf()
}

/**
 * 会议通知实体类
 */
class Meetingnotification : BaseEntity() {
    /** 会议ID */
    var meetingId: String = ""
    /** 人员ID */
    var personnelId: String = ""
    /** 通知方式(0-短信通知,1-机器人语音电话通知) */
    var method: Int = 0
    /** 通知状态(0-未发送,1-已发送,2-已取消) */
    var status: Int = 0
    /** 通知时间 */
    var time: Date? = null
    /** 通知结果记录 */
    var result: String = ""
    /** 通知类型(0-会议创建通知,1-会议开始通知,2-会议取消通知,3-会议调整通知) */
    var type: Int = 1
    /** 云平台回执ID */
    var receiptId: String = ""

    /** 关联的会议信息 */
    var meeting: Meeting? = null
    /** 关联的人员信息 */
    var personnel: Personnel? = null
}

/**
 * 会议通知查询条件类
 */
class MeetingnotificationSearch : BaseSearch() {
    /** 会议ID */
    var meetingId: String = ""
    /** 人员ID */
    var personnelId: String = ""
    /** 通知方式 */
    var method: Int? = null
    /** 通知状态 */
    var status: Int? = null
    /** 通知类型(0-会议创建通知,1-会议开始通知,2-会议取消通知,3-会议调整通知) */
    var type: Int? = null
    /** 是否加载会议信息 */
    var loadMeeting: Boolean = false
    /** 是否加载人员信息 */
    var loadPersonnel: Boolean = false
}

/**
 * 会议通知Mapper接口
 */
@Mapper
interface MeetingnotificationMapper : BaseMapper<Meetingnotification> {

    @Select("""
        <script>
            SELECT n.* FROM tbl_meetingnotification n
            <where>
                n.sysDeleted = 0
                <if test="meetingId != ''">
                    AND n.meetingId = #{meetingId}
                </if>
                <if test="personnelId != ''">
                    AND n.personnelId = #{personnelId}
                </if>
                <if test="method != null">
                    AND n.method = #{method}
                </if>
                <if test="status != null">
                    AND n.status = #{status}
                </if>
                <if test="type != null">
                    AND n.type = #{type}
                </if>
            </where>
            ORDER BY n.time ASC, n.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Meetingnotification>

    /**
     * 获取待发送的通知列表
     */
    @Select("""
        SELECT n.* FROM tbl_meetingnotification n
        JOIN tbl_meeting m ON n.meetingId = m.id
        WHERE n.sysDeleted = 0 
        AND n.status = 0 
        AND n.personnelId != ''
        AND (
            (m.status IN (0, 1)) OR
            (n.type = 2 AND m.status = 2) -- 允许检索会议已取消状态下的取消通知
        )
        AND m.sysDeleted = 0
        ORDER BY n.time ASC
    """)
    fun getPendingMeetingnotifications(): List<Meetingnotification>

    /**
     * 更新通知状态
     */
    @Update("""
        UPDATE tbl_meetingnotification
        SET status = #{status}, result = #{result}, receiptId = #{receiptId}
        WHERE id = #{id}
    """)
    fun updateMeetingnotificationStatus(id: String, status: Int, result: String, receiptId: String): Int

    /**
     * 根据回执ID查询通知信息
     */
    @Select("""
        SELECT * FROM tbl_meetingnotification
        WHERE receiptId = #{receiptId} AND sysDeleted = 0
        LIMIT 1
    """)
    fun getInfoByReceiptId(receiptId: String): Meetingnotification?

    /**
     * 更新通知结果信息
     */
    @Update("""
        UPDATE tbl_meetingnotification
        SET result = #{result}
        WHERE id = #{id}
    """)
    fun updateMeetingnotificationResult(id: String, result: String): Int

    /**
     * 根据人员ID和指定时间，查询该人员在指定时间之前的最近短信通知
     *
     * @param personnelId 人员ID
     * @param beforeTime 查询此时间之前的通知
     * @return 通知信息
     */
    @Select("""
        SELECT * FROM tbl_meetingnotification
        WHERE personnelId = #{personnelId} 
        AND method = 0 
        AND status = 1
        AND time <= #{beforeTime}
        AND sysDeleted = 0
        ORDER BY time DESC, sysCreated DESC
        LIMIT 1
    """)
    fun getLatestSmsNotificationBefore(personnelId: String, beforeTime: Date): Meetingnotification?
}

/**
 * 会议通知Service类
 */
@Service
class MeetingnotificationService(
    mapper: MeetingnotificationMapper,
    private val meetingMapper: MeetingMapper,
    private val meetingroomMapper: MeetingroomMapper,
    private val personnelMapper: PersonnelMapper,
    private val aliyunSmsUtil: AliyunSmsUtil,
    private val aliyunVmsUtil: AliyunVmsUtil,
    private val createVmsTemplateConfig: MeetingCreateVmsTemplateConfig,
    private val startVmsTemplateConfig: MeetingStartVmsTemplateConfig,
    private val cancelVmsTemplateConfig: MeetingCancelVmsTemplateConfig,
    private val changeVmsTemplateConfig: MeetingChangeVmsTemplateConfig
) : BaseService<Meetingnotification, MeetingnotificationMapper, MeetingnotificationSearch>(mapper) {

    // 日期格式化
    private val dateFormatter = SimpleDateFormat("yyyy-MM-dd HH:mm")

    // 短信通知模板编码
    @Value("\${aliyun.sms.template.meeting.create:}")
    private var smsCreateTemplateCode: String = ""

    @Value("\${aliyun.sms.template.meeting.start:}")
    private var smsStartTemplateCode: String = ""

    @Value("\${aliyun.sms.template.meeting.cancel:}")
    private var smsCancelTemplateCode: String = ""

    @Value("\${aliyun.sms.template.meeting.change:}")
    private var smsChangeTemplateCode: String = ""

    @Value("\${spring.application.name:}")
    private var applicationName: String = ""

    override fun getEntityPage(search: MeetingnotificationSearch): PaginationInfo<Meetingnotification> {
        val paginationInfo = super.getEntityPage(search)

        // 如果需要加载会议信息和人员信息
        if (search.loadMeeting) loadMeetingInfo(paginationInfo.result)
        if (search.loadPersonnel) loadPersonnelInfo(paginationInfo.result)

        return paginationInfo
    }

    /**
     * 获取会议通知详情
     */
    override fun getEntity(id: String): Meetingnotification? {
        val meetingnotification = super.getEntity(id)
        if (meetingnotification != null) {
            loadMeetingInfo(listOf(meetingnotification))
            loadPersonnelInfo(listOf(meetingnotification))
        }
        return meetingnotification
    }

    /**
     * 加载会议信息
     */
    private fun loadMeetingInfo(list: List<Meetingnotification>) {
        list.forEach { meetingnotification ->
            if (meetingnotification.meetingId.isNotEmpty()) {
                meetingnotification.meeting = meetingMapper.getInfo(meetingnotification.meetingId)
            }
        }
    }

    /**
     * 加载人员信息
     */
    private fun loadPersonnelInfo(list: List<Meetingnotification>) {
        list.forEach { meetingnotification ->
            if (meetingnotification.personnelId.isNotEmpty()) {
                meetingnotification.personnel = personnelMapper.getInfo(meetingnotification.personnelId)
            }
        }
    }

    /**
     * 获取待发送的通知列表
     */
    fun getPendingMeetingnotifications(): List<Meetingnotification> {
        val meetingnotifications = mapper.getPendingMeetingnotifications()
        loadMeetingInfo(meetingnotifications)
        loadPersonnelInfo(meetingnotifications)
        return meetingnotifications
    }

    /**
     * 发送通知
     *
     * @param id 通知ID
     * @return 发送结果
     */
    fun sendMeetingnotification(id: String): Result {
        val meetingnotification = mapper.getInfo(id)
        if (meetingnotification == null) {
            return Result.getError("通知不存在")
        }

        if (meetingnotification.status != 0) {
            return Result.getError("通知已发送或已取消，无法重复发送")
        }

        // 获取会议信息
        val meeting = meetingMapper.getInfo(meetingnotification.meetingId)
        if (meeting == null) {
            return Result.getError("会议不存在")
        }

        // 如果接收者是会议发起人，则不发送通知
        if (meetingnotification.personnelId == meeting.creatorId) {
            log.info("Skipping meetingnotification [${id}] for meeting [${meeting.id}] to creator [${meeting.creatorId}]")
            // 更新通知状态为已取消
            val updateResult = mapper.updateMeetingnotificationStatus(id, 2, "会议发起人无需接收通知", "")
            if (updateResult > 0) {
                log.info("Successfully marked meetingnotification [$id] as cancelled for creator")
            } else {
                log.error("Failed to update meetingnotification [$id] status for creator")
            }
            return Result.getSuccessInfo("会议发起人无需接收通知")
        }

        // 如果是会议取消通知，即使会议已取消也允许发送
        // 如果是其他类型通知，则会议必须是待开始或进行中状态
        if (meetingnotification.type != 2 && meeting.status != 0 && meeting.status != 1) {
            return Result.getError("会议已取消或已结束，无法发送通知")
        }

        // 检查是否指定了特定人员，如果没有则返回错误
        if (meetingnotification.personnelId.isEmpty()) {
            return Result.getError("必须指定接收通知的人员")
        }

        val (resultMessage, receiptId) = when (meetingnotification.method) {
            0 -> { // 短信通知
                // 只发送给特定人员
                val (msg, bizId) = sendSmsNotification(meeting, meetingnotification.personnelId, meetingnotification.type)
                msg to bizId
            }
            1 -> { // 语音电话通知
                val (msg, callId) = sendVoiceCallNotification(meeting, meetingnotification.personnelId, meetingnotification.type)
                msg to callId
            }
            else -> {
                "未知的通知类型" to ""
            }
        }

        // 更新通知状态
        val status = 1 // 已发送
        val updateResult = mapper.updateMeetingnotificationStatus(id, status, resultMessage, receiptId)

        return if (updateResult > 0) {
            log.info("Meetingnotification [$id] for meeting [${meetingnotification.meetingId}] sent successfully: $resultMessage")
            Result.getSuccessInfo("通知发送成功")
        } else {
            log.error("Failed to update meetingnotification [$id] status")
            Result.getError("通知状态更新失败")
        }
    }

    /**
     * 发送短信通知给特定人员
     *
     * @param meeting 会议信息
     * @param personnelId 人员ID
     * @param notificationType 通知类型
     * @return 发送结果描述
     */
    private fun sendSmsNotification(meeting: Meeting, personnelId: String, notificationType: Int): Pair<String, String> {
        // 获取会议相关信息
        val meetingTitle = meeting.title
        val creatorName = meeting.creatorName

        // 获取会议室信息
        val meetingroom = if (meeting.meetingroomId.isNotEmpty()) {
            meetingroomMapper.getInfo(meeting.meetingroomId)
        } else null

        val meetingroomName = meetingroom?.name ?: "未知地点"

        // 获取人员信息
        val personnel = personnelMapper.getInfo(personnelId)
        if (personnel == null || personnel.phone.isEmpty()) {
            return "无法发送短信通知：人员不存在或没有电话号码" to ""
        }

        // 直接使用传入的通知类型
        val type = notificationType

        // 根据通知类型获取对应的模板编码
        val templateCode = when (type) {
            0 -> smsCreateTemplateCode // 会议创建通知
            1 -> smsStartTemplateCode  // 会议开始通知
            2 -> smsCancelTemplateCode // 会议取消通知
            3 -> smsChangeTemplateCode // 会议调整通知
            else -> "" // 未知类型
        }

        // 检查模板编码是否配置
        if (templateCode.isEmpty()) {
            log.error("No SMS template code configured for meetingnotification type $type")
            return "短信模板未配置，无法发送会议通知" to ""
        }

        // 构建短信参数，使用会议发起人名称而不是参会人名称
        val params = mapOf(
            "meetingTitle" to meetingTitle,
            "creatorName" to creatorName,
            "meetingrommName" to meetingroomName,
            "meetingStartTime" to dateFormatter.format(meeting.startTime),
            "meetingEndTime" to dateFormatter.format(meeting.endTime),
            "personnelName" to personnel.name,
            "NoName" to applicationName
        )

        // 发送短信
        val smsResult = aliyunSmsUtil.sendSmsWithBizId(
            personnel.phone,
            params,
            templateCode
        )

        // 根据通知类型获取通知类型简短描述（用于日志和返回消息）
        val typeDesc = when (type) {
            0 -> "创建"
            1 -> "开始"
            2 -> "取消"
            3 -> "调整"
            else -> "通知"
        }

        return if (smsResult.first) {
            log.info("SMS $typeDesc meetingnotification sent successfully to ${personnel.name} (${personnel.phone}) using template: $templateCode, bizId: ${smsResult.second}")
            "已成功发送会议${typeDesc}短信通知给 ${personnel.name}" to smsResult.second
        } else {
            log.error("Failed to send SMS $typeDesc meetingnotification to ${personnel.name} (${personnel.phone}) using template: $templateCode")
            "发送会议${typeDesc}短信通知给 ${personnel.name} 失败" to smsResult.second
        }
    }

    /**
     * 发送语音电话通知给特定人员
     * @param meeting 会议信息
     * @param personnelId 人员ID
     * @param notificationType 通知类型
     * @return 发送结果描述
     */
    private fun sendVoiceCallNotification(meeting: Meeting, personnelId: String, notificationType: Int): Pair<String, String> {
        // 参数准备
        val meetingTitle = meeting.title
        val creatorName = meeting.creatorName
        val meetingTimeRange = if (meeting.startTime != null && meeting.endTime != null) {
            "${dateFormatter.format(meeting.startTime)} 到 ${dateFormatter.format(meeting.endTime)}"
        } else "未知时间"
        val meetingroom = if (meeting.meetingroomId.isNotEmpty()) meetingroomMapper.getInfo(meeting.meetingroomId) else null
        val meetingroomName = formatMeetingRoomName(meetingroom?.name ?: "未知地点")
        val personnel = personnelMapper.getInfo(personnelId)

        // 校验人员信息
        if (personnel == null || personnel.phone.isEmpty()) {
            return "无法发送语音电话通知：人员不存在或没有电话号码" to ""
        }

        // 根据通知类型选择模板配置
        val type = notificationType

        // 获取对应类型的模板配置
        val (startCode, menuKeyMapList) = when (type) {
            0 -> { // 会议创建通知
                createVmsTemplateConfig.startCode to createVmsTemplateConfig.menuKeyMapList
            }
            1 -> { // 会议开始通知（使用专门的会议开始通知模板配置）
                startVmsTemplateConfig.startCode to startVmsTemplateConfig.menuKeyMapList
            }
            2 -> { // 会议取消通知
                cancelVmsTemplateConfig.startCode to cancelVmsTemplateConfig.menuKeyMapList
            }
            3 -> { // 会议调整通知
                changeVmsTemplateConfig.startCode to changeVmsTemplateConfig.menuKeyMapList
            }
            else -> {
                "" to listOf()
            }
        }

        // 校验模板配置
        if (startCode.isEmpty()) {
            log.error("No VMS template configured for meetingnotification type $type")
            return "语音模板未配置，无法发送会议通知" to ""
        }

        // 构建TTS参数（JSON字符串）
        val ttsParams = "{" +
            "\"meetingTitle\":\"$meetingTitle\"," +
            "\"creatorName\":\"$creatorName\"," +
            "\"meetingrommName\":\"$meetingroomName\"," +
            "\"personnelName\":\"${personnel.name}\"," +
            "\"meetingStartTime\":\"$meetingTimeRange\"," +
            "\"meetingEndTime\":\"${dateFormatter.format(meeting.endTime)}\"," +
            "\"NoName\":\"$applicationName\"}"

        // 将菜单按键配置转换为Map列表
        val vmsMenuOptions = menuKeyMapList.map { keyMap ->
            mapOf(
                "key" to keyMap.key,
                "code" to keyMap.code,
                "ttsParams" to ttsParams
            )
        }

        // 调用阿里云VMS接口发起语音电话
        val (success, callId) = aliyunVmsUtil.call(
            personnel.phone,
            startCode,
            ttsParams,
            menuKeyMapList = vmsMenuOptions
        )

        // 结果处理
        val typeDesc = when (type) {
            0 -> "创建"
            1 -> "开始"
            2 -> "取消"
            3 -> "调整"
            else -> "通知"
        }
        return if (success) {
            log.info("VMS $typeDesc meetingnotification sent successfully to ${personnel.name} (${personnel.phone}) using template: $startCode, callId: $callId")
            "已成功发送会议${typeDesc}语音电话通知给 ${personnel.name}" to callId
        } else {
            log.error("Failed to send VMS $typeDesc meetingnotification to ${personnel.name} (${personnel.phone}) using template: $startCode")
            "发送会议${typeDesc}语音电话通知给 ${personnel.name} 失败" to callId
        }
    }

    /**
     * 取消通知
     */
    fun cancelMeetingnotification(id: String, reason: String): Result {
        val status = 2 // 已取消
        val updateResult = mapper.updateMeetingnotificationStatus(id, status, reason, "")
        return if (updateResult > 0) {
            Result.getSuccessInfo("通知取消成功")
        } else {
            Result.getError("通知取消失败")
        }
    }
}

/**
 * 会议通知Controller类
 */
@RestController
@RequestMapping("/api/Meetingnotification")
class MeetingnotificationResource(
    service: MeetingnotificationService
) : BaseResource<MeetingnotificationSearch, Meetingnotification, MeetingnotificationMapper, MeetingnotificationService>(service)

/**
 * 会议通知调度器
 * 实现两种机制结合的方案：
 * 1. 定时任务：低频率全表扫描，作为兜底机制
 * 2. 事件驱动：在会议通知创建和修改时，动态注册定时任务，准确发送通知
 */
@Configuration
@EnableScheduling
class MeetingnotificationScheduler {

    @Autowired
    private lateinit var meetingnotificationService: MeetingnotificationService

    @Autowired
    private lateinit var meetingnotificationMapper: MeetingnotificationMapper

    @Autowired
    private lateinit var taskScheduler: TaskScheduler

    @Autowired
    private lateinit var meetingMapper: MeetingMapper

    // 存储通知ID和对应的定时任务
    private val meetingnotificationTaskMap = ConcurrentHashMap<String, ScheduledFuture<*>>()

    // 系统默认时区
    private val systemZoneId = ZoneId.systemDefault()

    /**
     * 系统启动时初始化所有未发送的通知任务
     */
    @PostConstruct
    fun initScheduledTasks() {
        try {
            log.info("System startup, initializing meetingnotification scheduler...")

            // 查询所有未发送的通知
            val pendingMeetingnotifications = meetingnotificationService.getPendingMeetingnotifications()

            log.info("Found ${pendingMeetingnotifications.size} pending meetingnotifications to schedule")

            val now = Date()
            // 为每个未发送的通知注册定时任务
            for (meetingnotification in pendingMeetingnotifications) {
                if (meetingnotification.time != null && meetingnotification.time!!.after(now)) {
                    scheduleMeetingnotificationTask(meetingnotification)
                    log.info("Registered meetingnotification task for meeting [${meetingnotification.meetingId}], scheduled time: ${meetingnotification.time}")
                } else if (meetingnotification.time != null && meetingnotification.time!!.before(now)) {
                    // 通知时间已过但仍为未发送状态，根据会议状态决定是否发送
                    val meeting = meetingnotification.meeting

                    // 如果是会议取消通知，即使会议已取消也允许发送
                    // 对于其他类型的通知，只有会议处于待开始或进行中状态才发送
                    if (meeting != null && (
                                (meetingnotification.type == 2 && meeting.status == 2) || // 会议取消通知且会议已取消
                                        (meeting.status == 0 || meeting.status == 1) // 会议待开始或进行中
                                )) {
                        // 发送通知
                        sendMeetingnotification(meetingnotification.id)
                        log.warn("Found overdue meetingnotification [${meetingnotification.id}] for meeting [${meetingnotification.meetingId}], sent meetingnotification")
                    } else {
                        // 会议已结束或已取消，标记通知为已取消
                        val reason = when (meeting?.status) {
                            2 -> "通知时间已过期且会议已取消"
                            3 -> "通知时间已过期且会议已结束"
                            else -> "通知时间已过期且无法获取会议状态"
                        }
                        meetingnotificationService.cancelMeetingnotification(meetingnotification.id, reason)
                        log.warn("Found overdue meetingnotification [${meetingnotification.id}] for meeting [${meetingnotification.meetingId}], marked as cancelled: $reason")
                    }
                }
            }

            log.info("Meetingnotification scheduler initialization completed")
        } catch (e: Exception) {
            errorlog(e)
            log.error("Meetingnotification scheduler initialization failed: ${e.message}")
        }
    }

    /**
     * 每小时执行一次的全表扫描，作为兜底机制
     * 防止由于系统重启或其他原因导致的定时任务丢失
     */
    @Scheduled(cron = "0 15 * * * ?") // 每小时的15分执行一次，错开会议状态检查
    fun scanAndSendMeetingnotifications() {
        try {
            log.info("Starting scheduled meetingnotification scan...")

            // 查询所有未发送的通知
            val pendingMeetingnotifications = meetingnotificationService.getPendingMeetingnotifications()

            // 检查是否有需要立即发送的通知
            val now = Date()
            val overdueMeetingnotifications = pendingMeetingnotifications.filter { it.time != null && it.time!!.before(now) }

            if (overdueMeetingnotifications.isNotEmpty()) {
                log.info("Found ${overdueMeetingnotifications.size} overdue meetingnotifications to process")

                for (meetingnotification in overdueMeetingnotifications) {
                    try {
                        // 获取会议状态
                        val meeting = meetingnotification.meeting

                        // 如果是会议取消通知，即使会议已取消也允许发送
                        // 对于其他类型的通知，只有会议处于待开始或进行中状态才发送
                        if (meeting != null && (
                                    (meetingnotification.type == 2 && meeting.status == 2) || // 会议取消通知且会议已取消
                                            (meeting.status == 0 || meeting.status == 1) // 会议待开始或进行中
                                    )) {
                            // 发送通知
                            sendMeetingnotification(meetingnotification.id)
                            log.warn("Found overdue meetingnotification [${meetingnotification.id}] for meeting [${meetingnotification.meetingId}], sent meetingnotification")
                        } else {
                            // 会议已结束或已取消，标记通知为已取消
                            val reason = when (meeting?.status) {
                                2 -> "通知时间已过期且会议已取消"
                                3 -> "通知时间已过期且会议已结束"
                                else -> "通知时间已过期且无法获取会议状态"
                            }
                            meetingnotificationService.cancelMeetingnotification(meetingnotification.id, reason)
                            log.warn("Found overdue meetingnotification [${meetingnotification.id}] for meeting [${meetingnotification.meetingId}], marked as cancelled: $reason")
                        }
                    } catch (e: Exception) {
                        log.error("Failed to process meetingnotification [${meetingnotification.id}]: ${e.message}")
                        errorlog(e)
                    }
                }
            }

            // 检查是否有需要重新注册的定时任务
            val futureMeetingnotifications = pendingMeetingnotifications.filter { it.time != null && it.time!!.after(now) }

            for (meetingnotification in futureMeetingnotifications) {
                if (!meetingnotificationTaskMap.containsKey(meetingnotification.id)) {
                    scheduleMeetingnotificationTask(meetingnotification)
                    log.info("Re-registered meetingnotification task for meeting [${meetingnotification.meetingId}]")
                }
            }

            // 清理已发送或已取消但仍在任务Map中的通知
            cleanupMeetingnotificationTasks()

            log.info("Scheduled meetingnotification scan completed")
        } catch (e: Exception) {
            errorlog(e)
            log.error("Error during scheduled meetingnotification scan: ${e.message}")
        }
    }

    /**
     * 清理已发送或已取消但仍在任务Map中的通知
     */
    private fun cleanupMeetingnotificationTasks() {
        try {
            val taskIds = meetingnotificationTaskMap.keys.toList()

            for (id in taskIds) {
                val meetingnotification = meetingnotificationMapper.getInfo(id)
                if (meetingnotification == null || meetingnotification.status != 0) {
                    // 通知不存在或状态不是未发送，取消任务
                    cancelMeetingnotificationTask(id)
                    log.info("Cleaned up meetingnotification task for meetingnotification [${id}]")
                }
            }
        } catch (e: Exception) {
            errorlog(e)
        }
    }

    /**
     * 为通知注册定时任务
     */
    fun scheduleMeetingnotificationTask(meetingnotification: Meetingnotification) {
        if (meetingnotification.id.isEmpty() || meetingnotification.time == null) {
            return
        }

        // 取消已存在的任务
        cancelMeetingnotificationTask(meetingnotification.id)

        // 如果通知时间已过，则不需要注册任务
        if (meetingnotification.time!!.before(Date())) {
            return
        }

        // 获取会议状态
        val meeting = meetingMapper.getInfo(meetingnotification.meetingId)
        if (meeting == null) {
            log.info("Skip registering meetingnotification task for meetingnotification [${meetingnotification.id}] as meeting does not exist")
            return
        }

        // 如果接收者是会议发起人，则不注册通知任务
        if (meetingnotification.personnelId == meeting.creatorId) {
            log.info("Skip registering meetingnotification task for meeting [${meetingnotification.meetingId}] to creator [${meeting.creatorId}]")
            return
        }

        // 如果是会议取消通知，即使会议已取消也允许发送
        // 对于其他类型的通知，如果会议已取消或已结束，则不注册任务
        if (meetingnotification.type != 2 && (meeting.status == 2 || meeting.status == 3)) {
            log.info("Skip registering meetingnotification task for meeting [${meetingnotification.meetingId}] as meeting is cancelled or ended")
            return
        }

        // 注册新任务，使用系统时区正确处理时间
        // 将Date转换为当前系统时区的ZonedDateTime，再获取Instant
        val zonedDateTime = meetingnotification.time!!.toInstant().atZone(systemZoneId)
        log.info("Scheduling meetingnotification for meeting [${meetingnotification.meetingId}] at local time: $zonedDateTime")

        val task = taskScheduler.schedule({
            try {
                sendMeetingnotification(meetingnotification.id)
            } catch (e: Exception) {
                errorlog(e)
            } finally {
                // 任务执行完成后从Map中移除
                meetingnotificationTaskMap.remove(meetingnotification.id)
            }
        }, zonedDateTime.toInstant())

        // 将任务保存到Map中
        meetingnotificationTaskMap[meetingnotification.id] = task
        log.info("Registered meetingnotification task for meeting [${meetingnotification.meetingId}], scheduled time: ${meetingnotification.time}, timezone: $systemZoneId")
    }

    /**
     * 取消通知定时任务
     */
    fun cancelMeetingnotificationTask(meetingnotificationId: String) {
        val task = meetingnotificationTaskMap.remove(meetingnotificationId)
        task?.cancel(false)
    }

    /**
     * 发送通知
     */
    private fun sendMeetingnotification(meetingnotificationId: String) {
        try {
            // 发送通知
            val result = meetingnotificationService.sendMeetingnotification(meetingnotificationId)
            if (result.rlt != Result.SUCCESS) {
                log.error("Failed to send meetingnotification [$meetingnotificationId]: ${result.info}")
            }
        } catch (e: Exception) {
            log.error("Error during meetingnotification sending [$meetingnotificationId]: ${e.message}")
            errorlog(e)
        }
    }
}
