package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Commuteroutestop: BaseModel() {
    var routeId: String = ""
    var sequence: Int = 0
    var sysDeleted: Int = 0

    var name: String = ""
    var lat: Double = 0.0
    var lng: Double = 0.0
}

class CommuteroutestopSearch: BaseSearch() {
    var routeId: String = ""
    var stopId: String = ""
}

@Mapper
interface CommuteroutestopMapper : BaseMapper<Commuteroutestop> {
    @Select("""
        <script>
            SELECT crs.* FROM tbl_commuteroutestop crs
            <where>
                AND crs.sysDeleted = 0
                <if test="routeId != ''">
                    AND crs.routeId = #{routeId}
                </if>
                <if test="stopId != ''">
                    AND crs.stopId = #{stopId}
                </if>
            </where>
            ORDER BY crs.sequence ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commuteroutestop>

    // 根据routeId删除
    @Delete("""
        DELETE FROM tbl_commuteroutestop
        WHERE routeId = #{routeId}
    """)
    fun deleteByRouteId(routeId: String)
}

@Service
class CommuteroutestopService(mapper: CommuteroutestopMapper) : BaseService<Commuteroutestop, CommuteroutestopMapper>(mapper)

@RestController
@RequestMapping("/api/commuteroutestop")
class CommuteroutestopResource(service: CommuteroutestopService) : BaseResource<CommuteroutestopSearch, Commuteroutestop, CommuteroutestopMapper, CommuteroutestopService>(service)
