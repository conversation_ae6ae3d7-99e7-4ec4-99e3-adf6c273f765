package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Commuteroutestop: BaseModel() {
    var routeId: String = ""
    var stopId: String = ""
    var sequence: Int = 0
    var sysDeleted: Int = 0
}

class CommuteroutestopSearch: BaseSearch() {
    var routeId: String = ""
    var stopId: String = ""
}

@Mapper
interface CommuteroutestopMapper : BaseMapper<Commuteroutestop> {
    @Select("""
        <script>
            SELECT * FROM tbl_commuteroutestop
            <where>
                AND sysDeleted = 0
                <if test="routeId != ''">
                    AND routeId = #{routeId}
                </if>
                <if test="stopId != ''">
                    AND stopId = #{stopId}
                </if>
            </where>
            ORDER BY routeId ASC, sequence ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commuteroutestop>
}

@Service
class CommuteroutestopService(mapper: CommuteroutestopMapper) : BaseService<Commuteroutestop, CommuteroutestopMapper>(mapper)

@RestController
@RequestMapping("/api/commuteroutestop")
class CommuteroutestopResource(service: CommuteroutestopService) : BaseResource<CommuteroutestopSearch, Commuteroutestop, CommuteroutestopMapper, CommuteroutestopService>(service)
