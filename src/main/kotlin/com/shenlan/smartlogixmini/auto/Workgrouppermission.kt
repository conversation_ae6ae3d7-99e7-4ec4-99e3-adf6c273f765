package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 工作组权限关联实体类
 */
class Workgrouppermission : BaseModel() {
    // 数据库表字段

    /** 工作组ID */
    var workgroupId: String = ""
    /** 权限ID */
    var permissionId: String = ""

    // 关联字段

    /** 关联的工作组信息 */
    var workgroup: Workgroup? = null
    /** 关联的权限信息 */
    var permission: Permission? = null
}

/**
 * 工作组权限查询条件类
 */
class WorkgrouppermissionSearch : BaseSearch() {
    /** 工作组ID */
    var workgroupId: String = ""
    /** 权限ID */
    var permissionId: String = ""
    /** 权限名称 */
    var permissionName: String = ""
    /** 是否加载工作组信息 */
    var loadWorkgroup: Boolean = false
    /** 是否加载权限信息 */
    var loadPermission: Boolean = false
}

/**
 * 工作组权限Mapper接口
 */
@Mapper
interface WorkgrouppermissionMapper : BaseMapper<Workgrouppermission> {

    @Select("""
        <script>
            SELECT wp.* FROM tbl_workgrouppermission wp
            <if test="permissionName != ''">
                INNER JOIN tbl_permission p ON wp.permissionId = p.id
            </if>
            <where>
                wp.sysDeleted = 0
                <if test="workgroupId != ''">
                    AND wp.workgroupId = #{workgroupId}
                </if>
                <if test="permissionId != ''">
                    AND wp.permissionId = #{permissionId}
                </if>
                <if test="permissionName != ''">
                    AND p.name = #{permissionName}
                </if>
            </where>
            ORDER BY wp.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Workgrouppermission>

    /**
     * 查询指定工作组和权限的关联记录
     */
    @Select("""
        SELECT * FROM tbl_workgrouppermission
        WHERE sysDeleted = 0 AND workgroupId = #{workgroupId} AND permissionId = #{permissionId}
        LIMIT 1
    """)
    fun getInfoByWorkgroupAndPermission(workgroupId: String, permissionId: String): Workgrouppermission?

    /**
     * 删除指定工作组的所有权限关联记录
     */
    @Delete("""
        DELETE FROM tbl_workgrouppermission
        WHERE workgroupId = #{workgroupId}
    """)
    fun deleteByWorkgroupId(workgroupId: String): Int
}

/**
 * 工作组权限Service类
 */
@Service
class WorkgrouppermissionService(
    mapper: WorkgrouppermissionMapper,
    private val workgroupMapper: WorkgroupMapper,
    private val permissionMapper: PermissionMapper
) : BaseService<Workgrouppermission, WorkgrouppermissionMapper>(mapper) {

    /**
     * 重写save方法，确保工作组权限关联记录不重复
     */
    @Transactional
    override fun save(model: Workgrouppermission): Result {
        // 检查工作组ID不能为空
        if (model.workgroupId.isEmpty()) {
            return Result.getError("工作组ID不能为空")
        }

        // 检查权限ID不能为空
        if (model.permissionId.isEmpty()) {
            return Result.getError("权限ID不能为空")
        }

        // 检查工作组是否存在
        val workgroup = workgroupMapper.getInfo(model.workgroupId)
        if (workgroup == null) {
            return Result.getError("指定的工作组不存在")
        }

        // 检查权限是否存在
        val permission = permissionMapper.getInfo(model.permissionId)
        if (permission == null) {
            return Result.getError("指定的权限不存在")
        }

        // 查询是否存在相同的工作组权限关联记录（除了当前编辑的记录）
        val existing = mapper.getInfoByWorkgroupAndPermission(model.workgroupId, model.permissionId)
        if (existing != null && existing.id != model.id) {
            return Result.getError("该工作组已拥有此权限")
        }

        // 验证通过，调用父类的save方法完成保存
        return super.save(model)
    }

    /**
     * 重写getList方法，根据需要加载关联信息
     */
    override fun getList(page: BaseSearch): Result {
        val result = super.getList(page)
        if (page is WorkgrouppermissionSearch) {
            val workgrouppermissionList = result.toList<Workgrouppermission>()

            // 如果需要加载工作组信息
            if (page.loadWorkgroup) {
                workgrouppermissionList.forEach { loadWorkgroup(it) }
            }

            // 如果需要加载权限信息
            if (page.loadPermission) {
                workgrouppermissionList.forEach { loadPermission(it) }
            }
        }
        return result
    }

    /**
     * 重写getInfo方法，加载关联信息
     */
    override fun getInfo(id: String): Result {
        val workgrouppermission = mapper.getInfo(id)
        if (workgrouppermission != null) {
            // 加载工作组信息
            loadWorkgroup(workgrouppermission)
            // 加载权限信息
            loadPermission(workgrouppermission)
        }
        return Result.getSuccess(workgrouppermission)
    }

    /**
     * 加载工作组信息
     */
    private fun loadWorkgroup(workgrouppermission: Workgrouppermission) {
        if (workgrouppermission.workgroupId.isNotEmpty()) {
            workgrouppermission.workgroup = workgroupMapper.getInfo(workgrouppermission.workgroupId)
        }
    }

    /**
     * 加载权限信息
     */
    private fun loadPermission(workgrouppermission: Workgrouppermission) {
        if (workgrouppermission.permissionId.isNotEmpty()) {
            workgrouppermission.permission = permissionMapper.getInfo(workgrouppermission.permissionId)
        }
    }

    /**
     * 设置工作组权限列表（先删除原有权限，再添加新权限）
     */
    @Transactional
    fun setPermissions(workgroupId: String, permissionNameList: List<String>): Result {
        // 检查工作组是否存在
        val workgroup = workgroupMapper.getInfo(workgroupId)
        if (workgroup == null) {
            return Result.getError("指定的工作组不存在")
        }

        // 先删除该工作组的所有原有权限
        mapper.deleteByWorkgroupId(workgroupId)

        // 如果权限名称列表不为空，则添加新权限
        if (permissionNameList.isNotEmpty()) {
            // 创建新的权限关联记录列表
            val workgrouppermissionList = mutableListOf<BaseModel>()
            permissionNameList.forEach { permissionName ->
                val permission = permissionMapper.getInfoByName(permissionName)
                if (permission != null) {
                    val workgrouppermission = Workgrouppermission()
                    workgrouppermission.id = uuid()
                    workgrouppermission.workgroupId = workgroupId
                    workgrouppermission.permissionId = permission.id
                    workgrouppermissionList.add(workgrouppermission)
                }
            }

            // 使用父类的insertList方法批量插入
            if (workgrouppermissionList.isNotEmpty()) {
                mapper.insertList(workgrouppermissionList)
            }
        }

        return Result.getSuccessInfo("权限设置成功")
    }
}

/**
 * 工作组权限Controller类
 */
@RestController
@RequestMapping("/api/Workgrouppermission")
class WorkgrouppermissionResource(service: WorkgrouppermissionService) : BaseResource<WorkgrouppermissionSearch, Workgrouppermission, WorkgrouppermissionMapper, WorkgrouppermissionService>(service)
