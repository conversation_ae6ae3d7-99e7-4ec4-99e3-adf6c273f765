package com.shenlan.smartlogixmini.auto

import com.fasterxml.jackson.databind.ObjectMapper
import com.shenlan.smartlogixmini.util.*
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.Signature
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import java.util.concurrent.ConcurrentLinkedQueue


class Accesslog : BaseModel() {
    /** 用户ID */
    var userId: String = ""
    /** 用户姓名 */
    var userName: String = ""
    /** 组织机构名称 */
    var orgName: String = ""
    /** 类名 */
    var className: String = ""
    /** 方法名 */
    var methodName: String = ""
    /** 执行时间(毫秒) */
    var executeTime: Int = 0
    /** 参数信息 */
    var parameters: String = ""
    /** 返回结果 */
    var result: String = ""
    /** 是否出错(0-否,1-是) */
    var ifError: Int = 0
    /** 错误信息 */
    var errorInfo: String = ""
}

@Mapper
interface AccesslogMapper : BaseMapper<Accesslog> {
    @Select("""
        <script>
        SELECT * FROM tbl_accesslog
        <where>
            <if test="userId != ''">
                AND userId = #{userId}
            </if>
        </where>
        ORDER BY sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Accesslog>

    /**
     * 删除超过指定天数的访问日志
     * @param days 保留天数，超过此天数的日志将被删除
     * @return 删除的记录数
     */
    @Delete("DELETE FROM tbl_accesslog WHERE sysCreated < DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    fun deleteOldLogs(days: Int): Int
}

class AccesslogSearch : BaseSearch() {
    /** 用户ID */
    var userId: String = ""
}

@Service
class AccesslogService(mapper: AccesslogMapper) : BaseService<Accesslog, AccesslogMapper>(mapper)

@RestController
@RequestMapping("/api/Accesslog")
class AccesslogResource(service: AccesslogService) : BaseResource<AccesslogSearch, Accesslog, AccesslogMapper, AccesslogService>(service)

@Aspect
@Component
class AccessAspect {

    // 线程安全的队列，替代ArrayList
    companion object {
        private val accessQueue = ConcurrentLinkedQueue<Accesslog>()
        private const val MAX_QUEUE_SIZE = 10000 // 最大队列容量，防止内存泄漏

        /**
         * 获取并清空访问日志队列
         */
        fun getAndClearAccessLogs(): List<Accesslog> {
            val logs = mutableListOf<Accesslog>()
            while (accessQueue.isNotEmpty() && logs.size < 1000) { // 批量处理，每次最多1000条
                accessQueue.poll()?.let { logs.add(it) }
            }
            return logs
        }

        /**
         * 获取当前队列大小
         */
        fun getQueueSize(): Int = accessQueue.size
    }

    @Around("execution(* com.shenlan.smartlogixmini.auto.*Resource.*(..))")
    fun accessAround(joinPoint: ProceedingJoinPoint): Any? {
        val className = joinPoint.target.javaClass.simpleName
        val methodName = joinPoint.signature.name
        val parameters = formatParameters(joinPoint.signature, joinPoint.args)

        log.info("Enter: {}.{}() with argument[s] = {}", className, methodName, parameters)

        val startTime = System.currentTimeMillis()
        var result: Any? = null
        var hasError = false
        var errorMessage = ""

        try {
            // 执行目标方法
            result = joinPoint.proceed()
        } catch (e: Exception) {
            hasError = true
            errorMessage = e.message ?: "Unknown error"
            log.error("Error in {}.{}(): {}", className, methodName, errorMessage)
            errorlog(e)
            throw e // 重新抛出异常，保持原有行为
        } finally {
            val executeTime = System.currentTimeMillis() - startTime
            log.info("Exit: {}.{}() Time = {}ms", className, methodName, executeTime)

            // 记录访问日志
            recordAccessLog(className, methodName, parameters, executeTime.toInt(), result, hasError, errorMessage)
        }

        return result
    }

    /**
     * 定时任务：批量保存访问日志
     * 每60秒执行一次，从AccessAspect队列中获取日志并保存到数据库
     */
    @Scheduled(fixedDelay = (60 * 1000).toLong())
    fun insertAccessLogs() {
        try {
            val logs = getAndClearAccessLogs()
            if (logs.isNotEmpty()) {
                log.info("Saving {} access logs to database", logs.size)
                getBean(AccesslogMapper::class.java).insertList(logs)
                log.debug("Successfully saved {} access logs", logs.size)
            }
        } catch (e: Exception) {
            log.error("Failed to save access logs to database: {}", e.message)
            errorlog(e)
            // 发生异常时，队列已经被清空，避免重复处理
        }
    }

    /**
     * 监控任务：定期检查访问日志队列状态
     * 每5分钟执行一次，监控队列大小，及时发现潜在问题
     */
    @Scheduled(fixedDelay = (5 * 60 * 1000).toLong())
    fun monitorAccessLogQueue() {
        val queueSize = getQueueSize()
        if (queueSize > 5000) {
            log.warn("Access log queue size is high: {} entries", queueSize)
        } else if (queueSize > 0) {
            log.debug("Access log queue size: {} entries", queueSize)
        }
    }

    /**
     * 清理任务：定期清理超过30天的访问日志
     * 每天凌晨2点执行一次
     */
    @Scheduled(cron = "0 0 2 * * ?")
    fun cleanOldAccessLogs() {
        try {
            log.info("Starting cleanup of old access logs")
            val deletedCount = getBean(AccesslogMapper::class.java).deleteOldLogs(30)
            log.info("Cleaned up {} access logs older than 30 days", deletedCount)
        } catch (e: Exception) {
            log.error("Failed to clean up old access logs: {}", e.message)
            errorlog(e)
        }
    }

    /**
     * 使用Jackson格式化参数为JSON格式，并使用真实的参数名
     * 对特殊类型（如MultipartFile）进行智能摘要处理
     */
    private fun formatParameters(signature: Signature, args: Array<Any>?): String {
        if (args.isNullOrEmpty()) {
            return "{}"
        }

        return try {
            val objectMapper = getBean(ObjectMapper::class.java)
            val paramMap = mutableMapOf<String, Any>()

            // 获取真实的参数名
            val parameterNames = getParameterNames(signature)

            args!!.forEachIndexed { index, arg ->
                val paramName = if (index < parameterNames.size) {
                    parameterNames[index]
                } else {
                    "arg${index + 1}" // 降级使用通用名称
                }

                // 智能处理特殊类型
                val processedArg = when {
                    arg.javaClass.name.contains("MultipartFile") -> {
                        extractFileInfo(arg)
                    }
                    arg.javaClass.name.contains("HttpServletRequest") -> {
                        mapOf(
                            "type" to "HttpServletRequest",
                            "method" to getProperty(arg, "method"),
                            "requestURI" to getProperty(arg, "requestURI")
                        )
                    }
                    arg.javaClass.name.contains("HttpServletResponse") -> {
                        mapOf("type" to "HttpServletResponse")
                    }
                    else -> arg
                }

                paramMap[paramName] = processedArg
            }

            objectMapper.writeValueAsString(paramMap)
        } catch (e: Exception) {
            log.warn("Failed to format parameters with Jackson: {}", e.message)
            // 降级处理：使用简单的字符串表示
            try {
                val paramMap = mutableMapOf<String, String>()
                val parameterNames = getParameterNames(signature)

                args!!.forEachIndexed { index, arg ->
                    val paramName = if (index < parameterNames.size) {
                        parameterNames[index]
                    } else {
                        "arg${index + 1}"
                    }
                    // 降级处理时也要处理特殊类型
                    val argValue = when {
                        arg.javaClass.name.contains("MultipartFile") -> {
                            "[MultipartFile:${getProperty(arg, "originalFilename") ?: "unknown"}]"
                        }
                        arg.javaClass.name.contains("HttpServletRequest") -> {
                            "[HttpServletRequest]"
                        }
                        arg.javaClass.name.contains("HttpServletResponse") -> {
                            "[HttpServletResponse]"
                        }
                        else -> arg.toString()
                    }
                    paramMap[paramName] = argValue
                }
                "{${paramMap.entries.joinToString(",") { "\"${it.key}\":\"${it.value}\"" }}}"
            } catch (fallbackException: Exception) {
                log.warn("Fallback parameter formatting also failed: {}", fallbackException.message)
                "{}"
            }
        }
    }

    /**
     * 提取文件信息摘要，避免记录文件内容导致日志过长
     */
    private fun extractFileInfo(file: Any): Map<String, Any?> {
        return try {
            // 直接转换为 MultipartFile 接口使用，避免反射访问权限问题
            if (file is MultipartFile) {
                mapOf(
                    "type" to "MultipartFile",
                    "originalFilename" to file.originalFilename,
                    "size" to file.size,
                    "contentType" to file.contentType,
                    "isEmpty" to file.isEmpty
                )
            } else {
                // 降级为反射方式
                mapOf(
                    "type" to "MultipartFile",
                    "originalFilename" to getProperty(file, "originalFilename"),
                    "size" to getProperty(file, "size"),
                    "contentType" to getProperty(file, "contentType"),
                    "isEmpty" to getProperty(file, "isEmpty")
                )
            }
        } catch (e: Exception) {
            log.debug("Failed to extract file info: {}", e.message)
            mapOf("type" to "MultipartFile", "error" to "Failed to extract file info")
        }
    }

    /**
     * 通过反射获取对象属性值
     */
    private fun getProperty(obj: Any, propertyName: String): Any? {
        return try {
            // 先设置方法可访问
            val getterName = "get${propertyName.capitalize()}"
            val method = obj.javaClass.getMethod(getterName)
            method.isAccessible = true
            method.invoke(obj)
        } catch (e: NoSuchMethodException) {
            try {
                // 尝试is开头的boolean getter方法
                val isGetterName = "is${propertyName.capitalize()}"
                val method = obj.javaClass.getMethod(isGetterName)
                method.isAccessible = true
                method.invoke(obj)
            } catch (e2: NoSuchMethodException) {
                try {
                    // 特殊处理isEmpty方法可能的命名差异
                    if (propertyName == "isEmpty") {
                        val method = obj.javaClass.getMethod("empty")
                        method.isAccessible = true
                        method.invoke(obj)
                    } else {
                        log.debug("No method found for property '{}' in {}", propertyName, obj.javaClass.simpleName)
                        null
                    }
                } catch (e3: Exception) {
                    log.debug("Failed to get property '{}' from {}: {}", propertyName, obj.javaClass.simpleName, e3.message)
                    null
                }
            } catch (e2: Exception) {
                log.debug("Failed to get property '{}' from {}: {}", propertyName, obj.javaClass.simpleName, e2.message)
                null
            }
        } catch (e: Exception) {
            log.debug("Failed to get property '{}' from {}: {}", propertyName, obj.javaClass.simpleName, e.message)
            null
        }
    }

    /**
     * 获取方法的真实参数名
     */
    private fun getParameterNames(signature: Signature): List<String> {
        return try {
            if (signature is MethodSignature) {
                val method = signature.method
                val parameterNames = method.parameters.map { parameter ->
                    // 尝试获取参数名，如果没有编译时保留参数名信息，则使用参数类型作为名称
                    if (parameter.isNamePresent) {
                        parameter.name
                    } else {
                        // 降级：使用参数类型的简单名称
                        parameter.type.simpleName.toLowerCase() + (method.parameters.indexOf(parameter) + 1)
                    }
                }
                parameterNames
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            log.debug("Failed to get parameter names: {}", e.message)
            emptyList()
        }
    }

    /**
     * 记录访问日志
     */
    private fun recordAccessLog(
        className: String,
        methodName: String,
        parameters: String,
        executeTime: Int,
        result: Any?,
        hasError: Boolean,
        errorMessage: String
    ) {
        try {
            // 检查队列容量，防止内存泄漏
            if (accessQueue.size >= MAX_QUEUE_SIZE) {
                log.warn("Access log queue is full (size: {}), dropping oldest entries", accessQueue.size)
                // 移除一半的旧记录
                repeat(MAX_QUEUE_SIZE / 2) {
                    accessQueue.poll()
                }
            }

            // 只有当结果是Result类型时才记录（保持原有逻辑）
            if (result is Result) {
                // 获取当前用户信息，失败时使用默认值
                var userId = ""
                var userName = ""
                var orgName = ""

                val currentUser = getUser()
                if (currentUser != null) {
                    userId = currentUser.id
                    userName = currentUser.name
                    orgName = currentUser.organization?.name ?: ""
                }

                // 使用Jackson将result转换为JSON字符串
                val resultJson = try {
                    if (hasError) {
                        "Error: $errorMessage"
                    } else {
                        val objectMapper = getBean(ObjectMapper::class.java)
                        objectMapper.writeValueAsString(result)
                    }
                } catch (e: Exception) {
                    log.warn("Failed to convert result to JSON: {}", e.message)
                    // 降级处理：使用toString()
                    if (hasError) "Error: $errorMessage" else result.toString()
                }

                val accessLog = Accesslog().apply {
                    id = uuid()
                    this.userId = userId
                    this.userName = userName
                    this.orgName = orgName
                    this.className = className
                    this.methodName = methodName
                    this.executeTime = executeTime
                    this.parameters = parameters
                    this.result = resultJson
                    this.ifError = if (hasError) 1 else 0
                    this.errorInfo = errorMessage
                }

                accessQueue.offer(accessLog)
                log.debug("Access log recorded for {}.{}(), queue size: {}", className, methodName, accessQueue.size)
            }
        } catch (e: Exception) {
            // 记录日志失败不应影响业务逻辑
            log.error("Failed to record access log for {}.{}(): {}", className, methodName, e.message)
            errorlog(e)
        }
    }
}
