package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*
import kotlin.system.measureTimeMillis

class Takeoutmenu: BaseModel {
    var menuDate: String = ""
    var sysDeleted: Int = 0

    var takeoutMenuDishList: MutableList<Takeoutmenudish> = mutableListOf()

    constructor()

    constructor(menuDate: String) {
        this.id = uuid()
        this.menuDate = menuDate
    }
}

class TakeoutmenuSearch: BaseSearch() {
    var menuDate: String = ""
}

@Mapper
interface TakeoutmenuMapper : BaseMapper<Takeoutmenu> {
    @Select("""
        <script>
            SELECT * FROM tbl_takeoutmenu
            <where>
                AND sysDeleted = 0
                <if test="menuDate != ''">
                    AND menuDate = #{menuDate}
                </if>
            </where>
            ORDER BY menuDate DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Takeoutmenu>

    // 校验menuDate是否存在
    @Select("SELECT COUNT(*) FROM tbl_takeoutmenu WHERE menuDate = #{menuDate} AND sysDeleted = 0")
    fun checkMenuDateExists(menuDate: String): Int

    @Select("SELECT * FROM tbl_takeoutmenu WHERE menuDate = #{menuDate} AND sysDeleted = 0")
    fun getMenuByDate(menuDate: String): List<Takeoutmenu>
}

@Service
class TakeoutmenuService(mapper: TakeoutmenuMapper) : BaseService<Takeoutmenu, TakeoutmenuMapper>(mapper) {
    override fun save(model: Takeoutmenu): Result {
        val takeoutMenuDishMapper = getBean(TakeoutmenudishMapper::class.java)

        return try {
            val time = measureTimeMillis {
                // 1.1 获取当天菜谱
                val takeoutMenu = mapper.getMenuByDate(model.menuDate).elementAtOrNull(0)
                    ?: return Result.getError("当前日期未开放")

                // 1.2 保存菜谱详细
                takeoutMenuDishMapper.insertList(model.takeoutMenuDishList.map { takeoutMenuDish ->
                    Takeoutmenudish(takeoutMenu.id, takeoutMenuDish)
                })
            }

            log.info("Takeout menu saved successfully, cost $time ms")

            Result.getSuccess(model.id)
        } catch (e: Exception) {
            log.error("Failed to save takeout menu: ${e.message}")
            Result.getError("外卖菜谱发布失败: ${e.message}")
        }
    }

    fun getMenuByDate(menuDate: String): Result {
        val takeoutMenu = mapper.getMenuByDate(menuDate).elementAtOrNull(0)
            ?: return Result.getError("当前日期未开放")

        takeoutMenu.takeoutMenuDishList.addAll(
            getBean(TakeoutmenudishMapper::class.java).getList(
                TakeoutmenudishSearch().apply { this.takeoutMenuId = takeoutMenu.id }
            )
        )

        return Result.getSuccess(takeoutMenu)
    }
}

@RestController
@RequestMapping("/api/takeoutmenu")
class TakeoutmenuResource(service: TakeoutmenuService) : BaseResource<TakeoutmenuSearch, Takeoutmenu, TakeoutmenuMapper, TakeoutmenuService>(service) {
    /**
     * 发布菜谱
     */
    @PostMapping("/save")
    override fun save(@RequestBody model: Takeoutmenu): Result {
        return try {
            service.save(model)
        } catch (e: Exception) {
            log.error("保存菜谱失败: ${e.message}")
            Result.getError("保存菜谱失败: ${e.message}")
        }
    }

    /**
     * 根据日期获取菜谱
     */
    @GetMapping("/getTakeoutMenuByDate/{menuDate}")
    fun getMenuByDate(@PathVariable menuDate: String): Result {
        return try {
            service.getMenuByDate(menuDate)
        } catch (e: Exception) {
            log.error("获取菜谱失败: ${e.message}")
            Result.getError("获取菜谱失败: ${e.message}")
        }
    }
}