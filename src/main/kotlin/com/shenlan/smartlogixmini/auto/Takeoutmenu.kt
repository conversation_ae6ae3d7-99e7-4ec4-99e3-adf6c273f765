package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.NotifyMessageUtil.saveNotifyMessage
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.localDateFormatter
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDate
import java.util.*
import kotlin.system.measureTimeMillis

class Takeoutmenu: BaseModel {
    var menuDate: String = ""
    var sysDeleted: Int = 0
    var published: Boolean = false

    var takeoutMenuDishList: MutableList<Takeoutmenudish> = mutableListOf()

    var notifyMessage: Notifymessage? = null

    constructor()

    constructor(menuDate: String) {
        this.id = uuid()
        this.menuDate = menuDate
    }
}

class TakeoutmenuSearch: BaseSearch() {
    var menuDate: String = ""
}

@Mapper
interface TakeoutmenuMapper : BaseMapper<Takeoutmenu> {
    @Select("""
        <script>
            SELECT * FROM tbl_takeoutmenu
            <where>
                AND sysDeleted = 0
                <if test="menuDate != ''">
                    AND menuDate = #{menuDate}
                </if>
            </where>
            ORDER BY menuDate DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Takeoutmenu>

    // 校验menuDate是否存在
    @Select("SELECT COUNT(*) FROM tbl_takeoutmenu WHERE menuDate = #{menuDate} AND sysDeleted = 0")
    fun checkMenuDateExists(menuDate: String): Int

    @Select("SELECT * FROM tbl_takeoutmenu WHERE menuDate = #{menuDate} AND sysDeleted = 0")
    fun getMenuByDate(menuDate: String): List<Takeoutmenu>

    // 更新published状态
    @Update("UPDATE tbl_takeoutmenu SET published = #{published} WHERE id = #{id}")
    fun updatePublished(id: String, published: Boolean)
}

@Service
class TakeoutmenuService(mapper: TakeoutmenuMapper) : BaseService<Takeoutmenu, TakeoutmenuMapper>(mapper) {
    override fun save(model: Takeoutmenu): Result {
        val takeoutMenuDishMapper = getBean(TakeoutmenudishMapper::class.java)

        return try {
            val time = measureTimeMillis {
                // 1.1 获取当天菜谱
                val takeoutMenu = mapper.getMenuByDate(model.menuDate).elementAtOrNull(0)
                    ?: return Result.getError("当前日期未开放")

                // 1.2 保存菜谱详细
                takeoutMenuDishMapper.insertList(model.takeoutMenuDishList.map { takeoutMenuDish ->
                    Takeoutmenudish(takeoutMenu.id, takeoutMenuDish)
                })

                // 1.3 更新published状态
                mapper.updatePublished(takeoutMenu.id, true)
            }

            log.info("Takeout menu saved successfully, cost $time ms")

            // 保存通知信息
            val users = getBean(PersonnelMapper::class.java).getAllPersonnelIds()
            saveNotifyMessage(users.map { it.id },
                NotifyModule.TAKEOUT.value,
                "normal",
                "外卖订餐通知",
                "明天的外卖面食上新啦，快来订餐吧！",
                "")

            // 删除管理的通知信息
            getBean(NotifyusermessageMapper::class.java).deleteHistoryMessages("", NotifyModule.TAKEOUT.value, "management")

            Result.getSuccess(model.id)
        } catch (e: Exception) {
            log.error("Failed to save takeout menu: ${e.message}")
            Result.getError("外卖菜谱发布失败: ${e.message}")
        }
    }

    fun getMenuByDate(menuDate: String, userId: String): Result {
        val takeoutMenu = mapper.getMenuByDate(menuDate).elementAtOrNull(0)
            ?: return Result.getError("当前日期未开放")

        takeoutMenu.takeoutMenuDishList.addAll(
            getBean(TakeoutmenudishMapper::class.java).getList(
                TakeoutmenudishSearch().apply { this.takeoutMenuId = takeoutMenu.id }
            )
        )

        if (menuDate == localDateFormatter.format(LocalDate.now()))
            takeoutMenu.notifyMessage = getBean(NotifymessageMapper::class.java).getListByUserIdAndModuleAndType(userId, NotifyModule.TAKEOUT.toString(), "management").elementAtOrNull(0)

        return Result.getSuccess(takeoutMenu)
    }
}

@RestController
@RequestMapping("/api/takeoutmenu")
class TakeoutmenuResource(service: TakeoutmenuService) : BaseResource<TakeoutmenuSearch, Takeoutmenu, TakeoutmenuMapper, TakeoutmenuService>(service) {
    /**
     * 发布菜谱
     */
    @PostMapping("/save")
    override fun save(@RequestBody model: Takeoutmenu): Result {
        return try {
            service.save(model)
        } catch (e: Exception) {
            log.error("保存菜谱失败: ${e.message}")
            Result.getError("保存菜谱失败: ${e.message}")
        }
    }

    /**
     * 根据日期获取菜谱
     */
    @GetMapping("/getTakeoutMenuByDate/{menuDate}/{userId}")
    fun getMenuByDate(@PathVariable menuDate: String, @PathVariable userId: String): Result {
        return try {
            service.getMenuByDate(menuDate, userId)
        } catch (e: Exception) {
            log.error("获取菜谱失败: ${e.message}")
            Result.getError("获取菜谱失败: ${e.message}")
        }
    }
}