package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import kotlin.system.measureTimeMillis

class Menucomment: BaseEntity() {
    var menuDate: String = ""
    var userId: String = ""
    var comment: String = ""
    var sysDeleted: Int = 0
    var userName: String = ""
    val surname: String
        get() = userName.take(1)
    var commentTime: String = ""
    var ratingList = mutableListOf<Dishrating>()
}

class MenucommentSearch: BaseSearch() {
    var menuDate: String = ""
    var userId: String = ""
    var comment: String = ""
}

@Mapper
interface MenucommentMapper : BaseMapper<Menucomment> {
    @Select("""
        <script>
            SELECT mc.id, mc.menuDate, mc.userId, mc.comment, date_format(mc.sysCreated, '%Y-%m-%d %H:%i:%s') as commentTime, p.name as userName FROM tbl_menucomment mc left join tbl_personnel p on mc.userId = p.id
            <where>
                AND mc.sysDeleted = 0
                <if test="menuDate != ''">
                    AND mc.menuDate = #{menuDate}
                </if>
                <if test="userId != ''">
                    AND mc.userId = #{userId}
                </if>
            </where>
            ORDER BY mc.menuDate DESC, mc.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Menucomment>
}

@Service
class MenucommentService(mapper: MenucommentMapper) : BaseService<Menucomment, MenucommentMapper, MenucommentSearch>(mapper) {
    override fun save(model: Menucomment): Result {
        val dishRatingMapper = getBean(DishratingMapper::class.java)
        val dishMapper = getBean(DishMapper::class.java)
        log.info("Saving menu comment: ${model.comment.toJsonString}")

        val time = measureTimeMillis {
            super.save(model)

            if (model.ratingList.isNotEmpty()) {
                // 保存菜品评分
                dishRatingMapper.insertList(model.ratingList.filter { it.rating > 0 }.map {
                    it.id = uuid()
                    it.userId = model.userId
                    it.commentId = model.id
                    it
                })

                // 计算菜品平均分
                val ratingMap = dishRatingMapper.getDishIdAndRatingByDishIdList(model.ratingList.filter { it.rating > 0 }.map { it.dishId }).groupBy { it.dishId }
                ratingMap.forEach { (dishId, ratingList) ->
                    val avgRating = ratingList.filter { it.rating > 0 }.map { it.rating }.average()
                    dishMapper.updateDishAverageRating(dishId, avgRating)
                }
            }
        }

        log.info("Menucomment saved successfully, cost $time ms")

        return Result.getSuccess(model.id)
    }

    override fun getList(page: MenucommentSearch): Result {
        val menuDate = page.menuDate
        val commentList = mapper.getList(MenucommentSearch().apply {
            this.menuDate = menuDate
            this.userId = page.userId
        })
        val ratingMap = if (commentList.isNotEmpty())
            getBean(DishratingMapper::class.java).getDishIdAndRatingByCommentIdList(commentList.map { it.id }).groupBy {  it.userId }
        else
            mutableMapOf<String, List<Dishrating>>()
        commentList.forEach { comment -> comment.ratingList.addAll(ratingMap.getOrDefault(comment.userId, listOf()).filter {
            it.rating > 0
        }) }
        return Result.getSuccess(commentList)
    }
}

@RestController
@RequestMapping("/api/menucomment")
class MenucommentResource(service: MenucommentService) : BaseResource<MenucommentSearch, Menucomment, MenucommentMapper, MenucommentService>(service) {
    /**
     * 提交评论
     */
    @PostMapping("/save")
    override fun save(@RequestBody model: Menucomment): Result {
        return try {
            service.save(model)
            Result.getSuccessInfo("保存评论成功")
        } catch (e: Exception) {
            log.error("保存评论失败: ${e.message}")
            Result.getError("保存评论失败: ${e.message}")
        }
    }
}
