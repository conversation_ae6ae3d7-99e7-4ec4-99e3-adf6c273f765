package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Menucomment: BaseModel() {
    var menuDate: String = ""
    var userId: String = ""
    var comment: String = ""
    var sysDeleted: Int = 0
}

class MenucommentSearch: BaseSearch() {
    var menuDate: String = ""
    var userId: String = ""
}

@Mapper
interface MenucommentMapper : BaseMapper<Menucomment> {
    @Select("""
        <script>
            SELECT * FROM tbl_menucomment
            <where>
                AND sysDeleted = 0
                <if test="menuDate != ''">
                    AND DATE(menuDate) = #{menuDate}
                </if>
                <if test="userId != ''">
                    AND userId = #{userId}
                </if>
            </where>
            ORDER BY menuDate DESC, sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Menucomment>
}

@Service
class MenucommentService(mapper: MenucommentMapper) : BaseService<Menucomment, MenucommentMapper>(mapper)

@RestController
@RequestMapping("/api/menucomment")
class MenucommentResource(service: MenucommentService) : BaseResource<MenucommentSearch, Menucomment, MenucommentMapper, MenucommentService>(service)
