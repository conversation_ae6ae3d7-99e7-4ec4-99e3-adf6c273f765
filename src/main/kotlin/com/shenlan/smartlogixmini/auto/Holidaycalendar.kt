package com.shenlan.smartlogixmini.auto

import com.jayway.jsonpath.Configuration
import com.jayway.jsonpath.JsonPath
import com.jayway.jsonpath.Option
import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.MenuUtil
import com.shenlan.smartlogixmini.util.log
import okhttp3.OkHttpClient
import okhttp3.Request
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.concurrent.TimeUnit

/**
 * 节假日历实体类
 */
class Holidaycalendar : BaseEntity() {
    // 数据库表字段

    /** 日期 */
    var date: LocalDate? = null
    /** 官方类型(0-未知,1-节假日,2-补班日) */
    var officialType: Int = 0
    /** 手动类型(0-未知,1-节假日,2-补班日,3-工作日,4-非工作日) */
    var manualType: Int = 0
    /** 官方名称 */
    var officialName: String = ""

    // 其他字段

    /** 实际类型(1-节假日,2-补班日,3-工作日,4-非工作日) */
    val type: Int
        get() {
            // 如果手动类型不为0，返回手动类型
            if (manualType != 0) {
                return manualType
            }

            // 如果手动类型为0且官方类型不为0，返回官方类型
            if (officialType != 0) {
                return officialType
            }

            // 如果都为0，根据日期判断是否为工作日
            return when (date?.dayOfWeek) {
                DayOfWeek.SATURDAY, DayOfWeek.SUNDAY -> 4 // 非工作日
                else -> 3 // 工作日
            }
        }

    /** 名称(周一...周日、国庆节...) */
    val dayOrHolidayName: String
        get() {
            // 如果官方名称不为空，返回官方名称
            if (officialName.isNotEmpty()) {
                return officialName
            }

            // 如果官方名称为空，根据日期返回周几
            return when (date?.dayOfWeek) {
                DayOfWeek.MONDAY -> "周一"
                DayOfWeek.TUESDAY -> "周二"
                DayOfWeek.WEDNESDAY -> "周三"
                DayOfWeek.THURSDAY -> "周四"
                DayOfWeek.FRIDAY -> "周五"
                DayOfWeek.SATURDAY -> "周六"
                DayOfWeek.SUNDAY -> "周日"
                else -> ""
            }
        }
}

/**
 * 节假日历查询条件类
 */
class HolidaycalendarSearch : BaseSearch() {
    /** 开始日期(yyyy-MM-dd) */
    var startDate: String = ""
    /** 结束日期(yyyy-MM-dd) */
    var endDate: String = ""
    /** 官方类型(0-未知,1-节假日,2-补班日) */
    var officialType: Int? = null
    /** 手动类型(0-未知,1-节假日,2-补班日,3-工作日,4-非工作日) */
    var manualType: Int? = null
}

/**
 * 节假日历Mapper接口
 */
@Mapper
interface HolidaycalendarMapper : BaseMapper<Holidaycalendar> {

    @Select("""
        <script>
            SELECT * FROM tbl_holidaycalendar
            <where>
                <if test="startDate != ''">
                    AND date >= #{startDate}
                </if>
                <if test="endDate != ''">
                    AND date &lt;= #{endDate}
                </if>
                <if test="officialType != null">
                    AND officialType = #{officialType}
                </if>
                <if test="manualType != null">
                    AND manualType = #{manualType}
                </if>
            </where>
            ORDER BY date
        </script>
    """)
    override fun getList(search: BaseSearch): List<Holidaycalendar>

    /**
     * 根据日期查询节假日历信息
     */
    @Select("SELECT * FROM tbl_holidaycalendar WHERE date = #{date} LIMIT 1")
    fun getInfoByDate(@Param("date") date: LocalDate): Holidaycalendar?

    /**
     * 查询指定日期范围内的节假日历
     */
    @Select("SELECT * FROM tbl_holidaycalendar WHERE date BETWEEN #{startDate} AND #{endDate} ORDER BY date")
    fun getListByDateRange(@Param("startDate") startDate: String, @Param("endDate") endDate: String): List<Holidaycalendar>
}

/**
 * 节假日历Service类
 */
@Service
class HolidaycalendarService(
    mapper: HolidaycalendarMapper,
    private val hairdateconfigService: HairdateconfigService
) : BaseService<Holidaycalendar, HolidaycalendarMapper, HolidaycalendarSearch>(mapper) {

    // 配置属性
    @Value("\${application.apihubs-api-key}")
    private lateinit var apiKey: String

    // HTTP客户端
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .build()

    /**
     * 重写getEntityPage方法，当指定日期范围时补充缺失的日期对象
     */
    override fun getEntityPage(search: HolidaycalendarSearch): PaginationInfo<Holidaycalendar> {
        // 如果startDate和endDate都有效，使用补充对象的逻辑
        if (search.startDate.isNotEmpty() && search.endDate.isNotEmpty()) {
            val startDate = LocalDate.parse(search.startDate)
            val endDate = LocalDate.parse(search.endDate)
            val result = getHolidayCalendarsByDateRange(startDate, endDate)
            return PaginationInfo.ofFullList(search, result)
        }

        // 否则使用父类的默认实现
        return super.getEntityPage(search)
    }

    /**
     * 根据日期获取节假日历信息
     * 如果数据库中存在对应日期的数据则返回数据库数据，否则返回默认对象
     */
    fun getEntityByDate(date: LocalDate): Holidaycalendar {
        val calendars = getHolidayCalendarsByDateRange(date, date)
        return calendars.first()
    }

    /**
     * 重写方法，确保日期不重复且不为空
     */
    @Transactional
    override fun saveEntity(entity: Holidaycalendar): String {
        // 检查日期不能为空
        if (entity.date == null) {
            throw BusinessException("日期不能为空")
        }

        // 查询是否存在相同日期的记录（除了当前编辑的记录）
        val existingHolidayByDate = mapper.getInfoByDate(entity.date!!)
        if (existingHolidayByDate != null && existingHolidayByDate.id != entity.id) {
            throw BusinessException("该日期已存在记录")
        }

        // 验证类型值的有效性
        if (entity.officialType !in 0..2) {
            throw BusinessException("官方类型值无效，只能为0-2")
        }

        if (entity.manualType !in 0..4) {
            throw BusinessException("手动类型值无效，只能为0-4")
        }

        log.info("Saving holiday calendar for date: ${entity.date}")
        return super.saveEntity(entity)
    }

    /**
     * 更新节假日历数据
     */
    @Transactional
    fun updateHolidayCalendar() {
        log.info("Starting holiday calendar update")

        val currentYear = LocalDate.now().year

        // 更新今年的节假日数据
        updateHolidayDataForYear(currentYear)

        // 尝试更新明年的节假日数据（国务院一般年尾公布）
        updateHolidayDataForYear(currentYear + 1)

        log.info("Holiday calendar update completed")
    }

    /**
     * 更新指定年份的节假日数据
     */
    private fun updateHolidayDataForYear(year: Int) {
        try {
            log.info("Updating holiday data for year: $year")

            // 创建安全的JsonPath配置
            val safeConfig: Configuration = Configuration.builder()
                .options(Option.DEFAULT_PATH_LEAF_TO_NULL)
                .options(Option.SUPPRESS_EXCEPTIONS)
                .build()

            // 调用节假日API
            val apiUrl = "https://api.apihubs.cn/holiday/get?api_key=$apiKey&year=$year&cn=1&size=366"
            val request = Request.Builder()
                .url(apiUrl)
                .build()

            httpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    log.warn("Failed to fetch holiday data for year $year: ${response.code()}")
                    return
                }

                val responseBody = response.body()?.string()
                if (responseBody.isNullOrEmpty()) {
                    log.warn("Empty response body for year $year")
                    return
                }

                // 直接获取节假日列表
                val holidayList = JsonPath.using(safeConfig).parse(responseBody).read<List<Map<String, Any>>>("$.data.list")
                if (holidayList == null) {
                    log.warn("No holiday list data for year $year")
                    return
                }

                // 处理每个节假日数据
                var updatedCount = 0
                holidayList.forEach { holidayNode ->
                    try {
                        // 获取日期（格式: 20251006）
                        val dateInt = holidayNode["date"] as? Int
                        if (dateInt == null) {
                            log.warn("Invalid date format in holiday data")
                            return@forEach
                        }

                        // 转换日期格式
                        val dateStr = dateInt.toString()
                        val dateYear = dateStr.substring(0, 4).toInt()
                        val month = dateStr.substring(4, 6).toInt()
                        val day = dateStr.substring(6, 8).toInt()
                        val date = LocalDate.of(dateYear, month, day)

                        // 获取相关字段
                        val holidayToday = holidayNode["holiday_today"] as? Int ?: 2
                        val holidayRecess = holidayNode["holiday_recess"] as? Int ?: 2
                        val holidayOvertime = holidayNode["holiday_overtime"] as? Int ?: 10
                        val holidayOrCn = holidayNode["holiday_or_cn"] as? String ?: ""

                        // 确定officialType
                        val officialType = when {
                            holidayRecess == 1 -> 1  // 节假日
                            holidayOvertime != 10 -> 2  // 补班日
                            else -> 0  // 未知
                        }

                        // 确定officialName
                        val officialName = if (holidayToday == 1) holidayOrCn else ""

                        // 检查数据库中是否已存在该日期的记录
                        val existingCalendar = mapper.getInfoByDate(date)

                        if (officialType != 0) {
                            // 确实是官方节假日或补班日，更新数据库
                            updateOrCreateHolidayCalendar(date, officialType, officialName)
                            updatedCount++
                        } else if (existingCalendar != null && (existingCalendar.officialType != 0 || existingCalendar.officialName.isNotEmpty())) {
                            // 不再是官方节假日，但数据库中有记录且有官方信息，需要清空官方字段
                            updateOrCreateHolidayCalendar(date, 0, "")
                            updatedCount++
                        }

                    } catch (e: Exception) {
                        log.error("Failed to process holiday data: ${e.message}")
                    }
                }

                log.info("Updated $updatedCount holiday records for year $year")
            }

        } catch (e: Exception) {
            log.error("Error updating holiday data for year $year")
        }
    }

    /**
     * 更新或创建节假日历记录
     */
    private fun updateOrCreateHolidayCalendar(date: LocalDate, officialType: Int, officialName: String) {
        try {
            // 查询数据库中是否已存在该日期的记录
            val existingCalendar = mapper.getInfoByDate(date)

            if (existingCalendar != null) {
                // 更新现有记录的官方类型和官方名称
                var needUpdate = false
                if (existingCalendar.officialType != officialType) {
                    existingCalendar.officialType = officialType
                    needUpdate = true
                }
                if (existingCalendar.officialName != officialName) {
                    existingCalendar.officialName = officialName
                    needUpdate = true
                }
                if (needUpdate) {
                    super.saveEntity(existingCalendar)
                    log.info("Updated holiday calendar for date: $date, officialType: $officialType, officialName: $officialName")
                }
            } else {
                // 创建新记录
                val newCalendar = Holidaycalendar()
                newCalendar.date = date
                newCalendar.officialType = officialType
                newCalendar.manualType = 0
                newCalendar.officialName = officialName
                super.saveEntity(newCalendar)
                log.info("Created holiday calendar for date: $date, officialType: $officialType, officialName: $officialName")
            }

        } catch (e: Exception) {
            log.error("Failed to update holiday calendar for date: $date")
        }
    }

    /**
     * 获取指定日期范围内每天的节假日历信息
     * 如果数据库中存在对应日期的数据则返回数据库数据，否则创建默认对象（不保存到数据库）
     */
    fun getHolidayCalendarsByDateRange(startDate: LocalDate, endDate: LocalDate): List<Holidaycalendar> {
        // 查询数据库中指定日期范围内的节假日历数据
        val existingCalendars = mapper.getListByDateRange(startDate.toString(), endDate.toString())
        val existingCalendarMap = existingCalendars.associateBy { it.date }

        val result = mutableListOf<Holidaycalendar>()
        var currentDate = startDate

        // 遍历日期范围内的每一天
        while (!currentDate.isAfter(endDate)) {
            val calendar = existingCalendarMap[currentDate] ?: run {
                // 如果数据库中没有对应日期的数据，创建默认对象
                val newCalendar = Holidaycalendar()
                newCalendar.date = currentDate
                newCalendar.officialType = 0
                newCalendar.manualType = 0
                newCalendar.officialName = ""
                newCalendar
            }
            result.add(calendar)
            currentDate = currentDate.plusDays(1)
        }

        return result
    }

    /**
     * 获取理发用的节假日历列表
     * 返回计算日期及其前29天的节假日历
     */
    fun getHaircutHolidaycalendarList(): List<Holidaycalendar> {
        val endDate = hairdateconfigService.calculateDate()
        val startDate = endDate.minusDays(29)

        log.info("Getting haircut holiday calendar list from $startDate to $endDate")
        return getHolidayCalendarsByDateRange(startDate, endDate)
    }

    /**
     * 获取菜谱用的节假日历列表
     * 返回最新周日往前29天的节假日历
     */
    fun getMenuHolidaycalendarList(): List<Holidaycalendar> {
        val endDate = MenuUtil.getLatestMenuSunday()
        val startDate = endDate.minusDays(29)

        log.info("Getting menu holiday calendar list from $startDate to $endDate")
        return getHolidayCalendarsByDateRange(startDate, endDate)
    }

    /**
     * 获取外卖用的节假日历列表
     * 返回后天及其往前29天的节假日历
     */
    fun getTakeoutHolidaycalendarList(): List<Holidaycalendar> {
        val endDate = LocalDate.now().plusDays(2)
        val startDate = endDate.minusDays(29)

        log.info("Getting takeout holiday calendar list from $startDate to $endDate")
        return getHolidayCalendarsByDateRange(startDate, endDate)
    }

    /**
     * 获取巡检用的节假日历列表
     * 返回：往前28天 + 今天 + 往后2天的节假日历（共31天）
     */
    fun getInspectionrecordcalendarList(): List<Holidaycalendar> {
        val endDate = LocalDate.now().plusDays(2)
        val startDate = LocalDate.now().minusDays(28)

        log.info("Getting takeout holiday calendar list from $startDate to $endDate")
        return getHolidayCalendarsByDateRange(startDate, endDate)
    }
}

/**
 * 节假日历定时任务类
 */
@Component
class HolidaycalendarScheduler(private val holidaycalendarService: HolidaycalendarService) {

    /**
     * 每10天更新一次节假日历数据
     */
    @Scheduled(initialDelay = 10 * 24 * 60 * 60 * 1000L, fixedRate = 10 * 24 * 60 * 60 * 1000L)
    fun updateHolidayCalendar() {
        holidaycalendarService.updateHolidayCalendar()
    }
}

/**
 * 节假日历Controller类
 */
@RestController
@RequestMapping("/api/Holidaycalendar")
class HolidaycalendarResource(service: HolidaycalendarService) : BaseResource<HolidaycalendarSearch, Holidaycalendar, HolidaycalendarMapper, HolidaycalendarService>(service) {

    /**
     * 获取理发用的节假日历列表
     * 返回计算日期及其前29天的节假日历
     */
    @GetMapping("/getHaircutHolidaycalendarList")
    fun getHaircutHolidaycalendarList(): Result {
        val holidayList = service.getHaircutHolidaycalendarList()
        return Result.getSuccess(holidayList)
    }

    /**
     * 获取菜谱用的节假日历列表
     * 返回最新周日往前29天的节假日历
     */
    @GetMapping("/getMenuHolidaycalendarList")
    fun getMenuHolidaycalendarList(): Result {
        val holidayList = service.getMenuHolidaycalendarList()
        return Result.getSuccess(holidayList)
    }

    /**
     * 获取外卖用的节假日历列表
     * 返回后天及其往前29天的节假日历
     */
    @GetMapping("/getTakeoutHolidaycalendarList")
    fun getTakeoutHolidaycalendarList(): Result {
        val holidayList = service.getTakeoutHolidaycalendarList()
        return Result.getSuccess(holidayList)
    }

    /**
     * 批量保存节假日历
     */
    @PostMapping("/saveList")
    @Transactional
    fun saveList(@RequestBody holidaycalendarList: List<Holidaycalendar>): Result {
        val savedIds = holidaycalendarList.map { service.saveEntity(it) }
        return Result.getSuccess(savedIds)
    }

    /**
     * 获取巡检用的节假日历列表
     * 返回：往前28天 + 今天 + 往后2天的节假日历（共31天）
     */
    @GetMapping("/getInspectionrecordcalendarList")
    fun getInspectionrecordcalendarList(): Result {
        val holidayList = service.getInspectionrecordcalendarList()
        return Result.getSuccess(holidayList)
    }

    /**
     * 更新节假日历数据
     * 从外部API获取最新的节假日数据并更新数据库
     */
    @GetMapping("/updateHolidayCalendar")
    fun updateHolidayCalendar(): Result {
        service.updateHolidayCalendar()
        return Result.getSuccessInfo("节假日历数据更新完成")
    }
}
