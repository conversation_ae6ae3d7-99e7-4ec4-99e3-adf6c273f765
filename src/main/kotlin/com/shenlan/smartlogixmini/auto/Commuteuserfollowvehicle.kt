package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Commuteuserfollowvehicle: BaseModel() {
    var userId: String = ""
    var carId: String = ""
    var sysDeleted: Int = 0

    var followed: Boolean = false
}

class CommuteuserfollowvehicleSearch: BaseSearch() {
    var userId: String = ""
    var carId: String = ""
}

@Mapper
interface CommuteuserfollowvehicleMapper : BaseMapper<Commuteuserfollowvehicle> {
    @Select("""
        <script>
            SELECT * FROM tbl_commuteuserfollowvehicle
            <where>
                AND sysDeleted = 0
                <if test="userId != ''">
                    AND userId = #{userId}
                </if>
                <if test="carId != ''">
                    AND carId = #{carId}
                </if>
            </where>
            ORDER BY sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commuteuserfollowvehicle>

    // 根据carId和userId删除
    @Delete("""
        DELETE FROM tbl_commuteuserfollowvehicle
        WHERE userId = #{userId} AND carId = #{carId}
    """)
    fun deleteByUserIdAndRouteId(userId: String, carId: String)
}

@Service
class CommuteuserfollowvehicleService(mapper: CommuteuserfollowvehicleMapper) : BaseService<Commuteuserfollowvehicle, CommuteuserfollowvehicleMapper>(mapper) {
    override fun save(model: Commuteuserfollowvehicle): Result {
        return if (model.followed) {
            super.save(model)
            Result.getSuccess("关注成功")
        } else {
            mapper.deleteByUserIdAndRouteId(model.userId, model.carId)
            Result.getSuccess("取消关注成功")
        }
    }
}

@RestController
@RequestMapping("/api/commuteuserfollowvehicle")
class CommuteuserfollowvehicleResource(service: CommuteuserfollowvehicleService) : BaseResource<CommuteuserfollowvehicleSearch, Commuteuserfollowvehicle, CommuteuserfollowvehicleMapper, CommuteuserfollowvehicleService>(service)
