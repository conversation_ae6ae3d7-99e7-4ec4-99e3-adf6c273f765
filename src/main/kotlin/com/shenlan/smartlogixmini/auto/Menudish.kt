package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.MenuUtil.syncTakeoutDish
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Menudish: BaseEntity {
    var menuId: String = ""
    var dishId: String = ""
    var dishName: String = ""
    var sortOrder: Int = 0
    var price: Double = 0.0
    var portionUnit: Int = 0
    var limitPerPerson: Int = 1
    var imageId: String = ""
    var sysDeleted: Int = 0

    // 是否同步菜品库
    var synced: Boolean = false

    // 关联表信息
    var imageUrl: String = ""
        get() = if (field.isEmpty()) field else AppPro.imgPrefix + field

    constructor()

    constructor(menuId: String, sortOrder: Int, menuDish: Menudish) {
        this.id = uuid()
        this.menuId = menuId
        this.sortOrder = sortOrder
        this.dishId = menuDish.dishId
        this.price = menuDish.price
        this.portionUnit = menuDish.portionUnit
        this.limitPerPerson = menuDish.limitPerPerson
        this.imageId = menuDish.imageId
        this.dishName = menuDish.dishName
        this.synced = menuDish.synced
    }
}

class MenudishSearch: BaseSearch() {
    var menuId: String = ""
    var dishId: String = ""
}

@Mapper
interface MenudishMapper : BaseMapper<Menudish> {
    @Select("""
        <script>
            SELECT md.*,di.imageUrl FROM tbl_menudish md 
            left join tbl_dishimage di on md.imageId = di.id
            <where>
                AND md.sysDeleted = 0
                <if test="menuId != ''">
                    AND md.menuId = #{menuId}
                </if>
                <if test="dishId != ''">
                    AND md.dishId = #{dishId}
                </if>
            </where>
            ORDER BY md.sortOrder ASC, md.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Menudish>

    @Select("""
        SELECT md.*,di.imageUrl FROM tbl_menudish md 
        left join tbl_dishimage di on md.imageId = di.id
        WHERE md.id = #{id}
    """)
    override fun getInfo(id: String): Menudish?
}

@Service
class MenudishService(mapper: MenudishMapper) : BaseService<Menudish, MenudishMapper, MenudishSearch>(mapper) {
    override fun save(model: Menudish): Result {
        if (model.synced) {
//            getBean(DishMapper::class.java).syncDishByMenuDish(model)
            syncTakeoutDish(Takeoutdish(model.dishId, model.dishName, model.price, model.portionUnit, model.limitPerPerson, model.imageId))
        }
        return super.save(model)
    }

    fun syncDelete(menuDish: Menudish): Result {
        if (menuDish.synced)
            getBean(DishMapper::class.java).delete(menuDish.dishId)
        return super.delete(menuDish.id)
    }
}

@RestController
@RequestMapping("/api/menudish")
class MenudishResource(service: MenudishService) : BaseResource<MenudishSearch, Menudish, MenudishMapper, MenudishService>(service) {
    /**
     * 同步下架菜谱菜品
     */
    @PostMapping("/syncDelete")
    fun syncDelete(@RequestBody menuDish: Menudish): Result {
        return service.syncDelete(menuDish)
    }
}
