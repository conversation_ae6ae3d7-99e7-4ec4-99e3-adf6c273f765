package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Menudish: BaseModel() {
    var menuId: String = ""
    var dishId: String = ""
    var sortOrder: Int = 0
    var price: Double = 0.0
    var portionUnit: Int = 0
    var limitPerPerson: Int? = null
    var sysUpdated: Date? = null
    var sysDeleted: Int = 0
}

class MenudishSearch: BaseSearch() {
    var menuId: String = ""
    var dishId: String = ""
}

@Mapper
interface MenudishMapper : BaseMapper<Menudish> {
    @Select("""
        <script>
            SELECT * FROM tbl_menudish
            <where>
                AND sysDeleted = 0
                <if test="menuId != ''">
                    AND menuId = #{menuId}
                </if>
                <if test="dishId != ''">
                    AND dishId = #{dishId}
                </if>
            </where>
            ORDER BY sortOrder ASC, sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Menudish>
}

@Service
class MenudishService(mapper: MenudishMapper) : BaseService<Menudish, MenudishMapper>(mapper)

@RestController
@RequestMapping("/api/menudish")
class MenudishResource(service: MenudishService) : BaseResource<MenudishSearch, Menudish, MenudishMapper, MenudishService>(service)
