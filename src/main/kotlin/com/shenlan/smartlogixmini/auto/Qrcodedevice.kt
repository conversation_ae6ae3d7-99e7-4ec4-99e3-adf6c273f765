package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController


class Qrcodedevice : BaseEntity {

    /** 设备Mac地址 */
    var macAddr: String = ""
    /** 设置开始时间 */
    var startTime: String = ""
    /** 设置周期（单位(s)） */
    var period: Int = 0

    constructor()
}

@Mapper
interface QrcodedeviceMapper : BaseMapper<Qrcodedevice> {

    @Select(""" select count(*) from tbl_qrcodedevice where macAddr = #{macAddr} and id != #{id} """)
    fun checkMacAddrExists(macAddr: String, id: String): Int

    @Select(""" select a.* from tbl_qrcodedevice a join tbl_site b on a.macAddr = b.macAddr where b.siteNumber = #{deviceNumber} """)
    fun getInfoBySiteNumber(siteNumber: String): Qrcodedevice?

    @Select(""" select * from tbl_qrcodedevice where macAddr = #{macAddr} """)
    fun getInfoByMacAddr(macAddr: String): Qrcodedevice?
}

class QrcodedeviceSearch : BaseSearch {
    constructor()
}

@Service
open class QrcodedeviceService(mapper: QrcodedeviceMapper) : BaseService<Qrcodedevice, QrcodedeviceMapper, QrcodedeviceSearch>(mapper) {

    fun getInfoByMacAddr(macAddr: String):Result {
        var qd = mapper.getInfoByMacAddr(macAddr)
        if (qd == null) {
            return Result.getSuccess("设备未入库，请先维护入库！")
        }
        return Result.getSuccess(qd)
    }

    override fun save(model: Qrcodedevice): Result {

        //校验设备MAC地址是否重复
        val count = mapper.checkMacAddrExists(model.macAddr, model.id)
        if (count > 0) {
            return Result.getError("设备MAC已入库！")
        }

        //给定一个初始的startTime
        model.startTime = "2025-07-01 12:00"

        return super.save(model)
    }
}

@RestController
@RequestMapping("/api/Qrcodedevice")
open class QrcodedeviceResource(service: QrcodedeviceService) : BaseResource<QrcodedeviceSearch, Qrcodedevice, QrcodedeviceMapper, QrcodedeviceService>(service) {

    /**
     * 根据macAddr获取设备信息
     */
    @GetMapping("/getInfoByMacAddr/{macAddr}")
    fun getInfoByMacAddr(@PathVariable macAddr: String):Result {
        return service.getInfoByMacAddr(macAddr)
    }
}

