package com.shenlan.smartlogixmini.auto

import com.fasterxml.jackson.annotation.JsonFormat
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.notEmpty
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import javax.validation.Valid

/**
 * 理发基础时间配置实体类
 */
class Hairbaseconfig : BaseModel() {
    var weekDays:String = "周一,周三,周五"
    @JsonFormat(pattern = "HH:mm")
    var dailyOpenTime: LocalTime = LocalTime.of(8, 30)
    @JsonFormat(pattern = "HH:mm")
    var dailyCloseTime: LocalTime = LocalTime.of(17, 0)
    @JsonFormat(pattern = "HH:mm")
    var lunchStartTime: LocalTime = LocalTime.of(11, 30)
    @JsonFormat(pattern = "HH:mm")
    var lunchEndTime: LocalTime = LocalTime.of(13, 30)
    var customerInterval:Int=30
}

/**
 * 理发基础时间配置查询条件类
 */
class HairbaseconfigSearch: BaseSearch() {
}

/**
 * 理发基础时间配置Mapper接口
 */
@Mapper
interface HairbaseconfigMapper : BaseMapper<Hairbaseconfig> {
    @Select("SELECT * FROM tbl_hairbaseconfig ORDER BY sysCreated DESC LIMIT 1")
    fun getBaseConfig(): Hairbaseconfig


}

/**
 * 理发基础时间配置服务类
 */
@Service
class HairbaseconfigService(
    mapper: HairbaseconfigMapper,
    private val hairdateconfigMapper: HairdateconfigMapper,
    private val hairdateconfigservice: HairdateconfigService
) : BaseService<Hairbaseconfig, HairbaseconfigMapper>(mapper) {
    fun getBaseConfig(): Hairbaseconfig {
        return mapper.getBaseConfig()
    }

    /**
     * 重写重置默认理发配置逻辑
     * 新增每次重置后将还未发布的下下期时间段删除并根据最新配置重新生成
     */
    @Transactional
    override fun save(model: Hairbaseconfig): Result {
        if (model.id.notEmpty()) {
            delete(model.id)
        }
        if (model.id.isEmpty()) {
            model.id = uuid()
        }
        mapper.insert(model)
//        删除所有未发布的预约配置
        val hairdateconfigList=hairdateconfigMapper.getListByIfopen(0)
        val idList=hairdateconfigList.map {it.id}
        idList.forEach {
            hairdateconfigservice.delete(it)
        }
//          根据最新的配置生成新的预约配置
        val localDateTime=LocalDateTime.now()
//        issue表示要生成的预约期数
        val issue=hairdateconfigservice.caluateMaxIssueByLocalDateTime(localDateTime)+1
        val map=hairdateconfigservice.caluateDateByIssue(issue)
        val issueStartTime=map.get("issueStartTime")!!
        hairdateconfigservice.generateNextTwoWeeksConfigs(issueStartTime)
        return Result.getSuccess(model.id)
    }

}

/**
 * 理发基础时间配置控制器
 */
@RestController
@RequestMapping("/api/Hairbaseconfig")
class HairbaseconfigResource(service: HairbaseconfigService) :
    BaseResource<HairbaseconfigSearch, Hairbaseconfig, HairbaseconfigMapper, HairbaseconfigService>(service) {
    @PostMapping("/getBaseConfig")
    fun getBaseConfig():Result{
        return Result.getSuccess(service.getBaseConfig())
    }
}

/**
 *每一期的第二周周五五点固定发放下一期预约名额给客户以及生成下下期的理发时间配置
 */
@Component
class ReservationPeriodTask(private val hairdateconfigservice: HairdateconfigService) {
    @Scheduled(cron = "0 24 18 * * FRI")
    @Transactional
    fun generateNextPeriod() {
        log.info("每周五定时开放理发预约配置")
        val today = LocalDate.now()
        val baseCycleFriday = LocalDate.of(2025, 6, 20)
        val weeksFromBase = ChronoUnit.WEEKS.between(baseCycleFriday, today)
        val isSecondWeekFriday = weeksFromBase % 2 == 0L
        if (isSecondWeekFriday) {
            /**
             * 开放已经配置好的上一轮配置给客户预约
             */
            val openIssue=hairdateconfigservice.calculateIssueByDate(today)+1
            hairdateconfigservice.updateOpenStatus(openIssue)
            /**
             * 开放理发师的新一轮预约配置
             */
            //因为只有在一期当中的第二周周五五点才能开启理发师那边的配置所以固定的是17天
            val nextNextCycleMonday = today.plusDays(17)
            val endNextCycleTime=nextNextCycleMonday.plusDays(13)
            hairdateconfigservice.generateNextTwoWeeksConfigs(nextNextCycleMonday)
            log.info("触发日期：{}，开放新一轮理发配置开始时间：{}，结束时间：{}", today, nextNextCycleMonday,endNextCycleTime)
        }
        else{
            log.info("{}不符合触发条件，开放失败", today)
        }
    }
}