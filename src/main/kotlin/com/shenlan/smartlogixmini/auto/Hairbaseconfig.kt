package com.shenlan.smartlogixmini.auto

import com.fasterxml.jackson.annotation.JsonFormat
import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDate
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import javax.validation.Valid

/**
 * 理发基础时间配置实体类
 */
class Hairbaseconfig : BaseModel() {
    var weekDays:String = "周一,周三,周五"
    @JsonFormat(pattern = "HH:mm")
    var dailyOpenTime: LocalTime = LocalTime.of(8, 30)
    @JsonFormat(pattern = "HH:mm")
    var dailyCloseTime: LocalTime = LocalTime.of(17, 0)
    @JsonFormat(pattern = "HH:mm")
    var lunchStartTime: LocalTime = LocalTime.of(11, 30)
    @JsonFormat(pattern = "HH:mm")
    var lunchEndTime: LocalTime = LocalTime.of(13, 30)
    var customerInterval:Int=30
}

/**
 * 理发基础时间配置查询条件类
 */
class HairbaseconfigSearch: BaseSearch() {
}

/**
 * 理发基础时间配置Mapper接口
 */
@Mapper
interface HairbaseconfigMapper : BaseMapper<Hairbaseconfig> {
    @Select("SELECT * FROM tbl_hairbaseconfig ORDER BY sysCreated DESC LIMIT 1")
    fun getBaseConfig(): Hairbaseconfig


}

/**
 * 理发基础时间配置服务类
 */
@Service
class HairbaseconfigService(
    mapper: HairbaseconfigMapper
) : BaseService<Hairbaseconfig, HairbaseconfigMapper>(mapper) {
    fun getBaseConfig(): Hairbaseconfig {
        return mapper.getBaseConfig()
    }

}

/**
 * 理发基础时间配置控制器
 */
@RestController
@RequestMapping("/api/Hairbaseconfig")
class HairbaseconfigResource(service: HairbaseconfigService) :
    BaseResource<HairbaseconfigSearch, Hairbaseconfig, HairbaseconfigMapper, HairbaseconfigService>(service) {
    @PostMapping("/getBaseConfig")
    fun getBaseConfig():Hairbaseconfig{
        return service.getBaseConfig()
    }

}


//@Component
//class OpenHairdatetimetask(hairdateconfigService: HairdateconfigService){
//    @Scheduled(cron = "0 0 17 * * FRI")//每周五五点触发
//    @Transactional
//    fun generateNextPeriod() {
//        val today = LocalDate.now()
//        val baseCycleFriday = LocalDate.of(2025, 6, 20) // 硬编码将2025-6-20作为一期的第二周,周五
//        // 计算当前日期与基准日期相差的周数
//        val weeksFromBase = ChronoUnit.WEEKS.between(baseCycleFriday, today)
//        val isSecondWeekFriday = weeksFromBase % 2 == 0 // 每两周触发一次
//
//        if (isSecondWeekFriday) {
//            // 计算下下一期的开始日期（基准日期 + (weeksFromBase + 2) * 7天）
//            val nextNextCycleStart = baseCycleFriday.plusWeeks(weeksFromBase + 2)
//            val nextNextCycleEnd = nextNextCycleStart.plusDays(13)
//
//            // 生成配置
//            val result = configService.generateConfigsForPeriod(
//                startDate = nextNextCycleStart,
//                endDate = nextNextCycleEnd
//            )
//
//            log.info("触发周期：{}，生成下下一期配置：{}至{}，共{}条",
//                today, nextNextCycleStart, nextNextCycleEnd, result.count)
//        } else {
//            log.debug("当前是第{}周周五，不生成配置", weeksFromBase % 2 + 1)
//        }
//    }
//}