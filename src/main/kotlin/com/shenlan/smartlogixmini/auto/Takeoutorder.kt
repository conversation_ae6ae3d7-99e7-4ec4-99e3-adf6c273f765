package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.TakeoutUtil.TAKEOUT_UNPAID_KEEP_TIME
import com.shenlan.smartlogixmini.util.TakeoutUtil.cancelOrderScheduleWithTimer
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.localDateTimeFormatter
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import com.shenlan.smartlogixmini.util.uuid
import com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*
import kotlin.collections.emptyList
import kotlin.math.max
import kotlin.math.min

class Takeoutorder: BaseModel() {
    var userId: String = ""
    var orderDate: String = ""
    var status: String = ""
    var cabinetId: String = ""
    var pickupCode: String = ""
    var pickupTime: String = ""
        get() {
            return if (field.isEmpty()) {
                val timestamp = sysCreated?.time ?: 0L
                localDateTimeFormatter.format(
                    Instant.ofEpochMilli(timestamp)
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime().plusDays(1))
            } else
                field
        }
    var sysDeleted: Int = 0

    var takeoutOrderItemList = mutableListOf<Takeoutorderitem>()
    val statusCn: String
        get() {
            return when(status) {
                "0" -> "待支付"
                "1" -> "待取餐"
                "2" -> "已完成"
                "3" -> "已取消"
                else -> "未知"
            }
        }
    var userName: String = ""
    var phone: String = ""
    var totalPrice: Double = 0.0
    var transactionId: String = ""

    var code: String = ""

    // 待支付剩余时间
    val remainingTime: Int
        get() {
            return if (status == "0") {
                val startTime = sysCreated?.time ?: 0L
                if (startTime == 0L)
                    0
                else
                    max(0, TAKEOUT_UNPAID_KEEP_TIME / 1000 - ((Date().time - startTime) / 1000).toInt())
            } else {
                0
            }
        }
}

enum class OrderStatus(val value: Int) {
    PENDING(0),
    PAID(1),
    COMPLETED(2),
    CANCELLED(3)
}

data class PrepayOrderResponse(
    val orderId: String,
    val prepayResponse: Any?
)

class TakeoutorderSearch: BaseSearch() {
    var userId: String = ""
    var status: String = ""
    var cabinetId: String = ""
    var pickupCode: String = ""
}

@Mapper
interface TakeoutorderMapper : BaseMapper<Takeoutorder> {
    @Select("""
        SELECT too.*,p.name as userName,p.phone as phone FROM tbl_takeoutorder too left join tbl_personnel p on too.userId = p.id
        WHERE too.id = #{id}
    """)
    override fun getInfo(id: String): Takeoutorder?

    @Select("""
        <script>
            SELECT too.*,p.name as userName,p.phone as phone FROM tbl_takeoutorder too left join tbl_personnel p on too.userId = p.id
            <where>
                AND too.sysDeleted = 0
                <if test="userId != ''">
                    AND too.userId = #{userId}
                </if>
                <if test="status != ''">
                    AND too.status = #{status}
                </if>
                <if test="cabinetId != ''">
                    AND too.cabinetId = #{cabinetId}
                </if>
                <if test="pickupCode != ''">
                    AND too.pickupCode = #{pickupCode}
                </if>
            </where>
            ORDER BY too.orderDate DESC, too.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Takeoutorder>

    // 更新订单状态
    @Update("""
        UPDATE tbl_takeoutorder
        SET status = #{status}
        WHERE id = #{id}
    """)
    fun updateOrderStatus(id: String, status: Int)

    // 待取餐的订单状态更新为已完成
    @Update("""
        UPDATE tbl_takeoutorder
        SET status = #{newStatus}
        WHERE status = #{oldStatus}
    """)
    fun updateOrderStatusByOldStatus(oldStatus: Int, newStatus: Int)

    @Update("""
        UPDATE tbl_takeoutorder
        SET cabinetId = #{cabinetId}
        WHERE id = #{orderId}
    """)
    fun updateCabinetId(orderId: String, cabinetId: String)

    // 更新取餐号
    @Update("""
        UPDATE tbl_takeoutorder
        SET pickupCode = #{pickupCode}
        WHERE id = #{orderId}
    """)
    fun updatePickupCode(orderId: String, pickupCode: String)

    // 更新transactionId,pickupCode,status
    @Update("""
        UPDATE tbl_takeoutorder
        SET transactionId = #{transactionId}, pickupCode = #{pickupCode}, status = #{status}
        WHERE id = #{orderId}
    """)
    fun updateTransactionId(orderId: String, transactionId: String, pickupCode: String, status: Int)
}

@Service
class TakeoutorderService(mapper: TakeoutorderMapper) : BaseService<Takeoutorder, TakeoutorderMapper>(mapper) {
    override fun getInfo(id: String): Result {
        val order = (super.getInfo(id).datas as Takeoutorder?) ?: return Result.getError("订单${id}不存在")
        println(order.remainingTime)
        order.takeoutOrderItemList.addAll(getBean(TakeoutorderitemMapper::class.java).getList(TakeoutorderitemSearch().apply { orderId = id }))
        order.totalPrice = order.takeoutOrderItemList.sumByDouble { it.price * it.orderCount }
        return Result.getSuccess(order)
    }

    override fun save(model: Takeoutorder): Result {
        val takeoutOrderItemMapper = getBean(TakeoutorderitemMapper::class.java)

        return try {
            // 1.1 保存订单
            model.status = OrderStatus.PENDING.value.toString()
            model.orderDate = localDateTimeFormatter.format(LocalDateTime.now())
            model.pickupTime = localDateTimeFormatter.format(LocalDateTime.now().plusDays(1))
            super.save(model)
            // 15分钟后自动取消未支付订单
            cancelOrderScheduleWithTimer(model.id)

            // 1.2 保存订单项
            takeoutOrderItemMapper.insertList(model.takeoutOrderItemList.onEach { takeoutOrderItem ->
                takeoutOrderItem.id = uuid()
                takeoutOrderItem.orderId = model.id
            })

            // 1.3 生成微信预支付订单
            val orderRequest = OrderRequest(
                model.takeoutOrderItemList.joinToString(";") { "${it.dishName} x ${it.orderCount}" },
                model.id,
                (model.takeoutOrderItemList.sumByDouble { it.price * it.orderCount } * 100.0).toInt(),
                model.code
            )
            val prepayResponse = try {
                WeChatPayUtil.jsapiPrepay(orderRequest)
            } catch (e: Exception) {
                Result.getError("预支付失败")
            }

            Result.getSuccess(PrepayOrderResponse(model.id, prepayResponse.datas))
        } catch (e: Exception) {
            log.error("Failed to save takeout order: ${e.message}")
            Result.getError("外卖订单保存失败: ${e.message}")
        }
    }

    override fun getList(page: BaseSearch): Result {
        val search = page as TakeoutorderSearch

        // 1.1 查询订单列表
        val orderList = mapper.getList(search)
        if (orderList.isEmpty()) {
            return Result.getSuccess(orderList)
        }

        // 1.2 查询订单项列表
        val orderItemMap = getBean(TakeoutorderitemMapper::class.java).getListByOrderIdList(orderList.map {
            it.id
        }).groupBy { it.orderId }

        // 1.3 将订单项列表添加到订单中，计算订单总额
        orderList.forEach { order ->
            order.takeoutOrderItemList.addAll(orderItemMap.getOrDefault(order.id, listOf()))
            order.totalPrice = order.takeoutOrderItemList.sumByDouble { it.price * it.orderCount }
        }

        return Result.getSuccess(orderList)
    }

    fun updateOrderStatus(id: String, status: OrderStatus): Result {
        return try {
            mapper.updateOrderStatus(id, status.value)
            Result.getSuccessInfo("更新订单${id}状态成功，当前状态：${status.value}")
        } catch (e: Exception) {
            log.error("更新订单状态失败: ${e.message}")
            Result.getError("更新订单状态失败: ${e.message}")
        }
    }

    fun updateCabinetId(orderId: String, cabinetId: String): Result {
        return try {
            mapper.updateCabinetId(orderId, cabinetId)
            Result.getSuccessInfo("更新订单${orderId}取餐柜id成功，当前取餐柜id：${cabinetId}")
        } catch (e: Exception) {
            log.error("更新订单取餐柜id失败: ${e.message}")
            Result.getError("更新订单取餐柜id失败: ${e.message}")
        }
    }
}

@RestController
@RequestMapping("/api/takeoutorder")
class TakeoutorderResource(service: TakeoutorderService) : BaseResource<TakeoutorderSearch, Takeoutorder, TakeoutorderMapper, TakeoutorderService>(service) {
    /**
     * 保存订单
     */
    @PostMapping("/save")
    override fun save(@RequestBody model: Takeoutorder): Result {
        return try {
            service.save(model)
        } catch (e: Exception) {
            log.error("保存订单失败: ${e.message}")
            Result.getError("保存订单失败: ${e.message}")
        }
    }

    /**
     * 更新订单状态
     */
    @GetMapping("/updateStatus/{id}/{status}")
    fun updateStatus(@PathVariable id: String, @PathVariable status: Int): Result {
        return service.updateOrderStatus(id, OrderStatus.values().first { it.value == status })
    }

    /**
     * 更新取餐柜id
     */
    @GetMapping("/updateCabinetId/{orderId}/{cabinetId}")
    fun updateCabinetId(@PathVariable orderId: String, @PathVariable cabinetId: String): Result {
        return service.updateCabinetId(orderId, cabinetId)
    }
}
