package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Commutepublishedroutestop: BaseEntity {
    var routeId: String = ""
    var sequence: Int = 0
    var name: String = ""
    var lat: Double = 0.0
    var lng: Double = 0.0
    var sysDeleted: Int = 0

    // 站点标识，用于区分站点和普通坐标
    var flag: Boolean = false

    // 是否路过
    var pass: Boolean = false

    constructor()

    constructor(stop: Commutepublishedroutestop) {
        this.id = stop.id
        this.routeId = stop.routeId
        this.sequence = stop.sequence
        this.name = stop.name
        this.lat = stop.lat
        this.lng = stop.lng
        this.sysDeleted = stop.sysDeleted
        this.flag = stop.flag
    }
}

class CommutepublishedroutestopSearch: BaseSearch() {
    var routeId: String = ""
    var name: String = ""
}

@Mapper
interface CommutepublishedroutestopMapper : BaseMapper<Commutepublishedroutestop> {
    @Select("""
        <script>
            SELECT * FROM tbl_commutepublishedroutestop
            <where>
                <if test="routeId != ''">
                    AND routeId = #{routeId}
                </if>
            </where>
            ORDER BY routeId ASC, sequence ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutepublishedroutestop>

    // 根据routeId列表查询站点
    @Select("""
        <script>
            SELECT * FROM tbl_commutepublishedroutestop
            WHERE 1 = 1
            <if test="list != null and list.size() > 0">
                AND routeId IN
                <foreach item="item" index="" collection="list" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </script>
    """)
    fun getListByRouteIdList(@Param("list") list: List<String>): List<Commutepublishedroutestop>

    // 所有的sysDeleted置为1
    @Update("""
        UPDATE tbl_commutepublishedroutestop
        SET sysDeleted = 1
        WHERE sysDeleted = 0
    """)
    fun deleteLogicAll()

    @Delete("""
        DELETE FROM tbl_commutepublishedroutestop
        WHERE routeId IN (SELECT id FROM tbl_commutepublishedroute WHERE organizationId = #{organizationId})
    """)
    fun deleteByOrganizationId(organizationId: String)

    // 根据最新的tbl_commutepublishedroute的id获取最新的站点列表
    @Select("""
        SELECT * FROM tbl_commutepublishedroutestop WHERE routeId = (SELECT id FROM tbl_commutepublishedroute where sysDeleted = 0 ORDER BY sysCreated DESC LIMIT 1)
    """)
    fun getLatestStopList(): List<Commutepublishedroutestop>
}

@Service
class CommutepublishedroutestopService(mapper: CommutepublishedroutestopMapper) : BaseService<Commutepublishedroutestop, CommutepublishedroutestopMapper, CommutepublishedroutestopSearch>(mapper) {

}

@RestController
@RequestMapping("/api/commutepublishedroutestop")
class CommutepublishedroutestopResource(service: CommutepublishedroutestopService) : BaseResource<CommutepublishedroutestopSearch, Commutepublishedroutestop, CommutepublishedroutestopMapper, CommutepublishedroutestopService>(service) {

}
