package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Commutepublishedroutestop: BaseModel() {
    var routeId: String = ""
    var sequence: Int = 0
    var name: String = ""
    var lat: Double = 0.0
    var lng: Double = 0.0
    var sysDeleted: Int = 0
}

class CommutepublishedroutestopSearch: BaseSearch() {
    var routeId: String = ""
    var name: String = ""
}

@Mapper
interface CommutepublishedroutestopMapper : BaseMapper<Commutepublishedroutestop> {
    @Select("""
        <script>
            SELECT * FROM tbl_commutepublishedroutestop
            <where>
                <if test="routeId != ''">
                    AND routeId = #{routeId}
                </if>
            </where>
            ORDER BY routeId ASC, sequence ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutepublishedroutestop>

    // 根据routeId列表查询站点
    @Select("""
        <script>
            SELECT * FROM tbl_commutepublishedroutestop
            WHERE routeId IN
            <foreach item="item" index="" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </script>
    """)
    fun getListByRouteIdList(@Param("list") list: List<String>): List<Commutepublishedroutestop>

    @Select("""
        SELECT * FROM tbl_commutepublishedroutestop WHERE sysDeleted = 0 order by sysCreated desc 
    """)
    fun getAllRoute(): List<Commuteroute>

    // 所有的sysDeleted置为1
    @Update("""
        UPDATE tbl_commutepublishedroutestop
        SET sysDeleted = 1
        WHERE sysDeleted = 0
    """)
    fun deleteLogicAll()
}

@Service
class CommutepublishedroutestopService(mapper: CommutepublishedroutestopMapper) : BaseService<Commutepublishedroutestop, CommutepublishedroutestopMapper>(mapper) {
    fun getAll(routeType: Int): List<Commuteroute> {
        val list = mapper.getAllRoute()
        if (routeType == CommuteRouteType.HOME.value) {
            list.forEach {
                val name = it.startStopName
                it.startStopName = it.endStopName
                it.endStopName = name
            }
        }
        return list
    }
}

@RestController
@RequestMapping("/api/commutepublishedroutestop")
class CommutepublishedroutestopResource(service: CommutepublishedroutestopService) : BaseResource<CommutepublishedroutestopSearch, Commutepublishedroutestop, CommutepublishedroutestopMapper, CommutepublishedroutestopService>(service) {
    @GetMapping("/getAll/{routeType}")
    fun getAll(@PathVariable routeType: Int): Result {
        return Result.getSuccess(service.getAll(routeType))
    }
}
