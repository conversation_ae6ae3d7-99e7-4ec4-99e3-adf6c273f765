package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 微信订阅消息模板实体类
 */
class Wechatsubscribemessagetemplate : BaseModel() {
    // 数据库表字段

    /** 模板名称 */
    var name: String = ""
    /** 模板ID */
    var templateId: String = ""
}

/**
 * 微信订阅消息模板查询条件类
 */
class WechatsubscribemessagetemplateSearch : BaseSearch()

/**
 * 微信订阅消息模板Mapper接口
 */
@Mapper
interface WechatsubscribemessagetemplateMapper : BaseMapper<Wechatsubscribemessagetemplate>

/**
 * 微信订阅消息模板Service类
 */
@Service
class WechatsubscribemessagetemplateService(
    mapper: WechatsubscribemessagetemplateMapper
) : BaseService<Wechatsubscribemessagetemplate, WechatsubscribemessagetemplateMapper>(mapper)

/**
 * 微信订阅消息模板Controller类
 */
@RestController
@RequestMapping("/api/Wechatsubscribemessagetemplate")
class WechatsubscribemessagetemplateResource(
    service: WechatsubscribemessagetemplateService
) : BaseResource<WechatsubscribemessagetemplateSearch, Wechatsubscribemessagetemplate, WechatsubscribemessagetemplateMapper, WechatsubscribemessagetemplateService>(service)
