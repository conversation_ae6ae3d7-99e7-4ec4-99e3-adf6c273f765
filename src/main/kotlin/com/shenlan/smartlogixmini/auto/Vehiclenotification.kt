package com.shenlan.smartlogixmini.auto

import com.github.pagehelper.PageHelper
import com.shenlan.smartlogixmini.mybatis.paginationInfo
import com.shenlan.smartlogixmini.util.AliyunSmsUtil
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import javax.validation.Valid

/** 车辆通知信息实体类 */
class Vehiclenotification : BaseModel() {
    /** 关联车辆id */
    var vehicleInfoId: String = ""
    /** 关联车主id */
    var personnelId: String = ""
    /** 通知方式 */
    var noticeMethod: String = "通知方式显示失败"
    /** 通知时间 */
    var noticeTime: String =LocalDateTime.now()
        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
    /** 反馈 */
    var feedbackContent: String = "无反馈"
    /** 逻辑处理管理员操作0表示需要展示的数据，1表示不需要显示的数据 */
    var adminDisplay:Int= 0
    /** 车主显示数据0表示车主未处理的通知反馈，1表示已经处理的通知反馈 */
    var vehicleOwnerDisplay:Int= 0

}

/** 车辆通知查询条件类 */
class VehiclenotificationSearch : BaseSearch()

/** 传输车辆通知反馈所需的数据dto */
data class Vehiclenotificationdto(
    var id: String = "",
    var personnelId: String = "",
    var name: String = "",
    var position: String = "",
    var phone: String = "",
    var vehicleInfoId: String = "",
    var licensePlateNumber: String = "",
    var noticeMethod: String = "",
    var noticeTime: String ="",
    var feedbackContent: String = "无反馈"
)

/** 车辆通知Mapper接口 */
@Mapper
interface VehiclenotificationMapper : BaseMapper<Vehiclenotification> {
    @Select(
        """
        SELECT 
            n.id As id,
            n.personnelId As personnelId,
            p.name AS name,
            p.position AS position,
            p.phone AS phone,
            n.vehicleInfoId AS vehicleInfoId,
            v.licensePlateNumber AS licensePlateNumber,
            n.noticeMethod AS noticeMethod,
            DATE_FORMAT(n.noticeTime, '%Y-%m-%d %H:%i:%s') AS noticeTime,
            n.feedbackContent AS feedbackContent
        FROM tbl_vehiclenotification n
        JOIN tbl_personnel p ON n.personnelId = p.id
        JOIN tbl_vehicleinfo v ON n.vehicleInfoId = v.id
        WHERE n.sysDeleted = 0
          AND p.sysDeleted = 0
          AND v.sysDeleted = 0
          AND n.adminDisplay=0
        ORDER BY n.noticeTime DESC
    """
    )
    fun getListAll(search: BaseSearch): List<Vehiclenotificationdto>

    @Update("UPDATE tbl_vehiclenotification SET adminDisplay = 1")
    fun deleteListByAdminDisplay(): Int

    @Update("UPDATE tbl_vehiclenotification SET adminDisplay = 1 WHERE id = #{id};")
    fun deleteByAdminDisplayAndId(id: String): Int

    @Select(
        """
        SELECT 
            n.id As id,
            n.personnelId As personnelId,
            p.name AS name,
            p.position AS position,
            p.phone AS phone,
            n.vehicleInfoId AS vehicleInfoId,
            v.licensePlateNumber AS licensePlateNumber,
            n.noticeMethod AS noticeMethod,
            DATE_FORMAT(n.noticeTime, '%Y-%m-%d %H:%i:%s') AS noticeTime,
            n.feedbackContent AS feedbackContent
        FROM tbl_vehiclenotification n
        JOIN tbl_personnel p ON n.personnelId = p.id
        JOIN tbl_vehicleinfo v ON n.vehicleInfoId = v.id
        WHERE n.sysDeleted = 0
          AND p.sysDeleted = 0
          AND v.sysDeleted = 0
          AND n.personnelId=#{personnelId}
          ANd n.vehicleOwnerDisplay=0
        ORDER BY n.noticeTime DESC
    """
    )
    fun getListByPersonnelId(personnelId: String): List<Vehiclenotificationdto>

    /** 新增方法（返回时间最近的一条） */
    @Select("""
        SELECT * FROM tbl_vehiclenotification 
        WHERE personnelId = #{personnelId} 
        AND vehicleOwnerDisplay=0
        ORDER BY noticeTime DESC 
        LIMIT 1
    """)
    fun getLatestByPersonnelId(personnelId: String): Vehiclenotification?


    /** 处理车主反馈 */
    @Update("UPDATE tbl_vehiclenotification SET adminDisplay = 0 ,feedbackContent=#{feedbackContent} ,vehicleOwnerDisplay=1 WHERE id = #{id}")
    fun feedBack(vehicleNotification: Vehiclenotification): Int

    /** 查询未被车主处理的数据 */
    @Select("""
        SELECT 
            n.id As id,
            n.personnelId As personnelId,
            p.name AS name,
            p.position AS position,
            p.phone AS phone,
            n.vehicleInfoId AS vehicleInfoId,
            v.licensePlateNumber AS licensePlateNumber,
            n.noticeMethod AS noticeMethod,
            DATE_FORMAT(n.noticeTime, '%Y-%m-%d %H:%i:%s') AS noticeTime,
            n.feedbackContent AS feedbackContent
        FROM tbl_vehiclenotification n
        JOIN tbl_personnel p ON n.personnelId = p.id
        JOIN tbl_vehicleinfo v ON n.vehicleInfoId = v.id
        WHERE n.sysDeleted = 0
          AND p.sysDeleted = 0
          AND v.sysDeleted = 0
          ANd n.vehicleOwnerDisplay=0
          AND n.personnelId=#{id}
        ORDER BY n.noticeTime DESC
    """)
    fun getListTip(id :String): List<Vehiclenotificationdto>
    
    @Select("SELECT * FROM tbl_vehiclenotification WHERE vehicleInfoId= #{vehicleId} AND vehicleOwnerDisplay=0")
    fun getInfoByVehicleInfoId(vehicleId:String): Vehiclenotification?
}

/** 车辆通知服务类 */
@Service
class VehiclenotificationService(
    private val vehiclenotificationMapper: VehiclenotificationMapper,
    private val personnelMapper: PersonnelMapper,
    private val vehicleinfoMapper: VehicleinfoMapper,
    private val aliyunSmsUtil: AliyunSmsUtil,
    private val personnelService: PersonnelService
) : BaseService<Vehiclenotification, VehiclenotificationMapper>(vehiclenotificationMapper) {
    /** 获取所有未被清除的通知反馈 */
    fun getListAll(page: BaseSearch): Result {
        val result: Any = if (page.currentPage == null || page.pageRecord == null || !page.ifPage) {
            mapper.getListAll(page)
        } else {
            PageHelper.startPage<Vehiclenotification>(page.currentPage!!, page.pageRecord!!)
                .doSelectPageInfo<Vehiclenotification> {
                    mapper.getListAll(page)
                }.paginationInfo
        }
        return Result.getSuccess(result)
    }

    /** 清空所有的通知反馈 */
    fun deleteList(): Result {
        return Result.getSuccess(vehiclenotificationMapper.deleteListByAdminDisplay())
    }

    /** 获取车主未处理的通知反馈 */
    fun getListByPersonnelId(personnelId: String): Result {
        return Result.getSuccess(vehiclenotificationMapper.getListByPersonnelId(personnelId))
    }

    /** 管理员根据id逻辑删除对应的通知反馈 */
    fun deleteByAdminDisplayAndId(id: String): Result {
        return Result.getSuccess(vehiclenotificationMapper.deleteByAdminDisplayAndId(id))
    }

    /** 车主反馈 */
    fun feedBack(vehicleNotification: Vehiclenotification): Result {
        if(vehicleNotification.feedbackContent.contains("稍后挪车")){
    // 调整前端返回的反馈内容字符串格式将稍后挪车20分钟改为20分钟内挪车
            val cleanedContent = vehicleNotification.feedbackContent.substring(4,6)
            if (cleanedContent.isNotEmpty()) {
                val newContent = cleanedContent+"分钟内挪车"
                vehicleNotification.feedbackContent = newContent
            }
        }
        return Result.getSuccess(vehiclenotificationMapper.feedBack(vehicleNotification))
    }

    /** 对接短信接口以及机器人语音接口以后的save修改 */
    override fun save(model: Vehiclenotification): Result {
        if(model.noticeMethod.contains("短信通知")){
            if(personnelMapper.getInfo(model.personnelId)!!.phone.isEmpty()){
                return Result.getError("该车主号码为空")
            }
            if(vehicleinfoMapper.getInfo(model.vehicleInfoId)!!.licensePlateNumber.isEmpty()){
                return Result.getError("该车主车牌号为空")
            }
            else{
                val result = aliyunSmsUtil.sendSms(
                    phoneNumber = personnelMapper.getInfo(model.personnelId)!!.phone,
                    params = mapOf("licensePlateNumber" to vehicleinfoMapper.getInfo(model.vehicleInfoId)!!.licensePlateNumber,
                                    "NoName" to "懃务通"),
                    templateCode = "SMS_486610255"
                )
                return if (result) {
                    if(vehiclenotificationMapper.getInfoByVehicleInfoId(model.vehicleInfoId)!=null){
                        model.id=vehiclenotificationMapper.getInfoByVehicleInfoId(model.vehicleInfoId)!!.id
                        val id=super.save(model).datas
                        Result.getSuccess("通知信息发送成功 $id")
                    }else
                    {
                        val id=super.save(model).datas
                        Result.getSuccess("通知信息发送成功 $id")
                    }

                } else {
                    Result.getError("通知信息发送失败")
                }
            }
        }
        val id=super.save(model).datas
        return Result.getSuccess("通知信息发送成功 $id")
    }
    fun getListTip():Result{
        val id=personnelService.getCurrentPersonnel()!!.id
        return Result.getSuccess(vehiclenotificationMapper.getListByPersonnelId(id))
    }
}


/** 车辆通知Controller类 */
@RestController
@RequestMapping("/api/vehiclenotification")
class VehiclenotificationResource(service: VehiclenotificationService) :
    BaseResource<VehiclenotificationSearch, Vehiclenotification, VehiclenotificationMapper, VehiclenotificationService>(service) {
    /** 管理员逻辑删除所有通知 */
    @GetMapping("deleteListByAdminDisplay")
    fun deleteList(): Result {
        return service.deleteList()
    }

    /** 管理员逻辑查询所有的通知反馈 */
    @PostMapping("getListAll")
    fun getListAll(@Valid @RequestBody search: VehiclenotificationSearch): Result {
        return service.getListAll(search)
    }

    /** 车主逻辑获取通知 */
    @GetMapping("getListByPersonnelId/{personnelId}")
    fun getListByPersonnelId(@PathVariable personnelId: String): Result {
        return service.getListByPersonnelId(personnelId)
    }

    /** 管理员逻辑删除通知 */
    @GetMapping("deleteByAdminDisplayAndId/{id}")
    fun deleteByAdminDisplayAndId(@PathVariable id: String): Result {
        return service.deleteByAdminDisplayAndId(id)
    }

    @PostMapping("feedBack")
    fun feedBack(@Valid @RequestBody vehicleNotification: Vehiclenotification): Result {
        return service.feedBack(vehicleNotification)
    }
    @PostMapping("getListTip")
    fun getListTip(): Result {
        return service.getListTip()
    }
}
