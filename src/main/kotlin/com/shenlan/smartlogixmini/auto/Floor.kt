package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getInStr
import com.shenlan.smartlogixmini.util.notEmpty
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController


class Floor : BaseEntity {

    /** 楼层名称 */
    var floorName: String = ""

    constructor()
}

@Mapper
interface FloorMapper : BaseMapper<Floor> {

    @Select(""" select * from tbl_floor where 1=1 ${'$'}{whereSql} order by sysCreated """)
    override fun getList(search: BaseSearch): List<Floor>

    @Select(""" select count(*) from tbl_floor where floorName = #{floorName} and id != #{id} """)
    fun checkFloorNameExists(floorName: String, id: String): Int

}

class FloorSearch : BaseSearch {

    var floorNameList: List<String>? = null
    var floorNames:String = ""
        get() {
            if (floorNameList != null && floorNameList!!.isNotEmpty()) {
                return floorNameList!!.getInStr()
            }
            return field
        }

    var whereSql = ""
        get() {
            var sql = ""
            if (floorNames.notEmpty()) sql += " and floorName in (${floorNames}) "
            return sql
        }

    constructor()
}

@Service
open class FloorService(mapper: FloorMapper) : BaseService<Floor, FloorMapper, FloorSearch>(mapper) {

    override fun save(model: Floor): Result {
        var count = mapper.checkFloorNameExists(model.floorName, model.id)
        if (count > 0) {
            return Result.getError("区域已存在！")
        }
        return super.save(model)
    }

}

@RestController
@RequestMapping("/api/Floor")
open class FloorResource(service: FloorService) : BaseResource<FloorSearch, Floor, FloorMapper, FloorService>(service) {
}

