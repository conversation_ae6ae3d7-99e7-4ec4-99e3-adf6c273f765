//package com.shenlan.smartlogixmini.auto
//
//import com.shenlan.smartlogixmini.util.uuid
//import org.apache.ibatis.annotations.Delete
//import org.apache.ibatis.annotations.Mapper
//import org.apache.ibatis.annotations.Param
//import org.apache.ibatis.annotations.Select
//import org.springframework.stereotype.Service
//import org.springframework.web.bind.annotation.RequestMapping
//import org.springframework.web.bind.annotation.RestController
//import java.util.*
//import kotlin.math.ln
//
//class Commutestop: BaseModel {
//    var name: String = ""
//    var lat: Double = 0.0
//    var lng: Double = 0.0
//    var sysDeleted: Int = 0
//
//    constructor()
//
//    constructor(id: String, name: String, lat: Double, lng: Double) {
//        this.id = if (id.isEmpty()) uuid() else id
//        this.name = name
//        this.lat = lat
//        this.lng = lng
//    }
//}
//
//class CommutestopSearch: BaseSearch() {
//    var name: String = ""
//}
//
//@Mapper
//interface CommutestopMapper : BaseMapper<Commutestop> {
//    @Select("""
//        <script>
//            SELECT * FROM tbl_commutestop
//            <where>
//                AND sysDeleted = 0
//                <if test="name != ''">
//                    AND name LIKE CONCAT('%', #{name}, '%')
//                </if>
//            </where>
//            ORDER BY name ASC
//        </script>
//    """)
//    override fun getList(search: BaseSearch): List<Commutestop>
//
//
//    // 根据Commuteroutestop sequence获取站点信息
//    @Select("""
//        SELECT cs.name,cs.lat,cs.lng from tbl_commutestop cs left join
//        (SELECT * FROM tbl_commuteroutestop WHERE routeId = #{routeId}) crs
//        on cs.id = crs.stopId
//        WHERE crs.sequence = #{sequence}
//    """)
//    fun getStopByStopId(routeId: String, sequence: Int): Commutestop?
//
//    // 根据id列表删除站点
//    @Delete("""
//        <script>
//            DELETE FROM tbl_commutestop
//            WHERE id IN
//            <foreach item="item" index="" collection="list" open="(" separator="," close=")">
//                #{item}
//            </foreach>
//        </script>
//    """)
//    fun deleteByIdList(@Param("list") list: List<String>)
//}
//
//@Service
//class CommutestopService(mapper: CommutestopMapper) : BaseService<Commutestop, CommutestopMapper>(mapper)
//
//@RestController
//@RequestMapping("/api/commutestop")
//class CommutestopResource(service: CommutestopService) : BaseResource<CommutestopSearch, Commutestop, CommutestopMapper, CommutestopService>(service)
