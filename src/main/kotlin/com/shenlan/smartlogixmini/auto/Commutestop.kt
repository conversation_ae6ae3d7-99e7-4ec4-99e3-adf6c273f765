package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Commutestop: BaseModel() {
    var name: String = ""
    var lat: Double = 0.0
    var lng: Double = 0.0
    var sysDeleted: Int = 0
}

class CommutestopSearch: BaseSearch() {
    var name: String = ""
}

@Mapper
interface CommutestopMapper : BaseMapper<Commutestop> {
    @Select("""
        <script>
            SELECT * FROM tbl_commutestop
            <where>
                AND sysDeleted = 0
                <if test="name != ''">
                    AND name LIKE CONCAT('%', #{name}, '%')
                </if>
            </where>
            ORDER BY name ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutestop>
}

@Service
class CommutestopService(mapper: CommutestopMapper) : BaseService<Commutestop, CommutestopMapper>(mapper)

@RestController
@RequestMapping("/api/commutestop")
class CommutestopResource(service: CommutestopService) : BaseResource<CommutestopSearch, Commutestop, CommutestopMapper, CommutestopService>(service)
