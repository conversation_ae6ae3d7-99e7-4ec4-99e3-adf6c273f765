package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Commutetrip: BaseEntity() {
    var carId: String = ""
    var routeId: String = ""
    var driverId: String = ""
    var startTime: String = ""
    var endTime: String? = null
    var predictEndTime: String = ""
    var status: Int? = null
    var sysDeleted: Int = 0
    var estimatedTime: Int = 0  // 预估时间 单位分钟
    var stopSequence: Int = 0   // 当前站点序列号

    // 当前站点
    var currentStop: Commutepublishedroutestop? = null
    // 下一站点
    var nextStop: Commutepublishedroutestop? = null
}

class CommutetripSearch: BaseSearch() {
    var carId: String = ""
    var routeId: String = ""
    var driverId: String = ""
    var status: Int? = null
}

@Mapper
interface CommutetripMapper : BaseMapper<Commutetrip> {
    @Select("""
        <script>
            SELECT id, carId, routeId, driverId, 
            date_format(startTime, '%Y-%m-%d %H:%i:%s') as startTime, date_format(predictEndTime, '%Y-%m-%d %H:%i:%s') as predictEndTime, date_format(endTime, '%Y-%m-%d %H:%i:%s') as endTime,
            status, sysCreated, sysDeleted, estimatedTime, stopSequence FROM tbl_commutetrip
            <where>
                AND sysDeleted = 0
                <if test="carId != ''">
                    AND carId = #{carId}
                </if>
                <if test="routeId != ''">
                    AND routeId = #{routeId}
                </if>
                <if test="driverId != ''">
                    AND driverId = #{driverId}
                </if>
                <if test="status != null">
                    AND status = #{status}
                </if>
            </where>
            ORDER BY startTime DESC, sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutetrip>

    // 班车更新状态
    @Update("""
        UPDATE tbl_commutetrip
        SET status = #{status}, endTime = #{endTime}
        WHERE carId = #{carId}
    """)
    fun updateTripStatus(carId: String, status: Int, endTime: String)

    // 更新当前站点序列号
    @Update("""
        UPDATE tbl_commutetrip
        SET stopSequence = #{stopSequence}
        WHERE id = #{id}
    """)
    fun updateStopSequence(id: String, stopSequence: Int)

    // 根据carId列表查询行驶日志
    @Select("""
        <script>
            SELECT id, carId, routeId, driverId, 
            date_format(startTime, '%Y-%m-%d %H:%i:%s') as startTime, date_format(predictEndTime, '%Y-%m-%d %H:%i:%s') as predictEndTime, date_format(endTime, '%Y-%m-%d %H:%i:%s') as endTime,
            status, sysCreated, sysDeleted, estimatedTime, stopSequence FROM tbl_commutetrip
            <where>
                AND sysDeleted = 0
                <if test="list != null and list.size() > 0">
                    AND carId IN
                    <foreach item="item" index="" collection="list" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
        </script>
    """)
    fun getTripListByCarIdList(list: List<String>): List<Commutetrip>

    // 根据carId，更新所有记录状态
    @Update("""
        UPDATE tbl_commutetrip
        SET status = #{status}, sysDeleted = 1
        WHERE carId = #{carId}
    """)
    fun updateTripStatusByCarId(carId: String, status: Int)

    // 获取最新的一条行驶记录
    @Select("""
        SELECT * FROM tbl_commutetrip WHERE sysDeleted = 0 ORDER BY sysCreated DESC LIMIT 1
    """)
    fun getLatestTrip(): Commutetrip?
}

@Service
class CommutetripService(mapper: CommutetripMapper) : BaseService<Commutetrip, CommutetripMapper, CommutetripSearch>(mapper) {
    fun updateStopSequence(id: String, stopSequence: Int): Result {
        mapper.updateStopSequence(id, stopSequence)
        return Result.getSuccess(id)
    }
}

@RestController
@RequestMapping("/api/commutetrip")
class CommutetripResource(service: CommutetripService) : BaseResource<CommutetripSearch, Commutetrip, CommutetripMapper, CommutetripService>(service) {
    @GetMapping("/updateStopSequence/{tripId}/{stopSequence}")
    fun updateStopSequence(@PathVariable tripId: String, @PathVariable stopSequence: Int): Result {
        return service.updateStopSequence(tripId, stopSequence)
    }
}
