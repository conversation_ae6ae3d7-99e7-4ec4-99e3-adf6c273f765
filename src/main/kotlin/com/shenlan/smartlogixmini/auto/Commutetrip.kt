package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Commutetrip: BaseModel() {
    var carId: String = ""
    var routeId: String = ""
    var driverId: String = ""
    var startTime: String = ""
    var endTime: String? = null
    var predictEndTime: String = ""
    var status: Int? = null
    var sysDeleted: Int = 0
}

class CommutetripSearch: BaseSearch() {
    var carId: String = ""
    var routeId: String = ""
    var driverId: String = ""
    var status: Int? = null
}

@Mapper
interface CommutetripMapper : BaseMapper<Commutetrip> {
    @Select("""
        <script>
            SELECT * FROM tbl_commutetrip
            <where>
                AND sysDeleted = 0
                <if test="carId != ''">
                    AND carId = #{carId}
                </if>
                <if test="routeId != ''">
                    AND routeId = #{routeId}
                </if>
                <if test="driverId != ''">
                    AND driverId = #{driverId}
                </if>
                <if test="status != null">
                    AND status = #{status}
                </if>
            </where>
            ORDER BY startTime DESC, sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutetrip>

    // 班车停车，更新状态
    @Update("""
        UPDATE tbl_commutetrip
        SET status = #{status}, endTime = #{endTime}
        WHERE carId = #{carId}
    """)
    fun updateTripStatus(carId: String, status: Int, endTime: String)
}

@Service
class CommutetripService(mapper: CommutetripMapper) : BaseService<Commutetrip, CommutetripMapper>(mapper)

@RestController
@RequestMapping("/api/commutetrip")
class CommutetripResource(service: CommutetripService) : BaseResource<CommutetripSearch, Commutetrip, CommutetripMapper, CommutetripService>(service)
