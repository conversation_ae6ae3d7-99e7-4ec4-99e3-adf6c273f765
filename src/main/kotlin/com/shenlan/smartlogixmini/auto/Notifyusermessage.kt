package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Notifyusermessage: BaseEntity() {
    var userId: String = ""
    var messageId: String = ""
    var pushStatus: Int? = null     // 0：已推送，1：未推送
    var sysDeleted: Int = 0
}

class NotifyusermessageSearch: BaseSearch() {
    var userId: String = ""
    var messageId: String = ""
    var pushStatus: Int? = null
}

@Mapper
interface NotifyusermessageMapper : BaseMapper<Notifyusermessage> {
    @Select("""
        <script>
            SELECT * FROM tbl_notifyusermessage
            <where>
                AND sysDeleted = 0
                <if test="userId != ''">
                    AND userId = #{userId}
                </if>
                <if test="messageId != ''">
                    AND messageId = #{messageId}
                </if>
                <if test="pushStatus != null">
                    AND pushStatus = #{pushStatus}
                </if>
            </where>
            ORDER BY sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Notifyusermessage>

    // 根据userId和messageId获取pushStatus为0的最新消息
    @Select("""
        SELECT * FROM tbl_notifyusermessage
        WHERE userId = #{userId} AND messageId = #{messageId} AND pushStatus = 0
        ORDER BY sysCreated DESC
        LIMIT 1
    """)
    fun getLatestByUserIdAndMessageId(@Param("userId") userId: String, @Param("messageId") messageId: String): Notifyusermessage?

    /**
     * userId、module、type，删除所有小于sysCreated的消息
     * WHERE userId = #{userId} AND messageId IN (
     *             SELECT id FROM tbl_notifymessage WHERE module = #{module} AND type = #{type}
     *         ) AND sysCreated <= #{sysCreated}
     */
    @Delete("""
        <script>
            DELETE FROM tbl_notifyusermessage
            <where>
                <if test="userId != ''">
                    AND userId = #{userId}
                </if>
                AND messageId IN (
                    SELECT id FROM tbl_notifymessage WHERE module = #{module} AND type = #{type} and sysDeleted = 0
                )
            </where>
        </script>
    """)
    fun deleteHistoryMessages(@Param("userId") userId: String, @Param("module") module: String, @Param("type") type: String)
}

@Service
class NotifyusermessageService(mapper: NotifyusermessageMapper) : BaseService<Notifyusermessage, NotifyusermessageMapper, NotifyusermessageSearch>(mapper)

@RestController
@RequestMapping("/api/notifyusermessage")
class NotifyusermessageResource(service: NotifyusermessageService) : BaseResource<NotifyusermessageSearch, Notifyusermessage, NotifyusermessageMapper, NotifyusermessageService>(service)
