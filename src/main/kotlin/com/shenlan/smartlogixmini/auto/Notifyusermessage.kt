package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Notifyusermessage: BaseModel() {
    var userId: String = ""
    var messageId: String = ""
    var pushStatus: Int? = null     // 0：已推送，1：未推送
    var sysDeleted: Int = 0
}

class NotifyusermessageSearch: BaseSearch() {
    var userId: String = ""
    var messageId: String = ""
    var pushStatus: Int? = null
}

@Mapper
interface NotifyusermessageMapper : BaseMapper<Notifyusermessage> {
    @Select("""
        <script>
            SELECT * FROM tbl_notifyusermessage
            <where>
                AND sysDeleted = 0
                <if test="userId != ''">
                    AND userId = #{userId}
                </if>
                <if test="messageId != ''">
                    AND messageId = #{messageId}
                </if>
                <if test="pushStatus != null">
                    AND pushStatus = #{pushStatus}
                </if>
            </where>
            ORDER BY sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Notifyusermessage>
}

@Service
class NotifyusermessageService(mapper: NotifyusermessageMapper) : BaseService<Notifyusermessage, NotifyusermessageMapper>(mapper)

@RestController
@RequestMapping("/api/notifyusermessage")
class NotifyusermessageResource(service: NotifyusermessageService) : BaseResource<NotifyusermessageSearch, Notifyusermessage, NotifyusermessageMapper, NotifyusermessageService>(service)
