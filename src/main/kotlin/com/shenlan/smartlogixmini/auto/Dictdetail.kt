package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.deepClone
import com.shenlan.smartlogixmini.util.errorlog
import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.concurrent.ConcurrentHashMap

/**
 * 字典详情实体类
 */
class Dictdetail : BaseModel() {
    // 数据库表字段

    /** 父级ID */
    var parentId: String = ""
    /** 字典编码 */
    var dictCode: String = ""
    /** 字典名称 */
    var dictName: String = ""
    /** 选项编码 */
    var itemCode: String = ""
    /** 选项名称 */
    var itemName: String = ""
    /** 英文名称 */
    var itemNameEn: String = ""
    /** 排序顺序 */
    var sortOrder: Int = 0

    // 关联字段

    /** 子级选项列表 */
    var children = mutableListOf<Dictdetail>()

    // 其他字段
    
    /** 是否选中 */
    var selected: Boolean = false
    /** 父级编码 */
    var parentCode: String = ""
}

data class DictdetailSearch(
    /** 字典编码 */
    val dictCode: String = "",
    /** 父级ID */
    val parentId: String = "",
    /** 选项编码 */
    val itemCode: String = "",
    /** 是否加载子项 */
    val loadChildren: Boolean = false
) : BaseSearch()

@Mapper
interface DictdetailMapper : BaseMapper<Dictdetail> {

    @Select("""
        <script>
        SELECT * FROM tbl_dictdetail
        <where>
            <if test="dictCode != ''">
                AND dictcode = #{dictCode}
            </if>
            <if test="parentId != ''">
                AND parentid = #{parentId}
            </if>
            <if test="itemCode != ''">
                AND itemcode = #{itemCode}
            </if>
        </where>
        ORDER BY dictcode, sortOrder, itemcode
        </script>
    """)
    override fun getList(search: BaseSearch): List<Dictdetail>
}

@Service
class DictdetailService(mapper: DictdetailMapper) : BaseService<Dictdetail, DictdetailMapper>(mapper) {

    // 使用线程安全的ConcurrentHashMap作为字典名称缓存
    private val dictNameCache = ConcurrentHashMap<String, String>()

    // 自我注入，解决@Cacheable内部调用失效问题
    @Autowired
    @Lazy
    private lateinit var self: DictdetailService

    /**
     * 构建树形结构
     */
    fun buildTree(details: List<Dictdetail>): List<Dictdetail> {
        // 分离父级和子级
        val parentMap = mutableMapOf<String, Dictdetail>()
        val childrenMap = mutableMapOf<String, MutableList<Dictdetail>>()

        details.forEach { detail ->
            if (detail.parentId.isEmpty()) {
                parentMap[detail.id] = detail
            } else {
                childrenMap.getOrPut(detail.parentId) { mutableListOf() }.add(detail)
            }
        }

        // 构建父子关系
        parentMap.values.forEach { parent ->
            parent.children = childrenMap[parent.id]?.toMutableList() ?: mutableListOf()
        }

        return parentMap.values.toList()
    }

    // ========== 字典缓存相关方法 ==========

    /**
     * 刷新字典名称缓存
     */
    @CacheEvict(value = ["dictdetails", "dictionary", "dictionaries"], allEntries = true)
    fun refreshCache() {
        try {
            log.info("Refreshing dictionary cache")
            dictNameCache.clear()

            val allDictDetails = mapper.getList()
            allDictDetails.forEach { detail ->
                val key = "${detail.dictCode};${detail.itemCode}"
                dictNameCache[key] = detail.itemName
            }

            log.info("Dictionary cache refreshed successfully, cached ${dictNameCache.size} items")
        } catch (e: Exception) {
            log.error("Failed to refresh dictionary cache: ${e.message}")
            errorlog(e)
        }
    }

    /**
     * 根据编码获取字典项名称
     */
    fun getDictName(itemCode: String, dictCode: String): String {
        if (itemCode.isEmpty()) return ""

        val key = "$dictCode;$itemCode"
        return dictNameCache[key] ?: run {
            // 缓存未命中时刷新缓存
            refreshCache()
            dictNameCache[key] ?: ""
        }
    }

    /**
     * 获取指定字典编码的所有选项（用于缓存）
     */
    @Cacheable("dictdetails", key = "#search")
    fun getDictdetailList(search: DictdetailSearch): List<Dictdetail> {
        return try {
            log.info("Query dict details by code: ${search.dictCode}, parentId: ${search.parentId}, loadChildren: ${search.loadChildren}")
            val details = mapper.getList(search)

            // 如果需要加载子项
            val result = if (search.loadChildren) {
                details.map { parent ->
                    val clonedParent = deepClone(parent)
                    // 查询子项
                    val childSearch = DictdetailSearch(
                        dictCode = search.dictCode,
                        parentId = parent.itemCode
                    )
                    val children = mapper.getList(childSearch)
                    clonedParent.children = children.map { deepClone(it) }.toMutableList()
                    clonedParent
                }
            } else {
                // 深度克隆避免缓存污染
                details.map { deepClone(it) }
            }

            result
        } catch (e: Exception) {
            log.error("Failed to query dict details for code ${search.dictCode}, parentId ${search.parentId}: ${e.message}")
            errorlog(e)
            emptyList()
        }
    }

    /**
     * 获取选中项的详情列表
     */
    fun getSelectedItems(dict: Dictdetail): List<Dictdetail> {
        val selectedItems = mutableListOf<Dictdetail>()
        collectSelectedItems(dict, selectedItems)
        return selectedItems
    }

    /**
     * 获取选中项的代码列表
     */
    fun getSelectedItemCodes(dict: Dictdetail): List<String> {
        val selectedItems = mutableListOf<Dictdetail>()
        collectSelectedItems(dict, selectedItems)
        return selectedItems.map { it.itemCode }
    }

    /**
     * 递归收集选中的项目
     */
    private fun collectSelectedItems(dict: Dictdetail, result: MutableList<Dictdetail>) {
        if (dict.selected) {
            result.add(dict)
        }
        dict.children.forEach { child ->
            collectSelectedItems(child, result)
        }
    }
}

@RestController
@RequestMapping("/api/Dictdetail")
class DictdetailResource(
    service: DictdetailService
) : BaseResource<DictdetailSearch, Dictdetail, DictdetailMapper, DictdetailService>(service)
