package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.getUser
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 消息模板实体类
 */
class Messagetemplate : BaseEntity() {
    // 数据库表字段

    /** 模板名称 */
    var name: String = ""
    /** 微信模板ID */
    var wechatTemplateId: String = ""
    /** 模板描述 */
    var description: String = ""
    /** 是否允许手动重复订阅 */
    var ifAllowManualResubscribe: Boolean = false

    // 关联字段

    /** 当前登录用户的订阅次数 */
    var currentUserSubscribeCount: Int = -1
}

/**
 * 消息模板查询条件类
 */
class MessagetemplateSearch : BaseSearch() {
    /** 是否允许手动重复订阅 */
    var ifAllowManualResubscribe: Boolean? = null
    /** 是否筛选当前登录用户有权限订阅的模板 */
    var filterByCurrentUserPermission: Boolean = false
    /** 当前用户ID(内部使用) */
    var currentUserId: String = ""
    /** 是否加载当前用户订阅次数 */
    var loadCurrentUserSubscribeCount: Boolean = false
}

/**
 * 消息模板Mapper接口
 */
@Mapper
interface MessagetemplateMapper : BaseMapper<Messagetemplate> {

    @Select("""
        <script>
            SELECT DISTINCT w.* FROM tbl_messagetemplate w
            <if test="filterByCurrentUserPermission == true">
                INNER JOIN tbl_templatepermission tp ON w.id = tp.templateId
                WHERE tp.sysDeleted = 0 AND w.sysDeleted = 0 
                AND tp.permissionId IN (
                    SELECT DISTINCT p.id FROM tbl_permission p
                    WHERE p.sysDeleted = 0 AND p.id IN (
                        SELECT pp.permissionId FROM tbl_personnelpermission pp
                        WHERE pp.sysDeleted = 0 AND pp.personnelId = #{currentUserId}
                        UNION
                        SELECT wp.permissionId FROM tbl_workgrouppermission wp
                        INNER JOIN tbl_workgrouppersonnel wgp ON wp.workgroupId = wgp.workgroupId
                        WHERE wp.sysDeleted = 0 AND wgp.sysDeleted = 0 AND wgp.personnelId = #{currentUserId}
                    )
                )
                <if test="ifAllowManualResubscribe != null">
                    AND w.ifAllowManualResubscribe = #{ifAllowManualResubscribe}
                </if>
            </if>
            <if test="filterByCurrentUserPermission == false">
                WHERE w.sysDeleted = 0
                <if test="ifAllowManualResubscribe != null">
                    AND w.ifAllowManualResubscribe = #{ifAllowManualResubscribe}
                </if>
            </if>
            ORDER BY w.name
        </script>
    """)
    override fun getList(search: BaseSearch): List<Messagetemplate>

    @Select(""" 
        SELECT * FROM tbl_messagetemplate 
        WHERE name = #{name}
    """)
    fun getInfoByName(name: String): Messagetemplate?
}

/**
 * 消息模板Service类
 */
@Service
class MessagetemplateService(
    mapper: MessagetemplateMapper,
    private val personneltemplateMapper: PersonneltemplateMapper
) : BaseService<Messagetemplate, MessagetemplateMapper, MessagetemplateSearch>(mapper) {

    fun getInfoByName(name: String) = mapper.getInfoByName(name)

    /**
     * 重写getEntity方法，加载当前用户订阅次数
     */
    override fun getEntity(id: String): Messagetemplate? {
        val messagetemplate = super.getEntity(id)
        if (messagetemplate != null) {
            loadCurrentUserSubscribeCount(listOf(messagetemplate))
        }
        return messagetemplate
    }

    /**
     * 重写getEntityPage方法，处理权限筛选逻辑
     */
    override fun getEntityPage(search: MessagetemplateSearch): PaginationInfo<Messagetemplate> {
        // 如果需要按当前用户权限筛选
        if (search.filterByCurrentUserPermission) {
            val currentUser = getUser()
            if (currentUser != null) {
                search.currentUserId = currentUser.id
            }
        }

        val paginationInfo = super.getEntityPage(search)
        val messagetemplateList = paginationInfo.result

        // 如果需要加载当前用户订阅次数
        if (search.loadCurrentUserSubscribeCount) {
            loadCurrentUserSubscribeCount(messagetemplateList)
        }

        return paginationInfo.withResult(messagetemplateList)
    }

    /**
     * 加载当前用户的订阅次数
     */
    private fun loadCurrentUserSubscribeCount(messagetemplateList: List<Messagetemplate>) {
        if (messagetemplateList.isEmpty()) return

        val currentUser = getUser()
        val currentUserId = currentUser?.id ?: return

        // 查询当前用户对所有模板的订阅记录
        val personneltemplateSearch = PersonneltemplateSearch()
        personneltemplateSearch.personnelId = currentUserId
        personneltemplateSearch.ifPage = false

        val personneltemplateList = personneltemplateMapper.getList(personneltemplateSearch)
        val subscribeCountMap = personneltemplateList.associateBy({ it.templateId }, { it.subscribeCount })

        // 填充订阅次数到每个模板对象
        messagetemplateList.forEach { messagetemplate ->
            messagetemplate.currentUserSubscribeCount = subscribeCountMap[messagetemplate.id] ?: -1
        }
    }
}

/**
 * 消息模板Controller类
 */
@RestController
@RequestMapping("/api/Messagetemplate")
class MessagetemplateResource(
    service: MessagetemplateService
) : BaseResource<MessagetemplateSearch, Messagetemplate, MessagetemplateMapper, MessagetemplateService>(service) {
    @GetMapping("/getInfoByName/{name}")
    fun getInfoByName(@PathVariable name: String) = Result.getSuccess(service.getInfoByName(name))
}
