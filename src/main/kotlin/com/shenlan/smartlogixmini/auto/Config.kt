package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Config : BaseEntity() {
    /** 配置编码 */
    var configCode: String = ""
    /** 配置名称 */
    var configName: String = ""
    /** JSON配置值 */
    var jsonValue: String = ""
    /** 配置描述 */
    var description: String = ""
}

@Mapper
interface ConfigMapper : BaseMapper<Config> {

    @Select("select jsonValue from tbl_config where configCode = #{code} limit 1")
    fun getValueByCode(@Param("code") code: String): String

    @Update("update tbl_config set jsonValue = #{value} where configCode = #{code}")
    fun updateConfig(@Param("code") code: String, @Param("value") value: String)
}

class ConfigSearch : BaseSearch()

@Service
class ConfigService(mapper: ConfigMapper ) : BaseService<Config, ConfigMapper, ConfigSearch>(mapper)

@RestController
@RequestMapping("/api/Config")
class ConfigResource(service: ConfigService) : BaseResource<ConfigSearch, Config, ConfigMapper, ConfigService>(service)
