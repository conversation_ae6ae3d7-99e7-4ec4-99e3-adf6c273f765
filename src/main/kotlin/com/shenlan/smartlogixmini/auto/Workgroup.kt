package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.getChineseFirstLetters
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 工作组实体类
 */
class Workgroup : BaseModel() {
    // 数据库表字段

    /** 工作组名称 */
    var name: String = ""
    /** 排序号 */
    var sortOrder: Int = 0

    // 关联字段

    /** 工作组成员列表 */
    var personnelList: List<Personnel> = listOf()
    /** 工作组权限列表 */
    var permissionList: List<Permission> = listOf()

    // 其他字段

    /** 用户名（创建工作组时使用，不保存到数据库） */
    var username: String = ""
    /** 密码（创建工作组时使用，不保存到数据库） */
    var password: String = ""
}

/**
 * 工作组查询条件类
 */
class WorkgroupSearch : BaseSearch() {
    /** 关键词(工作组名称) */
    var keyword: String = ""
    /** 工作组名称模糊匹配 */
    var nameLike: String = ""
    /** 是否加载负责人信息 */
    var loadLeader: Boolean = false
    /** 人员ID(查询指定人员所属的工作组) */
    var personnelId: String = ""
    /** 是否加载成员列表 */
    var loadPersonnelList: Boolean = false
    /** 是否加载权限列表 */
    var loadPermissionList: Boolean = false
}

/**
 * 工作组Mapper接口
 */
@Mapper
interface WorkgroupMapper : BaseMapper<Workgroup> {

    @Select("""
        <script>
            SELECT w.* FROM tbl_workgroup w
            <if test="personnelId != ''">
                INNER JOIN tbl_workgrouppersonnel wp ON w.id = wp.workgroupId
            </if>
            <where>
                w.sysDeleted = 0
                <if test="keyword != ''">
                    AND w.name LIKE CONCAT('%', #{keyword}, '%')
                </if>
                <if test="nameLike != ''">
                    AND w.name LIKE CONCAT('%', #{nameLike}, '%')
                </if>
                <if test="personnelId != ''">
                    AND wp.personnelId = #{personnelId}
                </if>
            </where>
            ORDER BY w.sortOrder, w.name
        </script>
    """)
    override fun getList(search: BaseSearch): List<Workgroup>

    /**
     * 重写getInfo方法
     */
    @Select("""
        SELECT * FROM tbl_workgroup
        WHERE id = #{id} AND sysDeleted = 0
    """)
    override fun getInfo(id: String): Workgroup?

    /**
     * 获取最大排序号
     */
    @Select("""
        SELECT COALESCE(MAX(sortOrder), 0) FROM tbl_workgroup
        WHERE sysDeleted = 0
    """)
    fun getMaxSortOrder(): Int
}

/**
 * 工作组Service类
 */
@Service
class WorkgroupService(
    mapper: WorkgroupMapper,
    private val personnelMapper: PersonnelMapper,
    private val workgrouppersonnelMapper: WorkgrouppersonnelMapper,
    private val permissionMapper: PermissionMapper,
    private val workgrouppermissionMapper: WorkgrouppermissionMapper,
    private val personnelpermissionMapper: PersonnelpermissionMapper
) : BaseService<Workgroup, WorkgroupMapper>(mapper) {

    /**
     * 重写getList方法，根据需要加载关联信息
     */
    override fun getEntityPage(search: BaseSearch): PaginationInfo<Workgroup> {
        search as WorkgroupSearch
        val paginationInfo = super.getEntityPage(search)
        // 获取工作组列表
        val workgroupList = paginationInfo.result

        // 加载关联信息
        workgroupList.forEach { workgroup ->
            if (search.loadPersonnelList) {
                loadPersonnelList(workgroup)
            }
            if (search.loadPermissionList) {
                loadPermissionList(workgroup)
            }
        }
        return paginationInfo
    }

    /**
     * 重写getInfo方法，加载工作组的关联信息
     */
    override fun getEntity(id: String): Workgroup? {
        val workgroup = mapper.getInfo(id)
        if (workgroup != null) {
            // 加载成员列表
            loadPersonnelList(workgroup)
            // 加载权限列表
            loadPermissionList(workgroup)
        }
        return workgroup
    }

    /**
     * 加载工作组成员列表
     */
    private fun loadPersonnelList(workgroup: Workgroup) {
        // 通过关系表查询工作组成员
        val search = WorkgrouppersonnelSearch()
        search.workgroupId = workgroup.id
        search.ifPage = false
        val relations = workgrouppersonnelMapper.getList(search)

        // 获取人员信息并排序：工作组默认账号置顶
        workgroup.personnelList = relations.mapNotNull { relation ->
            personnelMapper.getInfo(relation.personnelId)
        }.sortedWith(compareBy { it.ifWorkgroupDefault != 1 })
    }

    /**
     * 加载工作组权限列表
     */
    private fun loadPermissionList(workgroup: Workgroup) {
        // 通过关系表查询工作组权限
        val search = WorkgrouppermissionSearch()
        search.workgroupId = workgroup.id
        search.ifPage = false
        val relations = workgrouppermissionMapper.getList(search)

        // 获取权限信息
        workgroup.permissionList = relations.mapNotNull { relation ->
            permissionMapper.getInfo(relation.permissionId)
        }
    }

    /**
     * 重写save方法，设置sortOrder默认排在最后，并处理工作组人员关联
     */
    @Transactional
    override fun saveEntity(entity: Workgroup): String {
        // 设置排序号
        if (entity.id.isEmpty() && entity.sortOrder == 0) {
            val maxSortOrder = mapper.getMaxSortOrder()
            entity.sortOrder = maxSortOrder + 1
        }

        // 保存工作组基本信息
        val id = super.saveEntity(entity)

        // 处理工作组人员关联
        workgrouppersonnelMapper.deleteByWorkgroupId(entity.id)

        // 添加新的工作组人员关联
        if (entity.personnelList.isNotEmpty()) {
            for (personnel in entity.personnelList) {
                val relation = Workgrouppersonnel()
                relation.id = uuid()
                relation.workgroupId = entity.id
                relation.personnelId = personnel.id
                workgrouppersonnelMapper.insert(relation)
            }
        }

        // 处理工作组权限关联
        workgrouppermissionMapper.deleteByWorkgroupId(entity.id)

        // 添加新的工作组权限关联
        if (entity.permissionList.isNotEmpty()) {
            for (permission in entity.permissionList) {
                val relation = Workgrouppermission()
                relation.id = uuid()
                relation.workgroupId = entity.id
                relation.permissionId = permission.id
                workgrouppermissionMapper.insert(relation)
            }
        }

        // 创建或更新工作组默认账号
        if (entity.username.isNotEmpty() && entity.password.isNotEmpty()) {
            try {
                // 检查用户名是否已存在
                val existingPersonnel = personnelMapper.getInfoByUsername(entity.username)

                if (existingPersonnel != null) {
                    // 更新现有账号密码
                    existingPersonnel.password = entity.password
                    personnelMapper.delete(existingPersonnel.id)
                    personnelMapper.insert(existingPersonnel)
                    log.info("Updated password for existing account: ${entity.username}")

                    // 确保该人员在工作组中
                    val existingRelation = workgrouppersonnelMapper.getList(WorkgrouppersonnelSearch().apply {
                        workgroupId = entity.id
                        personnelId = existingPersonnel.id
                        ifPage = false
                    })

                    if (existingRelation.isEmpty()) {
                        val relation = Workgrouppersonnel()
                        relation.id = uuid()
                        relation.workgroupId = entity.id
                        relation.personnelId = existingPersonnel.id
                        workgrouppersonnelMapper.insert(relation)
                    }

                    // 更新现有人员的权限以匹配工作组权限
                    personnelpermissionMapper.deleteByPersonnelId(existingPersonnel.id)
                    if (entity.permissionList.isNotEmpty()) {
                        for (permission in entity.permissionList) {
                            val personnelPermission = Personnelpermission()
                            personnelPermission.id = uuid()
                            personnelPermission.personnelId = existingPersonnel.id
                            personnelPermission.permissionId = permission.id
                            personnelpermissionMapper.insert(personnelPermission)
                        }
                    }
                } else {
                    // 创建新账号
                    val personnel = Personnel()
                    personnel.id = uuid()
                    personnel.name = "${entity.name}账号"
                    personnel.nameInitials = getChineseFirstLetters(personnel.name)
                    personnel.username = entity.username
                    personnel.password = entity.password
                    personnel.type = 0 // 内部成员
                    personnel.organizationId = "" // 暂不指定组织机构
                    personnel.phone = "" // 暂不设置手机号
                    personnel.ifWorkgroupDefault = 1 // 标识为工作组默认账号

                    personnelMapper.insert(personnel)

                    // 将创建的人员添加到工作组中
                    val relation = Workgrouppersonnel()
                    relation.id = uuid()
                    relation.workgroupId = entity.id
                    relation.personnelId = personnel.id
                    workgrouppersonnelMapper.insert(relation)

                    // 为创建的人员设置与工作组相同的权限
                    if (entity.permissionList.isNotEmpty()) {
                        for (permission in entity.permissionList) {
                            val personnelPermission = Personnelpermission()
                            personnelPermission.id = uuid()
                            personnelPermission.personnelId = personnel.id
                            personnelPermission.permissionId = permission.id
                            personnelpermissionMapper.insert(personnelPermission)
                        }
                    }

                    log.info("Created new workgroup account: ${personnel.username}")
                }
            } catch (e: Exception) {
                // 记录创建/更新账号失败的日志，但不影响工作组创建
                log.warn("Create or update workgroup account failed: ${e.message}")
            }
        }

        return id
    }

    /**
     * 重写delete方法，删除工作组时同时删除相关的工作组人员关系和权限关系
     */
    @Transactional
    override fun deleteEntity(id: String): Int {
        // 先删除工作组人员关联
        workgrouppersonnelMapper.deleteByWorkgroupId(id)

        // 删除工作组权限关联
        workgrouppermissionMapper.deleteByWorkgroupId(id)

        // 再删除工作组
        return super.deleteEntity(id)
    }
}

/**
 * 工作组Controller类
 */
@RestController
@RequestMapping("/api/Workgroup")
class WorkgroupResource(service: WorkgroupService) : BaseResource<WorkgroupSearch, Workgroup, WorkgroupMapper, WorkgroupService>(service)
