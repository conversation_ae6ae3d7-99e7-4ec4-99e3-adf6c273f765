package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.getChineseFirstLetters
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 工作组实体类
 */
class Workgroup : BaseEntity() {
    // 数据库表字段

    /** 工作组名称 */
    var name: String = ""
    /** 排序号 */
    var sortOrder: Int = 0

    // 关联字段

    /** 工作组成员列表 */
    var personnelList: List<Personnel> = listOf()
    /** 工作组权限列表 */
    var permissionList: List<Permission> = listOf()

    // 其他字段

    /** 用户名（创建工作组时使用，不保存到数据库） */
    var username: String = ""
    /** 密码（创建工作组时使用，不保存到数据库） */
    var password: String = ""
}

/**
 * 工作组查询条件类
 */
class WorkgroupSearch : BaseSearch() {
    /** 关键词(工作组名称) */
    var keyword: String = ""
    /** 工作组名称模糊匹配 */
    var nameLike: String = ""
    /** 是否加载负责人信息 */
    var loadLeader: Boolean = false
    /** 人员ID(查询指定人员所属的工作组) */
    var personnelId: String = ""
    /** 是否加载成员列表 */
    var loadPersonnelList: Boolean = false
    /** 是否加载权限列表 */
    var loadPermissionList: Boolean = false
}

/**
 * 工作组Mapper接口
 */
@Mapper
interface WorkgroupMapper : BaseMapper<Workgroup> {

    @Select("""
        <script>
            SELECT w.* FROM tbl_workgroup w
            <if test="personnelId != ''">
                INNER JOIN tbl_workgrouppersonnel wp ON w.id = wp.workgroupId
            </if>
            <where>
                w.sysDeleted = 0
                <if test="keyword != ''">
                    AND w.name LIKE CONCAT('%', #{keyword}, '%')
                </if>
                <if test="nameLike != ''">
                    AND w.name LIKE CONCAT('%', #{nameLike}, '%')
                </if>
                <if test="personnelId != ''">
                    AND wp.personnelId = #{personnelId}
                </if>
            </where>
            ORDER BY w.sortOrder, w.name
        </script>
    """)
    override fun getList(search: BaseSearch): List<Workgroup>

    /**
     * 重写getInfo方法
     */
    @Select("""
        SELECT * FROM tbl_workgroup
        WHERE id = #{id} AND sysDeleted = 0
    """)
    override fun getInfo(id: String): Workgroup?

    /**
     * 获取最大排序号
     */
    @Select("""
        SELECT COALESCE(MAX(sortOrder), 0) FROM tbl_workgroup
        WHERE sysDeleted = 0
    """)
    fun getMaxSortOrder(): Int
}

/**
 * 工作组Service类
 */
@Service
class WorkgroupService(
    mapper: WorkgroupMapper,
    private val personnelMapper: PersonnelMapper,
    private val workgrouppersonnelMapper: WorkgrouppersonnelMapper,
    private val permissionMapper: PermissionMapper,
    private val workgrouppermissionMapper: WorkgrouppermissionMapper,
    private val personnelpermissionMapper: PersonnelpermissionMapper,
    private val personnelService: PersonnelService
) : BaseService<Workgroup, WorkgroupMapper, WorkgroupSearch>(mapper) {

    /**
     * 重写getList方法，根据需要加载关联信息
     */
    override fun getEntityPage(search: WorkgroupSearch): PaginationInfo<Workgroup> {
        val paginationInfo = super.getEntityPage(search)
        // 获取工作组列表
        val workgroupList = paginationInfo.result

        // 加载关联信息
        workgroupList.forEach { workgroup ->
            if (search.loadPersonnelList) {
                loadPersonnelList(workgroup)
            }
            if (search.loadPermissionList) {
                loadPermissionList(workgroup)
            }
        }
        return paginationInfo
    }

    /**
     * 重写getInfo方法，加载工作组的关联信息
     */
    override fun getEntity(id: String): Workgroup? {
        val workgroup = mapper.getInfo(id)
        if (workgroup != null) {
            // 加载成员列表
            loadPersonnelList(workgroup)
            // 加载权限列表
            loadPermissionList(workgroup)
        }
        return workgroup
    }

    /**
     * 加载工作组成员列表
     */
    private fun loadPersonnelList(workgroup: Workgroup) {
        // 通过关系表查询工作组成员
        val search = WorkgrouppersonnelSearch()
        search.workgroupId = workgroup.id
        search.ifPage = false
        val relations = workgrouppersonnelMapper.getList(search)

        // 获取人员信息并排序：工作组默认账号置顶
        workgroup.personnelList = relations.mapNotNull { relation ->
            personnelMapper.getInfo(relation.personnelId)
        }.sortedWith(compareBy { it.ownerWorkgroupId.isEmpty() })

        // 找到工作组默认账号，将其用户名和密码赋值给工作组
        val defaultPersonnel = workgroup.personnelList.find { it.ownerWorkgroupId == workgroup.id }
        if (defaultPersonnel != null) {
            workgroup.username = defaultPersonnel.username ?: ""
            workgroup.password = defaultPersonnel.password
        }
    }

    /**
     * 加载工作组权限列表
     */
    private fun loadPermissionList(workgroup: Workgroup) {
        // 通过关系表查询工作组权限
        val search = WorkgrouppermissionSearch()
        search.workgroupId = workgroup.id
        search.ifPage = false
        val relations = workgrouppermissionMapper.getList(search)

        // 获取权限信息
        workgroup.permissionList = relations.mapNotNull { relation ->
            permissionMapper.getInfo(relation.permissionId)
        }
    }

    /**
     * 重写save方法，设置sortOrder默认排在最后，并处理工作组人员关联
     */
    @Transactional
    override fun saveEntity(entity: Workgroup): String {
        // 设置排序号
        if (entity.id.isEmpty() && entity.sortOrder == 0) {
            val maxSortOrder = mapper.getMaxSortOrder()
            entity.sortOrder = maxSortOrder + 1
        }

        // 保存工作组基本信息
        val id = super.saveEntity(entity)

        // 处理工作组人员关联
        workgrouppersonnelMapper.deleteByWorkgroupId(entity.id)

        // 添加新的工作组人员关联
        if (entity.personnelList.isNotEmpty()) {
            for (personnel in entity.personnelList) {
                val relation = Workgrouppersonnel()
                relation.id = uuid()
                relation.workgroupId = entity.id
                relation.personnelId = personnel.id
                workgrouppersonnelMapper.insert(relation)
            }
        }

        // 处理工作组权限关联
        workgrouppermissionMapper.deleteByWorkgroupId(entity.id)

        // 添加新的工作组权限关联
        if (entity.permissionList.isNotEmpty()) {
            for (permission in entity.permissionList) {
                val relation = Workgrouppermission()
                relation.id = uuid()
                relation.workgroupId = entity.id
                relation.permissionId = permission.id
                workgrouppermissionMapper.insert(relation)
            }
        }

        // 创建或更新工作组默认账号
        if (entity.username.isNotEmpty() && entity.password.isNotEmpty()) {
            try {
                // 首先检查该工作组是否已有默认账号
                val personnelSearch = PersonnelSearch()
                personnelSearch.ownerWorkgroupId = entity.id
                personnelSearch.ifPage = false
                val existingDefaultPersonnelList = personnelService.getEntityList(personnelSearch)
                val existingDefaultPersonnel = existingDefaultPersonnelList.firstOrNull()

                if (existingDefaultPersonnel != null) {
                    // 更新现有默认账号的用户名和密码
                    existingDefaultPersonnel.username = entity.username
                    existingDefaultPersonnel.password = entity.password
                    existingDefaultPersonnel.ifEnabled = true // 确保启用状态

                    // 确保默认账号的workgroupList包含当前工作组，避免在保存时删除工作组关联
                    val currentWorkgroup = Workgroup()
                    currentWorkgroup.id = entity.id
                    currentWorkgroup.name = entity.name
                    existingDefaultPersonnel.workgroupList = listOf(currentWorkgroup)

                    personnelService.saveEntity(existingDefaultPersonnel)
                    log.info("Updated existing default account for workgroup ${entity.name}: username=${entity.username}")

                    // 确保该人员在工作组中
                    val existingRelation = workgrouppersonnelMapper.getList(WorkgrouppersonnelSearch().apply {
                        workgroupId = entity.id
                        personnelId = existingDefaultPersonnel.id
                        ifPage = false
                    })

                    if (existingRelation.isEmpty()) {
                        val relation = Workgrouppersonnel()
                        relation.id = uuid()
                        relation.workgroupId = entity.id
                        relation.personnelId = existingDefaultPersonnel.id
                        workgrouppersonnelMapper.insert(relation)
                    }

                    // 更新现有人员的权限以匹配工作组权限
                    personnelpermissionMapper.deleteByPersonnelId(existingDefaultPersonnel.id)
                    if (entity.permissionList.isNotEmpty()) {
                        for (permission in entity.permissionList) {
                            val personnelPermission = Personnelpermission()
                            personnelPermission.id = uuid()
                            personnelPermission.personnelId = existingDefaultPersonnel.id
                            personnelPermission.permissionId = permission.id
                            personnelpermissionMapper.insert(personnelPermission)
                        }
                    }
                } else {
                    // 该工作组没有默认账号，需要创建新账号
                    // 但首先检查用户名是否已被其他人员占用
                    val existingPersonnelByUsername = personnelService.getPersonnelByUsername(entity.username)
                    if (existingPersonnelByUsername != null) {
                        log.warn("Username ${entity.username} already exists for another personnel, cannot create default account for workgroup ${entity.name}")
                        throw BusinessException("用户名「${entity.username}」已被占用，无法创建工作组默认账号")
                    }

                    // 创建新的默认账号
                    val personnel = Personnel()
                    personnel.id = uuid()
                    personnel.name = "${entity.name}账号"
                    personnel.nameInitials = getChineseFirstLetters(personnel.name)
                    personnel.username = entity.username
                    personnel.password = entity.password
                    personnel.type = 0 // 内部成员
                    personnel.organizationId = "" // 暂不指定组织机构
                    personnel.phone = "" // 暂不设置手机号
                    personnel.ownerWorkgroupId = entity.id // 设置所属工作组ID

                    // 设置workgroupList包含当前工作组，确保保存时不会删除工作组关联
                    val currentWorkgroup = Workgroup()
                    currentWorkgroup.id = entity.id
                    currentWorkgroup.name = entity.name
                    personnel.workgroupList = listOf(currentWorkgroup)

                    personnelService.saveEntity(personnel)

                    // 将创建的人员添加到工作组中
                    val relation = Workgrouppersonnel()
                    relation.id = uuid()
                    relation.workgroupId = entity.id
                    relation.personnelId = personnel.id
                    workgrouppersonnelMapper.insert(relation)

                    // 为创建的人员设置与工作组相同的权限
                    if (entity.permissionList.isNotEmpty()) {
                        for (permission in entity.permissionList) {
                            val personnelPermission = Personnelpermission()
                            personnelPermission.id = uuid()
                            personnelPermission.personnelId = personnel.id
                            personnelPermission.permissionId = permission.id
                            personnelpermissionMapper.insert(personnelPermission)
                        }
                    }

                    log.info("Created new default account for workgroup ${entity.name}: ${personnel.username}")
                }
            } catch (e: Exception) {
                // 记录创建/更新账号失败的日志，如果是业务异常则重新抛出
                if (e is BusinessException) {
                    throw e
                } else {
                    log.warn("Create or update workgroup default account failed: ${e.message}")
                }
            }
        } else {
            // 用户名为空时，禁用对应的默认账号
            try {
                val personnelSearch = PersonnelSearch()
                personnelSearch.ownerWorkgroupId = entity.id
                personnelSearch.ifPage = false
                val existingDefaultPersonnelList = personnelService.getEntityList(personnelSearch)
                val existingDefaultPersonnel = existingDefaultPersonnelList.firstOrNull()

                if (existingDefaultPersonnel != null) {
                    // 将默认账号设为禁用状态
                    existingDefaultPersonnel.ifEnabled = false
                    personnelService.saveEntity(existingDefaultPersonnel)
                    log.info("Disabled default account for workgroup ${entity.name}: ${existingDefaultPersonnel.username}")
                }
            } catch (e: Exception) {
                log.warn("Disable workgroup default account failed: ${e.message}")
            }
        }

        return id
    }

    /**
     * 重写delete方法，删除工作组时同时删除相关的工作组人员关系和权限关系
     */
    @Transactional
    override fun deleteEntity(id: String): Int {
        // 查找并删除该工作组的默认账号人员
        val personnelSearch = PersonnelSearch()
        personnelSearch.ownerWorkgroupId = id
        personnelSearch.ifPage = false
        val defaultPersonnelList = personnelService.getEntityList(personnelSearch)

        defaultPersonnelList.forEach { personnel ->
            log.info("Deleting default account for workgroup: ${personnel.username}")
            personnelService.deleteEntity(personnel.id)
        }

        // 删除工作组人员关联
        workgrouppersonnelMapper.deleteByWorkgroupId(id)

        // 删除工作组权限关联
        workgrouppermissionMapper.deleteByWorkgroupId(id)

        // 再删除工作组
        return super.deleteEntity(id)
    }
}

/**
 * 工作组Controller类
 */
@RestController
@RequestMapping("/api/Workgroup")
class WorkgroupResource(service: WorkgroupService) : BaseResource<WorkgroupSearch, Workgroup, WorkgroupMapper, WorkgroupService>(service)
