package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 工作组实体类
 */
class Workgroup : BaseModel() {
    // 数据库表字段

    /** 工作组名称 */
    var name: String = ""
    /** 工作组描述 */
    var description: String = ""
    /** 负责人ID */
    var leaderId: String = ""
    /** 状态(0-禁用,1-启用) */
    var status: Int = 1
    /** 排序号 */
    var sortOrder: Int = 0

    // 关联字段

    /** 负责人信息 */
    var leader: Personnel? = null
    /** 工作组成员列表 */
    var personnelList: List<Personnel> = listOf()
}

/**
 * 工作组查询条件类
 */
class WorkgroupSearch : BaseSearch() {
    /** 关键词(工作组名称) */
    var keyword: String = ""
    /** 工作组名称模糊匹配 */
    var nameLike: String = ""
    /** 负责人ID */
    var leaderId: String = ""
    /** 状态(0-禁用,1-启用) */
    var status: Int? = null
    /** 是否加载负责人信息 */
    var loadLeader: Boolean = false
    /** 人员ID(查询指定人员所属的工作组) */
    var personnelId: String = ""
    /** 是否加载成员列表 */
    var loadPersonnelList: Boolean = false
}

/**
 * 工作组Mapper接口
 */
@Mapper
interface WorkgroupMapper : BaseMapper<Workgroup> {

    @Select("""
        <script>
            SELECT w.* FROM tbl_workgroup w
            <if test="personnelId != ''">
                INNER JOIN tbl_workgrouppersonnel wp ON w.id = wp.workgroupId
            </if>
            <where>
                w.sysDeleted = 0
                <if test="keyword != ''">
                    AND w.name LIKE CONCAT('%', #{keyword}, '%')
                </if>
                <if test="nameLike != ''">
                    AND w.name LIKE CONCAT('%', #{nameLike}, '%')
                </if>
                <if test="leaderId != ''">
                    AND w.leaderId = #{leaderId}
                </if>
                <if test="status != null">
                    AND w.status = #{status}
                </if>
                <if test="personnelId != ''">
                    AND wp.personnelId = #{personnelId}
                </if>
            </where>
            ORDER BY w.sortOrder, w.name
        </script>
    """)
    override fun getList(search: BaseSearch): List<Workgroup>

    /**
     * 重写getInfo方法
     */
    @Select("""
        SELECT * FROM tbl_workgroup
        WHERE id = #{id} AND sysDeleted = 0
    """)
    override fun getInfo(id: String): Workgroup?

    /**
     * 获取最大排序号
     */
    @Select("""
        SELECT COALESCE(MAX(sortOrder), 0) FROM tbl_workgroup
        WHERE sysDeleted = 0
    """)
    fun getMaxSortOrder(): Int
}

/**
 * 工作组Service类
 */
@Service
class WorkgroupService(
    mapper: WorkgroupMapper,
    private val personnelMapper: PersonnelMapper,
    private val workgrouppersonnelMapper: WorkgrouppersonnelMapper
) : BaseService<Workgroup, WorkgroupMapper>(mapper) {

    /**
     * 重写getList方法，根据需要加载关联信息
     */
    override fun getList(page: BaseSearch): Result {
        val result = super.getList(page)
        if (page is WorkgroupSearch) {
            // 获取工作组列表
            val workgroupList = result.toList<Workgroup>()

            // 加载关联信息
            workgroupList.forEach { workgroup ->
                if (page.loadLeader) {
                    loadLeader(workgroup)
                }
                if (page.loadPersonnelList) {
                    loadPersonnelList(workgroup)
                }
            }
        }
        return result
    }

    /**
     * 重写getInfo方法，加载工作组的关联信息
     */
    override fun getInfo(id: String): Result {
        val workgroup = mapper.getInfo(id)
        if (workgroup != null) {
            // 加载负责人信息
            loadLeader(workgroup)
            // 加载成员列表
            loadPersonnelList(workgroup)
        }
        return Result.getSuccess(workgroup)
    }

    /**
     * 加载工作组负责人信息
     */
    private fun loadLeader(workgroup: Workgroup) {
        if (workgroup.leaderId.isNotEmpty()) {
            workgroup.leader = personnelMapper.getInfo(workgroup.leaderId)
        }
    }

    /**
     * 加载工作组成员列表
     */
    private fun loadPersonnelList(workgroup: Workgroup) {
        // 通过关系表查询工作组成员
        val search = WorkgrouppersonnelSearch()
        search.workgroupId = workgroup.id
        search.ifPage = false
        val relations = workgrouppersonnelMapper.getList(search)

        // 获取人员信息
        workgroup.personnelList = relations.mapNotNull { relation ->
            personnelMapper.getInfo(relation.personnelId)
        }
    }

    /**
     * 重写save方法，设置sortOrder默认排在最后，并处理工作组人员关联
     */
    @Transactional
    override fun save(model: Workgroup): Result {
        // 如果是新增且sortOrder为0，设置为最大值+1
        if (model.id.isEmpty() && model.sortOrder == 0) {
            val maxSortOrder = mapper.getMaxSortOrder()
            model.sortOrder = maxSortOrder + 1
        }

        // 保存工作组基本信息
        val result = super.save(model)

        // 如果保存成功，处理工作组人员关联
        if (result.rlt == Result.SUCCESS) {
            // 删除原有的工作组人员关联
            workgrouppersonnelMapper.deleteByWorkgroupId(model.id)

            // 添加新的工作组人员关联
            if (model.personnelList.isNotEmpty()) {
                for (personnel in model.personnelList) {
                    val relation = Workgrouppersonnel()
                    relation.id = uuid()
                    relation.workgroupId = model.id
                    relation.personnelId = personnel.id
                    workgrouppersonnelMapper.insert(relation)
                }
            }
        }

        return result
    }

    /**
     * 重写delete方法，删除工作组时同时删除相关的工作组人员关系
     */
    @Transactional
    override fun delete(id: String): Result {
        // 先删除工作组人员关联
        workgrouppersonnelMapper.deleteByWorkgroupId(id)

        // 再删除工作组
        return super.delete(id)
    }
}

/**
 * 工作组Controller类
 */
@RestController
@RequestMapping("/api/Workgroup")
class WorkgroupResource(service: WorkgroupService) : BaseResource<WorkgroupSearch, Workgroup, WorkgroupMapper, WorkgroupService>(service)
