package com.shenlan.smartlogixmini.auto

import com.nlf.calendar.Solar
import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.*
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.io.RandomAccessFile
import java.nio.file.Files
import java.nio.file.Paths
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*
import javax.servlet.http.HttpServletRequest
import com.shenlan.smartlogixmini.util.MediaType as WechatMediaType


class Content : BaseEntity() {
    // 数据库表字段

    /** 关联的会议ID */
    var meetingId: String = ""
    /** 关联的报修记录ID */
    var repairrecordId: String = ""
    /** 关联的站点ID */
    var siteId: String = ""
    /** 关联的巡检打卡记录ID */
    var inspectionrecordId: String = ""
    /** 文件原始名称（不含扩展名） */
    var originalName: String = ""
    /** 文件在服务器上的存储名称（包含扩展名） */
    var storeName: String = ""
    /** 文件存储目录（按日期归档） */
    var storeDir: String = ""
    /** 文件类型（扩展名） */
    var fileType: String = ""
    /** 文件大小（字节） */
    var fileSize: Int? = null
    /** 视频时长（秒） */
    var duration: Double? = null
}

class ContentSearch : BaseSearch() {
    /** 关联的会议ID */
    var meetingId: String = ""
    /** 关联的报修记录ID */
    var repairrecordId: String = ""
    /** 关联的站点ID */
    var siteId: String = ""
    /** 关联的巡检打卡记录ID */
    var inspectionrecordId: String = ""
    /** 文件名关键词 */
    var keyword: String = ""
    /** 文件类型 */
    var fileType: String = ""
}

@Mapper
interface ContentMapper : BaseMapper<Content> {
    @Select("""
        <script>
            SELECT 
            <choose>
                <when test="onlyId">
                    id
                </when>
                <otherwise>
                    *
                </otherwise>
            </choose>
            FROM tbl_content
            <where>
                sysDeleted = 0
                <if test="meetingId != ''">
                    AND meetingId = #{meetingId}
                </if>
                <if test="repairrecordId != ''">
                    AND repairrecordId = #{repairrecordId}
                </if>
                <if test="siteId != ''">
                    AND siteId = #{siteId}
                </if>
                <if test="inspectionrecordId != ''">
                    AND inspectionrecordId = #{inspectionrecordId}
                </if>
                <if test="keyword != ''">
                    AND originalName LIKE CONCAT('%', #{keyword}, '%')
                </if>
                <if test="fileType != ''">
                    AND fileType = #{fileType}
                </if>
            </where>
            ORDER BY sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Content>

    @Select("""
        <script>
            SELECT * FROM tbl_content
            <where>
                sysDeleted = 0
                AND repairrecordId = #{id}
            </where>
            ORDER BY sysCreated DESC
        </script>
    """)
    fun getListByrepairrecordId(id: String): List<Content>

    @Select("""
        <script>
            SELECT * FROM tbl_content
            <where>
                sysDeleted = 0
                AND siteId = #{id}
            </where>
            ORDER BY sysCreated DESC
            LIMIT 1
        </script>
    """)
    fun getContentBySiteId(id: String): Content


    @Select("""
        <script>
            SELECT * FROM tbl_content
            <where>
                sysDeleted = 0
                AND inspectionrecordId = #{id}
            </where>
            ORDER BY sysCreated DESC
        </script>
    """)
    fun getListByInspectionrecordId(id: String): List<Content>

}

/** 文件下载数据 */
data class FileDownloadData(
    val data: ByteArray,
    val fileName: String,
    val mediaType: MediaType
)

/** Range请求文件数据 */
data class RangeFileData(
    val data: ByteArray,
    val fileName: String,
    val mediaType: MediaType,
    val fileLength: Long,
    val start: Long,
    val end: Long,
    val contentLength: Long,
    val isPartialContent: Boolean
)

@Service
class ContentService(
    mapper: ContentMapper,
    private val appPro: AppPro,
    private val wechatMediaSecCheckUtil: WechatMediaSecCheckUtil
) : BaseService<Content, ContentMapper, ContentSearch>(mapper) {

    @Value("\${application.host:}")
    private lateinit var applicationHost: String

    @Value("\${application.content-security-check-enabled:false}")
    private var contentSecurityCheckEnabled: Boolean = false

    /** 单文件上传 */
    fun uploadFile(file: MultipartFile): Content {
        val fileName = file.originalFilename!!
        val fileType = fileName.substring(fileName.lastIndexOf("."))
        val fileSize = file.size.toInt()

        val content = Content()
        content.originalName = fileName.replace(fileType, "")
        content.storeName = System.currentTimeMillis().toString() + fileType
        content.storeDir = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)
        content.fileType = fileType
        content.fileSize = fileSize

        // 创建目录并保存文件
        val dir = Paths.get(appPro.uploadPath, content.storeDir).toFile()
        if (!dir.exists()) dir.mkdirs()
        File(dir, content.storeName).writeBytes(file.bytes)

        log.info("upload file: {}", content.toJsonString)
        return content
    }

    /** 批量文件上传 */
    fun uploadFiles(fileList: List<MultipartFile>): List<Content> {
        return fileList.map { uploadFile(it) }
    }

    /** 重命名文件 */
    fun renameFile(id: String, newName: String): Content? {
        val content = getEntity(id) ?: return null
        content.originalName = newName
        super.saveEntity(content)
        return content
    }

    /** 获取文件下载数据 */
    fun getFileForDownload(id: String): FileDownloadData? {
        val content = getEntity(id) ?: return null
        val file = Paths.get(appPro.uploadPath, content.storeDir, content.storeName).toFile()

        if (!file.exists()) {
            return null
        }

        val data = Files.readAllBytes(file.toPath())
        val fileName = content.originalName + content.fileType

        return FileDownloadData(data, fileName, MediaType.APPLICATION_OCTET_STREAM)
    }

    /** 根据文件扩展名获取MediaType */
    private fun getMediaTypeByFileExtension(fileType: String): MediaType {
        return when (fileType.toLowerCase()) {
            ".pdf" -> MediaType.APPLICATION_PDF
            ".jpg", ".jpeg" -> MediaType.IMAGE_JPEG
            ".png" -> MediaType.IMAGE_PNG
            ".gif" -> MediaType.IMAGE_GIF
            ".svg" -> MediaType.parseMediaType("image/svg+xml")
            ".txt" -> MediaType.TEXT_PLAIN
            ".html", ".htm" -> MediaType.TEXT_HTML
            ".xml" -> MediaType.APPLICATION_XML
            ".json" -> MediaType.APPLICATION_JSON
            ".doc", ".docx" -> MediaType.parseMediaType("application/msword")
            ".xls", ".xlsx" -> MediaType.parseMediaType("application/vnd.ms-excel")
            ".ppt", ".pptx" -> MediaType.parseMediaType("application/vnd.ms-powerpoint")
            ".mp4" -> MediaType.parseMediaType("video/mp4")
            ".avi" -> MediaType.parseMediaType("video/avi")
            ".mov" -> MediaType.parseMediaType("video/quicktime")
            ".wmv" -> MediaType.parseMediaType("video/x-ms-wmv")
            ".flv" -> MediaType.parseMediaType("video/x-flv")
            ".mkv" -> MediaType.parseMediaType("video/x-matroska")
            ".webm" -> MediaType.parseMediaType("video/webm")
            ".m4v" -> MediaType.parseMediaType("video/x-m4v")
            else -> MediaType.APPLICATION_OCTET_STREAM
        }
    }

    /** 获取文件预览数据 */
    fun getFileForPreview(id: String): FileDownloadData? {
        val content = getEntity(id) ?: return null
        val file = Paths.get(appPro.uploadPath, content.storeDir, content.storeName).toFile()

        if (!file.exists()) {
            return null
        }

        val data = Files.readAllBytes(file.toPath())
        val fileName = content.originalName + content.fileType
        val mediaType = getMediaTypeByFileExtension(content.fileType)

        log.info("preview file: {} with type: {}", fileName, mediaType)
        return FileDownloadData(data, fileName, mediaType)
    }

    /** 获取文件Range数据（支持视频播放进度条） */
    fun getFileForRange(id: String, rangeHeader: String?): RangeFileData? {
        val content = getEntity(id) ?: return null
        val file = Paths.get(appPro.uploadPath, content.storeDir, content.storeName).toFile()

        if (!file.exists()) {
            return null
        }

        val fileLength = file.length()
        val fileName = content.originalName + content.fileType
        val mediaType = getMediaTypeByFileExtension(content.fileType)

        // 解析Range头
        val ranges = parseRangeHeader(rangeHeader, fileLength)

        if (ranges.isEmpty()) {
            // 没有Range头，返回完整文件
            val data = Files.readAllBytes(file.toPath())
            return RangeFileData(data, fileName, mediaType, fileLength, 0L, fileLength - 1, fileLength, false)
        }

        // 处理第一个范围（简化处理，只支持单个范围）
        val range = ranges[0]
        val start = range.first
        val end = range.second
        val contentLength = end - start + 1

        // 读取指定范围的数据
        val data = ByteArray(contentLength.toInt())
        RandomAccessFile(file, "r").use { randomAccessFile ->
            randomAccessFile.seek(start)
            randomAccessFile.readFully(data)
        }

        log.info("preview file range: {} range: {}-{}/{}", fileName, start, end, fileLength)
        return RangeFileData(data, fileName, mediaType, fileLength, start, end, contentLength, true)
    }

    /** 解析Range头 */
    private fun parseRangeHeader(rangeHeader: String?, fileLength: Long): List<Pair<Long, Long>> {
        @Suppress("UNNECESSARY_NOT_NULL_ASSERTION")
        if (rangeHeader.isNullOrEmpty() || !rangeHeader!!.startsWith("bytes=")) {
            return emptyList()
        }

        val ranges = mutableListOf<Pair<Long, Long>>()
        val rangeSpecs = rangeHeader.substring(6).split(",")

        for (rangeSpec in rangeSpecs) {
            val range = rangeSpec.trim()
            if (range.contains("-")) {
                val parts = range.split("-", limit = 2)
                val start = if (parts[0].isEmpty()) 0L else parts[0].toLongOrNull() ?: 0L
                val end = if (parts[1].isEmpty()) fileLength - 1 else parts[1].toLongOrNull() ?: (fileLength - 1)

                // 确保范围有效
                val validStart = maxOf(0L, start)
                val validEnd = minOf(fileLength - 1, end)

                if (validStart <= validEnd) {
                    ranges.add(Pair(validStart, validEnd))
                }
            }
        }

        return ranges
    }

    /** 清理不在数据库中的本地文件 */
    fun cleanOrphanedFiles() {
        log.info("Starting cleanup of orphaned files")

        try {
            // 获取数据库中所有文件信息
            val search = BaseSearch()
            search.ifPage = false
            val dbFileInfos = mapper.getList(search)
            val dbFilePaths = dbFileInfos.map {
                Paths.get(appPro.uploadPath, it.storeDir, it.storeName).toString()
            }.toSet()

            log.info("Found {} files in database", dbFilePaths.size)

            // 扫描上传目录
            val uploadDir = Paths.get(appPro.uploadPath).toFile()
            if (!uploadDir.exists()) {
                log.warn("Upload directory does not exist: {}", appPro.uploadPath)
                return
            }

            var totalFiles = 0
            var deletedFiles = 0

            // 递归扫描所有文件
            uploadDir.walkTopDown().filter { it.isFile }.forEach { file ->
                totalFiles++
                val filePath = file.absolutePath

                // 检查文件是否在数据库中
                if (!dbFilePaths.contains(filePath)) {
                    try {
                        if (file.delete()) {
                            deletedFiles++
                            log.info("Deleted orphaned file: {}", filePath)
                        } else {
                            log.warn("Failed to delete file: {}", filePath)
                        }
                    } catch (e: Exception) {
                        log.error("Error deleting file: {}", filePath, e)
                    }
                }
            }

            // 清理空目录
            cleanEmptyDirectories(uploadDir)

            log.info("Cleanup completed. Total files: {}, Deleted files: {}", totalFiles, deletedFiles)

        } catch (e: Exception) {
            log.error("Error during file cleanup", e)
            throw e
        }
    }

    /** 清理空目录 */
    private fun cleanEmptyDirectories(dir: File) {
        dir.walkBottomUp()
            .filter { it.isDirectory && it != dir }
            .forEach { directory ->
                try {
                    if (directory.listFiles()?.isEmpty() == true) {
                        if (directory.delete()) {
                            log.info("Deleted empty directory: {}", directory.absolutePath)
                        }
                    }
                } catch (e: Exception) {
                    log.warn("Failed to delete empty directory: {}", directory.absolutePath, e)
                }
            }
    }

    @Transactional
    override fun deleteEntity(id: String): Int {
        // 先获取要删除的Content实体
        val content = getEntity(id)
        if (content == null) {
            log.warn("Content not found for deletion: {}", id)
            return 0
        }

        // 构建文件路径
        val file = Paths.get(appPro.uploadPath, content.storeDir, content.storeName).toFile()

        // 调用父类方法删除数据库记录
        val deleteCount = super.deleteEntity(id)

        // 如果数据库删除成功，则删除本地文件
        if (deleteCount > 0 && file.exists()) {
            try {
                if (file.delete()) {
                    log.info("Deleted file: {}", file.absolutePath)

                    // 尝试删除空目录
                    val parentDir = file.parentFile
                    if (parentDir.exists() && parentDir.listFiles()?.isEmpty() == true) {
                        if (parentDir.delete()) {
                            log.info("Deleted empty directory: {}", parentDir.absolutePath)
                        }
                    }
                } else {
                    log.warn("Failed to delete file: {}", file.absolutePath)
                }
            } catch (e: Exception) {
                log.error("Error deleting file: {}", file.absolutePath, e)
                // 不抛出异常，因为数据库记录已经删除
            }
        }

        return deleteCount
    }

    /** 根据站点ID删除所有相关的Content实体 */
    @Transactional
    fun deleteEntityListBySiteId(siteId: String): Int {
        // 创建查询条件
        val search = ContentSearch()
        search.siteId = siteId
        search.ifPage = false  // 不分页，查询全部数据
        search.onlyId = true  // 只查询ID字段，提高性能

        // 获取所有相关的Content实体
        val contentList = getEntityList(search)

        // 删除所有找到的实体
        var totalDeleted = 0
        for (content in contentList) {
            totalDeleted += deleteEntity(content.id)
        }

        log.info("Deleted {} content entities for siteId: {}", totalDeleted, siteId)
        return totalDeleted
    }

    /** 获取当前节气对应的图片 */
    fun getCurrentSolarTermImage(): FileDownloadData? {
        try {
            // 获取当前日期
            val now = LocalDate.now()
            val solar = Solar.fromDate(Date.from(now.atStartOfDay(ZoneId.systemDefault()).toInstant()))
            val lunar = solar.lunar

            // 获取当前节气
            val currentJieQi = lunar.currentJieQi?.name ?: lunar.prevJieQi.name
            log.info("Current solar term: {}", currentJieQi)

            // 节气名称与文件序号的映射
            val solarTermMap = mapOf(
                "立春" to 1, "雨水" to 2, "惊蛰" to 3, "春分" to 4, "清明" to 5, "谷雨" to 6,
                "立夏" to 7, "小满" to 8, "芒种" to 9, "夏至" to 10, "小暑" to 11, "大暑" to 12,
                "立秋" to 13, "处暑" to 14, "白露" to 15, "秋分" to 16, "寒露" to 17, "霜降" to 18,
                "立冬" to 19, "小雪" to 20, "大雪" to 21, "冬至" to 22, "小寒" to 23, "大寒" to 24
            )

            // 获取节气对应的序号
            val solarTermNumber = solarTermMap[currentJieQi]
            if (solarTermNumber == null) {
                log.warn("Unknown solar term: {}", currentJieQi)
                return null
            }

            // 构建文件名
            val fileName = "${solarTermNumber}${currentJieQi}banner.jpg"

            // 从 resources 目录读取文件
            val inputStream = javaClass.getResourceAsStream("/solar-term/$fileName")
            if (inputStream == null) {
                log.warn("Solar term image not found: {}", fileName)
                return null
            }

            val data = inputStream.readBytes()
            val mediaType = MediaType.IMAGE_PNG

            log.info("Found solar term image: {} for term: {}", fileName, currentJieQi)
            return FileDownloadData(data, fileName, mediaType)

        } catch (e: Exception) {
            log.error("Error getting current solar term image", e)
            return null
        }
    }

    /**
     * 保存Content实体，并对图片进行异步安全检测
     * 如果安全检测不通过，会自动删除Content
     */
    @Transactional
    override fun saveEntity(entity: Content): String {
        // 调用父类方法保存实体
        val savedEntityId = super.saveEntity(entity)

        // 异步进行安全检测（不阻塞保存流程）
        try {
            performSecurityCheckAsync(entity)
        } catch (e: Exception) {
            log.error("Error initiating security check for content: {}", savedEntityId, e)
            // 安全检测启动失败不影响保存流程
        }

        return savedEntityId
    }

    /** 执行异步安全检测 */
    private fun performSecurityCheckAsync(content: Content) {
        // 检查是否启用内容安全检测
        if (!contentSecurityCheckEnabled) {
            log.debug("Content security check is disabled, skipping for content: {}", content.id)
            return
        }

        // 判断是否为图片文件
        if (!isImageFile(content.fileType)) {
            log.debug("Skip security check for non-image file: {}", content.id)
            return
        }

        // 获取当前用户的openid
        val currentUser = getUser()
        if (currentUser?.wechatOpenId.isNullOrBlank()) {
            log.warn("Skip security check due to missing user openid for content: {}", content.id)
            return
        }

        @Suppress("UNNECESSARY_NOT_NULL_ASSERTION")
        val openid = currentUser!!.wechatOpenId!!

        // 构建图片URL
        val imageUrl = buildImagePreviewUrl(content.id)

        log.info("Starting security check for image content: {}, url: {}, openid: {}", content.id, imageUrl, openid)

        // 调用异步安全检测
        try {
            wechatMediaSecCheckUtil.checkMediaAsync(
                mediaUrl = imageUrl,
                mediaType = WechatMediaType.IMAGE,
                scene = Scene.COMMENT,
                openid = openid
            ) { result ->
                handleSecurityCheckResult(content.id, result)
            }
        } catch (e: Exception) {
            log.error("Failed to start security check for content: {}", content.id, e)
        }
    }

    /** 判断是否为图片文件 */
    private fun isImageFile(fileType: String): Boolean {
        val imageExtensions = setOf(".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg")
        return imageExtensions.contains(fileType.toLowerCase())
    }

    /** 构建图片预览URL */
    private fun buildImagePreviewUrl(contentId: String): String {
        return "https://$applicationHost/api/Content/preview/$contentId"
    }

    /** 处理安全检测结果 */
    @Transactional
    fun handleSecurityCheckResult(contentId: String, result: MediaCheckAsyncResult) {
        try {
            log.info("Received security check result for content: {}, suggest: {}, label: {}",
                contentId, result.result?.suggest, result.result?.label)

            // 检测结果判断
            val suggest = result.result?.suggest
            val isRisky = suggest == Suggest.RISKY
            val needsReview = suggest == Suggest.REVIEW

            if (isRisky) {
                // 检测为风险内容，删除Content
                log.warn("Content failed security check (risky), deleting. contentId={}", contentId)
                val deleteCount = deleteEntity(contentId)
                if (deleteCount > 0) {
                    log.info("Successfully deleted risky content: {}", contentId)
                } else {
                    log.warn("Failed to delete risky content: {}", contentId)
                }
            } else if (needsReview) {
                // 需要人工审核，记录日志但不删除
                log.warn("Content requires manual review. contentId={}", contentId)
            } else {
                // 检测通过
                log.info("Content passed security check. contentId={}", contentId)
            }

        } catch (e: Exception) {
            log.error("Error handling security check result for content: {}", contentId, e)
        }
    }
}

@RestController
@RequestMapping("/api/Content")
class ContentResource(service: ContentService) : BaseResource<ContentSearch, Content, ContentMapper, ContentService>(service) {
    @PostMapping("/upload")
    fun upload(@RequestParam("file") file: MultipartFile): Result {
        val content = service.uploadFile(file)
        return Result.getSuccess(content)
    }

    @PostMapping("/uploadBatch")
    fun uploadBatch(@RequestParam("fileList") fileList: List<MultipartFile>): Result {
        val contentList = service.uploadFiles(fileList)
        return Result.getSuccess(contentList)
    }

    @PostMapping("/saveBatch")
    fun saveBatch(@RequestBody contentList: List<Content>): Result {
        val savedContentList = contentList.map { service.saveEntity(it) }
        return Result.getSuccess(savedContentList)
    }

    @PostMapping("/rename/{id}")
    fun rename(@PathVariable id: String, @RequestParam("newName") newName: String): Result {
        val content = service.renameFile(id, newName)
            ?: return Result.getError("附件不存在")
        return Result.getSuccess(content)
    }

    @GetMapping("/download/{id}")
    fun download(@PathVariable id: String): ResponseEntity<ByteArray> {
        val fileData = service.getFileForDownload(id)
            ?: return ResponseEntity.notFound().build()
        return ResponseEntity.ok()
            .contentType(fileData.mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"${fileData.fileName}\"")
            .body(fileData.data)
    }

    @GetMapping("/preview/{id}")
    fun preview(@PathVariable id: String, request: HttpServletRequest): ResponseEntity<ByteArray> {
        val rangeHeader = request.getHeader("Range")
        val rangeData = service.getFileForRange(id, rangeHeader)
            ?: return ResponseEntity.notFound().build()

        val responseBuilder = if (rangeData.isPartialContent) {
            ResponseEntity.status(HttpStatus.PARTIAL_CONTENT)
                .header(HttpHeaders.CONTENT_RANGE, "bytes ${rangeData.start}-${rangeData.end}/${rangeData.fileLength}")
        } else {
            ResponseEntity.ok()
        }

        return responseBuilder
            .contentType(rangeData.mediaType)
            .header(HttpHeaders.ACCEPT_RANGES, "bytes")
            .header(HttpHeaders.CONTENT_LENGTH, rangeData.contentLength.toString())
            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"${rangeData.fileName}\"")
            .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
            .body(rangeData.data)
    }

    @GetMapping("/getCurrentSolarTermImage")
    fun getCurrentSolarTermImage(): ResponseEntity<ByteArray> {
        val fileData = service.getCurrentSolarTermImage()
            ?: return ResponseEntity.notFound().build()

        return ResponseEntity.ok()
            .contentType(fileData.mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"${fileData.fileName}\"")
            .header(HttpHeaders.CACHE_CONTROL, "public, max-age=3600")
            .body(fileData.data)
    }
}

@Component
class ContentScheduler(
    private val contentService: ContentService
) {
    /** 每日凌晨2点清理孤立文件 */
    @Scheduled(cron = "0 0 2 * * ?")
    fun cleanOrphanedFiles() {
        log.info("Starting scheduled cleanup of orphaned files")
        try {
            contentService.cleanOrphanedFiles()
        } catch (e: Exception) {
            log.error("Scheduled cleanup failed", e)
        }
    }
}
