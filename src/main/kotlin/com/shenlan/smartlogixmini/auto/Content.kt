package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.io.RandomAccessFile
import java.nio.file.Files
import java.nio.file.Paths
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import javax.servlet.http.HttpServletRequest


class Content : BaseModel() {
    /** 关联的会议ID */
    var meetingId: String = ""
    /** 文件原始名称（不含扩展名） */
    var fullName: String = ""
    /** 文件在服务器上的存储名称（包含扩展名） */
    var storeName: String = ""
    /** 文件存储的相对路径（按日期归档） */
    var filePath: String = ""
    /** 文件类型（扩展名） */
    var fileType: String = ""
    /** 文件大小（字节） */
    var fileSize: Int? = null

    /** 关联的报修记录ID */
    var repairrecordId: String = ""
    /** 关联的站点ID */
    var siteId: String = ""
}

class ContentSearch : BaseSearch() {
    /** 关联的会议ID */
    var meetingId: String = ""
    /** 文件名关键词 */
    var keyword: String = ""
    /** 文件类型 */
    var fileType: String = ""
}

@Mapper
interface ContentMapper : BaseMapper<Content> {
    @Select("""
        <script>
            SELECT * FROM tbl_content
            <where>
                sysDeleted = 0
                <if test="meetingId != ''">
                    AND meetingId = #{meetingId}
                </if>
                <if test="keyword != ''">
                    AND fullName LIKE CONCAT('%', #{keyword}, '%')
                </if>
                <if test="fileType != ''">
                    AND fileType = #{fileType}
                </if>
            </where>
            ORDER BY sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Content>

    @Select("""
        <script>
            SELECT * FROM tbl_content
            <where>
                sysDeleted = 0
                AND repairrecordId = #{id}
            </where>
            ORDER BY sysCreated DESC
        </script>
    """)
    fun getListByrepairrecordId(id: String): List<Content>

    @Select("SELECT filePath, storeName FROM tbl_content WHERE sysDeleted = 0")
    fun getAllFileInfo(): List<Map<String, String>>

    @Select("""
        <script>
            SELECT * FROM tbl_content
            <where>
                sysDeleted = 0
                AND siteId = #{id}
            </where>
            ORDER BY sysCreated DESC
            LIMIT 1
        </script>
    """)
    fun getContentBySiteId(id: String): Content

    @Delete(""" DELETE from tbl_content where siteId = #{id} """)
    fun deleteBySiteId(id: String) : Int
}

/** 文件下载数据 */
data class FileDownloadData(
    val data: ByteArray,
    val fileName: String,
    val mediaType: MediaType
)

/** Range请求文件数据 */
data class RangeFileData(
    val data: ByteArray,
    val fileName: String,
    val mediaType: MediaType,
    val fileLength: Long,
    val start: Long,
    val end: Long,
    val contentLength: Long,
    val isPartialContent: Boolean
)

@Service
class ContentService(
    mapper: ContentMapper,
    private val appPro: AppPro
) : BaseService<Content, ContentMapper>(mapper) {

    /** 单文件上传 */
    fun uploadFile(file: MultipartFile): Content {
        val fileName = file.originalFilename!!
        val fileType = fileName.substring(fileName.lastIndexOf("."))
        val fileSize = file.size.toInt()

        val content = Content()
        content.fullName = fileName.replace(fileType, "")
        content.storeName = System.currentTimeMillis().toString() + fileType
        content.filePath = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)
        content.fileType = fileType
        content.fileSize = fileSize

        // 创建目录并保存文件
        val dir = Paths.get(appPro.uploadPath, content.filePath).toFile()
        if (!dir.exists()) dir.mkdirs()
        File(dir, content.storeName).writeBytes(file.bytes)

        log.info("upload file: {}", content.toJsonString)
        return content
    }

    /** 批量文件上传 */
    fun uploadFiles(fileList: List<MultipartFile>): List<Content> {
        return fileList.map { uploadFile(it) }
    }

    /** 重命名文件 */
    fun renameFile(id: String, newName: String): Content? {
        val content = getEntity(id) ?: return null
        content.fullName = newName
        super.saveEntity(content)
        return content
    }

    /** 获取文件下载数据 */
    fun getFileForDownload(id: String): FileDownloadData? {
        val content = getEntity(id) ?: return null
        val file = Paths.get(appPro.uploadPath, content.filePath, content.storeName).toFile()

        if (!file.exists()) {
            return null
        }

        val data = Files.readAllBytes(file.toPath())
        val fileName = content.fullName + content.fileType

        return FileDownloadData(data, fileName, MediaType.APPLICATION_OCTET_STREAM)
    }

    /** 获取文件预览数据 */
    fun getFileForPreview(id: String): FileDownloadData? {
        val content = getEntity(id) ?: return null
        val file = Paths.get(appPro.uploadPath, content.filePath, content.storeName).toFile()

        if (!file.exists()) {
            return null
        }

        val data = Files.readAllBytes(file.toPath())
        val fileName = content.fullName + content.fileType

        // 根据文件类型确定适当的MediaType
        val mediaType = when (content.fileType.toLowerCase()) {
            ".pdf" -> MediaType.APPLICATION_PDF
            ".jpg", ".jpeg" -> MediaType.IMAGE_JPEG
            ".png" -> MediaType.IMAGE_PNG
            ".gif" -> MediaType.IMAGE_GIF
            ".txt" -> MediaType.TEXT_PLAIN
            ".html", ".htm" -> MediaType.TEXT_HTML
            ".xml" -> MediaType.APPLICATION_XML
            ".json" -> MediaType.APPLICATION_JSON
            ".doc", ".docx" -> MediaType.parseMediaType("application/msword")
            ".xls", ".xlsx" -> MediaType.parseMediaType("application/vnd.ms-excel")
            ".ppt", ".pptx" -> MediaType.parseMediaType("application/vnd.ms-powerpoint")
            ".mp4" -> MediaType.parseMediaType("video/mp4")
            ".avi" -> MediaType.parseMediaType("video/avi")
            ".mov" -> MediaType.parseMediaType("video/quicktime")
            ".wmv" -> MediaType.parseMediaType("video/x-ms-wmv")
            ".flv" -> MediaType.parseMediaType("video/x-flv")
            ".mkv" -> MediaType.parseMediaType("video/x-matroska")
            ".webm" -> MediaType.parseMediaType("video/webm")
            ".m4v" -> MediaType.parseMediaType("video/x-m4v")
            else -> MediaType.APPLICATION_OCTET_STREAM
        }

        log.info("preview file: {} with type: {}", fileName, mediaType)
        return FileDownloadData(data, fileName, mediaType)
    }

    /** 获取文件Range数据（支持视频播放进度条） */
    fun getFileForRange(id: String, rangeHeader: String?): RangeFileData? {
        val content = getEntity(id) ?: return null
        val file = Paths.get(appPro.uploadPath, content.filePath, content.storeName).toFile()

        if (!file.exists()) {
            return null
        }

        val fileLength = file.length()
        val fileName = content.fullName + content.fileType

        // 根据文件类型确定适当的MediaType
        val mediaType = when (content.fileType.toLowerCase()) {
            ".pdf" -> MediaType.APPLICATION_PDF
            ".jpg", ".jpeg" -> MediaType.IMAGE_JPEG
            ".png" -> MediaType.IMAGE_PNG
            ".gif" -> MediaType.IMAGE_GIF
            ".txt" -> MediaType.TEXT_PLAIN
            ".html", ".htm" -> MediaType.TEXT_HTML
            ".xml" -> MediaType.APPLICATION_XML
            ".json" -> MediaType.APPLICATION_JSON
            ".doc", ".docx" -> MediaType.parseMediaType("application/msword")
            ".xls", ".xlsx" -> MediaType.parseMediaType("application/vnd.ms-excel")
            ".ppt", ".pptx" -> MediaType.parseMediaType("application/vnd.ms-powerpoint")
            ".mp4" -> MediaType.parseMediaType("video/mp4")
            ".avi" -> MediaType.parseMediaType("video/avi")
            ".mov" -> MediaType.parseMediaType("video/quicktime")
            ".wmv" -> MediaType.parseMediaType("video/x-ms-wmv")
            ".flv" -> MediaType.parseMediaType("video/x-flv")
            ".mkv" -> MediaType.parseMediaType("video/x-matroska")
            ".webm" -> MediaType.parseMediaType("video/webm")
            ".m4v" -> MediaType.parseMediaType("video/x-m4v")
            else -> MediaType.APPLICATION_OCTET_STREAM
        }

        // 解析Range头
        val ranges = parseRangeHeader(rangeHeader, fileLength)

        if (ranges.isEmpty()) {
            // 没有Range头，返回完整文件
            val data = Files.readAllBytes(file.toPath())
            return RangeFileData(data, fileName, mediaType, fileLength, 0L, fileLength - 1, fileLength, false)
        }

        // 处理第一个范围（简化处理，只支持单个范围）
        val range = ranges[0]
        val start = range.first
        val end = range.second
        val contentLength = end - start + 1

        // 读取指定范围的数据
        val data = ByteArray(contentLength.toInt())
        RandomAccessFile(file, "r").use { randomAccessFile ->
            randomAccessFile.seek(start)
            randomAccessFile.readFully(data)
        }

        log.info("preview file range: {} range: {}-{}/{}", fileName, start, end, fileLength)
        return RangeFileData(data, fileName, mediaType, fileLength, start, end, contentLength, true)
    }

    /** 解析Range头 */
    private fun parseRangeHeader(rangeHeader: String?, fileLength: Long): List<Pair<Long, Long>> {
        if (rangeHeader.isNullOrEmpty() || !rangeHeader!!.startsWith("bytes=")) {
            return emptyList()
        }

        val ranges = mutableListOf<Pair<Long, Long>>()
        val rangeSpecs = rangeHeader.substring(6).split(",")

        for (rangeSpec in rangeSpecs) {
            val range = rangeSpec.trim()
            if (range.contains("-")) {
                val parts = range.split("-", limit = 2)
                val start = if (parts[0].isEmpty()) 0L else parts[0].toLongOrNull() ?: 0L
                val end = if (parts[1].isEmpty()) fileLength - 1 else parts[1].toLongOrNull() ?: (fileLength - 1)

                // 确保范围有效
                val validStart = maxOf(0L, start)
                val validEnd = minOf(fileLength - 1, end)

                if (validStart <= validEnd) {
                    ranges.add(Pair(validStart, validEnd))
                }
            }
        }

        return ranges
    }

    /** 清理不在数据库中的本地文件 */
    fun cleanOrphanedFiles() {
        log.info("Starting cleanup of orphaned files")

        try {
            // 获取数据库中所有文件信息
            val dbFileInfos = mapper.getAllFileInfo()
            val dbFilePaths = dbFileInfos.map {
                Paths.get(appPro.uploadPath, it["filePath"] ?: "", it["storeName"] ?: "").toString()
            }.toSet()

            log.info("Found {} files in database", dbFilePaths.size)

            // 扫描上传目录
            val uploadDir = Paths.get(appPro.uploadPath).toFile()
            if (!uploadDir.exists()) {
                log.warn("Upload directory does not exist: {}", appPro.uploadPath)
                return
            }

            var totalFiles = 0
            var deletedFiles = 0

            // 递归扫描所有文件
            uploadDir.walkTopDown().filter { it.isFile }.forEach { file ->
                totalFiles++
                val filePath = file.absolutePath

                // 检查文件是否在数据库中
                if (!dbFilePaths.contains(filePath)) {
                    try {
                        if (file.delete()) {
                            deletedFiles++
                            log.info("Deleted orphaned file: {}", filePath)
                        } else {
                            log.warn("Failed to delete file: {}", filePath)
                        }
                    } catch (e: Exception) {
                        log.error("Error deleting file: {}", filePath, e)
                    }
                }
            }

            // 清理空目录
            cleanEmptyDirectories(uploadDir)

            log.info("Cleanup completed. Total files: {}, Deleted files: {}", totalFiles, deletedFiles)

        } catch (e: Exception) {
            log.error("Error during file cleanup", e)
            throw e
        }
    }

    /** 清理空目录 */
    private fun cleanEmptyDirectories(dir: File) {
        dir.walkBottomUp()
            .filter { it.isDirectory && it != dir }
            .forEach { directory ->
                try {
                    if (directory.listFiles()?.isEmpty() == true) {
                        if (directory.delete()) {
                            log.info("Deleted empty directory: {}", directory.absolutePath)
                        }
                    }
                } catch (e: Exception) {
                    log.warn("Failed to delete empty directory: {}", directory.absolutePath, e)
                }
            }
    }
}

@RestController
@RequestMapping("/api/Content")
class ContentResource(service: ContentService) : BaseResource<ContentSearch, Content, ContentMapper, ContentService>(service) {
    @PostMapping("/upload")
    fun upload(@RequestParam("file") file: MultipartFile): Result {
        val content = service.uploadFile(file)
        return Result.getSuccess(content)
    }

    @PostMapping("/uploadBatch")
    fun uploadBatch(@RequestParam("fileList") fileList: List<MultipartFile>): Result {
        val contentList = service.uploadFiles(fileList)
        return Result.getSuccess(contentList)
    }

    @PostMapping("/rename/{id}")
    fun rename(@PathVariable id: String, @RequestParam("newName") newName: String): Result {
        val content = service.renameFile(id, newName)
            ?: return Result.getError("附件不存在")
        return Result.getSuccess(content)
    }

    @GetMapping("/download/{id}")
    fun download(@PathVariable id: String): ResponseEntity<ByteArray> {
        val fileData = service.getFileForDownload(id)
            ?: return ResponseEntity.notFound().build()
        return ResponseEntity.ok()
            .contentType(fileData.mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"${fileData.fileName}\"")
            .body(fileData.data)
    }

    @GetMapping("/preview/{id}")
    fun preview(@PathVariable id: String, request: HttpServletRequest): ResponseEntity<ByteArray> {
        val rangeHeader = request.getHeader("Range")
        val rangeData = service.getFileForRange(id, rangeHeader)
            ?: return ResponseEntity.notFound().build()

        val responseBuilder = if (rangeData.isPartialContent) {
            ResponseEntity.status(HttpStatus.PARTIAL_CONTENT)
                .header(HttpHeaders.CONTENT_RANGE, "bytes ${rangeData.start}-${rangeData.end}/${rangeData.fileLength}")
        } else {
            ResponseEntity.ok()
        }

        return responseBuilder
            .contentType(rangeData.mediaType)
            .header(HttpHeaders.ACCEPT_RANGES, "bytes")
            .header(HttpHeaders.CONTENT_LENGTH, rangeData.contentLength.toString())
            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"${rangeData.fileName}\"")
            .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
            .body(rangeData.data)
    }
}

@Component
class ContentScheduler(
    private val contentService: ContentService
) {
    /** 每日凌晨2点清理孤立文件 */
    @Scheduled(cron = "0 0 2 * * ?")
    fun cleanOrphanedFiles() {
        log.info("Starting scheduled cleanup of orphaned files")
        try {
            contentService.cleanOrphanedFiles()
        } catch (e: Exception) {
            log.error("Scheduled cleanup failed", e)
        }
    }
}
