package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.nio.file.Files
import java.time.LocalDate
import java.time.format.DateTimeFormatter


class Content : BaseModel() {
    /** 关联的会议ID */
    var meetingId: String = ""
    /** 文件原始名称（不含扩展名） */
    var fullName: String = ""
    /** 文件在服务器上的存储名称（包含扩展名） */
    var storeName: String = ""
    /** 文件存储的相对路径（按日期归档） */
    var filePath: String = ""
    /** 文件类型（扩展名） */
    var fileType: String = ""
    /** 文件大小（字节） */
    var fileSize: Int? = null
}

class ContentSearch : BaseSearch() {
    /** 关联的会议ID */
    var meetingId: String = ""
    /** 文件名关键词 */
    var keyword: String = ""
    /** 文件类型 */
    var fileType: String = ""
}

@Mapper
interface ContentMapper : BaseMapper<Content> {
    @Select("""
        <script>
            SELECT * FROM tbl_content
            <where>
                sysDeleted = 0
                <if test="meetingId != ''">
                    AND meetingId = #{meetingId}
                </if>
                <if test="keyword != ''">
                    AND fullName LIKE CONCAT('%', #{keyword}, '%')
                </if>
                <if test="fileType != ''">
                    AND fileType = #{fileType}
                </if>
            </where>
            ORDER BY sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Content>
}

@Service
class ContentService(mapper: ContentMapper) : BaseService<Content, ContentMapper>(mapper)

@RestController
@RequestMapping("/api/Content")
class ContentResource(service: ContentService) : BaseResource<ContentSearch, Content, ContentMapper, ContentService>(service) {
    @PostMapping("/upload")
    fun upload(@RequestParam("file") file: MultipartFile): Result {
        val fileName = file.originalFilename!!
        val fileType = fileName.substring(fileName.lastIndexOf("."))
        val fileSize = file.size.toInt() // 获取文件大小并转换为Int类型
        val content = Content()
        content.fullName = fileName.replace(fileType, "")
        content.storeName = System.currentTimeMillis().toString() + fileType
        content.filePath = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)
        content.fileType = fileType
        content.fileSize = fileSize

        val dir = File(AppPro.uploadPath + content.filePath)
        if (!dir.exists()) dir.mkdirs()
        File(AppPro.uploadPath + content.filePath + File.separator + content.storeName).writeBytes(file.bytes)
        log.info("upload file: {}", content.toJsonString)
        service.save(content)
        return Result.getSuccess(content)
    }

    @PostMapping("/uploadForMeeting")
    fun uploadForMeeting(
        @RequestParam("file") file: MultipartFile,
        @RequestParam("meetingId") meetingId: String
    ): Result {
        val fileName = file.originalFilename!!
        val fileType = fileName.substring(fileName.lastIndexOf("."))
        val fileSize = file.size.toInt() // 获取文件大小并转换为Int类型
        val content = Content()
        content.fullName = fileName.replace(fileType, "")
        content.storeName = System.currentTimeMillis().toString() + fileType
        content.filePath = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)
        content.fileType = fileType
        content.fileSize = fileSize
        content.meetingId = meetingId

        val dir = File(AppPro.uploadPath + content.filePath)
        if (!dir.exists()) dir.mkdirs()
        File(AppPro.uploadPath + content.filePath + File.separator + content.storeName).writeBytes(file.bytes)
        log.info("upload meeting file: {}", content.toJsonString)
        service.save(content)
        return Result.getSuccess(content)
    }

    @PostMapping("/batchUploadForMeeting")
    fun batchUploadForMeeting(
        @RequestParam("files") files: Array<MultipartFile>,
        @RequestParam("meetingId") meetingId: String
    ): Result {
        val uploadResults = mutableListOf<Content>()

        for (file in files) {
            val fileName = file.originalFilename!!
            val fileType = fileName.substring(fileName.lastIndexOf("."))
            val fileSize = file.size.toInt()
            val content = Content()
            content.fullName = fileName.replace(fileType, "")
            content.storeName = System.currentTimeMillis().toString() + fileType
            content.filePath = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)
            content.fileType = fileType
            content.fileSize = fileSize
            content.meetingId = meetingId

            val dir = File(AppPro.uploadPath + content.filePath)
            if (!dir.exists()) dir.mkdirs()
            File(AppPro.uploadPath + content.filePath + File.separator + content.storeName).writeBytes(file.bytes)
            log.info("batch upload meeting file: {}", content.toJsonString)
            service.save(content)
            uploadResults.add(content)
        }

        return Result.getSuccess(uploadResults)
    }

    @PostMapping("/rename/{id}")
    fun rename(@PathVariable id: String, @RequestParam("newName") newName: String): Result {
        val content = service.getInfo(id).datas as Content? ?: return Result.getError("附件不存在")
        content.fullName = newName
        return service.save(content)
    }

    @GetMapping("/download/{id}")
    fun download(@PathVariable id: String): ResponseEntity<ByteArray> {
        val content = service.getInfo(id).datas as Content? ?: throw RuntimeException("附件不存在")
        val file = File(AppPro.uploadPath + content.filePath + File.separator + content.storeName)

        if (!file.exists()) {
            throw RuntimeException("文件不存在")
        }

        val data = Files.readAllBytes(file.toPath())
        val fileName = content.fullName + content.fileType

        return ResponseEntity.ok()
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"$fileName\"")
            .body(data)
    }

    @GetMapping("/preview/{id}")
    fun preview(@PathVariable id: String): ResponseEntity<ByteArray> {
        val content = service.getInfo(id).datas as Content? ?: throw RuntimeException("附件不存在")
        val file = File(AppPro.uploadPath + content.filePath + File.separator + content.storeName)

        if (!file.exists()) {
            throw RuntimeException("文件不存在")
        }

        val data = Files.readAllBytes(file.toPath())
        val fileName = content.fullName + content.fileType

        // 根据文件类型确定适当的MediaType
        val mediaType = when (content.fileType.toLowerCase()) {
            ".pdf" -> MediaType.APPLICATION_PDF
            ".jpg", ".jpeg" -> MediaType.IMAGE_JPEG
            ".png" -> MediaType.IMAGE_PNG
            ".gif" -> MediaType.IMAGE_GIF
            ".txt" -> MediaType.TEXT_PLAIN
            ".html", ".htm" -> MediaType.TEXT_HTML
            ".xml" -> MediaType.APPLICATION_XML
            ".json" -> MediaType.APPLICATION_JSON
            ".doc", ".docx" -> MediaType.parseMediaType("application/msword")
            ".xls", ".xlsx" -> MediaType.parseMediaType("application/vnd.ms-excel")
            ".ppt", ".pptx" -> MediaType.parseMediaType("application/vnd.ms-powerpoint")
            else -> MediaType.APPLICATION_OCTET_STREAM
        }

        log.info("preview file: {} with type: {}", fileName, mediaType)

        return ResponseEntity.ok()
            .contentType(mediaType)
            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"$fileName\"")
            .body(data)
    }
}
