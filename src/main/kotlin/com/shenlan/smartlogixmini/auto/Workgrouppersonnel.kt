package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 工作组人员关系实体类
 */
class Workgrouppersonnel : BaseModel() {
    // 数据库表字段

    /** 工作组ID */
    var workgroupId: String = ""
    /** 人员ID */
    var personnelId: String = ""

    // 关联字段

    /** 关联的工作组信息 */
    var workgroup: Workgroup? = null
    /** 关联的人员信息 */
    var personnel: Personnel? = null
}

/**
 * 工作组人员关系查询条件类
 */
class WorkgrouppersonnelSearch : BaseSearch() {
    /** 工作组ID */
    var workgroupId: String = ""
    /** 人员ID */
    var personnelId: String = ""
    /** 是否加载工作组信息 */
    var loadWorkgroup: Boolean = false
    /** 是否加载人员信息 */
    var loadPersonnel: Boolean = false
}

/**
 * 工作组人员关系Mapper接口
 */
@Mapper
interface WorkgrouppersonnelMapper : BaseMapper<Workgrouppersonnel> {

    @Select("""
        <script>
            SELECT wp.* FROM tbl_workgrouppersonnel wp
            <where>
                <if test="workgroupId != ''">
                    wp.workgroupId = #{workgroupId}
                </if>
                <if test="personnelId != ''">
                    AND wp.personnelId = #{personnelId}
                </if>
            </where>
            ORDER BY wp.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Workgrouppersonnel>

    /**
     * 重写getInfo方法
     */
    @Select("""
        SELECT * FROM tbl_workgrouppersonnel
        WHERE id = #{id}
    """)
    override fun getInfo(id: String): Workgrouppersonnel?

    /**
     * 删除指定工作组的所有人员关联
     */
    @Delete("""
        DELETE FROM tbl_workgrouppersonnel
        WHERE workgroupId = #{workgroupId}
    """)
    fun deleteByWorkgroupId(workgroupId: String): Int
}

/**
 * 工作组人员关系Service类
 */
@Service
class WorkgrouppersonnelService(
    mapper: WorkgrouppersonnelMapper,
    private val workgroupMapper: WorkgroupMapper,
    private val personnelMapper: PersonnelMapper
) : BaseService<Workgrouppersonnel, WorkgrouppersonnelMapper>(mapper) {

    /**
     * 重写getList方法，根据需要加载关联信息
     */
    override fun getList(page: BaseSearch): Result {
        val result = super.getList(page)
        if (page is WorkgrouppersonnelSearch) {
            // 获取关系列表
            val relationList = result.toList<Workgrouppersonnel>()

            // 加载关联信息
            relationList.forEach { relation ->
                if (page.loadWorkgroup) {
                    loadWorkgroup(relation)
                }
                if (page.loadPersonnel) {
                    loadPersonnel(relation)
                }
            }
        }
        return result
    }

    /**
     * 重写getInfo方法，加载关联信息
     */
    override fun getInfo(id: String): Result {
        val relation = mapper.getInfo(id)
        if (relation != null) {
            // 加载工作组信息
            loadWorkgroup(relation)
            // 加载人员信息
            loadPersonnel(relation)
        }
        return Result.getSuccess(relation)
    }

    /**
     * 加载工作组信息
     */
    private fun loadWorkgroup(relation: Workgrouppersonnel) {
        if (relation.workgroupId.isNotEmpty()) {
            relation.workgroup = workgroupMapper.getInfo(relation.workgroupId)
        }
    }

    /**
     * 加载人员信息
     */
    private fun loadPersonnel(relation: Workgrouppersonnel) {
        if (relation.personnelId.isNotEmpty()) {
            relation.personnel = personnelMapper.getInfo(relation.personnelId)
        }
    }
}

/**
 * 工作组人员关系Controller类
 */
@RestController
@RequestMapping("/api/Workgrouppersonnel")
class WorkgrouppersonnelResource(service: WorkgrouppersonnelService) : BaseResource<WorkgrouppersonnelSearch, Workgrouppersonnel, WorkgrouppersonnelMapper, WorkgrouppersonnelService>(service)
