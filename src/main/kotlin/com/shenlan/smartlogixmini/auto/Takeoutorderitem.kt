package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Takeoutorderitem: BaseEntity() {
    var orderId: String = ""
    var dishId: String = ""
    var dishName: String = ""
    var price: Double = 0.0
    var sysDeleted: Int = 0

    // 购买数目
    var orderCount: Int = 0

    var portionUnit: Int = 0
    var limitPerPerson: Int = 0

    var takeoutMenuDishId: String = ""

    var imageUrl: String = ""
//        get() = if (field.isEmpty()) field else AppPro.imgPrefix + field
}

class TakeoutorderitemSearch: BaseSearch() {
    var orderId: String = ""
    var dishId: String = ""
    var dishName: String = ""
}

@Mapper
interface TakeoutorderitemMapper : BaseMapper<Takeoutorderitem> {
    @Select("""
        SELECT toi.*,di.imageUrl FROM tbl_takeoutorderitem toi left join tbl_dishimage di on toi.dishId = di.id
        WHERE toi.id = #{id}
    """)
    override fun getInfo(id: String): Takeoutorderitem?

    @Select("""
        <script>
            SELECT toi.* FROM tbl_takeoutorderitem toi
            <where>
                AND toi.sysDeleted = 0
                <if test="orderId != ''">
                    AND toi.orderId = #{orderId}
                </if>
                <if test="dishId != ''">
                    AND toi.dishId = #{dishId}
                </if>
                <if test="dishName != ''">
                    AND toi.dishName LIKE CONCAT('%', #{dishName}, '%')
                </if>
            </where>
            ORDER BY toi.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Takeoutorderitem>

    // 根据orderId列表查询orderItem列表
    @Select("""
        <script>
            SELECT * FROM tbl_takeoutorderitem
            WHERE orderId IN
            <foreach item="item" index="" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </script>
    """)
    fun getListByOrderIdList(@Param("list") orderIdList: List<String>): List<Takeoutorderitem>
}

@Service
class TakeoutorderitemService(mapper: TakeoutorderitemMapper) : BaseService<Takeoutorderitem, TakeoutorderitemMapper, TakeoutorderitemSearch>(mapper)

@RestController
@RequestMapping("/api/takeoutorderitem")
class TakeoutorderitemResource(service: TakeoutorderitemService) : BaseResource<TakeoutorderitemSearch, Takeoutorderitem, TakeoutorderitemMapper, TakeoutorderitemService>(service)
