package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Takeoutorderitem: BaseModel() {
    var orderId: String = ""
    var dishId: String = ""
    var dishName: String = ""
    var price: Double = 0.0
    var sysDeleted: Int = 0

    // 购买数目
    var orderCount: Int = 0
}

class TakeoutorderitemSearch: BaseSearch() {
    var orderId: String = ""
    var dishId: String = ""
    var dishName: String = ""
}

@Mapper
interface TakeoutorderitemMapper : BaseMapper<Takeoutorderitem> {
    @Select("""
        <script>
            SELECT * FROM tbl_takeoutorderitem
            <where>
                AND sysDeleted = 0
                <if test="orderId != ''">
                    AND orderId = #{orderId}
                </if>
                <if test="dishId != ''">
                    AND dishId = #{dishId}
                </if>
                <if test="dishName != ''">
                    AND dishName LIKE CONCAT('%', #{dishName}, '%')
                </if>
            </where>
            ORDER BY sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Takeoutorderitem>

    // 根据orderId列表查询orderItem列表
    @Select("""
        <script>
            SELECT * FROM tbl_takeoutorderitem
            WHERE orderId IN
            <foreach item="item" index="" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </script>
    """)
    fun getListByOrderIdList(@Param("list") orderIdList: List<String>): List<Takeoutorderitem>
}

@Service
class TakeoutorderitemService(mapper: TakeoutorderitemMapper) : BaseService<Takeoutorderitem, TakeoutorderitemMapper>(mapper)

@RestController
@RequestMapping("/api/takeoutorderitem")
class TakeoutorderitemResource(service: TakeoutorderitemService) : BaseResource<TakeoutorderitemSearch, Takeoutorderitem, TakeoutorderitemMapper, TakeoutorderitemService>(service)
