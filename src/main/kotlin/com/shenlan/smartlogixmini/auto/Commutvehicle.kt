package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Commutvehicle: BaseModel() {
    var driverId: String = ""
    var routeId: String = ""
    var status: Int = 0
    var carId: String = ""
    var carPlate: String = ""
    var plateColor: String = ""
    var plateColorCode: String = ""
    var carName: String = ""
    var teamId: String = ""
    var teamName: String = ""
    var cmpName: String = ""
    var gps: Boolean = false
    var tmnKey: String = ""
    var dueDate: String = ""
    var onlineTime: String = ""
    var icon: String = ""
    var iconLink: String = ""
    var des: String = ""
    var bxts: String = ""
    var sysDeleted: Int = 0
}

class CommutvehicleSearch: BaseSearch() {
    var driverId: String = ""
    var routeId: String = ""
    var status: Int? = null
    var carPlate: String = ""
    var carName: String = ""
    var teamId: String = ""
    var teamName: String = ""
    var cmpName: String = ""
}

@Mapper
interface CommutvehicleMapper : BaseMapper<Commutvehicle> {
    @Select("""
        <script>
            SELECT * FROM tbl_commutvehicle
            <where>
                AND sysDeleted = 0
                <if test="driverId != ''">
                    AND driverId = #{driverId}
                </if>
                <if test="routeId != ''">
                    AND routeId = #{routeId}
                </if>
                <if test="status != null">
                    AND status = #{status}
                </if>
                <if test="carPlate != ''">
                    AND carPlate LIKE CONCAT('%', #{carPlate}, '%')
                </if>
                <if test="carName != ''">
                    AND carName LIKE CONCAT('%', #{carName}, '%')
                </if>
                <if test="teamId != ''">
                    AND teamId = #{teamId}
                </if>
                <if test="teamName != ''">
                    AND teamName LIKE CONCAT('%', #{teamName}, '%')
                </if>
                <if test="cmpName != ''">
                    AND cmpName LIKE CONCAT('%', #{cmpName}, '%')
                </if>
            </where>
            ORDER BY carPlate ASC, sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutvehicle>
}

@Service
class CommutvehicleService(mapper: CommutvehicleMapper) : BaseService<Commutvehicle, CommutvehicleMapper>(mapper)

@RestController
@RequestMapping("/api/commutvehicle")
class CommutvehicleResource(service: CommutvehicleService) : BaseResource<CommutvehicleSearch, Commutvehicle, CommutvehicleMapper, CommutvehicleService>(service)
