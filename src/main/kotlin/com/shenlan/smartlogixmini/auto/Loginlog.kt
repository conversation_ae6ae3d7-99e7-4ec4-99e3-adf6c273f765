package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.errorlog
import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 登录日志实体类
 */
class Loginlog : BaseModel() {
    /** 用户ID */
    var userId: String = ""
    /** 用户姓名 */
    var userName: String = ""
    /** 组织机构名称 */
    var orgName: String = ""
    /** 登录IP地址 */
    var ipAddress: String = ""
    /** 浏览器名称 */
    var browserName: String = ""
}

@Mapper
interface LoginlogMapper : BaseMapper<Loginlog> {
    @Select("""
    <script>
      SELECT * FROM tbl_loginlog
      <where>
        <if test="userId != ''">
          AND userId = #{userId}
        </if>
      </where>
      ORDER BY sysCreated DESC
    </script>
    """)
    override fun getList(search: BaseSearch): List<Loginlog>

    /**
     * 删除超过指定天数的登录日志
     * @param days 保留天数，超过此天数的日志将被删除
     * @return 删除的记录数
     */
    @Delete("DELETE FROM tbl_loginlog WHERE sysCreated < DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    fun deleteOldLogs(days: Int): Int
}

/**
 * 登录日志查询条件类
 */
class LoginlogSearch : BaseSearch() {
    /** 用户ID */
    var userId: String = ""
}

@Service
class LoginlogService(mapper: LoginlogMapper) : BaseService<Loginlog, LoginlogMapper>(mapper) {

    /**
     * 清理超过指定天数的登录日志
     * @param days 保留天数，超过此天数的日志将被删除
     * @return 删除的记录数
     */
    fun cleanOldLogs(days: Int): Int {
        try {
            log.info("Starting cleanup of old login logs older than {} days", days)
            val deletedCount = mapper.deleteOldLogs(days)
            log.info("Successfully cleaned up {} login logs older than {} days", deletedCount, days)
            return deletedCount
        } catch (e: Exception) {
            log.error("Failed to clean up old login logs: {}", e.message)
            errorlog(e)
            throw e
        }
    }
}

@RestController
@RequestMapping("/api/Loginlog")
class LoginlogResource(service: LoginlogService) : BaseResource<LoginlogSearch, Loginlog, LoginlogMapper, LoginlogService>(service)

/**
 * 登录日志定时清理调度器
 */
@Component
class LoginlogScheduler(
    private val loginlogService: LoginlogService
) {

    /**
     * 清理任务：定期清理超过90天的登录日志
     * 每天凌晨3点执行一次
     */
    @Scheduled(cron = "0 0 3 * * ?")
    fun cleanOldLogs() {
        try {
            log.info("Starting scheduled cleanup of old login logs")
            val deletedCount = loginlogService.cleanOldLogs(90)
            log.info("Scheduled cleanup completed, removed {} records", deletedCount)
        } catch (e: Exception) {
            log.error("Scheduled cleanup task failed: {}", e.message)
            errorlog(e)
        }
    }
}
