package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Dishrating: BaseModel() {
    var dishId: String = ""
    var userId: String = ""
    var rating: Int = 0
    var sysUpdated: String = ""
    var sysDeleted: Int = 0
    var commentId: String = ""
}

class DishratingSearch: BaseSearch() {
    var dishId: String = ""
    var userId: String = ""
    var rating: Int = 0
}

@Mapper
interface DishratingMapper : BaseMapper<Dishrating> {
    @Select("""
        <script>
            SELECT * FROM tbl_dishrating
            <where>
                AND sysDeleted = 0
                <if test="dishId != ''">
                    AND dishId = #{dishId}
                </if>
                <if test="userId != ''">
                    AND userId = #{userId}
                </if>
                <if test="rating != null">
                    AND rating = #{rating}
                </if>
            </where>
            ORDER BY sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Dishrating>
}

@Service
class DishratingService(mapper: DishratingMapper) : BaseService<Dishrating, DishratingMapper>(mapper)

@RestController
@RequestMapping("/api/dishrating")
class DishratingResource(service: DishratingService) : BaseResource<DishratingSearch, Dishrating, DishratingMapper, DishratingService>(service)
