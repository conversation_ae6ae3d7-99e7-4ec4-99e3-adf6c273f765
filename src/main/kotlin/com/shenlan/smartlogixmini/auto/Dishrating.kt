package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Dishrating: BaseModel() {
    var dishId: String = ""
    var userId: String = ""
    var rating: Int = 0
    var sysUpdated: String = ""
    var sysDeleted: Int = 0
    var commentId: String = ""

    // 关联表字段
    var dishName: String = ""
}

class DishratingSearch: BaseSearch() {
    var dishId: String = ""
    var userId: String = ""
    var rating: Int = 0
}

@Mapper
interface DishratingMapper : BaseMapper<Dishrating> {
    @Select("""
        <script>
            SELECT dr.*,d.name as dishName FROM tbl_dishrating dr left join tbl_dish d on dr.dishId = d.id
            <where>
                AND dr.sysDeleted = 0
                <if test="startTime != '' and endTime != ''">
                    AND dr.sysCreated between #{startTime} and #{endTime}
                </if>
            </where>
            ORDER BY dr.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Dishrating>

    /**
     * 根据dishId列表查询所有dishId和rating
     */
    @Select("""
        <script>
            SELECT dishId, rating FROM tbl_dishrating
            WHERE dishId IN
            <foreach item="item" index="" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </script>
    """)
    fun getDishIdAndRatingByDishIdList(@Param("list") dishIdList: List<String>): List<Dishrating>

    /**
     * 根据commentId列表查询所有dishId和rating
     */
    @Select("""
        <script>
            SELECT dr.userId, dr.rating, d.name as dishName FROM tbl_dishrating dr left join tbl_dish d on dr.dishId = d.id
            WHERE dr.commentId IN
            <foreach item="item" index="" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </script>
    """)
    fun getDishIdAndRatingByCommentIdList(@Param("list") commentIdList: List<String>): List<Dishrating>
}

@Service
class DishratingService(mapper: DishratingMapper) : BaseService<Dishrating, DishratingMapper>(mapper)

@RestController
@RequestMapping("/api/dishrating")
class DishratingResource(service: DishratingService) : BaseResource<DishratingSearch, Dishrating, DishratingMapper, DishratingService>(service)
