package com.shenlan.smartlogixmini.auto

import com.aliyun.mns.common.ServiceException
import com.shenlan.smartlogixmini.config.WeChatPayConfig
import com.shenlan.smartlogixmini.config.WebConfig
import com.shenlan.smartlogixmini.task.TakeoutOrderTask
import com.shenlan.smartlogixmini.util.HttpUtil
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import com.shenlan.smartlogixmini.util.uuid
import com.wechat.pay.java.core.RSAAutoCertificateConfig
import com.wechat.pay.java.core.cipher.RSASigner
import com.wechat.pay.java.core.exception.MalformedMessageException
import com.wechat.pay.java.core.notification.NotificationParser
import com.wechat.pay.java.core.util.PemUtil
import com.wechat.pay.java.service.partnerpayments.nativepay.model.Transaction
import com.wechat.pay.java.service.payments.jsapi.JsapiService
import com.wechat.pay.java.service.payments.jsapi.model.Payer
import com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest;
import com.wechat.pay.java.service.payments.nativepay.NativePayService
import com.wechat.pay.java.service.payments.nativepay.model.Amount
import com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse
import org.apache.http.HttpException
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.nio.file.Files
import java.nio.file.Paths
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec
import java.util.Base64
import javax.servlet.http.HttpServletRequest
import javax.xml.bind.ValidationException

class OrderRequest {
    var description: String = ""
    var outTradeNo: String = ""
    var amount: Int = 0
    var openid: String = ""
    var code: String = ""

    constructor()

    constructor(description: String, outTradeNo: String, amount: Int, code: String = "") {
        this.description = description
        this.outTradeNo = outTradeNo
        this.amount = amount
        this.code = code
    }
}

class JsCodeToSession() {
    var session_key: String = ""
    var openid: String = ""
}

class PaymentRequest {
    var appId: String = ""
    var timeStamp: String = ""
    var nonceStr: String = ""
    var packageStr: String = ""
    var signType: String = ""
    var paySign: String = ""

    constructor()

    constructor(timeStamp: String, nonceStr: String, packageStr: String, signType: String, paySign: String) {
        this.appId = WeChatPayConfig.appid
        this.timeStamp = timeStamp
        this.nonceStr = nonceStr
        this.packageStr = packageStr
        this.signType = signType
        this.paySign = paySign
    }
}

@Component
object WeChatPayUtil {
    private var rsaAutoCertificateConfig: RSAAutoCertificateConfig? = null

    fun init() {
//        WeChatPayConfig.init()
        rsaAutoCertificateConfig = WeChatPayConfig.getRSAAutoCertificateConfig()
    }

    fun getNativePayService(): NativePayService = NativePayService.Builder().config(rsaAutoCertificateConfig).build()

    fun getJsapiPayService(): JsapiService = JsapiService.Builder().config(rsaAutoCertificateConfig).build()

    fun getNotificationParser(): NotificationParser = NotificationParser(rsaAutoCertificateConfig)

    fun prepay(request: OrderRequest): PrepayResponse {
        val nativePayService = getNativePayService()
        val prepayRequest = PrepayRequest().apply {
            amount = Amount().apply { total = request.amount }
            appid = WeChatPayConfig.appid
            mchid = WeChatPayConfig.merchantId
            description = request.description
            outTradeNo = request.outTradeNo
            notifyUrl = WeChatPayConfig.notifyUrl
        }
        return nativePayService.prepay(prepayRequest)
    }

    fun jsapiPrepay(request: OrderRequest): Result {
        // 1.查询订单信息
        val orderItems = getBean(TakeoutorderitemMapper::class.java).getList(TakeoutorderitemSearch().apply { orderId = request.outTradeNo })
        if (orderItems.isEmpty()) return Result.getError("订单${request.outTradeNo}物品信息不存在")

        // 2.通过code换取openid
//        val uri = "https://api.weixin.qq.com/sns/jscode2session" +
//                "?appid=${WeChatPayConfig.appid}" +
//                "&secret=${WeChatPayConfig.appSecret}" +
//                "&js_code=${request.code}" +
//                "&grant_type=authorization_code"
//        val openid = HttpUtil.getJsp<JsCodeToSession>(uri).openid

        // 3.调起支付
        return try {
            val jsapiPayService = getJsapiPayService()
            val prepayRequest = com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest().apply {
                amount = com.wechat.pay.java.service.payments.jsapi.model.Amount().apply { total =
                    (orderItems.sumByDouble { it.price * it.orderCount } * 100).toInt()
                }
                appid = WeChatPayConfig.appid
                mchid = WeChatPayConfig.merchantId
                description = orderItems.joinToString(",") { "${it.dishName} x ${it.orderCount}" }
                outTradeNo = request.outTradeNo
                notifyUrl = WeChatPayConfig.notifyUrl
                payer = Payer().apply { this.openid = request.openid }  // JSAPI必须
            }
            log.info("prepayRequest: ${prepayRequest.toJsonString}")
            val prepayResponse = jsapiPayService.prepay(prepayRequest)
            val timeStamp = (System.currentTimeMillis() / 1000).toString()
            val nonceStr = uuid()
            val packageStr = "prepay_id=${prepayResponse.prepayId}"
            val signType = "RSA"
            val paySign = generatePaySign(
                WeChatPayConfig.appid,
                timeStamp,
                nonceStr,
                packageStr,
                System.getProperty("user.dir") + WeChatPayConfig.privateKeyPath)

            Result.getSuccess(PaymentRequest(
                timeStamp,
                nonceStr,
                packageStr,
                signType,
                paySign
            ))
        } catch (e: Exception) {
            log.error("Failed to prepay: ${e.message}")
            Result.getError("jsapi预支付失败: ${e.message}")
        }

    }

    fun generatePaySign(appId: String, timeStamp: String, nonceStr: String, packageStr: String, privateKeyPath: String): String {
        val message = listOf(appId, timeStamp, nonceStr, packageStr).joinToString("\n", postfix = "\n")
        val privateKey = loadPrivateKey(privateKeyPath)
        val signature = Signature.getInstance("SHA256withRSA")
        signature.initSign(privateKey)
        signature.update(message.toByteArray(Charsets.UTF_8))
        return Base64.getEncoder().encodeToString(signature.sign())
    }

    fun loadPrivateKey(path: String): PrivateKey {
        val keyBytes = Files.readAllBytes(Paths.get(path))
        val pem = String(keyBytes)
            .replace("-----BEGIN PRIVATE KEY-----", "")
            .replace("-----END PRIVATE KEY-----", "")
            .replace("\\s+".toRegex(), "")
        val decoded = Base64.getDecoder().decode(pem)
        val keySpec = PKCS8EncodedKeySpec(decoded)
        return KeyFactory.getInstance("RSA").generatePrivate(keySpec)
    }
}

@Service
class WechatpayService() {
    /**
     * 预支付接口
     * 生成订单信息
     *
     * @param request 请求体
     * @return Result
     */
    fun prepay(request: OrderRequest): PrepayResponse = WeChatPayUtil.prepay(request)

    fun jsapiPrepay(request: OrderRequest): Result {
        return WeChatPayUtil.jsapiPrepay(request)
    }

    /**
     * 验签、解密并转换成 Transaction
     * 处理回调相关业务逻辑
     *
     * @param requestParam 回调请求参数
     * @return Transaction
     */
    fun notify(requestParam: com.wechat.pay.java.core.notification.RequestParam): Transaction {
        log.info("requestParam: ${requestParam.toJsonString}")
        val parser = WeChatPayUtil.getNotificationParser()
        val transaction = parser.parse(requestParam, Transaction::class.java)

        log.info("transaction: ${transaction.toJsonString}")

        // 支付成功，更新订单状态
        if (transaction.tradeState.name == com.wechat.pay.java.service.partnerpayments.jsapi.model.Transaction.TradeStateEnum.SUCCESS.name) {
            val mapper = getBean(TakeoutorderMapper::class.java)
            val pickupCode = TakeoutOrderTask.getPickupCode()

            // 更新取餐号和状态
//            mapper.updatePickupCode(transaction.outTradeNo, pickupCode)
//            mapper.updateOrderStatus(transaction.outTradeNo, OrderStatus.PAID.value)
            mapper.updateTransactionId(transaction.outTradeNo, transaction.transactionId, pickupCode, OrderStatus.PAID.value)
        }

        return transaction
    }

//    fun mockPrepay(takeoutOrderId: String) {
//        val mapper = getBean(TakeoutorderMapper::class.java)
//        val pickupCode = TakeoutOrderTask.getPickupCode()
//
//        // 更新取餐号和状态
//        mapper.updatePickupCode(takeoutOrderId, pickupCode)
//        mapper.updateOrderStatus(takeoutOrderId, OrderStatus.PAID.value)
//    }
}

/**
 * 微信支付
 */
@RestController
@RequestMapping("/api/wechatpay")
class WechatpayResource(private val service: WechatpayService) {
    /**
     * 预支付接口
     *
     * @param request 请求体
     * @return Result
     */
    @PostMapping("prepay")
    fun prepay(@RequestBody request: OrderRequest): Result {
        return try {
            service.jsapiPrepay(request)
//            Result.getSuccess(service.mockPrepay(request.outTradeNo))
        } catch (e: HttpException) {
            Result.getError("调用微信支付服务 HTTP 请求异常：${e.message}")
        } catch (e: ValidationException) {
            Result.getError("验证微信支付签名失败：${e.message}")
        } catch (e: ServiceException) {
            Result.getError("微信支付服务异常：${e.message}")
        } catch (e: MalformedMessageException) {
            Result.getError("微信支付服务返回的报文格式错误：${e.message}")
        }
    }

    /**
     * 回调接口
     *
     * @param requestBody 请求体
     * @param headers 请求头
     * @return Result
     */
    @PostMapping("notify")
    fun notify(@RequestBody requestBody: String, request: HttpServletRequest): Result {
        try {
            val serialNumber = request.getHeader("Wechatpay-Serial")
            val nonce = request.getHeader("Wechatpay-Nonce")
            val signature = request.getHeader("Wechatpay-Signature")
            val timestamp = request.getHeader("Wechatpay-Timestamp")
            val headers = request.headerNames
//            log.info("wechat pay notify requestBody: $requestBody")
            log.info("wechat pay notify requestBody: $requestBody, headers: ${headers.toJsonString}, serialNumber: $serialNumber, nonce: $nonce, signature: $signature, timestamp: $timestamp")
            val requestParam = com.wechat.pay.java.core.notification.RequestParam.Builder()
                .serialNumber(serialNumber)
                .nonce(nonce)
                .signature(signature)
                .timestamp(timestamp)
                .body(requestBody)
                .build()

            return Result.getSuccess(service.notify(requestParam))
        } catch (e: ValidationException) {
            return Result.getError("验证微信支付签名失败：${e.message}")
        }
    }

    /**
     * 微信oauth回调接口
     */
//    @GetMapping("oauth")
//    fun oauth(@RequestParam code: String): Result {
//        WeChatPayConfig.updateOpenId(code)
//        return Result.getSuccess(code)
//    }
}