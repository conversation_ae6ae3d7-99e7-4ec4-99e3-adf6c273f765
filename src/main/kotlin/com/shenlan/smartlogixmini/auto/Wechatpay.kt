package com.shenlan.smartlogixmini.auto

import com.aliyun.mns.common.ServiceException
import com.shenlan.smartlogixmini.config.WeChatPayConfig
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import com.wechat.pay.java.core.RSAAutoCertificateConfig
import com.wechat.pay.java.core.exception.MalformedMessageException
import com.wechat.pay.java.core.http.HttpHeaders
import com.wechat.pay.java.core.notification.NotificationParser
import com.wechat.pay.java.service.partnerpayments.nativepay.model.Transaction
import com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest;
import com.wechat.pay.java.service.payments.nativepay.NativePayService
import com.wechat.pay.java.service.payments.nativepay.model.Amount
import com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse
import org.apache.http.HttpException
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import javax.xml.bind.ValidationException

class OrderRequest {
    var appid: String = ""
    var mchid: String = ""
    var description: String = ""
    var outTradeNo: String = ""
    var amount: Int = 0
}

@Component
object WeChatPayUtil {
    private var rsaAutoCertificateConfig: RSAAutoCertificateConfig? = null

    fun init() {
        rsaAutoCertificateConfig = WeChatPayConfig.getRSAAutoCertificateConfig()
    }

    fun getNativePayService(): NativePayService = NativePayService.Builder().config(rsaAutoCertificateConfig).build()

    fun getNotificationParser(): NotificationParser = NotificationParser(rsaAutoCertificateConfig)
}

@Service
class WechatpayService() {
    /**
     * 预支付接口
     * 生成订单信息
     *
     * @param request 请求体
     * @return Result
     */
    fun prepay(request: OrderRequest): PrepayResponse {
        val nativePayService = WeChatPayUtil.getNativePayService()
        val prepayRequest = PrepayRequest().apply {
            amount = Amount().apply { total = request.amount }
            appid = request.appid
            mchid = request.mchid
            description = request.description
            outTradeNo = request.outTradeNo
            notifyUrl = WeChatPayConfig.notifyUrl
        }
        return nativePayService.prepay(prepayRequest)
    }

    /**
     * 验签、解密并转换成 Transaction
     * 处理回调相关业务逻辑
     *
     * @param requestParam 回调请求参数
     * @return Transaction
     */
    fun notify(requestParam: com.wechat.pay.java.core.notification.RequestParam): Transaction {
        log.info("requestParam: ${requestParam.toJsonString}")
        val parser = WeChatPayUtil.getNotificationParser()

        val transaction = parser.parse(requestParam, Transaction::class.java)
        log.info("transaction: ${transaction.toJsonString}")

        return transaction
    }
}

/**
 * 微信支付
 */
@RestController
@RequestMapping("/api/wechatpay")
class WechatpayResource(private val service: WechatpayService) {
    /**
     * 预支付接口
     *
     * @param request 请求体
     * @return Result
     */
    @PostMapping("prepay")
    fun prepay(@RequestBody request: OrderRequest): Result {
        return try {
            Result.getSuccess(service.prepay(request))
        } catch (e: HttpException) {
            Result.getError("调用微信支付服务 HTTP 请求异常：${e.message}")
        } catch (e: ValidationException) {
            Result.getError("验证微信支付签名失败：${e.message}")
        } catch (e: ServiceException) {
            Result.getError("微信支付服务异常：${e.message}")
        } catch (e: MalformedMessageException) {
            Result.getError("微信支付服务返回的报文格式错误：${e.message}")
        }
    }

    /**
     * 回调接口
     *
     * @param requestBody 请求体
     * @param headers 请求头
     * @return Result
     */
    @GetMapping("notify")
    fun notify(@RequestBody requestBody: String, @RequestHeader headers: HttpHeaders): Result {
        try {
            val requestParam = com.wechat.pay.java.core.notification.RequestParam.Builder()
                .serialNumber(headers.getHeader("Wechatpay-Serial"))
                .nonce(headers.getHeader("Wechatpay-Nonce"))
                .signature(headers.getHeader("Wechatpay-Signature-Type"))
                .timestamp(headers.getHeader("Wechatpay-Timestamp"))
                .body(requestBody)
                .build()

            return Result.getSuccess(service.notify(requestParam))
        } catch (e: ValidationException) {
            return Result.getError("验证微信支付签名失败：${e.message}")
        }
    }
}