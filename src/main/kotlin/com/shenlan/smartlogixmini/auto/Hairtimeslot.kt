package com.shenlan.smartlogixmini.auto

import com.fasterxml.jackson.annotation.JsonFormat
import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * 理发时间段实体类
 */
class Hairtimeslot : BaseEntity {
    var configId:String = ""
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm")
    var startTime: LocalDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(0, 0))
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm")
    var endTime: LocalDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(0, 0))
    var status:Int =0//0未预约1已预约2不可约
    var hairreservation:Hairreservation? = null
    constructor(
        configId: String,
        startTime: LocalDateTime,
        endTime: LocalDateTime,
        status: Int
    ){
        this.configId = configId
        this.startTime = startTime
        this.endTime = endTime
        this.status = status
    }
    constructor()
}

/**
 * 理发时间段查询条件类
 */
class HairtimeslotSearch: BaseSearch() {
    var configId:String = ""
}

/**
 * 理发时间段配置Mapper接口
 */
@Mapper
interface HairtimeslotMapper : BaseMapper<Hairtimeslot> {
    @Select("""
        <script>
            SELECT * FROM tbl_hairtimeslot
             <where>
                sysDeleted = 0
                <if test="configId != ''">
                    AND configId = #{configId}
                </if>
             </where> 
               ORDER BY startTime ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Hairtimeslot>

    @Delete("DELETE FROM tbl_hairtimeslot WHERE configId = #{configId}")
    fun deleteListByConfigId(configId:String):Int
//    用户预约成功sql
    @Update("UPDATE tbl_hairtimeslot SET status = 1 WHERE id = #{id} AND status = 0")
    fun updateStatusByTimesSlotId(id:String):Int
//    理发师取消用户预约接口
    @Update("UPDATE tbl_hairtimeslot SET status = 2 WHERE id = #{id} AND status = 1")
    fun updateStatusTo2ByTimesSlotId(id:String):Int
//    用户取消预约，释放对应的时间段将状态改为0
    @Update("UPDATE tbl_hairtimeslot SET status = 0 WHERE id = #{id} AND status = 1")
    fun updateStatusTo0ByTimesSlotId(id: String):Int
//    根据id值修改时间段的状态
    @Update("UPDATE tbl_hairtimeslot SET status = 2 WHERE id = #{id} ")
    fun updateStatusTo2ById(id: String):Int
}

/**
 * 理发时间段配置服务类
 */
@Service
class HairtimeslotService(
    val hairDateConfigMapper: HairdateconfigMapper,
    val specialClosedTimeMapper: SpecialclosedtimeMapper,
    val hairTimeSlotMapper: HairtimeslotMapper,
    val hairreservationMapper: HairreservationMapper,
    val hairreservationService: HairreservationService,
    val hairnotificationMapper: HairnotificationMapper
) :BaseService<Hairtimeslot, HairtimeslotMapper, HairtimeslotSearch>(hairTimeSlotMapper) {
@Transactional
    fun getTotalInfoByConfigId(configId:String):List<Hairtimeslot> {
        val currentTime = LocalDateTime.now()
        var hairtimeslotList = mutableListOf<Hairtimeslot>()
        val search:HairtimeslotSearch = HairtimeslotSearch()
        search.configId = configId
        hairtimeslotList=hairTimeSlotMapper.getList(search).toMutableList()
//    每次获取时间段时，先判断这个时间段有没有过期过期的话把状态值设置为2
        hairtimeslotList.forEach{
            if(it.startTime.isBefore(currentTime)){
                it.status=2
                hairTimeSlotMapper.updateStatusTo2ById(it.id)
            }
        }
        hairtimeslotList.forEach{
            it.hairreservation=hairreservationService.getTotalInfoByTimeslotId(it.id)
        }
        return hairtimeslotList
    }
    /**
     * 根据营业日配置生成时段
     */
    @Transactional
    fun generateTimeSlots(config: Hairdateconfig): Int {
        println("开始生成时间段")
        var count = 0
        var loopCount = 0  // 循环计数器

        // 获取营业日日期
        val businessDate = config.businessDate ?: throw IllegalArgumentException("营业日日期不能为空")

        // 查询特殊休息时段
        val search = SpecialclosedtimeSearch().apply { this.configId = config.id }
        val specialClosedTimes = specialClosedTimeMapper.getList(search)

        // 参数校验
        require(config.intervalMinutes > 0) { "时间间隔必须为正整数" }
        require(config.openTime.isBefore(config.closeTime)) { "开始时间必须早于结束时间" }

        var currentTime = LocalDateTime.of(businessDate, config.openTime)
        val closeTime = LocalDateTime.of(businessDate, config.closeTime)
        val intervalMinutes = config.intervalMinutes

        while (currentTime < closeTime) {
            loopCount++  // 增加循环计数

            // 安全机制：防止死循环（150次上限）
            if (loopCount > 150) {
                throw IllegalStateException("生成时间段时循环超过150次，可能发生死循环。请检查参数：openTime=${config.openTime}, closeTime=${config.closeTime}, intervalMinutes=${config.intervalMinutes}")
            }

            val endTime = currentTime.plusMinutes(intervalMinutes.toLong())
            if (endTime > closeTime) break

            // 判断时段状态
            val isLunchTime = isOverlappingWithLunch(currentTime.toLocalTime(), endTime.toLocalTime(), config.lunchStartTime, config.lunchEndTime)
            val isSpecialClosed = isOverlappingWithSpecialClosedTimes(currentTime.toLocalTime(), endTime.toLocalTime(), specialClosedTimes)

            val status = when {
                isLunchTime -> 2
                isSpecialClosed -> 2
                else -> 0
            }

            // 创建时段
            val timeSlot = Hairtimeslot(
                configId = config.id,
                startTime =  currentTime,
                endTime =  endTime,
                status = status
            )
            log.info("自动生成的日期时间段，开始时间{},结束时间{}", timeSlot.startTime,timeSlot.endTime)
            this.save(timeSlot)
            count++
            currentTime = endTime
        }

        return count
    }
    /**
     * 判断时段是否与午休时间重叠
     */
    private fun isOverlappingWithLunch(
        startTime: LocalTime,
        endTime: LocalTime,
        lunchStartTime: LocalTime,
        lunchEndTime: LocalTime
    ): Boolean {
        return startTime < lunchEndTime && endTime > lunchStartTime
    }

    /**
     * 判断时段是否与特殊休息时段重叠
     */
    private fun isOverlappingWithSpecialClosedTimes(
        startTime: LocalTime,
        endTime: LocalTime,
        specialClosedTimes: List<Specialclosedtime>
    ): Boolean {
        return specialClosedTimes.any { special ->
            startTime < special.specialEndTime && endTime > special.specialStartTime
        }
    }

//    /**
//     * 判断时段是否与午休时间重叠
//     */
//    private fun isOverlappingWithLunch(
//        startTime: LocalTime,
//        endTime: LocalTime,
//        lunchStartTime: LocalTime,
//        lunchEndTime: LocalTime
//    ): Boolean {
//        return startTime < lunchEndTime && endTime > lunchStartTime
//    }
//
//    /**
//     * 判断时段是否与特殊休息时段重叠
//     */
//    private fun isOverlappingWithSpecialClosedTimes(
//        startTime: LocalTime,
//        endTime: LocalTime,
//        specialClosedTimes: List<Specialclosedtime>
//    ): Boolean {
//        return specialClosedTimes.any { special ->
//            startTime < special.specialEndTime && endTime > special.specialStartTime
//        }
//    }
}

/**
 * 特殊时间段配置控制器
 */
@RestController
@RequestMapping("/api/Hairtimeslot")
class HairtimeslotResource(service: HairtimeslotService) :
    BaseResource<HairtimeslotSearch, Hairtimeslot, HairtimeslotMapper, HairtimeslotService>(service) {
}
