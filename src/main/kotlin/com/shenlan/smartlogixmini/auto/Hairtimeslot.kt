package com.shenlan.smartlogixmini.auto

import com.fasterxml.jackson.annotation.JsonFormat
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.LocalTime

/**
 * 理发时间段实体类
 */
class Hairtimeslot : BaseModel {
    var configId:String = ""
    @JsonFormat(pattern = "HH:mm")
    var startTime: LocalTime = LocalTime.of(0, 0)
    @JsonFormat(pattern = "HH:mm")
    var endTime: LocalTime = LocalTime.of(0, 0)
    var status:Int =0
    constructor(
        configId: String,
        startTime: LocalTime,
        endTime: LocalTime,
        status: Int
    ){
        this.configId = configId
        this.startTime = startTime
        this.endTime = endTime
        this.status = status
    }
    constructor()
}

/**
 * 理发时间段查询条件类
 */
class HairtimeslotSearch: BaseSearch() {
    var configId:String = ""
}

/**
 * 理发时间段配置Mapper接口
 */
@Mapper
interface HairtimeslotMapper : BaseMapper<Hairtimeslot> {
    @Select("""
        <script>
            SELECT * FROM tbl_hairtimeslot
             <where>
                sysDeleted = 0
                <if test="configId != ''">
                    AND configId = #{configId}
                </if>
             </where>   
        </script>
    """)
    override fun getList(search: BaseSearch): List<Hairtimeslot>

    @Delete("DELETE FROM tbl_hairtimeslot WHERE configId = #{configId}")
    fun deleteListByConfigId(configId:String):Int

}

/**
 * 理发时间段配置服务类
 */
@Service
class HairtimeslotService(
     val hairDateConfigMapper: HairdateconfigMapper,
     val specialClosedTimeMapper: SpecialclosedtimeMapper,
     val hairTimeSlotMapper: HairtimeslotMapper
) :BaseService<Hairtimeslot, HairtimeslotMapper>(hairTimeSlotMapper) {

    /**
     * 根据营业日配置生成时段
     */
    @Transactional
    fun generateTimeSlots(config: Hairdateconfig): Int {
        println("开始生成时间段")
        var count = 0

        // 2. 查询该营业日的特殊休息时段
        val search = SpecialclosedtimeSearch().apply { this.configId = config.id }
        val specialClosedTimes = specialClosedTimeMapper.getList(search)

        // 生成营业时间内的所有时段（包括午休）
        var currentTime = config.openTime
        val closeTime = config.closeTime
        val intervalMinutes = config.intervalMinutes

        while (currentTime < closeTime) {
            val endTime = currentTime.plusMinutes(intervalMinutes.toLong())

            // 如果下一时段超过结束时间，则调整
            if (endTime > closeTime) {
                break
            }

            // 判断时段状态
            val isLunchTime = isOverlappingWithLunch(currentTime, endTime, config.lunchStartTime, config.lunchEndTime)
            val isSpecialClosed = isOverlappingWithSpecialClosedTimes(currentTime, endTime, specialClosedTimes)

            val status = when {
                isLunchTime -> 2  // 午休时段设为不可约
                isSpecialClosed -> 2  // 特殊休息时段设为不可约
                else -> 0  // 其他时段设为未预约
            }

            // 创建时段
            val timeSlot = Hairtimeslot(
                configId = config.id,
                startTime = currentTime,
                endTime = endTime,
                status = status
            )

            this.save(timeSlot)
            count++
            currentTime = endTime
        }

        return count
    }
    /**
     * 判断时段是否与午休时间重叠
     */
    private fun isOverlappingWithLunch(
        startTime: LocalTime,
        endTime: LocalTime,
        lunchStartTime: LocalTime,
        lunchEndTime: LocalTime
    ): Boolean {
        return startTime < lunchEndTime && endTime > lunchStartTime
    }

    /**
     * 判断时段是否与特殊休息时段重叠
     */
    private fun isOverlappingWithSpecialClosedTimes(
        startTime: LocalTime,
        endTime: LocalTime,
        specialClosedTimes: List<Specialclosedtime>
    ): Boolean {
        return specialClosedTimes.any { special ->
            startTime < special.specialEndTime && endTime > special.specialStartTime
        }
    }
}

/**
 * 特殊时间段配置控制器
 */
@RestController
@RequestMapping("/api/Hairtimeslot")
class HairtimeslotResource(service: HairtimeslotService) :
    BaseResource<HairtimeslotSearch, Hairtimeslot, HairtimeslotMapper, HairtimeslotService>(service) {
}
