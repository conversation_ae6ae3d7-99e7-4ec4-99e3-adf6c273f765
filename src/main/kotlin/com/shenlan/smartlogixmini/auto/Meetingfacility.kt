package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 会议服务实体类
 */
class Meetingfacility : BaseModel() {
    /** 会议ID */
    var meetingId: String = ""
    /** 服务类型(0-用餐,1-桌牌,2-纸笔,3-茶水,4-果盘) */
    var type: Int? = null
    /** 数量 */
    var quantity: Int = 0
    /** 服务内容(如桌牌内容、用餐时间等) */
    var content: String = ""

    /** 关联的会议信息 */
    var meeting: Meeting? = null
}

/**
 * 会议服务查询条件类
 */
class MeetingfacilitySearch : BaseSearch() {
    /** 会议ID */
    var meetingId: String = ""
    /** 服务类型(0-用餐,1-桌牌,2-纸笔,3-茶水,4-果盘) */
    var type: Int? = null
    /** 是否加载会议信息 */
    var loadMeeting: Boolean = false
}

/**
 * 会议服务Mapper接口
 */
@Mapper
interface MeetingfacilityMapper : BaseMapper<Meetingfacility> {

    @Select("""
        <script>
            SELECT mf.* FROM tbl_meetingfacility mf
            <where>
                mf.sysDeleted = 0
                <if test="meetingId != ''">
                    AND mf.meetingId = #{meetingId}
                </if>
                <if test="type != null">
                    AND mf.type = #{type}
                </if>
            </where>
            ORDER BY mf.type ASC, mf.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Meetingfacility>
}

/**
 * 会议服务Service类
 */
@Service
class MeetingfacilityService(
    mapper: MeetingfacilityMapper,
    private val meetingMapper: MeetingMapper
) : BaseService<Meetingfacility, MeetingfacilityMapper>(mapper) {

    /**
     * 获取会议服务列表
     */
    override fun getList(page: BaseSearch): Result {
        val search = page as MeetingfacilitySearch
        val result = super.getList(page)

        // 如果需要加载会议信息
        if (search.loadMeeting) {
            loadMeetingInfo(result.toList())
        }

        return result
    }

    /**
     * 获取会议服务详情
     */
    override fun getInfo(id: String): Result {
        val result = super.getInfo(id)
        if (result.datas != null) {
            val meetingfacility = result.datas as Meetingfacility
            loadMeetingInfo(listOf(meetingfacility))
        }
        return result
    }

    /**
     * 加载会议信息
     */
    private fun loadMeetingInfo(list: List<Meetingfacility>) {
        list.forEach { facility ->
            if (facility.meetingId.isNotEmpty()) {
                facility.meeting = meetingMapper.getInfo(facility.meetingId)
            }
        }
    }
}

/**
 * 会议服务Controller类
 */
@RestController
@RequestMapping("/api/Meetingfacility")
class MeetingfacilityResource(
    service: MeetingfacilityService
) : BaseResource<MeetingfacilitySearch, Meetingfacility, MeetingfacilityMapper, MeetingfacilityService>(service)
