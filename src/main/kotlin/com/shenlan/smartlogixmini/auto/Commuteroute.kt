package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Commuteroute: BaseModel() {
    var name: String = ""
    var startStopName: String = ""
    var endStopName: String = ""
    var routeType: Int = 0
    val routeTypeCn: String
        get() {
            return when(routeType) {
                0 -> "上班"
                1 -> "下班"
                else -> "未知"
            }
        }
    var sysDeleted: Int = 0
    var estimatedTime: Int = 0  // 预估时间 单位分钟

    var stopList: MutableList<Commuteroutestop> = mutableListOf()
    var vehicles: MutableList<Commutevehicle> = mutableListOf()

    // 关注状态
    var followed: Boolean = false
}

enum class CommuteRouteType(val value: Int) {
    WORK(0),   // 上班
    HOME(1);   // 下班
}

class CommuterouteSearch: BaseSearch() {
    var name: String = ""
    var routeType: Int? = null
    var userId: String = ""
}

@Mapper
interface CommuterouteMapper : BaseMapper<Commuteroute> {
    @Select("""
        <script>
            SELECT * FROM tbl_commuteroute 
            <where>
                AND sysDeleted = 0
                <if test="routeType != null">
                    AND routeType = #{routeType}
                </if>
            </where>
            ORDER BY sysCreated ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commuteroute>

    // 查询所有路线
    @Select("""
        SELECT * FROM tbl_commuteroute WHERE sysDeleted = 0
    """)
    fun getAllRoute(): List<Commuteroute>
}

@Service
class CommuterouteService(mapper: CommuterouteMapper) : BaseService<Commuteroute, CommuterouteMapper>(mapper) {
    override fun save(model: Commuteroute): Result {
        val stopMapper = getBean(CommutestopMapper::class.java)
        val routeStopMapper = getBean(CommuteroutestopMapper::class.java)

        return try {
            // 1.1 保存路线
            super.save(model)

            // 1.2 新增站点信息，只插入不存在的站点
            val stopMap = stopMapper.getList(CommutestopSearch()).associateBy { it.name }.toMutableMap()
            val stopNameSet = stopMap.values.map { it.name }.toSet()
            val newStopList = model.stopList.filter { it.stopName !in stopNameSet }.map { Commutestop(it.stopName, it.lat, it.lng) }
            newStopList.forEach {
                stopMap[it.name] = it
            }
            stopMapper.insertList(newStopList)

            // 1.3 新增路线-站点信息
            routeStopMapper.insertList(model.stopList.map {
                it.routeId = model.id
                it.stopId = stopMap[it.stopName]!!.id
                it
            })

            Result.getSuccess(model.id)
        } catch (e: Exception) {
            log.error("新增路线失败，e: ${e.message}")
            Result.getError(e.message ?: "")
        }
    }

    override fun getList(page: BaseSearch): Result {
        val search = page as CommuterouteSearch

        // 1.1 查询路线列表
        val routes = mapper.getList(search)

        // 1.2 查询所有已发车班车
        val vehicles = getBean(CommutevehicleMapper::class.java).getList(CommutevehicleSearch().apply {
            this.status = CommuteVehicleStatus.ONLINE.value
        }).groupBy { it.routeId }

        // 1.3 查询用户关注路线列表
        val followRoutes = getBean(CommuteuserfollowvehicleMapper::class.java).getList(CommuteuserfollowvehicleSearch().apply {
            this.userId = search.userId
        }).map { it.carId }
        routes.forEach { route -> route.followed = route.id in followRoutes }

        // 1.4 将班车添加到路线中
        routes.forEach { route ->
            route.vehicles.addAll(vehicles.getOrDefault(route.id, listOf()))
        }

        // 返回路线列表结果排序，id在followRoutes里面排在前面
        return Result.getSuccess(routes.sortedBy { if (it.id in followRoutes) 0 else 1 })
    }
}

@RestController
@RequestMapping("/api/commuteroute")
class CommuterouteResource(service: CommuterouteService) : BaseResource<CommuterouteSearch, Commuteroute, CommuterouteMapper, CommuterouteService>(service)
