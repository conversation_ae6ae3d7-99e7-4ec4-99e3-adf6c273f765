package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Commuteroute: BaseModel() {
    var name: String = ""
    var startStopName: String = ""
    var endStopName: String = ""
    var routeType: Int = 0
    val routeTypeCn: String
        get() {
            return when(routeType) {
                0 -> "上班"
                1 -> "下班"
                else -> "未知"
            }
        }
    var sysDeleted: Int = 0
    var estimatedTime: Int = 0  // 预估时间 单位分钟

    var stopList: MutableList<Commuteroutestop> = mutableListOf()
    var vehicles: MutableList<Commutevehicle> = mutableListOf()

    // 关注状态
    var followed: Boolean = false

    // 是否发布
    var published: Boolean = false
}

enum class CommuteRouteType(val value: Int) {
    WORK(0),   // 上班
    HOME(1);   // 下班
}

class CommuterouteSearch: BaseSearch() {
    var name: String = ""
    var routeType: Int? = null
    var userId: String = ""
    var routeIdList = mutableListOf<String>()
    var carIdsList = mutableListOf<String>()
}

@Mapper
interface CommuterouteMapper : BaseMapper<Commuteroute> {
    @Select("""
        <script>
            SELECT * FROM tbl_commuteroute 
            <where>
                AND sysDeleted = 0
                <if test="routeType != null">
                    AND routeType = #{routeType}
                </if>
            </where>
            ORDER BY sysCreated ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commuteroute>

    // 查询所有路线
    @Select("""
        SELECT * FROM tbl_commuteroute WHERE sysDeleted = 0 order by sysCreated desc 
    """)
    fun getAllRoute(): List<Commuteroute>

    // 查询所有已发布的班车
    @Select("""
        SELECT * FROM tbl_commuteroute WHERE sysDeleted = 0 and published = 1 order by sysCreated desc 
    """)
    fun getPublishedRoute(): List<Commuteroute>

    @Update("""
        UPDATE tbl_commuteroute
        SET sysDeleted = 1 and published = 0
        WHERE published = 1
    """)
    fun deletePublishedRoute()

    /**
     * 根据id列表将sysDeleted置为0和published置为1
     */
    @Update("""
        <script>
            UPDATE tbl_commuteroute
            SET sysDeleted = 0, published = 1
            WHERE id IN
            <foreach item="item" index="" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </script>
    """)
    fun updatePublished(@Param("list") routeIdList: List<String>)

    @Update("""
        UPDATE tbl_commuteroute
        SET sysDeleted = 1
        WHERE id = #{id}
    """)
    override fun deleteLogic(id: String): Int
}

@Service
class CommuterouteService(mapper: CommuterouteMapper) : BaseService<Commuteroute, CommuterouteMapper>(mapper) {
    override fun delete(id: String): Result {
        return super.deleteLogic(id)
    }

    override fun getInfo(id: String): Result {
        val route = super.getInfo(id).datas as Commuteroute
        route.stopList.addAll(getBean(CommuteroutestopMapper::class.java).getList(CommuteroutestopSearch().apply { routeId = id }))
        return Result.getSuccess(route)
    }

    override fun save(model: Commuteroute): Result {
        val routeStopMapper = getBean(CommuteroutestopMapper::class.java)

        return try {
            // 1.1 保存路线
            if (model.published) {
                model.id = ""
            }
            super.save(model)

            // 1.2 新增站点信息
//            stopMapper.deleteByIdList(model.stopList.map { it.stopId })
//            val stopList = model.stopList.map { Commutestop("", it.name, it.lat, it.lng) }
//            stopMapper.insertList(stopList)

            // 1.3 新增路线-站点信息
//            routeStopMapper.deleteByRouteId(model.id)
            routeStopMapper.insertList(model.stopList.map { stop ->
                stop.id = uuid()
                stop.routeId = model.id
//                stop.stopId = stopList.find { it.name == stop.name }!!.id
                stop
            })

            Result.getSuccess(model.id)
        } catch (e: Exception) {
            log.error("新增路线失败，e: ${e.message}")
            Result.getError(e.message ?: "新增路线失败")
        }
    }

    override fun getList(page: BaseSearch): Result {
        val search = page as CommuterouteSearch

        // 1.1 查询路线列表
        val routes = mapper.getList(search)

        // 1.2 查询所有已发车班车
        val vehicles = getBean(CommutevehicleMapper::class.java).getList(CommutevehicleSearch().apply {
            this.status = CommuteVehicleStatus.ONLINE.value
        }).groupBy { it.routeId }

        // 1.3 查询用户关注路线列表
        val followRoutes = getBean(CommuteuserfollowvehicleMapper::class.java).getList(CommuteuserfollowvehicleSearch().apply {
            this.userId = search.userId
        }).map { it.carId }
        routes.forEach { route -> route.followed = route.id in followRoutes }

        // 1.4 将班车添加到路线中
        routes.forEach { route ->
            route.vehicles.addAll(vehicles.getOrDefault(route.id, listOf()))
        }

        // 返回路线列表结果排序，id在followRoutes里面排在前面
        return Result.getSuccess(routes.sortedBy { if (it.id in followRoutes) 0 else 1 })
    }

    fun getAll(routeType: Int): List<Commuteroute> {
        val list = mapper.getAllRoute()
        if (routeType == CommuteRouteType.HOME.value) {
            list.forEach {
                val name = it.startStopName
                it.startStopName = it.endStopName
                it.endStopName = name
            }
        }
        return list
    }

    // 正式发布路线
    fun publish(search: CommuterouteSearch): Result {
        // 1. 将当前已发布的路线、班车逻辑删除
        mapper.deletePublishedRoute()
        getBean(CommutevehicleMapper::class.java).deletePublishedRoute()

        // 2. 发布当前路线、班车
        mapper.updatePublished(search.routeIdList)
        getBean(CommutevehicleMapper::class.java).publishVehicle(search.carIdsList)

        return Result.getSuccess("发布成功")
    }
}

@RestController
@RequestMapping("/api/commuteroute")
class CommuterouteResource(service: CommuterouteService) : BaseResource<CommuterouteSearch, Commuteroute, CommuterouteMapper, CommuterouteService>(service) {
    @GetMapping("/getAll/{routeType}")
    fun getAll(@PathVariable routeType: Int): Result {
        return Result.getSuccess(service.getAll(routeType))
    }

    @PostMapping("/publish")
    fun publish(@RequestBody search: CommuterouteSearch): Result {
        return service.publish(search)
    }
}
