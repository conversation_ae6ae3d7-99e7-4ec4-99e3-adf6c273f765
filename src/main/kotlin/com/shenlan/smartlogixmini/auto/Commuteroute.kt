package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.CommuteUtil
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Commuteroute: BaseEntity() {
    var name: String = ""
    var startStopName: String = ""
    var endStopName: String = ""
    var routeType: Int = 0
    val routeTypeCn: String
        get() {
            return when(routeType) {
                0 -> "上班"
                1 -> "下班"
                else -> "未知"
            }
        }
    var sysDeleted: Int = 0
    var estimatedTime: Int = 0  // 预估时间 单位分钟

    var stopList: MutableList<Commutepublishedroutestop> = mutableListOf()
    var vehicles: MutableList<Commutevehicle> = mutableListOf()

    var publishedId: String = ""
    var organizationId: String = ""

    /**
     * 2025-07-24 需求变更，每条路线绑定一辆班车
     */
    var carName: String = ""
}

enum class CommuteRouteType(val value: Int) {
    WORK(0),   // 上班
    HOME(1);   // 下班
}

class CommuterouteSearch: BaseSearch() {
    var name: String = ""
    var routeType: Int? = null
    var userId: String = ""
    var routeIdList = mutableListOf<String>()
    var carIdsList = mutableListOf<String>()
    var organizationId: String = ""
}

@Mapper
interface CommuterouteMapper : BaseMapper<Commuteroute> {
    @Select("""
        <script>
            SELECT * FROM tbl_commuteroute 
            <where>
                AND sysDeleted = 0
                <if test="routeType != null">
                    AND routeType = #{routeType}
                </if>
                <if test="organizationId != ''">
                    AND organizationId = #{organizationId}
                </if>
            </where>
            ORDER BY sysCreated ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commuteroute>

    // 查询所有路线
    @Select("""
        SELECT * FROM tbl_commuteroute WHERE sysDeleted = 0 AND organizationId = #{organizationId} order by sysCreated desc 
    """)
    fun getAllRoute(organizationId: String): List<Commuteroute>

    @Update("""
        UPDATE tbl_commuteroute
        SET sysDeleted = 1
        WHERE id = #{id}
    """)
    override fun deleteLogic(id: String): Int

    @Delete("""
        DELETE FROM tbl_commuteroute WHERE id = #{id}
    """)
    override fun delete(id: String): Int

    // 更新id更新publishedId
    @Update("""
        UPDATE tbl_commuteroute
        SET publishedId = #{publishedId}
        WHERE id = #{id}
    """)
    fun updatePublishedId(id: String, publishedId: String)
}

@Service
class CommuterouteService(mapper: CommuterouteMapper, private val organizationService: OrganizationService) : BaseService<Commuteroute, CommuterouteMapper, CommuterouteSearch>(mapper) {
    override fun delete(id: String): Result {
        getBean(CommuteroutestopMapper::class.java).deleteByRouteId(id)
        return super.delete(id)
    }

    override fun getList(page: CommuterouteSearch): Result {
        page.organizationId = getUser()?.branchOrganizationId ?: return Result.getError("无法获取当前用户所属中心，请联系管理员")
        return super.getList(page)
    }

    override fun getInfo(id: String): Result {
        val route = super.getInfo(id).datas as Commuteroute
        route.stopList.addAll(getBean(CommuteroutestopMapper::class.java).getList(CommuteroutestopSearch().apply {
            routeId = id
        }).sortedBy { it.sequence }.mapIndexed { i, it ->
            Commutepublishedroutestop().apply {
                this.id = it.id
                this.routeId = it.routeId
                this.sequence = i
                this.name = it.name
                this.lat = it.lat
                this.lng = it.lng
                this.sysDeleted = it.sysDeleted
                this.flag = it.flag
            }
        })
        return Result.getSuccess(route)
    }

    override fun save(model: Commuteroute): Result {
        val organizationId = getUser()?.branchOrganizationId ?: return Result.getError("无法获取当前用户所属中心，请联系管理员")
        model.organizationId = organizationId

        val routeStopMapper = getBean(CommuteroutestopMapper::class.java)

        return try {
            // 1.1 保存路线
            super.save(model)

            // 1.2 新增路线-站点信息
            routeStopMapper.deleteByRouteId(model.id)
            routeStopMapper.insertList(model.stopList.map { stop ->
                stop.id = uuid()
                stop.routeId = model.id
                stop
            })

            Result.getSuccess(model.id)
        } catch (e: Exception) {
            log.error("新增路线失败，e: ${e.message}")
            Result.getError(e.message ?: "新增路线失败")
        }
    }

    fun getAll(routeType: Int): List<Commuteroute> {
        val organizationId = getUser()?.branchOrganizationId ?: return listOf()

        val list = mapper.getAllRoute(organizationId)
        if (routeType == CommuteRouteType.HOME.value) {
            list.forEach {
                val name = it.startStopName
                it.startStopName = it.endStopName
                it.endStopName = name
            }
        }
        return list
    }

    // 正式发布路线
    fun publish(search: CommuterouteSearch): Result {
        val organizationId = getUser()?.branchOrganizationId ?: return Result.getError("无法获取当前用户所属中心，请联系管理员")
        CommuteUtil.publishedRoute(organizationId)
//        CommuteUtil.publishVehicle(organizationId)
        return Result.getSuccess("发布成功")
    }
}

@RestController
@RequestMapping("/api/commuteroute")
class CommuterouteResource(service: CommuterouteService) : BaseResource<CommuterouteSearch, Commuteroute, CommuterouteMapper, CommuterouteService>(service) {
    @GetMapping("/getAll/{routeType}")
    fun getAll(@PathVariable routeType: Int): Result {
        return Result.getSuccess(service.getAll(routeType))
    }

    @PostMapping("/publish")
    fun publish(@RequestBody search: CommuterouteSearch): Result {
        return service.publish(search)
    }
}
