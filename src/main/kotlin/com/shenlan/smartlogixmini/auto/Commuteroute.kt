package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getBean
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Commuteroute: BaseModel() {
    var name: String = ""
    var startStopName: String = ""
    var endStopName: String = ""
    var routeType: Int = 0
    val routeTypeCn: String
        get() {
            return when(routeType) {
                0 -> "上班"
                1 -> "下班"
                else -> "未知"
            }
        }
    var sysDeleted: Int = 0

    var stopList: MutableList<Commuteroutestop> = mutableListOf()
    var vehicles: MutableList<Commutevehicle> = mutableListOf()
}

enum class CommuteRouteType(val value: Int) {
    WORK(0),   // 上班
    HOME(1);   // 下班
}

class CommuterouteSearch: BaseSearch() {
    var name: String = ""
    var routeType: Int? = null
}

@Mapper
interface CommuterouteMapper : BaseMapper<Commuteroute> {
    @Select("""
        <script>
            SELECT * FROM tbl_commuteroute 
            <where>
                AND sysDeleted = 0
                <if test="routeType != null">
                    AND routeType = #{routeType}
                </if>
            </where>
            ORDER BY sysCreated ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commuteroute>
}

@Service
class CommuterouteService(mapper: CommuterouteMapper) : BaseService<Commuteroute, CommuterouteMapper>(mapper) {
    override fun getList(page: BaseSearch): Result {
        val search = page as CommuterouteSearch

        // 1.1 查询路线列表
        val routes = mapper.getList(search)

        // 1.2 查询所有已发车班车
        val vehicles = getBean(CommutevehicleMapper::class.java).getList(CommutevehicleSearch().apply {
            this.status = CommuteVehicleStatus.ONLINE.value
        }).groupBy { it.routeId }

        // 1.3 将班车添加到路线中
        routes.forEach { route ->
            route.vehicles.addAll(vehicles.getOrDefault(route.id, listOf()))
        }

        return Result.getSuccess(routes)
    }
}

@RestController
@RequestMapping("/api/commuteroute")
class CommuterouteResource(service: CommuterouteService) : BaseResource<CommuterouteSearch, Commuteroute, CommuterouteMapper, CommuterouteService>(service)
