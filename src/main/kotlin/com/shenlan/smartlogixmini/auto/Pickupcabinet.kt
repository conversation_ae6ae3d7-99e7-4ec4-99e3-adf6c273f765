package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Pickupcabinet: BaseModel() {
    var location: String = ""
    var sysUpdated: Date? = null
    var sysDeleted: Int = 0
}

class PickupcabinetSearch: BaseSearch() {
    var location: String = ""
}

@Mapper
interface PickupcabinetMapper : BaseMapper<Pickupcabinet> {
    @Select("""
        <script>
            SELECT * FROM tbl_pickupcabinet
            <where>
                AND sysDeleted = 0
                <if test="location != ''">
                    AND location LIKE CONCAT('%', #{location}, '%')
                </if>
            </where>
            ORDER BY location ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Pickupcabinet>
}

@Service
class PickupcabinetService(mapper: PickupcabinetMapper) : BaseService<Pickupcabinet, PickupcabinetMapper>(mapper)

@RestController
@RequestMapping("/api/pickupcabinet")
class PickupcabinetResource(service: PickupcabinetService) : BaseResource<PickupcabinetSearch, Pickupcabinet, PickupcabinetMapper, PickupcabinetService>(service)
