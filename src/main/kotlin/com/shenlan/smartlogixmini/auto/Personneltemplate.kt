package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.getUser
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

/**
 * 人员模板关联实体类
 * 用于关联Personnel和Wechatsubscribemessagetemplate，记录订阅信息
 */
class Personneltemplate : BaseEntity() {
    // 数据库表字段

    /** 人员ID */
    var personnelId: String = ""
    /** 微信订阅消息模板ID */
    var templateId: String = ""
    /** 订阅次数 */
    var subscribeCount: Int = 0

    // 关联字段

    /** 关联的人员信息 */
    var personnel: Personnel? = null
    /** 关联的消息模板信息 */
    var messagetemplate: Messagetemplate? = null

    /** 模板名称 */
    var name: String = ""
}

/**
 * 人员模板关联查询条件类
 */
class PersonneltemplateSearch : BaseSearch() {
    /** 人员ID */
    var personnelId: String = ""
    /** 微信订阅消息模板ID */
    var templateId: String = ""
    /** 最小订阅次数 */
    var minSubscribeCount: Int? = null
    /** 最大订阅次数 */
    var maxSubscribeCount: Int? = null
    /** 是否加载人员信息 */
    var loadPersonnel: Boolean = false
    /** 是否加载模板信息 */
    var loadMessagetemplate: Boolean = false
}

/**
 * 人员模板关联Mapper接口
 */
@Mapper
interface PersonneltemplateMapper : BaseMapper<Personneltemplate> {

    @Select("""
        <script>
            SELECT pt.* FROM tbl_personneltemplate pt
            <where>
                pt.sysDeleted = 0
                <if test="personnelId != ''">
                    AND pt.personnelId = #{personnelId}
                </if>
                <if test="templateId != ''">
                    AND pt.templateId = #{templateId}
                </if>
                <if test="minSubscribeCount != null">
                    AND pt.subscribeCount >= #{minSubscribeCount}
                </if>
                <if test="maxSubscribeCount != null">
                    AND pt.subscribeCount &lt;= #{maxSubscribeCount}
                </if>
            </where>
            ORDER BY pt.subscribeCount DESC, pt.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Personneltemplate>

    /**
     * 批量增加订阅次数
     */
    @Update("""
        <script>
            UPDATE tbl_personneltemplate
            SET subscribeCount = subscribeCount + 1
            WHERE templateId = #{templateId} AND sysDeleted = 0
            <if test="personnelIdList.size > 0">
                AND personnelId IN
                <foreach collection="personnelIdList" item="personnelId" open="(" separator="," close=")">
                    #{personnelId}
                </foreach>
            </if>
        </script>
    """)
    fun increaseSubscribeCount(@Param("personnelIdList") personnelIdList: List<String>, templateId: String): Int

    /**
     * 批量减少订阅次数
     */
    @Update("""
        <script>
            UPDATE tbl_personneltemplate
            SET subscribeCount = CASE
                WHEN subscribeCount > 0 THEN subscribeCount - 1
                ELSE 0
            END
            WHERE templateId = #{templateId} AND sysDeleted = 0
            <if test="personnelIdList.size > 0">
                AND personnelId IN
                <foreach collection="personnelIdList" item="personnelId" open="(" separator="," close=")">
                    #{personnelId}
                </foreach>
            </if>
        </script>
    """)
    fun decreaseSubscribeCount(@Param("personnelIdList") personnelIdList: List<String>, templateId: String): Int

    /**
     * 根据人员ID和模板ID查询订阅记录
     */
    @Select("""
        SELECT * FROM tbl_personneltemplate
        WHERE personnelId = #{personnelId} AND templateId = #{templateId} AND sysDeleted = 0
    """)
    fun getInfoByPersonnelIdAndtemplateId(personnelId: String, templateId: String): Personneltemplate?
}

/**
 * 人员模板关联Service类
 */
@Service
class PersonneltemplateService(
    mapper: PersonneltemplateMapper,
    private val personnelMapper: PersonnelMapper,
    private val messagetemplateMapper: MessagetemplateMapper
) : BaseService<Personneltemplate, PersonneltemplateMapper, PersonneltemplateSearch>(mapper) {

    override fun getEntityPage(search: PersonneltemplateSearch): PaginationInfo<Personneltemplate> {
        val paginationInfo = super.getEntityPage(search)
        val personneltemplateList = paginationInfo.result

        // 如果需要加载人员信息
        if (search.loadPersonnel) {
            loadPersonnelBatch(personneltemplateList)
        }

        // 如果需要加载模板信息
        if (search.loadMessagetemplate) {
            loadMessagetemplateBatch(personneltemplateList)
        }

        return paginationInfo.withResult(personneltemplateList)
    }

    override fun getEntity(id: String): Personneltemplate? {
        val personneltemplate = mapper.getInfo(id)
        if (personneltemplate != null) {
            // 加载关联信息
            loadPersonnelBatch(listOf(personneltemplate))
            loadMessagetemplateBatch(listOf(personneltemplate))
        }
        return personneltemplate
    }

    /**
     * 批量加载人员信息
     */
    private fun loadPersonnelBatch(personneltemplateList: List<Personneltemplate>) {
        val personnelIds = personneltemplateList
            .filter { it.personnelId.isNotEmpty() }
            .map { it.personnelId }
            .distinct()

        if (personnelIds.isEmpty()) return

        val personnelList = personnelMapper.getListByIds(personnelIds)
        val personnelMap = personnelList.associateBy { it.id }

        personneltemplateList.forEach { personneltemplate ->
            if (personneltemplate.personnelId.isNotEmpty()) {
                personneltemplate.personnel = personnelMap[personneltemplate.personnelId]
            }
        }
    }

    /**
     * 批量加载消息模板信息
     */
    private fun loadMessagetemplateBatch(personneltemplateList: List<Personneltemplate>) {
        val templateIds = personneltemplateList
            .filter { it.templateId.isNotEmpty() }
            .map { it.templateId }
            .distinct()

        if (templateIds.isEmpty()) return

        val templateList = messagetemplateMapper.getListByIds(templateIds)
        val templateMap = templateList.associateBy { it.id }

        personneltemplateList.forEach { personneltemplate ->
            if (personneltemplate.templateId.isNotEmpty()) {
                personneltemplate.messagetemplate = templateMap[personneltemplate.templateId]
            }
        }
    }

    /**
     * 更新当前用户的订阅次数
     * @param templateId 模板ID
     * @param delta 变化量，正数表示增加，负数表示减少
     */
    @Transactional
    fun updateCurrentUserSubscribeCount(templateId: String, delta: Int): Int {
        val currentUserId = getUser()?.id ?: return 0

        // 先检查记录是否存在，如果不存在就创建
        val existingRecord = mapper.getInfoByPersonnelIdAndtemplateId(currentUserId, templateId)
        if (existingRecord == null) {
            // 创建新记录
            val newRecord = Personneltemplate().apply {
                personnelId = currentUserId
                this.templateId = templateId
                subscribeCount = 0
            }
            saveEntity(newRecord)
        }

        // 根据delta的正负决定调用增加还是减少方法
        return if (delta > 0) {
            // 调用增加方法delta次
            repeat(delta) {
                mapper.increaseSubscribeCount(listOf(currentUserId), templateId)
            }
            delta
        } else if (delta < 0) {
            // 调用减少方法abs(delta)次
            repeat(-delta) {
                mapper.decreaseSubscribeCount(listOf(currentUserId), templateId)
            }
            delta
        } else {
            0
        }
    }

    /**
     * 增加当前用户的订阅次数
     */
    fun increaseCurrentUserSubscribeCount(templateId: String): Int {
        return updateCurrentUserSubscribeCount(templateId, 1)
    }

    /**
     * 减少当前用户的订阅次数
     */
    fun decreaseCurrentUserSubscribeCount(templateId: String): Int {
        return updateCurrentUserSubscribeCount(templateId, -1)
    }

    /**
     * 根据模板名称获取当前用户对应的订阅记录
     */
    @Transactional
    fun getCurrentUserPersonneltemplateByTemplateName(templateName: String): Personneltemplate? {
        val currentUserId = getUser()?.id ?: return null

        // 先根据模板名称获取模板信息
        val messagetemplate = messagetemplateMapper.getInfoByName(templateName) ?: return null

        // 根据当前用户ID和模板ID查询订阅记录
        val existingRecord = mapper.getInfoByPersonnelIdAndtemplateId(currentUserId, messagetemplate.id)

        // 如果记录不存在，创建新记录
        if (existingRecord == null) {
            val newRecord = Personneltemplate().apply {
                personnelId = currentUserId
                templateId = messagetemplate.id
                subscribeCount = 0
            }
            val savedId = saveEntity(newRecord)
            // 返回刚创建的记录
            return mapper.getInfo(savedId)
        }

        return existingRecord
    }

    fun getRepairTemplate():Result {

        var list = ArrayList<Personneltemplate>()
        var repairTemplateNameList = listOf("报修催办提醒", "报修派单通知", "维修取消通知")
        repairTemplateNameList.forEach { templateName ->
            getCurrentUserPersonneltemplateByTemplateName(templateName)?.let {
                it.name = templateName
                list.add(it)
            }
        }
        return Result.getSuccess(list.filter { it.subscribeCount <= 5 })
    }

}

/**
 * 人员模板关联Controller类
 */
@RestController
@RequestMapping("/api/Personneltemplate")
class PersonneltemplateResource(
    service: PersonneltemplateService
) : BaseResource<PersonneltemplateSearch, Personneltemplate, PersonneltemplateMapper, PersonneltemplateService>(service) {

    /**
     * 增加当前用户的订阅次数
     */
    @GetMapping("/increaseCurrentUserSubscribeCount/{templateId}")
    fun increaseCurrentUserSubscribeCount(@PathVariable templateId: String): Result {
        val count = service.increaseCurrentUserSubscribeCount(templateId)
        return if (count > 0) {
            Result.getSuccessInfo("订阅次数增加成功")
        } else {
            Result.getError("订阅次数增加失败")
        }
    }

    /**
     * 减少当前用户的订阅次数
     */
    @GetMapping("/decreaseCurrentUserSubscribeCount/{templateId}")
    fun decreaseCurrentUserSubscribeCount(@PathVariable templateId: String): Result {
        val count = service.decreaseCurrentUserSubscribeCount(templateId)
        return if (count > 0) {
            Result.getSuccessInfo("订阅次数减少成功")
        } else {
            Result.getError("订阅次数减少失败")
        }
    }

    /**
     * 根据模板名称获取当前用户对应的订阅记录
     */
    @GetMapping("/getCurrentUserPersonneltemplate")
    fun getCurrentUserPersonneltemplateByTemplateName(@RequestParam templateName: String): Result {
        val personneltemplate = service.getCurrentUserPersonneltemplateByTemplateName(templateName)
        return Result.getSuccess(personneltemplate)
    }

    /**
     * 获取当前用户对应报修相关的模板的订阅记录
     */
    @GetMapping("/getRepairTemplate")
    fun getRepairTemplate(): Result {
        return service.getRepairTemplate()
    }

}
