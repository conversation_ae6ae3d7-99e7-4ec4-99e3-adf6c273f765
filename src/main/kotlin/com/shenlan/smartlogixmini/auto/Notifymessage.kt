package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Notifymessage: BaseModel() {
    var module: String = ""
    var type: String = ""
    var title: String = ""
    var content: String = ""
    var extra: String = ""
    var sysDeleted: Int = 0
}

// 通知模块枚举
enum class NotifyModule(val value: String) {
    MENU("menu"),
    TAKEOUT("takeout"),
    COMMUTE("commute");

    override fun toString(): String = value
}

class NotifymessageSearch: BaseSearch() {
    var messageId: String = ""
    var userId: String = ""
    var module: String = ""
    var type: String = ""
    var title: String = ""
}

@Mapper
interface NotifymessageMapper : BaseMapper<Notifymessage> {
    @Select("""
        <script>
            SELECT * FROM tbl_notifymessage
            <where>
                AND sysDeleted = 0
                <if test="module != ''">
                    AND module = #{module}
                </if>
                <if test="type != ''">
                    AND type = #{type}
                </if>
                <if test="title != ''">
                    AND title LIKE CONCAT('%', #{title}, '%')
                </if>
            </where>
            ORDER BY sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Notifymessage>

    // 根据userId、module、type查询message，关联tbl_notifyusermessage表
    @Select("""
        <script>
            SELECT nm.* FROM tbl_notifymessage nm
            JOIN (SELECT * FROM tbl_notifyusermessage WHERE userId = #{userId} and pushStatus = 0) num 
            ON nm.id = num.messageId
            WHERE nm.module = #{module} AND nm.type = #{type}
            ORDER BY nm.sysCreated DESC
        </script>
    """)
    fun getListByUserIdAndModuleAndType(@Param("userId") userId: String, @Param("module") module: String, @Param("type") type: String): List<Notifymessage>

    // 更新tbl_notifyusermessage表状态
    @Update("""
        UPDATE tbl_notifyusermessage
        SET pushStatus = #{pushStatus}
        WHERE userId = #{userId} AND messageId = #{messageId}
    """)
    fun updateNotifyMessageStatus(@Param("userId") userId: String, @Param("messageId") messageId: String, @Param("pushStatus") pushStatus: Int): Int
}

@Service
class NotifymessageService(mapper: NotifymessageMapper) : BaseService<Notifymessage, NotifymessageMapper>(mapper) {
    fun getNotifyMessagesByUserIdAndModule(search: NotifymessageSearch): Result {
        return Result.getSuccess(mapper.getListByUserIdAndModuleAndType(search.userId, search.module, "normal"))
    }

    fun updateNotifyMessageStatus(search: NotifymessageSearch): Result {
        return Result.getSuccess(mapper.updateNotifyMessageStatus(search.userId, search.messageId, 1))
    }
}

@RestController
@RequestMapping("/api/notifymessage")
class NotifymessageResource(service: NotifymessageService) : BaseResource<NotifymessageSearch, Notifymessage, NotifymessageMapper, NotifymessageService>(service) {
    /**
     * 根据userId和module获取normal类型的消息
     */
    @PostMapping("/getNotifyMessagesByUserIdAndModule")
    fun getNotifyMessagesByUserIdAndModule(@RequestBody search: NotifymessageSearch): Result {
        return service.getNotifyMessagesByUserIdAndModule(search)
    }

    /**
     * 更新通知的状态
     */
    @PostMapping("/updateNotifyMessageStatus")
    fun updateNotifyMessageStatus(@RequestBody search: NotifymessageSearch): Result {
        return service.updateNotifyMessageStatus(search)
    }
}
