package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.errorlog
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.sqlMapper
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths

/**
 * 维护服务搜索参数
 */
class MaintainSearch : BaseSearch() {
    /** SQL语句 */
    var sql: String = ""
}

@Service
class MaintainService {

    // 注入应用程序名称配置
    @Value("\${spring.application.name}")
    private lateinit var projectName: String

    // 路径配置
    private val basePath = System.getProperty("user.dir")
    private val appUploadPath = Paths.get(AppPro.uploadPath, "app").toString()

    /**
     * 执行SQL语句
     */
    fun executeSql(search: MaintainSearch): Result {
        // 参数验证
        if (search.sql.isEmpty()) {
            return Result.getError("SQL语句不能为空")
        }

        log.info("Executing SQL: ${search.sql}")

        val result = sqlMapper.queryListMap(search.sql)
        return Result.getSuccess(result)
    }

    /**
     * 部署WAR文件
     */
    fun deploy(file: MultipartFile): Result {
        if (file.isEmpty) {
            return Result.getError("上传文件不能为空")
        }

        val originalFilename = file.originalFilename
        if (originalFilename.isNullOrEmpty()) {
            return Result.getError("文件名不能为空")
        }

        log.info("Deploying file: $originalFilename")

        try {
            return when (originalFilename) {
                "$projectName.war" -> deployProjectWar(file)
                else -> Result.getError("仅支持 $projectName.war 文件部署，当前文件: $originalFilename")
            }
        } catch (e: Exception) {
            log.error("Deployment failed: ${e.message}")
            throw e
        }
    }

    /**
     * 部署APP文件
     */
    fun deployApp(file: MultipartFile): Result {
        if (file.isEmpty) {
            return Result.getError("上传文件不能为空")
        }

        log.info("Deploying app file: ${file.originalFilename}")

        try {
            // 确保目录存在
            val appDir = File(appUploadPath)
            if (!appDir.exists()) {
                appDir.mkdirs()
            }

            val targetPath = Paths.get(appUploadPath, "$projectName.apk").toString()
            Files.newOutputStream(Paths.get(targetPath)).use { outputStream ->
                outputStream.write(file.bytes)
            }

            log.info("App deployed successfully to: $targetPath")
            return Result.getSuccessInfo("APP部署成功")
        } catch (e: Exception) {
            log.error("App deployment failed: ${e.message}")
            throw e
        }
    }

    /**
     * 部署项目WAR文件
     */
    private fun deployProjectWar(file: MultipartFile): Result {
        // 保存文件为 ${projectName}_new.war 格式
        val newFileName = "${projectName}_new.war"
        val filePath = Paths.get(basePath, newFileName).toString()

        // 保存上传的文件
        Files.newOutputStream(Paths.get(filePath)).use { outputStream ->
            outputStream.write(file.bytes)
        }
        log.info("War file saved as: $filePath")

        // 根据操作系统选择对应的部署脚本
        val osName = System.getProperty("os.name").toLowerCase()
        val scriptName = if (osName.contains("windows")) "deploy.bat" else "deploy.sh"

        // 在项目根目录查找脚本
        val scriptPath = Paths.get(basePath, scriptName).toString()
        val scriptFile = File(scriptPath)

        // 检查脚本文件是否存在
        if (!scriptFile.exists()) {
            return Result.getError("部署脚本 $scriptName 不存在，请检查脚本路径：$scriptPath")
        }

        // 异步执行部署脚本，避免自杀问题
        executeCommandAsync(scriptPath)

        return Result.getSuccessInfo("项目WAR部署已启动，请稍后检查应用程序状态")
    }

    /**
     * 异步执行系统命令（用于部署脚本，避免自杀问题）
     */
    private fun executeCommandAsync(scriptPath: String) {
        log.info("Starting deployment script asynchronously: $scriptPath")

        Thread {
            try {
                // 延迟2秒再开始执行脚本，确保当前请求已经返回
                Thread.sleep(2000)

                val processBuilder = ProcessBuilder()
                val isLinux = !System.getProperty("os.name").toLowerCase().contains("windows")

                // 根据操作系统设置命令
                if (!isLinux) {
                    // Windows: 使用 start 命令在新窗口中启动，完全独立运行
                    processBuilder.command("cmd", "/c", "start", "/wait", "cmd", "/c", scriptPath)

                    // 设置工作目录
                    processBuilder.directory(File(basePath))

                    // 重定向标准输入，避免交互等待
                    processBuilder.redirectInput(ProcessBuilder.Redirect.from(File("NUL")))
                } else {
                    // Linux/Unix: 使用 nohup 在后台执行脚本
                    val nohupFile = File(basePath, "nohup.out")

                    // 构建 nohup 命令：nohup bash script.sh >> nohup.out 2>&1 &
                    val command = "nohup bash \"$scriptPath\" >> \"${nohupFile.absolutePath}\" 2>&1 &"
                    processBuilder.command("bash", "-c", command)

                    // 设置工作目录
                    processBuilder.directory(File(basePath))

                    // 重定向标准输入输出，让进程完全独立（Java 8兼容方式）
                    processBuilder.redirectInput(ProcessBuilder.Redirect.from(File("/dev/null")))
                    processBuilder.redirectOutput(ProcessBuilder.Redirect.to(File("/dev/null")))
                    processBuilder.redirectError(ProcessBuilder.Redirect.to(File("/dev/null")))
                }

                processBuilder.start()
                log.info("Deployment script command executed")

            } catch (e: Exception) {
                log.error("Failed to start deployment script: ${e.message}")
                errorlog(e)
            }
        }.start()
    }
}

@RestController
@RequestMapping("/api/maintain")
class MaintainResource(private val service: MaintainService) {

    @PostMapping("/executeSql")
    fun executeSql(@RequestBody search: MaintainSearch): Result = service.executeSql(search)

    @PostMapping("/deploy")
    fun deploy(@RequestParam("file") file: MultipartFile): Result = service.deploy(file)

    @PostMapping("/deployApp")
    fun deployApp(@RequestParam("file") file: MultipartFile): Result = service.deployApp(file)
}
