package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 字典主表实体类
 */
class Dictionary : BaseEntity() {
    // 数据库表字段

    /** 字典编码 */
    var dictCode: String = ""
    /** 字典名称 */
    var dictName: String = ""
    /** 描述信息 */
    var description: String = ""
    /** 排序顺序 */
    var sortOrder: Int = 0

    // 关联字段

    /** 字典详情列表 */
    var dictDetailList = listOf<Dictdetail>()
}

class DictionarySearch : BaseSearch() {
    /** 字典编码 */
    var dictCode: String = ""
}


@Mapper
interface DictionaryMapper : BaseMapper<Dictionary> {
    @Select("""
    <script>
        SELECT *
        FROM tbl_dictionary
        <where>
            <if test="and dictCode != ''">
                AND dictcode = #{dictCode}
            </if>
        </where>
        ORDER BY sortOrder, dictcode
    </script>
    """)
    override fun getList(search: BaseSearch): List<Dictionary>

    @Select("""
        SELECT *
        FROM tbl_dictionary 
        WHERE dictcode = #{dictCode}
        LIMIT 1
    """)
    fun getInfoByCode(dictCode: String): Dictionary?
}

@Service
class DictionaryService(
    mapper: DictionaryMapper,
    private val dictdetailMapper: DictdetailMapper
) : BaseService<Dictionary, DictionaryMapper, DictionarySearch>(mapper) {

    /**
     * 根据字典编码获取字典详情
     */
    @Cacheable("dictionary", key = "#dictCode")
    fun getInfoByCode(dictCode: String): Dictionary? {
        return try {
            log.info("Query dictionary by code: $dictCode")
            val dictionary = mapper.getInfoByCode(dictCode) ?: return null

            dictionary.dictDetailList = dictdetailMapper.getList(DictdetailSearch(dictCode = dictCode))
            dictionary
        } catch (e: Exception) {
            log.error("Failed to query dictionary: ${e.message}")
            null
        }
    }
}

@RestController
@RequestMapping("/api/Dictionary")
class DictionaryResource(
    service: DictionaryService
) : BaseResource<DictionarySearch, Dictionary, DictionaryMapper, DictionaryService>(service) {

    @GetMapping("/getInfoByCode")
    fun getInfoByCode(dictCode: String): Result {
        return try {
            val dictionary = service.getInfoByCode(dictCode)
            if (dictionary != null) {
                Result.getSuccess(dictionary)
            } else {
                Result.getError("字典不存在")
            }
        } catch (e: Exception) {
            log.error("Failed to get dictionary by code: ${e.message}")
            Result.getError("查询字典失败")
        }
    }
}
