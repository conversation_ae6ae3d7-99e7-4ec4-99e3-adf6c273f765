package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.ImageUtil.updateImageDishId
import com.shenlan.smartlogixmini.util.MenuUtil
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Takeoutdish: BaseEntity {
    var name: String = ""
    var price: Double = 0.0
    var portionUnit: Int = 0
    var limitPerPerson: Int = 0
    var imageId: String = ""
    var sysDeleted: Int = 0

    var imageUrl: String = ""
        get() = if (field.isEmpty()) field else AppPro.imgPrefix + field

    var images: List<Dishimage> = listOf()

    var organizationId: String = ""

    constructor()

    constructor(dishId: String, dishName: String, price: Double, portionUnit: Int, limitPerPerson: Int, imageId: String, organizationId: String) {
        this.id = dishId
        this.name = dishName
        this.price = price
        this.portionUnit = portionUnit
        this.limitPerPerson = limitPerPerson
        this.imageId = imageId
        this.organizationId = organizationId
    }
}

class TakeoutdishSearch: BaseSearch() {
    var name: String = ""
    var organizationId: String = ""
}

@Mapper
interface TakeoutdishMapper : BaseMapper<Takeoutdish> {
    @Select(
        """
            select d.*, di.imageUrl FROM tbl_takeoutdish d left join tbl_dishimage di on d.imageId = di.id
            where d.id = #{id}
        """
    )
    override fun getInfo(id: String): Takeoutdish?

    @Select("""
        <script>
            SELECT d.*, di.imageUrl FROM tbl_takeoutdish d left join tbl_dishimage di on d.imageId = di.id
            <where>
                AND d.sysDeleted = 0
                <if test="name != ''">
                    AND d.name LIKE CONCAT('%', #{name}, '%')
                </if>
                <if test="organizationId != ''">
                    AND d.organizationId = #{organizationId}
                </if>
            </where>
            ORDER BY d.sysCreated desc
        </script>
    """)
    override fun getList(search: BaseSearch): List<Takeoutdish>

    @Update("""
        UPDATE tbl_takeoutdish
        SET sysDeleted = 1
        WHERE id = #{id}
    """)
    override fun deleteLogic(id: String): Int
}

@Service
class TakeoutdishService(
    mapper: TakeoutdishMapper,
    private val organizationService: OrganizationService
) : BaseService<Takeoutdish, TakeoutdishMapper, TakeoutdishSearch>(mapper) {
    override fun getList(page: TakeoutdishSearch): Result {
        val organizationId = getUser()?.branchOrganizationId ?: return Result.getError("无法获取当前用户所属中心，请联系管理员")
        page.organizationId = organizationId

        return super.getList(page)
    }
    override fun getInfo(id: String): Result {
        val dish = super.getInfo(id).datas as Takeoutdish?
        dish?.let {
            it.images = getBean(DishimageMapper::class.java).getList(DishimageSearch().apply { dishId = it.id }).map { image ->
                image.imageUrl = AppPro.imgPrefix + image.imageUrl
                image
            }
        }
        return Result.getSuccess(dish)
    }

    override fun delete(id: String): Result {
        return Result.getSuccess(mapper.deleteLogic(id))
    }

    override fun save(model: Takeoutdish): Result {
        val organizationId = getUser()?.branchOrganizationId ?: return Result.getError("无法获取当前用户所属中心，请联系管理员")
        model.organizationId = organizationId

        val checkDishNameExistsTips = MenuUtil.checkDishNameExists(model)
        require(checkDishNameExistsTips.isEmpty()) { return Result.getError(checkDishNameExistsTips, checkDishNameExistsTips) }
        log.info("save takeout dish: ${model.toJsonString}")
        super.save(model)
        getBean(DishimageMapper::class.java).clearDishId(model.id)
        updateImageDishId(model.imageId, model.id)
        model.images.forEach { image ->
            updateImageDishId(image.id, model.id)
        }
        MenuUtil.syncDish(model)
        return Result.getSuccess(model.id)
    }
}

@RestController
@RequestMapping("/api/takeoutdish")
class TakeoutdishResource(service: TakeoutdishService) : BaseResource<TakeoutdishSearch, Takeoutdish, TakeoutdishMapper, TakeoutdishService>(service)
