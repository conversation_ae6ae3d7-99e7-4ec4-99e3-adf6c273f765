package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Takeoutdish: BaseModel {
    var name: String = ""
    var price: Double = 0.0
    var portionUnit: Int = 0
    var limitPerPerson: Int = 0
    var imageId: String = ""
    var sysDeleted: Int = 0

    var imageUrl: String = ""
        get() = if (field.isEmpty()) field else AppPro.imgPrefix + field

    constructor()

    constructor(dishId: String, dishName: String, price: Double, portionUnit: Int, limitPerPerson: Int, imageId: String) {
        this.id = dishId
        this.name = dishName
        this.price = price
        this.portionUnit = portionUnit
        this.limitPerPerson = limitPerPerson
        this.imageId = imageId
    }
}

class TakeoutdishSearch: BaseSearch() {
    var name: String = ""
}

@Mapper
interface TakeoutdishMapper : BaseMapper<Takeoutdish> {
    @Select(
        """
            select d.*, di.imageUrl FROM tbl_takeoutdish d left join tbl_dishimage di on d.imageId = di.id
            where d.id = #{id}
        """
    )
    override fun getInfo(id: String): Takeoutdish?

    @Select("""
        <script>
            SELECT d.*, di.imageUrl FROM tbl_takeoutdish d left join tbl_dishimage di on d.imageId = di.id
            <where>
                <if test="name != ''">
                    AND d.name LIKE CONCAT('%', #{name}, '%')
                </if>
            </where>
            ORDER BY d.sysCreated desc
        </script>
    """)
    override fun getList(search: BaseSearch): List<Takeoutdish>

    @Update("""
        UPDATE tbl_takeoutdish
        SET sysDeleted = 1
        WHERE id = #{id}
    """)
    override fun deleteLogic(id: String): Int
}

@Service
class TakeoutdishService(mapper: TakeoutdishMapper) : BaseService<Takeoutdish, TakeoutdishMapper>(mapper) {
    override fun delete(id: String): Result {
        return Result.getSuccess(mapper.deleteLogic(id))
    }
}

@RestController
@RequestMapping("/api/takeoutdish")
class TakeoutdishResource(service: TakeoutdishService) : BaseResource<TakeoutdishSearch, Takeoutdish, TakeoutdishMapper, TakeoutdishService>(service)
