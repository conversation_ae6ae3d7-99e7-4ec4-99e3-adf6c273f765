package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Commutepublishedroute: BaseModel() {
    var name: String = ""
    var startStopName: String = ""
    var endStopName: String = ""
    var routeType: Int = 0
    var estimatedTime: Int = 0
    var sysDeleted: Int = 0
}

class CommutepublishedrouteSearch: BaseSearch() {
    var name: String = ""
    var startStopName: String = ""
    var endStopName: String = ""
    var routeType: Int? = null
}

@Mapper
interface CommutepublishedrouteMapper : BaseMapper<Commutepublishedroute> {
    @Select("""
        SELECT * FROM tbl_commutepublishedroute WHERE id = #{id}
    """)
    override fun getInfo(id: String): Commutepublishedroute?

    @Select("""
        <script>
            SELECT * FROM tbl_commutepublishedroute
            <where>
                AND sysDeleted = 0
                <if test="name != ''">
                    AND name LIKE CONCAT('%', #{name}, '%')
                </if>
                <if test="startStopName != ''">
                    AND startStopName LIKE CONCAT('%', #{startStopName}, '%')
                </if>
                <if test="endStopName != ''">
                    AND endStopName LIKE CONCAT('%', #{endStopName}, '%')
                </if>
                <if test="routeType != null">
                    AND routeType = #{routeType}
                </if>
            </where>
            ORDER BY routeType ASC, name ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutepublishedroute>

    @Select("""
        SELECT * FROM tbl_commutepublishedroute WHERE sysDeleted = 0 order by sysCreated desc 
    """)
    fun getAllRoute(): List<Commutepublishedroute>

    @Update("""
        UPDATE tbl_commutepublishedroute
        SET sysDeleted = 1
        WHERE sysDeleted = 0
    """)
    fun deleteLogicAll()
}

@Service
class CommutepublishedrouteService(mapper: CommutepublishedrouteMapper) : BaseService<Commutepublishedroute, CommutepublishedrouteMapper>(mapper) {
    fun getAll(routeType: Int): List<Commutepublishedroute> {
        val list = mapper.getAllRoute()
        if (routeType == CommuteRouteType.HOME.value) {
            list.forEach {
                val name = it.startStopName
                it.startStopName = it.endStopName
                it.endStopName = name
            }
        }
        return list
    }
}

@RestController
@RequestMapping("/api/commutepublishedroute")
class CommutepublishedrouteResource(service: CommutepublishedrouteService) : BaseResource<CommutepublishedrouteSearch, Commutepublishedroute, CommutepublishedrouteMapper, CommutepublishedrouteService>(service) {
    @GetMapping("/getAll/{routeType}")
    fun getAll(@PathVariable routeType: Int): Result {
        return Result.getSuccess(service.getAll(routeType))
    }
}
