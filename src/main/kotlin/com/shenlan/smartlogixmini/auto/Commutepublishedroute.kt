package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getBean
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Commutepublishedroute: BaseEntity() {
    var name: String = ""
    var startStopName: String = ""
    var endStopName: String = ""
    var routeType: Int = 0
    var estimatedTime: Int = 0
    var sysDeleted: Int = 0
}

class CommutepublishedrouteSearch: BaseSearch() {
    var name: String = ""
    var startStopName: String = ""
    var endStopName: String = ""
    var routeType: Int? = null
}

@Mapper
interface CommutepublishedrouteMapper : BaseMapper<Commutepublishedroute> {
    @Select("""
        SELECT * FROM tbl_commutepublishedroute WHERE id = #{id}
    """)
    override fun getInfo(id: String): Commutepublishedroute?

    @Select("""
        <script>
            SELECT * FROM tbl_commutepublishedroute
            <where>
                AND sysDeleted = 0
                <if test="name != ''">
                    AND name LIKE CONCAT('%', #{name}, '%')
                </if>
                <if test="startStopName != ''">
                    AND startStopName LIKE CONCAT('%', #{startStopName}, '%')
                </if>
                <if test="endStopName != ''">
                    AND endStopName LIKE CONCAT('%', #{endStopName}, '%')
                </if>
                <if test="routeType != null">
                    AND routeType = #{routeType}
                </if>
            </where>
            ORDER BY routeType ASC, name ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutepublishedroute>

    @Select("""
        SELECT * FROM tbl_commutepublishedroute WHERE sysDeleted = 0 order by sysCreated desc 
    """)
    fun getAllRoute(): List<Commutepublishedroute>

    @Update("""
        UPDATE tbl_commutepublishedroute
        SET sysDeleted = 1
        WHERE sysDeleted = 0
    """)
    fun deleteLogicAll()
}

@Service
class CommutepublishedrouteService(
    mapper: CommutepublishedrouteMapper,
    private val commuteVehicleService: CommutevehicleService
) : BaseService<Commutepublishedroute, CommutepublishedrouteMapper, CommutepublishedrouteSearch>(mapper) {
    override fun getList(page: CommutepublishedrouteSearch): Result {
        val search = page

        // 1.1 查询路线列表
        val routes = mapper.getList(search)

        // 1.2 查询所有已发车班车
        val vehicles = commuteVehicleService.getList(CommutevehicleSearch().apply {
            ifPage = false
            routeType = search.routeType
        })

        // 1.3 查询用户关注路线列表
        val followRoutes = getBean(CommuteuserfollowvehicleMapper::class.java).getList(CommuteuserfollowvehicleSearch().apply {
            this.userId = search.userId
        }).map { it.carId }
        routes.forEach { route -> route.followed = route.id in followRoutes }

        // 1.4 将班车添加到路线中
        routes.forEach { route ->
            route.vehicles.addAll(vehicles.getOrDefault(route.id, listOf()))
        }

        // 返回路线列表结果排序，id在followRoutes里面排在前面
        return Result.getSuccess(routes.sortedBy { if (it.id in followRoutes) 0 else 1 })
    }

    fun getAll(routeType: Int): List<Commutepublishedroute> {
        val list = mapper.getAllRoute()
        if (routeType == CommuteRouteType.HOME.value) {
            list.forEach {
                val name = it.startStopName
                it.startStopName = it.endStopName
                it.endStopName = name
            }
        }
        return list
    }
}

@RestController
@RequestMapping("/api/commutepublishedroute")
class CommutepublishedrouteResource(service: CommutepublishedrouteService) : BaseResource<CommutepublishedrouteSearch, Commutepublishedroute, CommutepublishedrouteMapper, CommutepublishedrouteService>(service) {
    @GetMapping("/getAll/{routeType}")
    fun getAll(@PathVariable routeType: Int): Result {
        return Result.getSuccess(service.getAll(routeType))
    }
}
