package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.Application
import com.shenlan.smartlogixmini.config.LoginCode
import com.shenlan.smartlogixmini.util.AliyunSmsUtil
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.beans.factory.annotation.Value
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*
import kotlin.system.exitProcess

/**
 * SQL操作Mapper接口
 * 提供动态SQL执行功能
 */
@Mapper
interface SqlMapper {

    @Select("\${sql}")
    fun queryInt(@Param("sql") sql: String): Int?

    @Select("\${sql}")
    fun queryStr(@Param("sql") sql: String): String?

    @Select("\${sql}")
    fun queryMap(@Param("sql") sql: String): HashMap<String, String>

    @Update("\${sql}")
    fun update(@Param("sql") sql: String)

    @Select("\${sql}")
    fun queryListMap(@Param("sql") sql: String): List<HashMap<String, Any>>

    @Select("\${sql}")
    fun <T> queryList(@Param("sql") sql: String): List<T>

    @Select("\${sql}")
    fun <T> query(@Param("sql") sql: String): T?
}

/**
 * 开放接口Mapper
 * 处理用户认证和权限相关查询
 */
@Mapper
interface OpenMapper {

    @Select("SELECT count(1) FROM tbl_userInfo WHERE phone = #{mobile}")
    fun countUserByMobile(mobile: String): Int?

    @Select("SELECT JsonValue FROM tbl_config WHERE configCode = 'APP_VERSION'")
    fun getAppVersion(): String?
}

/**
 * 用户相关接口
 */
@RestController
@RequestMapping("/api")
class UserResource {

    @GetMapping("/csrf")
    fun csrf(): Result = Result.success
}

/**
 * 开放接口控制器
 * 处理无需认证的公开接口
 */
@RestController
@RequestMapping("/api/open")
class OpenResource(
    private val aliyunSmsUtil: AliyunSmsUtil,
    private val dictdetailService: DictdetailService
) {
    @Value("\${spring.profiles.active}")
    private lateinit var active: String

    @Value("\${aliyun.sms.template.verification-code}")
    private lateinit var verificationCodeTemplate: String

    @GetMapping("/test")
    fun test(): Result {
        log.warn("Test endpoint accessed - warn level")
        log.error("Test endpoint accessed - error level")
        return Result.success
    }

    @GetMapping("/csrf")
    fun csrf(): Result = Result.success

    @GetMapping("shutdown")
    fun shutdown() {
        log.info("Application shutdown requested")
        exitProcess(1)
    }

    @GetMapping("getAccount")
    fun getAccount(): Result {
        return Result.getSuccess(getUser())
    }

    @GetMapping("getAllDict")
    fun getAllDict(): Result {
        // 刷新缓存并获取所有字典数据
        dictdetailService.refreshCache()
        val allDictList = getBean(DictdetailMapper::class.java).getList(DictdetailSearch())
        return Result.getSuccess(allDictList)
    }

    /**
     * 获取验证码接口
     * @param phone 手机号码
     * @return 验证码发送结果
     */
    @GetMapping("/getCode/{phone}")
    fun getCode(@PathVariable phone: String): Result {
        try {
            // 验证手机号格式
            if (!isValidPhoneNumber(phone)) {
                return Result.getError("手机号格式不正确")
            }

            // 检查手机号是否存在于Personnel表中
            val personnelMapper = getBean(PersonnelMapper::class.java)
            val personnel = personnelMapper.getInfoByPhone(phone)

            if (personnel == null) {
                return Result.getError("该手机号未关联到任何人员信息")
            }

            // 生成验证码
            val code = (Random().nextInt(9000) + 1000).toString()

            // 发送短信
            val params = mapOf("code" to code)
            val smsResult = aliyunSmsUtil.sendSms(phone, params, verificationCodeTemplate)
            if (!smsResult) {
                return Result.getError("验证码发送失败，请稍后重试")
            }

            // 保存验证码
            LoginCode.loginCodeMap[phone] = LoginCode(code, System.currentTimeMillis())
            log.info("Verification code sent successfully to: {}", phone)

            return Result.getSuccessInfo("验证码已发送，请注意查收")

        } catch (e: Exception) {
            log.error("Error occurred while sending verification code to: {}", phone)
            throw e
        }
    }

    /**
     * 验证登录接口
     * @param phone 手机号码
     * @param code 验证码
     * @return 验证结果
     */
    @GetMapping("/checkLogin/{phone}/{code}")
    fun checkLogin(@PathVariable phone: String, @PathVariable code: String): Result {
        try {
            // 验证手机号格式
            if (!isValidPhoneNumber(phone)) {
                return Result.getError("手机号格式不正确")
            }

            // 验证验证码格式
            if (code.isBlank()) {
                return Result.getError("验证码不能为空")
            }

            // 检查手机号是否存在于Personnel表中
            val personnelMapper = getBean(PersonnelMapper::class.java)
            val personnel = personnelMapper.getInfoByPhone(phone)

            if (personnel == null) {
                return Result.getError("该手机号未关联到任何人员信息")
            }

            // 超级管理员特殊验证码
            if (active != "prod" && code == "1234") {
                log.info("Admin login with special code for: {}", phone)
                LoginCode.loginCodeMap[phone] = LoginCode("1234", System.currentTimeMillis())
                return Result.success
            }

            // 常规验证码验证
            val loginCode = LoginCode.loginCodeMap[phone]
            return when {
                loginCode == null -> {
                    log.warn("No verification code found for: {}", phone)
                    Result.getError("请先获取验证码")
                }
                loginCode.code != code -> {
                    log.warn("Verification code mismatch for: {}", phone)
                    Result.getError("验证码不匹配")
                }
                loginCode.isTimeOut() -> {
                    log.warn("Verification code expired for: {}", phone)
                    LoginCode.loginCodeMap.remove(phone) // 清除过期验证码
                    Result.getError("验证码已失效，请重新获取验证码")
                }
                else -> {
                    log.info("Login verification successful for: {}", phone)
                    Result.success
                }
            }

        } catch (e: Exception) {
            log.error("Error occurred during login verification for: {}", phone)
            throw e
        }
    }

    @GetMapping("/getAppVersion")
    fun getAppVersion(): Result {
        try {
            val version = getBean(OpenMapper::class.java).getAppVersion()
            return Result.getSuccess(version)
        } catch (e: Exception) {
            log.error("Error occurred while getting app version")
            throw e
        }
    }

    /**
     * 验证手机号格式
     * @param phoneNumber 手机号
     * @return 是否有效
     */
    private fun isValidPhoneNumber(phoneNumber: String): Boolean {
        // 简单的手机号格式验证（11位数字）
        return phoneNumber.matches(Regex("^1[3-9]\\d{9}$"))
    }
}

/**
 * 运维操作接口
 */
@RestController
@RequestMapping("/api/operator")
class OperatorResource {

    @GetMapping("getStatus")
    fun getStatus(): Result = Result.success

    @GetMapping("shutdown")
    fun shutdown() {
        log.info("Application shutdown requested by operator")
        Application.context?.close()
    }
}
