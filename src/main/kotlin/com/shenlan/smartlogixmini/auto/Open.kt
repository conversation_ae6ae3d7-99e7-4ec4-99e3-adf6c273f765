package com.shenlan.smartlogixmini.auto

import com.jayway.jsonpath.Configuration
import com.jayway.jsonpath.JsonPath
import com.jayway.jsonpath.Option
import com.shenlan.smartlogixmini.Application
import com.shenlan.smartlogixmini.config.LoginCode
import com.shenlan.smartlogixmini.config.LoginMethod
import com.shenlan.smartlogixmini.util.*
import okhttp3.OkHttpClient
import okhttp3.Request
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.beans.factory.annotation.Value
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.system.exitProcess

/** 验证码接口结果状态枚举 */
enum class VerificationCodeStatus(override val code: Int, override val description: String) : ResultStatus {
    // 成功状态
    SUCCESS(0, "成功"),

    // 参数验证错误
    INVALID_PHONE_FORMAT(1001, "手机号格式不正确"),
    VERIFICATION_CODE_EMPTY(1002, "验证码不能为空"),
    WECHAT_CODE_EMPTY(1003, "code参数不能为空"),

    // 业务逻辑错误
    PHONE_NOT_REGISTERED(2001, "手机号未关联人员"),
    VERIFICATION_CODE_NOT_FOUND(2002, "验证码未找到"),
    VERIFICATION_CODE_MISMATCH(2003, "验证码不匹配"),
    VERIFICATION_CODE_EXPIRED(2004, "验证码已过期"),
    VERIFICATION_CODE_EXCEEDED_LIMIT(2005, "验证码尝试次数过多"),

    // 外部服务错误
    SMS_SEND_FAILED(3001, "短信发送失败"),
    WECHAT_API_ERROR(3002, "微信接口错误"),

    // 系统错误
    SYSTEM_ERROR(9001, "系统错误")
}

/**
 * SQL操作Mapper接口
 * 提供动态SQL执行功能
 */
@Mapper
interface SqlMapper {

    @Select("\${sql}")
    fun queryInt(@Param("sql") sql: String): Int?

    @Select("\${sql}")
    fun queryStr(@Param("sql") sql: String): String?

    @Select("\${sql}")
    fun queryMap(@Param("sql") sql: String): HashMap<String, String>

    @Update("\${sql}")
    fun update(@Param("sql") sql: String)

    @Select("\${sql}")
    fun queryListMap(@Param("sql") sql: String): List<HashMap<String, Any>>

    @Select("\${sql}")
    fun <T> queryList(@Param("sql") sql: String): List<T>

    @Select("\${sql}")
    fun <T> query(@Param("sql") sql: String): T?
}

/**
 * 开放接口Mapper
 * 处理用户认证和权限相关查询
 */
@Mapper
interface OpenMapper {

    @Select("SELECT count(1) FROM tbl_userInfo WHERE phone = #{mobile}")
    fun countUserByMobile(mobile: String): Int?

    @Select("SELECT JsonValue FROM tbl_config WHERE configCode = 'APP_VERSION'")
    fun getAppVersion(): String?
}

/**
 * 用户相关接口
 */
@RestController
@RequestMapping("/api")
class UserResource {

    @GetMapping("/csrf")
    fun csrf(): Result = Result.success
}

/**
 * 开放接口控制器
 * 处理无需认证的公开接口
 */
@RestController
@RequestMapping("/api/open")
class OpenResource(
    private val aliyunSmsUtil: AliyunSmsUtil,
    private val dictdetailService: DictdetailService,
    private val wechatAccessTokenUtil: WechatAccessTokenUtil
) {
    @Value("\${spring.profiles.active}")
    private lateinit var active: String

    @Value("\${aliyun.sms.template.verification-code}")
    private lateinit var verificationCodeTemplate: String

    // HTTP客户端
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .build()

    @GetMapping("/test")
    fun test(): Result {
        log.warn("Test endpoint accessed - warn level")
        log.error("Test endpoint accessed - error level")
        return Result.success
    }

    @GetMapping("/csrf")
    fun csrf(): Result = Result.success

    @GetMapping("shutdown")
    fun shutdown() {
        log.info("Application shutdown requested")
        exitProcess(1)
    }

    @GetMapping("getAccount")
    fun getAccount(): Result {
        return Result.getSuccess(getUser())
    }

    @GetMapping("getAllDict")
    fun getAllDict(): Result {
        // 刷新缓存并获取所有字典数据
        dictdetailService.refreshCache()
        val allDictList = getBean(DictdetailMapper::class.java).getList(DictdetailSearch())
        return Result.getSuccess(allDictList)
    }

    /**
     * 获取验证码接口
     * @param phone 手机号码
     * @return 验证码发送结果
     */
    @GetMapping("/getCode/{phone}")
    fun getCode(@PathVariable phone: String): Result {
        try {
            // 验证手机号格式
            if (!isValidPhoneNumber(phone)) {
                return Result.getError(VerificationCodeStatus.INVALID_PHONE_FORMAT)
            }

            // 检查手机号是否存在于Personnel表中
            val personnelMapper = getBean(PersonnelMapper::class.java)
            val personnel = personnelMapper.getInfoByPhone(phone)

            if (personnel == null) {
                return Result.getError(VerificationCodeStatus.PHONE_NOT_REGISTERED)
            }

            // 生成验证码
            val code = (Random().nextInt(9000) + 1000).toString()

            // 发送短信
            val params = mapOf("code" to code)
            val smsResult = aliyunSmsUtil.sendSms(phone, params, verificationCodeTemplate)
            if (!smsResult) {
                return Result.getError(VerificationCodeStatus.SMS_SEND_FAILED)
            }

            // 保存验证码
            LoginCode.loginCodeMap[phone] = LoginCode(code, System.currentTimeMillis(), LoginMethod.PHONE_CODE)
            log.info("Verification code sent successfully to: {}", phone)

            return Result.getSuccessInfo("验证码已发送，请注意查收")

        } catch (e: Exception) {
            log.error("Error occurred while sending verification code to: {}", phone)
            throw e
        }
    }

    @GetMapping("/getAppVersion")
    fun getAppVersion(): Result {
        try {
            val version = getBean(OpenMapper::class.java).getAppVersion()
            return Result.getSuccess(version)
        } catch (e: Exception) {
            log.error("Error occurred while getting app version")
            throw e
        }
    }

    /**
     * 获取微信手机号用于登录接口
     * @param code 微信小程序code
     * @return 登录结果
     */
    @GetMapping("/getWechatPhoneForLogin/{code}")
    fun getWechatPhoneForLogin(@PathVariable code: String): Result {
        try {
            if (code.isEmpty()) {
                return Result.getError(VerificationCodeStatus.WECHAT_CODE_EMPTY)
            }

            log.info("Attempting to get WeChat phone for login with code: $code")

            // 获取微信access_token
            val accessToken = wechatAccessTokenUtil.getAccessToken()

            // 创建安全的JsonPath配置
            val safeConfig: Configuration = Configuration.builder()
                .options(Option.DEFAULT_PATH_LEAF_TO_NULL)
                .options(Option.SUPPRESS_EXCEPTIONS)
                .build()

            // 调用微信获取手机号接口
            val apiUrl = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=$accessToken"
            val requestBody = okhttp3.RequestBody.create(
                okhttp3.MediaType.parse("application/json"),
                """{"code":"$code"}"""
            )

            val wechatRequest = Request.Builder()
                .url(apiUrl)
                .post(requestBody)
                .build()

            httpClient.newCall(wechatRequest).execute().use { response ->
                if (!response.isSuccessful) {
                    log.error("Failed to call WeChat API: ${response.code()}")
                    return Result.getError(VerificationCodeStatus.WECHAT_API_ERROR)
                }

                val responseBody = response.body()?.string()
                if (responseBody.isNullOrEmpty()) {
                    log.error("Empty response body from WeChat API")
                    return Result.getError(VerificationCodeStatus.WECHAT_API_ERROR)
                }

                log.info("WeChat API response: $responseBody")

                // 使用JsonPath解析响应
                val jsonContext = JsonPath.using(safeConfig).parse(responseBody)

                // 检查错误码
                val errcode = jsonContext.read<Int>("$.errcode")
                if (errcode != 0) {
                    val errmsg = jsonContext.read<String>("$.errmsg") ?: "未知错误"
                    log.error("WeChat API error: $errcode - $errmsg")
                    return Result.getError(VerificationCodeStatus.WECHAT_API_ERROR, "微信接口错误: $errmsg")
                }

                // 获取手机号
                val phoneNumber = jsonContext.read<String>("$.phone_info.phoneNumber")
                if (phoneNumber.isNullOrEmpty()) {
                    log.error("No phone number in WeChat response")
                    return Result.getError(VerificationCodeStatus.WECHAT_API_ERROR)
                }

                log.info("Retrieved phone number from WeChat: $phoneNumber")

                // 验证手机号格式
                if (!isValidPhoneNumber(phoneNumber)) {
                    log.error("Invalid phone number format: $phoneNumber")
                    return Result.getError(VerificationCodeStatus.INVALID_PHONE_FORMAT)
                }

                // 查询数据库中对应的人员信息
                val personnelMapper = getBean(PersonnelMapper::class.java)
                val personnel = personnelMapper.getInfoByPhone(phoneNumber)

                if (personnel == null) {
                    log.warn("No personnel found for phone: $phoneNumber")
                    return Result.getError(VerificationCodeStatus.PHONE_NOT_REGISTERED)
                }

                log.info("Found personnel for phone $phoneNumber: ${personnel.name}")

                // 生成登录验证码用于Spring Security验证
                val loginCode = uuid()
                LoginCode.loginCodeMap[phoneNumber] = LoginCode(loginCode, System.currentTimeMillis(), LoginMethod.WECHAT_PHONE)

                // 返回成功结果，前端需要使用phoneNumber和生成的验证码调用/api/authentication
                return Result.getSuccess(mapOf(
                    "phoneNumber" to phoneNumber,
                    "loginCode" to loginCode,
                    "personnel" to personnel
                ))
            }

        } catch (e: Exception) {
            log.error("Error occurred while getting WeChat phone for login", e)
            return Result.getError(VerificationCodeStatus.SYSTEM_ERROR, "获取微信手机号失败: ${e.message}")
        }
    }

    /**
     * 验证手机号格式
     * @param phoneNumber 手机号
     * @return 是否有效
     */
    private fun isValidPhoneNumber(phoneNumber: String): Boolean {
        // 简单的手机号格式验证（11位数字）
        return phoneNumber.matches(Regex("^1[3-9]\\d{9}$"))
    }
}

/**
 * 运维操作接口
 */
@RestController
@RequestMapping("/api/operator")
class OperatorResource {

    @GetMapping("getStatus")
    fun getStatus(): Result = Result.success

    @GetMapping("shutdown")
    fun shutdown() {
        log.info("Application shutdown requested by operator")
        Application.context?.close()
    }
}
