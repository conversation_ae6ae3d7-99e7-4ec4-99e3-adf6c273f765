package com.shenlan.smartlogixmini.auto

import com.fasterxml.jackson.annotation.JsonFormat
import com.shenlan.smartlogixmini.util.notEmpty
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import java.util.*
import java.time.format.TextStyle
import javax.validation.Valid

/**
 * 具体每日的理发日期配置实体类
 */
class Hairdateconfig : BaseModel {
    @JsonFormat(pattern = "yyyy/MM/dd" ,timezone = "GMT+8")
    var businessDate: LocalDate =LocalDate.now()
    @JsonFormat(pattern = "HH:mm")
    var openTime: LocalTime = LocalTime.of(8,30)
    @JsonFormat(pattern = "HH:mm")
    var closeTime: LocalTime = LocalTime.of(17,0)
    @JsonFormat(pattern = "HH:mm")
    var lunchStartTime: LocalTime = LocalTime.of(11,30)
    @JsonFormat(pattern = "HH:mm")
    var lunchEndTime: LocalTime = LocalTime.of(13,30)
    var intervalMinutes: Int = 30
    var ifopen:Int=0

    //关联字段
    val weekDay:String
        get() = when(businessDate.dayOfWeek){
            DayOfWeek.MONDAY -> "周一"
            DayOfWeek.TUESDAY -> "周二"
            DayOfWeek.WEDNESDAY -> "周三"
            DayOfWeek.THURSDAY -> "周四"
            DayOfWeek.FRIDAY -> "周五"
            DayOfWeek.SATURDAY -> "周六"
            DayOfWeek.SUNDAY -> "周日"
            else -> "Unknown"
        }
    var specialTimeList:List<Specialclosedtime> = listOf()
    constructor(businessDate: LocalDate,openTime: LocalTime, closeTime: LocalTime,lunchStartTime: LocalTime,lunchEndTime: LocalTime,intervalMinutes: Int){
        this.businessDate=businessDate
        this.openTime=openTime
        this.closeTime=closeTime
        this.lunchStartTime=lunchStartTime
        this.lunchEndTime=lunchEndTime
        this.intervalMinutes=intervalMinutes
    }
    constructor()

}

/**
 * 理发日期信息配置查询条件类
 */
class HairdateconfigSearch : BaseSearch() {
}

/**
 * 理发日期信息配置Mapper接口
 */
@Mapper
interface HairdateconfigMapper : BaseMapper<Hairdateconfig> {
    @Delete("DELETE FROM tbl_hairdateconfig where id=#{id}")
    override fun delete(id: String): Int
}

/**
 * 理发日期信息配置服务类
 */
@Service
class HairdateconfigService(
    var specialclosedtimeService: SpecialclosedtimeService,
    var specialclosedtimeMapper: SpecialclosedtimeMapper,
    var hairtimeSlotService: HairtimeslotService,
    var hairbaseconfigMapper: HairbaseconfigMapper,
    var hairtimeslotMapper: HairtimeslotMapper,
    mapper: HairdateconfigMapper,
    private val hairdateconfigMapper: HairdateconfigMapper
) : BaseService<Hairdateconfig, HairdateconfigMapper>(mapper) {

    /**
     * 重写保存基础配置方法，需要添加特殊休息时间的保存方式
     */
    @Transactional
    override fun save(model: Hairdateconfig): Result {
        val hairBaseConfig=hairbaseconfigMapper.getBaseConfig()
        model.intervalMinutes=hairBaseConfig.customerInterval
        model.lunchStartTime=hairBaseConfig.lunchStartTime
        model.lunchEndTime=hairBaseConfig.lunchEndTime
        if (model.id.notEmpty()) {
            delete(model.id)
            hairtimeslotMapper.deleteListByConfigId(model.id)
            if(model.specialTimeList.isNotEmpty()){
                model.specialTimeList.forEach{
                    it.configId = model.id
                    specialclosedtimeService.save(it)
                }
            }
        }
        if (model.id.isEmpty()) {
            model.id = uuid()
            if(model.specialTimeList.isNotEmpty()){
            model.specialTimeList.forEach {
                it.configId = model.id
                specialclosedtimeService.save(it)
            }
            }
        }
        hairtimeSlotService.generateTimeSlots(model)
        mapper.insert(model)
        return Result.getSuccess(model.id)
    }

    override fun getList(page: BaseSearch): Result {
        var hairdateconfigList:MutableList<Hairdateconfig> = mutableListOf()
        hairdateconfigList=mapper.getList(page).toMutableList()
        hairdateconfigList.forEach {
            var search:SpecialclosedtimeSearch = SpecialclosedtimeSearch()
            search.configId=it.id
            it.specialTimeList=specialclosedtimeMapper.getList(search)
        }
        return Result.getSuccess(hairdateconfigList)
    }
    @Transactional
    override fun delete(id: String): Result {
        val i=mapper.delete(id)
        val search:SpecialclosedtimeSearch=SpecialclosedtimeSearch()
        search.configId=id
        val specialTimeList=specialclosedtimeMapper?.getList(search)
        specialTimeList?.forEach{
            specialclosedtimeService.delete(it.id)
        }
        hairtimeslotMapper.deleteListByConfigId(id)
        return Result.getSuccess(i)
    }

    /**
     * 根据基础配置自动生成下两周的数据
     */
    @Transactional
    fun generateNextTwoWeeksConfigs(): Result {
        // 获取基础配置（默认取第一条）
        val baseConfig = hairbaseconfigMapper.getBaseConfig()
        var count:Int=0
        // 计算下周第一天（下周一）
        val today = LocalDate.now()
        val nextMonday = today.plusDays(8 - today.dayOfWeek.value.toLong()) // 8 - 当前星期几 = 距离下周一的天数
        for (i in 0 until 14) {
            val date = nextMonday.plusDays(i.toLong())
            val dayOfWeek = date.dayOfWeek.getDisplayName(TextStyle.FULL, Locale.CHINA)//将当天的星期英文转换成中文

            // 如果是基础配置中的营业日，则生成配置
            if (baseConfig.weekDays.contains(dayOfWeek)) {
                val dailyConfig = Hairdateconfig(
                    businessDate = date,
                    openTime = baseConfig.dailyOpenTime,
                    closeTime = baseConfig.dailyCloseTime,
                    lunchStartTime = baseConfig.lunchStartTime,
                    lunchEndTime = baseConfig.lunchEndTime,
                    intervalMinutes = baseConfig.customerInterval
                )
                this.save(dailyConfig)
                count++
            }
        }
        return Result.getSuccess(count)


    }
}








/**
 * 理发日期信息配置控制器
 */
@RestController
@RequestMapping("/api/Hairdateconfig")
class HairdateconfigResource(service: HairdateconfigService) :
    BaseResource<HairdateconfigSearch, Hairdateconfig, HairdateconfigMapper, HairdateconfigService>(service) {
    @PostMapping("/shenchenshuju")
    open fun setList() = service.generateNextTwoWeeksConfigs()
}
