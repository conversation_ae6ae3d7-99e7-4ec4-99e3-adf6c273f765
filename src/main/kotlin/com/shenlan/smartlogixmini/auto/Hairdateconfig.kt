package com.shenlan.smartlogixmini.auto
import com.fasterxml.jackson.annotation.JsonFormat
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.notEmpty
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

/**
 * 具体每日的理发日期配置实体类
 */
class Hairdateconfig : BaseEntity {
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    var businessDate: LocalDate = LocalDate.now()

    @JsonFormat(pattern = "HH:mm")
    var openTime: LocalTime = LocalTime.of(8, 30)

    @JsonFormat(pattern = "HH:mm")
    var closeTime: LocalTime = LocalTime.of(17, 0)

    @JsonFormat(pattern = "HH:mm")
    var lunchStartTime: LocalTime = LocalTime.of(11, 30)

    @JsonFormat(pattern = "HH:mm")
    var lunchEndTime: LocalTime = LocalTime.of(13, 30)
    var intervalMinutes: Int = 30
    var issue: Int = 0
    var ifopen: Int = 0//0表示未开放的预约日期，1表示已开放的预约日期，2表示已经过期的预约日期

    //关联字段
    val weekDay: String
        get() = when (businessDate.dayOfWeek) {
            DayOfWeek.MONDAY -> "周一"
            DayOfWeek.TUESDAY -> "周二"
            DayOfWeek.WEDNESDAY -> "周三"
            DayOfWeek.THURSDAY -> "周四"
            DayOfWeek.FRIDAY -> "周五"
            DayOfWeek.SATURDAY -> "周六"
            DayOfWeek.SUNDAY -> "周日"
            else -> "Unknown"
        }
//    剩余未理发的人员
    val readyCustomer: Int
        get() = hairtimeslot.count { it.status == 1 }
//    总理发人数（包含过期）
    val totalCustomer:Int
        get() = hairtimeslot.count { it.hairreservation?.status==0||it.hairreservation?.status==2 }

    var specialTimeList: List<Specialclosedtime> = listOf()
    var hairtimeslot: List<Hairtimeslot> = listOf()
    var orgId:String=""

    constructor(
        businessDate: LocalDate,
        openTime: LocalTime,
        closeTime: LocalTime,
        lunchStartTime: LocalTime,
        lunchEndTime: LocalTime,
        intervalMinutes: Int,
        orgId:String
    ) {
        this.businessDate = businessDate
        this.openTime = openTime
        this.closeTime = closeTime
        this.lunchStartTime = lunchStartTime
        this.lunchEndTime = lunchEndTime
        this.intervalMinutes = intervalMinutes
        this.orgId = orgId
    }

    constructor()

}

/**
 * 理发日期信息配置查询条件类
 */
class HairdateconfigSearch : BaseSearch() {
    var startDate: LocalDate? = null
    var endDate: LocalDate? = null
    var orgId:String?= null
}

/**
 * 理发日期信息配置Mapper接口
 */
@Mapper
interface HairdateconfigMapper : BaseMapper<Hairdateconfig> {
    @Delete("DELETE FROM tbl_hairdateconfig where id=#{id}")
    override fun delete(id: String): Int

    @Select("SELECT * FROM tbl_hairdateconfig WHERE businessDate=#{businessDate} AND orgId=#{orgId}")
    fun findByBusinessDateAndorgId(businessDate: LocalDate,orgId: String): Hairdateconfig

    @Update("UPDATE tbl_hairdateconfig SET ifopen = 1 WHERE issue = #{issue} AND ifopen = 0")
    fun updateOpenStatus(issue: Int): Int

    @Delete(" DELETE FROM tbl_hairdateconfig where ifopen=#{ifopen}")
    fun deleteListByIfopen(ifopen: Int): Int

    @Select("SELECT MAX(issue) AS max_issue FROM tbl_hairdateconfig")
    fun findMaxIssue(): Int

    @Select("SELECT * FROM tbl_hairdateconfig where ifopen=#{ifopen} AND orgID=#{orgId}")
    fun getListByIfopenAndOrgId(ifopen: Int,orgId: String): List<Hairdateconfig>

    @Select(
        """
    <script>
        SELECT * FROM tbl_hairdateconfig
        <where>
            sysDeleted = 0 AND ifopen=0
            <if test="startDate != null and endDate != null">
                AND businessDate BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="orgId != null">
                AND orgId = #{orgId}
            </if>
        </where>
        ORDER BY businessDate ASC
    </script>
"""
    )
    override fun getList(search: BaseSearch): List<Hairdateconfig>

    @Update("UPDATE tbl_hairdateconfig SET ifopen = 2 WHERE id = #{id} AND ifopen IN (1,2)")
    fun updateOpenStatusById(id: String): Int
    @Update("UPDATE tbl_hairdateconfig SET ifopen = 1 WHERE issue = #{issue} AND ifopen = 0 AND orgId=#{orgId}")
    fun updateOpenStatusByOrgIdAndIssue(issue: Int,orgId: String): Int
    /**
     * 查询期数对应的开放日期的开始时间和结束时间
     */
    @Select("SELECT businessDate FROM tbl_hairdateconfig WHERE issue=#{issue} AND orgId=#{orgId} ORDER BY businessDate DESC LIMIT 1")
    fun findMaxbussinessDateByIssue(issue: Int,orgId: String):LocalDate?
    @Select("SELECT businessDate FROM tbl_hairdateconfig WHERE issue=#{issue} AND orgId=#{orgId} ORDER BY businessDate ASC LIMIT 1")
    fun findMinbussinessDateByIssue(issue: Int,orgId: String):LocalDate?
}

/**
 * 理发日期信息配置服务类
 */
@Service
class HairdateconfigService(
    var specialclosedtimeService: SpecialclosedtimeService,
    var specialclosedtimeMapper: SpecialclosedtimeMapper,
    var hairtimeSlotService: HairtimeslotService,
    var hairbaseconfigMapper: HairbaseconfigMapper,
    var hairtimeslotMapper: HairtimeslotMapper,
    mapper: HairdateconfigMapper,
    private val hairdateconfigMapper: HairdateconfigMapper
) : BaseService<Hairdateconfig, HairdateconfigMapper, HairdateconfigSearch>(mapper) {

    /**
     * 重写保存基础配置方法，需要添加特殊休息时间的保存方式，以及自动生成对应当天的各个时间段时间情况
     */
    @Transactional
    override fun save(model: Hairdateconfig): Result {
        val orgId= getUser()?.branchOrganizationId?:return Result.getError("数据异常")
        if(orgId.isEmpty()){
            return Result.getError("数据异常")
        }
        val hairBaseConfig = hairbaseconfigMapper.getBaseConfig(orgId)
        if(hairBaseConfig==null){
            return Result.getError("数据异常")
        }
        model.orgId = orgId
        model.intervalMinutes = hairBaseConfig.customerInterval
        model.lunchStartTime = hairBaseConfig.lunchStartTime
        model.lunchEndTime = hairBaseConfig.lunchEndTime
//        根据日期生成计算出对应的期数
        model.issue = this.calculateIssueByDate(model.businessDate)
//        if (mapper.findByBusinessDate(model.businessDate).isNotEmpty()) {
//            log.error("${model.businessDate}的理发时间配置保存失败,因为该日期配置已经存在")
//            return Result.getError("${model.businessDate}的理发时间配置保存失败,因为该日期配置已经存在")
//        }
        if (model.id.notEmpty()) {
            delete(model.id)
//            修改配置前把所有的特殊时间段数据和分割的各个时间段数据清除
            specialclosedtimeMapper.deleteListByConfigId(model.id)
            hairtimeslotMapper.deleteListByConfigId(model.id)
            if (model.specialTimeList.isNotEmpty()) {
                model.specialTimeList.forEach {
                    it.configId = model.id
                    specialclosedtimeService.save(it)
                }
            }
        }
        if (model.id.isEmpty()) {
            model.id = uuid()
            if (model.specialTimeList.isNotEmpty()) {
                model.specialTimeList.forEach {
                    it.configId = model.id
                    specialclosedtimeService.save(it)
                }
            }
        }
        hairtimeSlotService.generateTimeSlots(model)
        mapper.insert(model)
        return Result.getSuccess(model.id)
    }

    //这里的getList只返回当天的配置以及特殊时间段的配置
    override fun getList(page: HairdateconfigSearch): Result {
        var hairdateconfigList: MutableList<Hairdateconfig> = mutableListOf()
        hairdateconfigList = mapper.getList(page).toMutableList()
        hairdateconfigList.forEach {
            var search: SpecialclosedtimeSearch = SpecialclosedtimeSearch()
            var search1: HairtimeslotSearch = HairtimeslotSearch()
            search1.configId = it.id
            search.configId = it.id
            it.specialTimeList = specialclosedtimeMapper.getList(search)
            it.hairtimeslot = hairtimeslotMapper.getList(search1)
        }
        return Result.getSuccess(hairdateconfigList)
    }

    //根据id彻底删除每日配置表对应的特殊时间字段表以及每天的各个段时间表
    @Transactional
    override fun delete(id: String): Result {
        val i = mapper.delete(id)
        specialclosedtimeMapper.deleteListByConfigId(id)
        hairtimeslotMapper.deleteListByConfigId(id)
        return Result.getSuccess(i)
    }

    @Transactional
    //    根据日期查询对应的当天配置
    fun findByBusinessDate(businessDate: LocalDate): Result {
        val nowDate = LocalDate.now()
        val orgId= getUser()?.branchOrganizationId?:return Result.getError("数据异常")
        if (orgId.isEmpty()){
            return Result.getError("数据异常")
        }
        if(businessDate==nowDate){
            log.info("用户刷新理发首页")
            val result=updateHairDate()
            if(result.rlt==1){
                return Result.getError("系统异常，用户刷新理发首页失败")
            }
        }
        var hairdateconfig = mapper.findByBusinessDateAndorgId(businessDate,orgId)
        if (hairdateconfig != null) {
//            根据现在的时间重置改天的开放状态
            if (hairdateconfig.businessDate.isBefore(nowDate)) {
                mapper.updateOpenStatusById(hairdateconfig.id)
//                如果已经过期的日期设置为2
                hairdateconfig.ifopen = 2
                val search: SpecialclosedtimeSearch = SpecialclosedtimeSearch()
                search.configId = hairdateconfig.id
                hairdateconfig.specialTimeList = specialclosedtimeMapper.getList(search)
                hairdateconfig.hairtimeslot = hairtimeSlotService.getTotalInfoByConfigId(hairdateconfig.id)
                return Result.getSuccess(hairdateconfig)
            } else {
                val search: SpecialclosedtimeSearch = SpecialclosedtimeSearch()
                search.configId = hairdateconfig.id
                hairdateconfig.specialTimeList = specialclosedtimeMapper.getList(search)
                hairdateconfig.hairtimeslot = hairtimeSlotService.getTotalInfoByConfigId(hairdateconfig.id)
                return Result.getSuccess(hairdateconfig)
            }
        } else
            return Result.getSuccess("")
    }
@Transactional
    //    返回新一期暂未被开放的预约当中第一周和第二周分别的配置情况
    fun getNewConfigNotOpenByWeek(i: Int): Result {
        val result=updateHairDate()
        if(result.rlt==0){
//        计算出当前需要配置的是第几期的理发内容
        val orgId= getUser()?.branchOrganizationId?:return Result.getError("数据异常，找不到匹配的机构id")
        if (orgId.isEmpty()){
            return Result.getError("数据异常，找不到匹配的机构id")
        }
        val time = LocalDateTime.now()
//        issue计算目前已经开放的最大期数
        val issue = this.caluateMaxIssueByLocalDateTime(time) + 1
        log.info("目前开放的最大理发最大期数是{}", issue-1)
        if(issue==-1){
            return Result.getError("数据异常，找不到匹配的机构id")
        }
        val map = this.caluateDateByIssue(issue)
        val startDate = map.get("issueStartTime")!!.plusDays(((i - 1) * 7).toLong())
        val endDate = startDate!!.plusDays(6)
        val search: HairdateconfigSearch = HairdateconfigSearch()
        search.startDate = startDate
        search.endDate = endDate
        search.orgId=orgId
        val List = this.getList(search).datas as MutableList<Hairdateconfig>
        return Result.getSuccess(List)}
        return Result.getError("系统异常")
    }

    //返回新一期暂未开放的预约当中第一周和第二周分别没有被配置的时间
    fun getFreeDateByWeek(i: Int): Result {
//        获取这一周的边界值
        val time = LocalDateTime.now()
        val issue = this.caluateMaxIssueByLocalDateTime(time) + 1
        if(issue==-1){
            return Result.getError("数据异常，找不到匹配的机构id")
        }
        log.info("目前开放的最大理发最大期数是{}", issue-1)
        val map = this.caluateDateByIssue(issue)
        val startDate = map.get("issueStartTime")!!.plusDays(((i - 1) * 7).toLong())
        val endDate = startDate!!.plusDays(6)
        val existingDates = this.getNewConfigNotOpenByWeek(i).datas as MutableList<Hairdateconfig>
        val list=existingDates.mapNotNull { it.businessDate }.toSet()

        // 生成完整日期范围并找出缺失的日期，格式化为"周X"
        val missingDates = (0..ChronoUnit.DAYS.between(startDate, endDate))
            .map { startDate.plusDays(it) }
            .filterNot { it in list }
            .map { date ->
                val dayOfWeek = when (date.dayOfWeek) {
                    DayOfWeek.MONDAY -> "周一"
                    DayOfWeek.TUESDAY -> "周二"
                    DayOfWeek.WEDNESDAY -> "周三"
                    DayOfWeek.THURSDAY -> "周四"
                    DayOfWeek.FRIDAY -> "周五"
                    DayOfWeek.SATURDAY -> "周六"
                    DayOfWeek.SUNDAY -> "周日"
                }
                "${date} ${dayOfWeek}"
            }
        return Result.getSuccess(missingDates)
    }

    /**
     * 重载区分重新编辑时候的空余日期
     */
    fun getFreeDateByWeek(i: Int,id: String): Result {
//        获取这一周的边界值
        val time = LocalDateTime.now()
        val issue = this.caluateMaxIssueByLocalDateTime(time) + 1
        if(issue==-1){
            return Result.getError("数据异常，找不到匹配的机构id")
        }
        log.info("目前开放的最大理发最大期数是{}", issue-1)
        val map = this.caluateDateByIssue(issue)
        val startDate = map.get("issueStartTime")!!.plusDays(((i - 1) * 7).toLong())
        val endDate = startDate!!.plusDays(6)
        val existingDates = this.getNewConfigNotOpenByWeek(i).datas as MutableList<Hairdateconfig>
        val list=existingDates
            .mapNotNull { it.businessDate }
            .filterNot { it==mapper.getInfo(id)!!.businessDate }
            .toSet()//自动去重
        // 生成完整日期范围并找出缺失的日期，格式化为"周X"
        val missingDates = (0..ChronoUnit.DAYS.between(startDate, endDate))
            .map { startDate.plusDays(it) }
            .filterNot { it in list }
            .map { date ->
                val dayOfWeek = when (date.dayOfWeek) {
                    DayOfWeek.MONDAY -> "周一"
                    DayOfWeek.TUESDAY -> "周二"
                    DayOfWeek.WEDNESDAY -> "周三"
                    DayOfWeek.THURSDAY -> "周四"
                    DayOfWeek.FRIDAY -> "周五"
                    DayOfWeek.SATURDAY -> "周六"
                    DayOfWeek.SUNDAY -> "周日"
                }
                "${date} ${dayOfWeek}"
            }
        return Result.getSuccess(missingDates)
    }

    /**
     * 根据一期的开始日期自动生成一期两周的数据
     */
    @Transactional
    fun generateNextTwoWeeksConfigs(date: LocalDate): Result {
        // 获取基础配置（默认取最新的一条）
        val orgId= getUser()?.branchOrganizationId?:return Result.getError("数据异常")
        if(orgId.isEmpty()){
            return Result.getError("数据异常")
        }
        val baseConfig = hairbaseconfigMapper.getBaseConfig(orgId)
        if(baseConfig==null){
            return Result.getError("数据异常")
        }
        var count: Int = 0
        for (i in 0 until 14) {
            val date = date.plusDays(i.toLong())
            val dayOfWeek = when (date.dayOfWeek) {
                DayOfWeek.MONDAY -> "周一"
                DayOfWeek.TUESDAY -> "周二"
                DayOfWeek.WEDNESDAY -> "周三"
                DayOfWeek.THURSDAY -> "周四"
                DayOfWeek.FRIDAY -> "周五"
                DayOfWeek.SATURDAY -> "周六"
                DayOfWeek.SUNDAY -> "周日"
                else -> "Unknown"
            }
            // 如果是基础配置中的营业日，则生成配置
            if (baseConfig.weekDays.contains(dayOfWeek)) {
                val dailyConfig = Hairdateconfig(
                    businessDate = date,
                    openTime = baseConfig.dailyOpenTime,
                    closeTime = baseConfig.dailyCloseTime,
                    lunchStartTime = baseConfig.lunchStartTime,
                    lunchEndTime = baseConfig.lunchEndTime,
                    intervalMinutes = baseConfig.customerInterval,
                    orgId=orgId
                )
                try {
                    val result = this.save(dailyConfig)
                    if (result.rlt == 0) {
                        count++
                        log.info("${dailyConfig.businessDate}理发时间配置成功")
                    }
                } catch (e: Exception) {
                    log.error("根据基础配置自动生成理发时间配置出现问题")
                }
            }
        }
        return Result.getSuccess(count)
    }

    fun updateOpenStatus(issue: Int): Int {
        return mapper.updateOpenStatus(issue)
    }

    /**
     * 根据年月日计算当前理发时间属于第几期issue
     * 以2025/06/20为第0期的第二周结束
     */
    fun calculateIssueByDate(date: LocalDate): Int {
        val baseCycleFriday = LocalDate.of(2025, 6, 22)
        val daysFromBase = ChronoUnit.DAYS.between(baseCycleFriday, date) - 1
        val issue = daysFromBase / 14 + 1
        return issue.toInt()
    }

    /**
     * 根据传入issue计算出该issue的开放时间和截止时间
     */
    fun caluateDateByIssue(issue: Int): Map<String, LocalDate> {
        val issueStartTime = LocalDate.of(2025, 6, 22).plusDays((14 * (issue - 1) + 1).toLong())
        val issueEndTime = LocalDate.of(2025, 6, 22).plusDays((14 * issue).toLong())
        val map = mapOf("issueStartTime" to issueStartTime, "issueEndTime" to issueEndTime)
        return map
    }

    /**
     *判断当前时间已经开放的issue最高是哪期
     * 原来需求是每一期的第二周周五五点开放新一期，现在修改为每一周的实际结束时间开放
     */
    fun caluateMaxIssueByLocalDateTime(dateTime: LocalDateTime): Int {
        val orgId= getUser()?.branchOrganizationId?:return -2
        if (orgId.isEmpty()){
            return -2
        }
        val date = dateTime.toLocalDate()
        val issueNow = this.calculateIssueByDate(date)
        val maxLocalDateTime=mapper.findMaxbussinessDateByIssue(issueNow, orgId)?.atTime(LocalTime.MAX)
        log.info("现在是理发的第{}期数,这一期理发的结束时间是{},当前时间是{}", issueNow, maxLocalDateTime,dateTime)
        if(maxLocalDateTime!=null){
//            判断当前时间是否在目前这一期的结束时间之后
            if(maxLocalDateTime.isBefore(dateTime)){
                val issue=issueNow+1
                return issue
            }
            else{
                return issueNow
            }
        }
        return issueNow+1
//        val map= this.caluateDateByIssue(issueNow)
//        if (issueAfter != issueNow) {
//            if (dateTime.dayOfWeek == DayOfWeek.SATURDAY || dateTime.dayOfWeek == DayOfWeek.SUNDAY ||
//                (dateTime.dayOfWeek == DayOfWeek.FRIDAY && dateTime.toLocalTime() >= LocalTime.of(17, 0))
//            ) {
//                return issueNow + 1
//            } else {
//                return issueNow
//            }
//        } else {
//            return issueNow
//        }
    }

    /**
     * 根据当前时间获取目前时间已经开放期数的开始时间和结束时间
     */
    fun caluateNewDateByMaxIssue(dateTime: LocalDateTime): Map<String, LocalDate> {
        val issue = caluateMaxIssueByLocalDateTime(dateTime)
        log.info("目前开放的最大理发最大期数是{}", issue)
        val map = this.caluateDateByIssue(issue)
        return map
    }

    /**
     *日历调用自动获取
     */
    fun calculateDate(): LocalDate {
        val time = LocalDateTime.now()
        val map = this.caluateNewDateByMaxIssue(time)
        return map.get("issueEndTime") as LocalDate
    }
    @Transactional
    fun updateHairDate():Result{
        val orgId= getUser()?.branchOrganizationId?:return Result.getError("理发日期更新失败,找不到用户对应的机构ID")
        if (orgId.isEmpty()){
            return Result.getError("理发日期更新失败,找不到用户对应的机构ID")
        }
        val openIssue=caluateMaxIssueByLocalDateTime(LocalDateTime.now())
        log.info("目前开放的最大理发最大期数是{}", openIssue)
        if(openIssue==-2){
            return Result.getError("数据异常,找不到对应的机构ID")
        }else{
            val i=mapper.updateOpenStatusByOrgIdAndIssue(openIssue,orgId)
            if(i==0){
                log.info("暂时没有需要更新开放的理发日期")
                return Result.getSuccessInfo("理发日期更新成功")
            }
            else{
                val map=caluateDateByIssue(openIssue+1)
                log.info("更新理发日期,目前需要配置的理发期数是{}", openIssue+1)
                val dateStart=map.get("issueStartTime")
                if (dateStart!=null){
                    try {
                        log.info("第{}理发期数已开放，现在开始根据基础配置生成第{}期配置",openIssue,openIssue+1)
                        generateNextTwoWeeksConfigs(dateStart)
                    } catch (e: Exception) {
                        return Result.getError("理发日期更新失败,下一期理发日期配置失败")
                    }
                    return Result.getSuccess("理发日期更新成功")
                }
                return Result.getError("获取需要配置的理发开始日期失败")
            }
        }
    }
}

/**
 * 理发日期信息配置控制器
 */
@RestController
@RequestMapping("/api/Hairdateconfig")
class HairdateconfigResource(service: HairdateconfigService) :
    BaseResource<HairdateconfigSearch, Hairdateconfig, HairdateconfigMapper, HairdateconfigService>(service) {
    //    @PostMapping("/shenchenshuju")
//    open fun setList() = service.generateNextTwoWeeksConfigs()
    @GetMapping("/getHairdateconfigByBusinessDate/{businessDate}") // 使用路径变量
    fun findByBusinessDate(@PathVariable @DateTimeFormat(pattern = "yyyy-MM-dd") businessDate: LocalDate): Result {
        return service.findByBusinessDate(businessDate)
    }

    @GetMapping("/getNewConfigNotOpenByWeek/{i}")
    fun getNewConfigNotOpenByWeek(@PathVariable i: String): Result {
        return service.getNewConfigNotOpenByWeek(i.toInt())
    }

    @GetMapping("/getFreeDateByWeek/{i}")
    fun getFreeDateByWeek(@PathVariable i: String): Result {
        return service.getFreeDateByWeek(i.toInt())
    }
//    重载区分重新编辑状态下的空余日期
    @GetMapping("/getFreeDateByWeek/{i}/{id}")
    fun getFreeDateByWeek(@PathVariable i: String,@PathVariable id: String): Result {
        return service.getFreeDateByWeek(i.toInt(),id)
    }
    @GetMapping("/updateHairDate")
    fun updateHairDate():Result{
        return service.updateHairDate()
    }

}
