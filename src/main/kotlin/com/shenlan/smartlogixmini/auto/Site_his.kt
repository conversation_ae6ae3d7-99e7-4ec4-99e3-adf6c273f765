package com.shenlan.smartlogixmini.auto

import com.github.pagehelper.PageHelper
import com.shenlan.smartlogixmini.mybatis.paginationInfo
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.getInStr
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.notEmpty
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.Date


class Site_his : Site {

    /** 站点Id */
    var siteId: String = ""
    /** 站点日期 */
    var siteDate: String = ""

    constructor()
}

fun convertToSite(historyList: List<Site_his>): List<Site> {
    return historyList.map { history ->
        Site().apply {
            id = history.siteId
            siteName = history.siteName
            siteNumber = history.siteNumber
            macAddr = history.macAddr
            floorName = history.floorName
            sitePosition = history.sitePosition
            remarks = history.remarks
            siteStatus = history.siteStatus
            statusChangeTime = history.statusChangeTime
            statusChangeMan = history.statusChangeMan
            content = history.content
        }
    }
}

@Mapper
interface Site_hisMapper : BaseMapper<Site_his> {

    @Select(""" select * from tbl_site_his where 1=1 ${'$'}{whereSql} order by sysCreated """)
    override fun getList(search: BaseSearch): List<Site_his>

}

class Site_hisSearch : BaseSearch {

    var siteDate:String = ""
    var idsList: List<String>? = null
    var ids:String = ""
        get() {
            if (idsList != null && idsList!!.isNotEmpty()) {
                return idsList!!.getInStr()
            }
            return field
        }
    var orgId:String = "" //机构Id
    var whereSql = ""
        get() {
            var sql = ""
            if (orgId.notEmpty()) sql += " and orgId = '${orgId}' "
            if (siteDate.notEmpty()) sql += " and siteDate = '${siteDate}' "
            if (ids.notEmpty()) sql += " and siteId in (${ids}) "
            return sql
        }

    constructor()
}

@Service
open class Site_hisService(mapper: Site_hisMapper) : BaseService<Site_his, Site_hisMapper, Site_hisSearch>(mapper) {

    override fun getList(page: Site_hisSearch): Result {
        //获取当前登录账户的机构Id 仅查询该机构下的数据
        var orgId = getUser()!!.branchOrganizationId
        page.orgId = orgId

        var result: Any?
        var list: List<Site_his>
        if (page.ifPage) {
            // 使用分页查询
            result = PageHelper.startPage<Site_his>(page.currentPage!!, page.pageRecord!!)
                .doSelectPageInfo<Site_his> {
                    mapper.getList(page)
                }
                .paginationInfo
            list = result.result as List<Site_his>
        }else {
            //不使用分页查询
            result = mapper.getList(page)
            list = result
        }

        list.forEach {
            it.content = getBean(ContentMapper::class.java).getContentBySiteId(it.id)
        }

        return Result.getSuccess(result)
    }

}

@RestController
@RequestMapping("/api/Site_his")
open class Site_hisResource(service: Site_hisService) : BaseResource<Site_hisSearch, Site_his, Site_hisMapper, Site_hisService>(service) {

}

