package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.*
import org.springframework.beans.factory.annotation.Value
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

/**
 * 短信发送测试模型类
 */
class SmsTestModel : BaseEntity() {
    /** 手机号码 */
    var phoneNumber: String = ""
    /** 短信模板参数(JSON格式的键值对) */
    var params: Map<String, String> = mapOf()
    /** 短信模板代码，不传则使用默认模板 */
    var templateCode: String = ""
    /** 短信签名，不传则使用默认签名 */
    var signName: String = ""
}

/**
 * 语音通话测试模型类
 */
class VmsTestModel : BaseEntity() {
    /** 被叫号码 */
    var calledNumber: String = ""
    /** 主叫号码 */
    var calledShowNumber: String = ""
    /** 语音模板代码（startCode） */
    var startCode: String = ""
    /** 开始TTS参数（JSON字符串，可选） */
    var startTtsParams: String? = null
    /** 菜单按键TTS配置（List<Map<String, Any>>，可选） */
    var menuKeyMapList: List<Map<String, Any>> = emptyList()
}

/**
 * 内容安全检测测试模型类
 */
class ContentSecurityTestModel : BaseEntity() {
    /** 评论内容检测 */
    @field:ContentSecurity(
        scene = WechatMsgSecCheckUtil.Scene.COMMENT,
        message = "评论内容包含违规信息"
    )
    var commentContent: String = ""
}

/**
 * 短信发送测试控制器类 - 纯测试云平台连通性
 */
@Validated
@RestController
@RequestMapping("/api/Test")
class TestResource(
    private val aliyunSmsUtil: AliyunSmsUtil,
    private val aliyunVmsUtil: AliyunVmsUtil,
    private val wechatAccessTokenUtil: WechatAccessTokenUtil,
    private val wechatMediaSecCheckUtil: WechatMediaSecCheckUtil
) {
    @Value("\${application.host:}")
    private lateinit var applicationHost: String

    @GetMapping("/test")
    fun test() {

    }

    @PostMapping("/sendSms")
    fun testSendSms(@RequestBody model: SmsTestModel): Result {
        if (model.phoneNumber.isEmpty()) {
            return Result.getError("手机号码不能为空")
        }

        // 调用短信发送工具类
        val result = aliyunSmsUtil.sendSms(
            phoneNumber = model.phoneNumber,
            params = model.params,
            templateCode = model.templateCode,
            signName = model.signName
        )

        return if (result) {
            Result.getSuccessInfo("短信平台连通性测试成功")
        } else {
            Result.getError("短信平台连通性测试失败")
        }
    }

    @PostMapping("/ivrCall")
    fun testIvrCall(@RequestBody model: VmsTestModel): Result {
        if (model.calledNumber.isEmpty()) {
            return Result.getError("被叫号码不能为空")
        }
        if (model.startCode.isEmpty()) {
            return Result.getError("语音模板代码不能为空")
        }
        val (success, callId) = aliyunVmsUtil.ivrCall(
            calledNumber = model.calledNumber,
            startCode = model.startCode,
            calledShowNumber = model.calledShowNumber,
            startTtsParams = model.startTtsParams,
            menuKeyMapList = model.menuKeyMapList
        )
        return if (success) {
            Result.getSuccessInfo("语音平台连通性测试成功，callId: $callId")
        } else {
            Result.getError("语音平台连通性测试失败")
        }
    }

    @GetMapping("/getAccessToken")
    fun testGetAccessToken(): Result {
        // 获取access_token
        val accessToken = wechatAccessTokenUtil.getAccessToken()

        // 获取token状态信息
        val isValid = wechatAccessTokenUtil.isTokenValid()
        val remainingTime = wechatAccessTokenUtil.getRemainingTime()

        // 构建响应数据
        val data = mapOf(
            "accessToken" to accessToken,
            "isValid" to isValid,
            "remainingTimeSeconds" to remainingTime
        )

        return Result.getSuccess(data)
    }

    /**
     * 参数的`@Valid` + 属性的`@field:ContentSecurity` 才能生效
     */
    @PostMapping("/testContentSecurityProperty")
    fun testContentSecurityProperty(@Valid @RequestBody model: ContentSecurityTestModel): Result {
        return Result.getSuccessInfo("属性注解内容安全检测通过")
    }

    /**
     * 类的`@Validated` + 参数的`@ContentSecurity` 才能生效
     */
    @PostMapping("/testContentSecurityParam")
    fun testContentSecurityParam(
        @ContentSecurity(
            scene = WechatMsgSecCheckUtil.Scene.COMMENT,
            message = "参数内容包含违规信息"
        )
        @RequestBody content: String
    ): Result {
        return Result.getSuccessInfo("参数注解内容安全检测通过")
    }

    /**
     * 测试微信多媒体内容安全检测
     */
    @GetMapping("/testWechatMediaCheck/{contentId}")
    fun testWechatMediaCheck(@PathVariable contentId: String): Result {
        try {
            // 获取当前用户的openid
            val openid = getUser()?.wechatOpenId
            if (openid.isNullOrBlank()) {
                return Result.getError("用户未登录或未绑定微信")
            }

            // 构建图片URL
            val imageUrl = "https://$applicationHost/api/Content/preview/$contentId"

            @Suppress("UNNECESSARY_NOT_NULL_ASSERTION")
            log.info("Testing wechat media check. imageUrl={}, openid={}", imageUrl, openid!!)

            // 调用媒体安全检测
            val response = wechatMediaSecCheckUtil.checkMediaAsync(
                mediaUrl = imageUrl,
                mediaType = MediaType.IMAGE,
                scene = Scene.COMMENT,
                openid =  openid
            ) { result ->
                // 回调函数，处理异步检测结果
                log.info("Media check result received. traceId={}, suggest={}, label={}, errcode={}", result.traceId, result.result?.suggest, result.result?.label, result.errcode)
            }

            return if (response.errcode == 0) {
                val data = mapOf(
                    "traceId" to response.traceId,
                    "imageUrl" to imageUrl,
                    "message" to "媒体内容安全检测请求已提交，结果将通过回调处理"
                )
                Result.getSuccess(data)
            } else {
                Result.getError("媒体内容安全检测请求失败：${response.errmsg}")
            }

        } catch (e: Exception) {
            log.error("Error testing wechat media check. id={}", contentId, e)
            return Result.getError("媒体内容安全检测测试失败：${e.message}")
        }
    }
}
