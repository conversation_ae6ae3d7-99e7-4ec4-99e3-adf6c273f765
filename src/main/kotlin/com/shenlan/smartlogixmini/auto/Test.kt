package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.AliyunSmsUtil
import com.shenlan.smartlogixmini.util.AliyunVmsUtil
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 短信发送测试模型类
 */
class SmsTestModel : BaseModel() {
    /** 手机号码 */
    var phoneNumber: String = ""
    /** 短信模板参数(JSON格式的键值对) */
    var params: Map<String, String> = mapOf()
    /** 短信模板代码，不传则使用默认模板 */
    var templateCode: String = ""
    /** 短信签名，不传则使用默认签名 */
    var signName: String = ""
}

/**
 * 语音通话测试模型类
 */
class VmsTestModel : BaseModel() {
    /** 被叫号码 */
    var calledNumber: String = ""
    /** 主叫号码 */
    var calledShowNumber: String = ""
    /** 语音模板代码（startCode） */
    var startCode: String = ""
    /** 开始TTS参数（JSON字符串，可选） */
    var startTtsParams: String? = null
    /** 菜单按键TTS配置（List<Map<String, Any>>，可选） */
    var menuKeyMapList: List<Map<String, Any>> = emptyList()
}

/**
 * 短信发送测试控制器类 - 纯测试云平台连通性
 */
@RestController
@RequestMapping("/api/Test")
class TestResource(
    private val aliyunSmsUtil: AliyunSmsUtil,
    private val aliyunVmsUtil: AliyunVmsUtil
) {

    @PostMapping("/sendSms")
    fun testSendSms(@RequestBody model: SmsTestModel): Result {
        if (model.phoneNumber.isEmpty()) {
            return Result.getError("手机号码不能为空")
        }

        // 调用短信发送工具类
        val result = aliyunSmsUtil.sendSms(
            phoneNumber = model.phoneNumber,
            params = model.params,
            templateCode = model.templateCode,
            signName = model.signName
        )

        return if (result) {
            Result.getSuccessInfo("短信平台连通性测试成功")
        } else {
            Result.getError("短信平台连通性测试失败")
        }
    }

    @PostMapping("/ivrCall")
    fun testIvrCall(@RequestBody model: VmsTestModel): Result {
        if (model.calledNumber.isEmpty()) {
            return Result.getError("被叫号码不能为空")
        }
        if (model.startCode.isEmpty()) {
            return Result.getError("语音模板代码不能为空")
        }
        val (success, callId) = aliyunVmsUtil.ivrCall(
            calledNumber = model.calledNumber,
            startCode = model.startCode,
            calledShowNumber = model.calledShowNumber,
            startTtsParams = model.startTtsParams,
            menuKeyMapList = model.menuKeyMapList
        )
        return if (success) {
            Result.getSuccessInfo("语音平台连通性测试成功，callId: $callId")
        } else {
            Result.getError("语音平台连通性测试失败")
        }
    }
}
