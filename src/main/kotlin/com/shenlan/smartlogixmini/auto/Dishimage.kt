package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.ImageUtil
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import java.io.File

class Dishimage: BaseEntity {
    var dishId: String = ""
    var imageUrl: String = ""
    var imageType: String = ""
    var sortOrder: Int = 0
    var sysDeleted: Int = 0

    constructor()

    constructor(dishId: String, imageUrl: String, imageType: String) {
        this.id = uuid()
        this.dishId = dishId
        this.imageUrl = imageUrl
        this.imageType = imageType
    }
}

enum class ImageType(val value: String) {
    Photo("photo"),
    AI("ai");

    override fun toString(): String = value
}

class DishimageSearch: BaseSearch() {
    var dishId: String = ""
    var imageType: String = ""
}

@Mapper
interface DishimageMapper : BaseMapper<Dishimage> {
    @Select("""
        <script>
            SELECT * FROM tbl_dishimage
            <where>
                AND sysDeleted = 0
                <if test="dishId != ''">
                    AND dishId = #{dishId}
                </if>
                <if test="imageType != ''">
                    AND imageType = #{imageType}
                </if>
            </where>
            ORDER BY sortOrder ASC, sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Dishimage>

    // 更新图片dishId
    @Update("""
        UPDATE tbl_dishimage
        SET dishId = #{dishId}
        WHERE id = #{id}
    """)
    fun updateDishId(id: String, dishId: String)

    @Update("""
        UPDATE tbl_dishimage
        SET sysDeleted = 1
        WHERE id = #{id}
    """)
    override fun deleteLogic(id: String): Int
}

@Service
class DishimageService(mapper: DishimageMapper) : BaseService<Dishimage, DishimageMapper, DishimageSearch>(mapper) {
//class DishimageService(mapper: DishimageMapper) : BaseService<Dishimage, DishimageMapper>(mapper) {
    override fun delete(id: String): Result {
        return super.deleteLogic(id)
    }

    override fun getInfo(id: String): Result {
        val image = mapper.getInfo(id)?.apply {
            imageUrl = AppPro.imgPrefix + imageUrl
        }
        return Result.getSuccess(image)
    }
}

@RestController
@RequestMapping("/api/dishimage")
class DishimageResource(service: DishimageService) : BaseResource<DishimageSearch, Dishimage, DishimageMapper, DishimageService>(service) {
    /**
     * 上传菜谱图片接口
     */
    @PostMapping("/upload/{dishId}")
    fun upload(@RequestParam("file") file: MultipartFile, @PathVariable dishId: String?): Result {
        val dishImage = Dishimage(dishId ?: "", "", ImageType.Photo.toString())
        log.info(dishImage.toJsonString)
        val dir = AppPro.uploadDir + File.separator + "dishimage" + File.separator + dishId
        val fileDir = File(System.getProperty("user.dir") + File.separator + dir)
        if (!fileDir.exists()) fileDir.mkdirs()
        val fileName = System.currentTimeMillis().toString() + file.originalFilename!!.substring(file.originalFilename!!.lastIndexOf("."))
        val filePath = System.getProperty("user.dir") + File.separator + dir + File.separator + fileName
        File(filePath).writeBytes(file.bytes)
        dishImage.imageUrl = (dir + File.separator + fileName).replace("\\", "/")
        service.save(dishImage)

        // 同步文件表，避免自动删除
        ImageUtil.syncContent(dishImage)

        return Result.getSuccess(dishImage.id)
    }

    /**
     * ai生成图片
     */
    @GetMapping("/generateAiImage/{dishName}/{dishId}")
    fun generateAiImage(@PathVariable dishName: String, @PathVariable dishId: String?): Result {
        val imageId = ImageUtil.dishNameToImage(dishName, dishId ?: "")
        val image = getBean(DishimageMapper::class.java).getInfo(imageId)?.apply {
            // 同步文件表，避免自动删除
            ImageUtil.syncContent(this)
            imageUrl = AppPro.imgPrefix + imageUrl
        }
        return Result.getSuccess(image)
    }
}
