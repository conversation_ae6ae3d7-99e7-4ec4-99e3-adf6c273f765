package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 职务实体类
 */
class Position : BaseEntity() {
    // 数据库表字段

    /** 职务名称 */
    var name: String = ""
    /** 排序序号 */
    var sortOrder: Int = 0
}

/**
 * 职务查询条件类
 */
class PositionSearch : BaseSearch() {
    /** 职务名称模糊匹配 */
    var nameLike: String = ""
}

/**
 * 职务Mapper接口
 */
@Mapper
interface PositionMapper : BaseMapper<Position> {

    @Select("""
        <script>
            SELECT * FROM tbl_position
            <where>
                sysDeleted = 0
                <if test="nameLike != ''">
                    AND name LIKE CONCAT('%', #{nameLike}, '%')
                </if>
            </where>
            ORDER BY sortOrder, name
        </script>
    """)
    override fun getList(search: BaseSearch): List<Position>

    /**
     * 根据职务名称查询单个职务信息
     */
    @Select("SELECT * FROM tbl_position WHERE sysDeleted = 0 AND name = #{name} LIMIT 1")
    fun getInfoByName(name: String): Position?

    /**
     * 获取当前最大的排序号
     */
    @Select("SELECT COALESCE(MAX(sortOrder), 0) FROM tbl_position WHERE sysDeleted = 0")
    fun getMaxSortOrder(): Int
}

/**
 * 职务Service类
 */
@Service
class PositionService(
    mapper: PositionMapper,
    private val personnelMapper: PersonnelMapper
) : BaseService<Position, PositionMapper, PositionSearch>(mapper) {

    /**
     * 重写save方法，确保职务名称不重复
     */
    @Transactional
    override fun saveEntity(entity: Position): String {
        // 检查职务名称不能为空
        if (entity.name.isEmpty()) {
            throw BusinessException("职务名称不能为空")
        }

        // 查询是否存在相同名称的职务（除了当前编辑的职务）
        val existingPositionByName = mapper.getInfoByName(entity.name)
        if (existingPositionByName != null && existingPositionByName.id != entity.id) {
            throw BusinessException("职务名称已存在，请使用其他名称")
        }

        // 处理排序号设置
        if (entity.sortOrder <= 0) {
            val maxSortOrder = mapper.getMaxSortOrder()
            entity.sortOrder = maxSortOrder + 1
            log.info("Set sortOrder for position '{}' to {}", entity.name, entity.sortOrder)
        }

        // 验证通过，调用父类的save方法完成保存
        return super.saveEntity(entity)
    }

    /**
     * 重写deleteEntity方法，删除职务时清除相关人员的position字段
     */
    @Transactional
    override fun deleteEntity(id: String): Int {
        // 获取要删除的职务信息
        val position = getEntity(id)
        if (position != null) {
            // 清除所有使用该职务的人员的position字段
            val clearedCount = personnelMapper.clearPosition(position.name)
            if (clearedCount > 0) {
                log.info("Cleared position '{}' from {} personnel records", position.name, clearedCount)
            }
        }

        // 删除职务
        return super.deleteEntity(id)
    }
}

/**
 * 职务Controller类
 */
@RestController
@RequestMapping("/api/Position")
class PositionResource(service: PositionService) : BaseResource<PositionSearch, Position, PositionMapper, PositionService>(service)
