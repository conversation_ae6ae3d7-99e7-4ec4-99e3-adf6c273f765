package com.shenlan.smartlogixmini.auto

import com.github.pagehelper.PageHelper
import com.shenlan.smartlogixmini.mybatis.paginationInfo
import com.shenlan.smartlogixmini.util.*
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.Date


open class Site : BaseEntity {

    /** 站点名称 */
    var siteName: String = ""
    /** 站点编号 */
    var siteNumber: String = ""
    /** 设备Mac */
    var macAddr: String = ""
    /** 楼层名称 */
    var floorName: String = ""
    /** 站点位置 */
    var sitePosition: String = ""
    /** 备注 */
    var remarks: String = ""
    /** 二维码标签状况(normal-正常 abnormal-异常) */
    var siteStatus: String = ""
    /** 二维码标签状况更新时间 */
    var statusChangeTime: Date ? = null
    /** 二维码标签状况更新人员 */
    var statusChangeMan: String = ""

    /** 打卡状态(completed-已完成 waiting-待打卡 notInTime-未到打卡时间 missing-缺卡) */
    var inspectionResult: String = ""
    var content: Content ?= null
    var inspectionrecordList: List<Inspectionrecord>? = null
    var checkInProgress: String = "" //打卡进度 已打卡次数/总打卡次数
    var ifCheckIn: Boolean ?=null //是否可以打卡(true-可以打卡 false-不可以打卡)
    var siteConfigId: String = ""
    var ifAllowEdit: Boolean ?=null //二维码标签状态的更改功能：今天之前或者今天之后 都不允许修改

    /** 关联机构ID */
    var orgId:String = ""

    constructor()
}

@Mapper
interface SiteMapper : BaseMapper<Site> {

    @Update(""" update tbl_site set siteStatus = #{siteStatus}, statusChangeTime = now(), statusChangeMan = #{statusChangeMan} where id = #{siteId} """)
    fun changeStatus(siteId: String,siteStatus: String,statusChangeMan: String): Int

    @Select(""" select * from tbl_site where 1=1 ${'$'}{whereSql} order by sysCreated """)
    override fun getList(search: BaseSearch): List<Site>

    @Select(""" select count(*) from tbl_site where orgId = #{orgId} and siteNumber = #{siteNumber} and id != #{id} """)
    fun checkSiteNumberExists(orgId: String, siteNumber: String, id: String): Int

    @Select(""" select count(*) from tbl_site where orgId = #{orgId} and macAddr = #{macAddr} and id != #{id} """)
    fun checkMacAddrExists(orgId: String, macAddr: String, id: String): Int

}

class SiteSearch : BaseSearch {

    var floorName:String = ""
    var idsList: List<String>? = null
    var ids:String = ""
        get() {
            if (idsList != null && idsList!!.isNotEmpty()) {
                return idsList!!.getInStr()
            }
            return field
        }
    var orgId:String = "" //机构Id
    var ifContainOrgId:String = "1" //是否需要筛选orgId（默认是需要筛选的）
    var whereSql = ""
        get() {
            var sql = ""
            if (orgId.notEmpty()) sql += " and orgId = '${orgId}' "
            if (floorName.notEmpty()) sql += " and floorName = '${floorName}' "
            if (ids.notEmpty()) sql += " and id in (${ids}) "
            return sql
        }

    constructor()
}

@Service
open class SiteService(mapper: SiteMapper, private val siteconfigMapper: SiteconfigMapper) : BaseService<Site, SiteMapper, SiteSearch>(mapper) {

    fun checkMacAddrExists(model: Site):Result {
        //获取当前登录账户的机构Id 仅查询该机构下的数据
        var orgId = getUser()!!.branchOrganizationId

        var count = mapper.checkMacAddrExists(orgId, model.macAddr, model.id)
        if (count > 0) {
            return Result.getError("该设备MAC已被绑定，请更换！")
        }
        return Result.getSuccess("MAC地址可用！")
    }

    fun checkDeviceExists(model: Site):Result {
        //获取当前登录账户的机构Id 仅查询该机构下的数据
        var orgId = getUser()!!.branchOrganizationId

        //校验Mac是否已被绑定
        if (model.macAddr.isNotEmpty()) {
            var macRlt = checkMacAddrExists(model)
            if (macRlt.rlt != 0) {
                return macRlt //如果校验不通过，直接返回错误信息
            }
        }

        //校验站点编号是否重复
        var count = mapper.checkSiteNumberExists(orgId, model.siteNumber, model.id)
        if (count > 0) {
            return Result.getError("设备编码已存在，请修改！")
        }

        return Result.getSuccess("设备信息可用！")
    }

    fun getAllByFloorName(): Result {
        //获取当前登录账户的机构Id 仅查询该机构下的数据
        var orgId = getUser()!!.branchOrganizationId

        var list = mapper.getList(SiteSearch().apply { this.orgId = orgId })
        list.forEach {
            it.content = getBean(ContentMapper::class.java).getContentBySiteId(it.id)
        }
        var map = list.groupBy { it.floorName }

        //给map键排序
        var floorList = getBean(FloorMapper::class.java).getList(FloorSearch().apply { this.orgId = orgId })
        var result = ArrayList<HashMap<String, List<Site>>>()
        floorList.forEach {
            if (map.containsKey(it.floorName) && map[it.floorName] != null) {
                result.add(hashMapOf(it.floorName to map[it.floorName]!!))
            }
        }

        return Result.getSuccess(result)
    }

    override fun delete(id: String): Result {
        var rlt = super.delete(id)
        if (rlt.rlt == 0 && rlt.datas != 0 ) { //删除成功 删除图片
            getBean(ContentService::class.java).deleteEntityListBySiteId(id)
        }

        //站点被删除 站点配置里的站点也要删除
        //查询所有配置了该站点的配置
        var siteconfigList = siteconfigMapper.getList(SiteconfigSearch().apply { siteId = id })
        //更新该配置站点信息
        siteconfigList.forEach {
            var siteIdList = ArrayList<String>()
            it.siteId.split(",").forEach { siteId ->
                if (siteId != id) { //如果站点ID不等于被删除的站点ID
                    siteIdList.add(siteId)
                }
            }
            it.siteId = siteIdList.joinToString(",") //更新站点ID
            siteconfigMapper.updateSiteInfo(it.id, it.siteId) //保存更新后的站点配置
        }

        return rlt
    }

    override fun save(model: Site): Result {

        if (model.id.isEmpty()) { //新增
            model.siteStatus = "normal" //默认正常
            //获取当前登录账户的机构Id 赋值
            var orgId = getUser()!!.branchOrganizationId
            model.orgId = orgId //设置机构Id
        }

        //校验Mac是否已被绑定 & 校验站点编号是否重复
        var checkRlt = checkDeviceExists(model)
        if (checkRlt.rlt != 0) {
            return checkRlt //如果校验不通过，直接返回错误信息
        }

        var rlt = super.save(model)
        if (rlt.rlt == 0) { //保存成功 插入图片
            model.content?.let {
                it.id = uuid()
                it.siteId = model.id
                getBean(ContentMapper::class.java).insert(it)
            }
        }

        return rlt
    }

    override fun getInfo(id: String): Result {
         var rlt = super.getInfo(id)
        if (rlt.rlt == 0 && rlt.datas != null) {
            val site = rlt.datas as Site
            site.content = getBean(ContentMapper::class.java).getContentBySiteId(id)
        }
        return rlt
    }

    override fun getList(page: SiteSearch): Result {
        //获取当前登录账户的机构Id 仅查询该机构下的数据
        if (page.ifContainOrgId != "0") {
            var orgId = getUser()!!.branchOrganizationId
            page.orgId = orgId
        }

        var result: Any?
        var list: List<Site>
        if (page.ifPage) {
            // 使用分页查询
            result = PageHelper.startPage<Site>(page.currentPage!!, page.pageRecord!!)
                .doSelectPageInfo<Site> {
                    mapper.getList(page)
                }
                .paginationInfo
            list = result.result as List<Site>
        }else {
            //不使用分页查询
            result = mapper.getList(page)
            list = result
        }

        list.forEach {
            it.content = getBean(ContentMapper::class.java).getContentBySiteId(it.id)
        }

        return Result.getSuccess(result)
    }

}

@RestController
@RequestMapping("/api/Site")
open class SiteResource(service: SiteService) : BaseResource<SiteSearch, Site, SiteMapper, SiteService>(service) {

    /**
     * 获取所有站点信息，按楼层分组
     */
    @GetMapping("/getAllByFloorName")
    fun getAllByFloorName(): Result {
        return service.getAllByFloorName()
    }

    /**
     * 校验设备MAC地址是否已被绑定
     */
    @PostMapping("/checkMacAddrExists")
    fun checkMacAddrExists(@RequestBody model: Site):Result {
        return service.checkMacAddrExists(model)
    }

    /**
     * 校验设备MAC地址是否已被绑定
     * 校验站点编号是否已存在
     */
    @PostMapping("/checkDeviceExists")
    fun checkDeviceExists(@RequestBody model: Site):Result {
        return service.checkDeviceExists(model)
    }
}

