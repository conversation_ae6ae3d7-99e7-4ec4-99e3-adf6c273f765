package com.shenlan.smartlogixmini.auto

import com.github.pagehelper.PageHelper
import com.shenlan.smartlogixmini.mybatis.paginationInfo
import com.shenlan.smartlogixmini.util.*
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.Date


class Site : BaseModel {

    /** 站点名称 */
    var siteName: String = ""
    /** 站点编号 */
    var siteNumber: String = ""
    /** 楼层名称 */
    var floorName: String = ""
    /** 站点位置 */
    var sitePosition: String = ""
    /** 备注 */
    var remarks: String = ""
    /** 二维码标签状况(normal-正常 abnormal-异常) */
    var siteStatus: String = ""
    /** 二维码标签状况更新时间 */
    var statusChangeTime: Date ? = null
    /** 二维码标签状况更新人员 */
    var statusChangeMan: String = ""

    /** 打卡状态(completed-已完成 waiting-待打卡 notInTime-未到打卡时间 missing-缺卡) */
    var inspectionResult: String = ""
    var content: Content ?= null
    var inspectionrecordList: List<Inspectionrecord>? = null
    var checkInProgress: String = "" //打卡进度 已打卡次数/总打卡次数
    var ifCheckIn: Boolean ?=null //是否可以打卡(true-可以打卡 false-不可以打卡)
    var siteConfigId: String = ""

    constructor()
}

@Mapper
interface SiteMapper : BaseMapper<Site> {

    @Update(""" update tbl_site set siteStatus = #{siteStatus}, statusChangeTime = now(), statusChangeMan = #{statusChangeMan} where id = #{siteId} """)
    fun changeStatus(siteId: String,siteStatus: String,statusChangeMan: String): Int

    @Select(""" select * from tbl_site where 1=1 ${'$'}{whereSql} order by sysCreated """)
    override fun getList(search: BaseSearch): List<Site>

}

class SiteSearch : BaseSearch {

    var floorName:String = ""
    var idsList: List<String>? = null
    var ids:String = ""
        get() {
            if (idsList != null && idsList!!.isNotEmpty()) {
                return idsList!!.getInStr()
            }
            return field
        }
    var whereSql = ""
        get() {
            var sql = ""
            if (floorName.notEmpty()) sql += " and floorName = '${floorName}' "
            if (ids.notEmpty()) sql += " and id in (${ids}) "
            return sql
        }

    constructor()
}

@Service
open class SiteService(mapper: SiteMapper) : BaseService<Site, SiteMapper>(mapper) {

    fun changeStatus(siteId: String,siteStatus: String): Result {

        var statusChangeMan = getUser()!!.name
        mapper.changeStatus(siteId, siteStatus, statusChangeMan)

        return Result.getSuccess("二维码标签状况已更新！")
    }

    fun getAllByFloorName(): Result {
        var list = mapper.getList(SiteSearch().apply { ifPage = false })
        var map = list.groupBy { it.floorName }
        return Result.getSuccess(map)
    }

    override fun delete(id: String): Result {
        var rlt = super.delete(id)
        if (rlt.rlt == 0 && rlt.datas != 0 ) { //删除成功 删除图片
            getBean(ContentMapper::class.java).deleteBySiteId(id)
        }
        return rlt
    }

    override fun save(model: Site): Result {

        if (model.id.isEmpty()) { //新增
            model.siteStatus = "normal" //默认正常
        }

        var rlt = super.save(model)
        if (rlt.rlt == 0) { //保存成功 插入图片
            model.content?.let {
                it.id = uuid()
                it.siteId = model.id
                getBean(ContentMapper::class.java).insert(it)
            }
        }
        return rlt
    }

    override fun getInfo(id: String): Result {
         var rlt = super.getInfo(id)
        if (rlt.rlt == 0 && rlt.datas != null) {
            val site = rlt.datas as Site
            site.content = getBean(ContentMapper::class.java).getContentBySiteId(id)
        }
        return rlt
    }

    override fun getList(page: BaseSearch): Result {

        var result: Any?
        var list: List<Site>
        if (page.ifPage) {
            // 使用分页查询
            result = PageHelper.startPage<Site>(page.currentPage!!, page.pageRecord!!)
                .doSelectPageInfo<Site> {
                    mapper.getList(page)
                }
                .paginationInfo
            list = result.result as List<Site>
        }else {
            //不使用分页查询
            result = mapper.getList(page)
            list = result
        }

        list.forEach {
            it.content = getBean(ContentMapper::class.java).getContentBySiteId(it.id)
        }

        return Result.getSuccess(result)
    }

}

@RestController
@RequestMapping("/api/Site")
open class SiteResource(service: SiteService) : BaseResource<SiteSearch, Site, SiteMapper, SiteService>(service) {

    @GetMapping("/getAllByFloorName")
    fun getAllByFloorName(): Result {
        return service.getAllByFloorName()
    }

    @GetMapping("/changeStatus/{siteId}/{siteStatus}")
    fun changeStatus(@PathVariable siteId: String,@PathVariable siteStatus: String): Result {
        return service.changeStatus(siteId, siteStatus)
    }
}

