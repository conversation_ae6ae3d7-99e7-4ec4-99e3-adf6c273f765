package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.getChineseFirstLetters
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

/**
 * 人员实体类
 */
class Personnel : BaseModel() {
    // 数据库表字段

    /** 组织机构ID */
    var organizationId: String = ""
    /** 姓名 */
    var name: String = ""
    /** 姓名首字母 */
    var nameInitials: String = ""
    /** 手机号码 */
    var phone: String = ""
    /** 职务 */
    var position: String = ""
    /** 邮箱 */
    var email: String = ""
    /** 人员类型(0-内部成员,1-外部成员) */
    var type: Int = 0
    /** 用户名 */
    var username: String? = null
        set(value) {
            field = if (value.isNullOrEmpty()) null else value
        }
    /** 密码 */
    var password: String = ""
    /** 是否是工作组创建的默认账号(0-否,1-是) */
    var ifWorkgroupDefault: Int = 0

    // 关联字段

    /** 关联的组织机构信息 */
    var organization: Organization? = null
    /** 车辆信息列表 */
    var vehicleinfoList: List<Vehicleinfo> = listOf()
    /** 所属工作组列表 */
    var workgroupList: List<Workgroup> = listOf()
    /** 权限列表 */
    var permissionList: List<Permission> = listOf()
}

/**
 * 人员分组类 - 统一的分组结构
 */
class PersonnelGroup {
    /** 分组类型(organization-组织机构, workgroup-工作组, external-外部人员) */
    var groupType: String = ""
    /** 分组名称 */
    var groupName: String = ""
    /** 分组成员列表 */
    var members: List<Personnel> = listOf()
    /** 排序值 */
    var sortOrder: Int = 0
}

/**
 * 人员查询条件类
 */
class PersonnelSearch : BaseSearch() {
    /** 关键词(姓名,姓名首字母) */
    var keyword: String = ""
    /** 姓名模糊匹配 */
    var nameLike: String = ""
    /** 组织机构ID */
    var organizationId: String = ""
    /** 人员类型(0-内部成员,1-外部成员) */
    var type: Int? = null
    /** 需要排除的人员ID */
    var excludeId: String = ""
    /** 是否返回分组结果 */
    var ifGrouped: Boolean = false
    /** 是否按组织机构分组 */
    var groupByOrganization: Boolean = false
    /** 是否按工作组分组 */
    var groupByWorkgroup: Boolean = false
    /** 是否排除当前登录用户 */
    var excludeCurrentUser: Boolean = false
    /** 工作组ID(查询指定工作组的成员) */
    var workgroupId: String = ""
    /** 工作组名称(按工作组名称查询成员) */
    var workgroupName: String = ""
    /** 权限ID列表(筛选拥有这些权限的人员) */
    var permissionIdList: List<String> = listOf()
    /** 权限名称列表(筛选拥有这些权限的人员) */
    var permissionNameList: List<String> = listOf()
    /** 是否加载车辆信息 */
    var loadVehicleinfoList: Boolean = false
    /** 是否加载组织机构信息 */
    var loadOrganization: Boolean = false
    /** 是否加载工作组列表 */
    var loadWorkgroupList: Boolean = false
    /** 是否加载权限列表 */
    var loadPermissionList: Boolean = false
}

/**
 * 人员Mapper接口
 */
@Mapper
interface PersonnelMapper : BaseMapper<Personnel> {

    @Select("""
        <script>
            SELECT DISTINCT p.* FROM tbl_personnel p
            LEFT JOIN tbl_organization o ON p.organizationId = o.id
            <if test="workgroupId != '' or workgroupName != ''">
                INNER JOIN tbl_workgrouppersonnel wp ON p.id = wp.personnelId
                INNER JOIN tbl_workgroup w ON wp.workgroupId = w.id
            </if>
            <if test="permissionIdList.size > 0 or permissionNameList.size > 0">
                INNER JOIN (
                    SELECT DISTINCT personnelId FROM (
                        SELECT pp.personnelId FROM tbl_personnelpermission pp 
                        INNER JOIN tbl_permission perm ON pp.permissionId = perm.id
                        WHERE pp.sysDeleted = 0 AND perm.sysDeleted = 0
                        <if test="permissionIdList.size > 0">
                            AND pp.permissionId IN 
                            <foreach collection="permissionIdList" item="permissionId" open="(" separator="," close=")">
                                #{permissionId}
                            </foreach>
                        </if>
                        <if test="permissionNameList.size > 0">
                            AND perm.name IN 
                            <foreach collection="permissionNameList" item="permissionName" open="(" separator="," close=")">
                                #{permissionName}
                            </foreach>
                        </if>
                        UNION
                        SELECT wgp.personnelId FROM tbl_workgrouppermission wp
                        INNER JOIN tbl_workgrouppersonnel wgp ON wp.workgroupId = wgp.workgroupId
                        INNER JOIN tbl_permission perm ON wp.permissionId = perm.id
                        WHERE wp.sysDeleted = 0 AND wgp.sysDeleted = 0 AND perm.sysDeleted = 0
                        <if test="permissionIdList.size > 0">
                            AND wp.permissionId IN 
                            <foreach collection="permissionIdList" item="permissionId" open="(" separator="," close=")">
                                #{permissionId}
                            </foreach>
                        </if>
                        <if test="permissionNameList.size > 0">
                            AND perm.name IN 
                            <foreach collection="permissionNameList" item="permissionName" open="(" separator="," close=")">
                                #{permissionName}
                            </foreach>
                        </if>
                    ) AS combined_permissions
                ) AS personnel_with_permissions ON p.id = personnel_with_permissions.personnelId
            </if>
            <where>
                p.sysDeleted = 0
                <if test="keyword != ''">
                    AND (p.name LIKE CONCAT('%', #{keyword}, '%') 
                         OR p.nameInitials LIKE CONCAT('%', UPPER(#{keyword}), '%'))
                </if>
                <if test="nameLike != ''">
                    AND p.name LIKE CONCAT('%', #{nameLike}, '%')
                </if>
                <if test="organizationId != ''">
                    AND p.organizationId = #{organizationId}
                </if>
                <if test="type != null">
                    AND p.type = #{type}
                </if>
                <if test="excludeId != ''">
                    AND p.id != #{excludeId}
                </if>
                <if test="workgroupId != ''">
                    AND wp.workgroupId = #{workgroupId}
                </if>
                <if test="workgroupName != ''">
                    AND w.name = #{workgroupName}
                </if>
            </where>
            ORDER BY p.name
        </script>
    """)
    override fun getList(search: BaseSearch): List<Personnel>

    /**
     * 根据手机号码查询单个人员信息
     */
    @Select("""
        SELECT * FROM tbl_personnel
        WHERE sysDeleted = 0 AND phone = #{phone} LIMIT 1
    """)
    fun getInfoByPhone(phone: String): Personnel?

    /**
     * 根据用户名查询单个人员信息
     */
    @Select("""
        SELECT * FROM tbl_personnel
        WHERE sysDeleted = 0 AND username = #{username} LIMIT 1
    """)
    fun getInfoByUsername(username: String): Personnel?

    /**
     * 重写getInfo方法
     */
    @Select("""
        SELECT * FROM tbl_personnel
        WHERE id = #{id} AND sysDeleted = 0
    """)
    override fun getInfo(id: String): Personnel?

    /**
     * 根据permissionName获取personnel列表
     */
    @Select("""
        SELECT * FROM tbl_personnel WHERE id IN (
            SELECT personnelId FROM tbl_personnelpermission WHERE permissionId = (SELECT id FROM tbl_permission WHERE name = #{permissionName} )
        );
    """)
    fun getListByPermissionName(permissionName: String): List<Personnel>

    /**
     * 获取所有用户id
     */
    @Select("""
        SELECT id FROM tbl_personnel
    """)
    fun getAllPersonnelIds(): List<String>
}

/**
 * 人员Service类
 */
@Service
class PersonnelService(
    mapper: PersonnelMapper,
    private val vehicleinfoMapper: VehicleinfoMapper,
    private val organizationMapper: OrganizationMapper,
    private val workgroupMapper: WorkgroupMapper,
    private val workgrouppersonnelMapper: WorkgrouppersonnelMapper,
    private val permissionMapper: PermissionMapper,
    private val personnelpermissionMapper: PersonnelpermissionMapper
) : BaseService<Personnel, PersonnelMapper>(mapper) {

    /**
     * 重写save方法，确保手机号不重复
     */
    @Transactional
    override fun saveEntity(entity: Personnel): String {
        // 检查手机号不能为空
        if (entity.phone.isEmpty()) {
            throw BusinessException("手机号码不能为空")
        }

        // 检查组织机构ID不能为空（外部成员除外）
        if (entity.type == 0 && entity.organizationId.isEmpty()) {
            throw BusinessException("内部成员必须选择组织机构")
        }

        // 查询是否存在相同手机号的人员（除了当前编辑的人员）
        val existingPersonnel = mapper.getInfoByPhone(entity.phone)
        if (existingPersonnel != null && existingPersonnel.id != entity.id) {
            throw BusinessException("手机号码已存在，请使用其他手机号码")
        }

        // 自动生成拼音首字母
        entity.nameInitials = getChineseFirstLetters(entity.name)

        // 手机号验证通过，调用父类的save方法完成保存
        val personnelId = super.saveEntity(entity)

        // 处理权限关联
        processPermissionRelations(personnelId, entity.permissionList)

        // 处理工作组关联
        processWorkgroupRelations(personnelId, entity.workgroupList)

        return personnelId
    }

    override fun getEntityPage(search: BaseSearch): PaginationInfo<Personnel> {
        // 处理排除当前用户的逻辑
        if (search is PersonnelSearch && search.excludeCurrentUser) {
            val userPhone = getUser()?.phone ?: ""

            if (userPhone.isNotEmpty()) {
                val currentPersonnel = mapper.getInfoByPhone(userPhone)
                if (currentPersonnel != null) {
                    search.excludeId = currentPersonnel.id
                }
            }
        }

        // 常规处理流程
        val paginationInfo = super.getEntityPage(search)
        val personnelList = paginationInfo.result

        // 权限筛选已在SQL层完成，无需再次过滤
        search as PersonnelSearch

        // 根据分组需求决定是否加载组织机构信息
        if ((search.ifGrouped && search.groupByOrganization) || search.loadOrganization) {
            loadOrganizationsBatch(personnelList)
        }

        // 根据分组需求决定是否加载工作组信息
        if ((search.ifGrouped && search.groupByWorkgroup) || search.loadWorkgroupList) {
            loadWorkgroupsBatch(personnelList)
        }

        // 如果需要加载车辆信息
        if (search.loadVehicleinfoList) {
            personnelList.forEach { loadVehicles(it) }
        }

        // 如果需要加载权限信息
        if (search.loadPermissionList) {
            personnelList.forEach { loadPermissions(it) }
        }

        // 更新分页信息中的结果列表
        return paginationInfo.withResult(personnelList)
    }


    /**
     * 处理人员分组逻辑
     */
    fun processPersonnelGroups(search: PersonnelSearch, personnelList: List<Personnel>): List<PersonnelGroup> {
        return createPersonnelGroups(search, personnelList)
    }

    /**
     * 创建人员分组结果
     * 顺序：机构 -> 工作组 -> 外部人员
     */
    private fun createPersonnelGroups(page: PersonnelSearch, personnelList: List<Personnel>): List<PersonnelGroup> {
        val groups = mutableListOf<PersonnelGroup>()

        // 分离内部成员和外部成员
        val internalPersonnel = personnelList.filter { it.type == 0 }
        val externalPersonnel = personnelList.filter { it.type == 1 }

        // 1. 按组织机构分组（仅内部成员）
        if (page.groupByOrganization) {
            val organizationGroups = createOrganizationGroups(internalPersonnel)
            groups.addAll(organizationGroups)
        }

        // 2. 按工作组分组（包含内部成员和外部成员）
        if (page.groupByWorkgroup) {
            val workgroupGroups = createWorkgroupGroups(personnelList)
            groups.addAll(workgroupGroups)
        }

        // 3. 外部人员单独分组
        if (externalPersonnel.isNotEmpty()) {
            val externalGroup = PersonnelGroup()
            externalGroup.groupType = "external"
            externalGroup.groupName = "外部人员"
            externalGroup.members = externalPersonnel
            externalGroup.sortOrder = Int.MAX_VALUE // 确保外部人员组排在最后
            groups.add(externalGroup)
        }

        return groups
    }

    /**
     * 创建按组织机构分组的结果（仅处理内部成员）
     */
    private fun createOrganizationGroups(personnelList: List<Personnel>): List<PersonnelGroup> {
        // 按组织机构分组内部成员
        return personnelList.groupBy { it.organization?.name ?: "" }
            .filter { it.key.isNotEmpty() } // 过滤掉没有组织机构的人员
            .map { (organizationName, members) ->
                val group = PersonnelGroup()
                group.groupType = "organization"
                group.groupName = organizationName
                group.members = members
                group.sortOrder = members.firstOrNull()?.organization?.sortOrder ?: Int.MAX_VALUE
                group
            }
            .sortedBy { it.sortOrder }
    }

    /**
     * 创建按工作组分组的结果（包含内部成员和外部成员）
     */
    private fun createWorkgroupGroups(personnelList: List<Personnel>): List<PersonnelGroup> {
        // 收集所有工作组和对应的人员
        val workgroupMemberMap = mutableMapOf<String, MutableList<Personnel>>()
        val workgroupSortOrderMap = mutableMapOf<String, Int>()

        // 遍历人员，将每个人员添加到其所属的所有工作组中
        personnelList.forEach { personnel ->
            if (personnel.workgroupList.isNotEmpty()) {
                personnel.workgroupList.forEach { workgroup ->
                    val workgroupName = workgroup.name
                    if (!workgroupMemberMap.containsKey(workgroupName)) {
                        workgroupMemberMap[workgroupName] = mutableListOf()
                        workgroupSortOrderMap[workgroupName] = workgroup.sortOrder
                    }
                    workgroupMemberMap[workgroupName]!!.add(personnel)
                }
            }
        }

        // 转换为结果格式并排序
        return workgroupMemberMap.map { (workgroupName, members) ->
            val group = PersonnelGroup()
            group.groupType = "workgroup"
            group.groupName = workgroupName
            // 去重并排序：工作组默认账号置顶
            group.members = members.distinctBy { it.id }.sortedWith(compareBy { it.ifWorkgroupDefault != 1 })
            group.sortOrder = workgroupSortOrderMap[workgroupName] ?: Int.MAX_VALUE
            group
        }.sortedBy { it.sortOrder }
    }

    /**
     * 重写getInfo方法，加载人员的关联信息
     */
    override fun getEntity(id: String): Personnel? {
        val personnel = mapper.getInfo(id)
        if (personnel != null) {
            // 加载人员的组织机构信息
            loadOrganizationsBatch(listOf(personnel))
            // 加载人员的车辆信息
            loadVehicles(personnel)
            // 加载人员的工作组信息
            loadWorkgroupsBatch(listOf(personnel))
            // 加载人员的权限信息
            loadPermissions(personnel)
        }
        return personnel
    }

    /**
     * 批量加载人员的组织机构信息
     */
    private fun loadOrganizationsBatch(personnelList: List<Personnel>) {
        // 收集所有organizationId（去重、过滤空值）
        val organizationIds = personnelList
            .filter { it.organizationId.isNotEmpty() }
            .map { it.organizationId }
            .distinct()

        if (organizationIds.isEmpty()) return

        // 批量查询组织机构
        val organizations = organizationMapper.getListByIds(organizationIds)

        // 建立ID到对象的映射
        val organizationMap = organizations.associateBy { it.id }

        // 分配给对应人员
        personnelList.forEach { personnel ->
            if (personnel.organizationId.isNotEmpty()) {
                personnel.organization = organizationMap[personnel.organizationId]
            }
        }
    }

    /**
     * 批量加载人员的工作组信息
     */
    private fun loadWorkgroupsBatch(personnelList: List<Personnel>) {
        val personnelIds = personnelList.map { it.id }
        if (personnelIds.isEmpty()) return

        // 批量查询人员-工作组关系
        val relations = workgrouppersonnelMapper.getListByPersonnelIds(personnelIds)

        // 收集所有工作组ID并批量查询工作组信息
        val workgroupIds = relations.map { it.workgroupId }.distinct()
        if (workgroupIds.isEmpty()) return

        val workgroups = workgroupMapper.getListByIds(workgroupIds)
        val workgroupMap = workgroups.associateBy { it.id }

        // 构建人员ID到工作组列表的映射
        val personnelWorkgroupMap = mutableMapOf<String, MutableList<Workgroup>>()

        relations.forEach { relation ->
            val workgroup = workgroupMap[relation.workgroupId]
            if (workgroup != null) {
                if (!personnelWorkgroupMap.containsKey(relation.personnelId)) {
                    personnelWorkgroupMap[relation.personnelId] = mutableListOf()
                }
                personnelWorkgroupMap[relation.personnelId]!!.add(workgroup)
            }
        }

        // 分配给对应人员
        personnelList.forEach { personnel ->
            personnel.workgroupList = personnelWorkgroupMap[personnel.id] ?: listOf()
        }
    }

    /**
     * 加载人员的车辆信息
     */
    private fun loadVehicles(personnel: Personnel) {
        val search = VehicleinfoSearch()
        search.personnelId = personnel.id
        search.ifPage = false
        val vehicles = vehicleinfoMapper.getList(search)
        personnel.vehicleinfoList = vehicles
    }

    /**
     * 加载人员的权限信息
     */
    private fun loadPermissions(personnel: Personnel) {
        personnel.permissionList = permissionMapper.getFinalPermissionsByPersonnelId(personnel.id)
    }

    /**
     * 处理人员权限关联
     */
    private fun processPermissionRelations(personnelId: String, permissionList: List<Permission>) {
        // 先删除该人员的所有现有权限关联
        personnelpermissionMapper.deleteByPersonnelId(personnelId)

        // 如果权限列表不为空，则添加新权限关联
        if (permissionList.isNotEmpty()) {
            // 创建新的权限关联记录列表
            val personnelpermissionList = mutableListOf<BaseModel>()
            permissionList.forEach { permission ->
                if (permission.id.isNotEmpty()) {
                    val personnelpermission = Personnelpermission()
                    personnelpermission.id = uuid()
                    personnelpermission.personnelId = personnelId
                    personnelpermission.permissionId = permission.id
                    personnelpermissionList.add(personnelpermission)
                }
            }

            // 使用BaseMapper的insertList方法批量插入
            if (personnelpermissionList.isNotEmpty()) {
                personnelpermissionMapper.insertList(personnelpermissionList)
            }
        }
    }

    /**
     * 处理人员工作组关联
     */
    private fun processWorkgroupRelations(personnelId: String, workgroupList: List<Workgroup>) {
        // 先删除该人员的所有现有工作组关联
        workgrouppersonnelMapper.deleteByPersonnelId(personnelId)

        // 如果工作组列表不为空，则添加新工作组关联
        if (workgroupList.isNotEmpty()) {
            // 创建新的工作组关联记录列表
            val workgrouppersonnelList = mutableListOf<BaseModel>()
            workgroupList.forEach { workgroup ->
                if (workgroup.id.isNotEmpty()) {
                    val workgrouppersonnel = Workgrouppersonnel()
                    workgrouppersonnel.id = uuid()
                    workgrouppersonnel.personnelId = personnelId
                    workgrouppersonnel.workgroupId = workgroup.id
                    workgrouppersonnelList.add(workgrouppersonnel)
                }
            }

            // 使用BaseMapper的insertList方法批量插入
            if (workgrouppersonnelList.isNotEmpty()) {
                workgrouppersonnelMapper.insertList(workgrouppersonnelList)
            }
        }
    }


    /**
     * 根据当前登录用户的手机号码获取对应人员信息
     * 如果找不到对应人员，则返回null
     *
     * @return 人员信息对象
     */
    fun getCurrentPersonnel(): Personnel? {
        return getUser()
    }

    /**
     * 根据手机号码获取人员信息
     *
     * @param phone 手机号码
     * @return 人员信息对象，如果找不到则返回null
     */
    fun getPersonnelByPhone(phone: String): Personnel? {
        if (phone.isEmpty()) {
            return null
        }

        val personnel = mapper.getInfoByPhone(phone)
        if (personnel != null) {
            return getEntity(personnel.id)
        }

        return null
    }

    /**
     * 根据用户名获取人员信息
     *
     * @param username 用户名
     * @return 人员信息对象，如果找不到则返回null
     */
    fun getPersonnelByUsername(username: String): Personnel? {
        if (username.isEmpty()) {
            return null
        }

        val personnel = mapper.getInfoByUsername(username)
        if (personnel != null) {
            return getEntity(personnel.id)
        }

        return null
    }
}

/**
 * 人员Controller类
 */
@RestController
@RequestMapping("/api/Personnel")
class PersonnelResource(service: PersonnelService) : BaseResource<PersonnelSearch, Personnel, PersonnelMapper, PersonnelService>(service) {

    /**
     * 重写getList方法，处理分组逻辑
     */
    @PostMapping("/getList")
    override fun getList(@Valid @RequestBody search: PersonnelSearch): Result {
        val paginationInfo = service.getEntityPage(search)
        val personnelList = paginationInfo.result

        if (search.ifGrouped) {
            val groupedResult = service.processPersonnelGroups(search, personnelList)
            val result: Any = if (search.ifPage) {
                PaginationInfo.of(search, groupedResult)
            } else {
                groupedResult
            }
            return Result.getSuccess(result)
        } else {
            val result: Any = if (search.ifPage) {
                paginationInfo
            } else {
                personnelList
            }
            return Result.getSuccess(result)
        }
    }

    /**
     * 获取当前登录用户对应的人员信息
     */
    @GetMapping("/getCurrentPersonnel")
    fun getCurrentPersonnel(): Result {
        val personnel = service.getCurrentPersonnel()
        return Result.getSuccess(personnel)
    }
}
