package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.getChineseFirstLetters
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 人员实体类
 */
class Personnel : BaseModel() {
    // 数据库表字段

    /** 组织机构ID */
    var organizationId: String = ""
    /** 姓名 */
    var name: String = ""
    /** 姓名首字母 */
    var nameInitials: String = ""
    /** 手机号码 */
    var phone: String = ""
    /** 职务 */
    var position: String = ""
    /** 邮箱 */
    var email: String = ""
    /** 人员类型(0-内部成员,1-外部成员) */
    var type: Int = 0
    /** 状态(0-禁用,1-启用) */
    var status: Int = 1

    // 关联字段

    /** 关联的组织机构信息 */
    var organization: Organization? = null
    /** 车辆信息列表 */
    var vehicleinfoList: List<Vehicleinfo> = listOf()
    /** 所属工作组列表 */
    var workgroupList: List<Workgroup> = listOf()
}

/**
 * 人员分组类 - 统一的分组结构
 */
class PersonnelGroup {
    /** 分组类型(organization-组织机构, workgroup-工作组, external-外部人员) */
    var groupType: String = ""
    /** 分组名称 */
    var groupName: String = ""
    /** 分组成员列表 */
    var members: List<Personnel> = listOf()
    /** 排序值 */
    var sortOrder: Int = 0
}

/**
 * 人员查询条件类
 */
class PersonnelSearch : BaseSearch() {
    /** 关键词(姓名) */
    var keyword: String = ""
    /** 姓名模糊匹配 */
    var nameLike: String = ""
    /** 组织机构ID */
    var organizationId: String = ""
    /** 人员类型(0-内部成员,1-外部成员) */
    var type: Int? = null
    /** 需要排除的人员ID */
    var excludeId: String = ""
    /** 是否返回分组结果 */
    var ifGrouped: Boolean = false
    /** 是否按组织机构分组 */
    var groupByOrganization: Boolean = false
    /** 是否按工作组分组 */
    var groupByWorkgroup: Boolean = false
    /** 是否排除当前登录用户 */
    var excludeCurrentUser: Boolean = false
    /** 工作组ID(查询指定工作组的成员) */
    var workgroupId: String = ""
    /** 是否加载车辆信息 */
    var loadVehicleinfoList: Boolean = false
    /** 是否加载组织机构信息 */
    var loadOrganization: Boolean = false
    /** 是否加载工作组列表 */
    var loadWorkgroupList: Boolean = false
}

/**
 * 人员Mapper接口
 */
@Mapper
interface PersonnelMapper : BaseMapper<Personnel> {

    @Select("""
        <script>
            SELECT p.* FROM tbl_personnel p
            LEFT JOIN tbl_organization o ON p.organizationId = o.id
            <if test="workgroupId != ''">
                INNER JOIN tbl_workgrouppersonnel wp ON p.id = wp.personnelId
            </if>
            <where>
                p.sysDeleted = 0
                <if test="keyword != ''">
                    AND (p.name LIKE CONCAT('%', #{keyword}, '%') 
                         OR p.nameInitials LIKE CONCAT('%', UPPER(#{keyword}), '%'))
                </if>
                <if test="nameLike != ''">
                    AND p.name LIKE CONCAT('%', #{nameLike}, '%')
                </if>
                <if test="organizationId != ''">
                    AND p.organizationId = #{organizationId}
                </if>
                <if test="type != null">
                    AND p.type = #{type}
                </if>
                <if test="excludeId != ''">
                    AND p.id != #{excludeId}
                </if>
                <if test="workgroupId != ''">
                    AND wp.workgroupId = #{workgroupId}
                </if>
            </where>
            ORDER BY o.sortOrder, o.name, p.name
        </script>
    """)
    override fun getList(search: BaseSearch): List<Personnel>

    /**
     * 根据手机号码查询单个人员信息
     */
    @Select("""
        SELECT * FROM tbl_personnel
        WHERE sysDeleted = 0 AND phone = #{phone} LIMIT 1
    """)
    fun getInfoByPhone(phone: String): Personnel?

    /**
     * 重写getInfo方法
     */
    @Select("""
        SELECT * FROM tbl_personnel
        WHERE id = #{id} AND sysDeleted = 0
    """)
    override fun getInfo(id: String): Personnel?
}

/**
 * 人员Service类
 */
@Service
class PersonnelService(
    mapper: PersonnelMapper,
    private val vehicleinfoMapper: VehicleinfoMapper,
    private val organizationMapper: OrganizationMapper,
    private val workgroupService: WorkgroupService
) : BaseService<Personnel, PersonnelMapper>(mapper) {

    /**
     * 重写save方法，确保手机号不重复
     */
    override fun save(model: Personnel): Result {
        // 检查手机号不能为空
        if (model.phone.isEmpty()) {
            return Result.getError("手机号码不能为空")
        }

        // 检查组织机构ID不能为空（外部成员除外）
        if (model.type == 0 && model.organizationId.isEmpty()) {
            return Result.getError("内部成员必须选择组织机构")
        }

        // 查询是否存在相同手机号的人员（除了当前编辑的人员）
        val existingPersonnel = mapper.getInfoByPhone(model.phone)
        if (existingPersonnel != null && existingPersonnel.id != model.id) {
            return Result.getError("手机号码已存在，请使用其他手机号码")
        }

        // 自动生成拼音首字母
        model.nameInitials = getChineseFirstLetters(model.name)

        // 手机号验证通过，调用父类的save方法完成保存
        return super.save(model)
    }

    /**
     * 重写getList方法，根据需要加载关联信息和处理分组逻辑
     */
    override fun getList(page: BaseSearch): Result {
        // 处理排除当前用户的逻辑
        if (page is PersonnelSearch && page.excludeCurrentUser) {
            val userPhone = getUser()?.phone ?: ""

            if (userPhone.isNotEmpty()) {
                val currentPersonnel = mapper.getInfoByPhone(userPhone)
                if (currentPersonnel != null) {
                    page.excludeId = currentPersonnel.id
                }
            }
        }

        // 常规处理流程
        val result = super.getList(page)
        if (page is PersonnelSearch) {
            // 获取人员列表
            val personnelList = result.toList<Personnel>()

            // 根据分组需求决定是否加载组织机构信息
            if ((page.ifGrouped && page.groupByOrganization) || page.loadOrganization) {
                personnelList.forEach { loadOrganization(it) }
            }

            // 根据分组需求决定是否加载工作组信息
            if ((page.ifGrouped && page.groupByWorkgroup) || page.loadWorkgroupList) {
                personnelList.forEach { loadWorkgroups(it) }
            }

            // 如果需要加载车辆信息
            if (page.loadVehicleinfoList) {
                personnelList.forEach { loadVehicles(it) }
            }

            // 如果需要返回分组结果
            if (page.ifGrouped) {
                val groupedResult = createPersonnelGroups(page, personnelList)

                // 替换结果数据
                val data = result.datas
                if (data is PaginationInfo) {
                    data.result = groupedResult
                } else {
                    result.datas = groupedResult
                }
            }
        }
        return result
    }

    /**
     * 创建人员分组结果
     * 顺序：机构 -> 工作组 -> 外部人员
     */
    private fun createPersonnelGroups(page: PersonnelSearch, personnelList: List<Personnel>): List<PersonnelGroup> {
        val groups = mutableListOf<PersonnelGroup>()

        // 分离内部成员和外部成员
        val internalPersonnel = personnelList.filter { it.type == 0 }
        val externalPersonnel = personnelList.filter { it.type == 1 }

        // 1. 按组织机构分组（仅内部成员）
        if (page.groupByOrganization) {
            val organizationGroups = createOrganizationGroups(internalPersonnel)
            groups.addAll(organizationGroups)
        }

        // 2. 按工作组分组（包含内部成员和外部成员）
        if (page.groupByWorkgroup) {
            val workgroupGroups = createWorkgroupGroups(personnelList)
            groups.addAll(workgroupGroups)
        }

        // 3. 外部人员单独分组
        if (externalPersonnel.isNotEmpty()) {
            val externalGroup = PersonnelGroup()
            externalGroup.groupType = "external"
            externalGroup.groupName = "外部人员"
            externalGroup.members = externalPersonnel
            externalGroup.sortOrder = Int.MAX_VALUE // 确保外部人员组排在最后
            groups.add(externalGroup)
        }

        return groups
    }

    /**
     * 创建按组织机构分组的结果（仅处理内部成员）
     */
    private fun createOrganizationGroups(personnelList: List<Personnel>): List<PersonnelGroup> {
        // 按组织机构分组内部成员
        return personnelList.groupBy { it.organization?.name ?: "" }
            .filter { it.key.isNotEmpty() } // 过滤掉没有组织机构的人员
            .map { (organizationName, members) ->
                val group = PersonnelGroup()
                group.groupType = "organization"
                group.groupName = organizationName
                group.members = members
                group.sortOrder = members.firstOrNull()?.organization?.sortOrder ?: Int.MAX_VALUE
                group
            }
            .sortedBy { it.sortOrder }
    }

    /**
     * 创建按工作组分组的结果（包含内部成员和外部成员）
     */
    private fun createWorkgroupGroups(personnelList: List<Personnel>): List<PersonnelGroup> {
        // 收集所有工作组和对应的人员
        val workgroupMemberMap = mutableMapOf<String, MutableList<Personnel>>()
        val workgroupSortOrderMap = mutableMapOf<String, Int>()

        // 遍历人员，将每个人员添加到其所属的所有工作组中
        personnelList.forEach { personnel ->
            if (personnel.workgroupList.isNotEmpty()) {
                personnel.workgroupList.forEach { workgroup ->
                    val workgroupName = workgroup.name
                    if (!workgroupMemberMap.containsKey(workgroupName)) {
                        workgroupMemberMap[workgroupName] = mutableListOf()
                        workgroupSortOrderMap[workgroupName] = workgroup.sortOrder
                    }
                    workgroupMemberMap[workgroupName]!!.add(personnel)
                }
            }
        }

        // 转换为结果格式并排序
        return workgroupMemberMap.map { (workgroupName, members) ->
            val group = PersonnelGroup()
            group.groupType = "workgroup"
            group.groupName = workgroupName
            group.members = members.distinctBy { it.id } // 去重，防止同一人员在同一工作组中重复
            group.sortOrder = workgroupSortOrderMap[workgroupName] ?: Int.MAX_VALUE
            group
        }.sortedBy { it.sortOrder }
    }

    /**
     * 重写getInfo方法，加载人员的关联信息
     */
    override fun getInfo(id: String): Result {
        val personnel = mapper.getInfo(id)
        if (personnel != null) {
            // 加载人员的组织机构信息
            loadOrganization(personnel)
            // 加载人员的车辆信息
            loadVehicles(personnel)
            // 加载人员的工作组信息
            loadWorkgroups(personnel)
        }
        return Result.getSuccess(personnel)
    }

    /**
     * 加载人员的组织机构信息
     */
    private fun loadOrganization(personnel: Personnel) {
        if (personnel.organizationId.isNotEmpty()) {
            personnel.organization = organizationMapper.getInfo(personnel.organizationId)
        }
    }

    /**
     * 加载人员的车辆信息
     */
    private fun loadVehicles(personnel: Personnel) {
        val search = VehicleinfoSearch()
        search.personnelId = personnel.id
        search.ifPage = false
        val vehicles = vehicleinfoMapper.getList(search)
        personnel.vehicleinfoList = vehicles
    }

    /**
     * 加载人员的工作组信息
     */
    private fun loadWorkgroups(personnel: Personnel) {
        val search = WorkgroupSearch()
        search.personnelId = personnel.id
        search.ifPage = false
        personnel.workgroupList = workgroupService.getDataList(search)
    }

    /**
     * 根据当前登录用户的手机号码获取对应人员信息
     * 如果找不到对应人员，则返回null
     *
     * @return 人员信息对象
     */
    fun getCurrentPersonnel(): Personnel? {
        val userPhone = getUser()?.phone ?: ""
        log.info("Current user phone: {}", userPhone)

        if (userPhone.isNotEmpty()) {
            val personnel = mapper.getInfoByPhone(userPhone)
            if (personnel != null) {
                log.info("Found personnel info: ID: {}, Name: {}", personnel.id, personnel.name)
                // 加载组织机构信息
                loadOrganization(personnel)
                return personnel
            }
        }

        log.info("Personnel info not found")
        return null
    }

    /**
     * 根据手机号码获取人员信息
     *
     * @param phone 手机号码
     * @return 人员信息对象，如果找不到则返回null
     */
    fun getPersonnelByPhone(phone: String): Personnel? {
        if (phone.isEmpty()) {
            return null
        }

        val personnel = mapper.getInfoByPhone(phone)
        if (personnel != null) {
            // 加载人员的组织机构信息
            loadOrganization(personnel)
            // 加载人员的车辆信息
            loadVehicles(personnel)
            // 加载人员的工作组信息
            loadWorkgroups(personnel)
        }

        return personnel
    }
}

/**
 * 人员Controller类
 */
@RestController
@RequestMapping("/api/Personnel")
class PersonnelResource(service: PersonnelService) : BaseResource<PersonnelSearch, Personnel, PersonnelMapper, PersonnelService>(service) {

    /**
     * 获取当前登录用户对应的人员信息
     */
    @GetMapping("/getCurrentPersonnel")
    fun getCurrentPersonnel(): Result {
        val personnel = service.getCurrentPersonnel()
        return Result.getSuccess(personnel)
    }
}
