package com.shenlan.smartlogixmini.auto

import com.jayway.jsonpath.Configuration
import com.jayway.jsonpath.JsonPath
import com.jayway.jsonpath.Option
import com.shenlan.smartlogixmini.config.AppUser
import com.shenlan.smartlogixmini.config.LoginMethod
import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.AliyunSmsUtil
import com.shenlan.smartlogixmini.util.getChineseFirstLetters
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.uuid
import okhttp3.OkHttpClient
import okhttp3.Request
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import java.util.concurrent.TimeUnit
import javax.validation.Valid

/**
 * 人员实体类
 */
class Personnel : BaseEntity() {
    // 数据库表字段

    /** 所属工作组ID（作为默认账号时） */
    var ownerWorkgroupId: String = ""
    /** 组织机构ID */
    var organizationId: String = ""
    /** 姓名 */
    var name: String = ""
    /** 姓名首字母 */
    var nameInitials: String = ""
    /** 手机号码 */
    var phone: String = ""
    /** 职务 */
    var position: String = ""
    /** 邮箱 */
    var email: String = ""
    /** 人员类型(0-内部成员,1-外部成员) */
    var type: Int = 0
    /** 用户名 */
    var username: String? = null
        set(value) {
            field = if (value.isNullOrEmpty()) null else value
        }
    /** 密码 */
    var password: String = ""
    /** 微信OpenID */
    var wechatOpenId: String = ""
    /** 是否启用 */
    var ifEnabled: Boolean = true

    // 关联字段

    /** 关联的组织机构信息 */
    var organization: Organization? = null
    /** 车辆信息列表 */
    var vehicleinfoList: List<Vehicleinfo> = listOf()
    /** 所属工作组列表 */
    var workgroupList: List<Workgroup> = listOf()
    /** 人员原始权限列表 */
    var permissionList: List<Permission> = listOf()
    /** 最终权限列表(人员权限+工作组权限) */
    var finalPermissionList: List<Permission> = listOf()

    // 其他字段

    /** 当前登录方式(WECHAT_PHONE-微信手机号,PHONE_CODE-手机验证码,USERNAME_PASSWORD-账号密码) */
    var currentLoginMethod: LoginMethod? = null
}

/**
 * 人员分组类 - 统一的分组结构
 */
class PersonnelGroup {
    /** 分组类型(organization-组织机构, workgroup-工作组, external-外部人员) */
    var groupType: String = ""
    /** 分组名称 */
    var groupName: String = ""
    /** 分组成员列表 */
    var members: List<Personnel> = listOf()
    /** 排序值 */
    var sortOrder: Int = 0
}

/**
 * 人员查询条件类
 */
class PersonnelSearch : BaseSearch() {
    /** 所属工作组ID（作为默认账号时，空字符串表示筛选非工作组默认账号） */
    var ownerWorkgroupId: String? = null
    /** 关键词(姓名,姓名首字母) */
    var keyword: String = ""
    /** 姓名模糊匹配 */
    var nameLike: String = ""
    /** 组织机构ID */
    var organizationId: String = ""
    /** 职务 */
    var position: String = ""
    /** 人员类型(0-内部成员,1-外部成员) */
    var type: Int? = null
    /** 是否启用 */
    var ifEnabled: Boolean? = true
    /** 需要排除的人员ID */
    var excludeId: String = ""
    /** 是否返回分组结果 */
    var ifGrouped: Boolean = false
    /** 是否按组织机构分组 */
    var groupByOrganization: Boolean = false
    /** 是否按工作组分组 */
    var groupByWorkgroup: Boolean = false
    /** 是否排除当前登录用户 */
    var excludeCurrentUser: Boolean = false
    /** 工作组ID(查询指定工作组的成员) */
    var workgroupId: String = ""
    /** 工作组名称(按工作组名称查询成员) */
    var workgroupName: String = ""
    /** 权限ID列表(筛选拥有这些权限的人员) */
    var permissionIdList: List<String> = listOf()
    /** 权限名称列表(筛选拥有这些权限的人员) */
    var permissionNameList: List<String> = listOf()
    /** 是否加载车辆信息 */
    var loadVehicleinfoList: Boolean = false
    /** 是否加载组织机构信息 */
    var loadOrganization: Boolean = false
    /** 是否加载工作组列表 */
    var loadWorkgroupList: Boolean = false
    /** 是否加载人员原始权限列表 */
    var loadPermissionList: Boolean = false
    /** 是否加载最终权限列表(人员权限+工作组权限) */
    var loadFinalPermissionList: Boolean = false
}

/**
 * 人员Mapper接口
 */
@Mapper
interface PersonnelMapper : BaseMapper<Personnel> {

    @Select("""
        <script>
            SELECT DISTINCT p.* FROM tbl_personnel p
            LEFT JOIN tbl_organization o ON p.organizationId = o.id
            <if test="workgroupId != '' or workgroupName != ''">
                INNER JOIN tbl_workgrouppersonnel wp ON p.id = wp.personnelId
                INNER JOIN tbl_workgroup w ON wp.workgroupId = w.id
            </if>
            <if test="permissionIdList.size > 0 or permissionNameList.size > 0">
                INNER JOIN (
                    SELECT DISTINCT personnelId FROM (
                        SELECT pp.personnelId FROM tbl_personnelpermission pp 
                        INNER JOIN tbl_permission perm ON pp.permissionId = perm.id
                        WHERE pp.sysDeleted = 0 AND perm.sysDeleted = 0
                        <if test="permissionIdList.size > 0">
                            AND pp.permissionId IN 
                            <foreach collection="permissionIdList" item="permissionId" open="(" separator="," close=")">
                                #{permissionId}
                            </foreach>
                        </if>
                        <if test="permissionNameList.size > 0">
                            AND perm.name IN 
                            <foreach collection="permissionNameList" item="permissionName" open="(" separator="," close=")">
                                #{permissionName}
                            </foreach>
                        </if>
                        UNION
                        SELECT wgp.personnelId FROM tbl_workgrouppermission wp
                        INNER JOIN tbl_workgrouppersonnel wgp ON wp.workgroupId = wgp.workgroupId
                        INNER JOIN tbl_permission perm ON wp.permissionId = perm.id
                        WHERE wp.sysDeleted = 0 AND wgp.sysDeleted = 0 AND perm.sysDeleted = 0
                        <if test="permissionIdList.size > 0">
                            AND wp.permissionId IN 
                            <foreach collection="permissionIdList" item="permissionId" open="(" separator="," close=")">
                                #{permissionId}
                            </foreach>
                        </if>
                        <if test="permissionNameList.size > 0">
                            AND perm.name IN 
                            <foreach collection="permissionNameList" item="permissionName" open="(" separator="," close=")">
                                #{permissionName}
                            </foreach>
                        </if>
                    ) AS combined_permissions
                ) AS personnel_with_permissions ON p.id = personnel_with_permissions.personnelId
            </if>
            <where>
                p.sysDeleted = 0
                <if test="ownerWorkgroupId != null">
                    AND p.ownerWorkgroupId = #{ownerWorkgroupId}
                </if>
                <if test="keyword != ''">
                    AND (p.name LIKE CONCAT('%', #{keyword}, '%') 
                         OR p.nameInitials LIKE CONCAT('%', UPPER(#{keyword}), '%'))
                </if>
                <if test="nameLike != ''">
                    AND p.name LIKE CONCAT('%', #{nameLike}, '%')
                </if>
                <if test="organizationId != ''">
                    AND p.organizationId = #{organizationId}
                </if>
                <if test="position != ''">
                    AND p.position = #{position}
                </if>
                <if test="type != null">
                    AND p.type = #{type}
                </if>
                <if test="ifEnabled != null">
                    AND p.ifEnabled = #{ifEnabled}
                </if>
                <if test="excludeId != ''">
                    AND p.id != #{excludeId}
                </if>
                <if test="workgroupId != ''">
                    AND wp.workgroupId = #{workgroupId}
                </if>
                <if test="workgroupName != ''">
                    AND w.name = #{workgroupName}
                </if>
            </where>
            ORDER BY p.nameInitials
        </script>
    """)
    override fun getList(search: BaseSearch): List<Personnel>

    /**
     * 根据手机号码查询单个人员信息
     */
    @Select("""
        SELECT * FROM tbl_personnel
        WHERE sysDeleted = 0 AND phone = #{phone} LIMIT 1
    """)
    fun getInfoByPhone(phone: String): Personnel?

    /**
     * 根据用户名查询单个人员信息
     */
    @Select("""
        SELECT * FROM tbl_personnel
        WHERE sysDeleted = 0 AND username = #{username} LIMIT 1
    """)
    fun getInfoByUsername(username: String): Personnel?

    /**
     * 重写getInfo方法
     */
    @Select("""
        SELECT * FROM tbl_personnel
        WHERE id = #{id} AND sysDeleted = 0
    """)
    override fun getInfo(id: String): Personnel?

    /**
     * 清除指定微信OpenID的其他绑定
     */
    @Update("""
        UPDATE tbl_personnel 
        SET wechatOpenId = '' 
        WHERE wechatOpenId = #{wechatOpenId} AND id != #{excludeUserId} AND sysDeleted = 0
    """)
    fun clearWechatOpenIdBinding(wechatOpenId: String, excludeUserId: String): Int

    /**
     * 更新用户的微信OpenID
     */
    @Update("""
        UPDATE tbl_personnel 
        SET wechatOpenId = #{wechatOpenId} 
        WHERE id = #{userId} AND sysDeleted = 0
    """)
    fun updateWechatOpenId(userId: String, wechatOpenId: String): Int

    /**
     * 清除指定职务的所有人员的position字段
     */
    @Update("""
        UPDATE tbl_personnel 
        SET position = '' 
        WHERE position = #{position} AND sysDeleted = 0
    """)
    fun clearPosition(position: String): Int
}

/**
 * 人员Service类
 */
@Service
class PersonnelService(
    mapper: PersonnelMapper,
    private val vehicleinfoMapper: VehicleinfoMapper,
    private val organizationMapper: OrganizationMapper,
    private val workgroupMapper: WorkgroupMapper,
    private val workgrouppersonnelMapper: WorkgrouppersonnelMapper,
    private val permissionMapper: PermissionMapper,
    private val personnelpermissionMapper: PersonnelpermissionMapper,
    private val personnelpermissionService: PersonnelpermissionService
) : BaseService<Personnel, PersonnelMapper, PersonnelSearch>(mapper) {

    /**
     * 重写save方法，确保手机号不重复
     */
    @Transactional
    override fun saveEntity(entity: Personnel): String {
        // 检查手机号不能为空（工作组默认账号除外）
        if (entity.phone.isEmpty() && entity.ownerWorkgroupId.isEmpty()) {
            throw BusinessException("手机号码不能为空")
        }

        // 检查组织机构ID不能为空（外部成员和工作组默认账号除外）
        if (entity.type == 0 && entity.organizationId.isEmpty() && entity.ownerWorkgroupId.isEmpty()) {
            throw BusinessException("内部成员必须选择组织机构")
        }

        // 查询是否存在相同手机号的人员（除了当前编辑的人员，跳过手机号为空的情况）
        if (entity.phone.isNotEmpty()) {
            val existingPersonnel = mapper.getInfoByPhone(entity.phone)
            if (existingPersonnel != null && existingPersonnel.id != entity.id) {
                throw BusinessException("手机号码已存在，请重新输入")
            }
        }

        // 自动生成拼音首字母
        entity.nameInitials = getChineseFirstLetters(entity.name)

        // 手机号验证通过，调用父类的save方法完成保存
        val personnelId = super.saveEntity(entity)

        // 处理权限关联
        processPermissionRelations(personnelId, entity.permissionList)

        // 处理工作组关联
        processWorkgroupRelations(personnelId, entity.workgroupList)

        return personnelId
    }

    override fun getEntityPage(search: PersonnelSearch): PaginationInfo<Personnel> {
        // 处理排除当前用户的逻辑
        if (search.excludeCurrentUser) {
            val userPhone = getUser()?.phone ?: ""

            if (userPhone.isNotEmpty()) {
                val currentPersonnel = mapper.getInfoByPhone(userPhone)
                if (currentPersonnel != null) {
                    search.excludeId = currentPersonnel.id
                }
            }
        }

        // 常规处理流程
        val paginationInfo = super.getEntityPage(search)
        val personnelList = paginationInfo.result

        // 根据分组需求决定是否加载组织机构信息
        if ((search.ifGrouped && search.groupByOrganization) || search.loadOrganization) {
            loadOrganizationsBatch(personnelList)
        }

        // 根据分组需求决定是否加载工作组信息
        if ((search.ifGrouped && search.groupByWorkgroup) || search.loadWorkgroupList) {
            loadWorkgroupsBatch(personnelList)
        }

        // 如果需要加载车辆信息
        if (search.loadVehicleinfoList) {
            personnelList.forEach { loadVehicles(it) }
        }

        // 如果需要加载人员原始权限信息
        if (search.loadPermissionList) {
            personnelList.forEach { loadPermissions(it) }
        }

        // 如果需要加载最终权限信息
        if (search.loadFinalPermissionList) {
            personnelList.forEach { loadFinalPermissions(it) }
        }

        // 更新分页信息中的结果列表
        return paginationInfo.withResult(personnelList)
    }


    /**
     * 处理人员分组逻辑
     */
    fun processPersonnelGroups(search: PersonnelSearch, personnelList: List<Personnel>): List<PersonnelGroup> {
        return createPersonnelGroups(search, personnelList)
    }

    /**
     * 创建人员分组结果
     * 顺序：机构 -> 工作组 -> 外部人员
     */
    private fun createPersonnelGroups(page: PersonnelSearch, personnelList: List<Personnel>): List<PersonnelGroup> {
        val groups = mutableListOf<PersonnelGroup>()

        // 分离内部成员和外部成员
        val internalPersonnel = personnelList.filter { it.type == 0 }
        val externalPersonnel = personnelList.filter { it.type == 1 }

        // 1. 按组织机构分组（仅内部成员）
        if (page.groupByOrganization) {
            val organizationGroups = createOrganizationGroups(internalPersonnel)
            groups.addAll(organizationGroups)
        }

        // 2. 按工作组分组（包含内部成员和外部成员）
        if (page.groupByWorkgroup) {
            val workgroupGroups = createWorkgroupGroups(personnelList)
            groups.addAll(workgroupGroups)
        }

        // 3. 外部人员单独分组
        if (externalPersonnel.isNotEmpty()) {
            val externalGroup = PersonnelGroup()
            externalGroup.groupType = "external"
            externalGroup.groupName = "外部人员"
            externalGroup.members = externalPersonnel
            externalGroup.sortOrder = Int.MAX_VALUE // 确保外部人员组排在最后
            groups.add(externalGroup)
        }

        return groups
    }

    /**
     * 创建按组织机构分组的结果（仅处理内部成员）
     */
    private fun createOrganizationGroups(personnelList: List<Personnel>): List<PersonnelGroup> {
        // 按组织机构分组内部成员
        return personnelList.groupBy { it.organization?.name ?: "" }
            .filter { it.key.isNotEmpty() } // 过滤掉没有组织机构的人员
            .map { (organizationName, members) ->
                val group = PersonnelGroup()
                group.groupType = "organization"
                group.groupName = organizationName
                group.members = members
                group.sortOrder = members.firstOrNull()?.organization?.sortOrder ?: Int.MAX_VALUE
                group
            }
            .sortedBy { it.sortOrder }
    }

    /**
     * 创建按工作组分组的结果（包含内部成员和外部成员）
     */
    private fun createWorkgroupGroups(personnelList: List<Personnel>): List<PersonnelGroup> {
        // 收集所有工作组和对应的人员
        val workgroupMemberMap = mutableMapOf<String, MutableList<Personnel>>()
        val workgroupSortOrderMap = mutableMapOf<String, Int>()

        // 遍历人员，将每个人员添加到其所属的所有工作组中
        personnelList.forEach { personnel ->
            if (personnel.workgroupList.isNotEmpty()) {
                personnel.workgroupList.forEach { workgroup ->
                    val workgroupName = workgroup.name
                    if (!workgroupMemberMap.containsKey(workgroupName)) {
                        workgroupMemberMap[workgroupName] = mutableListOf()
                        workgroupSortOrderMap[workgroupName] = workgroup.sortOrder
                    }
                    workgroupMemberMap[workgroupName]!!.add(personnel)
                }
            }
        }

        // 转换为结果格式并排序
        return workgroupMemberMap.map { (workgroupName, members) ->
            val group = PersonnelGroup()
            group.groupType = "workgroup"
            group.groupName = workgroupName
            // 去重并排序：工作组默认账号置顶
            group.members = members.distinctBy { it.id }.sortedWith(compareBy { it.ownerWorkgroupId.isEmpty() })
            group.sortOrder = workgroupSortOrderMap[workgroupName] ?: Int.MAX_VALUE
            group
        }.sortedBy { it.sortOrder }
    }

    /**
     * 重写getInfo方法，加载人员的关联信息
     */
    override fun getEntity(id: String): Personnel? {
        val personnel = mapper.getInfo(id)
        if (personnel != null) {
            // 加载人员的组织机构信息
            loadOrganizationsBatch(listOf(personnel))
            // 加载人员的车辆信息
            loadVehicles(personnel)
            // 加载人员的工作组信息
            loadWorkgroupsBatch(listOf(personnel))
            // 加载人员的原始权限信息
            loadPermissions(personnel)
            // 加载人员的最终权限信息
            loadFinalPermissions(personnel)
        }
        return personnel
    }

    /**
     * 批量加载人员的组织机构信息
     */
    private fun loadOrganizationsBatch(personnelList: List<Personnel>) {
        // 收集所有organizationId（去重、过滤空值）
        val organizationIds = personnelList
            .filter { it.organizationId.isNotEmpty() }
            .map { it.organizationId }
            .distinct()

        if (organizationIds.isEmpty()) return

        // 批量查询组织机构
        val organizations = organizationMapper.getListByIds(organizationIds)

        // 建立ID到对象的映射
        val organizationMap = organizations.associateBy { it.id }

        // 分配给对应人员
        personnelList.forEach { personnel ->
            if (personnel.organizationId.isNotEmpty()) {
                personnel.organization = organizationMap[personnel.organizationId]
            }
        }
    }

    /**
     * 批量加载人员的工作组信息
     */
    private fun loadWorkgroupsBatch(personnelList: List<Personnel>) {
        val personnelIds = personnelList.map { it.id }
        if (personnelIds.isEmpty()) return

        // 批量查询人员-工作组关系
        val relations = workgrouppersonnelMapper.getListByPersonnelIds(personnelIds)

        // 收集所有工作组ID并批量查询工作组信息
        val workgroupIds = relations.map { it.workgroupId }.distinct()
        if (workgroupIds.isEmpty()) return

        val workgroups = workgroupMapper.getListByIds(workgroupIds)
        val workgroupMap = workgroups.associateBy { it.id }

        // 构建人员ID到工作组列表的映射
        val personnelWorkgroupMap = mutableMapOf<String, MutableList<Workgroup>>()

        relations.forEach { relation ->
            val workgroup = workgroupMap[relation.workgroupId]
            if (workgroup != null) {
                if (!personnelWorkgroupMap.containsKey(relation.personnelId)) {
                    personnelWorkgroupMap[relation.personnelId] = mutableListOf()
                }
                personnelWorkgroupMap[relation.personnelId]!!.add(workgroup)
            }
        }

        // 分配给对应人员
        personnelList.forEach { personnel ->
            personnel.workgroupList = personnelWorkgroupMap[personnel.id] ?: listOf()
        }
    }

    /**
     * 加载人员的车辆信息
     */
    private fun loadVehicles(personnel: Personnel) {
        val search = VehicleinfoSearch()
        search.personnelId = personnel.id
        search.ifPage = false
        val vehicles = vehicleinfoMapper.getList(search)
        personnel.vehicleinfoList = vehicles
    }

    /**
     * 加载人员的原始权限信息（仅人员直接权限）
     */
    private fun loadPermissions(personnel: Personnel) {
        val search = PersonnelpermissionSearch()
        search.personnelId = personnel.id
        search.ifPage = false
        search.loadPermission = true
        val personnelpermissionList = personnelpermissionService.getEntityList(search)
        personnel.permissionList = personnelpermissionList.mapNotNull { it.permission }
    }

    /**
     * 加载人员的最终权限信息（人员权限+工作组权限）
     */
    private fun loadFinalPermissions(personnel: Personnel) {
        personnel.finalPermissionList = permissionMapper.getFinalPermissionsByPersonnelId(personnel.id)
    }

    /**
     * 处理人员权限关联
     */
    private fun processPermissionRelations(personnelId: String, permissionList: List<Permission>) {
        // 先删除该人员的所有现有权限关联
        personnelpermissionMapper.deleteByPersonnelId(personnelId)

        // 如果权限列表不为空，则添加新权限关联
        if (permissionList.isNotEmpty()) {
            // 创建新的权限关联记录列表
            val personnelpermissionList = mutableListOf<BaseEntity>()
            permissionList.forEach { permission ->
                if (permission.id.isNotEmpty()) {
                    val personnelpermission = Personnelpermission()
                    personnelpermission.id = uuid()
                    personnelpermission.personnelId = personnelId
                    personnelpermission.permissionId = permission.id
                    personnelpermissionList.add(personnelpermission)
                }
            }

            // 使用BaseMapper的insertList方法批量插入
            if (personnelpermissionList.isNotEmpty()) {
                personnelpermissionMapper.insertList(personnelpermissionList)
            }
        }
    }

    /**
     * 处理人员工作组关联
     */
    private fun processWorkgroupRelations(personnelId: String, workgroupList: List<Workgroup>) {
        // 先删除该人员的所有现有工作组关联
        workgrouppersonnelMapper.deleteByPersonnelId(personnelId)

        // 如果工作组列表不为空，则添加新工作组关联
        if (workgroupList.isNotEmpty()) {
            // 创建新的工作组关联记录列表
            val workgrouppersonnelList = mutableListOf<BaseEntity>()
            workgroupList.forEach { workgroup ->
                if (workgroup.id.isNotEmpty()) {
                    val workgrouppersonnel = Workgrouppersonnel()
                    workgrouppersonnel.id = uuid()
                    workgrouppersonnel.personnelId = personnelId
                    workgrouppersonnel.workgroupId = workgroup.id
                    workgrouppersonnelList.add(workgrouppersonnel)
                }
            }

            // 使用BaseMapper的insertList方法批量插入
            if (workgrouppersonnelList.isNotEmpty()) {
                workgrouppersonnelMapper.insertList(workgrouppersonnelList)
            }
        }
    }


    /**
     * 根据当前登录用户的手机号码获取对应人员信息
     * 如果找不到对应人员，则返回null
     *
     * @return 人员信息对象
     */
    fun getCurrentPersonnel(): Personnel? {
        return getUser()
    }

    /**
     * 根据手机号码获取人员信息
     *
     * @param phone 手机号码
     * @return 人员信息对象，如果找不到则返回null
     */
    fun getPersonnelByPhone(phone: String): Personnel? {
        if (phone.isEmpty()) {
            return null
        }

        val personnel = mapper.getInfoByPhone(phone)
        if (personnel != null) {
            return getEntity(personnel.id)
        }

        return null
    }

    /**
     * 根据用户名获取人员信息
     *
     * @param username 用户名
     * @return 人员信息对象，如果找不到则返回null
     */
    fun getPersonnelByUsername(username: String): Personnel? {
        if (username.isEmpty()) {
            return null
        }

        val personnel = mapper.getInfoByUsername(username)
        if (personnel != null) {
            return getEntity(personnel.id)
        }

        return null
    }

    /**
     * 重写deleteEntity方法，删除人员及其关联数据
     */
    @Transactional
    override fun deleteEntity(id: String): Int {
        // 先删除该人员的权限关联
        personnelpermissionMapper.deleteByPersonnelId(id)

        // 删除该人员的工作组关联
        workgrouppersonnelMapper.deleteByPersonnelId(id)

        // 删除人员本身
        return super.deleteEntity(id)
    }

    /**
     * 绑定微信账号到当前用户
     * @param wechatOpenId 微信OpenID
     * @return 绑定是否成功
     */
    @Transactional
    fun bindWechatAccount(wechatOpenId: String): Boolean {
        val currentUser = getUser() ?: return false

        // 清除该微信OpenID的其他绑定
        mapper.clearWechatOpenIdBinding(wechatOpenId, currentUser.id)

        // 绑定到当前用户
        val updateCount = mapper.updateWechatOpenId(currentUser.id, wechatOpenId)

        return updateCount > 0
    }
}

/**
 * 人员Controller类
 */
@RestController
@RequestMapping("/api/Personnel")
class PersonnelResource(
    service: PersonnelService,
    private val hairreservationService: HairreservationService,
    private val notifymessageService: NotifymessageService,
    private val aliyunSmsUtil: AliyunSmsUtil
) : BaseResource<PersonnelSearch, Personnel, PersonnelMapper, PersonnelService>(service) {

    @Value("\${wechat.app-id}")
    private lateinit var wechatAppId: String

    @Value("\${wechat.app-secret}")
    private lateinit var wechatAppSecret: String

    @Value("\${aliyun.sms.template.verification-code}")
    private lateinit var verificationCodeTemplate: String

    // HTTP客户端
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .build()

    /**
     * 重写getList方法，处理分组逻辑
     */
    @PostMapping("/getList")
    override fun getList(@Valid @RequestBody search: PersonnelSearch): Result {
        val paginationInfo = service.getEntityPage(search)
        val personnelList = paginationInfo.result

        if (search.ifGrouped) {
            val groupedResult = service.processPersonnelGroups(search, personnelList)
            val result: Any = if (search.ifPage) {
                PaginationInfo.ofFullList(search, groupedResult)
            } else {
                groupedResult
            }
            return Result.getSuccess(result)
        } else {
            val result: Any = if (search.ifPage) {
                paginationInfo
            } else {
                personnelList
            }
            return Result.getSuccess(result)
        }
    }

    /**
     * 获取当前登录用户对应的人员信息
     */
    @GetMapping("/getCurrentPersonnel")
    fun getCurrentPersonnel(): Result {
        val personnel = service.getCurrentPersonnel()
        return Result.getSuccess(personnel)
    }

    /**
     * 获取首页通知信息
     */
    @GetMapping("/getHomePageNotices")
    fun getHomePageNotices(): Result {
        val userId = getUser()!!.id

        // 获取理发通知
        val haircutResult = hairreservationService.getNoticeBypersonnelId()
        val haircutData = haircutResult.datas

        // 获取理发删除状态通知
        val haircutDeleteResult = hairreservationService.getDeleteStatuesByPersonnelId()
        val haircutDeleteData = haircutDeleteResult.datas

        // 获取菜单通知
        val menuSearch = NotifymessageSearch()
        menuSearch.userId = userId
        menuSearch.module = "menu"
        val menuResult = notifymessageService.getNotifyMessagesByUserIdAndModule(menuSearch)
        val menuData = menuResult.datas

        // 获取外卖通知
        val takeoutSearch = NotifymessageSearch()
        takeoutSearch.userId = userId
        takeoutSearch.module = "takeout"
        val takeoutResult = notifymessageService.getNotifyMessagesByUserIdAndModule(takeoutSearch)
        val takeoutData = takeoutResult.datas

        // 组装结果
        val result = mapOf(
            "haircut" to haircutData,
            "haircutDelete" to haircutDeleteData,
            "menu" to menuData,
            "takeout" to takeoutData
        )

        return Result.getSuccess(result)
    }

    /**
     * 绑定微信账号接口
     * @param code 微信小程序授权码
     * @return 绑定结果
     */
    @GetMapping("/bindWechat/{code}")
    fun bindWechat(@PathVariable code: String): Result {
        try {
            if (code.isEmpty()) {
                return Result.getError("code参数不能为空")
            }

            log.info("Binding WeChat account with code: $code")

            // 构建微信code2session请求URL
            val apiUrl = "https://api.weixin.qq.com/sns/jscode2session" +
                        "?appid=$wechatAppId" +
                        "&secret=$wechatAppSecret" +
                        "&js_code=$code" +
                        "&grant_type=authorization_code"

            // 调用微信接口
            val wechatRequest = Request.Builder()
                .url(apiUrl)
                .get()
                .build()

            httpClient.newCall(wechatRequest).execute().use { response ->
                if (!response.isSuccessful) {
                    log.error("Failed to call WeChat API: ${response.code()}")
                    return Result.getError("微信接口调用失败")
                }

                val responseBody = response.body()?.string()
                if (responseBody.isNullOrEmpty()) {
                    log.error("Empty response body from WeChat API")
                    return Result.getError("微信接口返回数据为空")
                }

                log.info("WeChat API response: $responseBody")

                // 创建安全的JsonPath配置
                val safeConfig: Configuration = Configuration.builder()
                    .options(Option.DEFAULT_PATH_LEAF_TO_NULL)
                    .options(Option.SUPPRESS_EXCEPTIONS)
                    .build()

                // 解析微信响应
                val jsonContext = JsonPath.using(safeConfig).parse(responseBody)

                // 检查错误码
                val errcode = jsonContext.read<Int>("$.errcode")
                if (errcode != null && errcode != 0) {
                    val errmsg = jsonContext.read<String>("$.errmsg") ?: "未知错误"
                    log.error("WeChat API error: $errcode - $errmsg")
                    return Result.getError("微信接口错误: $errmsg")
                }

                // 获取openid和session_key
                val openid = jsonContext.read<String>("$.openid")
                val sessionKey = jsonContext.read<String>("$.session_key")

                if (openid.isNullOrEmpty()) {
                    log.error("No openid in WeChat response")
                    return Result.getError("未能获取到微信OpenID")
                }

                if (sessionKey.isNullOrEmpty()) {
                    log.error("No session_key in WeChat response")
                    return Result.getError("未能获取到微信SessionKey")
                }

                log.info("Retrieved openid: $openid and session_key from WeChat")

                // 绑定微信账号到当前用户
                val bindSuccess = service.bindWechatAccount(openid)
                if (!bindSuccess) {
                    log.error("Failed to bind WeChat account to current user")
                    return Result.getError("绑定微信账号失败")
                }

                // 将session_key保存到当前AppUser对象
                val appUser = SecurityContextHolder.getContext().authentication.principal as? AppUser
                if (appUser != null) {
                    appUser.wechatSessionKey = sessionKey
                    log.info("Updated AppUser with session_key for user: ${appUser.username}")
                }

                log.info("Successfully bound WeChat account for user: ${appUser?.username}")
                return Result.getSuccessInfo("微信账号绑定成功")
            }

        } catch (e: Exception) {
            log.error("Error occurred while binding WeChat account", e)
            return Result.getError("绑定微信账号失败: ${e.message}")
        }
    }

    /**
     * 发送更换手机号验证码
     * @param newPhone 新手机号
     * @return 发送结果
     */
    @GetMapping("/sendChangePhoneCode/{newPhone}")
    fun sendChangePhoneCode(@PathVariable newPhone: String): Result {
        try {
            // 验证手机号格式
            if (!isValidPhoneNumber(newPhone)) {
                return Result.getError("手机号格式不正确")
            }

            // 获取当前用户信息
            val currentUser = getUser()
            if (currentUser == null) {
                return Result.getError("用户未登录")
            }

            // 检查新手机号是否与当前手机号相同
            if (currentUser.phone == newPhone) {
                return Result.getError("新手机号不能与当前手机号相同")
            }

            // 检查新手机号是否已被其他用户使用
            val existingPersonnel = service.getPersonnelByPhone(newPhone)
            if (existingPersonnel != null && existingPersonnel.id != currentUser.id) {
                return Result.getError("该手机号已被其他用户使用")
            }

            // 生成验证码
            val code = (java.util.Random().nextInt(9000) + 1000).toString()

            // 发送短信验证码
            val params = mapOf("code" to code)
            val smsResult = aliyunSmsUtil.sendSms(newPhone, params, verificationCodeTemplate)
            if (!smsResult) {
                return Result.getError("验证码发送失败，请稍后重试")
            }

            // 保存验证码到内存中
            changePhoneCodeMap[newPhone] = ChangePhoneCode(code, System.currentTimeMillis(), currentUser.id)

            log.info("Change phone verification code sent successfully to: {} for user: {}", newPhone, currentUser.name)
            return Result.getSuccessInfo("验证码已发送，请注意查收")

        } catch (e: Exception) {
            log.error("Error occurred while sending change phone verification code to: {}", newPhone)
            throw e
        }
    }

    /**
     * 验证验证码并更换手机号
     * @param newPhone 新手机号
     * @param code 验证码
     * @return 更换结果
     */
    @GetMapping("/changePhone/{newPhone}/{code}")
    fun changePhone(@PathVariable newPhone: String, @PathVariable code: String): Result {
        try {
            // 验证手机号格式
            if (!isValidPhoneNumber(newPhone)) {
                return Result.getError("手机号格式不正确")
            }

            // 验证验证码格式
            if (code.isBlank()) {
                return Result.getError("验证码不能为空")
            }

            // 获取当前用户信息
            val currentUser = getUser()
            if (currentUser == null) {
                return Result.getError("用户未登录")
            }

            // 检查新手机号是否与当前手机号相同
            if (currentUser.phone == newPhone) {
                return Result.getError("新手机号不能与当前手机号相同")
            }

            // 再次检查新手机号是否已被其他用户使用
            val existingPersonnel = service.getPersonnelByPhone(newPhone)
            if (existingPersonnel != null && existingPersonnel.id != currentUser.id) {
                return Result.getError("该手机号已被其他用户使用")
            }

            // 验证验证码
            val changePhoneCode = changePhoneCodeMap[newPhone]

            when {
                changePhoneCode == null -> {
                    log.warn("No verification code found for change phone request: {} -> {}", currentUser.phone, newPhone)
                    return Result.getError("请先获取验证码")
                }
                changePhoneCode.isTimeOut() -> {
                    log.warn("Verification code expired for change phone request: {} -> {}", currentUser.phone, newPhone)
                    changePhoneCodeMap.remove(newPhone) // 清除过期验证码
                    return Result.getError("验证码已失效，请重新获取验证码")
                }
                changePhoneCode.userId != currentUser.id -> {
                    log.warn("User ID mismatch for change phone request: {} -> {}", currentUser.phone, newPhone)
                    return Result.getError("验证码无效")
                }
                changePhoneCode.retryCount >= MAX_RETRY_COUNT -> {
                    log.warn("Too many retry attempts for change phone request: {} -> {}", currentUser.phone, newPhone)
                    changePhoneCodeMap.remove(newPhone) // 清除验证码
                    return Result.getError("验证码错误次数过多，请重新获取验证码")
                }
                changePhoneCode.code != code -> {
                    // 验证码错误，增加重试次数
                    val updatedCode = changePhoneCode.copy(retryCount = changePhoneCode.retryCount + 1)
                    changePhoneCodeMap[newPhone] = updatedCode

                    val remainingTries = MAX_RETRY_COUNT - updatedCode.retryCount
                    log.warn("Verification code mismatch for change phone request: {} -> {}, remaining tries: {}",
                        currentUser.phone, newPhone, remainingTries)

                    return if (remainingTries > 0) {
                        Result.getError("验证码不匹配，还可重试${remainingTries}次")
                    } else {
                        changePhoneCodeMap.remove(newPhone) // 清除验证码
                        Result.getError("验证码错误次数过多，请重新获取验证码")
                    }
                }
            }

            // 验证码正确，清除验证码
            changePhoneCodeMap.remove(newPhone)

            // 更新用户手机号
            val personnel = service.getEntity(currentUser.id)
            if (personnel == null) {
                return Result.getError("用户信息不存在")
            }

            personnel.phone = newPhone
            service.saveEntity(personnel)

            log.info("Phone number changed successfully for user: {} from {} to {}", currentUser.name, currentUser.phone, newPhone)
            return Result.getSuccessInfo("手机号更换成功")

        } catch (e: Exception) {
            log.error("Error occurred while changing phone number for user: {} to: {}", getUser()?.name, newPhone)
            throw e
        }
    }

    /**
     * 验证手机号格式
     * @param phoneNumber 手机号
     * @return 是否有效
     */
    private fun isValidPhoneNumber(phoneNumber: String): Boolean {
        // 简单的手机号格式验证（11位数字）
        return phoneNumber.matches(Regex("^1[3-9]\\d{9}$"))
    }

    /**
     * 更换手机号验证码数据类
     */
    private data class ChangePhoneCode(
        val code: String,
        val createTime: Long,
        val userId: String,
        val retryCount: Int = 0
    ) {
        /** 是否超时（5分钟） */
        fun isTimeOut(): Boolean {
            return System.currentTimeMillis() - createTime > 1000 * 60 * 5
        }
    }

    companion object {
        /** 更换手机号验证码Map，key: 新手机号, value: 验证码数据 */
        private val changePhoneCodeMap = hashMapOf<String, ChangePhoneCode>()

        /** 最大重试次数 */
        private const val MAX_RETRY_COUNT = 3
    }
}
