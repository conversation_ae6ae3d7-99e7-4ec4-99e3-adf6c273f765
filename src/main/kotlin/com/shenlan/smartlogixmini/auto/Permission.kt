package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 权限实体类
 */
class Permission : BaseModel() {
    // 数据库表字段

    /** 权限名称 */
    var name: String = ""
    /** 排序值 */
    var sortOrder: Int = 0
}

/**
 * 权限查询条件类
 */
class PermissionSearch : BaseSearch()

/**
 * 权限Mapper接口
 */
@Mapper
interface PermissionMapper : BaseMapper<Permission> {

    @Select("""
        SELECT * FROM tbl_permission
        WHERE sysDeleted = 0
        ORDER BY sortOrder, name
    """)
    override fun getList(search: BaseSearch): List<Permission>

    /**
     * 根据权限名称查询单个权限信息
     */
    @Select("""
        SELECT * FROM tbl_permission
        WHERE sysDeleted = 0 AND name = #{name} LIMIT 1
    """)
    fun getInfoByName(name: String): Permission?

    /**
     * 获取用户最终权限列表（个人权限 + 工作组权限）
     */
    @Select("""
        SELECT DISTINCT p.* FROM tbl_permission p
        WHERE p.sysDeleted = 0 AND p.id IN (
            SELECT pp.permissionId FROM tbl_personnelpermission pp
            WHERE pp.sysDeleted = 0 AND pp.personnelId = #{personnelId}
            UNION
            SELECT wp.permissionId FROM tbl_workgrouppermission wp
            INNER JOIN tbl_workgrouppersonnel wgp ON wp.workgroupId = wgp.workgroupId
            WHERE wp.sysDeleted = 0 AND wgp.sysDeleted = 0 AND wgp.personnelId = #{personnelId}
        )
        ORDER BY p.sortOrder, p.name
    """)
    fun getFinalPermissionsByPersonnelId(personnelId: String): List<Permission>

    /**
     * 检查用户是否拥有指定权限
     */
    @Select("""
        SELECT COUNT(*) > 0 FROM (
            SELECT pp.permissionId FROM tbl_personnelpermission pp
            WHERE pp.sysDeleted = 0
            AND pp.personnelId = #{personnelId} AND pp.permissionId = #{permissionId}
            UNION
            SELECT wp.permissionId FROM tbl_workgrouppermission wp
            INNER JOIN tbl_workgrouppersonnel wgp ON wp.workgroupId = wgp.workgroupId
            WHERE wp.sysDeleted = 0 AND wgp.sysDeleted = 0
            AND wgp.personnelId = #{personnelId} AND wp.permissionId = #{permissionId}
        ) AS combined_permissions
    """)
    fun hasPermission(personnelId: String, permissionId: String): Boolean
}

/**
 * 权限Service类
 */
@Service
class PermissionService(
    mapper: PermissionMapper
) : BaseService<Permission, PermissionMapper>(mapper) {

    /**
     * 重写save方法，确保权限名称不重复
     */
    @Transactional
    override fun save(model: Permission): Result {
        // 检查权限名称不能为空
        if (model.name.isEmpty()) {
            return Result.getError("权限名称不能为空")
        }

        // 查询是否存在相同名称的权限（除了当前编辑的权限）
        val existingPermission = mapper.getInfoByName(model.name)
        if (existingPermission != null && existingPermission.id != model.id) {
            return Result.getError("权限名称已存在，请使用其他权限名称")
        }

        // 验证通过，调用父类的save方法完成保存
        return super.save(model)
    }
}

/**
 * 权限Controller类
 */
@RestController
@RequestMapping("/api/Permission")
class PermissionResource(
    service: PermissionService,
    private val permissionMapper: PermissionMapper
) : BaseResource<PermissionSearch, Permission, PermissionMapper, PermissionService>(service) {

    /**
     * 获取用户最终权限列表
     */
    @GetMapping("/getFinalPermissions/{personnelId}")
    fun getFinalPermissions(@PathVariable personnelId: String): Result {
        // 格式校验
        if (personnelId.isEmpty()) {
            return Result.getError("人员ID不能为空")
        }

        // 直接调用Mapper层方法
        val permissionList = permissionMapper.getFinalPermissionsByPersonnelId(personnelId)
        return Result.getSuccess(permissionList)
    }

    /**
     * 检查用户是否拥有指定权限
     */
    @GetMapping("/checkPermission/{personnelId}/{permissionId}")
    fun checkPermission(@PathVariable personnelId: String, @PathVariable permissionId: String): Result {
        // 格式校验
        if (personnelId.isEmpty()) {
            return Result.getError("人员ID不能为空")
        }
        if (permissionId.isEmpty()) {
            return Result.getError("权限ID不能为空")
        }

        // 直接调用Mapper层方法
        val hasPermission = permissionMapper.hasPermission(personnelId, permissionId)
        return Result.getSuccess(hasPermission)
    }
}
