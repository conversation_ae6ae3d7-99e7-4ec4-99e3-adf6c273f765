package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Commutevehicleconfig: BaseEntity() {
    var carId: String = ""
    var carName: String = ""
    var sysDeleted: Int = 0
}

class CommutevehicleconfigSearch: BaseSearch() {
    var carName: String = ""
}

@Mapper
interface CommutevehicleconfigMapper : BaseMapper<Commutevehicleconfig> {
    @Select("""
        <script>
            SELECT * FROM tbl_commutevehicleconfig
            <where>
                AND sysDeleted = 0
                <if test="carName != ''">
                    AND carName LIKE CONCAT('%', #{carName}, '%')
                </if>
            </where>
            ORDER BY carName ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutevehicleconfig>

    // 获取所有班车
    @Select("""
        SELECT * FROM tbl_commutevehicleconfig WHERE sysDeleted = 0 order by sysCreated DESC 
    """)
    fun getAllVehicle(): List<Commutevehicleconfig>

    // 根据carName查询是否同名
    @Select("""
        SELECT * FROM tbl_commutevehicleconfig WHERE sysDeleted = 0 AND carName = #{carName}
    """)
    fun getInfoByCarName(carName: String): Commutevehicleconfig?

    // 根据id更新carId
    @Update("""
        UPDATE tbl_commutevehicleconfig
        SET carId = #{carId}
        WHERE id = #{id}
    """)
    fun updateCarId(id: String, carId: String)
}

@Service
class CommutevehicleconfigService(mapper: CommutevehicleconfigMapper) : BaseService<Commutevehicleconfig, CommutevehicleconfigMapper, CommutevehicleconfigSearch>(mapper) {
    override fun save(model: Commutevehicleconfig): Result {
        if (model.id.isEmpty() && mapper.getInfoByCarName(model.carName) != null)
            return Result.getError("车牌名称已存在")
        return super.save(model)
    }

    fun getAll() = mapper.getAllVehicle()
}

@RestController
@RequestMapping("/api/commutevehicleconfig")
class CommutevehicleconfigResource(service: CommutevehicleconfigService) : BaseResource<CommutevehicleconfigSearch, Commutevehicleconfig, CommutevehicleconfigMapper, CommutevehicleconfigService>(service) {
    @GetMapping("/getAll")
    fun getAll(): Result {
        return Result.getSuccess(service.getAll())
    }
}
