package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Takeoutmenudish: BaseEntity {
    var takeoutMenuId: String = ""
    var dishId: String = ""
    var dishName: String = ""
    var price: Double = 0.0
    var portionUnit: Int = 0
    var limitPerPerson: Int = 0
    var imageId: String = ""
    var sysDeleted: Int = 0

    // 订购数量
    var totalCount: Int = 0
    var imageUrl: String = ""
        get() = if (field.isEmpty()) field else AppPro.imgPrefix + field

    constructor()

    constructor(takeoutMenuId: String, takeoutMenuDish: Takeoutmenudish) {
        this.id = uuid()
        this.takeoutMenuId = takeoutMenuId
        this.dishId = takeoutMenuDish.dishId
        this.dishName = takeoutMenuDish.dishName
        this.price = takeoutMenuDish.price
        this.portionUnit = takeoutMenuDish.portionUnit
        this.limitPerPerson = takeoutMenuDish.limitPerPerson
        this.imageId = takeoutMenuDish.imageId
    }
}

class TakeoutmenudishSearch: BaseSearch() {
    var takeoutMenuId: String = ""
    var dishId: String = ""
    var dishName: String = ""
}

@Mapper
interface TakeoutmenudishMapper : BaseMapper<Takeoutmenudish> {
    @Select("""
        <script>
            SELECT td.*,di.imageUrl FROM tbl_takeoutmenudish td
            left join tbl_takeoutdish d on td.dishId = d.id
            left join tbl_dishimage di on d.imageId = di.id
            <where>
                AND td.sysDeleted = 0
                <if test="takeoutMenuId != ''">
                    AND td.takeoutMenuId = #{takeoutMenuId}
                </if>
                <if test="dishId != ''">
                    AND td.dishId = #{dishId}
                </if>
                <if test="dishName != ''">
                    AND td.dishName LIKE CONCAT('%', #{dishName}, '%')
                </if>
            </where>
            ORDER BY td.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Takeoutmenudish>

    // 根据MenuDate查询列表
    @Select("""
        <script>
            SELECT * from tbl_takeoutmenudish td 
            WHERE td.takeoutMenuId = (SELECT id from tbl_takeoutmenu WHERE menuDate = #{menuDate} limit 1)
        </script>
    """)
    fun getListByMenuDate(menuDate: String): List<Takeoutmenudish>

    @Update("""
        <script>
            UPDATE tbl_takeoutmenudish
            SET totalCount = CASE id
            <foreach collection="dishes" item="dish">
                WHEN #{dish.id} THEN #{dish.totalCount}
            </foreach>
            END
            WHERE id IN
            <foreach collection="dishes" item="dish" open="(" separator="," close=")">
                #{dish.id}
            </foreach>
        </script>
    """)
    fun batchUpdateTakeoutMenuDish(@Param("dishes") list: List<Takeoutmenudish>)

    // 根据id列表查询
    @Select("""
        <script>
            SELECT td.*,di.imageUrl FROM tbl_takeoutmenudish td
            left join tbl_takeoutdish d on td.dishId = d.id
            left join tbl_dishimage di on d.imageId = di.id
            WHERE td.id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </script>
    """)
    fun getListByIdList(@Param("list") list: List<String>): List<Takeoutmenudish>

    // 获取所有已发布的菜品id
    @Select("""
        select distinct(tmd.dishId) from tbl_takeoutmenudish tmd left join (select * from tbl_takeoutmenu where menuDate < #{date}) tm on tmd.takeoutMenuId = tm.id ;
    """)
    fun getAllDishId(date: String): List<String>

    @Delete("""
        DELETE FROM tbl_takeoutmenudish WHERE takeoutMenuId = #{takeoutMenuId}
    """)
    fun deleteByTakeoutMenuId(takeoutMenuId: String)
}

@Service
class TakeoutmenudishService(mapper: TakeoutmenudishMapper) : BaseService<Takeoutmenudish, TakeoutmenudishMapper, TakeoutmenudishSearch>(mapper)

@RestController
@RequestMapping("/api/takeoutmenudish")
class TakeoutmenudishResource(service: TakeoutmenudishService) : BaseResource<TakeoutmenudishSearch, Takeoutmenudish, TakeoutmenudishMapper, TakeoutmenudishService>(service)
