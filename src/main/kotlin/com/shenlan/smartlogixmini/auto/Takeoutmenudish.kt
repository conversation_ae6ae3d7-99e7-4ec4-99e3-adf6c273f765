package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

class Takeoutmenudish: BaseModel {
    var takeoutMenuId: String = ""
    var dishId: String = ""
    var dishName: String = ""
    var price: Double = 0.0
    var portionUnit: Int = 0
    var limitPerPerson: Int = 0
    var imageId: String = ""
    var sysDeleted: Int = 0

    // 订购数量
    var orderCount: Int = 0
    var imageUrl: String = ""

    constructor()

    constructor(takeoutMenuId: String, takeoutMenuDish: Takeoutmenudish) {
        this.id = uuid()
        this.takeoutMenuId = takeoutMenuId
        this.dishId = takeoutMenuDish.dishId
        this.dishName = takeoutMenuDish.dishName
        this.price = takeoutMenuDish.price
        this.portionUnit = takeoutMenuDish.portionUnit
        this.limitPerPerson = takeoutMenuDish.limitPerPerson
        this.imageId = takeoutMenuDish.imageId
    }
}

class TakeoutmenudishSearch: BaseSearch() {
    var takeoutMenuId: String = ""
    var dishId: String = ""
    var dishName: String = ""
}

@Mapper
interface TakeoutmenudishMapper : BaseMapper<Takeoutmenudish> {
    @Select("""
        <script>
            SELECT td.*,di.imageUrl FROM tbl_takeoutmenudish td left join tbl_dishimage di on td.imageId = di.id
            <where>
                AND td.sysDeleted = 0
                <if test="takeoutMenuId != ''">
                    AND td.takeoutMenuId = #{takeoutMenuId}
                </if>
                <if test="dishId != ''">
                    AND td.dishId = #{dishId}
                </if>
                <if test="dishName != ''">
                    AND td.dishName LIKE CONCAT('%', #{dishName}, '%')
                </if>
            </where>
            ORDER BY td.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Takeoutmenudish>
}

@Service
class TakeoutmenudishService(mapper: TakeoutmenudishMapper) : BaseService<Takeoutmenudish, TakeoutmenudishMapper>(mapper)

@RestController
@RequestMapping("/api/takeoutmenudish")
class TakeoutmenudishResource(service: TakeoutmenudishService) : BaseResource<TakeoutmenudishSearch, Takeoutmenudish, TakeoutmenudishMapper, TakeoutmenudishService>(service)
