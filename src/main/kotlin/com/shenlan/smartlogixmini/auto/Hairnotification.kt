package com.shenlan.smartlogixmini.auto

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.databind.ObjectMapper
import com.shenlan.smartlogixmini.util.*
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import javax.validation.Valid


/**
 * 理发通知实体类
 */
class Hairnotification : BaseEntity() {
    var reservationId: String = ""
    var noticeMethod: String = ""

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    var noticeTime: LocalDateTime = LocalDateTime.now()
    var feedbackContent: String = "无反馈"

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    var feedbackTime: LocalDateTime? = null
    var adminDisplay: Int = 0
    var userDisplay: Int = 0
    var receiptId: String = ""
}


/**
 * 理发通知查询条件类
 */
class HairnotificationSearch : BaseSearch() {

}

/**
 * 理发通知Mapper接口
 */
@Mapper
interface HairnotificationMapper : BaseMapper<Hairnotification> {
    /**
     * 根据对应的预约id获取最新一条的通知id
     */
    @Select("SELECT * FROM tbl_hairnotification WHERE reservationId = #{reservationId}  AND sysDeleted = 0  ORDER BY noticeTime DESC ")
    fun getNotificationByReservationId(reservationId: String): List<Hairnotification>?

    @Select("SELECT * FROM tbl_hairnotification WHERE reservationId = #{reservationId} AND adminDisplay = 0  ORDER BY noticeTime DESC ")
    fun getAdminReservationWithNotification(reservationId: String): List<Hairnotification>

    //      管理员逐个删除对应的通知反馈
    @Update("UPDATE tbl_hairnotification SET adminDisplay = 1 WHERE id = #{id}")
    fun deleteByIdAndAdminDisplay(id: String): Int

    //      管理员删除所有的通知反馈
    @Update("UPDATE tbl_hairnotification SET adminDisplay = 1 ")
    fun deleteListByAdminDisplay(): Int

    //      根据预约id逻辑删除该条通知反馈1表示不可见
    @Update("UPDATE tbl_hairnotification SET adminDisplay = 1 , userDisplay=1 WHERE reservationId = #{reservationId}")
    fun deleteListByReservationId(reservationId: String): Int

    //      根据预约id修改反馈内容（对应客户自己取消预约后将预约信息更新为取消预约）
    @Update("UPDATE tbl_hairnotification SET adminDisplay = 0 , userDisplay=1 , feedbackContent='取消理发' , feedbackTime=#{feedbackTime} WHERE reservationId = #{reservationId}")
    fun updateByReservationId(reservationId: String, feedbackTime: String): Int

    //      根据预约id查询对应的未被用户处理的通知信息
    @Select("SELECT * FROM tbl_hairnotification WHERE reservationId = #{reservationId} AND userDisplay = 0  ORDER BY noticeTime DESC limit 1")
    fun getNoticeByReserveId(reserveId: String): Hairnotification

    //    接收短信回复时间和职工手机号，查询职工回复前第一条未被职工处理过的通过短信通知的通知反馈记录
    @Select(
        """
        SELECT hn.* 
        FROM tbl_hairnotification hn
        LEFT JOIN tbl_hairreservation hr ON hr.id = hn.reservationId
        WHERE hn.userDisplay=0
          AND hn.noticeMethod LIKE '%短信通知%'
          AND hn.noticeTime <= #{beforeTime}
          AND hr.customerPhone=#{customerPhone}
          AND hr.sysDeleted = 0
          AND hn.sysDeleted = 0
        ORDER BY hn.noticeTime DESC limit 1
    """
    )
    fun getNoticeBySendSMS(customerPhone: String, beforeTime: Date): Hairnotification?

    /** 处理职工理发反馈 */
    @Update("UPDATE tbl_hairnotification SET adminDisplay = 0 ,feedbackContent=#{feedbackContent} ,userDisplay=1, feedbackTime=#{feedbackTime} WHERE id = #{id}")
    fun feedBack(hairnotification: Hairnotification): Int

    /**
     * 插入对应的云回执id
     */
    @Update("UPDATE tbl_hairnotification SET receiptId = #{callId} WHERE id = #{id}")
    fun insertReceiptIdById(callId: String, id: String): Int

    /**
     * 根据云回执id获取通知对象信息
     */
    @Select("SELECT * FROM tbl_hairnotification WHERE receiptId = #{receiptId} ")
    fun getInfoByReceiptId(receiptId: String): Hairnotification?

}

/**
 * 理发通知服务类
 */
@Service
class HairnotificationService(
    mapper: HairnotificationMapper,
    private val aliyunVmsUtil: AliyunVmsUtil,
    private val aliyunSmsUtil: AliyunSmsUtil,
    private val hairreservationService: HairreservationService,
    private val hairreservationMapper: HairreservationMapper,
    private val wechatSubscribeMessageUtil: WechatSubscribeMessageUtil,
    private val objectMapper: ObjectMapper = ObjectMapper(),
    private val personnelMapper: PersonnelMapper
) : BaseService<Hairnotification, HairnotificationMapper, HairnotificationSearch>(mapper) {
    fun getNotificationByReservationId(reservationId: String): List<Hairnotification>? {
        return mapper.getNotificationByReservationId(reservationId)
    }

    fun deleteByAdminDisplayAndId(Id: String): Result {
        return Result.getSuccess(mapper.deleteByIdAndAdminDisplay(Id))
    }

    fun deleteListByAdminDisplay(): Result {
        return Result.getSuccess(mapper.deleteListByAdminDisplay())
    }


    @Transactional
    override fun save(model: Hairnotification): Result {
        try {
            // 基础信息校验
            val reservation = hairreservationMapper.getInfo(model.reservationId)
                ?: return Result.getError("未找到该预约信息")

            val phoneNumber = reservation.customerPhone
            val name = reservation.customerName
            val personnel = personnelMapper.getInfoByPhone(phoneNumber)

            if (phoneNumber == null || name == null) {
                return Result.getError("未找到该职工信息")
            }
            if(model.noticeMethod.isEmpty()){
                model.feedbackTime=null
                return super.save(model)
            }
            // 记录每种通知方式的结果
            val notificationResults = mutableMapOf<String, Boolean>()

            // 处理短信通知
            if (model.noticeMethod.contains("短信通知")) {
                try {
                    val smsResult = this.sendSmsNotice(phoneNumber, name,"SMS_490305127")
                    notificationResults["短信通知"] = smsResult

                    if (smsResult) {
                        log.info("理发短信通知发送成功, 手机号: {}", phoneNumber)
                    } else {
                        log.warn("理发短信通知发送失败, 手机号: {}", phoneNumber)

                        // 如果没有其他通知方式，返回错误
                        if (!hasOtherEnabledMethods(model, "短信通知")) {
                            return Result.getError("短信通知发送失败，请选择其他通知方式")
                        }
                    }
                } catch (e: Exception) {
                    log.error("发送短信通知时发生异常, 手机号: {}", phoneNumber, e)
                    notificationResults["短信通知"] = false

                    // 如果没有其他通知方式，返回错误
                    if (!hasOtherEnabledMethods(model, "短信通知")) {
                        return Result.getError("短信通知发送失败，请选择其他通知方式")
                    }
                }
            }

            // 处理机器人语音通知
            if (model.noticeMethod.contains("机器人语音电话通知")) {
                try {
                    val (success, callId) = this.sendAppointmentReminder(phoneNumber, name)
                    model.receiptId=callId
                    notificationResults["机器人语音电话通知"] = success

                    if (!success) {
                        log.warn("机器人语音通知发送失败, 手机号: {}", phoneNumber)

                        // 如果没有其他通知方式，返回错误
                        if (!hasOtherEnabledMethods(model, "机器人语音电话通知")) {
                            return Result.getError("机器人语音电话通知发送失败，请选择其他通知方式")
                        }
                    }
                } catch (e: Exception) {
                    log.error("发送机器人语音通知时发生异常, 手机号: {}", phoneNumber, e)
                    notificationResults["机器人语音电话通知"] = false

                    // 如果没有其他通知方式，返回错误
                    if (!hasOtherEnabledMethods(model, "机器人语音电话通知")) {
                        return Result.getError("机器人语音电话通知发送失败，请选择其他通知方式")
                    }
                }
            }

            // 处理微信通知
            if (model.noticeMethod.contains("微信通知")) {
                try {
                    // 增加空值检查
                    if (personnel?.wechatOpenId == null) {
                        log.warn("微信通知失败: 用户微信OpenID为空, 手机号: {}", phoneNumber)
                        notificationResults["微信通知"] = false

                        // 如果没有其他通知方式，返回错误
                        if (!hasOtherEnabledMethods(model, "微信通知")) {
                            return Result.getError("微信通知失败，请选择其他通知方式")
                        }
                    } else {
                        // 发送微信订阅消息
                        val wechatResult = wechatSubscribeMessageUtil.sendMessage(
                            personnel.wechatOpenId,
                            "miOOXyW5_Rxu9y-e1oFcetFZtqY4yJlB0hFWs4wHtO8",
                            mapOf(
                                "thing1" to "当前理发师已空闲,请您尽快前往理发",
                                "date2" to LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))
                            ),
                            "pages/home/<USER>",
                            "trial"
                        )
                        notificationResults["微信通知"] = wechatResult

                        if (!wechatResult) {
                            log.warn("微信通知失败, 手机号: {}", phoneNumber)

                            // 如果没有其他通知方式，返回错误
                            if (!hasOtherEnabledMethods(model, "微信通知")) {
                                return Result.getError("微信通知失败，请选择其他通知方式")
                            }
                        }
                    }
                } catch (e: Exception) {
                    log.error("发送微信通知时发生异常, 手机号: {}", phoneNumber, e)
                    notificationResults["微信通知"] = false

                    // 如果没有其他通知方式，返回错误
                    if (!hasOtherEnabledMethods(model, "微信通知")) {
                        return Result.getError("微信通知失败，请选择其他通知方式")
                    }
                }
            }

            // 如果所有选中的通知方式都失败，返回错误
            if (notificationResults.isNotEmpty() && notificationResults.values.all { !it }) {
                return Result.getError("所有通知方式均失败")
            }
            model.feedbackTime=null
            return super.save(model)
        } catch (e: Exception) {
            log.error("保存理发通知信息时发生未知异常", e)
            return Result.getError("系统错误，请稍后重试")
        }
    }

    // 检查除当前失败方式外，是否还有其他启用的通知方式
     private fun hasOtherEnabledMethods(model: Hairnotification, failedMethod: String): Boolean {
        return when (failedMethod) {
            "短信通知" -> model.noticeMethod.contains("机器人语音电话通知") || model.noticeMethod.contains("微信通知")
            "机器人语音电话通知" -> model.noticeMethod.contains("短信通知") || model.noticeMethod.contains("微信通知")
            "微信通知" -> model.noticeMethod.contains("短信通知") || model.noticeMethod.contains("机器人语音电话通知")
            else -> false
        }
    }

    /**
     * 职工处理反馈信息
     */
    @Transactional
    fun feedBackByUser(hairnotification: Hairnotification): Result {
        val nowTime = LocalDateTime.now()
        hairnotification.feedbackTime = nowTime
        if (hairnotification.feedbackContent.contains("取消理发")) {
            val hairreservation = hairreservationMapper.getInfo(hairnotification.reservationId)!!
            return hairreservationService.deleteReservationByUser(hairreservation)
        }
        val i = mapper.feedBack(hairnotification)
        return Result.getSuccess(i)
    }

    /**
     *     发送理发通知短信给职工
     */
     private fun sendSmsNotice(phoneNumber: String, name: String,templateCode: String): Boolean {
        val result = aliyunSmsUtil.sendSms(
            phoneNumber = phoneNumber,
            params = mapOf("name" to name, "NoName" to "蓝海懃务"),
            templateCode = templateCode
        )
        return result
    }

    /**
     * 发起理发预约提醒电话
     * @param phoneNumber 客户电话号码
     * @param customerName 客户姓名（用于个性化提示）
     */
    fun sendAppointmentReminder(phoneNumber: String, customerName: String): Pair<Boolean, String> {
        // 构建初始TTS参数
        val initialParams = mapOf("name" to customerName, "NoName" to "懃务通").toJsonString()

//        // 构建菜单按键映射（使用真实模板ID）
//        val menuKeyMapList = listOf(
//            // 按键1：立即到店
//            mapOf(
//                "key" to "1",
//                "code" to "TTS_315260267", // 立即到店确认模板ID
//                "ttsParams" to "{}"
//            ),
//            // 按键2：稍后到店30分钟
//            mapOf(
//                "key" to "2",
//                "code" to "TTS_313995311",
//                "ttsParams" to "{}"
//            ),
//            // 按键3：取消预约
//            mapOf(
//                "key" to "3",
//                "code" to "TTS_313475333", // 取消确认模板ID
//                "ttsParams" to "{}"
//            )
//        )

        // 发起呼叫
        val (success, callId) = aliyunVmsUtil.singleCallByTts(
            calledNumber = phoneNumber,
            ttsCode = "TTS_322450014", // 理发通知模板
            ttsParam = initialParams,
            speed =-450
        )

        if (success) {
            log.info("理发预约语音提醒已发送至 $phoneNumber, callId: $callId")
            return success to callId
        } else {
            log.error("发送理发预约语音提醒至 $phoneNumber 失败")
            return false to ""
        }
    }

    // 辅助函数：将Map转换为JSON字符串
    private fun Any.toJsonString(): String {
        return objectMapper.writeValueAsString(this)
    }

}

/**
 * 理发通知控制器
 */
@RestController
@RequestMapping("/api/Hairnotification")
class HairnotificationResource(service: HairnotificationService) :
    BaseResource<HairnotificationSearch, Hairnotification, HairnotificationMapper, HairnotificationService>(service) {
    @GetMapping("/deleteByAdminDisplayAndId/{id}")
    fun deleteByAdminDisplayAndId(@PathVariable id: String) = service.deleteByAdminDisplayAndId(id)

    @GetMapping("/deleteListByAdminDisplay")
    fun deleteListByAdminDisplay() = service.deleteListByAdminDisplay()

    @PostMapping("/feedBackByUser")
    fun feedBackByUser(@Valid @RequestBody model: Hairnotification) = service.feedBackByUser(model)
}
