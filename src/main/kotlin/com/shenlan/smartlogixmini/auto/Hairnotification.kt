package com.shenlan.smartlogixmini.auto
import com.fasterxml.jackson.annotation.JsonFormat
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import javax.validation.Valid


/**
 * 理发通知实体类
 */
class Hairnotification : BaseModel() {
    var reservationId:String = ""
    var noticeMethod:String = ""
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm：ss")
    var noticeTime: LocalDateTime = LocalDateTime.now()
    var feedbackContent:String = ""
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm：ss")
    var feedbackTime: LocalDateTime? = null
    var adminDisplay:Int=0
    var userDisplay:Int=0
}



/**
 * 理发通知查询条件类
 */
class HairnotificationSearch: BaseSearch() {

}

/**
 * 理发通知Mapper接口
 */
@Mapper
interface HairnotificationMapper : BaseMapper<Hairnotification> {
    /**
     * 根据对应的预约id获取最新一条的通知id
      */
      @Select("SELECT * FROM tbl_hairnotification WHERE reservationId = #{reservationId}  AND sysDeleted = 0  ORDER BY noticeTime DESC ")
      fun getNotificationByReservationId(reservationId: String): List<Hairnotification>?
      @Select("SELECT * FROM tbl_hairnotification WHERE reservationId = #{reservationId} AND adminDisplay = 0  ORDER BY noticeTime DESC ")
      fun getAdminReservationWithNotification(reservationId:String):List<Hairnotification>
//      管理员逐个删除对应的通知反馈
      @Update("UPDATE tbl_hairnotification SET adminDisplay = 1 WHERE id = #{id}")
      fun deleteByIdAndAdminDisplay(id: String):Int
//      管理员删除所有的通知反馈
      @Update("UPDATE tbl_hairnotification SET adminDisplay = 1 ")
      fun deleteListByAdminDisplay(): Int
//      根据预约id逻辑删除该条通知反馈1表示不可见
      @Update("UPDATE tbl_hairnotification SET adminDisplay = 1 , userDisplay=1 WHERE reservationId = #{reservationId}")
      fun deleteListByReservationId(reservationId: String):Int
//      根据预约id修改反馈内容（对应客户自己取消预约后将预约信息更新为取消预约）
      @Update("UPDATE tbl_hairnotification SET adminDisplay = 0 , userDisplay=1 , feedbackContent='取消预约' , feedbackTime=#{feedbackTime} WHERE reservationId = #{reservationId}")
      fun updateByReservationId(reservationId: String,feedbackTime:String):Int


}

/**
 * 理发通知服务类
 */
@Service
class HairnotificationService(
    mapper: HairnotificationMapper
) : BaseService<Hairnotification, HairnotificationMapper>(mapper) {
    fun getNotificationByReservationId(reservationId:String):List<Hairnotification>? {
        return mapper.getNotificationByReservationId(reservationId)
    }
    fun deleteByAdminDisplayAndId( Id:String):Result {
        return Result.getSuccess(mapper.deleteByIdAndAdminDisplay(Id))
    }
    fun deleteListByAdminDisplay():Result {
        return Result.getSuccess(mapper.deleteListByAdminDisplay())
    }

}

/**
 * 理发通知控制器
 */
@RestController
@RequestMapping("/api/Hairnotification")
class HairnotificationResource(service: HairnotificationService) :
    BaseResource<HairnotificationSearch, Hairnotification, HairnotificationMapper, HairnotificationService>(service) {
    @GetMapping("/deleteByAdminDisplayAndId/{id}")
    fun deleteByAdminDisplayAndId(@PathVariable id: String) = service.deleteByAdminDisplayAndId(id)
    @GetMapping("/deleteListByAdminDisplay")
    fun deleteListByAdminDisplay()=service.deleteListByAdminDisplay()

}
