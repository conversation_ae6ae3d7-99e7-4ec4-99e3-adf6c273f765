package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Dish: BaseModel() {
    var creatorId: String = ""
    var name: String = ""
    var description: String = ""
    var price: Double = 0.0
    var portionUnit: String = ""
    var limitPerPerson: Int = 0
    var avgRating: Double = 0.0
    var imageUrl: String = ""
}

class DishSearch: BaseSearch() {
    var name: String = ""
}

@Mapper
interface DishMapper : BaseMapper<Dish> {
    @Select("""
        <script>
            SELECT * FROM tbl_dish
            <where>
                <if test="name != ''">
                    AND name LIKE CONCAT('%', #{name}, '%')
                </if>
            </where>
            ORDER BY name ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Dish>
}

@Service
class DishService(mapper: DishMapper) : BaseService<Dish, DishMapper>(mapper)

@RestController
@RequestMapping("/api/dish")
class DishResource(service: DishService) : BaseResource<DishSearch, Dish, DishMapper, DishService>(service)
