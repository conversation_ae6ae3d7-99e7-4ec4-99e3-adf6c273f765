package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Dish: BaseModel() {
    var creatorId: String = ""
    var name: String = ""
    var description: String = ""
    var price: Double = 0.0
    var portionUnit: Int = 0
    var limitPerPerson: Int = 0
    var avgRating: Double = 0.0
    var imageId: String = ""
    var timePeriod: String = ""

    var imageUrl: String = ""

    var images: List<Dishimage> = listOf()
}

class DishSearch: BaseSearch() {
    var name: String = ""
    var timePeriod: String = ""
}

@Mapper
interface DishMapper : BaseMapper<Dish> {
    @Select(
        """
            select d.*, di.imageUrl FROM tbl_dish d left join tbl_dishimage di on d.imageId = di.id
            where d.id = #{id}
        """
    )
    override fun getInfo(id: String): Dish?

    @Select("""
        <script>
            SELECT d.*, di.imageUrl FROM tbl_dish d left join tbl_dishimage di on d.imageId = di.id
            <where>
                <if test="name != ''">
                    AND d.name LIKE CONCAT('%', #{name}, '%')
                </if>
                <if test="timePeriod != ''">
                    AND d.timePeriod = #{timePeriod}
                </if>
            </where>
            ORDER BY d.name ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Dish>

    // 同步菜品，更新菜名、图片ID、价格、份量、限购
    @Update("""
        UPDATE tbl_dish
        SET name = #{dishName}, imageId = #{imageId}, price = #{price}, portionUnit = #{portionUnit}, limitPerPerson = #{limitPerPerson}
        WHERE id = #{dishId}
    """)
    fun syncDishByMenuDish(menuDish: Menudish)

    @Update("""
        UPDATE tbl_dish
        SET name = #{name}, imageId = #{imageId}, price = #{price}, portionUnit = #{portionUnit}, limitPerPerson = #{limitPerPerson}
        WHERE id = #{id}
    """)
    fun update(dish: Dish)

    @Update("""
        UPDATE tbl_dish
        SET avgRating = #{avgRating}
        WHERE id = #{dishId}
    """)
    fun updateDishAverageRating(dishId: String, avgRating: Double)

    // 单独替换图片id
    @Update("""
        UPDATE tbl_dish
        SET imageId = #{imageId}
        WHERE id = #{dishId}
    """)
    fun updateDishImageId(dishId: String, imageId: String)
}

@Service
class DishService(mapper: DishMapper) : BaseService<Dish, DishMapper>(mapper) {
    override fun getInfo(id: String): Result {
        val dish = super.getInfo(id).datas as Dish?
        dish?.let {
            it.images = getBean(DishimageMapper::class.java).getList(DishimageSearch().apply { dishId = it.id })
        }
        return Result.getSuccess(dish)
    }

    override fun save(model: Dish): Result {
        super.save(model)
        getBean(DishimageMapper::class.java).updateDishId(model.imageId, model.id)
        return Result.getSuccess(model.id)
    }

    fun update(dish: Dish): Result {
        mapper.update(dish)
        return Result.getSuccess(dish.id)
    }

    fun updateDishImageId(dishId: String, imageId: String): Result {
        mapper.updateDishImageId(dishId, imageId)
        return Result.getSuccess(dishId)
    }
}

@RestController
@RequestMapping("/api/dish")
class DishResource(service: DishService) : BaseResource<DishSearch, Dish, DishMapper, DishService>(service) {
    /**
     * 修改菜品
     */
    @PostMapping("/update")
    fun update(@RequestBody dish: Dish): Result {
        return service.update(dish)
    }

    /**
     * 单独替换图片id
     */
    @PostMapping("/updateDishImageId")
    fun updateDishImageId(@RequestBody dish: Dish): Result {
        return service.updateDishImageId(dish.id, dish.imageId)
    }
}
