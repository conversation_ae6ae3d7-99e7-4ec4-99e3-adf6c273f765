package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.MenuUtil
import com.shenlan.smartlogixmini.util.MenuUtil.syncTakeoutDish
import com.shenlan.smartlogixmini.util.getBean
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Dish: BaseEntity() {
    var creatorId: String = ""
    var name: String = ""
    var description: String = ""
    var price: Double = 0.0
    var portionUnit: Int = 0
    var limitPerPerson: Int = 0
    var avgRating: Double = 0.0
    var imageId: String = ""
    var timePeriod: String = ""

    var imageUrl: String = ""
        get() = if (field.isEmpty()) field else AppPro.imgPrefix + field

    var images: List<Dishimage> = listOf()

    // 是否同步菜品库
    var synced: Boolean = false
}

class DishSearch: BaseSearch() {
    var name: String = ""
    var timePeriod: String = ""
}

@Mapper
interface DishMapper : BaseMapper<Dish> {
    @Select(
        """
            select d.*, di.imageUrl FROM tbl_dish d left join tbl_dishimage di on d.imageId = di.id
            where d.id = #{id}
        """
    )
    override fun getInfo(id: String): Dish?

    @Select("""
        <script>
            SELECT d.*, di.imageUrl FROM tbl_dish d left join tbl_dishimage di on d.imageId = di.id
            <where>
                AND d.sysDeleted = 0
                <if test="name != ''">
                    AND d.name LIKE CONCAT('%', #{name}, '%')
                </if>
                <if test="timePeriod != ''">
                    AND d.timePeriod = #{timePeriod}
                </if>
            </where>
            ORDER BY d.sysCreated desc
        </script>
    """)
    override fun getList(search: BaseSearch): List<Dish>

    // 同步菜品，更新菜名、图片ID、价格、份量、限购
    @Update("""
        UPDATE tbl_dish
        SET name = #{dishName}, imageId = #{imageId}, price = #{price}, portionUnit = #{portionUnit}, limitPerPerson = #{limitPerPerson}
        WHERE id = #{dishId}
    """)
    fun syncDishByMenuDish(menuDish: Menudish)

    @Update("""
        UPDATE tbl_dish
        SET name = #{name}, imageId = #{imageId}, price = #{price}, portionUnit = #{portionUnit}, limitPerPerson = #{limitPerPerson}, synced = #{synced}
        WHERE id = #{id}
    """)
    fun update(dish: Dish)

    @Update("""
        UPDATE tbl_dish
        SET avgRating = #{avgRating}
        WHERE id = #{dishId}
    """)
    fun updateDishAverageRating(dishId: String, avgRating: Double)

    // 单独替换图片id
    @Update("""
        UPDATE tbl_dish
        SET imageId = #{imageId}
        WHERE id = #{dishId}
    """)
    fun updateDishImageId(dishId: String, imageId: String)

    @Update("""
        UPDATE tbl_dish
        SET sysDeleted = 1
        WHERE id = #{id}
    """)
    override fun deleteLogic(id: String): Int
}

@Service
class DishService(mapper: DishMapper) : BaseService<Dish, DishMapper, DishSearch>(mapper) {
    override fun delete(id: String): Result {
        return Result.getSuccess(mapper.deleteLogic(id))
    }

    override fun getInfo(id: String): Result {
        val dish = super.getInfo(id).datas as Dish?
        dish?.let {
            it.images = getBean(DishimageMapper::class.java).getList(DishimageSearch().apply { dishId = it.id }).map { image ->
                image.imageUrl = AppPro.imgPrefix + image.imageUrl
                image
            }
        }
        return Result.getSuccess(dish)
    }

    override fun save(model: Dish): Result {
        val checkDishNameExistsTips = MenuUtil.checkDishNameExists(model)
        require(checkDishNameExistsTips.isEmpty()) { return Result.getError(checkDishNameExistsTips) }

        super.save(model)
        model.images.forEach { image ->
            getBean(DishimageMapper::class.java).updateDishId(image.id, model.id)
        }
//        getBean(DishimageMapper::class.java).updateDishId(model.imageId, model.id)
        if (model.synced) {
            syncTakeoutDish(Takeoutdish(model.id, model.name, model.price, model.portionUnit, model.limitPerPerson, model.imageId))
        }
        return Result.getSuccess(model.id)
    }

    fun update(dish: Dish): Result {
        val checkDishNameExistsTips = MenuUtil.checkDishNameExists(dish)
        require(checkDishNameExistsTips.isEmpty()) { return Result.getError(checkDishNameExistsTips) }

        mapper.update(dish)
        if (dish.synced) {
            syncTakeoutDish(Takeoutdish(dish.id, dish.name, dish.price, dish.portionUnit, dish.limitPerPerson, dish.imageId))
        }
        return Result.getSuccess(dish.id)
    }

    fun updateDishImageId(dishId: String, imageId: String): Result {
        mapper.updateDishImageId(dishId, imageId)
        return Result.getSuccess(dishId)
    }
}

@RestController
@RequestMapping("/api/dish")
class DishResource(service: DishService) : BaseResource<DishSearch, Dish, DishMapper, DishService>(service) {
    /**
     * 修改菜品
     */
    @PostMapping("/update")
    fun update(@RequestBody dish: Dish): Result {
        return service.update(dish)
    }

    /**
     * 单独替换图片id
     */
    @PostMapping("/updateDishImageId")
    fun updateDishImageId(@RequestBody dish: Dish): Result {
        return service.updateDishImageId(dish.id, dish.imageId)
    }
}
