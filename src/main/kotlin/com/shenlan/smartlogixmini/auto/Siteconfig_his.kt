package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getInStr
import com.shenlan.smartlogixmini.util.notEmpty
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController


class Siteconfig_his : Siteconfig {

    /** 配置Id */
    var siteconfigId: String = ""
    /** 配置生效日期 */
    var siteconfigDate: String = ""

    constructor()

}

fun convertToSiteconfig(historyList: List<Siteconfig_his>): List<Siteconfig> {
    return historyList.map { history ->
        Siteconfig().apply {
            id = history.siteconfigId
            taskName = history.taskName
            inspectionTeamId = history.inspectionTeamId
            inspectionTeamName = history.inspectionTeamName
            siteId = history.siteId
            siteName = history.siteName
            remarks = history.remarks
            inspectionDay = history.inspectionDay
            startTime = history.startTime
            endTime = history.endTime
            inspectionFrequency = history.inspectionFrequency
            inspectionInterval = history.inspectionInterval
            permission = history.permission
        }
    }
}

@Mapper
interface Siteconfig_hisMapper : BaseMapper<Siteconfig_his> {

    @Select(""" select * from tbl_siteconfig_his where 1=1 ${'$'}{whereSql} order by sysCreated,inspectionTeamName """)
    override fun getList(search: BaseSearch): List<Siteconfig_his>

    @Select(""" select * from tbl_siteconfig_his where siteconfigDate = #{queryDate} and orgId = #{orgId} and inspectionTeamId = #{teamId} order by sysCreated limit 1 """)
    fun getInfoByTeamId(orgId: String, teamId: String, queryDate: String): Siteconfig?

}

class Siteconfig_hisSearch : BaseSearch {

    var siteconfigDate:String = ""
    var orgId:String = "" //机构Id
    var whereSql = ""
        get() {
            var sql = ""
            if (orgId.notEmpty()) sql += " and orgId = '${orgId}' "
            if (siteconfigDate.notEmpty()) sql += " and siteconfigDate = '${siteconfigDate}' "
            return sql
        }

    constructor()
}

@Service
open class Siteconfig_hisService(mapper: Siteconfig_hisMapper) : BaseService<Siteconfig_his, Siteconfig_hisMapper, Siteconfig_hisSearch>(mapper) {


}

@RestController
@RequestMapping("/api/Siteconfig_his")
open class Siteconfig_hisResource(service: Siteconfig_hisService) : BaseResource<Siteconfig_hisSearch, Siteconfig_his, Siteconfig_hisMapper, Siteconfig_hisService>(service) {


}

