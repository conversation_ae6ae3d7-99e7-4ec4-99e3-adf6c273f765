package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.*
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDate


open class Siteconfig : BaseEntity {

    /** 任务名称 */
    var taskName: String = ""
    /** 巡检团队ID */
    var inspectionTeamId: String = ""
    /** 巡检团队名称 */
    var inspectionTeamName: String = ""
    /** 站点ID */
    var siteId: String = ""
    /** 站点名称 */
    var siteName: String = ""
    /** 备注 */
    var remarks: String = ""
    /** 巡检日期 */
    var inspectionDay: String = ""
    /** 巡检开始时间 */
    var startTime: String = ""
    /** 巡检结束时间 */
    var endTime: String = ""
    /** 巡检频率（次数） */
    var inspectionFrequency: Int? = null
    /** 巡检间隔 */
    var inspectionInterval: Int? = null

    var siteList: List<Site> ?= null // 站点列表

    var permission: String = "" // 保存配置里工作组的权限

    /** 关联机构ID */
    var orgId:String = ""

    constructor()
}

@Mapper
interface SiteconfigMapper : BaseMapper<Siteconfig> {

    @Select(""" select a.id,a.taskName,a.inspectionTeamId,b.name inspectionTeamName,a.siteId,a.remarks,a.inspectionDay,a.startTime,a.endTime,a.inspectionFrequency,a.inspectionInterval,a.orgId
        from tbl_siteconfig a left join tbl_workgroup b on a.inspectionTeamId = b.id 
        where 1=1 ${'$'}{whereSql} order by a.sysCreated """)
    override fun getList(search: BaseSearch): List<Siteconfig>

    @Select(""" select a.id,a.taskName,a.inspectionTeamId,b.name inspectionTeamName,a.siteId,a.remarks,a.inspectionDay,a.startTime,a.endTime,a.inspectionFrequency,a.inspectionInterval
        from tbl_siteconfig a left join tbl_workgroup b on a.inspectionTeamId = b.id
        where orgId = #{orgId} and inspectionTeamId = #{teamId} order by a.sysCreated limit 1 """)
    fun getInfoByTeamId(orgId: String, teamId: String): Siteconfig?

    @Update(""" update tbl_siteconfig set siteId = #{siteId} where id = #{id} """)
    fun updateSiteInfo(id: String, siteId: String): Int

    @Select(""" select a.id,a.taskName,a.inspectionTeamId,b.name inspectionTeamName,a.siteId,a.remarks,a.inspectionDay,a.startTime,a.endTime,a.inspectionFrequency,a.inspectionInterval,a.orgId
        from tbl_siteconfig a left join tbl_workgroup b on a.inspectionTeamId = b.id 
        where a.id = #{id} """)
    override fun getInfo(id: String): Siteconfig?

}

class SiteconfigSearch : BaseSearch {

    var siteId:String = ""
    var orgId:String = "" //机构Id
    var whereSql = ""
        get() {
            var sql = ""
            if (orgId.notEmpty()) sql += " and orgId = '${orgId}' "
            if (siteId.notEmpty()) sql += " and siteId like '%${siteId}%' "
            return sql
        }

    constructor()
}

@Service
open class SiteconfigService(
    mapper: SiteconfigMapper,
    private val siteService: SiteService,
    private val workgroupService: WorkgroupService,
    private val siteconfig_hisMapper: Siteconfig_hisMapper) : BaseService<Siteconfig, SiteconfigMapper, SiteconfigSearch>(mapper) {

    override fun getList(page: SiteconfigSearch): Result {
        //获取当前登录账户的机构Id 仅查询该机构下的数据
        var orgId = getUser()!!.branchOrganizationId
        page.orgId = orgId
        return super.getList(page)
    }

    override fun getInfo(id: String): Result {
        var rlt = super.getInfo(id)
        if (rlt.rlt == 0 && rlt.datas != null) {
            //获取实时站点名称
            val siteconfig = rlt.datas as Siteconfig
            siteconfig.siteList =
                siteService.getList(SiteSearch().apply { ifPage = false; idsList = siteconfig.siteId.split(",") }).datas as List<Site>
            siteconfig.siteName = siteconfig.siteList?.joinToString(",") { it.siteName } ?: ""
            //将siteconfig.siteList按楼层排序
            //获取当前登录账户的机构Id 仅查询该机构下的数据
            var orgId = getUser()!!.branchOrganizationId
            var floorList = getBean(FloorMapper::class.java).getList(FloorSearch().apply { this.orgId = orgId })
            siteconfig.siteList = floorList.flatMap { floor ->
                siteconfig.siteList?.filter { it.floorName == floor.floorName }.orEmpty()
            }.toMutableList()
        }
        return rlt
    }

    fun getSiteconfigByLoginUser(queryDate: String): Result {
        //判断当前登录账号是否是工作组账号 如果不是 返回提示
        if (getUser()!!.ownerWorkgroupId.isEmpty()) { //工作组Id为空 代表是职工账号
            return Result.getError("请以工作组账号登录执行巡检任务！")
        }

        //获取当前登录账户的机构Id 仅查询该机构下的数据
        var orgId = getUser()!!.branchOrganizationId

        var inspectionTeamId = getUser()!!.workgroupList.firstOrNull()?.id ?: return Result.getError("账号异常，请联系管理员！")
        var inspectionTeamConfig: Siteconfig?
        if (LocalDate.parse(queryDate) <= LocalDate.now()) {
            inspectionTeamConfig = siteconfig_hisMapper.getInfoByTeamId(orgId,inspectionTeamId,queryDate)
        }else {
            inspectionTeamConfig = mapper.getInfoByTeamId(orgId,inspectionTeamId)
        }
        if (inspectionTeamConfig == null) {
            return Result.getError("暂无巡检任务！")
        }
        return Result.getSuccess(inspectionTeamConfig)
    }

    override fun save(model: Siteconfig): Result {
        //获取当前登录账户的机构Id
        var orgId = getUser()!!.branchOrganizationId

        if (model.id.isEmpty()) {
            model.orgId = orgId
        }

        //新增、编辑配置时，若工作组已有配置，且不是当前配置，需增加提示
        var existingConfig = mapper.getInfoByTeamId(orgId, model.inspectionTeamId)
        if (existingConfig != null && existingConfig.id != model.id) {
            return Result.getError("该工作组已存在巡检任务，请勿重复添加！")
        }
        return super.save(model)
    }

    fun getByConfigPermission(queryDate: String, teamType: String):Result {
        //获取当前登录账户的机构Id 仅查询该机构下的数据
        var orgId = getUser()!!.branchOrganizationId

        //获取配置列表数据
        var siteconfigList: List<Siteconfig>
        //如果queryDate是历史日期跟当前日期 获取Siteconfig_his的数据
        var permissionMap = mutableMapOf(
            PermissionName.PROPERTY_INSPECTION.value to listOf<Siteconfig>(),
            PermissionName.CLEANING_SERVICE.value to listOf<Siteconfig>(),
            PermissionName.DAY_SHIFT_INSPECTION.value to listOf<Siteconfig>(),
            PermissionName.NIGHT_SHIFT_INSPECTION.value to listOf<Siteconfig>()
        )

        //如果查询日期小于等于当前日期，则查询历史数据，否则查询最新数据
        if (LocalDate.parse(queryDate) <= LocalDate.now()) {
            siteconfigList = convertToSiteconfig(siteconfig_hisMapper.getList(Siteconfig_hisSearch().apply { this.orgId = orgId;this.siteconfigDate = queryDate }))
            siteconfigList.forEach {
                var permission = it.permission
                permissionMap.keys.forEach { type ->
                    if (permission.contains(type)) {
                        if (permissionMap[type] == null) {
                            permissionMap[type] = listOf(it)
                        } else {
                            permissionMap[type] = permissionMap[type]!!.plus(it)
                        }
                    }
                }
            }
        }else {
            siteconfigList = mapper.getList(SiteSearch().apply { this.orgId = orgId })
            siteconfigList.forEach {
                //按照物业巡检、保洁清洁、白班巡检、夜班巡检权限 来寻找包含这些权限的巡检团队所在的配置
                var permissionList = workgroupService.getEntity(it.inspectionTeamId)?.permissionList
                if (permissionList != null) {
                    permissionList.forEach { permission ->
                        var permissionName = permission.name?.value
                        if (permissionName!=null && permissionName.isNotEmpty() && permissionMap.containsKey(permissionName)) {
                            if (permissionMap[permissionName] == null) {
                                permissionMap[permissionName] = listOf(it)
                            }else {
                                permissionMap[permissionName] = permissionMap[permissionName]!!.plus(it)
                            }
                        }
                    }
                }
            }
        }
        var key = when (teamType) {
            "property" -> "物业巡检"
            "clean" -> "保洁清洁"
            "white" -> "白班巡检"
            "night" -> "夜班巡检"
            else -> ""
        }
        return Result.getSuccess(permissionMap[key])
    }

}

@RestController
@RequestMapping("/api/Siteconfig")
open class SiteconfigResource(service: SiteconfigService) : BaseResource<SiteconfigSearch, Siteconfig, SiteconfigMapper, SiteconfigService>(service) {

    /**
     * 获取当前登录用户的巡检配置
     * @param queryDate 查询日期，格式为yyyy-MM-dd
     * @return 返回当前登录用户的巡检配置
     * tip：如果查询日期小于等于当前日期，采用历史巡检配置数据，否则直接取最新的巡检配置数据
     */
    @GetMapping("/getSiteconfigByLoginUser/{queryDate}")
    fun getSiteconfigByLoginUser(@PathVariable queryDate: String): Result {
        return service.getSiteconfigByLoginUser(queryDate)
    }

    /**
     * 获取巡检配置列表
     * @param queryDate 查询日期，格式为yyyy-MM-dd
     * @param teamType 团队类型，取值为 "property"、"clean"、"white"、"night"
     * @return 返回指定团队类型的巡检配置列表
     * tip：如果查询日期小于等于当前日期，采用历史巡检配置数据，否则直接取最新的巡检配置数据
     */
    @GetMapping("/getByConfigPermission/{queryDate}/{teamType}")
    fun getByConfigPermission(@PathVariable queryDate: String, @PathVariable teamType: String):Result {
        return service.getByConfigPermission(queryDate, teamType)
    }

}

