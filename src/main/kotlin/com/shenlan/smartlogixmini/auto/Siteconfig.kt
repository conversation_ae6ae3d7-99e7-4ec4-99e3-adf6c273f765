package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.getUser
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*


class Siteconfig : BaseModel {

    /** 任务名称 */
    var taskName: String = ""
    /** 巡检团队ID */
    var inspectionTeamId: String = ""
    /** 巡检团队名称 */
    var inspectionTeamName: String = ""
    /** 站点ID */
    var siteId: String = ""
    /** 站点名称 */
    var siteName: String = ""
    /** 备注 */
    var remarks: String = ""
    /** 巡检日期 */
    var inspectionDay: String = ""
    /** 巡检开始时间 */
    var startTime: String = ""
    /** 巡检结束时间 */
    var endTime: String = ""
    /** 巡检频率（次数） */
    var inspectionFrequency: Int? = null
    /** 巡检间隔 */
    var inspectionInterval: Int? = null

    constructor()
}

@Mapper
interface SiteconfigMapper : BaseMapper<Siteconfig> {

    @Select(""" select * from tbl_siteconfig order by sysCreated """)
    override fun getList(search: BaseSearch): List<Siteconfig>

    @Select(""" select * from tbl_siteconfig where inspectionTeamId = #{teamId} order by sysCreated limit 1 """)
    fun getInfoByTeamId(teamId: String): Siteconfig?

}

class SiteconfigSearch : BaseSearch {
    constructor()
}

@Service
open class SiteconfigService(mapper: SiteconfigMapper) : BaseService<Siteconfig, SiteconfigMapper>(mapper) {

    fun getSiteconfigByLoginUser(): Result {
        var inspectionTeamId = getUser()?.workgroupList?.firstOrNull()?.id ?: return Result.getError("当前用户没有关联巡检团队！")
        var inspectionTeamConfig = mapper.getInfoByTeamId(inspectionTeamId)
        if (inspectionTeamConfig == null) {
            return Result.getError("暂无巡检任务！")
        }
        return Result.getSuccess(inspectionTeamConfig)
    }

}

@RestController
@RequestMapping("/api/Siteconfig")
open class SiteconfigResource(service: SiteconfigService) : BaseResource<SiteconfigSearch, Siteconfig, SiteconfigMapper, SiteconfigService>(service) {

    @GetMapping("/getSiteconfigByLoginUser")
    fun getSiteconfigByType(): Result {
        return service.getSiteconfigByLoginUser()
    }

}

