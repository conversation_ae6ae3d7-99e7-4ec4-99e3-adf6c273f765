package com.shenlan.smartlogixmini.auto

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.databind.ObjectMapper
import com.shenlan.smartlogixmini.util.*
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import javax.validation.Valid


/**
 * 预约信息实体类
 */
class Hairreservation : BaseEntity() {
    var timeslotId: String = ""
    var personnelId: String = ""
    var customerName: String = ""
    var customerPhone: String = ""
    var baberPhone: String = ""
    var status: Int = 0//0预约有效 1预约无效待确认（被理发师取消） 2预约过期 3预约无效已确认 4用户自己取消预约

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm")
    var hairStartTime: LocalDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(0, 0))

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm")
    var hairEndTime: LocalDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(0, 0))

    //    不存在数据库为了传输的数据
    var hairnotificationList: List<Hairnotification>? = null
}

/**
 * 预约信息查询条件类
 */
class HairreservationSearch : BaseSearch() {
    var timeslotId: String = ""
}

/**
 * 特殊时间段配置Mapper接口
 */
@Mapper
interface HairreservationMapper : BaseMapper<Hairreservation> {
    //    @Select("""
//        <script>
//            SELECT * FROM tbl_hairreservation
//             <where>
//                sysDeleted = 0
//                <if test="timeslotId != ''">
//                    AND timeslotId = #{timeslotId}
//                </if>
//             </where>
//        </script>
//    """)
//    override fun getList(search: BaseSearch): List<Hairreservation>
    @Select("SELECT * FROM tbl_hairreservation WHERE timeslotId=#{timeslotId} AND status IN(0,2)")
    fun getInfoBytimeslotId(timeslotId: String): Hairreservation?

    /**
     *  连表查询查询有通知信息的预约
     */
    @Select(
        """
        SELECT hr.* 
        FROM tbl_hairreservation hr
        LEFT JOIN tbl_hairnotification hn ON hr.id = hn.reservationId
        WHERE hn.adminDisplay=0
          AND hr.sysDeleted = 0
          AND hn.sysDeleted = 0
          ORDER BY hn.noticeTime DESC 
    """
    )
    fun getAdminReservationWithNotificationFilter(): List<Hairreservation>?

    /**
     * 查询个人预约信息记录
     */
    @Select("SELECT * FROM tbl_hairreservation WHERE sysDeleted = 0 AND personnelId=#{personnelId} AND hairStartTime BETWEEN #{issueStartTime} AND #{issueEndTime} AND status IN (0,2) ORDER BY hairStartTime DESC limit 1")
    fun getInfoByPersonnelId(
        personnelId: String,
        issueStartTime: LocalDateTime,
        issueEndTime: LocalDateTime
    ): Hairreservation

    /**
     * 根据id值修改预约状态信息,修改成已过期状态
     */
//    2表示过期的预约
    @Update("UPDATE tbl_hairreservation SET status = 2 WHERE id = #{id} ")
    fun updateStatusTo2ById(id: String): Int

    //    1表示被理发师取消的预约
    @Update("UPDATE tbl_hairreservation SET status = 1 WHERE id = #{id} AND status=0")
    fun updateStatusTo1ById(id: String): Int

    //   用户获取是否有被理发师取消的预约判断是否触发弹窗,返回一条最新的
    @Select("SELECT * FROM tbl_hairreservation WHERE sysDeleted = 0 AND personnelId=#{personnelId} AND status=1 ORDER BY hairStartTime DESC limit 1")
    fun getDeleteStatueByAdmin(personnelId: String): Hairreservation?

    //    4表示客户自己取消的理发预约
    @Update("UPDATE tbl_hairreservation SET status = 4 WHERE id = #{id} AND status=0")
    fun updateStatusTo4ById(id: String): Int

    //    1表示被理发师取消的预约，用户确认后将他改成3
    @Update("UPDATE tbl_hairreservation SET status = 3 WHERE id = #{id} AND status=1")
    fun updateStatusTo3ById(id: String): Int

    /**
     * 根据personnelId查询每个用户对应的需要处理的理发通知对应的预约信息,只有一条通知采取覆盖的方式。但还是加上了根据时间查询查出最新的一条避免认为改动数据库
     */
    @Select(
        """
        SELECT hr.* 
        FROM tbl_hairreservation hr
        LEFT JOIN tbl_hairnotification hn ON hr.id = hn.reservationId
        WHERE hn.userDisplay=0
          AND hr.personnelId=#{personnelId}
          AND hr.sysDeleted = 0
          AND hn.sysDeleted = 0
        ORDER BY hn.noticeTime DESC limit 1
        FOR UPDATE
    """
    )
    fun getNoticeByPersonnelId(personnelId: String): Hairreservation?
//    查询该时间段是否有被理发师取消的预约
    @Select("SELECT * FROM tbl_hairreservation WHERE timeslotId=#{timeslotId} AND status IN(1,3)")
    fun getDeleteInfoBytimeslotId(timeslotId: String): Hairreservation?
//    基础配置修改电话号码将所有属于该机构的预约信息修改为配置中的电话号码
    @Update("UPDATE tbl_hairreservation SET baberPhone=#{baberPhone} WHERE baberPhone=#{phoneNumber}")
    fun updateBaberphoneByBaseConfig(baberPhone: String,phoneNumber: String): Int

}

/**
 * 预约服务类
 */
@Service
class HairreservationService(
    mapper: HairreservationMapper,
    private val aliyunVmsUtil: AliyunVmsUtil,
    private val aliyunSmsUtil: AliyunSmsUtil,
    private val hairdateconfigMapper: HairdateconfigMapper,
    @Lazy
    private val hairnotificationService: HairnotificationService,
    private val hairtimeslotMapper: HairtimeslotMapper,
    @Lazy
    private val hairdateconfigService: HairdateconfigService,
    private val objectMapper: ObjectMapper = ObjectMapper(),
    private val hairnotificationMapper: HairnotificationMapper,
    private val hairreservationMapper: HairreservationMapper,
    private val wechatSubscribeMessageUtil: WechatSubscribeMessageUtil,
    private val personnelMapper: PersonnelMapper,
    private val messagesubscriptionMapper :MessagesubscriptionMapper,
    hairreservationMapper1: HairreservationMapper,
    @Lazy
    private val hairbaseconfigService: HairbaseconfigService
) : BaseService<Hairreservation, HairreservationMapper, HairreservationSearch>(mapper) {
    @Transactional
    override fun save(model: Hairreservation): Result {
        if (this.getReservationByPersonnelId().datas == "") {
            model.id = uuid()
            model.personnelId = getUser()!!.id
            if(hairbaseconfigService.getBaseConfig().datas!=""){
            val hairBaseConfig=hairbaseconfigService.getBaseConfig().datas as? Hairbaseconfig
            if(hairBaseConfig!= null)
            model.baberPhone=hairBaseConfig.baberPhone
            }
            model.customerName = getUser()!!.name
            model.customerPhone = getUser()!!.phone ?: ""
            model.hairStartTime = hairtimeslotMapper.getInfo(model.timeslotId)!!.startTime
            model.hairEndTime = hairtimeslotMapper.getInfo(model.timeslotId)!!.endTime
            hairtimeslotMapper.updateStatusByTimesSlotId(model.timeslotId)
            mapper.insert(model)
            return Result.getSuccess(model.id)
        } else {
            return Result.getError("已存在预约信息")
        }
    }

    fun getTotalInfoByTimeslotId(timeslotId: String): Hairreservation? {
        var hairreservation = mapper.getInfoBytimeslotId(timeslotId)
        if (hairreservation != null) {
            hairreservation.hairnotificationList =
                hairnotificationMapper.getAdminReservationWithNotification(hairreservation.id)
        }
        return hairreservation
    }

    fun getAdminReservationWithNotificationFilter(): Result {
        val orgId=getUser()?.branchOrganizationId?:return Result.getError("数据缺失，找不到用户对应的机构")
        if(orgId.isEmpty()){
            return Result.getError("数据缺失，找不到用户对应的机构")
        }
        val list = mapper.getAdminReservationWithNotificationFilter()?.distinctBy { it.id }?.toMutableList()
        /**
         * 这样会出现异常，需采用迭代器
         */
//        if(list !=null){
//        for ((index,item) in list!!.withIndex()){
//            if(personnelMapper.getInfo(item.personnelId) != null) {
//                val personnel=personnelMapper.getInfo(item.personnelId)
//                if(personnel!!.branchOrganizationId!=orgId){
//                    list?.removeAt(index)
//                }
//            }
//        }
//        }
        if (list != null) {
            // 获取集合的迭代器（而非通过索引遍历）
            val iterator = list.iterator()
            /**
             * 遍历集合元素并且对能够对元素进行安全的删除操作
             */
            while (iterator.hasNext()) {
                val item = iterator.next() // 获取当前元素
                val personnel = personnelMapper.getInfo(item.personnelId)
                if (personnel != null && personnel.branchOrganizationId != orgId) {
                    // 用迭代器的 remove() 删除当前元素，不会触发异常
                    iterator.remove()
                }
            }
        }
        list?.forEach {
            it.hairnotificationList = hairnotificationMapper.getAdminReservationWithNotification(it.id)
        }
        return Result.getSuccess(list)
    }

    /**
     * 获取个人的预约信息
     */
    @Transactional
    fun getReservationByPersonnelId(): Result {
        val personnelId = getUser()?.id?:return  Result.getError("数据异常，获取用户登入id出错")
        val orgId=getUser()?.branchOrganizationId?:return Result.getError("数据异常，获取用户机构id失败")
        if(orgId.isEmpty()){
            return Result.getError("数据缺失，获取用户机构id失败")
        }
        val nowTime = LocalDateTime.now()
        val issueOpenMax = hairdateconfigService.caluateMaxIssueByLocalDateTime(nowTime)
        val map = hairdateconfigService.caluateDateByIssue(issueOpenMax)
        /**
         * 重新编写过期逻辑，区分不同机构的一期开始时间和结束时间
         * 原来是查询一期的开始时间和结束时间判断其有没有预约。现在根据现在处于的期数的实际开始时间和实际结束时间决定
         */
//        一期当中实际的开放开始日期
        var issueStartDate = hairdateconfigMapper.findMinbussinessDateByIssue(issueOpenMax,orgId)
        var issueStartTime:LocalDateTime?
        if(issueStartDate!=null){
            issueStartTime = issueStartDate.atStartOfDay()
        }else{
            issueStartDate=map.get("issueStartTime")
            issueStartTime = issueStartDate!!.atStartOfDay()
        }
//        一期当中实际的开放截止日期
        var issueEndDate = hairdateconfigMapper.findMaxbussinessDateByIssue(issueOpenMax,orgId)
        var issueEndTime:LocalDateTime?
        if(issueEndDate!=null){
            issueEndTime = issueEndDate.atTime(LocalTime.MAX)
        }else
        {
            issueEndDate = map.get("issueEndTime") as LocalDate
            issueEndTime = issueEndDate.atTime(LocalTime.MAX)
        }

        log.info(
            "查询理发个人预约信息现在开放的期数最大的是第{}期，查询预约的时间段的开始时间是{}结束时间是{}",
            issueOpenMax,
            issueStartTime,
            issueEndTime
        )
        val hairreservation = mapper.getInfoByPersonnelId(personnelId, issueStartTime, issueEndTime)
        if(issueEndTime.isBefore(nowTime)){
            return Result.getSuccess("")
        }
        if (hairreservation != null) {
            if (hairreservation.hairStartTime.isBefore(nowTime)) {
                mapper.updateStatusTo2ById(hairreservation.id)
//                判断预约的时间段是否是过去时，如果是将状态改成2，表示该条数据已经过期
                hairreservation.status = 2
                return Result.getSuccess(hairreservation)
            } else {
                return Result.getSuccess(hairreservation)
            }
        }
        return Result.getSuccess("")
    }

    data class DeleteInfoByAdmin(
        val id: String,
        val timeslotId: String,
        val noticeMethod: String
    )
    @Transactional
//    理发师取消预约
//    现在的逻辑是修改预约的状态和对应的预约的时间段的状态
    fun deleteReservationByAdmin(model: DeleteInfoByAdmin): Result {
        try {
            // 基础信息校验
            val reservation = hairreservationMapper.getInfo(model.id)
                ?: return Result.getError("未找到该预约信息")
            val timeRange = formatTimeRange(reservation!!.hairStartTime, reservation!!.hairEndTime)
            val phoneNumber = reservation.customerPhone
            val name = reservation.customerName
            val personnel = personnelMapper.getInfoByPhone(phoneNumber)

            if (phoneNumber == null || name == null) {
                return Result.getError("未找到该职工信息")
            }
            if(model.noticeMethod.isEmpty()){
  //  修改预约状态为1表示预约失效（被管理员取消）
                val i = mapper.updateStatusTo1ById(model.id)
//    逻辑删除该条预约的通知反馈
                hairnotificationMapper.deleteListByReservationId(model.id)
//    修改对应的hairtimeslot状态为不可约状态
                hairtimeslotMapper.updateStatusTo2ByTimesSlotId(model.timeslotId)
                if(i!=0){
                return Result.getSuccess(i)}
                else{
                    return Result.getError("取消理发预约失败，请刷新重试")
                }
            }
            // 记录每种通知方式的结果
            val notificationResults = mutableMapOf<String, Boolean>()

            // 处理短信通知
            if (model.noticeMethod.contains("短信通知")) {
                try {
                    val smsResult = this.sendSmsNotice(phoneNumber, name,timeRange,"SMS_490655061")
                    notificationResults["短信通知"] = smsResult

                    if (smsResult) {
                        log.info("理发短信通知发送成功, 手机号: {}", phoneNumber)
                    } else {
                        log.warn("理发短信通知发送失败, 手机号: {}", phoneNumber)

                        // 如果没有其他通知方式，返回错误
                        if (!hasOtherEnabledMethods(model, "短信通知")) {
                            return Result.getError("短信通知发送失败，请选择其他通知方式")
                        }
                    }
                } catch (e: Exception) {
                    log.error("发送短信通知时发生异常, 手机号: {}", phoneNumber, e)
                    notificationResults["短信通知"] = false

                    // 如果没有其他通知方式，返回错误
                    if (!hasOtherEnabledMethods(model, "短信通知")) {
                        return Result.getError("短信通知发送失败，请选择其他通知方式")
                    }
                }
            }

            // 处理机器人语音通知
            if (model.noticeMethod.contains("机器人语音电话通知")) {
                try {
                    val (success, callId) = this.sendAppointmentReminder(phoneNumber, name,timeRange)
                    notificationResults["机器人语音电话通知"] = success

                    if (!success) {
                        log.warn("机器人语音通知发送失败, 手机号: {}", phoneNumber)

                        // 如果没有其他通知方式，返回错误
                        if (!hasOtherEnabledMethods(model, "机器人语音电话通知")) {
                            return Result.getError("机器人语音电话通知发送失败，请选择其他通知方式")
                        }
                    }
                } catch (e: Exception) {
                    log.error("发送机器人语音通知时发生异常, 手机号: {}", phoneNumber, e)
                    notificationResults["机器人语音电话通知"] = false

                    // 如果没有其他通知方式，返回错误
                    if (!hasOtherEnabledMethods(model, "机器人语音电话通知")) {
                        return Result.getError("机器人语音电话通知发送失败，请选择其他通知方式")
                    }
                }
            }

            // 处理微信通知
            if (model.noticeMethod.contains("微信通知")) {
                try {
                    // 增加空值检查
                    if (personnel?.wechatOpenId == null) {
                        log.warn("微信通知失败: 用户微信OpenID为空, 手机号: {}", phoneNumber)
                        notificationResults["微信通知"] = false

                        // 如果没有其他通知方式，返回错误
                        if (!this.hasOtherEnabledMethods(model, "微信通知")) {
                            return Result.getError("微信通知失败，请选择其他通知方式")
                        }
                    } else {
                        // 发送微信订阅消息
                        val wechatResult = wechatSubscribeMessageUtil.sendMessage(
                            personnel!!.wechatOpenId,
                            "SZuyH5peuqTbVd9Wo0V_bCwf5jAqmPi6SD1j8RAsV1Y",
                            mapOf("thing12" to "理发师临时有其他安排,感谢您的理解", "time15" to timeRange),
                            "pages/home/<USER>"
                        )
                        val wechatOpenIdList:List<String> = listOf(personnel.wechatOpenId)
                        notificationResults["微信通知"] = wechatResult
                        if (!wechatResult) {
                            log.warn("微信通知失败, 手机号: {}", phoneNumber)

                            // 如果没有其他通知方式，返回错误
                            if (!hasOtherEnabledMethods(model, "微信通知")) {
                                return Result.getError("微信通知失败，请选择其他通知方式")
                            }
                        }
//                        微信推送成功后,调用这个方法减少订阅次数
                        messagesubscriptionMapper.decreaseSubscribeCount(wechatOpenIdList,"SZuyH5peuqTbVd9Wo0V_bCwf5jAqmPi6SD1j8RAsV1Y")
                    }
                } catch (e: Exception) {
                    log.error("发送微信通知时发生异常, 手机号: {}", phoneNumber, e)
                    notificationResults["微信通知"] = false

                    // 如果没有其他通知方式，返回错误
                    if (!hasOtherEnabledMethods(model, "微信通知")) {
                        return Result.getError("微信通知失败，请选择其他通知方式")
                    }
                }
            }

            // 如果所有选中的通知方式都失败，返回错误
            if (notificationResults.isNotEmpty() && notificationResults.values.all { !it }) {
                return Result.getError("所有通知方式均失败")
            }
            //        修改预约状态为1表示预约失效（被管理员取消）
            val i = mapper.updateStatusTo1ById(model.id)
//    逻辑删除该条预约的通知反馈
            hairnotificationMapper.deleteListByReservationId(model.id)
//    修改对应的hairtimeslot状态为不可约状态
            hairtimeslotMapper.updateStatusTo2ByTimesSlotId(model.timeslotId)
            if (i!=0){
                return Result.getSuccess(i)
            }
            else{
                return Result.getError("取消理发预约失败，请刷新重试")
            }

        } catch (e: Exception) {
            log.error("取消理发通知信息时发生未知异常", e)
            return Result.getError("系统错误，请稍后重试")
        }

//        原始处理方式未加通知方式
//        修改预约状态为1表示预约失效（被管理员取消）
//        val i = mapper.updateStatusTo1ById(model.id)
////    逻辑删除该条预约的通知反馈
//        hairnotificationMapper.deleteListByReservationId(model.id)
////    修改对应的hairtimeslot状态为不可约状态
//        hairtimeslotMapper.updateStatusTo2ByTimesSlotId(model.timeslotId)
////        获取顾客的微信id进行推送
//        val reservation = mapper.getInfo(model.id)
//        val personnel = personnelMapper.getInfo(reservation!!.personnelId)
//        val timeRange = formatTimeRange(reservation!!.hairStartTime, reservation!!.hairEndTime)
//        try {
//            // 发送微信订阅消息
//            wechatSubscribeMessageUtil.sendMessage(
//                personnel!!.wechatOpenId,
//                "SZuyH5peuqTbVd9Wo0V_bCwf5jAqmPi6SD1j8RAsV1Y",
//                mapOf("thing12" to "理发师临时有其他安排,感谢您的理解", "time15" to timeRange),
//                "subpages/haircut/pages/appointment/index"
//            )
//        } catch (e: Exception) {
//            log.error("Failed to send WeChat subscribe message: ${e.message}")
    //        }
//        return Result.getSuccess(i)
    }
    // 检查除当前失败方式外，是否还有其他启用的通知方式
    private fun hasOtherEnabledMethods(model: DeleteInfoByAdmin, failedMethod: String): Boolean {
        return when (failedMethod) {
            "短信通知" -> model.noticeMethod.contains("机器人语音电话通知") || model.noticeMethod.contains("微信通知")
            "机器人语音电话通知" -> model.noticeMethod.contains("短信通知") || model.noticeMethod.contains("微信通知")
            "微信通知" -> model.noticeMethod.contains("短信通知") || model.noticeMethod.contains("机器人语音电话通知")
            else -> false
        }
    }
    private fun sendSmsNotice(phoneNumber: String, name: String,time:String,templateCode: String): Boolean {
        val result = aliyunSmsUtil.sendSms(
            phoneNumber = phoneNumber,
            params = mapOf("name" to name, "time" to time),
            templateCode = templateCode
        )
        return result
    }private fun sendAppointmentReminder(phoneNumber: String, customerName: String,time:String): Pair<Boolean, String> {
        // 构建初始TTS参数
        val initialParams = mapOf("name" to customerName, "time" to time).toJsonString()
        val (success, callId) = aliyunVmsUtil.singleCallByTts(
            calledNumber = phoneNumber,
            ttsCode = "TTS_322655023", // 理发通知模板
            ttsParam = initialParams,
            speed =-450
        )

        if (success) {
            log.info("理发预约语音提醒已发送至 $phoneNumber, callId: $callId")
            return success to callId
        } else {
            log.error("发送理发预约语音提醒至 $phoneNumber 失败")
            return false to ""
        }
    }
    private fun Any.toJsonString(): String {
        return objectMapper.writeValueAsString(this)
    }
    /**
     * 将两个LocalDateTime拼接成时间段字符串
     */
    fun formatTimeRange(start: LocalDateTime, end: LocalDateTime): String {
        // 定义时间格式
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")

        // 格式化开始时间和结束时间
        val startStr = start.format(formatter)
        val endStr = end.format(formatter)
        // 同一天时优化显示（只显示结束时间的时分部分）
        return if (start.toLocalDate() == end.toLocalDate()) {
            startStr + "~" + endStr.substringAfter(" ")
        } else {
            startStr + "~" + endStr
        }
    }

    //    查询是否有被理发师取消的预约时间,对应是否触发弹窗
    fun getDeleteStatuesByPersonnelId(): Result {
        val nameList = getUser()!!.finalPermissionList.map { it.name }
        if (PermissionName.STAFF_PERMISSION in nameList) {
            val personnelId = getUser()!!.id
            if (mapper.getDeleteStatueByAdmin(personnelId) != null) {
                return Result.getSuccess(mapper.getDeleteStatueByAdmin(personnelId))
            }
            return Result.getSuccess("")
        }
        else{
            log.info("不存在职工权限理发弹窗失效")
            return Result.getSuccess("")
        }

}

    @Transactional
//    用户自己取消预约的理发时间.
    fun deleteReservationByUser(model: Hairreservation): Result {
//        释放对应的时间段
        val i = hairtimeslotMapper.updateStatusTo0ByTimesSlotId(model.timeslotId)
//    更改这条预约信息的状态为4表示客户自己取消的预约
        mapper.updateStatusTo4ById(model.id)
//    如果有通知反馈的话修改通知反馈的内容为取消预约，并且获取当前时间作为反馈时间
        // 获取当前时间
        val now = LocalDateTime.now()
// 定义时间戳格式（数据库标准格式）
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
// 格式化为字符串
        val timestampStr = now.format(formatter)
        hairnotificationMapper.updateByReservationId(model.id, timestampStr)
        return Result.getSuccess(i)
    }

    /**
     *    判断是否触发职工首页通知理发弹窗(后续需要修改存在bug如果理发师有病上一期的通知不删除再次通知，那可能会覆盖这一期的通知)
     */
    @Transactional
    fun getNoticeBypersonnelId(): Result {
        val personnel =getUser()
        if(personnel!!.finalPermissionList.isEmpty()){
            return Result.getSuccess("")
        }else{
//            新增判断用户是否存在职工权限
            val nameList=personnel!!.finalPermissionList.map { it.name }
            if(PermissionName.STAFF_PERMISSION in nameList){
                val personnelId = getUser()!!.id
                val hairreservation = mapper.getNoticeByPersonnelId(personnelId)
                if (hairreservation != null) {
                    val notice = hairnotificationMapper.getNoticeByReserveId(hairreservation.id)
                    var noticeList: List<Hairnotification> = listOf(notice)
                    hairreservation.hairnotificationList = noticeList
                    return Result.getSuccess(hairreservation)
                }
                return Result.getSuccess("")
            }
            log.info("不存在职工权限理发弹窗失效")
            return Result.getSuccess("")
        }
//        val personnelId = getUser()!!.id
//        val hairreservation = mapper.getNoticeByPersonnelId(personnelId)
//        if (hairreservation != null) {
//            val notice = hairnotificationMapper.getNoticeByReserveId(hairreservation.id)
//            var noticeList: List<Hairnotification> = listOf(notice)
//            hairreservation.hairnotificationList = noticeList
//            return Result.getSuccess(hairreservation)
//        }
//        return Result.getSuccess("")
    }

    /**
     * 职工反馈被理发师取消预约的通知
     */
    fun feedBackByAdminDelete(id: String): Result {
        return Result.getSuccess(mapper.updateStatusTo3ById(id))
    }
}

/**
 * 预约信息控制器
 */
@RestController
@RequestMapping("/api/Hairreservation")
class HairreservationResource(service: HairreservationService) :
    BaseResource<HairreservationSearch, Hairreservation, HairreservationMapper, HairreservationService>(service) {
    //        获取通知反馈页面信息
    @PostMapping("/getAdminReservationWithNotificationFilter")
    fun getAdminReservationWithNotificationFilter() = service.getAdminReservationWithNotificationFilter()

    //    查询个人预约情况
    @PostMapping("/getReservationByPersonnelId")
    fun getReservationByPersonnelId() = service.getReservationByPersonnelId()

    //    理发师取消预约
    @PostMapping("/deleteReservationByAdmin")
    fun deleteReservationByAdmin(@Valid @RequestBody model: HairreservationService.DeleteInfoByAdmin) = service.deleteReservationByAdmin(model)

    //    查询是否有被理发师取消的预约时间,判断是否触发弹窗
    @PostMapping("/getDeleteInfoByPersonnelId")
    fun getDeleteStatuesByPersonnelId() = service.getDeleteStatuesByPersonnelId()

    //    用户自己取消理发预约
    @PostMapping("/deleteReserveByUser")
    fun deleteReserveByUser(@Valid @RequestBody model: Hairreservation) = service.deleteReservationByUser(model)

    //    用户获取理发弹窗接口通过personnelId
    @PostMapping("/getNoticeByPersonnelId")
    fun getNoticeByPersonnelId() = service.getNoticeBypersonnelId()

    //    反馈接口，用户回应被理发师取消预约接口
    @GetMapping("/feedBackByAdminDelete/{id}")
    fun feedBackByAdminDelete(@PathVariable id: String) = service.feedBackByAdminDelete(id)

}
