package com.shenlan.smartlogixmini.auto
import com.shenlan.smartlogixmini.util.log
import com.fasterxml.jackson.annotation.JsonFormat
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import javax.validation.Valid


/**
 * 预约信息实体类
 */
class Hairreservation : BaseModel() {
    var timeslotId:String = ""
    var personnelId:String = ""
    var customerName:String = ""
    var customerPhone:String = ""
    var baberPhone:String = "13680291189"
    var status:Int = 0//0预约有效 1预约无效待确认（被理发师取消） 2预约过期 3预约无效已确认 4用户自己取消预约
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm")
    var hairStartTime: LocalDateTime =LocalDateTime.of(LocalDate.now(), LocalTime.of(0, 0))
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm")
    var hairEndTime: LocalDateTime =LocalDateTime.of(LocalDate.now(), LocalTime.of(0, 0))
//    不存在数据库为了传输的数据
    var hairnotificationList:List<Hairnotification>?=null
}

/**
 * 预约信息查询条件类
 */
class HairreservationSearch: BaseSearch() {
    var timeslotId:String = ""
}

/**
 * 特殊时间段配置Mapper接口
 */
@Mapper
interface HairreservationMapper : BaseMapper<Hairreservation> {
//    @Select("""
//        <script>
//            SELECT * FROM tbl_hairreservation
//             <where>
//                sysDeleted = 0
//                <if test="timeslotId != ''">
//                    AND timeslotId = #{timeslotId}
//                </if>
//             </where>
//        </script>
//    """)
//    override fun getList(search: BaseSearch): List<Hairreservation>
        @Select("SELECT * FROM tbl_hairreservation WHERE timeslotId=#{timeslotId} AND status IN(0,2)")
        fun getInfoBytimeslotId(timeslotId: String): Hairreservation?

    /**
     *  连表查询查询有通知信息的预约
     */
    @Select("""
        SELECT hr.* 
        FROM tbl_hairreservation hr
        LEFT JOIN tbl_hairnotification hn ON hr.id = hn.reservationId
        WHERE hn.adminDisplay=0
          AND hr.sysDeleted = 0
          AND hn.sysDeleted = 0
    """)
        fun getAdminReservationWithNotificationFilter(): List<Hairreservation>?
    /**
     * 查询个人预约信息记录
     */
    @Select("SELECT * FROM tbl_hairreservation WHERE sysDeleted = 0 AND personnelId=#{personnelId} AND hairStartTime BETWEEN #{issueStartTime} AND #{issueEndTime} AND status IN (0,2) ORDER BY hairStartTime DESC limit 1")
    fun getInfoByPersonnelId(personnelId: String,issueStartTime:LocalDateTime,issueEndTime:LocalDateTime): Hairreservation
    /**
     * 根据id值修改预约状态信息,修改成已过期状态
     */
//    2表示过期的预约
    @Update("UPDATE tbl_hairreservation SET status = 2 WHERE id = #{id} ")
    fun updateStatusTo2ById(id:String):Int
//    1表示被理发师取消的预约
    @Update("UPDATE tbl_hairreservation SET status = 1 WHERE id = #{id} AND status=0")
    fun updateStatusTo1ById(id:String):Int
//   用户获取是否有被理发师取消的预约判断是否触发弹窗,返回一条最新的
    @Select("SELECT * FROM tbl_hairreservation WHERE sysDeleted = 0 AND personnelId=#{personnelId} AND status=1 ORDER BY hairStartTime DESC limit 1")
    fun getDeleteStatueByAdmin(personnelId: String):Hairreservation?
//    4表示客户自己取消的理发预约
    @Update("UPDATE tbl_hairreservation SET status = 4 WHERE id = #{id} AND status=0")
    fun updateStatusTo4ById(id:String):Int


}

/**
 * 预约服务类
 */
@Service
class HairreservationService(
    mapper: HairreservationMapper,
    private val hairtimeslotMapper: HairtimeslotMapper,
    @Lazy
    private val hairdateconfigService: HairdateconfigService,
    private val hairnotificationMapper: HairnotificationMapper
) : BaseService<Hairreservation, HairreservationMapper>(mapper) {
    @Transactional
    override fun save(model: Hairreservation): Result {
        model.id= uuid()
        model.personnelId= getUser()!!.id
        model.customerName = getUser()!!.name
        model.customerPhone = getUser()!!.phone
        model.hairStartTime=hairtimeslotMapper.getInfo(model.timeslotId)!!.startTime
        model.hairEndTime=hairtimeslotMapper.getInfo(model.timeslotId)!!.endTime
        hairtimeslotMapper.updateStatusByTimesSlotId(model.timeslotId)
        mapper.insert(model)
        return Result.getSuccess(model.id)
    }
    fun getTotalInfoByTimeslotId(timeslotId: String): Hairreservation? {
        var hairreservation=mapper.getInfoBytimeslotId(timeslotId)
        if (hairreservation != null) {
        hairreservation.hairnotificationList=hairnotificationMapper.getAdminReservationWithNotification(hairreservation.id)
        }
        return hairreservation
    }
    fun getAdminReservationWithNotificationFilter(): Result {
        val list=mapper.getAdminReservationWithNotificationFilter()?.distinctBy{it.id}
        list?.forEach{
            it.hairnotificationList=hairnotificationMapper.getAdminReservationWithNotification(it.id)
        }
        return Result.getSuccess(list)
    }

    /**
     * 获取个人的预约信息
     */
    @Transactional
    fun getReservationByPersonnelId(): Result {
        val personnelId= getUser()!!.id
        val nowTime = LocalDateTime.now()
        val issue=hairdateconfigService.calculateIssueByDate(nowTime.toLocalDate())
        val map=hairdateconfigService.caluateDateByIssue(issue)
        val issueStartDate=map.get("issueStartTime") as LocalDate
        val issueStartTime=issueStartDate.atStartOfDay()
        val issueEndDate=map.get("issueEndTime") as LocalDate
        val issueEndTime=issueEndDate.atTime(LocalTime.MAX)
        log.info("查询理发个人预约信息现在是第{}期，这一期的开始时间是{}结束时间是{}", issue,issueStartTime,issueEndTime)
        val hairreservation=mapper.getInfoByPersonnelId(personnelId,issueStartTime,issueEndTime)
        if(hairreservation != null) {
            if(hairreservation.hairEndTime.isBefore(nowTime)){
                mapper.updateStatusTo2ById(hairreservation.id)
//                判断预约的时间段是否是过去时，如果是将状态改成2，表示该条数据已经过期
                hairreservation.status=2
                return Result.getSuccess(hairreservation)
            }
            else{
                return Result.getSuccess(hairreservation)
            }
        }
        return Result.getSuccess("")
    }
    @Transactional
//    理发师取消预约
//    现在的逻辑是修改预约的状态和对应的预约的时间段的状态
    fun deleteReservationByAdmin(model: Hairreservation):Result {
//        修改预约状态为1表示预约失效（被管理员取消）
        val i=mapper.updateStatusTo1ById(model.id)
//    逻辑删除该条预约的通知反馈
        hairnotificationMapper.deleteListByReservationId(model.id)
//    修改对应的hairtimeslot状态为不可约状态
        hairtimeslotMapper.updateStatusTo2ByTimesSlotId(model.timeslotId)
     return Result.getSuccess(i)
    }
//    查询是否有被理发师取消的预约时间,对应是否触发弹窗
    fun getDeleteStatuesByPersonnelId(): Result {
        val personnelId= getUser()!!.id
        return Result.getSuccess(mapper.getDeleteStatueByAdmin(personnelId))
    }
    @Transactional
//    用户自己取消预约的理发时间.
    fun  deleteReservationByUser(model: Hairreservation): Result {
//        释放对应的时间段
        val i=hairtimeslotMapper.updateStatusTo0ByTimesSlotId(model.timeslotId)
//    更改这条预约信息的状态为4表示客户自己取消的预约
        mapper.updateStatusTo4ById(model.id)
//    如果有通知反馈的话修改通知反馈的内容为取消预约，并且获取当前时间作为反馈时间
    // 获取当前时间
        val now = LocalDateTime.now()
// 定义时间戳格式（数据库标准格式）
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
// 格式化为字符串
        val timestampStr = now.format(formatter)
        hairnotificationMapper.updateByReservationId(model.id,timestampStr)
        return Result.getSuccess(i)
    }
}

/**
 * 预约信息控制器
 */
@RestController
@RequestMapping("/api/Hairreservation")
class HairreservationResource(service: HairreservationService) :
    BaseResource<HairreservationSearch, Hairreservation, HairreservationMapper, HairreservationService>(service) {
//        获取通知反馈页面信息
    @PostMapping("/getAdminReservationWithNotificationFilter")
    fun getAdminReservationWithNotificationFilter() = service.getAdminReservationWithNotificationFilter()
//    查询个人预约情况
    @PostMapping("/getReservationByPersonnelId")
    fun getReservationByPersonnelId() = service.getReservationByPersonnelId()
//    理发师取消预约
    @PostMapping("/deleteReservationByAdmin")
    fun deleteReservationByAdmin(@Valid @RequestBody model: Hairreservation) = service.deleteReservationByAdmin(model)
//    查询是否有被理发师取消的预约时间,判断是否触发弹窗
    @PostMapping("/getDeleteInfoByPersonnelId")
    fun getDeleteStatuesByPersonnelId()=service.getDeleteStatuesByPersonnelId()
//    用户自己取消理发预约
    @PostMapping("/deleteReserveByUser")
    fun deleteReserveByUser(@Valid @RequestBody model: Hairreservation) = service.deleteReservationByUser(model)
}
