package com.shenlan.smartlogixmini.auto

import com.aliyun.mns.common.utils.DateUtil
import com.shenlan.smartlogixmini.util.*
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.nio.charset.StandardCharsets
import java.text.SimpleDateFormat
import java.time.*
import java.time.format.DateTimeFormatter
import java.util.*


class Inspectionrecord : BaseModel {

    /** 站点ID */
    var siteId: String = ""
    /** 站点名称 */
    var siteName: String = ""
    /** 巡检时间 */
    var inspectionTime: Date? = null
    /** 巡检人员 */
    var inspectionMan: String = ""
    /** 巡检结果(正常-success、异常-error) */
    var inspectionResult: String = ""
    /** 备注 */
    var remarks: String = ""
    /** 关联配置Id */
    var siteConfigId: String = ""
    /** 二维码标签状况(normal-正常 abnormal-异常) */
    var siteStatus: String = ""

    var contentList: List<Content> = listOf()

    constructor()
}

@Mapper
interface InspectionrecordMapper : BaseMapper<Inspectionrecord> {


    @Select(""" select * from tbl_inspectionrecord where 1=1 ${'$'}{whereSql} order by inspectionTime desc """)
    override fun getList(search: BaseSearch): List<Inspectionrecord>

}

class InspectionrecordSearch : BaseSearch {

    var loginUserRole = "" //当前登录账号是以 职工-workers or 管理员-administrator 身份登录
    var inspectionTeamType = "" //巡检团队类型是固定的 并跟前端约定好是这几个字段 白班巡检-white, 夜班巡检-night, 物业巡检-property, 保洁清洁-clean
    var queryDate = "" //查询日期 格式为yyyy-MM-dd
    var siteConfigId = "" //巡检配置ID

    var whereSql = ""
        get() {
            var sql = ""
            if (siteConfigId.notEmpty()) sql += " and siteConfigId = '${siteConfigId}' "
            if(startTime.notEmpty() && endTime.notEmpty()) {
                sql += " and (inspectionTime BETWEEN '$startTime' and '$endTime') "
            }
            return sql
        }

    constructor()
}

@Service
open class InspectionrecordService(mapper: InspectionrecordMapper,private val siteService: SiteService) : BaseService<Inspectionrecord, InspectionrecordMapper>(mapper) {

    fun getInspectionrecord(search: InspectionrecordSearch):Result {

        var loginUserRole = search.loginUserRole
        var inspectionTeamType = search.inspectionTeamType
        search.ifPage = false //不分页
        var queryDate = search.queryDate
        //根据queryDate获取当前是星期几
        var date = LocalDate.parse(queryDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        //获取星期几 1-7 代表周一到周日
        var weekDay = date.dayOfWeek.value.toString()
        println("查询日期：$queryDate，星期几：$weekDay")

        //获取当前登录用户信息
        var loginUser = getUser()!!

        //获取配置列表数据
        var siteconfigList = getBean(SiteconfigMapper::class.java).getList(SiteconfigSearch().apply { ifPage = false })

        var inspectionTeamConfig: Siteconfig ?= null //获取当前巡检配置
        if (loginUserRole == "administrator") { //如果是管理员 通过传参inspectionTeamType巡检团队类型对应确认
            inspectionTeamConfig = when (inspectionTeamType) {
                //获取巡检团队 依靠巡检团队名称（即工作组名称）来区分
                "white" -> siteconfigList.firstOrNull { it.inspectionTeamName.contains("白班") }
                "night" -> siteconfigList.firstOrNull { it.inspectionTeamName.contains("夜班") }
                "property" -> siteconfigList.firstOrNull { it.inspectionTeamName.contains("物业") }
                "clean" -> siteconfigList.firstOrNull { it.inspectionTeamName.contains("保洁") }
                else -> return Result.getError("巡检团队类型不正确！")
            }
        }else { //如果是职工 通过当前登录用户所在的巡检团队来确认
            var inspectionTeamId = loginUser.workgroupList.firstOrNull()?.id ?: return Result.getError("当前用户没有关联巡检团队！")
            inspectionTeamConfig = siteconfigList.firstOrNull { it.inspectionTeamId == inspectionTeamId }
        }

        //如果没有巡检配置
        if (inspectionTeamConfig == null) {
            return Result.getSuccess("暂无巡检任务！")
        }

        //如果查询的‘周几’不包含在巡检配置的巡检日期
        if (!inspectionTeamConfig.inspectionDay.contains(weekDay)) {
            return Result.getSuccess("暂无巡检任务！")
        }

        search.siteConfigId = inspectionTeamConfig.id //确认巡检配置ID后 将巡检配置ID传入查询条件

        //判断配置时间是否是跨日的 如果开始时间大于结束时间 代表是跨天的打卡
        if (inspectionTeamConfig.startTime.split(":")[0].toInt() > inspectionTeamConfig.endTime.split(":")[0].toInt()) {
            //将跨天打卡记录的 开始时间 结束时间计算出来
            var map = handleTaskQuery(queryDate,inspectionTeamConfig.endTime.split(":")[0].toInt())
            search.startTime = "${map["startTime"]!!.split(" ")[0]} ${inspectionTeamConfig.startTime}:00"
            search.endTime = map["endTime"]!!
            println("跨天打卡。")
        }else {
            //如果是当天打卡 则直接使用查询日期作为开始时间和结束时间
            search.startTime = "$queryDate ${inspectionTeamConfig.startTime}:00"
            search.endTime = "$queryDate ${inspectionTeamConfig.endTime}:00"
            println("当天打卡。")
        }
        println("查询巡检记录的时间范围：${search.startTime} 到 ${search.endTime}")

        //获取巡检记录列表数据
        var inspectionrecordList = mapper.getList(search) //查询这一天的所有数据（ifPage，startTime，endTime，siteConfigId）

        //获取巡检配置所有需要打卡的点位
        var siteIdList = inspectionTeamConfig.siteId.split(",")
        //返回所有需要打卡的点位列表
        var siteList = siteService.getList(SiteSearch().apply { ifPage = false; idsList = siteIdList}).datas as List<Site>
        //将巡检记录按照站点归类划分
        siteList.forEach { site ->
            //为站点添加巡检记录
            site.inspectionrecordList = inspectionrecordList.filter { it.siteId == site.id }
            //为站点添加巡检配置ID
            site.siteConfigId = inspectionTeamConfig.id
            //站点打卡总次数
            var checkInCount = site.inspectionrecordList?.count { it.siteStatus!="abnormal" }?:0
            //为站点添加打卡进度
            site.checkInProgress = "$checkInCount/${inspectionTeamConfig.inspectionFrequency}"
            /**
             * 为站点添加巡检结果
             * 巡检规则结果规则：
             * 0. 如果查询日期大于当前日期，则统一为“未到打卡时间”
             * 1. 已完成：如果打卡次数等于巡检频率，则为已完成
             * 2. 未到打卡时间：如果当前时间小于巡检开始时间，则为未到打卡时间
             * 3. 待打卡：如果打卡次数小于巡检频率且当前时间大于等于巡检开始时间小于结束时间，则为待打卡
             * 4. 缺卡：如果打卡次数小于巡检频率且当前时间大于等于巡检结束时间，则为缺卡
             */
            site.inspectionResult = when {
                isCurrentTimeBefore("$queryDate 00:00:00") -> "未到打卡时间"

                checkInCount >= inspectionTeamConfig.inspectionFrequency!! -> "已完成"

                isCurrentTimeBefore(search.startTime) -> "未到打卡时间"

                checkInCount < inspectionTeamConfig.inspectionFrequency!! &&
                        isCurrentTimeAfter(search.startTime) &&
                        isCurrentTimeBefore(search.endTime) -> "待打卡"

                checkInCount < inspectionTeamConfig.inspectionFrequency!! && isCurrentTimeAfter(search.endTime) -> "缺卡"
                else -> ""
            }
            //如果是待打卡状态，判断最新一次打卡时间跟当前时间的打卡间隔是否满足配置要求
            site.ifCheckIn = false
            if (site.inspectionResult == "待打卡") {
                if (inspectionTeamConfig.inspectionFrequency == 1) {
                    //如果巡检频率为1次，则直接可以打卡
                    site.ifCheckIn = true
                }else {
                    //否则需要判断最新打卡时间和当前时间的间隔是否满足巡检间隔要求
                    //获取最新一次打卡时间
                    val lastInspectionTime = site.inspectionrecordList?.filter { it.siteStatus!="abnormal" }?.sortedBy { it.inspectionTime }?.lastOrNull()?.inspectionTime
                    val requiredInterval = inspectionTeamConfig.inspectionInterval ?: 0 //获取巡检间隔
                    if (lastInspectionTime != null) {
                        //计算最新打卡时间到当前时间的间隔
//                        val interval = (Date().time - lastInspectionTime.time) / (1000 * 60) // 转换为分钟
                        val interval = Duration.between(lastInspectionTime.toInstant(), Instant.now()).toHours() // 转换为小时
                        println("最新打卡时间: ${lastInspectionTime}, 当前时间: ${Date()}, 巡检间隔: $requiredInterval 小时, 间隔: $interval 小时")
                        if (interval >= requiredInterval) {
                            //如果间隔大于等于巡检间隔 可以打卡
                            site.ifCheckIn = true
                        }
                    }else {
                        //如果规定时间内 一次打卡都没有 也可以直接打卡
                        site.ifCheckIn = true
                    }
                }
            }
        }
        //将站点按照楼层分组
        var siteListByFloor = siteList.groupBy { it.floorName }

        return Result.getSuccess(siteListByFloor)
    }

    fun exportInspectionrecord(search: InspectionrecordSearch):ByteArray? {
        search.ifPage = false //不分页
        var list = mapper.getList(search)
        if (list.isEmpty()) return null

        var export_list = ArrayList<InspectionrecordSimple>()
        list.forEach {
            export_list.add(InspectionrecordSimple().apply {
                siteName = it.siteName
                inspectionResult = if (it.inspectionResult == "success") "正常" else "异常"
                inspectionMan = it.inspectionMan
                inspectionTime = SimpleDateFormat("yyyy/MM/dd HH:mm").format(it.inspectionTime)
                remarks = it.remarks
                siteStatus = if (it.siteStatus == "abnormal") "二维码标签异常" else ""
            })
        }

        var excelBytes = PoiUtil.exportExcel(export_list,emptySet())

        return excelBytes
    }

    override fun save(model: Inspectionrecord): Result {

        if (model.id.isEmpty()) { // 新增
            model.inspectionTime = Date()
            model.inspectionMan = getUser()!!.name //工作组账号登录 账号名称就是工作组名称
        }

        var rlt = super.save(model)

        if (rlt.rlt == 0) { //保存成功 插入关联文件
            model.contentList.forEach {
                it.id = uuid()
                it.inspectionrecordId = model.id
                getBean(ContentMapper::class.java).insert(it)
            }
        }

        //如果是保存二维码标签状态 更新站点的二维码标签状况
        if (model.siteStatus == "abnormal") {
            getBean(SiteMapper::class.java).changeStatus(model.siteId, model.siteStatus, getUser()!!.name)
        }

        return rlt
    }

    override fun getInfo(id: String): Result {
        var rlt = super.getInfo(id)
        if (rlt.rlt == 0 && rlt.datas != null) { //获取关联的文件列表
            var model = rlt.datas as Inspectionrecord
            model.contentList = getBean(ContentMapper::class.java).getListByInspectionrecordId(model.id)
        }
        return rlt
    }

}

class InspectionrecordSimple {
    @ExcelColumn(name = "巡检点", order = 1)
    var siteName: String = ""
    @ExcelColumn(name = "巡检结果", order = 2)
    var inspectionResult: String = ""
    @ExcelColumn(name = "巡检人", order = 3)
    var inspectionMan: String = ""
    @ExcelColumn(name = "时间", order = 4)
    var inspectionTime: String = ""
    @ExcelColumn(name = "巡检说明", order = 5)
    var remarks: String = ""
    @ExcelColumn(name = "其他", order = 6)
    var siteStatus: String = ""
}

/**
 * 检查当前时间是否在指定时间之前
 */
fun isCurrentTimeBefore(timeString: String): Boolean {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    val targetTime = LocalDateTime.parse(timeString, formatter)
    val currentTime = LocalDateTime.now()
    // 模拟当前时间为2025-06-26 23:00:00
//    val currentTime = LocalDateTime.of(2025, 6, 27, 23, 0, 0)
    return currentTime.isBefore(targetTime)
}

/**
 * 检查当前时间是否在指定时间之后
 */
fun isCurrentTimeAfter(timeString: String): Boolean {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    val targetTime = LocalDateTime.parse(timeString, formatter)
    val currentTime = LocalDateTime.now()
    // 模拟当前时间为2025-06-26 23:00:00
//    val currentTime = LocalDateTime.of(2025, 6, 27, 23, 0, 0)
    return currentTime.isAfter(targetTime)
}

fun handleTaskQuery(dateFromFront: String, taskStartHour: Int):HashMap<String, String> {

    var result = HashMap<String, String>()

    // 定义常量
    val DATE_FORMATTER = DateTimeFormatter.ISO_DATE
    val TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    val TASK_START_HOUR = taskStartHour

    // 解析日期（只解析一次）
    val queryDate = try {
        LocalDate.parse(dateFromFront, DATE_FORMATTER)
    } catch (e: Exception) {
        println("日期格式错误: $dateFromFront")
        return result
    }

    val today = LocalDate.now()
    val timeZone = ZoneId.systemDefault()

    // 检查日期范围
    when {
        queryDate > today -> {
            println("查询日期大于今天")
            //返回查询日期
            val startTime = queryDate.atStartOfDay(timeZone).plusHours(TASK_START_HOUR.toLong())
            val endTime = startTime.plusDays(1)
            return result.apply {
                put("startTime", startTime.format(TIME_FORMATTER))
                put("endTime", endTime.format(TIME_FORMATTER))
            }
        }
        queryDate < today -> {
            // 处理历史数据
            val startTime = queryDate.atStartOfDay(timeZone).plusHours(TASK_START_HOUR.toLong())
            val endTime = startTime.plusDays(1)
            println("历史任务: ${startTime.format(TIME_FORMATTER)} 到 ${endTime.format(TIME_FORMATTER)}")
            return result.apply {
                put("startTime", startTime.format(TIME_FORMATTER))
                put("endTime", endTime.format(TIME_FORMATTER))
            }
        }
    }

    // 处理当天数据
    val now = ZonedDateTime.now(timeZone)
    // 模拟当前时间十分秒
//        val now = ZonedDateTime.of(today, LocalTime.of(2, 0), timeZone) // 模拟当前时间为今日07:00
    val taskStartTime = ZonedDateTime.of(today, LocalTime.of(TASK_START_HOUR, 0), timeZone)
    val taskEndTime = taskStartTime.plusDays(1)

    when {
        now >= taskStartTime && now < taskEndTime -> {
            println("今日任务: ${taskStartTime.format(TIME_FORMATTER)} 到 ${taskEndTime.format(TIME_FORMATTER)}")
            return result.apply {
                put("startTime", taskStartTime.format(TIME_FORMATTER))
                put("endTime", taskEndTime.format(TIME_FORMATTER))
            }
        }
        else -> {
            println("上一伦任务: ${
                taskStartTime.minusDays(1).format(TIME_FORMATTER)
            } 到 ${taskStartTime.format(TIME_FORMATTER)}")
            return result.apply {
                put("startTime", taskStartTime.minusDays(1).format(TIME_FORMATTER))
                put("endTime", taskStartTime.format(TIME_FORMATTER))
            }
        }
    }
}

@RestController
@RequestMapping("/api/Inspectionrecord")
open class InspectionrecordResource(service: InspectionrecordService) : BaseResource<InspectionrecordSearch, Inspectionrecord, InspectionrecordMapper, InspectionrecordService>(service) {

    @PostMapping("/getInspectionrecord")
    fun getInspectionrecord(@RequestBody search: InspectionrecordSearch):Result {
        return service.getInspectionrecord(search)
    }

    @PostMapping("/exportInspectionrecord")
    fun exportInspectionrecord(@RequestBody search: InspectionrecordSearch):Result {
        return Result.getSuccess(service.exportInspectionrecord(search))
    }

    @PostMapping("/downloadInspectionrecord")
    fun downloadInspectionrecord(@RequestBody search: InspectionrecordSearch): ResponseEntity<ByteArrayResource> {
        var excelBytes = service.exportInspectionrecord(search)

        if (excelBytes == null) {
            return ResponseEntity.notFound().build()
        }

        var filename = "巡检记录${search.startTime.split(" ")[0]}~${search.endTime.split(" ")[0]}.xlsx"

        return ResponseEntity.ok()
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=\"${encodeFilename(filename)}\""
            )
            .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
            .header(HttpHeaders.PRAGMA, "no-cache")
            .header(HttpHeaders.EXPIRES, "0")
            .body(ByteArrayResource(excelBytes))

    }

    private fun encodeFilename(filename: String): String {
        return filename.replace("\"", "\\\"")
            .toByteArray(StandardCharsets.UTF_8)
            .let { java.net.URLEncoder.encode(String(it), "UTF-8") }
    }

}

