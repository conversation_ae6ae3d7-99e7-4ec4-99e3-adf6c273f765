package com.shenlan.smartlogixmini.auto

import com.jhg.QrDecode.CQrDecode
import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.*
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.*
import java.nio.charset.StandardCharsets
import java.nio.file.Files
import java.nio.file.Paths
import java.text.SimpleDateFormat
import java.time.*
import java.time.format.DateTimeFormatter
import java.util.*
import java.time.LocalDateTime
import java.time.Duration


class Inspectionrecord : BaseEntity {

    /** 站点ID */
    var siteId: String = ""
    /** 站点名称 */
    var siteName: String = ""
    /** 巡检时间 */
    var inspectionTime: Date? = null
    /** 巡检人员 */
    var inspectionMan: String = ""
    /** 巡检结果(正常-success、异常-error) */
    var inspectionResult: String = ""
    /** 备注 */
    var remarks: String = ""
    /** 关联配置Id */
    var siteConfigId: String = ""
    /** 二维码标签状况(normal-正常 abnormal-异常) */
    var siteStatus: String = ""

    var contentList: List<Content> = listOf()
    var inspectionType: String = "" //提交类型（A-提交更改二维码标签状态 B-提交打卡记录）

    /** 关联机构ID */
    var orgId:String = ""

    constructor()
}

@Mapper
interface InspectionrecordMapper : BaseMapper<Inspectionrecord> {


    @Select(""" select * from tbl_inspectionrecord where 1=1 ${'$'}{whereSql} order by inspectionTime desc """)
    override fun getList(search: BaseSearch): List<Inspectionrecord>

}

class InspectionrecordSearch : BaseSearch {

    var loginUserRole = "" //当前登录账号是以 职工-workers or 管理员-administrator 身份登录
    var inspectionTeamType = "" //巡检团队类型是固定的 并跟前端约定好是这几个字段 白班巡检-white, 夜班巡检-night, 物业巡检-property, 保洁清洁-clean
    var queryDate = "" //查询日期 格式为yyyy-MM-dd
    var siteConfigId = "" //巡检配置ID
    var orgId:String = "" //机构Id

    var whereSql = ""
        get() {
            var sql = ""
            if (orgId.notEmpty()) sql += " and orgId = '${orgId}' "
            if (siteConfigId.notEmpty()) sql += " and siteConfigId = '${siteConfigId}' "
            if(startTime.notEmpty() && endTime.notEmpty()) {
                sql += " and (inspectionTime BETWEEN '$startTime' and '$endTime') "
            }
            return sql
        }

    constructor()
}

@Service
open class InspectionrecordService(
    mapper: InspectionrecordMapper,
    private val siteService: SiteService,
    private val site_hisService: Site_hisService,
    private val siteconfigMapper: SiteconfigMapper,
    private val siteconfig_hisMapper: Siteconfig_hisMapper
) : BaseService<Inspectionrecord, InspectionrecordMapper, InspectionrecordSearch>(mapper) {

    fun getInspectionrecord(search: InspectionrecordSearch):Result {
        //获取当前登录账户的机构Id 仅查询该机构下的数据
        var orgId = getUser()!!.branchOrganizationId

        var loginUserRole = search.loginUserRole
        search.ifPage = false //不分页
        var queryDate = search.queryDate
        //根据queryDate获取当前是星期几
        var date = LocalDate.parse(queryDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        //获取星期几 1-7 代表周一到周日
        var weekDay = date.dayOfWeek.value.toString()
        println("查询日期：$queryDate，星期几：$weekDay")

        //获取当前登录用户信息
        var loginUser = getUser()!!

        //获取配置列表数据
        var siteconfigList: List<Siteconfig>
        //如果queryDate是历史日期跟当前日期 获取Siteconfig_his的数据
        if (LocalDate.parse(queryDate) <= LocalDate.now()) {
            siteconfigList = convertToSiteconfig(siteconfig_hisMapper.getList(Siteconfig_hisSearch().apply { this.orgId = orgId;this.siteconfigDate = queryDate }))
        }else {
            siteconfigList = siteconfigMapper.getList(SiteconfigSearch().apply { this.orgId = orgId })
        }

        var inspectionTeamConfig: Siteconfig ?= null //获取当前巡检配置
        if (loginUserRole == "administrator") { //如果是巡检管理 通过前端传入的巡检配置ID来确认
            inspectionTeamConfig = siteconfigList.find { it.id == search.siteConfigId } //查找选择的配置Id
        }else { //如果是职工 通过当前登录用户所在的巡检团队来确认
            var inspectionTeamId = loginUser.workgroupList.firstOrNull()?.id ?: return Result.getError("当前用户没有关联巡检团队！")
            inspectionTeamConfig = siteconfigList.firstOrNull { it.inspectionTeamId == inspectionTeamId }
        }

        var result = mutableMapOf<String, List<Site>>()

        //如果没有巡检配置
        if (inspectionTeamConfig == null) {
            return Result.getSuccess("暂无巡检任务！")
        }

        //如果查询的‘周几’不包含在巡检配置的巡检日期
        if (!inspectionTeamConfig.inspectionDay.contains(weekDay)) {
            return Result.getSuccess("暂无巡检任务！")
        }

        result = getSiteListByFloor(inspectionTeamConfig, queryDate, search) as MutableMap<String, List<Site>>

        var floorList = getBean(FloorMapper::class.java).getList(FloorSearch()).map { it.floorName } //获取所有楼层名称列表 已排序 以此为顺序将map键排序返回给前端
        var sortedMap = sortMapByCustomOrder(result,floorList)

        return Result.getSuccess(sortedMap)
    }

    fun getSiteListByFloor(inspectionTeamConfig: Siteconfig, queryDate: String, search: InspectionrecordSearch): Map<String, List<Site>> {

        search.siteConfigId = inspectionTeamConfig.id //确认巡检配置ID后 将巡检配置ID传入查询条件

        //判断配置时间是否是跨日的 如果开始时间大于结束时间 代表是跨天的打卡
        if (inspectionTeamConfig.startTime.split(":")[0].toInt() >= inspectionTeamConfig.endTime.split(":")[0].toInt()) {
            //将跨天打卡记录的 开始时间 结束时间计算出来
            var map = handleTaskQuery(queryDate,inspectionTeamConfig.endTime.split(":")[0].toInt(),inspectionTeamConfig.endTime.split(":")[1].toInt())
            search.startTime = "${map["startTime"]!!.split(" ")[0]} ${inspectionTeamConfig.startTime}:00"
            search.endTime = map["endTime"]!!
            println("跨天打卡。")
        }else {
            //如果是当天打卡 则直接使用查询日期作为开始时间和结束时间
            search.startTime = "$queryDate ${inspectionTeamConfig.startTime}:00"
            search.endTime = "$queryDate ${inspectionTeamConfig.endTime}:00"
            println("当天打卡。")
        }
        println("查询巡检记录的时间范围：${search.startTime} 到 ${search.endTime}")

        //获取巡检记录列表数据 升序
        var inspectionrecordList = mapper.getList(search).sortedBy { it.inspectionTime } //查询这一天的所有数据（ifPage，startTime，endTime，siteConfigId）

        //获取巡检配置所有需要打卡的点位
        var siteIdList = inspectionTeamConfig.siteId.split(",")
        //返回所有需要打卡的点位列表
        //如果queryDate是历史日期 获取Site_his的数据
        var siteList: List<Site>
        if (LocalDate.parse(queryDate) < LocalDate.now()) {
            siteList = convertToSite(site_hisService.getList(Site_hisSearch().apply { ifPage = false; siteDate = queryDate; idsList = siteIdList}).datas as List<Site_his>)
        }else {
            siteList = siteService.getList(SiteSearch().apply { ifPage = false; idsList = siteIdList}).datas as List<Site>
        }
        //二维码标签状态的更改功能：今天之前或者今天之后 都不允许修改
        var ifAllowEdit = if (queryDate == LocalDate.now().format(DateTimeFormatter.ISO_DATE)) true else false
        //将巡检记录按照站点归类划分
        siteList.forEach { site ->
            site.ifAllowEdit = ifAllowEdit
            //为站点添加巡检记录
            site.inspectionrecordList = inspectionrecordList.filter { it.siteId == site.id }
            //为站点添加巡检配置ID
            site.siteConfigId = inspectionTeamConfig.id
            //站点打卡总次数
            var checkInCount = site.inspectionrecordList?.count { it.siteStatus!="abnormal" }?:0
            //为站点添加打卡进度
            site.checkInProgress = "$checkInCount/${inspectionTeamConfig.inspectionFrequency}"
            /**
             * 为站点添加巡检结果
             * 巡检规则结果规则：
             * 0. 如果查询日期大于当前日期，则统一为“未到打卡时间”
             * 1. 已完成：如果打卡次数等于巡检频率，则为已完成
             * 2. 未到打卡时间：如果当前时间小于巡检开始时间，则为未到打卡时间
             * 3. 待打卡：如果打卡次数小于巡检频率且当前时间大于等于巡检开始时间小于结束时间，则为待打卡
             * 4. 缺卡：如果打卡次数小于巡检频率且当前时间大于等于巡检结束时间，则为缺卡
             */
            site.inspectionResult = when {
                isCurrentTimeBefore("$queryDate 00:00:00") -> "未到打卡时间"

                checkInCount >= inspectionTeamConfig.inspectionFrequency!! -> "已完成"

                isCurrentTimeBefore(search.startTime) -> "未到打卡时间"

                checkInCount < inspectionTeamConfig.inspectionFrequency!! &&
                        isCurrentTimeAfter(search.startTime) &&
                        isCurrentTimeBefore(search.endTime) -> "待打卡"

                checkInCount < inspectionTeamConfig.inspectionFrequency!! && isCurrentTimeAfter(search.endTime) -> "缺卡"
                else -> ""
            }
            //如果是待打卡状态，判断最新一次打卡时间跟当前时间的打卡间隔是否满足配置要求
            site.ifCheckIn = false
            if (site.inspectionResult == "待打卡") {
                if (inspectionTeamConfig.inspectionFrequency == 1) {
                    //如果巡检频率为1次，则直接可以打卡
                    site.ifCheckIn = true
                }else {
                    //否则需要判断最新打卡时间和当前时间的间隔是否满足巡检间隔要求
                    //获取最新一次打卡时间
                    val lastInspectionTime = site.inspectionrecordList?.filter { it.siteStatus!="abnormal" }?.sortedBy { it.inspectionTime }?.lastOrNull()?.inspectionTime
                    val requiredInterval = inspectionTeamConfig.inspectionInterval ?: 0 //获取巡检间隔
                    if (lastInspectionTime != null) {
                        //计算最新打卡时间到当前时间的间隔
//                        val interval = (Date().time - lastInspectionTime.time) / (1000 * 60) // 转换为分钟
                        val interval = Duration.between(lastInspectionTime.toInstant(), Instant.now()).toHours() // 转换为小时
                        println("最新打卡时间: ${lastInspectionTime}, 当前时间: ${Date()}, 巡检间隔: $requiredInterval 小时, 间隔: $interval 小时")
                        if (interval >= requiredInterval) {
                            //如果间隔大于等于巡检间隔 可以打卡
                            site.ifCheckIn = true
                        }
                    }else {
                        //如果规定时间内 一次打卡都没有 也可以直接打卡
                        site.ifCheckIn = true
                    }
                }
            }
            //如果是待打卡状态+间隔时间不足 状态改成“间隔时间不足”
            if (site.inspectionResult == "待打卡" && !site.ifCheckIn!!) {
                site.inspectionResult = "间隔时间不足"
            }
        }
        //将站点按照楼层分组
        var siteListByFloor = siteList.groupBy { it.floorName }
        return siteListByFloor
    }

    fun sortMapByCustomOrder(
        originalMap: Map<String, List<Site>>,
        orderList: List<String>
    ): SortedMap<String, List<Site>> {
        val orderMap = orderList.withIndex().associate { (index, key) -> key to index }

        val comparator = Comparator<String> { a, b ->
            val indexA = orderMap[a] ?: Int.MAX_VALUE
            val indexB = orderMap[b] ?: Int.MAX_VALUE
            indexA.compareTo(indexB)
        }

        return originalMap.toSortedMap(comparator)
    }

    fun exportInspectionrecord(search: InspectionrecordSearch):ByteArray? {
        search.ifPage = false //不分页
        var list = mapper.getList(search)
        if (list.isEmpty()) return null

        var export_list = ArrayList<InspectionrecordSimple>()
        list.forEach {
            export_list.add(InspectionrecordSimple().apply {
                siteName = it.siteName
                inspectionResult = if (it.inspectionResult == "success") "正常" else if (it.inspectionResult == "error") "异常" else ""
                inspectionMan = it.inspectionMan
                inspectionTime = SimpleDateFormat("yyyy/MM/dd HH:mm").format(it.inspectionTime)
                remarks = it.remarks
                siteStatus = if (it.siteStatus == "abnormal") "二维码标签异常" else ""
            })
        }

        var excelBytes = PoiUtil.exportExcel(export_list,emptySet())

        return excelBytes
    }

    override fun save(model: Inspectionrecord): Result {

        model.inspectionTime = Date()
        model.inspectionMan = getUser()!!.name //工作组账号登录 账号名称就是工作组名称
        //获取当前登录账户的机构Id 赋值
        var orgId = getUser()!!.branchOrganizationId
        model.orgId = orgId //设置机构Id

        //如果是提交更改二维码标签状态
        if (model.inspectionType == "A") {
            //1. 更新巡检记录的二维码标签状况
            getBean(SiteMapper::class.java).changeStatus(model.siteId, model.siteStatus, getUser()!!.name)
            //2. 如果是异常状态 需要插入数据库
            if (model.siteStatus == "abnormal") {
                super.save(model)
            }
            return Result.getSuccess("二维码标签状况已更新！")
        }

        //如果是提交打卡记录
        var rlt = super.save(model)

        if (rlt.rlt == 0) { //保存成功 插入关联文件
            model.contentList.forEach {
                it.id = uuid()
                it.inspectionrecordId = model.id
                getBean(ContentMapper::class.java).insert(it)
            }
        }

        //打卡成功 更新站点的二维码标签状况为正常
        getBean(SiteMapper::class.java).changeStatus(model.siteId, "normal", getUser()!!.name)

        return rlt
    }

    override fun getInfo(id: String): Result {
        var rlt = super.getInfo(id)
        if (rlt.rlt == 0 && rlt.datas != null) { //获取关联的文件列表
            var model = rlt.datas as Inspectionrecord
            model.contentList = getBean(ContentMapper::class.java).getListByInspectionrecordId(model.id)
        }
        return rlt
    }

    fun analyzeQrCode(qrCode: QrCode):Result {
        var qr = CQrDecode()
        var path = "./00000013_JHG.dat"
        var qrString = qrCode.qrString
        var qrTime = qrCode.qrTime
        var check2 = qrCode.check2
        var result = qrCode.result

        if (check2 != 0) {
            //说明是校时操作 校时操作需要传参check2及卡号 卡号是十六进制字符串 转成Int类型
            result[0] = Integer.parseInt(qrCode.targetId, 16)
        }

        var resultCode = qr.API_GetCodeIII(path, "", "", qrString, qrTime, dateFormat(Date()), check2, result)

        // 获取1900年的时间
        val calendar: Calendar = GregorianCalendar(1900, 0, 1, 0, 0, 0)
        // 获取1900年的时间戳（毫秒）
        val dt1900 = calendar.time.time
        // 转换获取的时间戳（秒）
        val longTime = result[3].toLong() and 0xFFFFFFFFL
        // 计算实际时间
        val actualTime = dateFormat(Date(longTime * 1000 + dt1900))

        var res = listOf(Integer.toHexString(result[0]).toUpperCase(), result[1], result[2], actualTime, result[4])
        var map = mutableMapOf("resultCode" to resultCode, "result" to res)
        return Result.getSuccess(map)
    }

    fun analyzeQrCode2(qrString: String):Result {
        //解析二维码字符串
        var qrcodeList = QrCodeUtil.decode(qrString).split("@")
        var siteNumber = qrcodeList[0]
        var checkTimeStr = qrcodeList[1] //解析出来的刷新时间 格式为yyyy-MM-dd HH:mm:ss
        println("设备编码：${siteNumber}, 解析出来的刷新时间：${checkTimeStr}")

        //获取二维码设备相关信息
        var qrcodedevice = getBean(QrcodedeviceMapper::class.java).getInfoBySiteNumber(siteNumber)
        if (qrcodedevice == null) {
            return Result.getError("二维码设备信息未被维护入库！")
        }
        //获取二维码设备的开始时间
        var startTimeStr = qrcodedevice.startTime
        //获取二维码设备的打卡周期
        var period = qrcodedevice.period.toLong()

        var formatterWithSeconds = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        var formatterWithoutSeconds = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
        var startTime = LocalDateTime.parse(startTimeStr, formatterWithoutSeconds)
        var checkTime = LocalDateTime.parse(checkTimeStr, formatterWithSeconds)
        var currentTime = LocalDateTime.now()

        //计算此次打卡有效的开始时间和结束时间范围
        var (lastRefreshTime, nextRefreshTime) = calculateRefreshTimes(startTime, currentTime, period)
        println("开始时间：$lastRefreshTime, 结束时间：$nextRefreshTime, 当前时间：$currentTime")

        //判断时间是否在 [lastRefreshTime, nextRefreshTime) 范围内
        //时间范围左右两边各放宽30秒误差
        lastRefreshTime = lastRefreshTime.minusSeconds(30)
        nextRefreshTime = nextRefreshTime.plusSeconds(30)
        println("放宽后的开始时间：$lastRefreshTime, 放宽后的结束时间：$nextRefreshTime, 当前时间：$currentTime")
        val ifValid = checkTime.isAfter(lastRefreshTime) && checkTime.isBefore(nextRefreshTime)
        println("二维码是否有效：$ifValid")
        log.info("二维码设备：$siteNumber, 是否有效：$ifValid, 开始时间：$lastRefreshTime, 结束时间：$nextRefreshTime, 当前时间：$currentTime")

        var map = mapOf("siteNumber" to siteNumber, "ifValid" to ifValid)
        return Result.getSuccess(map)
    }

    // 私有方法：计算刷新时间范围
    private fun calculateRefreshTimes(startTime: LocalDateTime, referenceTime: LocalDateTime, periodSeconds: Long): Pair<LocalDateTime, LocalDateTime> {
        return when {
            // 情况1：开始时间在当前时间之后
            startTime.isAfter(referenceTime) -> {
                startTime to startTime.plusSeconds(periodSeconds)
            }

            // 情况2：开始时间在当前时间之前或相等
            else -> {
                var elapsedSeconds = Duration.between(startTime, referenceTime).seconds
                var fullPeriods = elapsedSeconds / periodSeconds //取模计算最接近当前时间的完整的周期数
                var lastRefreshTime = startTime.plusSeconds(fullPeriods * periodSeconds)
                lastRefreshTime to lastRefreshTime.plusSeconds(periodSeconds)
            }
        }
    }

}

class InspectionrecordSimple {
    @ExcelColumn(name = "巡检点", order = 1)
    var siteName: String = ""
    @ExcelColumn(name = "巡检结果", order = 2)
    var inspectionResult: String = ""
    @ExcelColumn(name = "巡检人", order = 3)
    var inspectionMan: String = ""
    @ExcelColumn(name = "时间", order = 4)
    var inspectionTime: String = ""
    @ExcelColumn(name = "巡检说明", order = 5)
    var remarks: String = ""
    @ExcelColumn(name = "其他", order = 6)
    var siteStatus: String = ""
}

class QrCode {
    var qrString: String = "" //二维码字符串
    var qrTime: String = "" //二维码时间 格式为yyyy-MM-dd HH:mm:ss
    var check2: Int = 0 //校验码
    var result: IntArray = IntArray(5) { 0 } //结果数组
    var targetId: String = "" //十六进制卡号

    constructor()
}

class QrCode2 {
    var qrString: String = "" //二维码字符串
    var qrTime: String = "" //二维码开始时间 格式为yyyy-MM-dd HH:mm:ss
    var period: Long = 0L //刷屏周期(s)

    constructor()
}

/**
 * 检查当前时间是否在指定时间之前
 */
fun isCurrentTimeBefore(timeString: String): Boolean {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    val targetTime = LocalDateTime.parse(timeString, formatter)
    val currentTime = LocalDateTime.now()
    // 模拟当前时间为2025-06-26 23:00:00
//    val currentTime = LocalDateTime.of(2025, 6, 27, 23, 0, 0)
    return currentTime.isBefore(targetTime)
}

/**
 * 检查当前时间是否在指定时间之后
 */
fun isCurrentTimeAfter(timeString: String): Boolean {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    val targetTime = LocalDateTime.parse(timeString, formatter)
    val currentTime = LocalDateTime.now()
    // 模拟当前时间为2025-06-26 23:00:00
//    val currentTime = LocalDateTime.of(2025, 6, 27, 23, 0, 0)
    return currentTime.isAfter(targetTime)
}

fun handleTaskQuery(dateFromFront: String, taskStartHour: Int, taskStartMinute: Int):HashMap<String, String> {

    var result = HashMap<String, String>()

    // 定义常量
    val DATE_FORMATTER = DateTimeFormatter.ISO_DATE
    val TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    val TASK_START_HOUR = taskStartHour
    val TASK_START_MINUTE = taskStartMinute

    // 解析日期（只解析一次）
    val queryDate = try {
        LocalDate.parse(dateFromFront, DATE_FORMATTER)
    } catch (e: Exception) {
        println("日期格式错误: $dateFromFront")
        return result
    }

    val today = LocalDate.now()
    val timeZone = ZoneId.systemDefault()

    // 检查日期范围
    when {
        queryDate > today -> {
            println("查询日期大于今天")
            //返回查询日期
            val startTime = queryDate.atStartOfDay(timeZone).plusHours(TASK_START_HOUR.toLong()).plusMinutes(TASK_START_MINUTE.toLong())
            val endTime = startTime.plusDays(1)
            return result.apply {
                put("startTime", startTime.format(TIME_FORMATTER))
                put("endTime", endTime.format(TIME_FORMATTER))
            }
        }
        queryDate < today -> {
            // 处理历史数据
            val startTime = queryDate.atStartOfDay(timeZone).plusHours(TASK_START_HOUR.toLong()).plusMinutes(TASK_START_MINUTE.toLong())
            val endTime = startTime.plusDays(1)
            println("历史任务: ${startTime.format(TIME_FORMATTER)} 到 ${endTime.format(TIME_FORMATTER)}")
            return result.apply {
                put("startTime", startTime.format(TIME_FORMATTER))
                put("endTime", endTime.format(TIME_FORMATTER))
            }
        }
    }

    // 处理当天数据
    val now = ZonedDateTime.now(timeZone)
    // 模拟当前时间十分秒
//        val now = ZonedDateTime.of(today, LocalTime.of(2, 0), timeZone) // 模拟当前时间为今日07:00
    val taskStartTime = ZonedDateTime.of(today, LocalTime.of(TASK_START_HOUR, TASK_START_MINUTE), timeZone)
    val taskEndTime = taskStartTime.plusDays(1)

    when {
        now >= taskStartTime && now < taskEndTime -> {
            println("今日任务: ${taskStartTime.format(TIME_FORMATTER)} 到 ${taskEndTime.format(TIME_FORMATTER)}")
            return result.apply {
                put("startTime", taskStartTime.format(TIME_FORMATTER))
                put("endTime", taskEndTime.format(TIME_FORMATTER))
            }
        }
        else -> {
            println("上一伦任务: ${
                taskStartTime.minusDays(1).format(TIME_FORMATTER)
            } 到 ${taskStartTime.format(TIME_FORMATTER)}")
            return result.apply {
                put("startTime", taskStartTime.minusDays(1).format(TIME_FORMATTER))
                put("endTime", taskStartTime.format(TIME_FORMATTER))
            }
        }
    }
}

@RestController
@RequestMapping("/api/Inspectionrecord")
open class InspectionrecordResource(
    service: InspectionrecordService,
    private val appPro: AppPro
) : BaseResource<InspectionrecordSearch, Inspectionrecord, InspectionrecordMapper, InspectionrecordService>(service) {

    @PostMapping("/getInspectionrecord")
    fun getInspectionrecord(@RequestBody search: InspectionrecordSearch):Result {
        return service.getInspectionrecord(search)
    }

    @PostMapping("/exportInspectionrecord")
    fun exportInspectionrecord(@RequestBody search: InspectionrecordSearch):Result {
        return Result.getSuccess(service.exportInspectionrecord(search))
    }

    @GetMapping("/downloadInspectionrecord/{startTime}/{endTime}")
    fun downloadInspectionrecord(@PathVariable startTime: String, @PathVariable endTime: String): ResponseEntity<ByteArrayResource> {
        var excelBytes = service.exportInspectionrecord(InspectionrecordSearch().apply {
            this.startTime = startTime + " 00:00:00"
            this.endTime = endTime + " 23:59:59"
        })

        if (excelBytes == null) {
            return ResponseEntity.notFound().build()
        }

        var filename = "巡检记录${startTime}~${endTime}.xlsx"

        return ResponseEntity.ok()
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=\"${encodeFilename(filename)}\""
            )
            .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
            .header(HttpHeaders.PRAGMA, "no-cache")
            .header(HttpHeaders.EXPIRES, "0")
            .body(ByteArrayResource(excelBytes))

    }

    private fun encodeFilename(filename: String): String {
        return filename.replace("\"", "\\\"")
            .toByteArray(StandardCharsets.UTF_8)
            .let { java.net.URLEncoder.encode(String(it), "UTF-8") }
    }

    @PostMapping("/analyzeQrCode")
    fun analyzeQrCode(@RequestBody qrCode: QrCode): Result {
        return service.analyzeQrCode(qrCode)
    }

    @GetMapping("/analyzeQrCode2/{qrString}")
    fun analyzeQrCode2(@PathVariable qrString: String): Result {
        return service.analyzeQrCode2(qrString)
    }

    @PostMapping("/download")
    fun download(@RequestBody search: InspectionrecordSearch): Result {
        //获取当前登录账户的机构Id 仅查询该机构下的数据
        var orgId = getUser()!!.branchOrganizationId

        // 1. 生成Excel文件字节
        val excelBytes = service.exportInspectionrecord(InspectionrecordSearch().apply {
            this.startTime = search.startTime + " 00:00:00"
            this.endTime = search.endTime + " 23:59:59"
            this.orgId = orgId
        }) ?:
        return Result.getError("没有可导出的巡检记录数据")

        // 2. 创建导出目录（如果不存在）
        val date = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)
        val dir = Paths.get(appPro.uploadPath, date)
        Files.createDirectories(dir)

        // 3. 生成唯一文件名并保存
//        val filename = "巡检记录${search.startTime}~${search.endTime}.xlsx"
        val filename = "${System.currentTimeMillis()}.xlsx"
        val filePath = dir.resolve(filename)
        Files.write(filePath, excelBytes)

        // 4. 返回文件访问URL
        val fileUrl = "${appPro.imgPrefix}${appPro.uploadDir}/${date}/${filename}"
        return Result.getSuccess(fileUrl)
    }

}

