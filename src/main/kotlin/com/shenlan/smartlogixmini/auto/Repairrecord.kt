package com.shenlan.smartlogixmini.auto

import com.fasterxml.jackson.annotation.JsonFormat
import com.github.pagehelper.PageHelper
import com.shenlan.smartlogixmini.mybatis.paginationInfo
import com.shenlan.smartlogixmini.util.*
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.HashMap


class Repairrecord : BaseModel {
    /** 报修人员账户 */
    var repairManId: String = ""
    /** 报修人员姓名 */
    var repairMan: String = ""
    /** 报修人员手机 */
    var repairTel: String = ""
    /** 报修位置 */
    var position: String = ""
    /** 紧急程度(高-high、中-middle、低-low) */
    var urgency: String = ""
    /** 报修内容 */
    var repairContent: String = ""
    /** 报修时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    var repairTime: Date? = null
    /** 报修状态(待处理-pending、处理中-processing、已完结-completed、已取消-cancelled) */
    var repairStatus: String = ""
    /** 催办状态(催办-urging、已催办-urged) */
    var urgentStatus: String = ""
    /** 催办内容 */
    var urgentContent: String = ""
    /** 催办时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    var urgentTime: Date? = null
    /** 指派状态(待指派-assigning、已指派-assigned、已重新指派-reassigned) */
    var assignStatus: String = ""
    /** 指派时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    var assignTime: Date? = null
    /** 维修人员 */
    var fixMan: String = ""
    /** 维修人员手机 */
    var fixManTel: String = ""
    /** 完结时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    var completeTime: Date? = null
    /** 完结原因(已解决、无法处理、耗材不够，需要备件、其他) */
    var completeReason: String = ""
    /** 备注 */
    var remarks: String = ""
    /** 取消人员 */
    var cancelMan: String = ""
    /** 取消时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    var cancelTime: Date? = null
    /** 取消原因 */
    var cancelReason: String = ""
    /** 报修单编号 */
    var repairNumber: String = ""

    //--------------------------------
    /** 登录用户角色(职工-workers or 管理员-administrator) */
    var loginUserRole: String = ""
    var contentList: List<Content> = listOf()

    constructor()
}

@Mapper
interface RepairrecordMapper : BaseMapper<Repairrecord> {

    @Update(""" update tbl_repairrecord set repairStatus = 'completed',completeTime = SYSDATE(),completeReason = #{completeReason},remarks = #{remarks} where id = #{id} """)
    fun repairComplete(completeReason:String, remarks: String, id: String ):Int

    @Update(""" update tbl_repairrecord set assignStatus = #{assignStatus},repairStatus = 'processing',assignTime = SYSDATE(),fixMan = #{fixMan},fixManTel = #{fixManTel} where id = #{id} """)
    fun repairAssign(assignStatus: String, fixMan: String, fixManTel: String, id: String):Int

    @Select(""" select count(1) from tbl_repairrecord where DATE(repairTime) = CURDATE() """)
    fun getTodayRepairCount(): Int

    @Update(""" update tbl_repairrecord set cancelTime = SYSDATE(),cancelReason = #{cancelReason},cancelMan = #{cancelMan},repairStatus = 'cancelled' where id = #{id} """)
    fun cancelRepair(id: String, cancelReason: String, cancelMan: String):Int

    @Update(""" update tbl_repairrecord set urgentStatus = 'urged',urgentContent = #{urgentContent},urgentTime = SYSDATE() where id = #{id} """)
    fun urgentRepair(id: String, urgentContent: String): Int

    @Select(""" select * from tbl_repairrecord where 1=1 ${'$'}{whereSql} order by sysUpdated desc """)
    override fun getList(search: BaseSearch): List<Repairrecord>
}

class RepairrecordSearch : BaseSearch {

    var keyword:String = ""
    var repairStatus:String = "" //待处理-pending、处理中-processing、已完结-completed、已取消-cancelled
    var repairManId:String = ""
    var fixMan:String = ""
    var whereSql = ""
        get() {
            var sql = ""
            if (keyword.notEmpty()) sql += " and repairContent like '%${keyword}%' "
            if (repairStatus.notEmpty()) sql += " and repairStatus = '${repairStatus}' " //获取四个状态下的列表用此字段做筛选
            if (repairManId.notEmpty()) sql += " and repairManId = '${repairManId}' " //职工端只能看到自己提交的记录，筛选此字段
            if(startTime.notEmpty() && endTime.notEmpty()) {
                sql +=
                    when(repairStatus) {
                        "pending" -> " and (repairTime BETWEEN '$startTime' and '$endTime') " //待处理-筛选报修时间
                        "processing" -> " and (assignTime BETWEEN '$startTime' and '$endTime') " //处理中-筛选指派时间
                        "completed" -> " and (completeTime BETWEEN '$startTime' and '$endTime') " //已完结-筛选完结时间
                        "cancelled" -> " and (cancelTime BETWEEN '$startTime' and '$endTime') " //已取消-筛选取消时间
                    else -> ""
                }
            }
            if (fixMan.notEmpty()) sql += " and fixMan = '${fixMan}' "
            return sql
        }

    var loginUserRole = "" //当前登录账号是以 职工-workers or 管理员-administrator 身份登录

    constructor()
}

@Service
open class RepairrecordService(mapper: RepairrecordMapper) : BaseService<Repairrecord, RepairrecordMapper>(mapper) {

    override fun getInfo(id: String): Result {
        var rlt = super.getInfo(id)
        if (rlt.rlt == 0 && rlt.datas != null) { //获取关联的文件列表
            var model = rlt.datas as Repairrecord
            model.contentList = getBean(ContentMapper::class.java).getListByrepairrecordId(model.id)
        }
        return rlt
    }

    override fun getList(page: BaseSearch): Result {

        var loginUserId = getUser()!!.id //当前登录账户Id
        page as RepairrecordSearch
        var loginUserRole = page.loginUserRole
        if (loginUserRole == "workers") {
            page.repairManId = loginUserId //如果是职工身份登录时 只获取职工关联的列表 不给repairManId赋值则获取所有
        }

        var result: Any?
        var list: List<Repairrecord>
        if (page.ifPage) {
            // 使用分页查询
            result = PageHelper.startPage<Repairrecord>(page.currentPage!!, page.pageRecord!!)
                .doSelectPageInfo<Repairrecord> {
                    mapper.getList(page)
                }
                .paginationInfo
            list = result.result as List<Repairrecord>
        }else {
            //不使用分页查询
            result = mapper.getList(page)
            list = result
        }

        //获取关联的文件列表
        list.forEach {
            it.contentList = getBean(ContentMapper::class.java).getListByrepairrecordId(it.id)
        }

        return Result.getSuccess(result)
    }

    override fun save(model: Repairrecord): Result {

        if (model.id.isEmpty()) { //新增
            model.repairTime = Date()
            model.repairStatus = "pending"
            model.urgentStatus = "urging"
            model.assignStatus = "assigning"
            model.repairNumber = "BXGL-" + SimpleDateFormat("yyyyMMdd").format(Date()) + "-" + String.format("%03d",(mapper.getTodayRepairCount()+1))
        }

        var rlt = super.save(model)

        if (rlt.rlt == 0) { //保存成功 插入关联文件
            model.contentList.forEach {
                it.id = uuid()
                it.repairrecordId = model.id
                getBean(ContentMapper::class.java).insert(it)
            }
        }

        //todo: 报修成功 小程序给所有管理员微信用户推送通知提醒
        var map = mutableMapOf(
            "repairContent" to model.repairContent,
            "urgency" to model.urgency,
            "repairMan" to model.repairMan,
            "repairTime" to model.repairTime
        )
        map.pj()
        //获取所有管理员账户列表 推送

        return rlt
    }

    fun cancelRepair(id: String, cancelReason: String, loginUserRole: String):Result {

        var loginUserName = getUser()!!.name //获取当前登录账号人姓名
        mapper.cancelRepair(id, cancelReason, loginUserName)

        //todo: 取消成功 推送通知提醒
        var record = mapper.getInfo(id) //获取报修单信息
        if (record == null) return Result.getError("报修单不存在")
        if (loginUserRole == "workers") { //如果取消人是以职工身份登录的

            //小程序给所有管理员微信用户推送通知提醒
            var map = mutableMapOf(
                "cancelReason" to cancelReason,
                "cancelMan" to loginUserName,
                "cancelTime" to record.cancelTime
            )
            map.pj()
            //获取所有管理员账户列表 推送

        }else { //如果取消人是管理员

            //小程序给对应职工微信用户推送通知提醒
            var map = mutableMapOf(
                "repairContent" to record.repairContent,
                "repairStatus" to record.repairStatus,
                "cancelReason" to cancelReason,
                "cancelTime" to record.cancelTime
            )
            map.pj()
            var repairManId = record.repairManId
        }


        return Result.getSuccess("取消成功")
    }

    fun urgentRepair(id: String, urgentContent: String):Result {

        mapper.urgentRepair(id, urgentContent)

        //todo: 报修成功 小程序给所有管理员微信用户推送通知提醒
        var record = mapper.getInfo(id)
        if (record == null) return Result.getError("报修单不存在")
        var map = mutableMapOf(
            "urgentContent" to urgentContent,
            "repairMan" to record.repairMan, //催办人员只能是保修单创建者
            "urgentTime" to record.urgentTime
        )
        map.pj()
        //获取所有管理员账户列表 推送

        return Result.getSuccess("催办成功")
    }

    fun repairAssign(assignStatus:String, fixMan: String, fixManTel: String, id: String):Result {

        mapper.repairAssign(assignStatus, fixMan, fixManTel, id)

        //todo: 指派成功 小程序给对应职工微信用户推送通知提醒
        var record = mapper.getInfo(id)
        if (record == null) return Result.getError("报修单不存在")
        var map = mutableMapOf(
            "repairContent" to record.repairContent,
            "assignStatus" to assignStatus,
            "fixMan" to fixMan,
            "fixManTel" to fixManTel
        )
        map.pj()
        var repairManId = record.repairManId

        return Result.getSuccess("指派成功")
    }

    fun repairComplete(completeReason:String, remarks: String, id: String):Result {

        mapper.repairComplete(completeReason, remarks, id)

        //todo: 完结成功 小程序给对应职工微信用户推送通知提醒
        var record = mapper.getInfo(id)
        if (record == null) return Result.getError("报修单不存在")
        var map = mutableMapOf(
            "repairContent" to record.repairContent,
            "repairStatus" to record.repairStatus,
            "completeReason" to completeReason,
            "completeTime" to record.completeTime
        )
        map.pj()
        var repairManId = record.repairManId

        return Result.getSuccess("完结成功")
    }

}

@RestController
@RequestMapping("/api/Repairrecord")
open class RepairrecordResource(service: RepairrecordService) : BaseResource<RepairrecordSearch, Repairrecord, RepairrecordMapper, RepairrecordService>(service) {

    /**
     * 取消报修
     */
    @PostMapping("/cancelRepair")
    fun cancelRepair(@RequestBody model: Repairrecord):Result {
        return service.cancelRepair(model.id, model.cancelReason, model.loginUserRole)
    }

    /**
     * 催办报修
     */
    @PostMapping("/urgentRepair")
    fun urgentRepair(@RequestBody model: Repairrecord):Result {
        return service.urgentRepair(model.id, model.urgentContent)
    }

    /**
     * 报修指派
     */
    @PostMapping("/repairAssign")
    fun repairAssign(@RequestBody model: Repairrecord):Result {
        return service.repairAssign(model.assignStatus, model.fixMan, model.fixManTel, model.id)
    }

    /**
     * 报修完结
     */
    @PostMapping("/repairComplete")
    fun repairComplete(@RequestBody model: Repairrecord):Result {
        return service.repairComplete(model.completeReason, model.remarks, model.id)
    }
}

