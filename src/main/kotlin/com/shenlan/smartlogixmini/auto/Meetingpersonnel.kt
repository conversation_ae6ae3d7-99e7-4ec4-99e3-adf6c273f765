package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 参会人员关系实体类
 */
class Meetingpersonnel : BaseModel() {
    /** 会议ID */
    var meetingId: String = ""
    /** 人员ID */
    var personnelId: String = ""
    /** 参会角色(0-普通参会人,1-发起人) */
    var role: Int = 0
    /** 参会反馈(0-未反馈,1-参加,2-建议延期,3-不参加) */
    var feedback: Int = 0
    /** 反馈原因 */
    var reason: String = ""

    /** 关联的人员信息 */
    var personnel: Personnel? = null
}

/**
 * 参会人员关系查询条件类
 */
class MeetingpersonnelSearch : BaseSearch() {
    /** 会议ID */
    var meetingId: String = ""
    /** 人员ID */
    var personnelId: String = ""
    /** 参会反馈(0-未反馈,1-参加,2-建议延期,3-不参加) */
    var feedback: Int = 0
    /** 是否加载人员信息 */
    var loadPersonnel: Boolean = false
}

/**
 * 参会人员关系Mapper接口
 */
@Mapper
interface MeetingpersonnelMapper : BaseMapper<Meetingpersonnel> {

    @Select("""
        <script>
            SELECT mp.*, p.name, p.phone, p.organizationId, p.position, o.name as organizationName
            FROM tbl_meetingpersonnel mp
            LEFT JOIN tbl_personnel p ON mp.personnelId = p.id
            LEFT JOIN tbl_organization o ON p.organizationId = o.id
            <where>
                mp.sysDeleted = 0
                <if test="meetingId != ''">
                    AND mp.meetingId = #{meetingId}
                </if>
                <if test="personnelId != ''">
                    AND mp.personnelId = #{personnelId}
                </if>
                <if test="feedback != 0">
                    AND mp.feedback = #{feedback}
                </if>
            </where>
            ORDER BY mp.role DESC, p.name
        </script>
    """)
    override fun getList(search: BaseSearch): List<Meetingpersonnel>

    @Select("""
        SELECT COUNT(*) FROM tbl_meetingpersonnel
        WHERE meetingId = #{meetingId} AND feedback = #{feedback} AND sysDeleted = 0
    """)
    fun countFeedbackByMeeting(meetingId: String, feedback: Int): Int

    @Select("""
        SELECT mp.* FROM tbl_meetingpersonnel mp
        JOIN tbl_personnel p ON mp.personnelId = p.id
        WHERE mp.meetingId = #{meetingId} AND p.phone = #{phone}
        AND mp.sysDeleted = 0
        LIMIT 1
    """)
    fun getInfoByMeetingIdAndPhone(meetingId: String, phone: String): Meetingpersonnel?

    @Update("""
        UPDATE tbl_meetingpersonnel
        SET feedback = #{feedback}, reason = #{reason}, sysUpdated = NOW()
        WHERE meetingId = #{meetingId} AND personnelId = #{personnelId} AND sysDeleted = 0
    """)
    fun updateFeedback(meetingId: String, personnelId: String, feedback: Int, reason: String): Int
}

/**
 * 参会人员关系Service类
 */
@Service
class MeetingpersonnelService(
    mapper: MeetingpersonnelMapper,
    private val personnelMapper: PersonnelMapper,
    private val personnelService: PersonnelService
) : BaseService<Meetingpersonnel, MeetingpersonnelMapper>(mapper) {

    /**
     * 重写获取单个信息的方法，支持加载关联的人员信息
     */
    override fun getEntity(id: String): Meetingpersonnel? {
        val meetingpersonnel = mapper.getInfo(id) ?: return null

        // 如果需要加载人员信息
        if (meetingpersonnel.personnelId.isNotEmpty()) {
            meetingpersonnel.personnel = personnelMapper.getInfo(meetingpersonnel.personnelId)
        }

        return meetingpersonnel
    }

    /**
     * 重写获取列表的方法，支持加载关联的人员信息
     */
    override fun getEntityPage(search: BaseSearch): PaginationInfo<Meetingpersonnel> {
        search as MeetingpersonnelSearch
        val paginationInfo = super.getEntityPage(search)

        // 如果需要加载人员信息
        if (search.loadPersonnel) {
            for (item in paginationInfo.result) {
                if (item.personnelId.isNotEmpty()) {
                    item.personnel = personnelMapper.getInfo(item.personnelId)
                }
            }
        }

        return paginationInfo
    }

    /**
     * 统计会议的参会反馈情况
     */
    fun getFeedbackStats(meetingId: String): Result {
        val stats = hashMapOf<String, Int>()

        val noResponse = mapper.countFeedbackByMeeting(meetingId, 0)
        val attend = mapper.countFeedbackByMeeting(meetingId, 1)
        val delay = mapper.countFeedbackByMeeting(meetingId, 2)
        val decline = mapper.countFeedbackByMeeting(meetingId, 3)

        val total = noResponse + attend + delay + decline

        stats["total"] = total
        stats["noResponse"] = noResponse
        stats["attend"] = attend
        stats["delay"] = delay
        stats["decline"] = decline

        return Result.getSuccess(stats)
    }

    /**
     * 获取当前登录用户在指定会议中的参会信息
     */
    fun getCurrentUserMeetingPersonnel(meetingId: String): Result {
        // 获取当前登录用户对应的人员信息
        val currentPersonnel = personnelService.getCurrentPersonnel()
            ?: return Result.getError("未找到当前登录用户对应的人员信息")

        // 查询该用户在指定会议中的参会信息
        val meetingPersonnel = mapper.getInfoByMeetingIdAndPhone(meetingId, currentPersonnel.phone)
            ?: return Result.getError("您不是该会议的参会人员")

        // 加载关联的人员信息
        meetingPersonnel.personnel = currentPersonnel

        return Result.getSuccess(meetingPersonnel)
    }
}

/**
 * 参会人员关系Controller类
 */
@RestController
@RequestMapping("/api/Meetingpersonnel")
class MeetingpersonnelResource(service: MeetingpersonnelService) : BaseResource<MeetingpersonnelSearch, Meetingpersonnel, MeetingpersonnelMapper, MeetingpersonnelService>(service) {

    @GetMapping("/getFeedbackStats/{meetingId}")
    fun getFeedbackStats(@PathVariable meetingId: String): Result {
        return service.getFeedbackStats(meetingId)
    }

    @GetMapping("/getCurrentUserMeetingPersonnel/{meetingId}")
    fun getCurrentUserMeetingPersonnel(@PathVariable meetingId: String): Result {
        return service.getCurrentUserMeetingPersonnel(meetingId)
    }
}
