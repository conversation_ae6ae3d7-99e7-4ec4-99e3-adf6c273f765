package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.localDateTimeFormatter
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDateTime

class Commutevehicle: BaseModel() {
    var driverId: String = ""
    var routeId: String = ""
    var status: Int = 0
    var carId: String = ""
    var carPlate: String = ""
    var plateColor: String = ""
    var plateColorCode: String = ""
    var carName: String = ""
    var teamId: String = ""
    var teamName: String = ""
    var cmpName: String = ""
    var gps: Boolean = false
    var tmnKey: String = ""
    var dueDate: String = ""
    var onlineTime: String = ""
    var icon: String = ""
    var iconLink: String = ""
    var des: String = ""
    var bxts: String = ""
    var sysDeleted: Int = 0

    var lat: Double = 0.0
    var lng: Double = 0.0
    var commuteTrip: Commutetrip? = null
    var commuteRoute: Commuteroute? = null
}

enum class CommuteVehicleStatus(val value: Int) {
    OFFLINE(0),
    ONLINE(1),
    MAINTENANCE(2)
}

class CommutevehicleSearch: BaseSearch() {
    var carId: String = ""
    var driverId: String = ""
    var routeId: String = ""
    var status: Int? = null
    var carPlate: String = ""
    var carName: String = ""
    var teamId: String = ""
    var teamName: String = ""
    var cmpName: String = ""
}

@Mapper
interface CommutevehicleMapper : BaseMapper<Commutevehicle> {
    @Select("""
        <script>
            SELECT cv.*, cvl.lat, cvl.lng FROM tbl_commutevehicle cv left join tbl_commutevehiclelocation cvl on cv.id = cvl.id
            <where>
                AND cv.sysDeleted = 0
                <if test="carId != ''">
                    AND cv.id = #{carId}
                </if>
                <if test="driverId != ''">
                    AND cv.driverId = #{driverId}
                </if>
                <if test="routeId != ''">
                    AND cv.routeId = #{routeId}
                </if>
                <if test="status != null">
                    AND cv.status = #{status}
                </if>
            </where>
            ORDER BY cv.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutevehicle>

    // 获取所有班车id
    @Select("""
        SELECT DISTINCT id FROM tbl_commutevehicle
    """)
    fun getAllCarId(): List<String>

    // 批量更新班车信息
    @Update("""
        <script>
            UPDATE tbl_commutevehicle
            <set>
                carPlate = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.carPlate}
                </foreach>
                END,
                plateColor = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.plateColor}
                </foreach>
                END,
                plateColorCode = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.plateColorCode}
                </foreach>
                END,
                carName = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.carName}
                </foreach>
                END,
                teamId = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.teamId}
                </foreach>
                END,
                teamName = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.teamName}
                </foreach>
                END,
                cmpName = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.cmpName}
                    </foreach>
                END,
                gps = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.gps}
                </foreach>
                END,
                tmnKey = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.tmnKey}
                </foreach>
                END,
                dueDate = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.dueDate}
                </foreach>
                END,
                onlineTime = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.onlineTime}
                </foreach>
                END,
                icon = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.icon}
                </foreach>
                END,
                iconLink = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.iconLink}
                </foreach>
                END,
                des = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.des}
                </foreach>
                END,
                bxts = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.bxts}
                </foreach>
                END
            </set>
            WHERE id IN
            <foreach collection="vehicles" item="vehicle" open="(" separator="," close=")">
                #{vehicle.id}
            </foreach>
        </script>
    """)
    fun batchUpdateVehicle(@Param("vehicles") list: List<Commutevehicle>)

    // 更新driverId、routeId、status
    @Update("""
        UPDATE tbl_commutevehicle
        SET driverId = #{driverId}, routeId = #{routeId}, status = #{status}
        WHERE id = #{carId}
    """)
    fun updateVehicleStatus(vehicle: Commutevehicle)
}

@Service
class CommutevehicleService(mapper: CommutevehicleMapper) : BaseService<Commutevehicle, CommutevehicleMapper>(mapper) {
    fun updateVehicleStatus(vehicle: Commutevehicle): Result {
        // 1.1 更新班车状态
        mapper.updateVehicleStatus(vehicle)

        // 1.2 记录班车发车记录
        if (vehicle.status == CommuteVehicleStatus.ONLINE.value) {
            val commuteTrip = Commutetrip().apply {
                this.id = uuid()
                this.carId = vehicle.carId
                this.driverId = vehicle.driverId
                this.routeId = vehicle.routeId
                this.status = CommuteVehicleStatus.ONLINE.value
                this.startTime = localDateTimeFormatter.format(LocalDateTime.now())
                this.predictEndTime = localDateTimeFormatter.format(LocalDateTime.now().plusHours(2))
            }
            getBean(CommutetripMapper::class.java).insert(commuteTrip)
        } else if (vehicle.status == CommuteVehicleStatus.OFFLINE.value) {
            getBean(CommutetripMapper::class.java).updateTripStatus(vehicle.carId, CommuteVehicleStatus.OFFLINE.value, localDateTimeFormatter.format(LocalDateTime.now()))
        }

        return Result.getSuccess(vehicle.id)
    }

    fun getVehicleDetail(search: CommutevehicleSearch): Result {
        val commuteRouteMapper = getBean(CommuterouteMapper::class.java)
        val commuteTripMapper = getBean(CommutetripMapper::class.java)
        val commuteRouteStopMapper = getBean(CommuteroutestopMapper::class.java)

        // 1.1 获取班车信息
        val vehicle = mapper.getList(search).firstOrNull() ?: return Result.getError("未找到班车信息")

        // 1.2 获取发车记录
        val commuteTrip = commuteTripMapper.getList(CommutetripSearch().apply {
            this.carId = vehicle.id
            this.status = CommuteVehicleStatus.ONLINE.value
        }).firstOrNull()

        // 1.3 获取路线信息
        commuteTrip?.let { trip ->
            vehicle.commuteTrip = trip
            vehicle.commuteRoute = commuteRouteMapper.getInfo(trip.routeId)
            vehicle.commuteRoute?.stopList?.addAll(commuteRouteStopMapper.getList(CommuteroutestopSearch().apply { routeId = trip.routeId }))
        }

        return Result.getSuccess(vehicle)
    }
}

@RestController
@RequestMapping("/api/commutevehicle")
class CommutevehicleResource(service: CommutevehicleService) : BaseResource<CommutevehicleSearch, Commutevehicle, CommutevehicleMapper, CommutevehicleService>(service) {
    @PostMapping("/updateVehicleStatus")
    fun updateVehicleStatus(@RequestBody vehicle: Commutevehicle): Result {
        return service.updateVehicleStatus(vehicle)
    }

    // 获取班车详细
    @PostMapping("/getVehicleDetail")
    fun getVehicleDetail(@RequestBody search: CommutevehicleSearch): Result {
        return service.getVehicleDetail(search)
    }
}
