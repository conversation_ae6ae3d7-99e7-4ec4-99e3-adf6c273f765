package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.localDateTimeFormatter
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDateTime

class Commutevehicle: BaseModel() {
    var driverId: String = ""
    var routeId: String = ""
    var status: Int = 0
    var carId: String = ""
    var carPlate: String = ""
    var plateColor: String = ""
    var plateColorCode: String = ""
    var carName: String = ""
    var teamId: String = ""
    var teamName: String = ""
    var cmpName: String = ""
    var gps: Boolean = false
    var tmnKey: String = ""
    var dueDate: String? = null
    var onlineTime: String? = null
    var icon: String = ""
    var iconLink: String = ""
    var des: String = ""
    var bxts: String = ""
    var sysDeleted: Int = 0

    var lat: Double = 0.0
    var lng: Double = 0.0
    var commuteTrip: Commutetrip? = null
    var commuteRoute: Commuteroute? = null

    // 关注状态
    var followed: Boolean = false
}

enum class CommuteVehicleStatus(val value: Int) {
    OFFLINE(0),
    ONLINE(1),
    MAINTENANCE(2)
}

class CommutevehicleSearch: BaseSearch() {
    var carId: String = ""
    var driverId: String = ""
    var routeId: String = ""
    var status: Int? = null
    var carPlate: String = ""
    var carName: String = ""
    var teamId: String = ""
    var teamName: String = ""
    var cmpName: String = ""
    var routeType: Int? = null
    var userId: String = ""
}

@Mapper
interface CommutevehicleMapper : BaseMapper<Commutevehicle> {
    @Select("""
        <script>
            SELECT cv.*, cvl.lat, cvl.lng FROM tbl_commutevehicle cv left join tbl_commutevehiclelocation cvl on cv.id = cvl.id
            <where>
                AND cv.sysDeleted = 0
                <if test="carId != ''">
                    AND cv.id = #{carId}
                </if>
                <if test="driverId != ''">
                    AND cv.driverId = #{driverId}
                </if>
                <if test="routeId != ''">
                    AND cv.routeId = #{routeId}
                </if>
                <if test="status != null">
                    AND cv.status = #{status}
                </if>
            </where>
            ORDER BY cv.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutevehicle>

    // 获取所有班车id
    @Select("""
        SELECT DISTINCT id FROM tbl_commutevehicle
    """)
    fun getAllCarId(): List<String>

    // 批量更新班车信息
    @Update("""
        <script>
            UPDATE tbl_commutevehicle
            <set>
                carPlate = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.carPlate}
                </foreach>
                END,
                plateColor = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.plateColor}
                </foreach>
                END,
                plateColorCode = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.plateColorCode}
                </foreach>
                END,
                carName = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.carName}
                </foreach>
                END,
                teamId = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.teamId}
                </foreach>
                END,
                teamName = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.teamName}
                </foreach>
                END,
                cmpName = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.cmpName}
                    </foreach>
                END,
                gps = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.gps}
                </foreach>
                END,
                tmnKey = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.tmnKey}
                </foreach>
                END,
                dueDate = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.dueDate}
                </foreach>
                END,
                onlineTime = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.onlineTime}
                </foreach>
                END,
                icon = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.icon}
                </foreach>
                END,
                iconLink = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.iconLink}
                </foreach>
                END,
                des = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.des}
                </foreach>
                END,
                bxts = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.bxts}
                </foreach>
                END
            </set>
            WHERE id IN
            <foreach collection="vehicles" item="vehicle" open="(" separator="," close=")">
                #{vehicle.id}
            </foreach>
        </script>
    """)
    fun batchUpdateVehicle(@Param("vehicles") list: List<Commutevehicle>)

    // 更新driverId、routeId、status
    @Update("""
        UPDATE tbl_commutevehicle
        SET driverId = #{driverId}, routeId = #{routeId}, status = #{status}
        WHERE id = #{carId}
    """)
    fun updateVehicleStatus(vehicle: Commutevehicle)

    // 获取所有班车
    @Select("""
        SELECT * FROM tbl_commutevehicle WHERE sysDeleted = 0 order by sysCreated DESC 
    """)
    fun getAllVehicle(): List<Commutevehicle>
}

@Service
class CommutevehicleService(mapper: CommutevehicleMapper) : BaseService<Commutevehicle, CommutevehicleMapper>(mapper) {
    // 获取上下班通勤班车列表
    override fun getList(page: BaseSearch): Result {
        val search = page as CommutevehicleSearch

        // 1.1 查询班车列表
        var vehicles = mapper.getList(search)

        // 1.2 查询班车关注状态
        val followVehicles = getBean(CommuteuserfollowvehicleMapper::class.java).getList(CommuteuserfollowvehicleSearch().apply {
            this.userId = search.userId
        }).map { it.carId }
        vehicles.forEach { vehicle -> vehicle.followed = vehicle.id in followVehicles }

        // 1.3 查询所有路线，过滤上下班车辆
        val routes = getBean(CommuterouteMapper::class.java).getAllRoute().associateBy { it.id }
        vehicles.forEach { vehicle ->
            vehicle.commuteRoute = routes.getOrDefault(vehicle.routeId, routes.values.first())
        }
        if (search.routeType != null)
            vehicles = vehicles.filter { it.commuteRoute?.routeType == search.routeType }

        // 1.4 添加行驶日志
        val tripGroup = getBean(CommutetripMapper::class.java).getTripListByCarIdList(
            vehicles.filter { it.status == CommuteVehicleStatus.ONLINE.value }.map { it.id }
        ).groupBy { it.carId }
        val routeStopGroup = getBean(CommuteroutestopMapper::class.java).getList(CommuteroutestopSearch()).groupBy { it.routeId }
        vehicles.forEach { vehicle ->
            vehicle.commuteTrip = tripGroup[vehicle.carId]?.first()
            vehicle.commuteTrip?.let { trip ->
                trip.currentStop = routeStopGroup[trip.routeId]?.find { it.sequence == trip.stopSequence }
                trip.nextStop = routeStopGroup[trip.routeId]?.find { it.sequence == trip.stopSequence + 1 }
                vehicle.commuteRoute?.stopList?.addAll(routeStopGroup[trip.routeId] ?: listOf())
            }
        }

        return Result.getSuccess(vehicles.sortedBy { if (it.id in followVehicles) 0 else 1 })
    }

    fun updateVehicleStatus(vehicle: Commutevehicle): Result {
        // 1.1 更新班车状态
        mapper.updateVehicleStatus(vehicle)

        // 1.2 记录班车发车记录
        if (vehicle.status == CommuteVehicleStatus.ONLINE.value) {
            // 1.2.1 获取路线信息
            val route = getBean(CommuterouteMapper::class.java).getInfo(vehicle.routeId)

            // 1.2.2 记录发车记录
            val commuteTrip = Commutetrip().apply {
                this.id = uuid()
                this.carId = vehicle.carId
                this.driverId = vehicle.driverId
                this.routeId = vehicle.routeId
                this.status = CommuteVehicleStatus.ONLINE.value
                this.startTime = localDateTimeFormatter.format(LocalDateTime.now())
                this.predictEndTime = localDateTimeFormatter.format(LocalDateTime.now().plusMinutes(route?.estimatedTime?.toLong() ?: 60L))
                this.estimatedTime = route?.estimatedTime ?: 60
                this.stopSequence = 0
            }
            getBean(CommutetripMapper::class.java).insert(commuteTrip)
        } else if (vehicle.status == CommuteVehicleStatus.OFFLINE.value) {
            getBean(CommutetripMapper::class.java).updateTripStatus(vehicle.carId, CommuteVehicleStatus.OFFLINE.value, localDateTimeFormatter.format(LocalDateTime.now()))
        }

        return Result.getSuccess(vehicle.id)
    }

    fun getVehicleDetail(search: CommutevehicleSearch): Result {
        val commuteRouteMapper = getBean(CommuterouteMapper::class.java)
        val commuteTripMapper = getBean(CommutetripMapper::class.java)
        val commuteRouteStopMapper = getBean(CommuteroutestopMapper::class.java)

        // 1.1 获取班车信息
        val vehicle = mapper.getList(search).firstOrNull() ?: return Result.getError("未找到班车信息")

        // 1.2 获取发车记录
        val commuteTrip = commuteTripMapper.getList(CommutetripSearch().apply {
            this.carId = vehicle.id
            this.status = CommuteVehicleStatus.ONLINE.value
        }).firstOrNull()

        // 1.3 获取路线信息
        commuteTrip?.let { trip ->
            val stopList = commuteRouteStopMapper.getList(CommuteroutestopSearch().apply { routeId = trip.routeId })
            vehicle.commuteTrip = trip.apply {
                currentStop = stopList.find { it.sequence == trip.stopSequence }
                nextStop = stopList.find { it.sequence == trip.stopSequence + 1 }
            }
            vehicle.commuteRoute = commuteRouteMapper.getInfo(trip.routeId)
            vehicle.commuteRoute?.stopList?.addAll(stopList)
        }

        return Result.getSuccess(vehicle)
    }

    fun getAll() = mapper.getAllVehicle()
}

@RestController
@RequestMapping("/api/commutevehicle")
class CommutevehicleResource(service: CommutevehicleService) : BaseResource<CommutevehicleSearch, Commutevehicle, CommutevehicleMapper, CommutevehicleService>(service) {
    @PostMapping("/updateVehicleStatus")
    fun updateVehicleStatus(@RequestBody vehicle: Commutevehicle): Result {
        return service.updateVehicleStatus(vehicle)
    }

    // 获取班车详细
    @GetMapping("/getVehicleDetail/{carId}")
    fun getVehicleDetail(@PathVariable carId: String): Result {
        return service.getVehicleDetail(CommutevehicleSearch().apply { this.carId = carId })
    }

    // 获取所有班车
    @GetMapping("/getAll")
    fun getAll(): Result {
        return Result.getSuccess(service.getAll())
    }

    // 获取当前班车下一站点
//    @GetMapping("/getVehicleNextStop/{carId}")
//    fun getVehicleNextStop(@PathVariable carId: String): Result {
//        return  Result.getSuccess("")
//    }
}
