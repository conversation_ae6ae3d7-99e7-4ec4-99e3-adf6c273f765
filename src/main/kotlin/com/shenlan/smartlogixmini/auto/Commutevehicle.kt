package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

class Commutevehicle: BaseModel() {
    var driverId: String = ""
    var routeId: String = ""
    var status: Int = 0
    var carId: String = ""
    var carPlate: String = ""
    var plateColor: String = ""
    var plateColorCode: String = ""
    var carName: String = ""
    var teamId: String = ""
    var teamName: String = ""
    var cmpName: String = ""
    var gps: Boolean = false
    var tmnKey: String = ""
    var dueDate: String = ""
    var onlineTime: String = ""
    var icon: String = ""
    var iconLink: String = ""
    var des: String = ""
    var bxts: String = ""
    var sysDeleted: Int = 0
}

class CommutevehicleSearch: BaseSearch() {
    var driverId: String = ""
    var routeId: String = ""
    var status: Int? = null
    var carPlate: String = ""
    var carName: String = ""
    var teamId: String = ""
    var teamName: String = ""
    var cmpName: String = ""
}

@Mapper
interface CommutevehicleMapper : BaseMapper<Commutevehicle> {
    @Select("""
        <script>
            SELECT * FROM tbl_commutvehicle
            <where>
                AND sysDeleted = 0
                <if test="driverId != ''">
                    AND driverId = #{driverId}
                </if>
                <if test="routeId != ''">
                    AND routeId = #{routeId}
                </if>
                <if test="status != null">
                    AND status = #{status}
                </if>
                <if test="carPlate != ''">
                    AND carPlate LIKE CONCAT('%', #{carPlate}, '%')
                </if>
                <if test="carName != ''">
                    AND carName LIKE CONCAT('%', #{carName}, '%')
                </if>
                <if test="teamId != ''">
                    AND teamId = #{teamId}
                </if>
                <if test="teamName != ''">
                    AND teamName LIKE CONCAT('%', #{teamName}, '%')
                </if>
                <if test="cmpName != ''">
                    AND cmpName LIKE CONCAT('%', #{cmpName}, '%')
                </if>
            </where>
            ORDER BY carPlate ASC, sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutevehicle>

    // 获取所有班车id
    @Select("""
        SELECT DISTINCT id FROM tbl_commutvehicle
    """)
    fun getAllCarId(): List<String>

    // 批量更新班车信息
    @Update("""
        <script>
            UPDATE tbl_commutvehicle
            <set>
                carPlate = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.carPlate}
                </foreach>
                END,
                plateColor = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.plateColor}
                </foreach>
                END,
                plateColorCode = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.plateColorCode}
                </foreach>
                END,
                carName = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.carName}
                </foreach>
                END,
                teamId = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.teamId}
                </foreach>
                END,
                teamName = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.teamName}
                </foreach>
                END,
                cmpName = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.cmpName}
                    </foreach>
                END,
                gps = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.gps}
                </foreach>
                END,
                tmnKey = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.tmnKey}
                </foreach>
                END,
                dueDate = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.dueDate}
                </foreach>
                END,
                onlineTime = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.onlineTime}
                </foreach>
                END,
                icon = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.icon}
                </foreach>
                END,
                iconLink = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.iconLink}
                </foreach>
                END,
                des = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.des}
                </foreach>
                END,
                bxts = CASE id
                <foreach collection="vehicles" item="vehicle" separator=",">
                    WHEN #{vehicle.id} THEN #{vehicle.bxts}
                </foreach>
                END
            </set>
            WHERE id IN
            <foreach collection="vehicles" item="vehicle" open="(" separator="," close=")">
                #{vehicle}
            </foreach>
        </script>
    """)
    fun batchUpdateVehicle(@Param("vehicles") list: List<Commutevehicle>)
}

@Service
class CommutevehicleService(mapper: CommutevehicleMapper) : BaseService<Commutevehicle, CommutevehicleMapper>(mapper)

@RestController
@RequestMapping("/api/commutevehicle")
class CommutevehicleResource(service: CommutevehicleService) : BaseResource<CommutevehicleSearch, Commutevehicle, CommutevehicleMapper, CommutevehicleService>(service)
