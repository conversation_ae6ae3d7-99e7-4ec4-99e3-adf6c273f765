package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.CommuteUtil
import com.shenlan.smartlogixmini.util.CommuteUtil.MockPosition
import com.shenlan.smartlogixmini.util.CommuteUtil.getDistance
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.localDateTimeFormatter
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Insert
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDateTime

class Commutevehicle: BaseEntity() {
    var driverId: String = ""
    var routeId: String = ""
    var status: Int = 0
    var carId: String = ""
    var carPlate: String = ""
    var plateColor: String = ""
    var plateColorCode: String = ""
    var carName: String = ""
    var teamId: String = ""
    var teamName: String = ""
    var cmpName: String = ""
    var gps: Boolean = false
    var tmnKey: String = ""
    var dueDate: String? = null
    var onlineTime: String? = null
    var icon: String = ""
    var iconLink: String = ""
    var des: String = ""
    var bxts: String = ""
    var sysDeleted: Int = 0

    var lat: Double = 0.0
    var lng: Double = 0.0
    var commuteTrip: Commutetrip? = null
    var commuteRoute: Commuteroute? = null

    // 关注状态
    var followed: Boolean = false

    // 模拟行驶
    var simulation: Boolean = false

    // 最近一次发车记录的发车类型
    val routeType: Int
        get() {
            return if (LocalDateTime.now().hour >= 12)
                1
            else
                0
        }

    // 是否发布
    var published: Boolean = false

    var organizationId: String = ""
}

data class SimpleCommuteVehicle(
    val id: String,
    val carName: String,
    val routeId: String,
    val lat: Double,
    val lng: Double
)

enum class CommuteVehicleStatus(val value: Int) {
    OFFLINE(0),
    ONLINE(1),
    // 停靠中
    STOP(2),
    MAINTENANCE(3)
}

data class CommuteConfig(
    val lastVehicle: Commutevehicle?,
    val lastRoute: Commutepublishedroute?,
    val vehicles: List<Commutevehicle>,
    val routes: List<Commutepublishedroute>
)

class CommutevehicleSearch: BaseSearch() {
    var carId: String = ""
    var driverId: String = ""
    var routeId: String = ""
    var status: Int? = null
    var carPlate: String = ""
    var carName: String = ""
    var teamId: String = ""
    var teamName: String = ""
    var cmpName: String = ""
    var routeType: Int? = null
    var userId: String = ""
    var organizationId: String = ""
}

@Mapper
interface CommutevehicleMapper : BaseMapper<Commutevehicle> {
    @Select("""
        SELECT cv.*, cvl.lat, cvl.lng FROM tbl_commutevehicle cv left join tbl_commutevehiclelocation cvl on cv.id = cvl.id
        WHERE cv.id = #{id}
    """)
    override fun getInfo(id: String): Commutevehicle?

    @Insert("""
        <script>
            <foreach collection="modelList" item="model" separator=";">
                INSERT INTO tbl_commutevehicle (id, driverId, routeId, status, carId, carPlate, plateColor, plateColorCode, carName, teamId, teamName, cmpName, gps, tmnKey, dueDate, onlineTime, icon, iconLink, des, bxts, sysDeleted, organizationId)
                VALUES (#{model.id}, #{model.driverId}, #{model.routeId}, #{model.status}, #{model.carId}, #{model.carPlate}, #{model.plateColor}, #{model.plateColorCode}, #{model.carName}, #{model.teamId}, #{model.teamName}, #{model.cmpName}, #{model.gps}, #{model.tmnKey}, #{model.dueDate}, #{model.onlineTime}, #{model.icon}, #{model.iconLink}, #{model.des}, #{model.bxts}, #{model.sysDeleted}, #{model.organizationId})
            </foreach>
        </script>
    """)
    override fun insertList(@Param("modelList") modelList: List<BaseEntity>): Int

    @Select("""
        <script>
            SELECT cv.*, cvl.lat, cvl.lng FROM tbl_commutevehicle cv left join tbl_commutevehiclelocation cvl on cv.id = cvl.id
            <where>
                AND cv.sysDeleted = 0
                <if test="carId != ''">
                    AND cv.id = #{carId}
                </if>
                <if test="driverId != ''">
                    AND cv.driverId = #{driverId}
                </if>
                <if test="routeId != ''">
                    AND cv.routeId = #{routeId}
                </if>
                <if test="status != null">
                    AND cv.status = #{status}
                </if>
                <if test="routeType != null">
                    AND cv.routeType = #{routeType}
                </if>
                <if test="carName != ''">
                    AND cv.carName LIKE CONCAT('%', #{carName}, '%')
                </if>
                <if test="organizationId != ''">
                    AND cv.organizationId = #{organizationId}
                </if>
            </where>
            ORDER BY cv.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Commutevehicle>

    // 获取所有班车id
    @Select("""
        SELECT DISTINCT id FROM tbl_commutevehicle
    """)
    fun getAllCarId(): List<String>

    @Select("""
        SELECT * FROM tbl_commutevehicle WHERE carName = #{carName}
    """)
    fun getInfoByCarName(carName: String): Commutevehicle?

    // 批量更新班车信息
    @Update("""
        <script>
            UPDATE tbl_commutevehicle
            <set>
                carPlate = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.carPlate}
                </foreach>
                ELSE carPlate
                END,
                plateColor = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.plateColor}
                </foreach>
                ELSE plateColor
                END,
                plateColorCode = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.plateColorCode}
                </foreach>
                ELSE plateColorCode
                END,
                carName = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.carName}
                </foreach>
                ELSE carName
                END,
                teamId = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.teamId}
                </foreach>
                ELSE teamId
                END,
                teamName = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.teamName}
                </foreach>
                ELSE teamName
                END,
                cmpName = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.cmpName}
                </foreach>
                ELSE cmpName
                END,
                gps = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.gps}
                </foreach>
                ELSE gps
                END,
                tmnKey = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.tmnKey}
                </foreach>
                ELSE tmnKey
                END,
                dueDate = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.dueDate}
                </foreach>
                ELSE dueDate
                END,
                onlineTime = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.onlineTime}
                </foreach>
                ELSE onlineTime
                END,
                icon = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.icon}
                </foreach>
                ELSE icon
                END,
                iconLink = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.iconLink}
                </foreach>
                ELSE iconLink
                END,
                des = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.des}
                </foreach>
                ELSE des
                END,
                bxts = CASE id
                <foreach collection="vehicles" item="vehicle" separator="">
                    WHEN #{vehicle.id} THEN #{vehicle.bxts}
                </foreach>
                ELSE bxts
                END
            </set>
            WHERE id IN
            <foreach collection="vehicles" item="vehicle" open="(" separator="," close=")">
                #{vehicle.id}
            </foreach>
        </script>
    """)
    fun batchUpdateVehicle(@Param("vehicles") list: List<Commutevehicle>)

    // 更新driverId、routeId、status
    @Update("""
        UPDATE tbl_commutevehicle
        SET driverId = #{driverId}, routeId = #{routeId}, status = #{status}, routeType = #{routeType}
        WHERE id = #{carId}
    """)
    fun updateVehicleStatus(vehicle: Commutevehicle)

    // 获取所有班车
    @Select("""
        <script>
            SELECT * FROM tbl_commutevehicle 
            <where>
                AND sysDeleted = 0
                <if test="organizationId != ''">
                    AND organizationId = #{organizationId}
                </if>
            </where>
            order by sysCreated DESC 
        </script>
    """)
    fun getAllVehicle(@Param("organizationId") organizationId: String): List<Commutevehicle>

    @Select("""
        SELECT DISTINCT carName FROM tbl_commutevehicle WHERE sysDeleted = 0
    """)
    fun getAllCarName(): List<String>

    // 获取所有班车，包括已删除的
    @Select("""
        SELECT * FROM tbl_commutevehicle order by sysCreated DESC 
    """)
    fun getAllVehicleWithDeleted(): List<Commutevehicle>

    @Update("""
        UPDATE tbl_commutevehicle
        SET sysDeleted = 1
        WHERE id = #{id}
    """)
    override fun deleteLogic(id: String): Int

    // 所有的sysDeleted置为1
    @Update("""
        UPDATE tbl_commutevehicle
        SET sysDeleted = 1
        WHERE sysDeleted = 0 and organizationId = #{organizationId}
    """)
    fun deleteLogicAll(organizationId: String)

    // 根据routeId列表查询
    @Select("""
        <script>
            SELECT cv.*, cvl.lat, cvl.lng FROM tbl_commutevehicle cv left join tbl_commutevehiclelocation cvl on cv.id = cvl.id
            WHERE cv.sysDeleted = 0
            <if test="list != null and list.size() > 0">
                AND cv.routeId IN
                <foreach item="item" index="" collection="list" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </script>
    """)
    fun getListByRouteIdList(@Param("list") list: List<String>): List<Commutevehicle>

    // 根据carName设置sysDelete状态和organizationId
    @Update("""
        UPDATE tbl_commutevehicle
        SET sysDeleted = #{sysDeleted}, organizationId = #{organizationId}
        WHERE id = #{id}
    """)
    fun updateDeleteStatusById(id: String, sysDeleted: Int, organizationId: String)

    // 根据carName更新routeId、organizationId
    @Update("""
        UPDATE tbl_commutevehicle
        SET routeId = #{routeId}, organizationId = #{organizationId}, sysDeleted = 0
        WHERE carName = #{carName}
    """)
    fun updateRouteId(carName: String, routeId: String, organizationId: String)
}

@Service
class CommutevehicleService(mapper: CommutevehicleMapper) : BaseService<Commutevehicle, CommutevehicleMapper, CommutevehicleSearch>(mapper) {
    override fun delete(id: String): Result {
        return super.deleteLogic(id)
    }

    override fun save(model: Commutevehicle): Result {
        if (model.id.isEmpty()) {
            model.id = uuid()
        }
        model.carId = model.id
        return super.save(model)
    }

    // 获取上下班通勤班车列表
    override fun getList(page: CommutevehicleSearch): Result {
        val search = page
        search.organizationId = getUser()?.branchOrganizationId ?: return Result.getError("无法获取当前用户所属中心，请联系管理员")

        // 1.1 查询班车列表
        val vehicles = mapper.getList(search)

        // 1.2 查询班车关注状态
//        val followVehicles = getBean(CommuteuserfollowvehicleMapper::class.java).getList(CommuteuserfollowvehicleSearch().apply {
//            this.userId = search.userId
//        }).map { it.carId }
//        vehicles.forEach { vehicle -> vehicle.followed = vehicle.id in followVehicles }

        // 1.3 查询所有路线，过滤上下班车辆
//        val routes = getBean(CommuterouteMapper::class.java).getPublishedRoute().associateBy { it.id }
//        vehicles.forEach { vehicle ->
//            vehicle.commuteRoute = routes.getOrDefault(vehicle.routeId, routes.values.elementAtOrNull(0))
//            vehicle.commuteRoute?.let {
//                if (vehicle.routeType == CommuteRouteType.HOME.value) {
//                    val name = it.startStopName
//                    it.startStopName = it.endStopName
//                    it.endStopName = name
//                }
//            }
//        }
//
//        // 1.4 添加行驶日志
//        val tripGroup = getBean(CommutetripMapper::class.java).getTripListByCarIdList(
//            vehicles.filter { it.status == CommuteVehicleStatus.ONLINE.value || it.status == CommuteVehicleStatus.STOP.value }.map { it.id }
//        ).groupBy { it.carId }
//        val routeStopGroup = getBean(CommuteroutestopMapper::class.java).getList(CommuteroutestopSearch()).groupBy { it.routeId }
//        vehicles.forEach { vehicle ->
//            vehicle.commuteTrip = tripGroup[vehicle.carId]?.elementAtOrNull(0)
//            vehicle.commuteTrip?.let { trip ->
//                routeStopGroup[trip.routeId]?.let { stopList ->
//                    val newStopList = if (vehicle.routeType == CommuteRouteType.HOME.value) {
//                        stopList.reversed()
//                    } else
//                        stopList
//                    newStopList.forEachIndexed { index, stop ->
//                        stop.sequence = index
//                    }
//
//                    trip.currentStop = newStopList.find { it.sequence == trip.stopSequence }
//                    trip.nextStop = newStopList.find { it.sequence == trip.stopSequence + 1 }
//                    vehicle.commuteRoute?.stopList?.addAll(newStopList)
//                }
//            }
//        }

        // 1.3 添加行驶日志
        CommuteUtil.getCommuteTrip(vehicles)

//        return Result.getSuccess(vehicles.sortedBy { if (it.id in followVehicles) 0 else 1 })
        return Result.getSuccess(vehicles)
    }

    fun updateVehicleStatus(vehicle: Commutevehicle): Result {
        // 1.1 更新班车状态
        mapper.updateVehicleStatus(vehicle)

        log.info("updateVehicleStatus: ${vehicle.toJsonString}")

        // 1.2 记录班车发车记录
        if (vehicle.status == CommuteVehicleStatus.ONLINE.value) {
            // 1.2.1 获取路线信息
            val route = getBean(CommutepublishedrouteMapper::class.java).getInfo(vehicle.routeId)

            // 1.2.2 记录发车记录
            val commuteTrip = Commutetrip().apply {
                this.id = uuid()
                this.carId = vehicle.carId
                this.driverId = vehicle.driverId
                this.routeId = vehicle.routeId
                this.status = CommuteVehicleStatus.ONLINE.value
                this.startTime = localDateTimeFormatter.format(LocalDateTime.now())
                this.predictEndTime = localDateTimeFormatter.format(LocalDateTime.now().plusMinutes(route?.estimatedTime?.toLong() ?: 60L))
                this.estimatedTime = route?.estimatedTime ?: 60
                this.stopSequence = 0
            }
            getBean(CommutetripMapper::class.java).updateTripStatusByCarId(vehicle.carId, CommuteVehicleStatus.OFFLINE.value)
            getBean(CommutetripMapper::class.java).insert(commuteTrip)

            if (vehicle.simulation) {
                log.info("start vehicle simulation")
                var stopList = getBean(CommutepublishedroutestopMapper::class.java).getList(CommuteroutestopSearch().apply { routeId = vehicle.routeId })
                if (vehicle.routeType == CommuteRouteType.HOME.value) {
                    stopList = stopList.reversed()
                }

                if (AppPro.env == "dev" || AppPro.env == "test") {
                    CommuteUtil.simulateBus(vehicle.carId, stopList.map {
                        MockPosition(it.lat, it.lng)
                    })
                } else {
                    CommuteUtil.initVehicleLocation(vehicle.carId, stopList.map {
                        MockPosition(it.lat, it.lng)
                    })
                }
            } else {
                // 需要找出班车当前位置
                val  commuteVehicleLocation = getBean(CommutevehiclelocationMapper::class.java).getInfo(vehicle.carId) ?: return Result.getError("无法定位班车位置")
                // 需要深拷贝
                var positionList = getBean(CommutepublishedroutestopMapper::class.java).getList(CommuteroutestopSearch().apply { routeId = vehicle.routeId }).map { Commutepublishedroutestop(it) }.sortedBy { it.sequence }
                if (vehicle.routeType == CommuteRouteType.HOME.value)
                    positionList = positionList.reversed()
                // 找出最近点的坐标
                val index = positionList.indexOf(positionList.minBy { getDistance(commuteVehicleLocation.lat, commuteVehicleLocation.lng, it.lat, it.lng) })
                val passStopCount = positionList.count { it.sequence < index && it.flag }
                log.info("${vehicle.carId} ${vehicle.carName} 最近点: $index, 已经过站数: $passStopCount")
                getBean(CommutetripMapper::class.java).updateStopSequence(commuteTrip.id, passStopCount)
            }
        } else if (vehicle.status == CommuteVehicleStatus.OFFLINE.value) {
            getBean(CommutetripMapper::class.java).updateTripStatus(vehicle.carId, CommuteVehicleStatus.OFFLINE.value, localDateTimeFormatter.format(LocalDateTime.now()))
        }

        return Result.getSuccess(vehicle.id)
    }

    fun getVehicleDetail(search: CommutevehicleSearch): Result {
        // 1.1 获取班车信息
        val vehicle = mapper.getList(search).firstOrNull() ?: return Result.getError("未找到班车信息")

//        // 1.2 获取发车记录
//        val commuteTrip = commuteTripMapper.getList(CommutetripSearch().apply {
//            this.carId = vehicle.id
////            this.status = CommuteVehicleStatus.ONLINE.value
//        }).firstOrNull { it.status == CommuteVehicleStatus.ONLINE.value || it.status == CommuteVehicleStatus.STOP.value }
//
//        // 1.3 获取路线信息
//        commuteTrip?.let { trip ->
//            var stopList = commuteRouteStopMapper.getList(CommuteroutestopSearch().apply { routeId = trip.routeId })
//            // 站点反转，序号重新赋值
//            if (vehicle.routeType == CommuteRouteType.HOME.value) {
//                stopList = stopList.reversed()
//                stopList.forEachIndexed { index, stop ->
//                    stop.sequence = index
//                }
//            }
//            vehicle.commuteTrip = trip.apply {
//                currentStop = stopList.find { it.sequence == trip.stopSequence }
//                nextStop = stopList.find { it.sequence == trip.stopSequence + 1 }
//            }
//            vehicle.commuteRoute = commuteRouteMapper.getInfo(trip.routeId)
//            vehicle.commuteRoute?.stopList?.addAll(stopList)
//            vehicle.commuteRoute?.let {
//                it.startStopName = it.stopList.first().name
//                it.endStopName = it.stopList.last().name
//            }
//        }

        // 1.2 获取发车记录
        CommuteUtil.getCommuteTrip(listOf(vehicle))

        return Result.getSuccess(vehicle)
    }

    fun getAll() = mapper.getAllVehicle(getUser()?.branchOrganizationId ?: "")

    fun getAllCarName(): List<String> {
        val carNameList = mapper.getAllCarName().sortedBy { it }
        // 查询已经绑定的班车
        val bindCarNameList = getBean(CommuterouteMapper::class.java).getList(CommuterouteSearch()).map { it.carName }.distinct()
        return carNameList.filter { it !in bindCarNameList }
    }

    fun getCommuteConfig(search: CommutevehicleSearch): Result {
        val organizationId = getUser()?.branchOrganizationId ?: return Result.getError("无法获取当前用户所属中心，请联系管理员")
        val vehicles = mapper.getAllVehicle(organizationId)
        val routes = getBean(CommutepublishedrouteService::class.java).getAll(search.routeType ?: 0, organizationId)
        val lastTrip = getBean(CommutetripMapper::class.java).getList(CommutetripSearch().apply { driverId = search.driverId }).maxBy { it.sysCreated?.time ?: 0 }
        val lastVehicle = vehicles.firstOrNull { it.id == (lastTrip?.carId ?: "") }
        val lastRoute = routes.firstOrNull { it.id == (lastTrip?.routeId ?: "") }

        // 判断路线是否有班车行驶
        vehicles.filter {
            it.status == CommuteVehicleStatus.ONLINE.value || it.status == CommuteVehicleStatus.STOP.value
        }.forEach { vehicle ->
            routes.find { it.id == vehicle.routeId }?.existVehicle = true
        }

        return Result.getSuccess(CommuteConfig(
            lastVehicle,
            lastRoute,
            vehicles.sortedBy { if (it.id == (lastTrip?.carId ?: "")) 0 else 1 },
            routes.sortedBy { if (it.id == (lastTrip?.routeId ?: "")) 0 else 1 }
        ))
    }
}

@RestController
@RequestMapping("/api/commutevehicle")
class CommutevehicleResource(service: CommutevehicleService) : BaseResource<CommutevehicleSearch, Commutevehicle, CommutevehicleMapper, CommutevehicleService>(service) {
    @PostMapping("/updateVehicleStatus")
    fun updateVehicleStatus(@RequestBody vehicle: Commutevehicle): Result {
        return service.updateVehicleStatus(vehicle)
    }

    // 获取班车详细
    @GetMapping("/getVehicleDetail/{carId}")
    fun getVehicleDetail(@PathVariable carId: String): Result {
        return service.getVehicleDetail(CommutevehicleSearch().apply { this.carId = carId })
    }

    // 获取所有班车
    @GetMapping("/getAll")
    fun getAll(): Result {
        return Result.getSuccess(service.getAll())
    }

    // 获取所有班车名称
    @GetMapping("/getAllName")
    fun getAllName(): Result {
        return Result.getSuccess(service.getAllCarName())
    }

    // 获取当前班车下一站点
//    @GetMapping("/getVehicleNextStop/{carId}")
//    fun getVehicleNextStop(@PathVariable carId: String): Result {
//        return  Result.getSuccess("")
//    }
    // 获取发车配置
    @PostMapping("/getCommuteConfig")
    fun getCommuteConfig(@RequestBody search: CommutevehicleSearch): Result {
        return service.getCommuteConfig(search)
    }
}
