package com.shenlan.smartlogixmini.auto

import com.fasterxml.jackson.annotation.JsonFormat
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.LocalTime


/**
 * 特殊时间段配置实体类
 */
class Specialclosedtime : BaseEntity() {
    var configId:String = ""
    @JsonFormat(pattern = "HH:mm")
    var specialStartTime: LocalTime=LocalTime.of(0, 0)
    @JsonFormat(pattern = "HH:mm")
    var specialEndTime: LocalTime=LocalTime.of(0, 0)
}

/**
 * 特殊时间段配置查询条件类
 */
class SpecialclosedtimeSearch: BaseSearch() {
    var configId:String = ""
}

/**
 * 特殊时间段配置Mapper接口
 */
@Mapper
interface SpecialclosedtimeMapper : BaseMapper<Specialclosedtime> {
    @Select("""
        <script>
            SELECT * FROM tbl_specialclosedtime
             <where>
                sysDeleted = 0
                <if test="configId != ''">
                    AND configId = #{configId}
                </if>
             </where> 
               ORDER BY specialStartTime ASC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Specialclosedtime>
    //根据configId删除对应的元素
    @Delete("DELETE FROM tbl_specialclosedtime WHERE configId=#{configId}")
    fun deleteListByConfigId(configId:String):Int
}

/**
 * 特殊时间段配置服务类
 */
@Service
class SpecialclosedtimeService(
    mapper: SpecialclosedtimeMapper
) : BaseService<Specialclosedtime, SpecialclosedtimeMapper, SpecialclosedtimeSearch>(mapper) {

}

/**
 * 特殊时间段配置控制器
 */
@RestController
@RequestMapping("/api/Specialclosedtime")
class SpecialclosedtimeResource(service: SpecialclosedtimeService) :
    BaseResource<SpecialclosedtimeSearch, Specialclosedtime, SpecialclosedtimeMapper, SpecialclosedtimeService>(service) {
}
