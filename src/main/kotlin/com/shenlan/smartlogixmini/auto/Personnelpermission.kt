package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.mybatis.PaginationInfo
import com.shenlan.smartlogixmini.util.uuid
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 人员权限关联实体类
 */
class Personnelpermission : BaseEntity() {
    // 数据库表字段

    /** 人员ID */
    var personnelId: String = ""
    /** 权限ID */
    var permissionId: String = ""

    // 关联字段

    /** 关联的人员信息 */
    var personnel: Personnel? = null
    /** 关联的权限信息 */
    var permission: Permission? = null
}

/**
 * 人员权限查询条件类
 */
class PersonnelpermissionSearch : BaseSearch() {
    /** 人员ID */
    var personnelId: String = ""
    /** 权限ID */
    var permissionId: String = ""
    /** 权限名称 */
    var permissionName: String = ""
    /** 是否加载人员信息 */
    var loadPersonnel: Boolean = false
    /** 是否加载权限信息 */
    var loadPermission: Boolean = false
}

/**
 * 人员权限Mapper接口
 */
@Mapper
interface PersonnelpermissionMapper : BaseMapper<Personnelpermission> {

    @Select("""
        <script>
            SELECT pp.* FROM tbl_personnelpermission pp
            <if test="permissionName != ''">
                INNER JOIN tbl_permission p ON pp.permissionId = p.id
            </if>
            <where>
                pp.sysDeleted = 0
                <if test="personnelId != ''">
                    AND pp.personnelId = #{personnelId}
                </if>
                <if test="permissionId != ''">
                    AND pp.permissionId = #{permissionId}
                </if>
                <if test="permissionName != ''">
                    AND p.name = #{permissionName}
                </if>
            </where>
            ORDER BY pp.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Personnelpermission>

    /**
     * 查询指定人员和权限的关联记录
     */
    @Select("""
        SELECT * FROM tbl_personnelpermission
        WHERE sysDeleted = 0 AND personnelId = #{personnelId} AND permissionId = #{permissionId}
        LIMIT 1
    """)
    fun getInfoByPersonnelAndPermission(personnelId: String, permissionId: String): Personnelpermission?

    /**
     * 删除指定人员的所有权限关联记录
     */
    @Delete("""
        DELETE FROM tbl_personnelpermission
        WHERE personnelId = #{personnelId}
    """)
    fun deleteByPersonnelId(personnelId: String): Int
}

/**
 * 人员权限Service类
 */
@Service
class PersonnelpermissionService(
    mapper: PersonnelpermissionMapper,
    private val personnelMapper: PersonnelMapper,
    private val permissionMapper: PermissionMapper
) : BaseService<Personnelpermission, PersonnelpermissionMapper, PersonnelpermissionSearch>(mapper) {

    /**
     * 重写save方法，确保人员权限关联记录不重复
     */
    @Transactional
    override fun saveEntity(entity: Personnelpermission): String {
        // 检查人员ID不能为空
        if (entity.personnelId.isEmpty()) {
            throw BusinessException("人员ID不能为空")
        }

        // 检查权限ID不能为空
        if (entity.permissionId.isEmpty()) {
            throw BusinessException("权限ID不能为空")
        }

        // 检查人员是否存在
        val personnel = personnelMapper.getInfo(entity.personnelId)
        if (personnel == null) {
            throw BusinessException("指定的人员不存在")
        }

        // 检查权限是否存在
        val permission = permissionMapper.getInfo(entity.permissionId)
        if (permission == null) {
            throw BusinessException("指定的权限不存在")
        }

        // 查询是否存在相同的人员权限关联记录（除了当前编辑的记录）
        val existing = mapper.getInfoByPersonnelAndPermission(entity.personnelId, entity.permissionId)
        if (existing != null && existing.id != entity.id) {
            throw BusinessException("该人员已拥有此权限")
        }

        // 验证通过，调用父类的save方法完成保存
        return super.saveEntity(entity)
    }

    /**
     * 重写getList方法，根据需要加载关联信息
     */
    override fun getEntityPage(search: PersonnelpermissionSearch): PaginationInfo<Personnelpermission> {
        val paginationInfo = super.getEntityPage(search)
        val personnelpermissionList = paginationInfo.result

        // 如果需要加载人员信息
        if (search.loadPersonnel) {
            personnelpermissionList.forEach { loadPersonnel(it) }
        }

        // 如果需要加载权限信息
        if (search.loadPermission) {
            personnelpermissionList.forEach { loadPermission(it) }
        }
        return paginationInfo
    }

    /**
     * 重写方法，加载关联信息
     */
    override fun getEntity(id: String): Personnelpermission? {
        val personnelpermission = mapper.getInfo(id)
        if (personnelpermission != null) {
            // 加载人员信息
            loadPersonnel(personnelpermission)
            // 加载权限信息
            loadPermission(personnelpermission)
        }
        return personnelpermission
    }

    /**
     * 加载人员信息
     */
    private fun loadPersonnel(personnelpermission: Personnelpermission) {
        if (personnelpermission.personnelId.isNotEmpty()) {
            personnelpermission.personnel = personnelMapper.getInfo(personnelpermission.personnelId)
        }
    }

    /**
     * 加载权限信息
     */
    private fun loadPermission(personnelpermission: Personnelpermission) {
        if (personnelpermission.permissionId.isNotEmpty()) {
            personnelpermission.permission = permissionMapper.getInfo(personnelpermission.permissionId)
        }
    }

    /**
     * 设置人员权限列表（先删除原有权限，再添加新权限）
     */
    @Transactional
    fun setPermissions(personnelId: String, permissionNameList: List<PermissionName>): Result {
        // 检查人员是否存在
        val personnel = personnelMapper.getInfo(personnelId)
        if (personnel == null) {
            return Result.getError("指定的人员不存在")
        }

        // 先删除该人员的所有原有权限
        mapper.deleteByPersonnelId(personnelId)

        // 如果权限名称列表不为空，则添加新权限
        if (permissionNameList.isNotEmpty()) {
            // 创建新的权限关联记录列表
            val personnelpermissionList = mutableListOf<BaseEntity>()
            permissionNameList.forEach { permissionName ->
                val permission = permissionMapper.getInfoByName(permissionName)
                if (permission != null) {
                    val personnelpermission = Personnelpermission()
                    personnelpermission.id = uuid()
                    personnelpermission.personnelId = personnelId
                    personnelpermission.permissionId = permission.id
                    personnelpermissionList.add(personnelpermission)
                }
            }

            // 使用父类的insertList方法批量插入
            if (personnelpermissionList.isNotEmpty()) {
                mapper.insertList(personnelpermissionList)
            }
        }

        return Result.getSuccessInfo("权限设置成功")
    }
}

/**
 * 人员权限Controller类
 */
@RestController
@RequestMapping("/api/Personnelpermission")
class PersonnelpermissionResource(service: PersonnelpermissionService) : BaseResource<PersonnelpermissionSearch, Personnelpermission, PersonnelpermissionMapper, PersonnelpermissionService>(service)
