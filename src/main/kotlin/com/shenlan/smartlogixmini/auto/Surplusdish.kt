package com.shenlan.smartlogixmini.auto

import com.shenlan.smartlogixmini.config.AppPro
import com.shenlan.smartlogixmini.util.ImageUtil.updateImageDishId
import com.shenlan.smartlogixmini.util.NotifyMessageUtil.saveNotifyMessage
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.getUser
import com.shenlan.smartlogixmini.util.localDateFormatter
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDate
import java.util.*
import kotlin.math.max

class Surplusdish: BaseEntity() {
    var name: String = ""
    var price: Double = 0.0
    var imageId: String = ""
    var organizationId: String = ""
    var surplusNum: Int = 0
    var status: String = "0"
    val statusDesc: String
        get() {
            return when (status) {
                "0" -> "销售中"
                "1" -> "已售罄"
                "2" -> "已停售"
                else -> "未知"
            }
        }
    var menuDate: String = ""
    var orderQuantity: Int = 0
    var sysDeleted: Int = 0
    var published: Boolean = false

    var imageUrl: String = ""
        get() = if (field.isEmpty()) field else AppPro.imgPrefix + field
    var images: MutableList<Dishimage> = mutableListOf()

    // 剩余数目
    val remainingNum: Int
        get() = max(0, surplusNum - orderQuantity)
}

class SurplusdishSearch: BaseSearch() {
    var name: String = ""
    var organizationId: String = ""
    var status: Int? = null
    var menuDate: String = ""
    var published: Boolean? = null
}

/**
 * 盈余菜品状态
 * 0：销售中 1：已售罄 2：已停售
 */
enum class SurplusDishStatus(val value: Int) {
    SALE(0),
    SOLD_OUT(1),
    STOP_SALE(2)
}

@Mapper
interface SurplusdishMapper : BaseMapper<Surplusdish> {
    @Select("""
        <script>
            SELECT sd.*, di.imageUrl FROM tbl_surplusdish sd left join tbl_dishimage di on sd.imageId = di.id
            <where>
                AND sd.sysDeleted = 0
                <if test="name != ''">
                    AND sd.name LIKE CONCAT('%', #{name}, '%')
                </if>
                <if test="organizationId != ''">
                    AND sd.organizationId = #{organizationId}
                </if>
                <if test="status != null">
                    AND sd.status = #{status}
                </if>
                <if test="menuDate != ''">
                    AND sd.menuDate = #{menuDate}
                </if>
                <if test="published != null">
                    AND sd.published = #{published}
                </if>
            </where>
            ORDER BY sd.sysCreated DESC
        </script>
    """)
    override fun getList(search: BaseSearch): List<Surplusdish>

    @Update("""
        UPDATE tbl_surplusdish
        SET sysDeleted = 1
        WHERE id = #{id}
    """)
    override fun deleteLogic(id: String): Int

    // 更新菜品状态
    @Update("""
        UPDATE tbl_surplusdish
        SET status = #{status}
        WHERE id = #{id}
    """)
    fun updateStatus(id: String, status: String)

    // 根据id列表查询
    @Select("""
        <script>
            SELECT sd.*, di.imageUrl FROM tbl_surplusdish sd left join tbl_dishimage di on sd.imageId = di.id
            WHERE sd.id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </script>
    """)
    fun getListByIdList(@Param("list") list: List<String>): List<Surplusdish>

    @Update("""
        UPDATE tbl_surplusdish
        SET published = #{published}
        WHERE menuDate = #{menuDate} AND organizationId = #{organizationId}
    """)
    fun updatePublishedByDate(menuDate: String, organizationId: String, published: Boolean)

    @Update("""
        <script>
            UPDATE tbl_surplusdish
            SET orderQuantity = CASE id
            <foreach collection="dishes" item="dish">
                WHEN #{dish.id} THEN #{dish.orderQuantity}
            </foreach>
            END
            WHERE id IN
            <foreach collection="dishes" item="dish" open="(" separator="," close=")">
                #{dish.id}
            </foreach>
        </script>
    """)
    fun batchUpdateSurplusDish(@Param("dishes") list: List<Surplusdish>)
}

@Service
class SurplusdishService(mapper: SurplusdishMapper) : BaseService<Surplusdish, SurplusdishMapper, SurplusdishSearch>(mapper) {
    override fun save(model: Surplusdish): Result {
        val organizationId = getUser()?.branchOrganizationId ?: return Result.getError("无法获取当前用户所属中心，请联系管理员")
        model.organizationId = organizationId

        val sameDish = mapper.getList(SurplusdishSearch().apply {
            name = model.name
            this.organizationId = organizationId
            menuDate = model.menuDate
        }).filter { it.id != model.id }
        require(sameDish.isEmpty()) { return Result.getError("当天已存在同名盈余菜品") }

        super.save(model)
        getBean(DishimageMapper::class.java).clearDishId(model.id)
        updateImageDishId(model.imageId, model.id)
        model.images.forEach { image ->
            updateImageDishId(image.id, model.id)
        }
        return Result.getSuccess(model.id)
    }

    override fun getInfo(id: String): Result {
        val dish = super.getInfo(id).datas as Surplusdish?
        dish?.let {
            it.images.addAll(getBean(DishimageMapper::class.java).getList(DishimageSearch().apply { dishId = it.id }).map { image ->
                image.imageUrl = AppPro.imgPrefix + image.imageUrl
                image
            })
        }
        return Result.getSuccess(dish)
    }

    override fun delete(id: String): Result {
        return super.deleteLogic(id)
    }

    override fun getList(page: SurplusdishSearch): Result {
        page.organizationId = getUser()?.branchOrganizationId ?: return Result.getError("无法获取当前用户所属中心，请联系管理员")
        return super.getList(page)
    }

    // 停止售卖
    fun stopSale(id: String): Result {
        mapper.updateStatus(id, SurplusDishStatus.STOP_SALE.value.toString())
        return Result.getSuccess(id)
    }

    // 职工端获取盈余菜品列表
    fun getSurplusDishListForStaff(page: SurplusdishSearch): Result {
        page.organizationId = getUser()?.branchOrganizationId ?: return Result.getError("无法获取当前用户所属中心，请联系管理员")
        page.published = true
        page.status = SurplusDishStatus.SALE.value
        page.ifPage = false
        val list = mapper.getList(page)
        return Result.getSuccess(list.filter { it.remainingNum > 0 })
    }

    // 管理端获取盈余菜品列表
    fun getSurplusDishListForAdmin(page: SurplusdishSearch): Result {
        page.organizationId = getUser()?.branchOrganizationId ?: return Result.getError("无法获取当前用户所属中心，请联系管理员")
        page.ifPage = false
        val list = mapper.getList(page)
        return Result.getSuccess(mapOf(
            "published" to list.all { it.published },
            "list" to list
        ))
    }

    // 发布当天所有盈余菜品
    fun publishSurplusDish(menuDate: String): Result {
        val organizationId = getUser()?.branchOrganizationId ?: return Result.getError("无法获取当前用户所属中心，请联系管理员")

        mapper.updatePublishedByDate(menuDate, organizationId, true)

        // 保存通知信息
        val personnelSearch = PersonnelSearch()
        personnelSearch.ifPage = false
        personnelSearch.permissionNameList = listOf(PermissionName.STAFF_PERMISSION)
        personnelSearch.branchOrganizationId = organizationId
        val users = getBean(PersonnelMapper::class.java).getList(personnelSearch)

        saveNotifyMessage(users.map { it.id },
            NotifyModule.SURPLUS.value,
            "normal",
            "盈余菜品通知",
            "今天食堂还有盈余菜品，快来订购吧！",
            menuDate,
            organizationId
        )

        return Result.getSuccess("发布成功")
    }
}

@RestController
@RequestMapping("/api/surplusdish")
class SurplusdishResource(service: SurplusdishService) : BaseResource<SurplusdishSearch, Surplusdish, SurplusdishMapper, SurplusdishService>(service) {
    @GetMapping("/stopSale/{id}")
    fun stopSale(@PathVariable id: String): Result {
        return service.stopSale(id)
    }

    @GetMapping("/getSurplusDishListForStaff/{menuDate}")
    fun getSurplusDishListForStaff(@PathVariable menuDate: String): Result {
        return service.getSurplusDishListForStaff(SurplusdishSearch().apply { this.menuDate = menuDate })
    }

    @GetMapping("/getSurplusDishListForAdmin/{menuDate}")
    fun getSurplusDishListForAdmin(@PathVariable menuDate: String): Result {
        return service.getSurplusDishListForAdmin(SurplusdishSearch().apply { this.menuDate = menuDate })
    }

    @GetMapping("/publish")
    fun publish(): Result {
        return service.publishSurplusDish(localDateFormatter.format(LocalDate.now()))
    }
}
