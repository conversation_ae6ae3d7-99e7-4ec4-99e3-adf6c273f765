package com.shenlan.smartlogixmini.auto

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import com.shenlan.smartlogixmini.util.customObjectMapper
import com.shenlan.smartlogixmini.util.log
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.*
import java.security.MessageDigest
import java.util.concurrent.ConcurrentHashMap
import javax.annotation.PostConstruct
import javax.servlet.http.HttpServletRequest

/** 消息类型枚举 */
enum class MsgType(@JsonValue val value: String) {
    EVENT("event"); // 事件消息

    companion object {
        @JsonCreator
        @JvmStatic
        fun fromValue(value: String) = MsgType.values().find { it.value == value }
    }
}

/** 事件类型枚举 */
enum class EventType(@JsonValue val value: String) {
    WXA_MEDIA_CHECK("wxa_media_check"); // 媒体内容安全异步审查结果

    companion object {
        @JsonCreator
        @JvmStatic
        fun fromValue(value: String) = EventType.values().find { it.value == value }
    }
}

/**
 * 微信推送消息基础数据类
 */
data class WechatPushMessage(
    @JsonProperty("MsgType")
    val msgType: MsgType?,
    @JsonProperty("Event")
    val event: EventType?,
    @JsonIgnore
    val rawJsonString: String = ""
)

/**
 * 微信推送消息处理器接口
 * 实现该接口的类会被Spring容器自动发现并注册为事件处理器
 */
interface WechatPushMessageHandler {
    /** 处理消息 */
    fun handle(message: WechatPushMessage)

    /** 获取事件类型 */
    fun getEventType(): EventType
}

/**
 * 微信推送消息服务
 * 负责处理微信服务器的推送消息，包括URL验证、消息接收等功能（明文模式）
 * 官方文档：https://developers.weixin.qq.com/miniprogram/dev/framework/server-ability/message-push.html
 */
@Service
class WechatPushMessageService(
    private val messageHandlers: List<WechatPushMessageHandler>
) {

    // 存储不同事件类型的处理器
    private val eventHandlers = ConcurrentHashMap<EventType, (WechatPushMessage) -> Unit>()

    /**
     * 验证服务器URL（处理微信服务器的GET请求）
     *
     * @param signature 微信服务器传递的签名
     * @param token 配置的Token
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param echostr 随机字符串
     * @return 验证成功返回echostr，失败返回空字符串
     */
    fun verifyUrl(signature: String, token: String, timestamp: String, nonce: String, echostr: String): String {
        try {
            log.info("Verifying URL with params: timestamp=$timestamp, nonce=$nonce")

            // 验证签名
            if (verifySignature(signature, token, timestamp, nonce)) {
                log.info("URL verification successful")
                return echostr
            } else {
                log.error("URL verification failed: invalid signature")
                return ""
            }
        } catch (e: Exception) {
            log.error("Error during URL verification", e)
            return ""
        }
    }

    /**
     * 处理推送消息（处理微信服务器的POST请求）
     *
     * @param signature 微信服务器传递的签名
     * @param token 配置的Token
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param messageBody 消息体内容
     * @return 处理结果，通常返回"success"或空字符串
     */
    fun handlePushMessage(
        signature: String,
        token: String,
        timestamp: String,
        nonce: String,
        messageBody: String
    ): String {
        try {
            // 验证签名
            if (!verifySignature(signature, token, timestamp, nonce)) {
                log.error("Message push signature verification failed")
                return ""
            }

            // 明文模式，直接解析消息
            val message = customObjectMapper.readValue(messageBody, WechatPushMessage::class.java).copy(rawJsonString = messageBody)

            log.info("Received message: type=${message.msgType}, event=${message.event}")

            // 处理消息
            processMessage(message)

            return "success"
        } catch (e: Exception) {
            log.error("Error handling message push", e)
            return ""
        }
    }

    /**
     * 初始化事件处理器
     * 自动注册Spring容器中的所有WechatMessageHandler实现
     */
    @PostConstruct
    fun initEventHandlers() {
        // 自动注册Spring容器中的所有WechatMessageHandler实现
        messageHandlers.forEach { handler ->
            eventHandlers[handler.getEventType()] = { message -> handler.handle(message) }
            log.info("Auto-registered handler for event: ${handler.getEventType()}")
        }
    }

    /**
     * 验证签名
     *
     * @param signature 微信服务器传递的签名
     * @param token 配置的Token
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @return 验证是否通过
     */
    private fun verifySignature(signature: String, token: String, timestamp: String, nonce: String): Boolean {
        // 将token、timestamp、nonce三个参数进行字典序排序
        val params = listOf(token, timestamp, nonce).sorted()

        // 将三个参数字符串拼接成一个字符串
        val str = params.joinToString("")

        // 进行sha1加密
        val calculatedSignature = sha1(str)

        log.debug("Calculated signature: $calculatedSignature, received signature: $signature")

        // 开发者获得加密后的字符串可与signature对比
        return calculatedSignature == signature
    }

    /**
     * SHA1加密
     *
     * @param str 要加密的字符串
     * @return 加密后的字符串
     */
    private fun sha1(str: String): String {
        val md = MessageDigest.getInstance("SHA-1")
        val digest = md.digest(str.toByteArray())
        return digest.joinToString("") { "%02x".format(it) }
    }

    /**
     * 处理消息
     *
     * @param message 微信推送的消息
     */
    private fun processMessage(message: WechatPushMessage) {
        when (message.msgType) {
            MsgType.EVENT -> {
                // 处理事件消息
                message.event?.let { event ->
                    val handler = eventHandlers[event]
                    if (handler != null) {
                        try {
                            handler(message)
                            log.info("Successfully handled event: $event")
                        } catch (e: Exception) {
                            log.error("Error handling event: $event", e)
                        }
                    } else {
                        log.warn("No handler registered for event: $event")
                    }
                }
            }
            else -> {
                log.info("Received message of type: ${message.msgType}")
            }
        }
    }
}

/**
 * 微信推送消息资源控制器
 */
@RestController
@RequestMapping("/api/WechatPushMessage")
class WechatPushMessageResource(
    private val service: WechatPushMessageService,
    @Value("\${wechat.message-push.token:}") private val token: String
) {

    /**
     * 微信推送消息webhook端点 - GET请求用于URL验证
     */
    @GetMapping("/handle")
    fun verifyUrl(
        @RequestParam("signature") signature: String,
        @RequestParam("timestamp") timestamp: String,
        @RequestParam("nonce") nonce: String,
        @RequestParam("echostr") echostr: String
    ): String {
        log.info("Received URL verification request")

        return service.verifyUrl(signature, token, timestamp, nonce, echostr)
    }

    /**
     * 微信推送消息webhook端点 - POST请求用于接收推送消息
     */
    @PostMapping("/handle")
    fun handle(
        @RequestParam("signature") signature: String,
        @RequestParam("timestamp") timestamp: String,
        @RequestParam("nonce") nonce: String,
        @RequestBody messageBody: String,
        request: HttpServletRequest
    ): String {
        log.info("Received message push request")

        return service.handlePushMessage(
            signature,
            token,
            timestamp,
            nonce,
            messageBody
        )
    }
}
