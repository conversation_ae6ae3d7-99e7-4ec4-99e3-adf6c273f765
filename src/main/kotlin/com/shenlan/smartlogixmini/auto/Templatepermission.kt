package com.shenlan.smartlogixmini.auto

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 消息模板权限关联实体类
 */
class Templatepermission : BaseEntity() {
    // 数据库表字段

    /** 消息模板ID */
    var templateId: String = ""
    /** 权限ID */
    var permissionId: String = ""
}

/**
 * 消息模板权限关联查询条件类
 */
class TemplatepermissionSearch : BaseSearch() {
    /** 消息模板ID */
    var templateId: String = ""
    /** 权限ID */
    var permissionId: String = ""
}

/**
 * 消息模板权限关联Mapper接口
 */
@Mapper
interface TemplatepermissionMapper : BaseMapper<Templatepermission> {

    @Select("""
        <script>
            SELECT * FROM tbl_templatepermission
            <where>
                <if test="templateId != ''">AND templateId = #{templateId}</if>
                <if test="permissionId != ''">AND permissionId = #{permissionId}</if>
            </where>
        </script>
    """)
    override fun getList(search: BaseSearch): List<Templatepermission>
}

/**
 * 消息模板权限关联Service类
 */
@Service
class TemplatepermissionService(
    mapper: TemplatepermissionMapper
) : BaseService<Templatepermission, TemplatepermissionMapper, TemplatepermissionSearch>(mapper)

/**
 * 消息模板权限关联Controller类
 */
@RestController
@RequestMapping("/api/Templatepermission")
class TemplatepermissionResource(
    service: TemplatepermissionService
) : BaseResource<TemplatepermissionSearch, Templatepermission, TemplatepermissionMapper, TemplatepermissionService>(service)
