package com.shenlan.smartlogixmini.task

import com.shenlan.smartlogixmini.auto.MealTime
import com.shenlan.smartlogixmini.auto.Menu
import com.shenlan.smartlogixmini.auto.MenuMapper
import com.shenlan.smartlogixmini.auto.MenuSearch
import com.shenlan.smartlogixmini.auto.NotifyModule
import com.shenlan.smartlogixmini.auto.PermissionName
import com.shenlan.smartlogixmini.auto.PersonnelMapper
import com.shenlan.smartlogixmini.auto.PersonnelSearch
import com.shenlan.smartlogixmini.auto.Takeoutmenu
import com.shenlan.smartlogixmini.auto.TakeoutmenuMapper
import com.shenlan.smartlogixmini.auto.OrganizationMapper
import com.shenlan.smartlogixmini.auto.OrganizationSearch
import com.shenlan.smartlogixmini.auto.TakeoutmenuSearch
import com.shenlan.smartlogixmini.auto.TakeoutmenuService
import com.shenlan.smartlogixmini.auto.TakeoutmenudishMapper
import com.shenlan.smartlogixmini.util.MenuUtil
import com.shenlan.smartlogixmini.util.NotifyMessageUtil
import com.shenlan.smartlogixmini.util.NotifyMessageUtil.saveNotifyMessage
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.localDateFormatter
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.system.measureTimeMillis

@Component
class MenuTask(
    private val menuMapper: MenuMapper,
    private val takeoutMenuMapper: TakeoutmenuMapper,
    private val organizationMapper: OrganizationMapper
) {
    @EventListener(ApplicationReadyEvent::class)
    fun runOnStartup() {
        if (LocalDateTime.now().hour >= 17)
            clearTakeoutNotify()
        generateNextWeekMenu()
        generateTakeoutMenu()
    }

    /**
     * 每周五8点执行一次，生成下周的菜单
     */
    @Scheduled(cron = "0 0 8 ? * 5")
    fun generateNextWeekMenu() {

        val time = measureTimeMillis {
            val menuList = menuMapper.getList(MenuSearch())
            val mainOrganizationId = organizationMapper.getList(OrganizationSearch().apply {
                nameLike = "天津海事局"
            }).elementAtOrNull(0)?.id ?: "646cc4b3bc1042eaadf4cc54a0fe4caa"

            organizationMapper.getList(OrganizationSearch().apply {
                pid = mainOrganizationId
            }).forEach { organization ->
                // 生成下周菜单的逻辑
                val menuDateSet = mutableSetOf<String>()

                try {
                    // 1.1 生成下周菜单
                    MenuUtil.getMenuDateList().forEach { menuDate ->
//                        if (menuMapper.checkMenuDateExists(menuDate, MealTime.MORNING.toString(), organization.id) == 0) {
                        if (menuList.find { it.menuDate == menuDate && it.timePeriod == MealTime.MORNING.toString() && it.organizationId == organization.id } == null) {
                            // 生成早市菜单
                            menuMapper.insert(Menu(menuDate, MealTime.MORNING.toString(), organization.id))
                            menuDateSet.add(menuDate)
                        }
//                        if (menuMapper.checkMenuDateExists(menuDate, MealTime.AFTERNOON.toString(), organization.id) == 0) {
                        if (menuList.find { it.menuDate == menuDate && it.timePeriod == MealTime.AFTERNOON.toString() && it.organizationId == organization.id } == null) {
                            // 生成午市菜单
                            menuMapper.insert(Menu(menuDate, MealTime.AFTERNOON.toString(), organization.id))
                            menuDateSet.add(menuDate)
                        }
                    }

                    // 1.2 生成菜单通知信息
                    if (menuDateSet.isNotEmpty()) {
                        // 1.2.1 获取所有食堂管理权限的人员
                        val search = PersonnelSearch()
                        search.permissionNameList = listOf(PermissionName.MENU_MANAGEMENT)
                        search.ifPage = false
                        search.branchOrganizationId = organization.id
                        val users = getBean(PersonnelMapper::class.java).getList(search)

                        // 1.2.2 保存通知信息
                        saveNotifyMessage(users.map { it.id },
                            NotifyModule.MENU.value,
                            "management",
                            "下周菜单开放通知",
                            "下周的日期已开放，可前往配置菜谱啦~",
                            menuDateSet.joinToString(","),
                            organization.id
                        )
                    }

                    // 1.3 历史菜谱发布状态设置为1
                    menuMapper.updatePublishedByDate(MenuUtil.getLatestMenuSunday().minusWeeks(1).format(localDateFormatter), true, organization.id)
                } catch (e: Exception) {
                    log.error("Failed to generate next week's menu: ${e.message}")
                }
            }
        }

        log.info("Generated menus for next week, cost $time ms")
    }

    /**
     * 外卖菜谱生成，当天8点生成后两天的菜谱
     */
    @Scheduled(cron = "0 0 8 ? * *")
    fun generateTakeoutMenu() {
        // 生成外卖菜单的逻辑
        val localDate = LocalDate.now()

        val time = measureTimeMillis {
            val takeMenuList = takeoutMenuMapper.getList(TakeoutmenuSearch())
            val mainOrganizationId = organizationMapper.getList(OrganizationSearch().apply {
                nameLike = "天津海事局"
            }).elementAtOrNull(0)?.id ?: "646cc4b3bc1042eaadf4cc54a0fe4caa"

            organizationMapper.getList(OrganizationSearch().apply {
                pid = mainOrganizationId
            }).forEach { organization ->
                val menuDateList = mutableListOf<String>()

                try {
                    // 1.1 生成后天的菜单
                    val menuDate = localDateFormatter.format(localDate.plusDays(2))
                    // 1.1.1 生成菜单的逻辑
//                    if (takeoutMenuMapper.checkMenuDateExists(menuDate, organization.id) == 0) {
                    if (takeMenuList.find { it.menuDate == menuDate && it.organizationId == organization.id } == null) {
                        // 生成外卖菜单
                        takeoutMenuMapper.insert(Takeoutmenu(menuDate, organization.id))
                        menuDateList.add(menuDate)
                    }

                    // 1.2 生成外卖管理通知信息
                    if (menuDateList.isNotEmpty()) {
                        // 1.2.1 获取所有外卖管理权限的人员
                        val search = PersonnelSearch()
                        search.permissionNameList = listOf(PermissionName.TAKEOUT_MANAGEMENT)
                        search.ifPage = false
                        search.branchOrganizationId = organization.id
                        val users = getBean(PersonnelMapper::class.java).getList(search)

                        saveNotifyMessage(users.map { it.id },
                            NotifyModule.TAKEOUT.value,
                            "management",
                            "外卖管理开放通知",
                            "后天的日期已经开放，可前往配置订餐菜谱啦~",
                            menuDateList.joinToString(","),
                            organization.id
                        )
                    }

                    // 1.3 更新历史订单发布状态，将状态置为已发布
//                takeoutMenuMapper.updatePublishedByDate(localDateFormatter.format(localDate), true)
                    takeoutMenuMapper.updatePublishedByDate(localDateFormatter.format(localDate.plusDays(1)), true)

                    // 1.4 明天有新菜品时职工端发送通知
                    val publishedDishIds = getBean(TakeoutmenudishMapper::class.java).getAllDishId(localDateFormatter.format(localDate.plusDays(1))).toSet()
                    val takeoutMenu = getBean(TakeoutmenuService::class.java).getMenuByDate(localDateFormatter.format(localDate.plusDays(1)), "").datas
                        ?: return
                    if (takeoutMenu is Takeoutmenu && takeoutMenu.takeoutMenuDishList.any { it.dishId !in publishedDishIds } && LocalDateTime.now().hour < 17) {
                        log.info("new dishes: ${takeoutMenu.takeoutMenuDishList.filter { it.dishId !in publishedDishIds}.toJsonString}")
                        // 保存通知信息
                        val search = PersonnelSearch()
                        search.ifPage = false
                        search.permissionNameList = listOf(PermissionName.STAFF_PERMISSION)
                        search.branchOrganizationId = organization.id

                        val users = getBean(PersonnelMapper::class.java).getList(search)
                        saveNotifyMessage(users.map { it.id },
                            NotifyModule.TAKEOUT.value,
                            "normal",
                            "外卖订餐通知",
                            "明天的外卖面食上新啦，快来订餐吧！",
                            localDateFormatter.format(localDate.plusDays(1)),
                            organization.id
                        )
                    }
                } catch (e: Exception) {
                    log.error("Failed to generate takeout menu: ${e.message}")
                }
            }

        }

        log.info("Generated takeout menus, cost $time ms")
    }

    /**
     * 每天下午5点执行，清空职工端外卖通知
     */
    @Scheduled(cron = "0 0 17 ? * *")
    fun clearTakeoutNotify() {
        NotifyMessageUtil.deleteHistoryMessages(NotifyModule.TAKEOUT.value, "normal", "")
    }
}
