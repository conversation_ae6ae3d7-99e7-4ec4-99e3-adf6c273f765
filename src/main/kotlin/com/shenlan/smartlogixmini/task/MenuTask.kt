package com.shenlan.smartlogixmini.task

import com.shenlan.smartlogixmini.auto.MealTime
import com.shenlan.smartlogixmini.auto.Menu
import com.shenlan.smartlogixmini.auto.MenuMapper
import com.shenlan.smartlogixmini.util.localDateFormatter
import com.shenlan.smartlogixmini.util.log
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDate
import kotlin.system.measureTimeMillis

@Component
class MenuTask(private val menuMapper: MenuMapper) {
    @EventListener(ApplicationReadyEvent::class)
    fun runOnStartup() {
        generateNextWeekMenu()
    }

    /**
     * 每周五8点执行一次，生成下周的菜单
     */
    @Scheduled(cron = "0 0 8 ? * 6")
    fun generateNextWeekMenu() {
        // 生成下周菜单的逻辑
        val localDate = LocalDate.now()
        var count = 0

        val time = measureTimeMillis {
            try {
                for (i in 1..7) {
                    val menuDate = localDateFormatter.format(localDate.plusDays(i.toLong()))
                    // 生成菜单的逻辑
                    if (menuMapper.checkMenuDateExists(menuDate, MealTime.MORNING.toString()) == 0) {
                        // 生成早市菜单
                        menuMapper.insert(Menu(menuDate, MealTime.MORNING.toString()))
                        count++
                    }
                    if (menuMapper.checkMenuDateExists(menuDate, MealTime.AFTERNOON.toString()) == 0) {
                        // 生成午市菜单
                        menuMapper.insert(Menu(menuDate, MealTime.AFTERNOON.toString()))
                        count++
                    }
                }
            } catch (e: Exception) {
                log.error("Failed to generate next week's menu: ${e.message}")
            }
        }

        log.info("Generated $count menus for next week, cost $time ms")
    }
}