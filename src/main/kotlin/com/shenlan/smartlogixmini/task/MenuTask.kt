package com.shenlan.smartlogixmini.task

import com.shenlan.smartlogixmini.auto.MealTime
import com.shenlan.smartlogixmini.auto.Menu
import com.shenlan.smartlogixmini.auto.MenuMapper
import com.shenlan.smartlogixmini.auto.NotifyModule
import com.shenlan.smartlogixmini.auto.Notifymessage
import com.shenlan.smartlogixmini.auto.NotifymessageMapper
import com.shenlan.smartlogixmini.auto.Notifyusermessage
import com.shenlan.smartlogixmini.auto.NotifyusermessageMapper
import com.shenlan.smartlogixmini.auto.PermissionName
import com.shenlan.smartlogixmini.auto.PersonnelMapper
import com.shenlan.smartlogixmini.auto.Takeoutdish
import com.shenlan.smartlogixmini.auto.Takeoutmenu
import com.shenlan.smartlogixmini.auto.TakeoutmenuMapper
import com.shenlan.smartlogixmini.util.MenuUtil
import com.shenlan.smartlogixmini.util.NotifyMessageUtil.saveNotifyMessage
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.localDateFormatter
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.uuid
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDate
import kotlin.system.measureTimeMillis

@Component
class MenuTask(
    private val menuMapper: MenuMapper,
    private val takeoutMenuMapper: TakeoutmenuMapper
) {
    @EventListener(ApplicationReadyEvent::class)
    fun runOnStartup() {
        generateNextWeekMenu()
        generateTakeoutMenu()
    }

    /**
     * 每周五8点执行一次，生成下周的菜单
     */
    @Scheduled(cron = "0 0 8 ? * 6")
    fun generateNextWeekMenu() {
        // 生成下周菜单的逻辑
        val localDate = LocalDate.now()
        val meneDateSet = mutableSetOf<String>()

        val time = measureTimeMillis {
            try {
//                var dayOffset = 1L
//                // 生成从明天到最近周五的菜单，若明天为周五则直接停止
//                do {
//                    val menuDate = localDateFormatter.format(localDate.plusDays(dayOffset))
//                    // 生成菜单的逻辑
//                    if (menuMapper.checkMenuDateExists(menuDate, MealTime.MORNING.toString()) == 0) {
//                        // 生成早市菜单
//                        menuMapper.insert(Menu(menuDate, MealTime.MORNING.toString()))
//                        count++
//                    }
//                    if (menuMapper.checkMenuDateExists(menuDate, MealTime.AFTERNOON.toString()) == 0) {
//                        // 生成午市菜单
//                        menuMapper.insert(Menu(menuDate, MealTime.AFTERNOON.toString()))
//                        count++
//                    }
//                } while (localDate.plusDays(dayOffset ++).dayOfWeek != DayOfWeek.FRIDAY)
                // 1.1 生成下周菜单
                MenuUtil.getMenuDateList().forEach { menuDate ->
                    if (menuMapper.checkMenuDateExists(menuDate, MealTime.MORNING.toString()) == 0) {
                        // 生成早市菜单
                        menuMapper.insert(Menu(menuDate, MealTime.MORNING.toString()))
                        meneDateSet.add(menuDate)
                    }
                    if (menuMapper.checkMenuDateExists(menuDate, MealTime.AFTERNOON.toString()) == 0) {
                        // 生成午市菜单
                        menuMapper.insert(Menu(menuDate, MealTime.AFTERNOON.toString()))
                        meneDateSet.add(menuDate)
                    }
                }

                // 1.2 生成菜单通知信息
                if (meneDateSet.isNotEmpty()) {
                    // 1.2.1 获取所有食堂管理权限的人员
                    val users = getBean(PersonnelMapper::class.java).getListByPermissionName(PermissionName.MENU_MANAGEMENT.value)

                    // 1.2.2 保存通知信息
//                    val notifyMessage = Notifymessage().apply {
//                        id = uuid()
//                        module = NotifyModule.MENU.toString()
//                        type = "management"
//                        title = "下周菜单开放通知"
//                        content = "下周的日期已开放，可前往配置菜谱啦~"
//                        extra = meneDateSet.joinToString(",")
//                    }
//                    getBean(NotifymessageMapper::class.java).insert(notifyMessage)
//
//                    // 1.2.3 生成通知
//                    getBean(NotifyusermessageMapper::class.java).insertList(users.map { user ->
//                        Notifyusermessage().apply {
//                            id = uuid()
//                            userId = user.id
//                            messageId = notifyMessage.id
//                            pushStatus = 0
//                        }
//                    })
                    saveNotifyMessage(users.map { it.id },
                        NotifyModule.MENU.value,
                        "management",
                        "下周菜单开放通知",
                        "下周的日期已开放，可前往配置菜谱啦~",
                        meneDateSet.joinToString(","))
                }
            } catch (e: Exception) {
                log.error("Failed to generate next week's menu: ${e.message}")
            }
        }

        log.info("Generated ${meneDateSet.size} menus for next week, cost $time ms")
    }

    /**
     * 外卖菜谱生成，当天8点生成后两天的菜谱
     */
    @Scheduled(cron = "0 0 8 ? * *")
    fun generateTakeoutMenu() {
        // 生成外卖菜单的逻辑
        val localDate = LocalDate.now()
        val meneDateList = mutableListOf<String>()

        val time = measureTimeMillis {
            try {
                // 1.1 生成后天的菜单
                for (i in 1..2) {
                    val menuDate = localDateFormatter.format(localDate.plusDays(i.toLong()))
                    // 生成菜单的逻辑
                    if (takeoutMenuMapper.checkMenuDateExists(menuDate) == 0) {
                        // 生成外卖菜单
                        takeoutMenuMapper.insert(Takeoutmenu(menuDate))
                        meneDateList.add(menuDate)
                    }
                }

                // 1.2 生成外卖管理通知信息
                if (meneDateList.isNotEmpty()) {
                    // 1.2.1 获取所有外卖管理权限的人员
                    val users = getBean(PersonnelMapper::class.java).getListByPermissionName(PermissionName.TAKEOUT_MANAGEMENT.value)

                    saveNotifyMessage(users.map { it.id },
                        NotifyModule.TAKEOUT.value,
                        "management",
                        "外卖管理开放通知",
                        "后天的日期已经开放，可前往配置订餐菜谱啦~",
                        meneDateList.joinToString(","))
                }
            } catch (e: Exception) {
                log.error("Failed to generate takeout menu: ${e.message}")
            }
        }

        log.info("Generated ${meneDateList.size} takeout menus, cost $time ms")
    }
}