package com.shenlan.smartlogixmini.task

import com.shenlan.smartlogixmini.auto.MealTime
import com.shenlan.smartlogixmini.auto.Menu
import com.shenlan.smartlogixmini.auto.MenuMapper
import com.shenlan.smartlogixmini.auto.NotifyModule
import com.shenlan.smartlogixmini.auto.Notifymessage
import com.shenlan.smartlogixmini.auto.NotifymessageMapper
import com.shenlan.smartlogixmini.auto.Notifyusermessage
import com.shenlan.smartlogixmini.auto.NotifyusermessageMapper
import com.shenlan.smartlogixmini.auto.PermissionName
import com.shenlan.smartlogixmini.auto.PersonnelMapper
import com.shenlan.smartlogixmini.auto.PersonnelSearch
import com.shenlan.smartlogixmini.auto.Takeoutdish
import com.shenlan.smartlogixmini.auto.Takeoutmenu
import com.shenlan.smartlogixmini.auto.TakeoutmenuMapper
import com.shenlan.smartlogixmini.auto.MessagetemplateMapper
import com.shenlan.smartlogixmini.auto.TakeoutmenuService
import com.shenlan.smartlogixmini.auto.TakeoutmenudishMapper
import com.shenlan.smartlogixmini.util.MenuUtil
import com.shenlan.smartlogixmini.util.NotifyMessageUtil
import com.shenlan.smartlogixmini.util.NotifyMessageUtil.saveNotifyMessage
import com.shenlan.smartlogixmini.util.WechatSubscribeMessageUtil
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.localDateFormatter
import com.shenlan.smartlogixmini.util.localDateTimeFormatter
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.util.toJsonString
import com.shenlan.smartlogixmini.util.uuid
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.system.measureTimeMillis

@Component
class MenuTask(
    private val menuMapper: MenuMapper,
    private val takeoutMenuMapper: TakeoutmenuMapper
) {
    @EventListener(ApplicationReadyEvent::class)
    fun runOnStartup() {
        generateNextWeekMenu()
        generateTakeoutMenu()
    }

    /**
     * 每周五8点执行一次，生成下周的菜单
     */
    @Scheduled(cron = "0 0 8 ? * 6")
    fun generateNextWeekMenu() {
        // 生成下周菜单的逻辑
        val localDate = LocalDate.now()
        val meneDateSet = mutableSetOf<String>()

        val time = measureTimeMillis {
            try {
                // 1.1 生成下周菜单
                MenuUtil.getMenuDateList().forEach { menuDate ->
                    if (menuMapper.checkMenuDateExists(menuDate, MealTime.MORNING.toString()) == 0) {
                        // 生成早市菜单
                        menuMapper.insert(Menu(menuDate, MealTime.MORNING.toString()))
                        meneDateSet.add(menuDate)
                    }
                    if (menuMapper.checkMenuDateExists(menuDate, MealTime.AFTERNOON.toString()) == 0) {
                        // 生成午市菜单
                        menuMapper.insert(Menu(menuDate, MealTime.AFTERNOON.toString()))
                        meneDateSet.add(menuDate)
                    }
                }

                // 1.2 生成菜单通知信息
                if (meneDateSet.isNotEmpty()) {
                    // 1.2.1 获取所有食堂管理权限的人员
                    val search = PersonnelSearch()
                    search.permissionNameList = listOf(PermissionName.MENU_MANAGEMENT.value)
                    search.ifPage = false
                    val users = getBean(PersonnelMapper::class.java).getList(search)

                    // 1.2.2 保存通知信息
                    saveNotifyMessage(users.map { it.id },
                        NotifyModule.MENU.value,
                        "management",
                        "下周菜单开放通知",
                        "下周的日期已开放，可前往配置菜谱啦~",
                        meneDateSet.joinToString(","))
                }
            } catch (e: Exception) {
                log.error("Failed to generate next week's menu: ${e.message}")
            }
        }

        log.info("Generated ${meneDateSet.size} menus for next week, cost $time ms")
    }

    /**
     * 外卖菜谱生成，当天8点生成后两天的菜谱
     */
    @Scheduled(cron = "0 0 8 ? * *")
    fun generateTakeoutMenu() {
        // 生成外卖菜单的逻辑
        val localDate = LocalDate.now()
        val meneDateList = mutableListOf<String>()

        val time = measureTimeMillis {
            try {
                // 1.1 生成后天的菜单
                val menuDate = localDateFormatter.format(localDate.plusDays(2))
                // 1.1.1 生成菜单的逻辑
                if (takeoutMenuMapper.checkMenuDateExists(menuDate) == 0) {
                    // 生成外卖菜单
                    takeoutMenuMapper.insert(Takeoutmenu(menuDate))
                    meneDateList.add(menuDate)
                }

                // 1.2 生成外卖管理通知信息
                if (meneDateList.isNotEmpty()) {
                    // 1.2.1 获取所有外卖管理权限的人员
                    val search = PersonnelSearch()
                    search.permissionNameList = listOf(PermissionName.TAKEOUT_MANAGEMENT.value)
                    search.ifPage = false
                    val users = getBean(PersonnelMapper::class.java).getList(search)

                    saveNotifyMessage(users.map { it.id },
                        NotifyModule.TAKEOUT.value,
                        "management",
                        "外卖管理开放通知",
                        "后天的日期已经开放，可前往配置订餐菜谱啦~",
                        meneDateList.joinToString(","))
                }

                // 1.3 更新历史订单发布状态，将状态置为已发布
                takeoutMenuMapper.updatePublishedByDate(localDateFormatter.format(localDate), true)
                takeoutMenuMapper.updatePublishedByDate(localDateFormatter.format(localDate.plusDays(1)), true)

                // 1.4 明天有新菜品时职工端发送通知
                val publishedDishIds = getBean(TakeoutmenudishMapper::class.java).getAllDishId(localDateFormatter.format(localDate.plusDays(1))).toSet()
                val takeoutMenu = getBean(TakeoutmenuService::class.java).getMenuByDate(localDateFormatter.format(localDate.plusDays(1)), "").datas
                    ?: return
                if (takeoutMenu is Takeoutmenu && takeoutMenu.takeoutMenuDishList.any { it.dishId !in publishedDishIds }) {
                    log.info("new dishes: ${takeoutMenu.takeoutMenuDishList.filter { it.dishId !in publishedDishIds}.toJsonString}")
                    // 保存通知信息
                    val search = PersonnelSearch()
                    search.ifPage = false
                    search.permissionNameList = listOf(PermissionName.STAFF_PERMISSION.value)

                    val users = getBean(PersonnelMapper::class.java).getList(search)
                    saveNotifyMessage(users.map { it.id },
                        NotifyModule.TAKEOUT.value,
                        "normal",
                        "外卖订餐通知",
                        "明天的外卖面食上新啦，快来订餐吧！",
                        "")
                }
            } catch (e: Exception) {
                log.error("Failed to generate takeout menu: ${e.message}")
            }
        }

        log.info("Generated ${meneDateList.size} takeout menus, cost $time ms")
    }

    /**
     * 每天下午5点执行，清空职工端外卖通知
     */
    @Scheduled(cron = "0 0 17 ? * *")
    fun clearTakeoutNotify() {
        NotifyMessageUtil.deleteHistoryMessages(NotifyModule.TAKEOUT.value, "normal")
    }
}