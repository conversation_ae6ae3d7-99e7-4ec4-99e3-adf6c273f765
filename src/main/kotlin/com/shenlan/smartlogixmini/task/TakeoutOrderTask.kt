package com.shenlan.smartlogixmini.task

import com.shenlan.smartlogixmini.auto.OrderStatus
import com.shenlan.smartlogixmini.auto.TakeoutorderMapper
import com.shenlan.smartlogixmini.auto.TakeoutorderSearch
import com.shenlan.smartlogixmini.util.HttpUtil
import com.shenlan.smartlogixmini.util.TakeoutUtil
import com.shenlan.smartlogixmini.util.TakeoutUtil.TAKEOUT_UNPAID_KEEP_TIME
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.log
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.lang.Long.max
import kotlin.math.min

@Component
object TakeoutOrderTask {
    // 当天订单数目
    private var orderCount = 0

    @EventListener(ApplicationReadyEvent::class)
    fun runOnStartup() {
        updateOrderStatus()
    }

    // 获取订单号 000
    fun getPickupCode(): String {
        val count = getBean(TakeoutorderMapper::class.java).getTodayCompletedOrderCount()
        return String.format("%03d", count + 1)
    }

    /**
     * 每天零点重置订单数目
     * 同时将待取餐的订单状态更新为已完成
     */
    @Scheduled(cron = "0 0 0 ? * *")
    fun resetOrderCount() {
        orderCount = 0

        // 更新订单状态
        getBean(TakeoutorderMapper::class.java).updateOrderStatusByOldStatus(OrderStatus.PAID.value, OrderStatus.COMPLETED.value)
    }

    /**
     * 所有未支付状态的订单加入订单状态更新定时任务
     */
    fun updateOrderStatus() {
        val mapper = getBean(TakeoutorderMapper::class.java)
        val orderList = mapper.getList(TakeoutorderSearch().apply {
            status = OrderStatus.PENDING.value.toString()
        })
        log.info("Updating order status list, list size: ${orderList.size}")
        orderList.forEach { order ->
            val delay = max(0L, TAKEOUT_UNPAID_KEEP_TIME - (System.currentTimeMillis() - (order.sysCreated?.time ?: 0L)))
            TakeoutUtil.cancelOrderScheduleWithTimer(order.id, delay)
        }
    }
}