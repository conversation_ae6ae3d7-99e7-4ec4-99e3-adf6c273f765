package com.shenlan.smartlogixmini.task

import com.shenlan.smartlogixmini.auto.OrderStatus
import com.shenlan.smartlogixmini.auto.TakeoutorderMapper
import com.shenlan.smartlogixmini.util.getBean
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
object TakeoutOrderTask {
    // 当天订单数目
    private var orderCount = 0

    // 获取订单号 000
    fun getPickupCode(): String {
        return String.format("%03d", ++ orderCount)
    }

    /**
     * 每天零点重置订单数目
     * 同时将待取餐的订单状态更新为已完成
     */
    @Scheduled(cron = "0 0 0 ? * *")
    fun resetOrderCount() {
        orderCount = 0

        // 更新订单状态
        getBean(TakeoutorderMapper::class.java).updateOrderStatusByOldStatus(OrderStatus.PAID.value, OrderStatus.COMPLETED.value)
    }
}