package com.shenlan.smartlogixmini.task

import com.shenlan.smartlogixmini.auto.OrderStatus
import com.shenlan.smartlogixmini.auto.TakeoutorderMapper
import com.shenlan.smartlogixmini.auto.TakeoutorderSearch
import com.shenlan.smartlogixmini.util.HttpUtil
import com.shenlan.smartlogixmini.util.TakeoutUtil
import com.shenlan.smartlogixmini.util.TakeoutUtil.TAKEOUT_UNPAID_KEEP_TIME
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.log
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.lang.Long.max
import java.time.LocalDateTime
import kotlin.math.min

@Component
object TakeoutOrderTask {
    // 当天订单数目
    private var orderCount = 0

    @EventListener(ApplicationReadyEvent::class)
    fun runOnStartup() {
        cancelUnpaidOrder()
        updateOrderStatus()
    }

    // 获取订单号 000
    fun getPickupCode(organizationId: String): String {
        val count = getBean(TakeoutorderMapper::class.java).getTodayCompletedOrderCount(organizationId)
        return String.format("%03d", count + 1)
    }

    /**
     * 每天零点重置订单数目
     * 同时将待取餐的订单状态更新为已完成
     */
    @Scheduled(cron = "0 0 0 ? * *")
    fun resetOrderCount() {
        orderCount = 0

        // 更新订单状态
        getBean(TakeoutorderMapper::class.java).updateOrderStatusByOldStatus(OrderStatus.PAID.value, OrderStatus.COMPLETED.value)
    }

    /**
     * 每天下午5点，未支付的订单自动更新为已取消
     */
    @Scheduled(cron = "0 0 17 ? * *")
    fun cancelUnpaidOrder() {
        val time = LocalDateTime.now()
        if (time.isAfter(LocalDateTime.of(time.year, time.month, time.dayOfMonth, 17, 0)))
            getBean(TakeoutorderMapper::class.java).updateOrderStatusByOldStatus(OrderStatus.PENDING.value, OrderStatus.CANCELLED.value)
    }

    /**
     * 所有未支付状态的订单加入订单状态更新定时任务
     */
    fun updateOrderStatus() {
        val mapper = getBean(TakeoutorderMapper::class.java)
        val orderList = mapper.getList(TakeoutorderSearch().apply {
            status = OrderStatus.PENDING.value.toString()
        })
        log.info("Updating order status list, list size: ${orderList.size}")
        orderList.forEach { order ->
            val delay = max(0L, TAKEOUT_UNPAID_KEEP_TIME - (System.currentTimeMillis() - (order.sysCreated?.time ?: 0L)))
            TakeoutUtil.cancelOrderScheduleWithTimer(order.id, delay)
        }
    }
}