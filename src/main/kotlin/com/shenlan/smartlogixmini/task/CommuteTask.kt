package com.shenlan.smartlogixmini.task

import com.shenlan.smartlogixmini.auto.Commutevehiclelocation
import com.shenlan.smartlogixmini.auto.CommutevehiclelocationMapper
import com.shenlan.smartlogixmini.util.HttpUtil
import com.shenlan.smartlogixmini.util.customObjectMapper
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.log
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

class LoginJsp() {
    var sessionId: String = ""
}

class BaseJspResponse<T>() {
    var rspCode: String = ""
    var list: List<T> = emptyList()
}

@Component
class CommuteTask {
    var sessionId: String = ""

    @EventListener(ApplicationReadyEvent::class)
    fun runOnStartup() {
        sessionId = HttpUtil.getJsp<LoginJsp>("http://47.92.65.155:56666/gps-web/api/login.jsp?" +
                "userId=ceshi2025&password=c666b63419c90613f39d275a3fce862b" +
                "&loginType=user&onlyValidate=false&plateColor=&loginWay=interface&loginLang=zh_CN").sessionId
        updateCommuteVehicleLocation()
    }

    /**
     * 定时更新班车实时位置信息
     * 每30秒更新
     */
    @Scheduled(initialDelay =  2000, fixedDelay = 3000)
    fun updateCommuteVehicleLocation() {
        // 1.1 从外部接口获取班车实时位置信息
        log.info(sessionId)
        val mapper = getBean(CommutevehiclelocationMapper::class.java)
        val locations = HttpUtil.getJsp<BaseJspResponse<Commutevehiclelocation>>("http://47.92.65.155:56666/gps-web/api/get_gps_r.jsp?sessionId=$sessionId").list.onEach {
            it.id = it.carId
        }

        // 1.2 更新班车实时位置信息
        val allCarIds = mapper.getAllCarId().toSet()
        log.info("allCarIds: $allCarIds")

        // 1.3 如果locations的carId已存在则更新，否则插入
//        val updateList = mutableListOf<Commutevehiclelocation>()
//        val insertList = mutableListOf<Commutevehiclelocation>()
//        locations.forEach {
//            if (allCarIds.contains(it.carId)) {
//                updateList.add(it)
//            } else {
//                insertList.add(it)
//            }
//        }
        if (locations.any { it.id in allCarIds }) {
            mapper.batchUpdateLocation(locations.filter {  it.id in allCarIds })
        }
        if (locations.any { it.id !in allCarIds }) {
            mapper.insertList(locations.filter {  it.id !in allCarIds })
        }
        log.info("Commute vehicle location inserted: ${locations.filter {  it.id !in allCarIds }.size}, updated: ${locations.filter {  it.id in allCarIds }.size}")
    }
}