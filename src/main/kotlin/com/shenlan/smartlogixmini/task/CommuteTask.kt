package com.shenlan.smartlogixmini.task

import com.shenlan.smartlogixmini.auto.CommuteRouteType
import com.shenlan.smartlogixmini.auto.CommuteVehicleStatus
import com.shenlan.smartlogixmini.auto.CommuteroutestopMapper
import com.shenlan.smartlogixmini.auto.CommuteroutestopSearch
import com.shenlan.smartlogixmini.auto.CommutetripMapper
import com.shenlan.smartlogixmini.auto.Commutevehiclelocation
import com.shenlan.smartlogixmini.auto.CommutevehiclelocationMapper
import com.shenlan.smartlogixmini.auto.Commutevehicle
import com.shenlan.smartlogixmini.auto.CommutevehicleMapper
import com.shenlan.smartlogixmini.auto.CommutevehicleSearch
import com.shenlan.smartlogixmini.auto.SimpleCommuteVehicle
import com.shenlan.smartlogixmini.util.CommuteUtil
import com.shenlan.smartlogixmini.util.HttpUtil
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.localDateTimeFormatter
import com.shenlan.smartlogixmini.util.log
import com.shenlan.smartlogixmini.web.WebSocket
import com.shenlan.smartlogixmini.web.WebSocketType
import com.shenlan.smartlogixmini.web.WebsocketInfo
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDateTime

class LoginJsp() {
    var sessionId: String = ""
}

class BaseJspResponse<T>() {
    var rspCode: String = ""
    var list: List<T> = emptyList()
}

@Component
class CommuteTask {
    var sessionId: String = ""

    @EventListener(ApplicationReadyEvent::class)
    fun runOnStartup() {
        sessionId = HttpUtil.getJsp<LoginJsp>("http://47.92.65.155:56666/gps-web/api/login.jsp?" +
                "userId=ceshi2025&password=c666b63419c90613f39d275a3fce862b" +
                "&loginType=user&onlyValidate=false&plateColor=&loginWay=interface&loginLang=zh_CN").sessionId
        updateCommuteVehicle()
        updateCommuteVehicleLocation()
    }

    /**
     * 定时更新班车静态信息
     * 每小时更新
     */
    @Scheduled(cron = "0 0 * * * ?")
    fun updateCommuteVehicle() {
        val mapper = getBean(CommutevehicleMapper::class.java)

        // 1.1 从外部接口获取班车静态信息
        val vehicles = HttpUtil.getJsp<BaseJspResponse<Commutevehicle>>("http://47.92.65.155:56666/gps-web/api/get_car_list.jsp?sessionId=$sessionId").list.onEach {
            it.id = it.carId
        }

        // 1.2 获取所有班车静态信息id
        val allCarIds = mapper.getAllCarId().toSet()

        // 1.3 如果vehicles的carId已存在则更新，否则插入
        val insertVehicles = vehicles.filter {  it.id !in allCarIds }
        val updateVehicles = vehicles.filter {  it.id in allCarIds }
        if (insertVehicles.isNotEmpty()) {
            mapper.insertList(insertVehicles)
        }
        if (updateVehicles.isNotEmpty()) {
            mapper.batchUpdateVehicle(updateVehicles)
        }
        log.info("Commute vehicle inserted: ${insertVehicles.size}, updated: ${updateVehicles.size}")
    }

    /**
     * 定时更新班车实时位置信息
     * 每20秒更新
     */
    @Scheduled(initialDelay =  10 * 1000, fixedDelay = 20 * 1000)
    fun updateCommuteVehicleLocation() {
        val mapper = getBean(CommutevehiclelocationMapper::class.java)

        // 1.1 从外部接口获取班车实时位置信息
        val locations = HttpUtil.getJsp<BaseJspResponse<Commutevehiclelocation>>("http://47.92.65.155:56666/gps-web/api/get_gps_r.jsp?sessionId=$sessionId").list.onEach {
            it.id = it.carId
        }

        // 1.2 获取所有班车实时位置信息id
        val allCarIds = mapper.getAllCarId().toSet()

        // 1.3 如果locations的carId已存在则更新，否则插入
        val insertLocations = locations.filter {  it.id !in allCarIds }
        val updateLocations = locations.filter {  it.id in allCarIds }
        if (insertLocations.isNotEmpty()) {
            mapper.insertList(insertLocations)
        }
        if (updateLocations.isNotEmpty()) {
            mapper.batchUpdateLocation(updateLocations)
        }
//        WebSocket.sendInfo(WebsocketInfo(WebSocketType.COMMUTE_VEHICLE_LOCATION.value, localDateTimeFormatter.format(LocalDateTime.now()), locations))
        log.info("Commute vehicle location inserted: ${insertLocations.size}, updated: ${updateLocations.size}")

        // 1.4 发车状态下的班车需要更新到站情况
        try {
            // 1.4.1 获取所有发车中的班车
            val vehicles = getBean(CommutevehicleMapper::class.java).getList(CommutevehicleSearch().apply {
                status = CommuteVehicleStatus.ONLINE.value
            })

            // 1.4.2 获取所有站点信息
            val routeStopGroup = getBean(CommuteroutestopMapper::class.java).getList(CommuteroutestopSearch()).groupBy { it.routeId }

            // 1.4.3 获取所有班车行驶日志
            val tripGroup = getBean(CommutetripMapper::class.java).getTripListByCarIdList(vehicles.map { it.id }).groupBy { it.carId }

            // 1.4.4 更新班车到站情况
            vehicles.forEach { vehicle ->
                tripGroup[vehicle.id]?.elementAtOrNull(0)?.let { trip ->
                    val nextStop = if (vehicle.routeType == CommuteRouteType.HOME.value) {
                        routeStopGroup[trip.routeId]?.sortedByDescending { it.sequence }?.elementAtOrNull(trip.stopSequence + 1)
                    } else {
                        routeStopGroup[trip.routeId]?.sortedBy { it.sequence }?.elementAtOrNull(trip.stopSequence + 1)
                    }
                    nextStop?.let { stop ->
                        val distance = CommuteUtil.getDistance(vehicle.lat, vehicle.lng, stop.lat, stop.lng)
                        println("${vehicle.carName}距离${nextStop.name}: $distance")
                        if (distance < 100) {
                            getBean(CommutetripMapper::class.java).updateStopSequence(trip.id, trip.stopSequence + 1)
                            if (trip.status == CommuteVehicleStatus.ONLINE.value) {
                                getBean(CommutetripMapper::class.java).updateTripStatus(vehicle.id, CommuteVehicleStatus.STOP.value, localDateTimeFormatter.format(LocalDateTime.now()))
                            }
                        } else if (trip.status == CommuteVehicleStatus.STOP.value) {
                            getBean(CommutetripMapper::class.java).updateTripStatus(vehicle.id, CommuteVehicleStatus.ONLINE.value, localDateTimeFormatter.format(LocalDateTime.now()))
                        }
                    }
                }
            }

            WebSocket.sendInfo(WebsocketInfo(WebSocketType.COMMUTE_VEHICLE_LOCATION.value, localDateTimeFormatter.format(
                LocalDateTime.now()), vehicles.map {
                    SimpleCommuteVehicle(it.id, it.carName, it.routeId, it.lat, it.lng)
            }))
        } catch (e: Exception) {
            log.error("Failed to update commute vehicle status: ${e.message}")
        }
    }
}