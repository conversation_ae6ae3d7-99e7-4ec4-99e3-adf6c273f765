package com.shenlan.smartlogixmini.mns

import com.aliyun.mns.model.Message
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

/**
 * 队列类型枚举
 * 定义系统支持的所有MNS队列类型
 */
enum class QueueType {
    /**
     * 语音电话回执队列
     */
    VOICE_REPORT,
    
    /**
     * 短信上行消息队列
     */
    SMS_UP;
    
    /**
     * 获取配置前缀
     * 返回枚举名称的小写形式，用连字符替换下划线
     */
    val configPrefix: String
        get() = name.toLowerCase().replace("_", "-")
    
    companion object {
        /**
         * 根据配置前缀获取队列类型
         */
        fun fromConfigPrefix(prefix: String): QueueType? {
            return values().find { it.configPrefix == prefix }
        }
    }
}

/**
 * MNS配置类
 * 对应application.yml中的aliyun.mns配置
 */
@Configuration
@ConfigurationProperties(prefix = "aliyun.mns")
class MnsConfig {
    /**
     * 是否启用MNS服务
     */
    var enabled: Boolean = false

    /**
     * 阿里云AccessKeyId
     */
    var accessKeyId: String = ""

    /**
     * 阿里云AccessKeySecret
     */
    var accessKeySecret: String = ""

    /**
     * 队列配置集合
     */
    var queues: QueuesConfig = QueuesConfig()

    /**
     * 是否开启调试日志
     */
    var debug: Boolean = false

    /**
     * 队列配置集合类
     */
    class QueuesConfig {
        /**
         * 语音电话回执队列配置
         */
        var voiceReport: VoiceReportQueueConfig? = null
        
        /**
         * 短信上行消息队列配置
         */
        var smsUp: SmsUpQueueConfig? = null
        
        /**
         * 获取所有配置的队列
         */
        fun getAllQueues(): List<Pair<QueueType, QueueConfig>> {
            val queues = mutableListOf<Pair<QueueType, QueueConfig>>()
            
            voiceReport?.let { 
                if (it.isValid()) {
                    queues.add(QueueType.VOICE_REPORT to it)
                }
            }
            
            smsUp?.let {
                if (it.isValid()) {
                    queues.add(QueueType.SMS_UP to it)
                }
            }
            
            return queues
        }
    }
}

/**
 * MNS线程配置类
 * 对应application.yml中的aliyun.mns.thread配置
 */
@Configuration
@ConfigurationProperties(prefix = "aliyun.mns.thread")
class MnsThreadConfig {
    /**
     * 最小消费线程数
     */
    var consumeMinThreadSize: Int = 2

    /**
     * 最大消费线程数
     */
    var consumeMaxThreadSize: Int = 4

    /**
     * 线程队列大小
     */
    var threadQueueSize: Int = 50

    /**
     * 拉取消息线程数
     */
    var pullMsgThreadSize: Int = 1
}

/**
 * 队列配置基类
 */
open class QueueConfig {
    /**
     * 队列名称
     */
    var queueName: String = ""
    
    /**
     * 消息类型
     */
    var messageType: String = ""
    
    /**
     * 检查配置是否有效
     */
    fun isValid(): Boolean {
        return queueName.isNotEmpty() && messageType.isNotEmpty()
    }
}

/**
 * 语音电话回执队列配置
 */
@Configuration
@ConfigurationProperties(prefix = "aliyun.mns.queues.voice-report")
class VoiceReportQueueConfig : QueueConfig()

/**
 * 短信上行消息队列配置
 */
@Configuration
@ConfigurationProperties(prefix = "aliyun.mns.queues.sms-up")
class SmsUpQueueConfig : QueueConfig()

/**
 * MNS消息监听器接口
 * 用于处理从MNS队列接收到的消息，支持获取消息来源的队列类型
 */
interface MnsMessageListener {
    /**
     * 处理接收到的消息，并提供队列类型
     *
     * @param message MNS消息对象
     * @param queueType 消息来源的队列类型
     * @return 处理结果，true表示处理成功，false表示处理失败
     */
    fun handleMessage(message: Message, queueType: QueueType): Boolean
    
    /**
     * 兼容性方法，用于处理没有队列类型的消息
     * 默认实现会返回false，因为无法确定消息来源
     *
     * @param message MNS消息对象
     * @return 处理结果，true表示处理成功，false表示处理失败
     */
    fun handleMessage(message: Message): Boolean {
        // 默认实现，返回false表示处理失败
        return false
    }
    
    /**
     * 兼容性方法，用于处理字符串队列类型的消息
     * 尝试将字符串转换为QueueType枚举，然后调用带枚举参数的方法
     *
     * @param message MNS消息对象
     * @param queueTypeStr 消息来源的队列类型字符串
     * @return 处理结果，true表示处理成功，false表示处理失败
     */
    fun handleMessage(message: Message, queueTypeStr: String): Boolean {
        // 尝试转换为QueueType枚举
        val queueType = QueueType.fromConfigPrefix(queueTypeStr)
        return if (queueType != null) {
            handleMessage(message, queueType)
        } else {
            false // 未知队列类型，返回处理失败
        }
    }
}

/**
 * MNS消息处理结果
 */
enum class MnsMessageProcessResult {
    /**
     * 处理成功，消息将被删除
     */
    SUCCESS,

    /**
     * 处理失败，消息将重新入队
     */
    FAIL
}
