package com.shenlan.smartlogixmini.mns

import com.alicom.mns.tools.DefaultAlicomMessagePuller
import com.alicom.mns.tools.MessageListener
import com.google.gson.Gson
import com.shenlan.smartlogixmini.util.log
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.ApplicationListener
import org.springframework.context.event.ContextRefreshedEvent
import org.springframework.stereotype.Service
import java.text.SimpleDateFormat
import java.util.*
import javax.annotation.PreDestroy

/**
 * MNS服务
 * 负责初始化并启动MNS消息拉取器
 */
@Service
@ConditionalOnProperty(prefix = "aliyun.mns", name = ["enabled"], havingValue = "true")
class MnsService(
    private val mnsConfig: MnsConfig,
    private val mnsThreadConfig: MnsThreadConfig,
    private val listeners: List<MnsMessageListener>,
    private val voiceReportQueueConfig: VoiceReportQueueConfig,
    private val smsUpQueueConfig: SmsUpQueueConfig
) : ApplicationListener<ContextRefreshedEvent> {

    private val messagePullers = mutableListOf<DefaultAlicomMessagePuller>()
    private val gson = Gson()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    /**
     * 在应用程序上下文刷新时初始化MNS服务
     */
    override fun onApplicationEvent(event: ContextRefreshedEvent) {
        if (!mnsConfig.enabled) {
            log.info("MNS service is disabled, skipping initialization")
            return
        }

        // 将注入的配置赋值给MnsConfig的队列配置
        mnsConfig.queues.voiceReport = voiceReportQueueConfig
        mnsConfig.queues.smsUp = smsUpQueueConfig

        start()
    }

    /**
     * 启动MNS服务，支持多队列
     */
    private fun start() {
        log.info("Starting MNS service...")
        try {
            // 获取所有有效的队列配置
            val queueConfigs = mnsConfig.queues.getAllQueues()
            if (queueConfigs.isEmpty()) {
                log.warn("No valid MNS queues configured, service will not start any listener")
                return
            }

            // 遍历队列配置并启动监听器
            for ((queueType, queueConfig) in queueConfigs) {
                val messagePuller = DefaultAlicomMessagePuller()
                messagePuller.apply {
                    consumeMinThreadSize = mnsThreadConfig.consumeMinThreadSize
                    consumeMaxThreadSize = mnsThreadConfig.consumeMaxThreadSize
                    threadQueueSize = mnsThreadConfig.threadQueueSize
                    pullMsgThreadSize = mnsThreadConfig.pullMsgThreadSize
                    openDebugLog(mnsConfig.debug)
                    val internalListener = createInternalMessageListener(queueType)
                    startReceiveMsg(
                        mnsConfig.accessKeyId,
                        mnsConfig.accessKeySecret,
                        queueConfig.messageType,
                        queueConfig.queueName,
                        internalListener
                    )
                    log.info("MNS service started for queue type '${queueType.name}': name=${queueConfig.queueName}, message type=${queueConfig.messageType}")
                }
                messagePullers.add(messagePuller)
            }

            if (messagePullers.isNotEmpty()) {
                log.info("Total ${messagePullers.size} MNS queues initialized successfully")
            }
        } catch (e: Exception) {
            log.error("Failed to start MNS service: ${e.message}", e)
        }
    }

    /**
     * 创建内部消息监听器
     * 负责将消息转发到所有注册的外部监听器
     *
     * @param queueType 队列类型，用于标识消息来源
     * @return 消息监听器
     */
    private fun createInternalMessageListener(queueType: QueueType): MessageListener {
        return MessageListener { message ->
            // 记录消息接收日志
            log.info("Received MNS message from queue type '${queueType.name}' - Time: ${dateFormat.format(Date())}")
            log.info("Message ID: ${message.messageId}")
            log.info("Message Handle: ${message.receiptHandle}")
            log.info("Message Body: ${message.messageBodyAsString}")
            log.info("Message Dequeue Count: ${message.dequeueCount}")
            log.info("Processing Thread: ${Thread.currentThread().name}")

            try {
                // 解析消息内容
                val contentMap = gson.fromJson<Map<String, Any>>(
                    message.messageBodyAsString,
                    HashMap::class.java
                )

                // 如果没有注册监听器，记录警告日志
                if (listeners.isEmpty()) {
                    log.warn("No MNS message listeners registered, message will be discarded")
                    return@MessageListener true
                }

                // 转发消息到所有注册的监听器
                var allSuccess = true

                for (listener in listeners) {
                    try {
                        // 调用监听器处理消息，传递队列类型
                        val result = listener.handleMessage(message, queueType)

                        if (!result) {
                            allSuccess = false
                            log.warn("Listener ${listener.javaClass.simpleName} failed to process message from queue type '${queueType.name}'")
                        }
                    } catch (e: Exception) {
                        allSuccess = false
                        log.error("Listener ${listener.javaClass.simpleName} exception while processing message from queue type '${queueType.name}': ${e.message}", e)
                    }
                }

                // 如果任何一个监听器处理失败，则返回false，消息将被重新推送
                return@MessageListener allSuccess
            } catch (e: Exception) {
                log.error("Exception processing MNS message from queue type '${queueType.name}': ${e.message}", e)

                // 如果是JSON解析异常，返回true，消息将被删除
                // 避免无效消息反复推送导致资源浪费
                if (e is com.google.gson.JsonSyntaxException) {
                    log.error("JSON format error, message will be deleted: ${message.messageBodyAsString}", e)
                    return@MessageListener true
                }

                // 其他异常返回false，消息将被重新推送
                return@MessageListener false
            }
        }
    }

    /**
     * 在应用关闭时停止所有MNS服务
     */
    @PreDestroy
    fun stop() {
        log.info("Stopping all MNS services...")
        for (puller in messagePullers) {
            try {
                puller.stop()
            } catch (e: Exception) {
                log.error("Exception while stopping MNS service: ${e.message}", e)
            }
        }
        log.info("All MNS services stopped")
    }
}
