package com.shenlan.smartlogixmini.mns
import com.aliyun.mns.model.Message
import com.google.gson.Gson
import com.shenlan.smartlogixmini.auto.*
import com.shenlan.smartlogixmini.util.ifEmpty
import com.shenlan.smartlogixmini.util.log
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*

/**
 * 理发回执处理监听器
 * 负责处理从MNS队列接收到的语音电话回执消息和短信上行消息，并更新理发反馈状态
 */
@Service
@ConditionalOnProperty(prefix = "aliyun.mns", name = ["enabled"], havingValue = "true")
class HairCallbackListener(
    private val hairreservationService: HairreservationService,
    private val hairnotificationMapper: HairnotificationMapper,
    private val hairreservationMapper: HairreservationMapper,
    private val personnelMapper: PersonnelMapper,
    private val mnsConfig: MnsConfig
) : MnsMessageListener {
    private val gson = Gson()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    @Value("\${aliyun.sms.sign-name:智慧后勤}")
    private var smsSignName: String = ""

    /**
     * 处理接收到的消息
     *
     * @param message MNS消息对象
     * @param queueType 消息来源的队列类型
     * @return 处理结果，true表示处理成功，false表示处理失败
     */
    @Transactional
    override fun handleMessage(message: Message, queueType: QueueType): Boolean {
        log.info("HairCallbackListener received message: ${message.messageId} from queue type: ${queueType.name}")

        try {
            // 解析消息内容
            @Suppress("UNCHECKED_CAST")
            val callbackData = gson.fromJson(message.messageBodyAsString, Map::class.java) as Map<String, Any>

            // 根据队列类型判断消息类型
            return when (queueType) {
                QueueType.VOICE_REPORT -> handleVoiceReport(callbackData)
                QueueType.SMS_UP -> handleSmsUp(callbackData)
            }
        } catch (e: Exception) {
            log.error("Exception processing MNS message: ${e.message}", e)
            return false
        }
    }

    /**
     * 处理语音电话回执消息
     *
     * @return 处理结果，true表示处理成功，false表示处理失败
     */
    private fun handleVoiceReport(callbackData: Map<String, Any>): Boolean {
        // 提取关键字段
        val callId = callbackData["call_id"]?.toString() ?: ""
        val dtmfValue = callbackData["dtmf"]?.toString() ?: ""
        val callee = callbackData["callee"]?.toString() ?: ""
        val statusCode = callbackData["status_code"]?.toString() ?: ""
        val statusMsg = callbackData["status_msg"]?.toString() ?: ""

        log.info("Processing voice callback: callId=$callId, dtmf=$dtmfValue, callee=$callee, statusCode=$statusCode")

        // 检查必要字段
        if (callId.isEmpty()) {
            log.error("Missing call_id field in voice callback message")
            return true // 消息格式错误，但仍标记为处理成功，避免重复处理
        }

//        // 根据callId查找相应的通知记录
//        val meetingnotification = meetingnotificationMapper.getInfoByReceiptId(callId)
//        if (meetingnotification == null) {
//            log.info("No meetingnotification record found for callId: $callId")
//            return true // 找不到记录，但仍标记为处理成功，避免重复处理
//        }
//
//        // 检查通知是否为语音通知类型
//        if (meetingnotification.method != 1) {
//            log.error("Meetingnotification is not voice call type: ${meetingnotification.method}")
//            return true
//        }
//
//        // 只有用户提供了dtmf按键反馈时才更新参会反馈
//        if (dtmfValue.isNotEmpty()) {
//            // 将dtmf值转换为反馈类型
//            val feedback = dtmfValue.toIntOrNull() ?: 0
//            if (feedback in 1..3) { // 确保反馈值在有效范围内：1-参加，2-建议延期，3-不参加
//                // 更新参会人员的反馈状态
//                val result = meetingpersonnelMapper.updateFeedback(
//                    meetingnotification.meetingId,
//                    meetingnotification.personnelId,
//                    feedback,
//                    "通过语音电话反馈"
//                )
//
//                if (result > 0) {
//                    log.info("Successfully updated attendance feedback: meetingId=${meetingnotification.meetingId}, personnelId=${meetingnotification.personnelId}, feedback=$feedback")
//                    // 更新通知记录的结果信息
//                    val resultInfo = "status: $statusCode, message: $statusMsg, dtmf: $dtmfValue"
//                    meetingnotificationMapper.updateMeetingnotificationResult(meetingnotification.id, resultInfo)
//                    return true
//                } else {
//                    log.warn("Failed to update attendance feedback: meetingId=${meetingnotification.meetingId}, personnelId=${meetingnotification.personnelId}")
//                    return false // 更新失败，返回处理失败
//                }
//            } else {
//                log.warn("Invalid dtmf feedback value: $dtmfValue")
//                return true // 无效的dtmf值，但仍标记为处理成功
//            }
//        } else {
//            log.info("No dtmf input provided by user")
//            // 更新通知记录的结果信息
//            val resultInfo = "status: $statusCode, message: $statusMsg, no dtmf input"
//            meetingnotificationMapper.updateMeetingnotificationResult(meetingnotification.id, resultInfo)
            return true
        }


    /**
     * 处理短信上行消息
     *
     * @return 处理结果，true表示处理成功，false表示处理失败
     */
    private fun handleSmsUp(callbackData: Map<String, Any>): Boolean {
        // 提取关键字段
        val phoneNumber = callbackData["phone_number"]?.toString() ?: ""
        val content = callbackData["content"]?.toString() ?: ""
        val signName = callbackData["sign_name"]?.toString() ?: ""
        val destCode = callbackData["dest_code"]?.toString() ?: ""
        val sendTimeStr = callbackData["send_time"]?.toString() ?: ""

        log.info("Processing SMS up message: phoneNumber=$phoneNumber, content=$content, signName=$signName, sendTime=$sendTimeStr")

        // 检查必要字段
        if (phoneNumber.isEmpty() || content.isEmpty() || sendTimeStr.isEmpty()) {
            log.error("Missing required fields in SMS up message")
            return true // 消息格式错误，但仍标记为处理成功，避免重复处理
        }

        // 检查短信签名是否匹配（移除【】符号后比较）
        val normalizedSignName = signName.trim().replace(Regex("^【|】$"), "")
        if (normalizedSignName != smsSignName) {
            log.warn("SMS sign name not match: expected=$smsSignName, actual=$normalizedSignName")
            return true // 签名不匹配，但仍标记为处理成功，避免重复处理
        }

        // 解析短信内容
        val contentParts = parseContent(content)
        val feedbackCode = contentParts.first
        val reason = contentParts.second

        if (feedbackCode !in 4..6) {
            log.warn("Invalid feedback code in SMS content: $content")
            return true // 反馈代码无效，但仍标记为处理成功，避免重复处理
        }

        // 解析发送时间
        val sendTime = try {
            dateFormat.parse(sendTimeStr)
        } catch (e: Exception) {
            log.error("Failed to parse send_time: $sendTimeStr", e)
            Date() // 解析失败则使用当前时间
        }

        // 1. 查找对应的理发通知记录
        val hairNotification = hairnotificationMapper.getNoticeBySendSMS(phoneNumber,sendTime)
        if (hairNotification == null) {
            log.error("找不到对应的理发通知记录 $phoneNumber")
            return true // 找不到对应的理发通知记录，但仍标记为处理成功，避免重复处理
        }
        // 3. 更新通过短信发送的车主反馈
        if(feedbackCode==4){
            val feedback=Hairnotification().apply {
                this.id=hairNotification.id
                this.feedbackTime=sendTime.toLocalDateTime()
                this.feedbackContent="立即到店"
            }
            val result=hairnotificationMapper.feedBack(feedback)
            if (result > 0) {
                log.info("4 反馈立即到店成功$phoneNumber")}
            else {
                log.error("4 反馈立即到店失败$phoneNumber")
            }
            return true
        }
        if(feedbackCode==5){
            val feedback=Hairnotification().apply {
                this.id=hairNotification.id
                this.feedbackTime=sendTime.toLocalDateTime()
                this.feedbackContent=reason+"分钟内到店"
            }
            val result=hairnotificationMapper.feedBack(feedback)
            if (result > 0) {
                log.info("5 稍后到店通知反馈成功$phoneNumber")}
            else {
                log.error("5 稍后到店通知反馈失败$phoneNumber")
            }
            return true
        }

        if (feedbackCode == 6) {
            val hairreservation=hairreservationMapper.getInfo(hairNotification.reservationId)
            // 更新通知结果记录
            if (hairreservation == null) {
                log.error("6 找不到对应的预约记录")
                return true
            }
            val result=hairreservationService.deleteReservationByUser(hairreservation)
            if (result.rlt == 0) {
                log.info("6 取消理发成功$phoneNumber")}
            else {
                log.error("6 取消理发失败$phoneNumber")
            }
            return true
        }
        return true
    }

    /**
     * 解析短信内容，提取反馈代码和原因
     *
     * @return Pair<反馈代码, 原因>
     */
    private fun parseContent(content: String): Pair<Int, String> {
        val trimmedContent = content.trim()

        // 简单的内容（如"4"）
        if (trimmedContent.length == 1) {
            val code = trimmedContent.toIntOrNull() ?: 0
            return code to ""
        }

        // 带预计分钟数的内容（如"5,30"或"5，30"）
        val parts = trimmedContent.split(Regex("[,，]"), 2)
        if (parts.size == 2) {
            val code = parts[0].trim().toIntOrNull() ?: 0
            val reason = parts[1].trim()
            return code to reason
        }

        // 默认情况，取第一个字符作为状态码，转换失败则返回默认值0
        val code = trimmedContent.firstOrNull()?.toString()?.toIntOrNull() ?: 0
        return code to ""
    }
//    将Date转换成LocalDateTime
    fun Date.toLocalDateTime(): LocalDateTime {
        return this.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDateTime()
    }
}
