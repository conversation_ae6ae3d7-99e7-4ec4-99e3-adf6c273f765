package com.shenlan.smartlogixmini.mns

import com.aliyun.mns.model.Message
import com.google.gson.Gson
import com.shenlan.smartlogixmini.auto.*
import com.shenlan.smartlogixmini.util.ifEmpty
import com.shenlan.smartlogixmini.util.log
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.text.SimpleDateFormat
import java.util.*

/**
 * 挪车短信回执处理监听器
 * 负责处理从MNS队列接收到的挪车相关短信上行消息，并更新挪车订单状态
 */
@Service
@ConditionalOnProperty(prefix = "aliyun.mns", name = ["enabled"], havingValue = "true")
class ParkingSmsListener(
    private val vehicleInfoMapper: VehicleinfoMapper,
    private val mnsConfig: MnsConfig,
    private val personnelMapper: PersonnelMapper,
    private val vehiclenotificationMapper: VehiclenotificationMapper,
    private val vehicleinfoMapper: VehicleinfoMapper
) : MnsMessageListener {

    private val gson = Gson()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    @Value("\${aliyun.sms.sign-name:智慧后勤}")
    private var smsSignName: String = ""

    /**
     * 处理接收到的消息
     *
     * @param message MNS消息对象
     * @param queueType 消息来源的队列类型
     * @return 处理结果，true表示处理成功，false表示处理失败
     */
    @Transactional
    override fun handleMessage(message: Message, queueType: QueueType): Boolean {
        log.info("ParkingSmsListener received message: ${message.messageId} from queue type: ${queueType.name}")

        try {
            // 只处理短信上行消息
            if (queueType != QueueType.SMS_UP) {
                return true
            }

            // 解析消息内容
            @Suppress("UNCHECKED_CAST")
            val callbackData = gson.fromJson(message.messageBodyAsString, Map::class.java) as Map<String, Any>

            return handleSmsUp(callbackData)
        } catch (e: Exception) {
            log.error("Exception processing MNS message: ${e.message}", e)
            return false
        }
    }

    /**
     * 处理短信上行消息
     *
     * @return 处理结果，true表示处理成功，false表示处理失败
     */
    private fun handleSmsUp(callbackData: Map<String, Any>): Boolean {
        // 提取关键字段
        val phoneNumber = callbackData["phone_number"]?.toString() ?: ""
        val content = callbackData["content"]?.toString() ?: ""
        val signName = callbackData["sign_name"]?.toString() ?: ""
        val destCode = callbackData["dest_code"]?.toString() ?: ""
        val sendTimeStr = callbackData["send_time"]?.toString() ?: ""

        log.info("Processing SMS up message: phoneNumber=$phoneNumber, content=$content, signName=$signName, sendTime=$sendTimeStr")

        // 检查必要字段
        if (phoneNumber.isEmpty() || content.isEmpty() || sendTimeStr.isEmpty()) {
            log.error("Missing required fields in SMS up message")
            return true // 消息格式错误，但仍标记为处理成功，避免重复处理
        }

        // 检查短信签名是否匹配（移除【】符号后比较）
        val normalizedSignName = signName.trim().replace(Regex("^【|】$"), "")
        if (normalizedSignName != smsSignName) {
            log.warn("SMS sign name not match: expected=$smsSignName, actual=$normalizedSignName")
            return true // 签名不匹配，但仍标记为处理成功，避免重复处理
        }

        // 解析短信内容
        val contentParts = parseContent(content)
        val feedbackCode = contentParts.first
        val reason = contentParts.second

        if (feedbackCode !in 7..9) {
            log.warn("Invalid feedback code in SMS content: $content")
            return true // 反馈代码无效，但仍标记为处理成功，避免重复处理
        }

        // 解析发送时间
        val sendTime = try {
            dateFormat.parse(sendTimeStr)
        } catch (e: Exception) {
            log.error("Failed to parse send_time: $sendTimeStr", e)
            Date() // 解析失败则使用当前时间
        }

        // 1. 查找对应的人员
        val personnel = personnelMapper.getInfoByPhone(phoneNumber)
        if (personnel == null) {
            log.error("No personnel found for phone number: $phoneNumber")
            return true // 找不到人员记录，但仍标记为处理成功，避免重复处理
        }

        // 2. 根据该人员的手机号查询id并且处理挪车反馈
        val personnelId = personnel.id
        val id: String?= vehiclenotificationMapper.getLatestByPersonnelId(personnelId)?.id
        if (id == null) {
            log.warn("No SMS notice found for personnel: ${personnel.id} (${personnel.name}) before $sendTimeStr")
            return true // 找不到通知记录，但仍标记为处理成功，避免重复处理
        }

        // 3. 更新通过短信发送的车主反馈
        if(feedbackCode==7){
            val feedback=Vehiclenotification().apply {
                this.id=id
                this.feedbackContent="立即挪车"
            }
            val result=vehiclenotificationMapper.feedBack(feedback)
            if (result > 0) {
                log.info("7 Successfully updated  Vehiclenotification By SMS")}
            else {
                log.error("7 Failed to update Vehiclenotification")
            }
            return true
        }
        if(feedbackCode==9){
            val feedback=Vehiclenotification().apply {
                this.id=id
                this.feedbackContent="暂不方便挪车"
            }
            val result=vehiclenotificationMapper.feedBack(feedback)
            if (result > 0) {
                log.info("9 Successfully updated  Vehiclenotification By SMS")}
            else {
                log.error("9 Failed to update Vehiclenotification")
            }
            return true
        }

        if (feedbackCode == 8) {
            val feedback=Vehiclenotification().apply {
                this.id=id
                this.feedbackContent=reason+"分钟内挪车"
            }
            // 更新通知结果记录
            val result=vehiclenotificationMapper.feedBack(feedback)
            if (result > 0) {
                log.info("8 Successfully updated  Vehiclenotification By SMS")}
            else {
                log.error("8 Failed to update Vehiclenotification")
            }
            return true
        }
        return true

    }

    /**
     * 解析短信内容，提取回复代码
     */
    private fun parseContent(content: String): Pair<Int, String> {
        val trimmedContent = content.trim()

        // 简单的内容（如"7"）
        if (trimmedContent.length == 1) {
            val code = trimmedContent.toIntOrNull() ?: 0
            return code to ""
        }

        // 含分隔符的回复
        val parts = trimmedContent.split(Regex("[,，]"), 2)
        if (parts.size == 2) {
            val code = parts[0].trim().toIntOrNull() ?: 0
            val reason = parts[1].trim()
            return code to reason
        }
        // 默认情况
        val code = trimmedContent.firstOrNull()?.toString()?.toIntOrNull() ?: 0
        return code to ""
    }

}