package com.shenlan.smartlogixmini.mybatis

import com.shenlan.smartlogixmini.util.log
import org.springframework.stereotype.Component
import java.lang.reflect.Proxy

/**
 * BaseMapper代理工厂
 * 创建BaseMapper接口的动态代理对象，注入必要的依赖服务
 */
@Component
class BaseMapperProxyFactory(
    private val sqlRegistrationCache: SqlRegistrationCache,
    private val myBatisSqlRegistrar: MyBatisSqlRegistrar,
    private val databaseMetadataService: DatabaseMetadataService
) {

    /**
     * 为指定的Mapper接口创建动态代理对象
     */
    fun createProxy(mapperInterface: Class<*>, originalMapper: Any): Any {
        log.debug("Creating proxy for mapper: ${mapperInterface.simpleName}")

        // 创建调用处理器
        val invocationHandler = BaseMapperInvocationHandler(
            originalMapper = originalMapper,
            mapperInterface = mapperInterface,
            sqlRegistrationCache = sqlRegistrationCache,
            myBatisSqlRegistrar = myBatisSqlRegistrar,
            databaseMetadataService = databaseMetadataService
        )

        // 创建动态代理对象
        val proxy = Proxy.newProxyInstance(
            mapperInterface.classLoader,
            arrayOf(mapperInterface),
            invocationHandler
        )

        log.debug("Successfully created proxy for mapper: ${mapperInterface.simpleName}")
        return proxy
    }
}
