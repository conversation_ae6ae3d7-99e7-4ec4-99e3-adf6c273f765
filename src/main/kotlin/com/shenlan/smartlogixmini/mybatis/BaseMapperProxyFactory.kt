package com.shenlan.smartlogixmini.mybatis

import com.shenlan.smartlogixmini.auto.BaseMapper
import com.shenlan.smartlogixmini.util.log
import org.springframework.stereotype.Component
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Proxy

/**
 * BaseMapper代理工厂
 * 创建BaseMapper接口的动态代理对象，注入必要的依赖服务
 */
@Component
class BaseMapperProxyFactory(
    private val sqlRegistrationCache: SqlRegistrationCache,
    private val myBatisSqlRegistrar: MyBatisSqlRegistrar,
    private val databaseMetadataService: DatabaseMetadataService,
    private val sqlGenerator: SqlGenerator
) {

    /**
     * 为指定的Mapper接口创建动态代理对象
     */
    fun createProxy(mapperInterface: Class<*>, originalMapper: Any): Any {
        log.debug("Creating proxy for mapper: ${mapperInterface.simpleName}")

        // 创建调用处理器
        val invocationHandler = BaseMapperInvocationHandler(
            originalMapper = originalMapper,
            mapperInterface = mapperInterface,
            sqlRegistrationCache = sqlRegistrationCache,
            myBatisSqlRegistrar = myBatisSqlRegistrar,
            databaseMetadataService = databaseMetadataService,
            sqlGenerator = sqlGenerator
        )

        // 创建动态代理对象
        val proxy = Proxy.newProxyInstance(
            mapperInterface.classLoader,
            arrayOf(mapperInterface),
            invocationHandler
        )

        log.debug("Successfully created proxy for mapper: ${mapperInterface.simpleName}")
        return proxy
    }

    /**
     * 检查指定类是否为BaseMapper的实现类
     */
    fun isBaseMapperImplementation(clazz: Class<*>): Boolean {
        if (!clazz.isInterface) {
            return false
        }

        // 检查是否直接或间接继承BaseMapper
        return isAssignableFromBaseMapper(clazz)
    }

    /**
     * 检查类是否继承自BaseMapper
     */
    private fun isAssignableFromBaseMapper(clazz: Class<*>): Boolean {
        // 检查直接继承
        for (interfaceClass in clazz.interfaces) {
            if (interfaceClass == BaseMapper::class.java) {
                return true
            }
            // 递归检查间接继承
            if (isAssignableFromBaseMapper(interfaceClass)) {
                return true
            }
        }

        // 检查泛型接口
        for (genericInterface in clazz.genericInterfaces) {
            if (genericInterface is ParameterizedType) {
                val rawType = genericInterface.rawType
                if (rawType == BaseMapper::class.java) {
                    return true
                }
            }
        }

        return false
    }

    /**
     * 检查对象是否已经是代理对象
     */
    fun isProxy(obj: Any): Boolean {
        return Proxy.isProxyClass(obj.javaClass)
    }

    /**
     * 获取代理统计信息（用于调试）
     */
    fun getProxyStats(): Map<String, Any> {
        return mapOf(
            "registeredMappersCount" to sqlRegistrationCache.getRegisteredCount(),
            "registeredMapperNames" to sqlRegistrationCache.getRegisteredMapperNames()
        )
    }
} 
