package com.shenlan.smartlogixmini.mybatis

import com.shenlan.smartlogixmini.util.log
import org.springframework.stereotype.Component
import java.util.concurrent.ConcurrentHashMap

/**
 * SQL注册状态缓存
 * 维护已注册SQL的Mapper类集合，提供线程安全的注册状态检查和更新方法
 */
@Component
class SqlRegistrationCache {

    // 已注册SQL的Mapper类集合
    private val registeredMappers = ConcurrentHashMap.newKeySet<Class<*>>()

    // 每个Mapper类的锁对象映射
    private val mapperLocks = ConcurrentHashMap<Class<*>, Any>()

    /**
     * 检查指定Mapper类是否已注册SQL
     */
    fun isRegistered(mapperClass: Class<*>): Boolean {
        return registeredMappers.contains(mapperClass)
    }

    /**
     * 标记指定Mapper类为已注册状态
     */
    fun markAsRegistered(mapperClass: Class<*>) {
        registeredMappers.add(mapperClass)
        log.debug("Marked mapper as registered: ${mapperClass.simpleName}")
    }

    /**
     * 获取指定Mapper类的锁对象
     * 使用双重检查锁定模式确保每个Mapper类只有一个锁对象
     */
    fun getLockForMapper(mapperClass: Class<*>): Any {
        return mapperLocks.computeIfAbsent(mapperClass) { Any() }
    }

    /**
     * 清空所有注册状态（主要用于测试）
     */
    fun clear() {
        registeredMappers.clear()
        mapperLocks.clear()
        log.debug("Cleared all registration cache")
    }

    /**
     * 获取已注册的Mapper数量
     */
    fun getRegisteredCount(): Int {
        return registeredMappers.size
    }

    /**
     * 获取所有已注册的Mapper类名（用于调试）
     */
    fun getRegisteredMapperNames(): Set<String> {
        return registeredMappers.map { it.simpleName }.toSet()
    }
} 
