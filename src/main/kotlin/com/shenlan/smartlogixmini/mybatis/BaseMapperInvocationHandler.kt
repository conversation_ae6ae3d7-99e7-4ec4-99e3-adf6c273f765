package com.shenlan.smartlogixmini.mybatis

import com.shenlan.smartlogixmini.auto.BaseMapper
import com.shenlan.smartlogixmini.util.log
import java.lang.reflect.InvocationHandler
import java.lang.reflect.Method
import java.lang.reflect.ParameterizedType

/**
 * BaseMapper调用处理器
 * 实现懒加载SQL注册逻辑，在方法调用前检查并注册SQL
 */
class BaseMapperInvocationHandler(
    private val originalMapper: Any,
    private val mapperInterface: Class<*>,
    private val sqlRegistrationCache: SqlRegistrationCache,
    private val myBatisSqlRegistrar: MyBatisSqlRegistrar,
    private val databaseMetadataService: DatabaseMetadataService,
    private val sqlGenerator: SqlGenerator
) : InvocationHandler {

    override fun invoke(proxy: Any?, method: Method?, args: Array<out Any>?): Any? {
        // 检查是否为BaseMapper的方法
        if (method != null && isBaseMapperMethod(method)) {
            // 确保SQL已注册
            ensureSqlRegistered()
        }

        // 调用原始方法
        return try {
            if (args != null) {
                method?.invoke(originalMapper, *args)
            } else {
                method?.invoke(originalMapper)
            }
        } catch (e: Exception) {
            log.error("Error invoking method ${method?.name} on mapper ${mapperInterface.simpleName}", e)
            throw e
        }
    }

    /**
     * 检查方法是否为BaseMapper接口定义的方法
     */
    private fun isBaseMapperMethod(method: Method): Boolean {
        val baseMapperMethods = setOf(
            "getList", "getListByPid", "getInfo", 
            "insert", "insertList", "delete", "deleteLogic"
        )
        return baseMapperMethods.contains(method.name)
    }

    /**
     * 确保SQL已注册，使用双重检查锁定模式
     */
    private fun ensureSqlRegistered() {
        // 第一次检查（无锁）
        if (sqlRegistrationCache.isRegistered(mapperInterface)) {
            return
        }

        // 获取Mapper专用锁
        val lock = sqlRegistrationCache.getLockForMapper(mapperInterface)
        
        synchronized(lock) {
            // 第二次检查（有锁）
            if (sqlRegistrationCache.isRegistered(mapperInterface)) {
                return
            }

            // 执行SQL注册
            try {
                log.debug("Registering SQL for mapper: ${mapperInterface.simpleName}")
                
                // 获取实体类
                val entityClass = getEntityClassFromMapper(mapperInterface)
                if (entityClass == null) {
                    log.warn("Cannot determine entity class for mapper: ${mapperInterface.simpleName}")
                    return
                }

                // 获取表名
                val tableName = databaseMetadataService.getTableNameFromEntityClass(entityClass)

                // 获取表信息
                val tableInfo = databaseMetadataService.getTableInfo(tableName)
                if (tableInfo == null) {
                    log.warn("Cannot find table info for: $tableName (mapper: ${mapperInterface.simpleName})")
                    return
                }

                // 注册SQL
                myBatisSqlRegistrar.registerBaseMapperSqls(mapperInterface, tableInfo, sqlGenerator)

                // 标记为已注册
                sqlRegistrationCache.markAsRegistered(mapperInterface)

                log.debug("Successfully registered SQL for mapper: ${mapperInterface.simpleName} -> table: $tableName")

            } catch (e: Exception) {
                log.error("Failed to register SQL for mapper: ${mapperInterface.simpleName}", e)
                // 注册失败不影响应用运行，但会记录错误
            }
        }
    }

    /**
     * 从Mapper接口获取实体类型
     */
    private fun getEntityClassFromMapper(mapperClass: Class<*>): Class<*>? {
        return try {
            val genericInterfaces = mapperClass.genericInterfaces
            for (genericInterface in genericInterfaces) {
                if (genericInterface is ParameterizedType) {
                    val rawType = genericInterface.rawType
                    if (rawType == BaseMapper::class.java) {
                        val actualTypeArguments = genericInterface.actualTypeArguments
                        if (actualTypeArguments.isNotEmpty()) {
                            return actualTypeArguments[0] as Class<*>
                        }
                    }
                }
            }

            // 如果没有找到泛型参数，尝试从父接口查找
            for (parentInterface in mapperClass.interfaces) {
                if (parentInterface != BaseMapper::class.java) {
                    val entityClass = getEntityClassFromMapper(parentInterface)
                    if (entityClass != null) {
                        return entityClass
                    }
                }
            }

            null
        } catch (e: Exception) {
            log.error("Failed to get entity class from mapper: ${mapperClass.simpleName}", e)
            null
        }
    }
} 
