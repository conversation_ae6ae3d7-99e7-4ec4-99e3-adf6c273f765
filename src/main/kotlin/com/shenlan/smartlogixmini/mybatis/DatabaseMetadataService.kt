package com.shenlan.smartlogixmini.mybatis

import com.shenlan.smartlogixmini.util.log
import org.springframework.stereotype.Service
import javax.sql.DataSource

/**
 * 数据库元数据服务
 * 负责查询数据库表结构信息
 */
@Service
class DatabaseMetadataService(private val dataSource: DataSource) {

    /**
     * 获取表信息
     */
    fun getTableInfo(tableName: String): TableInfo? {
        return try {
            dataSource.connection.use { connection ->
                val databaseMetaData = connection.metaData
                val catalog = connection.catalog

                // 查询字段信息
                val columns = mutableListOf<ColumnInfo>()
                val columnsResultSet = databaseMetaData.getColumns(catalog, null, tableName, null)

                while (columnsResultSet.next()) {
                    val columnName = columnsResultSet.getString("COLUMN_NAME")
                    val dataType = columnsResultSet.getString("TYPE_NAME")
                    val nullable = columnsResultSet.getInt("NULLABLE") == 1

                    // 排除系统字段
                    if (!isSystemColumn(columnName)) {
                        columns.add(ColumnInfo(columnName, dataType, nullable))
                    }
                }
                columnsResultSet.close()

                // 查询主键信息
                val primaryKeys = mutableSetOf<String>()
                val primaryKeysResultSet = databaseMetaData.getPrimaryKeys(catalog, null, tableName)
                while (primaryKeysResultSet.next()) {
                    primaryKeys.add(primaryKeysResultSet.getString("COLUMN_NAME"))
                }
                primaryKeysResultSet.close()

                // 标记主键字段
                val finalColumns = columns.map { column ->
                    column.copy(isPrimaryKey = primaryKeys.contains(column.columnName))
                }

                if (finalColumns.isNotEmpty()) {
                    TableInfo(tableName, finalColumns)
                } else {
                    log.warn("Table $tableName not found or has no columns")
                    null
                }
            }
        } catch (e: Exception) {
            log.error("Failed to get table info for $tableName", e)
            null
        }
    }

    /**
     * 根据实体类名获取表名
     */
    fun getTableNameFromEntityClass(entityClass: Class<*>): String {
        // 将类名转换为表名：Userinfo -> tbl_userinfo
        val className = entityClass.simpleName
        return "tbl_${className.toLowerCase()}"
    }

    /**
     * 获取所有表名
     */
    fun getAllTableNames(): List<String> {
        return try {
            dataSource.connection.use { connection ->
                val databaseMetaData = connection.metaData
                val catalog = connection.catalog
                val tables = mutableListOf<String>()

                val tablesResultSet = databaseMetaData.getTables(catalog, null, "tbl_%", arrayOf("TABLE"))
                while (tablesResultSet.next()) {
                    val tableName = tablesResultSet.getString("TABLE_NAME")
                    tables.add(tableName)
                }
                tablesResultSet.close()

                tables
            }
        } catch (e: Exception) {
            log.error("Failed to get all table names", e)
            emptyList()
        }
    }

    /**
     * 判断是否为系统字段
     */
    private fun isSystemColumn(columnName: String): Boolean {
        return columnName in listOf("sysCreated", "sysUpdated", "sysDeleted")
    }
}

/**
 * 表信息数据类
 */
data class TableInfo(
    val tableName: String,
    val columns: List<ColumnInfo>
)

/**
 * 字段信息数据类
 */
data class ColumnInfo(
    val columnName: String,
    val dataType: String,
    val isNullable: Boolean,
    val isPrimaryKey: Boolean = false
)
