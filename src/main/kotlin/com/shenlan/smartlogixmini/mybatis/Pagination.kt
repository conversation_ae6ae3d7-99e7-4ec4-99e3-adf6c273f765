package com.shenlan.smartlogixmini.mybatis

import com.github.pagehelper.PageInfo
import com.shenlan.smartlogixmini.auto.BaseSearch

/**
 * 分页信息类
 */
class PaginationInfo<T> private constructor(
    ifPage: Boolean,
    currentPage: Int,
    pageRecord: Int,
    result: List<T>
) {
    val ifPage = ifPage
    val currentPage = if (ifPage) currentPage else 0
    val pageRecord = if (ifPage) pageRecord else 0
    val startRow = if (ifPage && currentPage > 0 && pageRecord > 0) (currentPage - 1) * pageRecord + 1 else 0
    val endRow = if (ifPage && currentPage > 0 && pageRecord > 0) minOf(currentPage * pageRecord, result.size) else 0
    val recordCount = result.size.toLong()
    val pageCount = if (ifPage && pageRecord > 0) ((result.size + pageRecord - 1) / pageRecord) else if (ifPage) 0 else 1
    val result = result

    /**
     * 更换result数据得到新的PaginationInfo对象
     */
    fun <R> withResult(newResult: List<R>): PaginationInfo<R> {
        return if (this.ifPage) {
            ofPage(this.currentPage, this.pageRecord, newResult)
        } else {
            ofList(newResult)
        }
    }

    companion object {
        /**
         * 创建分页信息对象（支持分页）
         */
        fun <T> ofPage(currentPage: Int, pageRecord: Int, result: List<T>): PaginationInfo<T> {
            return PaginationInfo(
                ifPage = true,
                currentPage = currentPage,
                pageRecord = pageRecord,
                result = result
            )
        }

        /**
         * 创建分页信息对象（不分页）
         */
        fun <T> ofList(result: List<T>): PaginationInfo<T> {
            return PaginationInfo(
                ifPage = false,
                currentPage = 0,
                pageRecord = 0,
                result = result
            )
        }

        /**
         * 根据BaseSearch创建分页信息对象
         */
        fun <T> of(search: BaseSearch, result: List<T>): PaginationInfo<T> {
            return if (search.ifPage) {
                ofPage(search.currentPage, search.pageRecord, result)
            } else {
                ofList(result)
            }
        }
    }
}

/**
 * PageInfo扩展属性，用于获取PaginationInfo
 */
val <T> PageInfo<T>.paginationInfo: PaginationInfo<T>
    get() = PaginationInfo.ofPage(
        currentPage = this.pageNum,
        pageRecord = this.pageSize,
        result = this.list
    )

