package com.shenlan.smartlogixmini.mybatis

import com.github.pagehelper.PageInfo

/**
 * 分页信息数据类
 */
data class PaginationInfo(
    // 分页参数
    var currentPage: Int,
    var pageRecord: Int,
    
    // 行范围
    var startRow: Int = 0,
    var endRow: Int = 0,
    
    // 统计信息
    var recordCount: Long = 0,
    var pageCount: Int = 0,
    
    // 结果数据
    var result: Any? = null
)

/**
 * PageInfo扩展属性，用于获取PaginationInfo
 */
val <T> PageInfo<T>.paginationInfo: PaginationInfo
    get() = PaginationInfo(
        currentPage = this.pageNum,
        pageRecord = this.pageSize,
        startRow = this.startRow,
        endRow = this.endRow,
        recordCount = this.total,
        pageCount = this.pages,
        result = this.list
    )

