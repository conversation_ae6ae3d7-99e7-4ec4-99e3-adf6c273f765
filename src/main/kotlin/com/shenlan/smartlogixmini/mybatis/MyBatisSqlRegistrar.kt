package com.shenlan.smartlogixmini.mybatis

import com.shenlan.smartlogixmini.auto.BaseMapper
import com.shenlan.smartlogixmini.auto.BaseSearch
import com.shenlan.smartlogixmini.util.log
import org.apache.ibatis.executor.keygen.Jdbc3KeyGenerator
import org.apache.ibatis.executor.keygen.NoKeyGenerator
import org.apache.ibatis.mapping.*
import org.apache.ibatis.scripting.LanguageDriver
import org.apache.ibatis.session.Configuration
import org.apache.ibatis.session.SqlSessionFactory
import org.springframework.stereotype.Component
import java.lang.reflect.ParameterizedType

/**
 * MyBatis SQL注册器
 * 动态向MyBatis Configuration注册MappedStatement
 */
@Component
class MyBatisSqlRegistrar(sqlSessionFactory: SqlSessionFactory) {

    private val configuration: Configuration = sqlSessionFactory.configuration
    private val languageDriver: LanguageDriver = configuration.defaultScriptingLanguageInstance

    /**
     * 获取MyBatis Configuration
     */
    fun getConfiguration(): Configuration = configuration

    /**
     * 为单个Mapper注册SQL（新增方法）
     */
    fun registerSqlsForMapper(mapperClass: Class<*>): Boolean {
        return try {
            log.debug("Registering SQLs for single mapper: ${mapperClass.simpleName}")

            // 这个方法会被BaseMapperInvocationHandler调用
            // 实际的注册逻辑在registerBaseMapperSqls中
            true
        } catch (e: Exception) {
            log.error("Failed to register SQLs for mapper: ${mapperClass.simpleName}", e)
            false
        }
    }

    /**
     * 检查Mapper是否已注册（新增方法）
     */
    fun isMapperRegistered(mapperClass: Class<*>): Boolean {
        return try {
            val mappedStatementId = "${mapperClass.name}.getList"
            configuration.hasStatement(mappedStatementId)
        } catch (e: Exception) {
            log.debug("Error checking mapper registration for ${mapperClass.simpleName}: ${e.message}")
            false
        }
    }

    /**
     * 注册SQL语句到MyBatis
     */
    fun registerSql(
        mapperClass: Class<*>,
        methodName: String,
        sql: String,
        sqlCommandType: SqlCommandType,
        parameterType: Class<*>? = null,
        resultType: Class<*>? = null
    ) {
        try {
            val mappedStatementId = "${mapperClass.name}.$methodName"

            // 检查是否已经注册
            if (configuration.hasStatement(mappedStatementId)) {
                log.debug("MappedStatement $mappedStatementId already exists, skipping registration")
                return
            }

            // 创建SqlSource
            val sqlSource = languageDriver.createSqlSource(configuration, sql, parameterType)

            // 构建MappedStatement
            val builder = MappedStatement.Builder(
                configuration,
                mappedStatementId,
                sqlSource,
                sqlCommandType
            )

            // 设置参数类型
            parameterType?.let {
                // 创建参数映射
                val parameterMap = ParameterMap.Builder(configuration, mappedStatementId, it, emptyList()).build()
                builder.parameterMap(parameterMap)
            }

            // 设置返回类型和结果映射
            resultType?.let {
                // 创建结果映射
                val resultMap = ResultMap.Builder(configuration, mappedStatementId, it, emptyList()).build()
                builder.resultMaps(listOf(resultMap))
            }

            // 设置其他属性
            builder.statementType(StatementType.PREPARED)
            builder.resultSetType(ResultSetType.FORWARD_ONLY)
            builder.flushCacheRequired(sqlCommandType != SqlCommandType.SELECT)
            builder.useCache(sqlCommandType == SqlCommandType.SELECT)

            // 设置主键生成器
            when (sqlCommandType) {
                SqlCommandType.INSERT -> {
                    builder.keyGenerator(Jdbc3KeyGenerator.INSTANCE)
                    builder.keyProperty("id")
                }
                else -> builder.keyGenerator(NoKeyGenerator.INSTANCE)
            }

            // 注册到Configuration
            val mappedStatement = builder.build()
            configuration.addMappedStatement(mappedStatement)

            log.debug("Successfully registered SQL: $mappedStatementId")

        } catch (e: Exception) {
            log.error("Failed to register SQL for ${mapperClass.simpleName}.$methodName", e)
            throw e
        }
    }

    /**
     * 为BaseMapper的所有方法注册SQL
     */
    fun registerBaseMapperSqls(
        mapperClass: Class<*>,
        tableInfo: TableInfo,
        sqlGenerator: SqlGenerator
    ) {
        // 获取实体类型
        val entityClass = getEntityClassFromMapper(mapperClass)

        if (entityClass == null) {
            log.warn("Cannot determine entity class for mapper: ${mapperClass.simpleName}")
            return
        }

        // 注册各种SQL方法
        registerGetListSql(mapperClass, tableInfo, sqlGenerator, entityClass)
        registerGetInfoSql(mapperClass, tableInfo, sqlGenerator, entityClass)
        registerGetListByIdsSql(mapperClass, tableInfo, sqlGenerator, entityClass)
        registerInsertSql(mapperClass, tableInfo, sqlGenerator, entityClass)
        registerDeleteSql(mapperClass, tableInfo, sqlGenerator)
        registerDeleteLogicSql(mapperClass, tableInfo, sqlGenerator)
        registerInsertListSql(mapperClass, tableInfo, sqlGenerator)
        registerGetListByPidSql(mapperClass, tableInfo, sqlGenerator, entityClass)
    }

    /**
     * 注册getList方法
     */
    private fun registerGetListSql(
        mapperClass: Class<*>,
        tableInfo: TableInfo,
        sqlGenerator: SqlGenerator,
        entityClass: Class<*>
    ) {
        val sql = sqlGenerator.generateSelectAllSql(tableInfo)
        registerSql(
            mapperClass = mapperClass,
            methodName = "getList",
            sql = sql,
            sqlCommandType = SqlCommandType.SELECT,
            parameterType = BaseSearch::class.java,
            resultType = entityClass
        )
        logGeneratedSql("getList", sql, tableInfo.tableName)
    }

    /**
     * 注册getInfo方法
     */
    private fun registerGetInfoSql(
        mapperClass: Class<*>,
        tableInfo: TableInfo,
        sqlGenerator: SqlGenerator,
        entityClass: Class<*>
    ) {
        val sql = sqlGenerator.generateSelectByIdSql(tableInfo)
        registerSql(
            mapperClass = mapperClass,
            methodName = "getInfo",
            sql = sql,
            sqlCommandType = SqlCommandType.SELECT,
            parameterType = String::class.java,
            resultType = entityClass
        )
        logGeneratedSql("getInfo", sql, tableInfo.tableName)
    }

    /**
     * 注册insert方法
     */
    private fun registerInsertSql(
        mapperClass: Class<*>,
        tableInfo: TableInfo,
        sqlGenerator: SqlGenerator,
        entityClass: Class<*>
    ) {
        val sql = sqlGenerator.generateInsertSql(tableInfo)
        registerSql(
            mapperClass = mapperClass,
            methodName = "insert",
            sql = sql,
            sqlCommandType = SqlCommandType.INSERT,
            parameterType = entityClass,
            resultType = Int::class.java
        )
        logGeneratedSql("insert", sql, tableInfo.tableName)
    }

    /**
     * 注册delete方法
     */
    private fun registerDeleteSql(
        mapperClass: Class<*>,
        tableInfo: TableInfo,
        sqlGenerator: SqlGenerator
    ) {
        val sql = sqlGenerator.generateDeleteSql(tableInfo)
        registerSql(
            mapperClass = mapperClass,
            methodName = "delete",
            sql = sql,
            sqlCommandType = SqlCommandType.DELETE,
            parameterType = String::class.java,
            resultType = Int::class.java
        )
        logGeneratedSql("delete", sql, tableInfo.tableName)
    }

    /**
     * 注册deleteLogic方法
     */
    private fun registerDeleteLogicSql(
        mapperClass: Class<*>,
        tableInfo: TableInfo,
        sqlGenerator: SqlGenerator
    ) {
        val sql = sqlGenerator.generateDeleteLogicSql(tableInfo)
        registerSql(
            mapperClass = mapperClass,
            methodName = "deleteLogic",
            sql = sql,
            sqlCommandType = SqlCommandType.UPDATE,
            parameterType = String::class.java,
            resultType = Int::class.java
        )
        logGeneratedSql("deleteLogic", sql, tableInfo.tableName)
    }

    /**
     * 注册insertList方法
     */
    private fun registerInsertListSql(
        mapperClass: Class<*>,
        tableInfo: TableInfo,
        sqlGenerator: SqlGenerator
    ) {
        val sql = sqlGenerator.generateInsertListSql(tableInfo)
        registerSql(
            mapperClass = mapperClass,
            methodName = "insertList",
            sql = sql,
            sqlCommandType = SqlCommandType.INSERT,
            parameterType = List::class.java,
            resultType = Int::class.java
        )
        logGeneratedSql("insertList", sql, tableInfo.tableName)
    }

    /**
     * 注册getListByIds方法
     */
    private fun registerGetListByIdsSql(
        mapperClass: Class<*>,
        tableInfo: TableInfo,
        sqlGenerator: SqlGenerator,
        entityClass: Class<*>
    ) {
        val sql = sqlGenerator.generateSelectByIdsSql(tableInfo)
        registerSql(
            mapperClass = mapperClass,
            methodName = "getListByIds",
            sql = sql,
            sqlCommandType = SqlCommandType.SELECT,
            parameterType = List::class.java,
            resultType = entityClass
        )
        logGeneratedSql("getListByIds", sql, tableInfo.tableName)
    }

    /**
     * 注册getListByPid方法
     */
    private fun registerGetListByPidSql(
        mapperClass: Class<*>,
        tableInfo: TableInfo,
        sqlGenerator: SqlGenerator,
        entityClass: Class<*>
    ) {
        val sql = sqlGenerator.generateSelectByPidSql(tableInfo)
        registerSql(
            mapperClass = mapperClass,
            methodName = "getListByPid",
            sql = sql,
            sqlCommandType = SqlCommandType.SELECT,
            parameterType = String::class.java,
            resultType = entityClass
        )
        logGeneratedSql("getListByPid", sql, tableInfo.tableName)
    }

    /**
     * 从Mapper接口获取实体类型
     */
    private fun getEntityClassFromMapper(mapperClass: Class<*>): Class<*>? {
        return try {
            val genericInterfaces = mapperClass.genericInterfaces
            for (genericInterface in genericInterfaces) {
                if (genericInterface is ParameterizedType) {
                    val rawType = genericInterface.rawType
                    if (rawType == BaseMapper::class.java) {
                        val actualTypeArguments = genericInterface.actualTypeArguments
                        if (actualTypeArguments.isNotEmpty()) {
                            return actualTypeArguments[0] as Class<*>
                        }
                    }
                }
            }
            null
        } catch (e: Exception) {
            log.error("Failed to get entity class from mapper: ${mapperClass.simpleName}", e)
            null
        }
    }

    /**
     * 记录生成的SQL
     */
    private fun logGeneratedSql(methodName: String, sql: String, tableName: String) {
        log.debug("Generated SQL for $tableName.$methodName: $sql")
    }
}
