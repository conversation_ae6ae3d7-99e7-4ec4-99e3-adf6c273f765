package com.shenlan.smartlogixmini.mybatis

import com.shenlan.smartlogixmini.auto.BaseMapper
import com.shenlan.smartlogixmini.util.log
import org.springframework.beans.factory.config.BeanPostProcessor
import org.springframework.stereotype.Component

/**
 * BaseMapper Bean后处理器
 * 在Spring容器初始化阶段将BaseMapper实现类替换为代理对象
 */
@Component
class BaseMapperBeanPostProcessor(
    private val baseMapperProxyFactory: BaseMapperProxyFactory
) : BeanPostProcessor {

    override fun postProcessAfterInitialization(bean: Any, beanName: String): Any {
        // 检查Bean是否为BaseMapper实现类
        if (isBaseMapperBean(bean)) {
            try {
                log.debug("Processing BaseMapper bean: $beanName")

                // 获取Bean的接口类型
                val mapperInterface = getBaseMapperSubinterface(bean)
                if (mapperInterface != null) {
                    // 创建代理对象
                    val proxy = baseMapperProxyFactory.createProxy(mapperInterface, bean)
                    log.info("Created proxy for BaseMapper: ${mapperInterface.simpleName}")
                    return proxy
                } else {
                    log.warn("Cannot determine mapper interface for bean: $beanName")
                }
            } catch (e: Exception) {
                log.error("Failed to create proxy for BaseMapper bean: $beanName", e)
                // 如果代理创建失败，返回原始Bean
            }
        }

        return bean
    }

    /**
     * 检查Bean是否为BaseMapper实现类
     * 支持直接实现和间接继承两种情况
     */
    private fun isBaseMapperBean(bean: Any): Boolean {
        return bean.javaClass.interfaces.any { interfaceClass ->
            interfaceClass.isInterface && BaseMapper::class.java.isAssignableFrom(interfaceClass)
        }
    }

    /**
     * 检查指定类是否为BaseMapper的子接口
     */
    fun isBaseMapperSubinterface(clazz: Class<*>): Boolean {
        return clazz.isInterface && BaseMapper::class.java.isAssignableFrom(clazz)
    }

    /**
     * 获取Bean实现的BaseMapper子接口
     */
    private fun getBaseMapperSubinterface(bean: Any): Class<*>? {
        return bean.javaClass.interfaces.firstOrNull { interfaceClass ->
            isBaseMapperSubinterface(interfaceClass)
        }
    }
}
