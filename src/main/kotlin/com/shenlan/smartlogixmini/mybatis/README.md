# MyBatis自动化处理模块

## 功能概述

基于数据库表结构自动生成并注册MyBatis SQL的完整解决方案，实现BaseMapper接口的动态SQL处理。

## 核心组件

### SQL生成与注册
- **SqlGenerator** - 根据表元数据生成标准CRUD SQL语句
- **MyBatisSqlRegistrar** - 动态向MyBatis Configuration注册MappedStatement
- **SqlRegistrationCache** - SQL注册状态缓存管理

### 代理与拦截
- **BaseMapperBeanPostProcessor** - Spring Bean后处理器，替换BaseMapper为代理对象
- **BaseMapperProxyFactory** - BaseMapper代理对象工厂
- **BaseMapperInvocationHandler** - 方法调用拦截器，实现懒加载SQL注册

### 元数据服务
- **DatabaseMetadataService** - 数据库表结构信息查询服务
- **Pagination** - 分页信息数据类和PageInfo转换扩展

## 工作机制

1. **Spring启动阶段**：Bean后处理器识别BaseMapper实现类并创建代理对象
2. **方法调用时**：拦截器检查SQL注册状态
3. **懒加载注册**：获取表结构→生成SQL→注册到MyBatis
4. **执行查询**：委托给原始Mapper执行数据库操作

## 支持的方法

- `getList()` - 列表查询
- `getInfo()` - 单条查询
- `getListByIds()` - 批量ID查询
- `getListByPid()` - 父子关系查询
- `insert()` / `insertList()` - 单条/批量插入
- `delete()` / `deleteLogic()` - 物理/逻辑删除

## 特性

- **零配置**：无需手写SQL和XML配置
- **表结构驱动**：基于数据库表自动适配
- **懒加载**：首次调用时才生成并注册SQL
- **线程安全**：双重检查锁定保证并发安全
- **系统字段过滤**：自动排除sysCreated、sysUpdated、sysDeleted
