package com.shenlan.smartlogixmini.mybatis

import com.shenlan.smartlogixmini.util.log
import org.springframework.stereotype.Component

/**
 * SQL生成器
 * 根据表元数据生成标准CRUD SQL语句
 */
@Component
class SqlGenerator {

    /**
     * 生成查询列表SQL
     */
    fun generateSelectAllSql(tableInfo: TableInfo): String {
        return "SELECT * FROM ${tableInfo.tableName} WHERE sysDeleted = 0"
    }

    /**
     * 生成根据ID查询SQL
     */
    fun generateSelectByIdSql(tableInfo: TableInfo): String {
        return "SELECT * FROM ${tableInfo.tableName} WHERE id = #{id} AND sysDeleted = 0"
    }

    /**
     * 生成插入SQL
     */
    fun generateInsertSql(tableInfo: TableInfo): String {
        val columns = tableInfo.columns
        val columnNames = columns.joinToString(",") { it.columnName }
        val columnValues = columns.joinToString(",") { "#{${it.columnName}}" }

        return "INSERT INTO ${tableInfo.tableName} ($columnNames) VALUES ($columnValues)"
    }

    /**
     * 生成物理删除SQL
     */
    fun generateDeleteSql(tableInfo: TableInfo): String {
        return "DELETE FROM ${tableInfo.tableName} WHERE id = #{id}"
    }

    /**
     * 生成逻辑删除SQL
     */
    fun generateDeleteLogicSql(tableInfo: TableInfo): String {
        return "UPDATE ${tableInfo.tableName} SET sysDeleted = 1 WHERE id = #{id}"
    }

    /**
     * 生成批量插入SQL
     */
    fun generateInsertListSql(tableInfo: TableInfo): String {
        val columns = tableInfo.columns
        val columnNames = columns.joinToString(",") { it.columnName }
        val columnValues = columns.joinToString(",") { "#{item.${it.columnName}}" }

        return """
            <script>
                <choose>
                    <when test="list.size() > 0">
                        INSERT INTO ${tableInfo.tableName} ($columnNames) VALUES 
                        <foreach collection="list" item="item" separator=",">
                            ($columnValues)
                        </foreach>
                    </when>
                    <otherwise>
                        SELECT 0
                    </otherwise>
                </choose>
            </script>
        """.trimIndent()
    }

    /**
     * 生成根据父ID查询SQL（用于树形结构）
     */
    fun generateSelectByPidSql(tableInfo: TableInfo): String {
        // 假设第二个字段是父ID字段
        val pidColumn = if (tableInfo.columns.size > 1) {
            tableInfo.columns[1].columnName
        } else {
            "pid" // 默认父ID字段名
        }

        return "SELECT * FROM ${tableInfo.tableName} WHERE $pidColumn = #{pid} AND sysDeleted = 0"
    }
}
