package com.shenlan.smartlogixmini.config

import com.shenlan.smartlogixmini.auto.BusinessException
import com.shenlan.smartlogixmini.auto.OpenMapper
import com.shenlan.smartlogixmini.auto.PersonnelService
import com.shenlan.smartlogixmini.auto.Result
import com.shenlan.smartlogixmini.util.*
import org.apache.catalina.connector.Connector
import org.springframework.beans.factory.BeanInitializationException
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory
import org.springframework.boot.web.server.MimeMappings
import org.springframework.boot.web.server.WebServerFactory
import org.springframework.boot.web.server.WebServerFactoryCustomizer
import org.springframework.boot.web.servlet.ServletContextInitializer
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.env.Environment
import org.springframework.core.io.Resource
import org.springframework.format.FormatterRegistry
import org.springframework.format.datetime.DateFormatter
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.builders.WebSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter
import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.session.SessionRegistryImpl
import org.springframework.security.core.userdetails.User
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.web.session.HttpSessionEventPublisher
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import java.io.File
import java.io.UnsupportedEncodingException
import java.net.URLDecoder.decode
import java.nio.charset.StandardCharsets
import java.nio.file.Paths
import javax.annotation.PostConstruct
import javax.servlet.ServletContext
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * 登录方式枚举
 */
enum class LoginMethod {
    WECHAT_PHONE,
    PHONE_CODE,
    USERNAME_PASSWORD
}

/**
 * Session认证相关常量
 */
object SessionConstants {
    const val USERNAME_PARAMETER = "j_username"
    const val PASSWORD_PARAMETER = "j_password"
}

/**
 * Web应用配置类
 * 负责Web服务器配置、静态资源处理和格式化器配置
 */
@Configuration
class WebConfig(
    private val env: Environment
) : ServletContextInitializer, WebServerFactoryCustomizer<WebServerFactory>, WebMvcConfigurer {

    @Value("\${server.http.port:-1}")
    private val httpPort: Int = -1

    // Web服务器配置
    override fun onStartup(servletContext: ServletContext?) {
        log.info("Web application configuration, using profiles: {}", env.activeProfiles as Array<*>)
    }

    override fun customize(server: WebServerFactory) {
        setMimeMappings(server)
        setLocationForStaticAssets(server)
    }

    // 静态资源处理
    override fun addResourceHandlers(registry: ResourceHandlerRegistry) {
        log.info("add uploadPath: {}", "file:${AppPro.uploadPath}")

        // 支持打包前后不同的路径结构
        registry.addResourceHandler("/${AppPro.uploadDir}/**")
            .addResourceLocations(
                "classpath:/${AppPro.uploadDir}/",     // 打包后：jar包内的upload目录
                "file:./${AppPro.uploadDir}/"         // 打包前：项目根目录下的upload目录
            )
            .setCachePeriod(0)           // 禁用缓存
            .resourceChain(false)        // 禁用资源链

        // 添加音频文件资源处理器，支持打包前后不同的路径结构
        registry.addResourceHandler("/music/**")
            .addResourceLocations(
                "classpath:/music/",     // 打包后：jar包内的music目录
                "file:./www/music/"      // 打包前：项目根目录下的www/music目录
            )
            .setCachePeriod(0)           // 禁用缓存避免Range请求问题
            .resourceChain(false)        // 禁用资源链
    }

    // 格式化器配置
    override fun addFormatters(registry: FormatterRegistry) {
        val dateFormatter = DateFormatter("yyyy-MM-dd HH:mm")
        registry.addFormatter(dateFormatter)
    }

    // MIME类型映射配置
    private fun setMimeMappings(server: WebServerFactory) {
        if (server is ConfigurableServletWebServerFactory) {
            val mappings = MimeMappings(MimeMappings.DEFAULT)
            val charset = StandardCharsets.UTF_8.name().toLowerCase()
            mappings.add("html", "${MediaType.TEXT_HTML_VALUE};charset=$charset")
            mappings.add("json", "${MediaType.APPLICATION_JSON_VALUE};charset=$charset")
            server.setMimeMappings(mappings)
        }
    }

    // 静态资源根目录配置
    private fun setLocationForStaticAssets(server: WebServerFactory) {
        if (server is ConfigurableServletWebServerFactory) {
            val prefixPath = resolvePathPrefix()
            val root = File("${prefixPath}www/")
            if (root.exists() && root.isDirectory) {
                server.setDocumentRoot(root)
            }
        }
    }

    // 路径前缀解析
    private fun resolvePathPrefix(): String {
        val fullExecutablePath = try {
            decode(this.javaClass.getResource("")!!.path, StandardCharsets.UTF_8.name())
        } catch (e: UnsupportedEncodingException) {
            this.javaClass.getResource("")!!.path
        }

        val rootPath = Paths.get(".").toUri().normalize().path
        val extractedPath = fullExecutablePath.replace(rootPath, "")
        val extractionEndIndex = extractedPath.indexOf("")
        return if (extractionEndIndex <= 0) "" else extractedPath.substring(0, extractionEndIndex)
    }

    /**
     * 配置Tomcat服务器工厂，添加额外的HTTP连接器
     * HTTP端口：从配置文件读取，默认为-1（不开启）
     * 仅当httpPort不为-1时才注册此Bean
     */
    @Bean
    @ConditionalOnExpression("'\${server.http.port:-1}' != '-1'")
    fun servletContainer(): TomcatServletWebServerFactory {
        return TomcatServletWebServerFactory().apply {
            // 添加HTTP连接器
            val httpConnector = Connector("org.apache.coyote.http11.Http11NioProtocol").apply {
                scheme = "http"
                port = httpPort
                secure = false
            }
            addAdditionalTomcatConnectors(httpConnector)
        }
    }
}

/**
 * Spring Security安全配置
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
class SecurityConfiguration(
    private val authenticationManagerBuilder: AuthenticationManagerBuilder,
    private val personnelService: PersonnelService,
    private val openMapper: OpenMapper
) : WebSecurityConfigurerAdapter() {

    companion object {
        /** 登录验证码存储Map */
        var loginCodeMap = hashMapOf<String, VerificationCode>()
    }

    @Value("\${spring.profiles.active}")
    private lateinit var active: String

    // 认证管理器配置
    @PostConstruct
    fun init() {
        try {
            authenticationManagerBuilder
                .userDetailsService(object : UserDetailsService {
                    override fun loadUserByUsername(username: String): UserDetails {
                        return <EMAIL>(username)
                    }
                })
                .passwordEncoder(BCryptPasswordEncoder())
        } catch (e: Exception) {
            throw BeanInitializationException("Security configuration failed", e)
        }
    }

    // 用户认证核心逻辑
    private fun loadUserByUsername(username: String): UserDetails {
        log.info("Authenticating user: {}", username)

        // 先尝试按手机号查询，再按用户名查询
        var personnel = personnelService.getPersonnelByPhone(username)
        var isPhoneLogin = personnel != null

        if (personnel == null) {
            // 手机号查不到，尝试按用户名查询
            personnel = personnelService.getPersonnelByUsername(username)
            isPhoneLogin = false
        }

        if (personnel == null) {
            log.error("User not found: {}", username)
            throw UsernameNotFoundException("账号或密码错误")
        }

        // 获取登录方式和密码
        val loginMethod: LoginMethod
        val password = if (isPhoneLogin) {
            // 手机号登录：使用验证码
            val loginCode = loginCodeMap[username] // 不删除，只获取
            var code = loginCode?.code
            // 手机号登录统一使用PHONE_CODE方式（包含微信手机号和短信验证码）
            loginMethod = LoginMethod.PHONE_CODE

            // 非生产环境支持demo123密码登录
            if (active != "prod" && code == null) {
                log.info("Non-production environment: enabling demo123 password for user: {}", username)
                code = "demo123"
            } else {
                // 使用通用验证码状态校验函数
                val verifyResult = verifyVerificationCode(username, loginCodeMap)

                when (verifyResult) {
                    VerificationCodeResult.VERIFICATION_CODE_NOT_FOUND -> {
                        log.error("Verification code not found for user: {}", username)
                        throw BusinessException(VerificationCodeResult.VERIFICATION_CODE_NOT_FOUND)
                    }
                    VerificationCodeResult.VERIFICATION_CODE_EXPIRED -> {
                        log.error("Verification code expired for user: {}", username)
                        throw BusinessException(VerificationCodeResult.VERIFICATION_CODE_EXPIRED)
                    }
                    VerificationCodeResult.VERIFICATION_CODE_EXCEEDED_LIMIT -> {
                        log.error("Verification code exceeded limit for user: {}", username)
                        throw BusinessException(VerificationCodeResult.VERIFICATION_CODE_EXCEEDED_LIMIT)
                    }
                    VerificationCodeResult.SUCCESS -> {
                        // 验证码存在且有效，获取验证码内容
                        code = loginCode?.code
                    }
                    else -> {
                        log.error("Verification code validation failed for user: {}", username)
                        throw BusinessException(VerificationCodeResult.SYSTEM_ERROR)
                    }
                }
            }

            code
        } else {
            // 用户名登录：使用存储的密码（明文存储）
            loginMethod = LoginMethod.USERNAME_PASSWORD
            if (personnel.password.isEmpty()) {
                log.error("User has no password set: {}", username)
                throw UsernameNotFoundException("账号或密码错误")
            }
            personnel.password
        }

        // 设置人员的当前登录方式
        personnel.currentLoginMethod = loginMethod

        return AppUser(
            username = username,
            password = BCryptPasswordEncoder().encode(password),
            personnelId = personnel.id,
            loginMethod = loginMethod
        )
    }

    /**
     * 获取客户端真实IP地址
     * 支持代理服务器和负载均衡器
     */
    private fun getClientIpAddress(request: HttpServletRequest): String {
        // 检查各种代理头
        val headers = listOf(
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        )

        for (header in headers) {
            val ip = request.getHeader(header)
            if (!ip.isNullOrEmpty() && !"unknown".equals(ip, ignoreCase = true)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                return ip.split(",")[0].trim()
            }
        }

        // 如果没有代理，直接获取远程地址
        return request.remoteAddr ?: ""
    }


    // Web安全配置
    override fun configure(web: WebSecurity) {
        web.ignoring()
            .antMatchers(HttpMethod.OPTIONS, "/**")
            .antMatchers("/assets/**/*.{js,html}")
            .antMatchers("/i18n/**")
            .antMatchers("/api/file/**")
            .antMatchers("/api/maintain/**")
            .antMatchers("/music/**")
    }

    // HTTP安全配置
    override fun configure(http: HttpSecurity) {
        http
            .csrf().disable()
            .exceptionHandling()
            .authenticationEntryPoint { _, response, _ ->
                response?.apply {
                    characterEncoding = "UTF-8"
                    contentType = "application/json; charset=utf-8"
                    status = HttpServletResponse.SC_UNAUTHORIZED
                    writer?.print(Result.getError("未登录或登录失效").toJsonString)
                }
            }
            .and()
            .formLogin()
            .loginProcessingUrl("/api/authentication")
            .successHandler { request, response, _ ->
                response?.apply {
                    characterEncoding = "UTF-8"
                    contentType = "application/json; charset=utf-8"

                    // 登录成功后删除验证码
                    val username = request.getParameter(SessionConstants.USERNAME_PARAMETER)
                    if (!username.isNullOrEmpty()) {
                        loginCodeMap.remove(username)
                        log.info("Login successful, verification code removed for user: {}", username)
                    }

                    writer?.print(Result.getSuccess(getUser()).toJsonString)
                }
            }
            .failureHandler { _, response, _ ->
                response?.apply {
                    characterEncoding = "UTF-8"
                    contentType = "application/json; charset=utf-8"
                    writer?.print(Result.getError("账号或密码错误").toJsonString)
                }
            }
            .usernameParameter(SessionConstants.USERNAME_PARAMETER)
            .passwordParameter(SessionConstants.PASSWORD_PARAMETER)
            .permitAll()
            .and()
            .logout()
            .logoutUrl("/api/logout")
            .logoutSuccessHandler { _, response, _ ->
                response?.apply {
                    characterEncoding = "UTF-8"
                    contentType = "application/json; charset=utf-8"
                    status = HttpServletResponse.SC_OK
                    writer?.print(Result.getSuccessInfo("退出登录成功").toJsonString)
                }
            }
            .permitAll()
            .and()
            .headers()
            .frameOptions()
            .disable()
            .and()
            .authorizeRequests()
            .antMatchers("/api/open/getAccount").authenticated()
            .antMatchers("/api/open/**").permitAll()
            .antMatchers("/api/authentication").permitAll()
            .antMatchers("/api/logout").permitAll()
            .antMatchers("/api/file/**").permitAll()
            .antMatchers("/api/maintain/**").permitAll()
            .antMatchers("/music/**").permitAll()
            .antMatchers("/api/wechatpay/**").permitAll()
            .antMatchers("/api/commute*/**").permitAll()
            .antMatchers("/api/Content/getCurrentSolarTermImage").permitAll()
            .antMatchers("/api/WechatPushMessage/handle").permitAll()
            .antMatchers("/api/Content/preview/*").permitAll()
            .antMatchers("/api/takeoutmenu/reset").permitAll()
            .antMatchers("/api/**").authenticated()
    }
}

/**
 * 应用用户类
 * 扩展Spring Security的User类，添加应用特定属性
 */
class AppUser(
    username: String,
    password: String,
    authorities: MutableCollection<out GrantedAuthority> = mutableListOf(),
    val personnelId: String,
    var wechatSessionKey: String = "",
    var loginMethod: LoginMethod
) : User(username, password, authorities)

/**
 * SPA前端路由转发控制器
 * 处理单页应用的路由转发
 */
@Controller
class SpaForwardController(
    private val applicationContext: ApplicationContext
) {

    @RequestMapping(
        value = ["/{path:[^.]*}", "/**/{path:[^.]*}"],
        method = [RequestMethod.GET]
    )
    fun forward(
        request: HttpServletRequest,
        response: HttpServletResponse,
        @PathVariable(required = false) path: String?
    ) {
        val uri = request.requestURI

        // 排除API和WebSocket路径
        if (uri.startsWith("/api") || uri.startsWith("/ws") || uri.startsWith("/tiles") || uri.startsWith("/music")) {
            response.sendError(HttpStatus.NOT_FOUND.value())
            return
        }

        // 查找index.html资源
        val indexResource = findIndexResource()

        if (indexResource != null && indexResource.exists() && indexResource.isReadable) {
            serveIndexHtml(response, indexResource)
        } else {
            log.error("index.html not found in any location")
            response.sendError(HttpStatus.NOT_FOUND.value())
        }
    }

    // 查找index.html资源
    private fun findIndexResource(): Resource? {
        val locations = listOf(
            "classpath:/static/index.html",
            "classpath:/index.html",
            "file:./www/index.html",
            "file:www/index.html"
        )

        for (location in locations) {
            val resource = applicationContext.getResource(location)
            if (resource.exists() && resource.isReadable) {
                return resource
            }
        }

        return null
    }

    // 提供index.html服务
    private fun serveIndexHtml(response: HttpServletResponse, indexResource: Resource) {
        response.contentType = "text/html;charset=UTF-8"

        try {
            indexResource.inputStream.use { input ->
                response.outputStream.use { output ->
                    input.copyTo(output)
                }
            }
        } catch (e: Exception) {
            log.error("Error serving index.html: {}", e.message)
            errorlog(e)
            response.sendError(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Error serving index.html")
        }
    }
}
