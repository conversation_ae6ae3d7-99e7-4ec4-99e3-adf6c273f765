package com.shenlan.smartlogixmini.config

import com.shenlan.smartlogixmini.auto.*
import com.shenlan.smartlogixmini.util.*
import org.springframework.beans.factory.BeanInitializationException
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.web.server.MimeMappings
import org.springframework.boot.web.server.WebServerFactory
import org.springframework.boot.web.server.WebServerFactoryCustomizer
import org.springframework.boot.web.servlet.FilterRegistrationBean
import org.springframework.boot.web.servlet.ServletContextInitializer
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.Ordered
import org.springframework.core.env.Environment
import org.springframework.core.io.Resource
import org.springframework.format.FormatterRegistry
import org.springframework.format.datetime.DateFormatter
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.builders.WebSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter
import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.core.session.SessionRegistryImpl
import org.springframework.security.core.userdetails.User
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken
import org.springframework.security.web.session.HttpSessionEventPublisher
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import java.io.File
import java.io.UnsupportedEncodingException
import java.net.URLDecoder.decode
import java.nio.charset.StandardCharsets
import java.nio.file.Paths
import java.util.*
import javax.annotation.PostConstruct
import javax.servlet.*
import javax.servlet.http.*

/**
 * Session认证相关常量
 */
object SessionConstants {
    const val SESSION_ID_HEADER = "Session-Id"
    const val SESSION_ID_AUTH_ATTR = "SESSION_ID_AUTH"
    const val JSESSIONID_COOKIE = "JSESSIONID"
}

/**
 * 登录验证码数据类
 */
data class LoginCode(
    var code: String, // 验证码
    var createTime: Long
) {
    // 是否超时（5分钟）
    fun isTimeOut(): Boolean {
        return System.currentTimeMillis() - createTime > 1000 * 60 * 5
    }

    companion object {
        var loginCodeMap = hashMapOf<String, LoginCode>()
    }
}

/**
 * 虚拟HttpSession实现
 * 用于Session-Id认证场景，避免创建真实服务器Session资源
 */
class VirtualHttpSession(
    private val sessionId: String,
    private val servletContext: ServletContext
) : HttpSession {

    private val creationTime = System.currentTimeMillis()
    private val attributes = mutableMapOf<String, Any>()

    override fun getId(): String = sessionId
    override fun getCreationTime(): Long = creationTime
    override fun getLastAccessedTime(): Long = System.currentTimeMillis()
    override fun getServletContext(): ServletContext = servletContext
    override fun setMaxInactiveInterval(interval: Int) {} // 虚拟Session不需要超时管理
    override fun getMaxInactiveInterval(): Int = Int.MAX_VALUE
    override fun getAttribute(name: String?): Any? = attributes[name]
    @Deprecated("Deprecated in Java")
    override fun getValue(name: String?): Any? = getAttribute(name)
    override fun getAttributeNames(): Enumeration<String> = Collections.enumeration(attributes.keys)
    @Deprecated("Deprecated in Java")
    override fun getValueNames(): Array<String> = attributes.keys.toTypedArray()

    override fun setAttribute(name: String?, value: Any?) {
        if (name != null && value != null) {
            attributes[name] = value
        }
    }

    @Deprecated("Deprecated in Java")
    override fun putValue(name: String?, value: Any?) = setAttribute(name, value)
    override fun removeAttribute(name: String?) { name?.let { attributes.remove(it) } }
    @Deprecated("Deprecated in Java")
    override fun removeValue(name: String?) = removeAttribute(name)
    override fun invalidate() { attributes.clear() }
    override fun isNew(): Boolean = false

    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun getSessionContext(): HttpSessionContext? = null
}

/**
 * Session-Id请求头转JSESSIONID Cookie过滤器
 * 将请求头中的Session-Id值转换为请求中的JSESSIONID Cookie，以便Spring Security识别会话
 */
class SessionIdToJSESSIONIDFilter : Filter {

    override fun init(filterConfig: FilterConfig?) {}
    override fun destroy() {}

    override fun doFilter(request: ServletRequest, response: ServletResponse, chain: FilterChain) {
        val httpRequest = request as HttpServletRequest
        val sessionId = httpRequest.getHeader(SessionConstants.SESSION_ID_HEADER)

        if (!sessionId.isNullOrEmpty()) {
            // 设置Session-Id认证标记
            httpRequest.setAttribute(SessionConstants.SESSION_ID_AUTH_ATTR, true)

            // 检查是否需要添加JSESSIONID Cookie
            val existingJSessionId = httpRequest.cookies?.find { it.name == SessionConstants.JSESSIONID_COOKIE }?.value

            if (existingJSessionId != sessionId) {
                val cookie = Cookie(SessionConstants.JSESSIONID_COOKIE, sessionId).apply {
                    path = "/"
                    isHttpOnly = true
                }

                log.info("Converting Session-Id to JSESSIONID for session: {}", sessionId)
                val wrappedRequest = CookieHttpServletRequestWrapper(httpRequest, cookie)
                chain.doFilter(wrappedRequest, response)
                return
            }
        }

        chain.doFilter(request, response)
    }
}

/**
 * Cookie包装器
 * 用于向HttpServletRequest中添加Cookie
 */
class CookieHttpServletRequestWrapper(
    request: HttpServletRequest,
    private val additionalCookie: Cookie
) : HttpServletRequestWrapper(request) {

    override fun getCookies(): Array<Cookie>? {
        val originalCookies = super.getCookies()

        return if (originalCookies == null) {
            arrayOf(additionalCookie)
        } else {
            val existingCookie = originalCookies.find { it.name == additionalCookie.name }
            if (existingCookie == null) {
                originalCookies + additionalCookie
            } else {
                originalCookies.map {
                    if (it.name == additionalCookie.name) additionalCookie else it
                }.toTypedArray()
            }
        }
    }

    override fun getRequestedSessionId(): String? {
        return if (additionalCookie.name == SessionConstants.JSESSIONID_COOKIE) {
            additionalCookie.value
        } else {
            super.getRequestedSessionId()
        }
    }

    override fun isRequestedSessionIdFromCookie(): Boolean {
        return if (additionalCookie.name == SessionConstants.JSESSIONID_COOKIE) {
            true
        } else {
            super.isRequestedSessionIdFromCookie()
        }
    }

    override fun getSession(create: Boolean): HttpSession? {
        // 对于Session-Id认证，避免创建新Session
        if (additionalCookie.name == SessionConstants.JSESSIONID_COOKIE) {
            val isSessionIdAuth = getAttribute(SessionConstants.SESSION_ID_AUTH_ATTR) as? Boolean ?: false
            if (isSessionIdAuth) {
                val existingSession = super.getSession(false)
                return existingSession ?: VirtualHttpSession(additionalCookie.value, servletContext)
            }
        }

        return super.getSession(create)
    }

    override fun getSession(): HttpSession? {
        // Session-Id认证情况下不默认创建新Session
        val isSessionIdAuth = getAttribute(SessionConstants.SESSION_ID_AUTH_ATTR) as? Boolean ?: false
        return if (isSessionIdAuth && additionalCookie.name == SessionConstants.JSESSIONID_COOKIE) {
            getSession(false)
        } else {
            getSession(true)
        }
    }

}

/**
 * Web应用配置类
 * 负责Web服务器配置、静态资源处理和格式化器配置
 */
@Configuration
class WebConfig(
    private val env: Environment
) : ServletContextInitializer, WebServerFactoryCustomizer<WebServerFactory>, WebMvcConfigurer {

    // Web服务器配置
    override fun onStartup(servletContext: ServletContext?) {
        log.info("Web application configuration, using profiles: {}", env.activeProfiles as Array<*>)
    }

    override fun customize(server: WebServerFactory) {
        setMimeMappings(server)
        setLocationForStaticAssets(server)
    }

    // 静态资源处理
    override fun addResourceHandlers(registry: ResourceHandlerRegistry) {
        log.info("add uploadPath: {}", "file:${AppPro.uploadPath}")
        registry.addResourceHandler("/${AppPro.uploadDir}/**").addResourceLocations("file:${AppPro.uploadPath}\\")

        // 添加音频文件资源处理器，支持打包前后不同的路径结构
        registry.addResourceHandler("/music/**")
            .addResourceLocations(
                "classpath:/music/",     // 打包后：jar包内的music目录
                "file:./www/music/"      // 打包前：项目根目录下的www/music目录
            )
            .setCachePeriod(0)           // 禁用缓存避免Range请求问题
            .resourceChain(false)        // 禁用资源链
    }

    // 格式化器配置
    override fun addFormatters(registry: FormatterRegistry) {
        val dateFormatter = DateFormatter("yyyy-MM-dd HH:mm")
        registry.addFormatter(dateFormatter)
    }

    // MIME类型映射配置
    private fun setMimeMappings(server: WebServerFactory) {
        if (server is ConfigurableServletWebServerFactory) {
            val mappings = MimeMappings(MimeMappings.DEFAULT)
            val charset = StandardCharsets.UTF_8.name().toLowerCase()
            mappings.add("html", "${MediaType.TEXT_HTML_VALUE};charset=$charset")
            mappings.add("json", "${MediaType.APPLICATION_JSON_VALUE};charset=$charset")
            server.setMimeMappings(mappings)
        }
    }

    // 静态资源根目录配置
    private fun setLocationForStaticAssets(server: WebServerFactory) {
        if (server is ConfigurableServletWebServerFactory) {
            val prefixPath = resolvePathPrefix()
            val root = File("${prefixPath}www/")
            if (root.exists() && root.isDirectory) {
                server.setDocumentRoot(root)
            }
        }
    }

    // 路径前缀解析
    private fun resolvePathPrefix(): String {
        val fullExecutablePath = try {
            decode(this.javaClass.getResource("")!!.path, StandardCharsets.UTF_8.name())
        } catch (e: UnsupportedEncodingException) {
            this.javaClass.getResource("")!!.path
        }

        val rootPath = Paths.get(".").toUri().normalize().path
        val extractedPath = fullExecutablePath.replace(rootPath, "")
        val extractionEndIndex = extractedPath.indexOf("")
        return if (extractionEndIndex <= 0) "" else extractedPath.substring(0, extractionEndIndex)
    }

    /**
     * 注册Session-Id转JSESSIONID过滤器
     */
    @Bean
    fun sessionIdToJSessionIdFilter(): FilterRegistrationBean<SessionIdToJSESSIONIDFilter> {
        return FilterRegistrationBean<SessionIdToJSESSIONIDFilter>().apply {
            filter = SessionIdToJSESSIONIDFilter()
            addUrlPatterns("/api/*")
            order = Ordered.HIGHEST_PRECEDENCE
            setName("sessionIdToJSessionIdFilter")
        }
    }
}

/**
 * Spring Security安全配置
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
class SecurityConfiguration(
    private val authenticationManagerBuilder: AuthenticationManagerBuilder,
    private val personnelService: PersonnelService,
    private val openMapper: OpenMapper,
    private val loginlogService: LoginlogService
) : WebSecurityConfigurerAdapter() {

    @Value("\${spring.profiles.active}")
    private lateinit var active: String

    // 认证管理器配置
    @PostConstruct
    fun init() {
        try {
            authenticationManagerBuilder
                .userDetailsService(object : UserDetailsService {
                    override fun loadUserByUsername(username: String): UserDetails {
                        return <EMAIL>(username)
                    }
                })
                .passwordEncoder(BCryptPasswordEncoder())
        } catch (e: Exception) {
            throw BeanInitializationException("Security configuration failed", e)
        }
    }

    // 用户认证核心逻辑
    private fun loadUserByUsername(username: String): UserDetails {
        log.info("Authenticating user: {}", username)

        // 先尝试按手机号查询，再按用户名查询
        var personnel = personnelService.getPersonnelByPhone(username)
        var isPhoneLogin = personnel != null

        if (personnel == null) {
            // 手机号查不到，尝试按用户名查询
            personnel = personnelService.getPersonnelByUsername(username)
            isPhoneLogin = false
        }

        if (personnel == null) {
            log.error("User not found: {}", username)
            throw UsernameNotFoundException("账号或密码错误")
        }

        val password = if (isPhoneLogin) {
            // 手机号登录：使用验证码
            var code = LoginCode.loginCodeMap.remove(username)?.code

            // 非生产环境支持demo123密码登录
            if (active != "prod" && code == null) {
                log.info("Non-production environment: enabling demo123 password for user: {}", username)
                code = "demo123"
            }

            code
        } else {
            // 用户名登录：使用存储的密码（明文存储）
            if (personnel.password.isEmpty()) {
                log.error("User has no password set: {}", username)
                throw UsernameNotFoundException("账号或密码错误")
            }
            personnel.password
        }

        return AppUser(
            username = username,
            password = BCryptPasswordEncoder().encode(password),
            personnel = personnel
        )
    }

    /**
     * 获取客户端真实IP地址
     * 支持代理服务器和负载均衡器
     */
    private fun getClientIpAddress(request: HttpServletRequest): String {
        // 检查各种代理头
        val headers = listOf(
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        )

        for (header in headers) {
            val ip = request.getHeader(header)
            if (!ip.isNullOrEmpty() && !"unknown".equals(ip, ignoreCase = true)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                return ip.split(",")[0].trim()
            }
        }

        // 如果没有代理，直接获取远程地址
        return request.remoteAddr ?: ""
    }

    /**
     * 记录登录日志
     */
    private fun recordLoginLog(request: HttpServletRequest) {
        try {
            // 获取当前登录用户信息
            val currentUser = getUser()!!

            // 创建登录日志记录
            val loginlog = Loginlog().apply {
                id = uuid()
                userId = currentUser.id
                userName = currentUser.name
                orgName = currentUser.organization?.name ?: ""
                ipAddress = getClientIpAddress(request)
                // 限制User-Agent字符串长度为100字符，避免超过数据库字段限制
                browserName = (request.getHeader("User-Agent") ?: "").let {
                    if (it.length > 254) it.substring(0, 254) else it
                }
            }

            // 保存登录日志
            loginlogService.saveEntity(loginlog)
            log.info("Login log recorded for user: {} from IP: {}", currentUser.name, loginlog.ipAddress)

        } catch (e: Exception) {
            // 记录日志失败不应影响登录流程
            log.error("Failed to record login log: {}", e.message)
            errorlog(e)
        }
    }

    // Web安全配置
    override fun configure(web: WebSecurity) {
        web.ignoring()
            .antMatchers(HttpMethod.OPTIONS, "/**")
            .antMatchers("/assets/**/*.{js,html}")
            .antMatchers("/i18n/**")
            .antMatchers("/api/file/**")
            .antMatchers("/api/maintain/**")
            .antMatchers("/music/**")
    }

    // HTTP安全配置
    override fun configure(http: HttpSecurity) {
        http
            .csrf().disable()
            .exceptionHandling()
            .authenticationEntryPoint { _, response, _ ->
                response?.apply {
                    characterEncoding = "UTF-8"
                    contentType = "application/json; charset=utf-8"
                    status = HttpServletResponse.SC_UNAUTHORIZED
                    writer?.print(Result.getError("未登录或登录失效").toJsonString)
                }
            }
            .and()
            .formLogin()
            .loginProcessingUrl("/api/authentication")
            .successHandler { request, response, _ ->
                // 记录登录日志
                recordLoginLog(request)

                response?.apply {
                    characterEncoding = "UTF-8"
                    contentType = "application/json; charset=utf-8"
                    
                    // 添加Session-Id响应头
                    val sessionId = request.getSession(false)?.id
                    if (!sessionId.isNullOrEmpty()) {
                        setHeader(SessionConstants.SESSION_ID_HEADER, sessionId)
                    }
                    
                    writer?.print(Result.getSuccess(getUser()).toJsonString)
                }
            }
            .failureHandler { _, response, _ ->
                response?.apply {
                    characterEncoding = "UTF-8"
                    contentType = "application/json; charset=utf-8"
                    writer?.print(Result.getError("账号或密码错误").toJsonString)
                }
            }
            .usernameParameter("j_username")
            .passwordParameter("j_password")
            .permitAll()
            .and()
            .logout()
            .logoutUrl("/api/logout")
            .logoutSuccessHandler { _, response, _ ->
                response?.apply {
                    characterEncoding = "UTF-8"
                    contentType = "application/json; charset=utf-8"
                    status = HttpServletResponse.SC_OK
                    writer?.print(Result.getSuccessInfo("退出登录成功").toJsonString)
                }
            }
            .permitAll()
            .and()
            .headers()
            .frameOptions()
            .disable()
            .and()
            .addFilterBefore(sessionIdAuthenticationFilter(), UsernamePasswordAuthenticationFilter::class.java)
            .authorizeRequests()
            .antMatchers("/api/open/getAccount").authenticated()
            .antMatchers("/api/open/**").permitAll()
            .antMatchers("/api/authentication").permitAll()
            .antMatchers("/api/logout").permitAll()
            .antMatchers("/api/file/**").permitAll()
            .antMatchers("/api/maintain/**").permitAll()
            .antMatchers("/music/**").permitAll()
            .antMatchers("/api/wechatpay/**").permitAll()
            .antMatchers("/api/**").permitAll()
            .and()
            .sessionManagement()
            .maximumSessions(100)
            .sessionRegistry(sessionRegistry())
    }

    // Session注册表Bean
    @Bean
    fun sessionRegistry() = SessionRegistryImpl()

    // HTTP Session事件发布器Bean
    @Bean
    fun httpSessionEventPublisher(): ServletListenerRegistrationBean<HttpSessionEventPublisher> {
        return ServletListenerRegistrationBean(HttpSessionEventPublisher())
    }

    /**
     * Session-Id认证过滤器
     * 专门处理通过Session-Id请求头进行的认证
     */
    private fun sessionIdAuthenticationFilter(): Filter {
        return object : Filter {
            override fun init(filterConfig: FilterConfig?) {}
            override fun destroy() {}

            override fun doFilter(request: ServletRequest, response: ServletResponse, chain: FilterChain) {
                val httpRequest = request as HttpServletRequest

                // 检查是否已经认证
                if (SecurityContextHolder.getContext().authentication?.isAuthenticated == true) {
                    chain.doFilter(request, response)
                    return
                }

                // 获取Session-Id请求头并进行认证
                val sessionId = httpRequest.getHeader(SessionConstants.SESSION_ID_HEADER)
                if (!sessionId.isNullOrEmpty()) {
                    authenticateSessionId(sessionId, httpRequest)
                }

                chain.doFilter(request, response)
            }

            /**
             * 根据Session-Id进行认证
             */
            private fun authenticateSessionId(sessionId: String, request: HttpServletRequest) {
                try {
                    val sessionInfo = sessionRegistry().getSessionInformation(sessionId)
                    if (sessionInfo != null && !sessionInfo.isExpired && sessionInfo.principal is UserDetails) {
                        val principal = sessionInfo.principal as UserDetails
                        val authentication = PreAuthenticatedAuthenticationToken(
                            principal,
                            sessionId,
                            principal.authorities
                        ).apply {
                            isAuthenticated = true
                        }

                        SecurityContextHolder.getContext().authentication = authentication
                        request.setAttribute(SessionConstants.SESSION_ID_AUTH_ATTR, true)
                        log.info("Session-Id authentication successful for session: {}", sessionId)
                    }
                } catch (e: Exception) {
                    log.warn("Session-Id authentication failed: {}", e.message)
                }
            }
        }
    }
}

/**
 * 应用用户类
 * 扩展Spring Security的User类，添加应用特定属性
 */
class AppUser(
    username: String,
    password: String,
    val personnel: Personnel,
    authorities: MutableCollection<out GrantedAuthority> = mutableListOf(),
    var wechatSessionKey: String = ""
) : User(username, password, authorities)

/**
 * SPA前端路由转发控制器
 * 处理单页应用的路由转发
 */
@Controller
class SpaForwardController(
    private val applicationContext: ApplicationContext
) {

    @RequestMapping(
        value = ["/{path:[^.]*}", "/**/{path:[^.]*}"],
        method = [RequestMethod.GET]
    )
    fun forward(
        request: HttpServletRequest,
        response: HttpServletResponse,
        @PathVariable(required = false) path: String?
    ) {
        val uri = request.requestURI

        // 排除API和WebSocket路径
        if (uri.startsWith("/api") || uri.startsWith("/ws") || uri.startsWith("/tiles") || uri.startsWith("/music")) {
            response.sendError(HttpStatus.NOT_FOUND.value())
            return
        }

        // 查找index.html资源
        val indexResource = findIndexResource()

        if (indexResource != null && indexResource.exists() && indexResource.isReadable) {
            serveIndexHtml(response, indexResource)
        } else {
            log.error("index.html not found in any location")
            response.sendError(HttpStatus.NOT_FOUND.value())
        }
    }

    // 查找index.html资源
    private fun findIndexResource(): Resource? {
        val locations = listOf(
            "classpath:/index.html",
            "file:./www/index.html",
            "file:www/index.html"
        )

        for (location in locations) {
            val resource = applicationContext.getResource(location)
            if (resource.exists() && resource.isReadable) {
                return resource
            }
        }

        return null
    }

    // 提供index.html服务
    private fun serveIndexHtml(response: HttpServletResponse, indexResource: Resource) {
        response.contentType = "text/html;charset=UTF-8"

        try {
            indexResource.inputStream.use { input ->
                response.outputStream.use { output ->
                    input.copyTo(output)
                }
            }
        } catch (e: Exception) {
            log.error("Error serving index.html: {}", e.message)
            errorlog(e)
            response.sendError(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Error serving index.html")
        }
    }
}
