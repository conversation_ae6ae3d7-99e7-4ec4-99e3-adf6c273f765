package com.shenlan.smartlogixmini.config

import com.shenlan.smartlogixmini.auto.*
import com.shenlan.smartlogixmini.util.*
import org.springframework.beans.factory.BeanInitializationException
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.web.server.MimeMappings
import org.springframework.boot.web.server.WebServerFactory
import org.springframework.boot.web.server.WebServerFactoryCustomizer
import org.springframework.boot.web.servlet.ServletContextInitializer
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.env.Environment
import org.springframework.core.io.Resource
import org.springframework.format.FormatterRegistry
import org.springframework.format.datetime.DateFormatter
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.builders.WebSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter
import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.session.SessionRegistryImpl
import org.springframework.security.core.userdetails.User
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.web.session.HttpSessionEventPublisher
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import java.io.File
import java.io.UnsupportedEncodingException
import java.net.URLDecoder.decode
import java.nio.charset.StandardCharsets
import java.nio.file.Paths
import javax.annotation.PostConstruct
import javax.servlet.ServletContext
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * 登录验证码数据类
 */
data class LoginCode(
    var code: String, // 验证码
    var createTime: Long
) {
    // 是否超时（5分钟）
    fun isTimeOut(): Boolean {
        return System.currentTimeMillis() - createTime > 1000 * 60 * 5
    }

    companion object {
        var loginCodeMap = hashMapOf<String, LoginCode>()
    }
}

/**
 * Web应用配置类
 * 负责Web服务器配置、静态资源处理和格式化器配置
 */
@Configuration
class WebConfig(
    private val env: Environment
) : ServletContextInitializer, WebServerFactoryCustomizer<WebServerFactory>, WebMvcConfigurer {

    // Web服务器配置
    override fun onStartup(servletContext: ServletContext?) {
        log.info("Web application configuration, using profiles: {}", env.activeProfiles as Array<*>)
    }

    override fun customize(server: WebServerFactory) {
        setMimeMappings(server)
        setLocationForStaticAssets(server)
    }

    // 静态资源处理
    override fun addResourceHandlers(registry: ResourceHandlerRegistry) {
        log.info("add uploadPath: {}", "file:${AppPro.uploadPath}")
        registry.addResourceHandler("${AppPro.uploadDir}/**").addResourceLocations("file:${AppPro.uploadPath}")

        // 添加音频文件资源处理器，支持打包前后不同的路径结构
        registry.addResourceHandler("/music/**")
            .addResourceLocations(
                "classpath:/music/",     // 打包后：jar包内的music目录
                "file:./www/music/"      // 打包前：项目根目录下的www/music目录
            )
            .setCachePeriod(0)           // 禁用缓存避免Range请求问题
            .resourceChain(false)        // 禁用资源链
    }

    // 格式化器配置
    override fun addFormatters(registry: FormatterRegistry) {
        val dateFormatter = DateFormatter("yyyy-MM-dd HH:mm")
        registry.addFormatter(dateFormatter)
    }

    // MIME类型映射配置
    private fun setMimeMappings(server: WebServerFactory) {
        if (server is ConfigurableServletWebServerFactory) {
            val mappings = MimeMappings(MimeMappings.DEFAULT)
            val charset = StandardCharsets.UTF_8.name().toLowerCase()
            mappings.add("html", "${MediaType.TEXT_HTML_VALUE};charset=$charset")
            mappings.add("json", "${MediaType.APPLICATION_JSON_VALUE};charset=$charset")
            server.setMimeMappings(mappings)
        }
    }

    // 静态资源根目录配置
    private fun setLocationForStaticAssets(server: WebServerFactory) {
        if (server is ConfigurableServletWebServerFactory) {
            val prefixPath = resolvePathPrefix()
            val root = File("${prefixPath}www/")
            if (root.exists() && root.isDirectory) {
                server.setDocumentRoot(root)
            }
        }
    }

    // 路径前缀解析
    private fun resolvePathPrefix(): String {
        val fullExecutablePath = try {
            decode(this.javaClass.getResource("")!!.path, StandardCharsets.UTF_8.name())
        } catch (e: UnsupportedEncodingException) {
            this.javaClass.getResource("")!!.path
        }

        val rootPath = Paths.get(".").toUri().normalize().path
        val extractedPath = fullExecutablePath.replace(rootPath, "")
        val extractionEndIndex = extractedPath.indexOf("")
        return if (extractionEndIndex <= 0) "" else extractedPath.substring(0, extractionEndIndex)
    }
}

/**
 * Spring Security安全配置
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
class SecurityConfiguration(
    private val authenticationManagerBuilder: AuthenticationManagerBuilder,
    private val personnelService: PersonnelService,
    private val openMapper: OpenMapper,
    private val loginlogService: LoginlogService
) : WebSecurityConfigurerAdapter() {

    @Value("\${spring.profiles.active}")
    private lateinit var active: String

    // 认证管理器配置
    @PostConstruct
    fun init() {
        try {
            authenticationManagerBuilder
                .userDetailsService(object : UserDetailsService {
                    override fun loadUserByUsername(username: String): UserDetails {
                        return <EMAIL>(username)
                    }
                })
                .passwordEncoder(BCryptPasswordEncoder())
        } catch (e: Exception) {
            throw BeanInitializationException("Security configuration failed", e)
        }
    }

    // 用户认证核心逻辑
    private fun loadUserByUsername(username: String): UserDetails {
        log.info("Authenticating user: {}", username)

        // 使用PersonnelService验证人员信息
        val personnel = personnelService.getPersonnelByPhone(username)
        if (personnel == null) {
            log.error("Phone number not found in Personnel table: {}", username)
            throw UsernameNotFoundException("该手机号未关联到任何人员信息")
        }

        // 获取并清除登录验证码
        var password = LoginCode.loginCodeMap.remove(username)?.code

        // 非生产环境支持demo123密码登录
        if (active != "prod" && password == null) {
            log.info("Non-production environment: enabling demo123 password for user: {}", username)
            password = "demo123"
        }

        return AppUser(
            username = username,
            password = BCryptPasswordEncoder().encode(password),
            personnel = personnel
        )
    }

    /**
     * 获取客户端真实IP地址
     * 支持代理服务器和负载均衡器
     */
    private fun getClientIpAddress(request: HttpServletRequest): String {
        // 检查各种代理头
        val headers = listOf(
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        )

        for (header in headers) {
            val ip = request.getHeader(header)
            if (!ip.isNullOrEmpty() && !"unknown".equals(ip, ignoreCase = true)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                return ip.split(",")[0].trim()
            }
        }

        // 如果没有代理，直接获取远程地址
        return request.remoteAddr ?: ""
    }

    /**
     * 记录登录日志
     */
    private fun recordLoginLog(request: HttpServletRequest) {
        try {
            // 获取当前登录用户信息
            val currentUser = getUser()!!

            // 创建登录日志记录
            val loginlog = Loginlog().apply {
                id = uuid()
                userId = currentUser.id
                userName = currentUser.name
                orgName = currentUser.organization?.name ?: ""
                ipAddress = getClientIpAddress(request)
                // 限制User-Agent字符串长度为100字符，避免超过数据库字段限制
                browserName = (request.getHeader("User-Agent") ?: "").let {
                    if (it.length > 254) it.substring(0, 254) else it
                }
            }

            // 保存登录日志
            loginlogService.save(loginlog)
            log.info("Login log recorded for user: {} from IP: {}", currentUser.name, loginlog.ipAddress)

        } catch (e: Exception) {
            // 记录日志失败不应影响登录流程
            log.error("Failed to record login log: {}", e.message)
            errorlog(e)
        }
    }

    // Web安全配置
    override fun configure(web: WebSecurity) {
        web.ignoring()
            .antMatchers(HttpMethod.OPTIONS, "/**")
            .antMatchers("/assets/**/*.{js,html}")
            .antMatchers("/i18n/**")
            .antMatchers("/api/file/**")
            .antMatchers("/api/maintain/**")
            .antMatchers("/music/**")
    }

    // HTTP安全配置
    override fun configure(http: HttpSecurity) {
        http
            .csrf().disable()
            .exceptionHandling()
            .authenticationEntryPoint { _, response, _ ->
                response?.apply {
                    characterEncoding = "UTF-8"
                    contentType = "application/json; charset=utf-8"
                    status = HttpServletResponse.SC_UNAUTHORIZED
                    writer?.print(Result.getError("未登录或登录失效").toJsonString)
                }
            }
            .and()
            .formLogin()
            .loginProcessingUrl("/api/authentication")
            .successHandler { request, response, _ ->
                // 记录登录日志
                recordLoginLog(request)

                response?.apply {
                    characterEncoding = "UTF-8"
                    contentType = "application/json; charset=utf-8"
                    writer?.print(Result.getSuccess(getUser()).toJsonString)
                }
            }
            .failureHandler { _, response, _ ->
                response?.apply {
                    characterEncoding = "UTF-8"
                    contentType = "application/json; charset=utf-8"
                    writer?.print(Result.getError("用户名或密码错误!").toJsonString)
                }
            }
            .usernameParameter("j_username")
            .passwordParameter("j_password")
            .permitAll()
            .and()
            .logout()
            .logoutUrl("/api/logout")
            .logoutSuccessHandler { _, response, _ ->
                response?.status = HttpServletResponse.SC_OK
            }
            .permitAll()
            .and()
            .headers()
            .frameOptions()
            .disable()
            .and()
            .authorizeRequests()
            .antMatchers("/api/open/getAccount").authenticated()
            .antMatchers("/api/open/**").permitAll()
            .antMatchers("/api/authentication").permitAll()
            .antMatchers("/api/logout").permitAll()
            .antMatchers("/api/file/**").permitAll()
            .antMatchers("/api/maintain/**").permitAll()
            .antMatchers("/music/**").permitAll()
            .antMatchers("/api/**").authenticated()
            .and()
            .sessionManagement()
            .maximumSessions(100)
            .sessionRegistry(sessionRegistry())
    }

    // Session注册表Bean
    @Bean
    fun sessionRegistry() = SessionRegistryImpl()

    // HTTP Session事件发布器Bean
    @Bean
    fun httpSessionEventPublisher(): ServletListenerRegistrationBean<HttpSessionEventPublisher> {
        return ServletListenerRegistrationBean(HttpSessionEventPublisher())
    }
}

/**
 * 应用用户类
 * 扩展Spring Security的User类，添加应用特定属性
 */
class AppUser(
    username: String,
    password: String,
    val personnel: Personnel,
    authorities: MutableCollection<out GrantedAuthority> = mutableListOf()
) : User(username, password, authorities)

/**
 * SPA前端路由转发控制器
 * 处理单页应用的路由转发
 */
@Controller
class SpaForwardController(
    private val applicationContext: ApplicationContext
) {

    @RequestMapping(
        value = ["/{path:[^.]*}", "/**/{path:[^.]*}"],
        method = [RequestMethod.GET]
    )
    fun forward(
        request: HttpServletRequest,
        response: HttpServletResponse,
        @PathVariable(required = false) path: String?
    ) {
        val uri = request.requestURI

        // 排除API和WebSocket路径
        if (uri.startsWith("/api") || uri.startsWith("/ws") || uri.startsWith("/tiles") || uri.startsWith("/music")) {
            response.sendError(HttpStatus.NOT_FOUND.value())
            return
        }

        // 查找index.html资源
        val indexResource = findIndexResource()

        if (indexResource != null && indexResource.exists() && indexResource.isReadable) {
            serveIndexHtml(response, indexResource)
        } else {
            log.error("index.html not found in any location")
            response.sendError(HttpStatus.NOT_FOUND.value())
        }
    }

    // 查找index.html资源
    private fun findIndexResource(): Resource? {
        val locations = listOf(
            "classpath:/index.html",
            "file:./www/index.html",
            "file:www/index.html"
        )

        for (location in locations) {
            val resource = applicationContext.getResource(location)
            if (resource.exists() && resource.isReadable) {
                return resource
            }
        }

        return null
    }

    // 提供index.html服务
    private fun serveIndexHtml(response: HttpServletResponse, indexResource: Resource) {
        response.contentType = "text/html;charset=UTF-8"

        try {
            indexResource.inputStream.use { input ->
                response.outputStream.use { output ->
                    input.copyTo(output)
                }
            }
        } catch (e: Exception) {
            log.error("Error serving index.html: {}", e.message)
            errorlog(e)
            response.sendError(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Error serving index.html")
        }
    }
}
