package com.shenlan.smartlogixmini.config

import com.wechat.pay.java.core.RSAAutoCertificateConfig
import com.wechat.pay.java.core.RSAPublicKeyConfig
import org.bouncycastle.jcajce.provider.asymmetric.RSA
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.bouncycastle.jce.provider.PEMUtil
import org.bouncycastle.openssl.PEMParser
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import org.springframework.util.ResourceUtils
import java.io.File
import java.nio.file.Files
import java.security.PrivateKey
import java.security.Security

@ConfigurationProperties(prefix = "wechat", ignoreUnknownFields = true)
@Component
object WeChatPayConfig {
    lateinit var appid: String
    lateinit var merchantId: String
    lateinit var privateKeyPath: String
    lateinit var merchantSerialNumber: String
    lateinit var apiV3Key: String
    lateinit var notifyUrl: String

    fun getRSAAutoCertificateConfig(): RSAAutoCertificateConfig {
        return RSAAutoCertificateConfig.Builder()
            .merchantId(merchantId)
            .privateKeyFromPath(System.getProperty("user.dir") + privateKeyPath)
            .merchantSerialNumber(merchantSerialNumber)
            .apiV3Key(apiV3Key)
            .build()
    }
}
