package com.shenlan.smartlogixmini.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component
import java.nio.file.Paths


@Component
@ConfigurationProperties(prefix = "application", ignoreUnknownFields = true)
object AppPro {
    var uploadDir = "upload"
    val uploadPath = Paths.get(System.getProperty("user.dir"), uploadDir).toString()
    var aes = false
    lateinit var imgPrefix: String
    lateinit var vehicleGpsAddress: String
    // 当前环境
    lateinit var env: String
}
