package com.shenlan.smartlogixmini.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component
import java.nio.file.Paths


@Component
@ConfigurationProperties(prefix = "application", ignoreUnknownFields = true)
object AppPro {
    var uploadDir = "upload"
    val uploadPath = Paths.get(System.getProperty("user.dir"), uploadDir).toString()
    var aes = false
    lateinit var logPath: String
    lateinit var imgPrefix: String
}
