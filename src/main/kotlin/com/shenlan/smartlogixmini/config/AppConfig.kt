package com.shenlan.smartlogixmini.config

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.JavaType
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.shenlan.smartlogixmini.util.AesUtil
import com.shenlan.smartlogixmini.util.customObjectMapper
import com.shenlan.smartlogixmini.util.errorlog
import com.shenlan.smartlogixmini.util.log
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.http.HttpInputMessage
import org.springframework.http.HttpOutputMessage
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.http.converter.json.MappingJacksonInputMessage
import org.springframework.scheduling.TaskScheduler
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler
import org.springframework.web.client.RestTemplate
import org.springframework.web.socket.server.standard.ServerEndpointExporter
import java.io.IOException
import java.lang.reflect.Type

@Configuration
open class AppConfig {

    @Bean
    @Primary
    open fun objectMapper(): ObjectMapper {
        return customObjectMapper
    }

    @Bean
    open fun taskScheduler(): TaskScheduler = ThreadPoolTaskScheduler().apply {
        poolSize = 3
        setThreadNamePrefix("smartlogix-task-")
        setWaitForTasksToCompleteOnShutdown(true)
    }

    @Bean
    open fun restTemplate(builder: RestTemplateBuilder): RestTemplate = builder.build()

    /**
     * 自定义HTTP消息转换器
     * 支持AES加密解密功能的JSON消息转换
     */
    @Bean
    open fun mappingJackson2HttpMessageConverter(): MappingJackson2HttpMessageConverter {
        return object : MappingJackson2HttpMessageConverter() {

            init {
                objectMapper = <EMAIL>()
            }

            override fun read(type: Type, contextClass: Class<*>?, inputMessage: HttpInputMessage): Any {
                return if (!AppPro.aes) {
                    // 未启用加密时使用默认处理
                    super.read(type, contextClass, inputMessage)
                } else {
                    // 启用加密时进行解密处理
                    val javaType = getJavaType(type, contextClass)
                    readWithDecryption(javaType, inputMessage)
                }
            }

            override fun writeInternal(`object`: Any, type: Type?, outputMessage: HttpOutputMessage) {
                if (!AppPro.aes) {
                    // 未启用加密时使用默认处理
                    super.writeInternal(`object`, type, outputMessage)
                } else {
                    // 启用加密时进行加密处理
                    writeWithEncryption(`object`, outputMessage)
                }
            }

            /**
             * 带解密功能的读取方法
             * 处理AES加密的请求数据
             */
            private fun readWithDecryption(javaType: JavaType, inputMessage: HttpInputMessage): Any {
                try {
                    // 处理特殊视图反序列化
                    if (inputMessage is MappingJacksonInputMessage) {
                        inputMessage.deserializationView?.let { view ->
                            return objectMapper.readerWithView(view)
                                .forType(javaType)
                                .readValue(inputMessage.body)
                        }
                    }

                    // 使用Kotlin标准库读取并解密数据
                    val jsonContent = inputMessage.body.bufferedReader(Charsets.UTF_8).use { it.readText() }
                    val jsonNode: JsonNode = objectMapper.readTree(jsonContent)

                    // 获取加密参数
                    val encryptedData = jsonNode.get(AesUtil.PARAMS)?.asText()
                        ?: throw HttpMessageNotReadableException("Missing encrypted params field", null)

                    // 解密数据
                    val decryptedData = AesUtil.aesDecrypt(encryptedData)
                        ?: throw HttpMessageNotReadableException("Failed to decrypt data", null)

                    return objectMapper.readValue(decryptedData, javaType)

                } catch (ex: JsonProcessingException) {
                    log.error("JSON parse error during decryption: ${ex.originalMessage}")
                    throw HttpMessageNotReadableException("JSON parse error: ${ex.originalMessage}", ex)
                } catch (ex: IOException) {
                    log.error("I/O error during message reading")
                    errorlog(ex)
                    throw HttpMessageNotReadableException("I/O error while reading input message", ex)
                } catch (ex: Exception) {
                    log.error("Unexpected error during decryption")
                    errorlog(ex)
                    throw HttpMessageNotReadableException("Decryption failed", ex)
                }
            }

            /**
             * 带加密功能的写入方法
             * 对响应数据进行AES加密
             */
            private fun writeWithEncryption(`object`: Any, outputMessage: HttpOutputMessage) {
                try {
                    val jsonString = objectMapper.writeValueAsString(`object`)
                    val encryptedData = AesUtil.aesEncrypt(jsonString)
                    outputMessage.body.write(encryptedData.toByteArray(Charsets.UTF_8))
                } catch (ex: JsonProcessingException) {
                    log.error("JSON serialization error during encryption")
                    errorlog(ex)
                    throw RuntimeException("Failed to serialize object for encryption", ex)
                } catch (ex: IOException) {
                    log.error("I/O error during response writing")
                    errorlog(ex)
                    throw RuntimeException("Failed to write encrypted response", ex)
                } catch (ex: Exception) {
                    log.error("Unexpected error during response encryption")
                    errorlog(ex)
                    throw RuntimeException("Failed to encrypt response", ex)
                }
            }
        }
    }
}

@Configuration
open class WebSocketConfig {
    @Bean
    open fun serverEndpointExporter() = ServerEndpointExporter()
}