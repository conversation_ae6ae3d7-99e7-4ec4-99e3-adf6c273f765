package com.shenlan.smartlogixmini.config

import com.shenlan.smartlogixmini.util.errorlog
import com.shenlan.smartlogixmini.util.log
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.stereotype.Component

/**
 * 自定义Flyway初始化器
 * 在应用启动的最早期执行Flyway迁移，确保在所有@PostConstruct方法之前完成
 */
@Component
@ConditionalOnProperty(name = ["spring.flyway.enabled"], havingValue = "true")
@Order(Ordered.HIGHEST_PRECEDENCE)
class CustomFlywayInitializer : InitializingBean {

    @Autowired(required = false)
    private var flywayMigrationInitializer: FlywayMigrationInitializer? = null

    override fun afterPropertiesSet() {
        try {
            if (flywayMigrationInitializer == null) {
                log.warn("FlywayMigrationInitializer not found, skipping custom Flyway migration")
                return
            }

            log.info("Starting Flyway database migration before business components initialization...")

            // 执行Flyway迁移
            flywayMigrationInitializer!!.afterPropertiesSet()

            log.info("Flyway database migration completed successfully!")
        } catch (e: Exception) {
            log.error("Custom Flyway migration failed!")
            errorlog(e)
            throw e
        }
    }
}
