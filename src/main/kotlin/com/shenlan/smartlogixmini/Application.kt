package com.shenlan.smartlogixmini

import com.shenlan.smartlogixmini.auto.WeChatPayUtil
import com.shenlan.smartlogixmini.util.initMapper
import com.shenlan.smartlogixmini.util.log
import org.springframework.boot.SpringApplication
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.context.ConfigurableApplicationContext
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.transaction.annotation.EnableTransactionManagement
import java.net.InetAddress

/**
 * 应用程序主类
 */
@SpringBootApplication
@EnableScheduling
@EnableTransactionManagement
class Application {
    companion object {
        var context: ConfigurableApplicationContext? = null
    }
}

/**
 * 应用程序入口函数
 */
fun main(args: Array<String>) {
    // 启动应用程序
    val applicationContext = SpringApplication(Application::class.java).run(*args)
    Application.context = applicationContext
    val env = applicationContext.environment

    // 获取应用程序访问URL信息
    val protocol = "http"
    val port = env.getProperty("server.port")
    val localUrl = "$protocol://localhost:$port"
    val externalUrl = "$protocol://${InetAddress.getLocalHost().hostAddress}:$port"

    // 打印应用程序信息
    "".log.info("\n" + """
        ----------------------------------------------------------
        	Application '{}' is running! Access URLs:
        	Local:    {}
        	External: {}
        	Profile:  {}
        	
        	Database Connection Info:
        	URL:      {}
        	Username: {}
        	Driver:   {}
        ----------------------------------------------------------
    """.trimIndent(),
        env.getProperty("spring.application.name"),
        localUrl,
        externalUrl,
        env.activeProfiles,
        env.getProperty("spring.datasource.url"),
        env.getProperty("spring.datasource.username"),
        env.getProperty("spring.datasource.driver-class-name")
    )

    initMapper()
    WeChatPayUtil.init()
}
