package com.jhg.QrDecode

import java.io.*
import com.shenlan.smartlogixmini.util.log

class CQrDecode {

    companion object {
        init {
            try {
                log.info("Current directory: " + System.getProperty("user.dir"))
                loadNativeLibrary()
                log.info("QrDecode dll library loaded successfully")
            } catch (e: Exception) {
                System.err.println("Failed to load QrDecode library: " + e.message)
                throw RuntimeException(e)
            }
        }

        @Throws(IOException::class)
        private fun loadNativeLibrary() {
            // 根据操作系统选择不同的库文件
            val osName = System.getProperty("os.name").toLowerCase()
            val (resourcePath, fileExtension) = when {
                osName.contains("windows") -> "/QrDecode.dll" to ".dll"
                osName.contains("linux") -> "/libQrDecode.so" to ".so"
                else -> throw UnsupportedOperationException("Unsupported operating system: $osName")
            }

            // 从classpath读取库文件
            val inputStream = CQrDecode::class.java.getResourceAsStream(resourcePath)
                ?: throw FileNotFoundException("Native library not found: $resourcePath")

            // 创建临时文件
            val tempFile = File.createTempFile("QrDecode", fileExtension)
            tempFile.deleteOnExit()

            // 将库文件写入临时文件
            inputStream.use { input ->
                FileOutputStream(tempFile).use { output ->
                    val buffer = ByteArray(1024)
                    var bytesRead: Int = 0
                    while (input.read(buffer).also { bytesRead = it } != -1) {
                        output.write(buffer, 0, bytesRead)
                    }
                }
            }

            // 加载库文件
            System.load(tempFile.absolutePath)
        }
    }

    external fun hello(str: String): Int
    external fun API_GetCode(path: String, qrString: String, result: IntArray): Int
    external fun API_GetCodeII(path: String, qrString: String, result: IntArray): Int
    external fun API_GetCodeIII(
        path: String,
        context: String,
        company_id: String,
        qrString: String,
        qr_time: String,
        commit_time: String,
        check2: Int,
        result: IntArray
    ): Int
}