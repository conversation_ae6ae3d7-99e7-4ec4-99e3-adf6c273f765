* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
html,
body {
  height: 100%;
  width: 100%;
  background-color: #fff;
  padding: 0;
  margin: 0;
}
body {
  font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI",
    <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol";
  color: #333333;
  line-height: 1.5;
  font-size: 1rem;
  overflow-x: hidden;
}
.main {
  padding: 0;
}

.description h1 {
  margin-top: 5.5rem;
  margin-bottom: 4rem;
  font-size: 2.5rem;
  font-weight: bold;
}
.description p {
  text-indent: 2em;
  font-size: 1.125rem;
  line-height: 1.5;
}
.features {
  margin-top: 4rem;
}
.features .card {
  border: none;
  width: 17.3125rem;
  font-size: 0.875rem;
  height: 25rem;
}

.features .card .card-body {
  padding: 1.3125rem 0 0 0;
}
.features .card .card-body .card-title span {
  font-size: 1rem;
}
.features .card .card-body .card-title img.icon {
  margin-right: 0.25rem;
  width: 1.25rem;
  height: 1.25rem;
}
.features .card .card-body .card-text {
  font-size: 0.875rem;
  text-align: left;
}

footer {
  width: 100%;
  /*135/16*/
  height: 8.4375rem;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(7, 82, 172, 1);
  font-size: 0.875rem;
  margin-top: 5rem;
}
footer p {
  line-height: 1;
}
a {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
}
a:hover {
  text-decoration: underline;
}
