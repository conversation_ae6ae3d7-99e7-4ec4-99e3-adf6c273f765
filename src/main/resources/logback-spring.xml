<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true">
    <include resource="org/springframework/boot/logging/logback/base.xml"/>

    <springProperty scope="context" name="logPath" source="application.log-path"/>

    <appender name="System_File"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/System/Info_%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%d %-5level [%thread] %logger{0}: %msg%n</Pattern>
        </encoder>
    </appender>

    <appender name="WARN_FILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/Warn/Warn_%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%d %-5level [%thread] %logger{0}: %msg%n</Pattern>
        </encoder>
    </appender>

    <appender name="Exception_File"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/Exception/Exception_%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%d %-5level [%thread] %logger{0}: %msg%n</Pattern>
        </encoder>
    </appender>

    <springProfile name="!prod">
        <logger name="com.shenlan" level="DEBUG"/>
    </springProfile>

    <springProfile name="prod">
        <logger name="com.shenlan" level="INFO"/>
    </springProfile>

    <logger name="springfox.documentation" level="WARN"/>
    <logger name="com.shenlan.smartlogixmini.mybatis" level="INFO"/>
    <logger name="org.springframework.aop.framework" level="WARN"/>
    <logger name="org.springframework.web.servlet.mvc.method.annotation" level="WARN"/>
    <logger name="org.springframework.context.support" level="WARN"/>
    <logger name="org.flywaydb.core.internal.util.scanner" level="ERROR"/>
    <logger name="org.apache.catalina.core" level="WARN"/>

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="System_File"/>
        <appender-ref ref="WARN_FILE"/>
        <appender-ref ref="Exception_File"/>
    </root>
</configuration>
