spring:
  datasource:
    type: org.springframework.jdbc.datasource.DriverManagerDataSource
    url: ************************************************************************************************************************
    username: shenlan
    password: <PERSON><PERSON><PERSON><PERSON>@2016
    driver-class-name: org.mariadb.jdbc.Driver

server:
  port: 443
  ssl:
    enabled: true
    key-store: classpath:qwtservice.com.p12
    key-store-password: SHENLAN@2016
    key-store-type: PKCS12
  http:
    port: 80

aliyun:
  sms:
    enabled: true
  vms:
    enabled: true
  mns:
    enabled: true

wechat:
  app-id: wx871224747248c1db
  app-secret: da6921e99ef1cb9986dc3b7d97c3116b
  merchant-id : 1719984993
  private-key-path: /wechatpay/apiclient_key.pem
  public-key-path: /wechatpay/pub_key.pem
  public-key-id: PUB_KEY_ID_0117199849932025061800182379003000
  merchant-serial-number: 398C02B7EF0E1464936C262A6803185BC79DB2D6
  api-v3-key: 6632c66dd7344a2aac0502f39c41c9c9
  notify-url: https://qwtservice.com/api/wechatpay/notify
  oauth-redirect-uri: https://qwtservice.com/api/wechatpay/oauth

application:
  imgPrefix: https://qwtservice.com/