spring:
  datasource:
    type: org.springframework.jdbc.datasource.DriverManagerDataSource
    url: *******************************************************************************************************************************
    username: shen<PERSON>
    password: <PERSON><PERSON><PERSON><PERSON>@2016
    driver-class-name: org.mariadb.jdbc.Driver

server:
  port: 8045

aliyun:
  sms:
    enabled: true
  vms:
    enabled: true
  mns:
    enabled: true
