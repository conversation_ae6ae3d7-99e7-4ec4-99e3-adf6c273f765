spring:
  application:
    name: "@projectName@"
  profiles:
    active: "@profileName@"
  servlet:
    multipart:
      maxFileSize: 500MB
      maxRequestSize: 500MB
  flyway:
    enabled: false
    locations:
      - filesystem:sql/flyway/migration
      - classpath:sql/flyway/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    encoding: UTF-8
    baseline-version: "0"
    baseline-description: "Initial baseline"

server:
  servlet:
    session:
      timeout: 259200

pagehelper:
  reasonable: true

application:
  log-path: "${user.dir}/logs/${spring.application.name}"
  apihubs-api-key: "b442800309b3a04d48191fd1153207820b73"

aliyun:
  access-key-id: "LTAI4FdqhciqpaNSEk1fjAd4"
  access-key-secret: "******************************"

  sms:
    enabled: false
    template:
      verification-code: "SMS_175330236"
      meeting:
        create: "SMS_486525256"
        start: "SMS_486525256"
        cancel: "SMS_486400280"
        change: "SMS_486460247"
    sign-name: "深蓝信息"
    access-key-id: ${aliyun.access-key-id}
    access-key-secret: ${aliyun.access-key-secret}

  vms:
    enabled: false
    access-key-id: ${aliyun.access-key-id}
    access-key-secret: ${aliyun.access-key-secret}
    endpoint: dyvmsapi.aliyuncs.com
    called-show-number: "02122797262"
    template:
      meeting:
        create:
          start-code: "TTS_318145091"
          menu-key-map-list:
            - key: "1"
              code: "TTS_313420217"
            - key: "2"
              code: "TTS_315085173"
            - key: "3"
              code: "TTS_315735134"
        start:
          start-code: "TTS_318145091"
          menu-key-map-list:
            - key: "1"
              code: "TTS_313420217"
            - key: "2"
              code: "TTS_315085173"
            - key: "3"
              code: "TTS_315735134"
        cancel:
          start-code: "TTS_316545129"
        change:
          start-code: "TTS_313995233"
          menu-key-map-list:
            - key: "1"
              code: "TTS_313420217"
            - key: "2"
              code: "TTS_315085173"
            - key: "3"
              code: "TTS_315735134"

  mns:
    enabled: false
    access-key-id: ${aliyun.access-key-id}
    access-key-secret: ${aliyun.access-key-secret}
    debug: false
    queues:
      voice-report:
        queue-name: "Alicom-Queue-1691889115587409-VoiceReport"
        message-type: "VoiceReport"
      sms-up:
        queue-name: "Alicom-Queue-1691889115587409-SmsUp"
        message-type: "SmsUp"

wechat:
  app-id: wx871224747248c1db
  app-secret: da6921e99ef1cb9986dc3b7d97c3116b
  merchant-id : 1719984993
  private-key-path: /src/main/resources/wechatpay/apiclient_key.pem
  merchant-serial-number: 398C02B7EF0E1464936C262A6803185BC79DB2D6
  api-v3-key: 6632c66dd7344a2aac0502f39c41c9c9
  notify-url: https://www.shenlaninfo.com:8047/api/wechatpay/notify
  oauth-redirect-uri: https://www.shenlaninfo.com:8047/api/wechatpay/oauth
