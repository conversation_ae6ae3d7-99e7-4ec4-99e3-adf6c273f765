spring:
  application:
    name: "@projectName@"
  profiles:
    active: "@profileName@"
  servlet:
    multipart:
      maxFileSize: 500MB
      maxRequestSize: 500MB

server:
  servlet:
    session:
      timeout: 259200

pagehelper:
  reasonable: true

application:
  log-path: "${user.dir}/logs/${spring.application.name}"
  apihubs-api-key: "b442800309b3a04d48191fd1153207820b73"

aliyun:
  access-key-id: "LTAI4FdqhciqpaNSEk1fjAd4"
  access-key-secret: "******************************"

  sms:
    enabled: false
    template:
      verification-code: "SMS_175330236"
      meeting:
        create: "SMS_486525256"
        start: "SMS_486525256"
        cancel: "SMS_486400280"
        change: "SMS_486460247"
    sign-name: "深蓝信息"
    access-key-id: ${aliyun.access-key-id}
    access-key-secret: ${aliyun.access-key-secret}

  vms:
    enabled: false
    access-key-id: ${aliyun.access-key-id}
    access-key-secret: ${aliyun.access-key-secret}
    endpoint: dyvmsapi.aliyuncs.com
    called-show-number: "02122797262"
    template:
      meeting:
        create:
          start-code: "TTS_318145091"
          menu-key-map-list:
            - key: "1"
              code: "TTS_313420217"
            - key: "2"
              code: "TTS_315085173"
            - key: "3"
              code: "TTS_315735134"
        start:
          start-code: "TTS_318145091"
          menu-key-map-list:
            - key: "1"
              code: "TTS_313420217"
            - key: "2"
              code: "TTS_315085173"
            - key: "3"
              code: "TTS_315735134"
        cancel:
          start-code: "TTS_316545129"
        change:
          start-code: "TTS_313995233"
          menu-key-map-list:
            - key: "1"
              code: "TTS_313420217"
            - key: "2"
              code: "TTS_315085173"
            - key: "3"
              code: "TTS_315735134"

  mns:
    enabled: false
    access-key-id: ${aliyun.access-key-id}
    access-key-secret: ${aliyun.access-key-secret}
    debug: false
    queues:
      voice-report:
        queue-name: "Alicom-Queue-1691889115587409-VoiceReport"
        message-type: "VoiceReport"
      sms-up:
        queue-name: "Alicom-Queue-1691889115587409-SmsUp"
        message-type: "SmsUp"

openai:
  zhipu-api-secret-key: e205997ec6464204a0017a7d822c6540.82QtNAjVUknvtZWk
