<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <charset>utf-8</charset>
            <pattern>%d %-5level [%thread] %logger{0}: %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.apache.ibatis.logging.LogFactory" level="ERROR"/>
    <logger name="org.hibernate.validator" level="ERROR"/>
    <logger name="org.mybatis.spring" level="ERROR"/>
    <logger name="com.shenlan.smartlogixmini.mybatis" level="ERROR"/>

    <root level="DEBUG">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
