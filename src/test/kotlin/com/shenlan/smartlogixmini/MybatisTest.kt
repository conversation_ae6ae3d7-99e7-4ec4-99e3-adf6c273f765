package com.shenlan.smartlogixmini

import com.shenlan.smartlogixmini.auto.PersonnelMapper
import com.shenlan.smartlogixmini.auto.PersonnelSearch
import com.shenlan.smartlogixmini.util.log
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.mybatis.spring.annotation.MapperScan
import org.mybatis.spring.boot.test.autoconfigure.MybatisTest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.context.annotation.ComponentScan
import org.springframework.test.context.ActiveProfiles

@MybatisTest
@MapperScan("com.shenlan.smartlogixmini.auto")
@ComponentScan("com.shenlan.smartlogixmini.mybatis")
@ActiveProfiles("dev")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MybatisTest {

    companion object {
        init {
            System.setProperty("spring.main.banner-mode", "off")
            java.util.logging.Logger.getLogger("org.springframework").level = java.util.logging.Level.OFF
        }
    }

    @Autowired
    private lateinit var personnelMapper: PersonnelMapper

    @Test
    fun testBaseMapperGetList() {
        val personnelSearch = PersonnelSearch()
        personnelSearch.ifPage = false
        val personnelList = personnelMapper.getList(personnelSearch)
        log.info("查询到人员数量: ${personnelList.size}")
    }
}
