import com.google.zxing.BarcodeFormat
import com.google.zxing.client.j2se.MatrixToImageWriter
import com.google.zxing.qrcode.QRCodeWriter
import com.shenlan.smartlogixmini.auto.Menu
import com.shenlan.smartlogixmini.util.MenuUtil
import com.shenlan.smartlogixmini.util.println
import org.junit.Test
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.time.LocalDate

class MenuTest {

    class CarListJspSimple<T>() {
        var rspCode: String = ""
        var list = mutableListOf<T>()
    }

    @Test
    fun generateNextWeekMenu() {
//        val certFactory = CertificateFactory.getInstance("X.509")
//        val cert = certFactory.generateCertificate(FileInputStream("D:\\Project\\shenlan\\smartlogix-mini\\master\\smartlogix-mini\\src\\main\\resources\\wechatpay\\apiclient_cert.pem")) as X509Certificate
//        val serialNumber = cert.serialNumber.toString(16).toUpperCase()
//
//        println("Merchant Serial Number: $serialNumber")
//        println(getMenuDateList(LocalDate.now().plusDays(-4)))
        println(URLEncoder.encode("https://www.shenlaninfo.com:8047/api/wechatpay/oauth", StandardCharsets.UTF_8.toString()))

        val width = 300
        val height = 300
        val qrCodeWriter = QRCodeWriter()
        val bitMatrix = qrCodeWriter.encode("weixin://wxpay/bizpayurl?pr=QEWYkciz3", BarcodeFormat.QR_CODE, width, height)

//        response.contentType = "image/png"
//        val stream = response.outputStream
//        MatrixToImageWriter.writeToStream(bitMatrix, "PNG", stream)
//        stream.flush()
//        stream.close()
        // 保存二维码
        println(MenuUtil.getLatestMenuSunday())
        println(MenuUtil.getMenuDateList())
        MatrixToImageWriter.writeToPath(bitMatrix, "PNG", java.nio.file.Paths.get("E:\\workspace\\2025\\06\\smart\\qrcode.png"))
    }
}
