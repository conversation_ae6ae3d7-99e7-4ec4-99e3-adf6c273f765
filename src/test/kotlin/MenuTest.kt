import com.shenlan.smartlogixmini.auto.MenuMapper
import com.shenlan.smartlogixmini.auto.MenucommentSearch
import com.shenlan.smartlogixmini.config.WeChatPayConfig
import com.shenlan.smartlogixmini.task.MenuTask
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.initMapper
import com.shenlan.smartlogixmini.util.localDateFormatter
import com.shenlan.smartlogixmini.util.println
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.junit4.SpringRunner
import java.io.FileInputStream
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import java.time.LocalDate

class MenuTest {


    @Test
    fun generateNextWeekMenu() {
        val certFactory = CertificateFactory.getInstance("X.509")
        val cert = certFactory.generateCertificate(FileInputStream("D:\\Project\\shenlan\\smartlogix-mini\\master\\smartlogix-mini\\src\\main\\resources\\wechatpay\\apiclient_cert.pem")) as X509Certificate
        val serialNumber = cert.serialNumber.toString(16).toUpperCase()

        println("Merchant Serial Number: $serialNumber")
    }
}