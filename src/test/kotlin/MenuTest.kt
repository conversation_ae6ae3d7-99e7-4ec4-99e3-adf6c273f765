import com.shenlan.smartlogixmini.auto.Commutevehiclelocation
import com.shenlan.smartlogixmini.task.BaseJspResponse
import com.shenlan.smartlogixmini.util.HttpUtil
import com.shenlan.smartlogixmini.util.toJsonString
import org.junit.Test

class MenuTest {

    class CarListJspSimple<T>() {
        var rspCode: String = ""
        var list = mutableListOf<T>()
    }

    @Test
    fun generateNextWeekMenu() {
//        val certFactory = CertificateFactory.getInstance("X.509")
//        val cert = certFactory.generateCertificate(FileInputStream("D:\\Project\\shenlan\\smartlogix-mini\\master\\smartlogix-mini\\src\\main\\resources\\wechatpay\\apiclient_cert.pem")) as X509Certificate
//        val serialNumber = cert.serialNumber.toString(16).toUpperCase()
//
//        println("Merchant Serial Number: $serialNumber")
        val locations = HttpUtil.getJsp<BaseJspResponse<Commutevehiclelocation>>("http://47.92.65.155:56666/gps-web/api/get_gps_r.jsp?sessionId=sQTl5VM5JmJ2EnMypzXz81ZidWZ5QmYtJ2X68VZ1gSM2IWMjpmY0FGZ0giMSVGVhNEM4BDZ1A1YYVjck5ENpZzMCNzZm9lYfZDZmRHZmQDZ0A2NxN2c4g2ZmIjMhJWY3MTYzEWaiZGMhRWY1sVYZdjT1EHNFBmYxI1MVhDWzUTZY").list.onEach {
            it.id = it.carId
        }
        println(locations.first().toJsonString)
    }
}