import com.fasterxml.jackson.module.kotlin.readValue
import com.google.zxing.BarcodeFormat
import com.google.zxing.client.j2se.MatrixToImageWriter
import com.google.zxing.qrcode.QRCodeWriter
import com.shenlan.smartlogixmini.auto.Commuteroute
import com.shenlan.smartlogixmini.auto.Commutetrip
import com.shenlan.smartlogixmini.auto.WeChatPayUtil.generatePaySign
import com.shenlan.smartlogixmini.util.ImageUtil
import com.shenlan.smartlogixmini.util.MenuUtil
import com.shenlan.smartlogixmini.util.customObjectMapper
import com.shenlan.smartlogixmini.util.localDateFormatter
import com.shenlan.smartlogixmini.util.localDateTimeFormatter
import com.shenlan.smartlogixmini.util.toJsonString
import org.junit.Test
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.time.LocalDate
import java.time.LocalDateTime


class MenuTest {

    class CarListJspSimple<T>() {
        var rspCode: String = ""
        var list = mutableListOf<T>()
    }

    @Test
    fun generateNextWeekMenu() {
//        val certFactory = CertificateFactory.getInstance("X.509")
//        val cert = certFactory.generateCertificate(FileInputStream("D:\\Project\\shenlan\\smartlogix-mini\\master\\smartlogix-mini\\src\\main\\resources\\wechatpay\\apiclient_cert.pem")) as X509Certificate
//        val serialNumber = cert.serialNumber.toString(16).toUpperCase()
//
//        println("Merchant Serial Number: $serialNumber")
//        println(getMenuDateList(LocalDate.now().plusDays(-4)))
        println(URLEncoder.encode("https://www.shenlaninfo.com:8047/api/wechatpay/oauth", StandardCharsets.UTF_8.toString()))

        val width = 300
        val height = 300
        val qrCodeWriter = QRCodeWriter()
        val bitMatrix = qrCodeWriter.encode("weixin://wxpay/bizpayurl?pr=vhIoeDtz3", BarcodeFormat.QR_CODE, width, height)

//        response.contentType = "image/png"
//        val stream = response.outputStream
//        MatrixToImageWriter.writeToStream(bitMatrix, "PNG", stream)
//        stream.flush()
//        stream.close()
        // 保存二维码
        println(MenuUtil.getLatestMenuSunday())
        println(MenuUtil.getMenuDateList())
        MatrixToImageWriter.writeToPath(bitMatrix, "PNG", java.nio.file.Paths.get("E:\\workspace\\2025\\06\\smart\\qrcode.png"))

//        simulateBus("", listOf(
//            CommuteUtil.MockPosition(22.673839, 114.131127),
//            CommuteUtil.MockPosition(22.681978, 114.125840),
//            CommuteUtil.MockPosition(22.670179, 114.122472)
//        ))cancelOrderScheduleWithTimer
//        cancelOrderScheduleWithTimer("1")
        val startTime: LocalDateTime = LocalDate.parse(MenuUtil.getMenuDateList().first(), localDateFormatter).atStartOfDay()
        println(localDateTimeFormatter.format(startTime))

        val str = generatePaySign("wx2421b1c4370ec43b", "1554208460", "593BEC0C930BF1AFEB40B4A08C8FB242", "prepay_id=wx201410272009395522657a690389285100",
            "E:\\workspace\\2025\\06\\test.pem")
        println(str)
        println("WMDC${localDateFormatter.format(LocalDateTime.now())}${String.format("%03d", 1 + 1)}")

//        Thread.sleep(600 * 1000)
        val map = mutableMapOf<String, List<TestSequence>>(
            "1" to listOf(TestSequence("A", 0), TestSequence("B", 1), TestSequence("C", 2)),
            "2" to listOf(TestSequence("D", 3), TestSequence("E", 4), TestSequence("F", 5))
        )

        var stopList = map.getOrDefault("1", listOf()).map { it.copy() }.sortedBy { it.sequence }
        stopList = stopList.reversed()
        stopList.forEachIndexed { index, stop -> stop.sequence = index }
        println(stopList.toJsonString)

        stopList = map.getOrDefault("1", listOf()).map { it.copy() }.sortedBy { it.sequence }
        stopList = stopList.reversed()
        stopList.forEachIndexed { index, stop -> stop.sequence = index }
        println(stopList.toJsonString)
    }

    data class TestSequence(
        var name: String,
        var sequence: Int
    )

    @Test
    fun test2() {
//        val client = ClientV4.Builder("e205997ec6464204a0017a7d822c6540.82QtNAjVUknvtZWk").build()
//        val createImageRequest: CreateImageRequest = CreateImageRequest().apply {
//            prompt = "包子"
//        }
//        createImageRequest.model = Constants.ModelCogView
//
//        //        createImageRequest.setPrompt("画一个温顺可爱的小狗");
//        val imageApiResponse = client.createImage(createImageRequest)
//        println("imageApiResponse:" + JSON.toJSONString(imageApiResponse))
//        println(customObjectMapper.readValue<ImageUtil.SimpleImageApiResponse>("{\"code\":200,\"data\":{\"array\":false,\"bigDecimal\":false,\"bigInteger\":false,\"binary\":false,\"boolean\":false,\"containerNode\":true,\"created\":1751615443,\"data\":[{\"array\":false,\"bigDecimal\":false,\"bigInteger\":false,\"binary\":false,\"boolean\":false,\"containerNode\":true,\"double\":false,\"float\":false,\"floatingPointNumber\":false,\"int\":false,\"integralNumber\":false,\"long\":false,\"missingNode\":false,\"nodeType\":\"OBJECT\",\"null\":false,\"number\":false,\"object\":true,\"pojo\":false,\"short\":false,\"textual\":false,\"url\":\"https://aigc-files.bigmodel.cn/api/cogview/20250704155043a1a1a683bf914968_0.png\",\"valueNode\":false}],\"double\":false,\"float\":false,\"floatingPointNumber\":false,\"int\":false,\"integralNumber\":false,\"long\":false,\"missingNode\":false,\"nodeType\":\"OBJECT\",\"null\":false,\"number\":false,\"object\":true,\"pojo\":false,\"short\":false,\"textual\":false,\"valueNode\":false},\"msg\":\"调用成功\",\"success\":true}").data.data.first().url.toJsonString)
//        val service = ImagexService.getInstance().apply {
//            accessKey = "AKLTNWJlZTA4MDA1ZTZkNDM0Yjg4OTUxZGZjOGVmY2IyZTM"
//            secretKey = "WVRrNFl6TTVZVGhrWWpjMk5EUmlZVGxtWlRnNU1EZGlNVEJtTW1NeU9XVQ=="
//        }
//
//        val query = GetCVTextGenerateImageQuery().apply {
//            serviceId = "9bskbuit6j"
//        }
//        val body = GetCVTextGenerateImageBody().apply {
//            template = "tplv-9bskbuit6j-image"
//            reqJson = mapOf(
//                "req_key" to "high_aes_general_v20_L",
//                "prompt" to "包子"
//            )
//            overwrite = false
//            modelAction = "CVProcess"
//            modelAction = "2022-08-31"
//            outputs = listOf<String>("AI/demo.png")
//        }
//
//        try {
//            val resp = service.getCVTextGenerateImage(query, body)
//            println(resp)
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
//        val str = """
//            {"id":"1d434f8d92f14323b2a816a10a08e60f","sysCreated":"2025-07-10 15:54","name":"","startStopName":"海源城","endStopName":"木古(地铁站)","routeType":0,"sysDeleted":0,"estimatedTime":0,"stopList":[{"sequence":0,"name":"海源城","lat":22.682391,"lng":114.125558,"flag":true},{"sequence":1,"name":"","lat":22.682175,"lng":114.125606,"flag":false},{"sequence":2,"name":"","lat":22.682175,"lng":114.125606,"flag":false},{"sequence":3,"name":"","lat":22.682127,"lng":114.125493,"flag":false},{"sequence":4,"name":"","lat":22.681993,"lng":114.1253,"flag":false},{"sequence":5,"name":"","lat":22.681993,"lng":114.1253,"flag":false},{"sequence":6,"name":"","lat":22.681676,"lng":114.125145,"flag":false},{"sequence":7,"name":"","lat":22.681676,"lng":114.125145,"flag":false},{"sequence":8,"name":"","lat":22.681644,"lng":114.125,"flag":false},{"sequence":9,"name":"","lat":22.681644,"lng":114.125,"flag":false},{"sequence":10,"name":"","lat":22.682116,"lng":114.124898,"flag":false},{"sequence":11,"name":"","lat":22.682352,"lng":114.12485,"flag":false},{"sequence":12,"name":"","lat":22.682379,"lng":114.124828,"flag":false},{"sequence":13,"name":"","lat":22.682481,"lng":114.124678,"flag":false},{"sequence":14,"name":"","lat":22.682481,"lng":114.124678,"flag":false},{"sequence":15,"name":"","lat":22.682095,"lng":114.124748,"flag":false},{"sequence":16,"name":"","lat":22.681563,"lng":114.124823,"flag":false},{"sequence":17,"name":"","lat":22.681376,"lng":114.124806,"flag":false},{"sequence":18,"name":"","lat":22.681311,"lng":114.124801,"flag":false},{"sequence":19,"name":"","lat":22.681311,"lng":114.124801,"flag":false},{"sequence":20,"name":"","lat":22.681258,"lng":114.124367,"flag":false},{"sequence":21,"name":"","lat":22.681075,"lng":114.123063,"flag":false},{"sequence":22,"name":"","lat":22.68099,"lng":114.122414,"flag":false},{"sequence":23,"name":"","lat":22.680968,"lng":114.122264,"flag":false},{"sequence":24,"name":"","lat":22.680909,"lng":114.121829,"flag":false},{"sequence":25,"name":"","lat":22.680727,"lng":114.12169,"flag":false},{"sequence":26,"name":"","lat":22.680383,"lng":114.121733,"flag":false},{"sequence":27,"name":"","lat":22.680147,"lng":114.12176,"flag":false},{"sequence":28,"name":"","lat":22.679868,"lng":114.121797,"flag":false},{"sequence":29,"name":"","lat":22.679166,"lng":114.121888,"flag":false},{"sequence":30,"name":"","lat":22.678651,"lng":114.121958,"flag":false},{"sequence":31,"name":"","lat":22.678356,"lng":114.121996,"flag":false},{"sequence":32,"name":"","lat":22.67724,"lng":114.12214,"flag":false},{"sequence":33,"name":"","lat":22.677133,"lng":114.122151,"flag":false},{"sequence":34,"name":"","lat":22.676988,"lng":114.122167,"flag":false},{"sequence":35,"name":"","lat":22.676585,"lng":114.122194,"flag":false},{"sequence":36,"name":"","lat":22.6765,"lng":114.122205,"flag":false},{"sequence":37,"name":"","lat":22.676258,"lng":114.122221,"flag":false},{"sequence":38,"name":"","lat":22.676167,"lng":114.122226,"flag":false},{"sequence":39,"name":"","lat":22.675781,"lng":114.122264,"flag":false},{"sequence":40,"name":"","lat":22.675491,"lng":114.122291,"flag":false},{"sequence":41,"name":"","lat":22.675443,"lng":114.122291,"flag":false},{"sequence":42,"name":"","lat":22.675019,"lng":114.122307,"flag":false},{"sequence":43,"name":"","lat":22.674483,"lng":114.122301,"flag":false},{"sequence":44,"name":"","lat":22.674321,"lng":114.122312,"flag":false},{"sequence":45,"name":"","lat":22.673656,"lng":114.122328,"flag":false},{"sequence":46,"name":"","lat":22.673211,"lng":114.122339,"flag":false},{"sequence":47,"name":"","lat":22.673093,"lng":114.122344,"flag":false},{"sequence":48,"name":"","lat":22.672444,"lng":114.122355,"flag":false},{"sequence":49,"name":"","lat":22.672278,"lng":114.12236,"flag":false},{"sequence":50,"name":"","lat":22.67187,"lng":114.122371,"flag":false},{"sequence":51,"name":"","lat":22.671746,"lng":114.122366,"flag":false},{"sequence":52,"name":"","lat":22.671371,"lng":114.122366,"flag":false},{"sequence":53,"name":"","lat":22.670845,"lng":114.122382,"flag":false},{"sequence":54,"name":"","lat":22.670051,"lng":114.122393,"flag":false},{"sequence":55,"name":"","lat":22.669805,"lng":114.122398,"flag":false},{"sequence":56,"name":"","lat":22.669815,"lng":114.122553,"flag":false},{"sequence":57,"name":"","lat":22.670046,"lng":114.122548,"flag":false},{"sequence":58,"name":"木古(地铁站)","lat":22.67018,"lng":114.122545,"flag":true}],"vehicles":[],"followed":false,"routeTypeCn":"上班"}
//        """.trimIndent()
//        val route = customObjectMapper.readValue<Commuteroute>(str)
//        println(route.toJsonString)
    }
}
