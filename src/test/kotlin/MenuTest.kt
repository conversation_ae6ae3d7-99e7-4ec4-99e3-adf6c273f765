import com.shenlan.smartlogixmini.auto.Commutvehicle
import com.shenlan.smartlogixmini.util.HttpUtil
import com.shenlan.smartlogixmini.util.toJsonString
import org.junit.Test

class MenuTest {


    @Test
    fun generateNextWeekMenu() {
//        val certFactory = CertificateFactory.getInstance("X.509")
//        val cert = certFactory.generateCertificate(FileInputStream("D:\\Project\\shenlan\\smartlogix-mini\\master\\smartlogix-mini\\src\\main\\resources\\wechatpay\\apiclient_cert.pem")) as X509Certificate
//        val serialNumber = cert.serialNumber.toString(16).toUpperCase()
//
//        println("Merchant Serial Number: $serialNumber")
        val response = HttpUtil.getJsp<Commutvehicle>("http://47.92.65.155:56666/gps-web/api/get_car_list.jsp?sessionId=s8UMgR2eghCNzRGdiFmMhJDZ1cWYmZzblFGMh91NfNTMkFTZnRGZj9lNfRDQnR0YEJTSyI1NKV2U3okZuZzd48UZCFzZi9lNfJDZ6Q3MmcGZnBWOxJ2cihmOmUmMoImN3ETYqFGdiRGMoQmU1UUYCdDT1oFNSB2Yxg1MYhzUzUUZY")
        println(response.toJsonString)
    }
}