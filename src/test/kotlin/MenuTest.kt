import com.shenlan.smartlogixmini.auto.Commutevehiclelocation
import com.shenlan.smartlogixmini.task.BaseJspResponse
import com.shenlan.smartlogixmini.util.HttpUtil
import com.shenlan.smartlogixmini.util.getBean
import com.shenlan.smartlogixmini.util.toJsonString
import org.junit.Test

class MenuTest {

    class CarListJspSimple<T>() {
        var rspCode: String = ""
        var list = mutableListOf<T>()
    }

    @Test
    fun generateNextWeekMenu() {
//        val certFactory = CertificateFactory.getInstance("X.509")
//        val cert = certFactory.generateCertificate(FileInputStream("D:\\Project\\shenlan\\smartlogix-mini\\master\\smartlogix-mini\\src\\main\\resources\\wechatpay\\apiclient_cert.pem")) as X509Certificate
//        val serialNumber = cert.serialNumber.toString(16).toUpperCase()
//
//        println("Merchant Serial Number: $serialNumber")
    }
}