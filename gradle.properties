# Gradleéç½®æä»¶
# æºæ§åå¤å°ç¨åºé¡¹ç®éç½®

# ==============================
# æå»ºæ§è½ä¼å
# ==============================
# å¯ç¨Gradleå®æ¤è¿ç¨
org.gradle.daemon=true

# å¯ç¨å¹¶è¡æå»º
org.gradle.parallel=true

# éç½®æå»ºç¼å­
org.gradle.caching=true

# è®¾ç½®JVMåæ°
org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# ==============================
# ä¾èµè§£æä¼å
# ==============================
# å¯ç¨ä¾èµéªè¯
org.gradle.dependency.verification=lenient

# ç½ç»è¶æ¶è®¾ç½®ï¼æ¯«ç§ï¼
systemProp.org.gradle.internal.http.connectionTimeout=60000
systemProp.org.gradle.internal.http.socketTimeout=60000

# éè¯æ¬¡æ°
systemProp.org.gradle.internal.repository.max.retries=3

# ==============================
# Kotlinç¼è¯ä¼å
# ==============================
# å¯ç¨Kotlinå¢éç¼è¯
kotlin.incremental=true

# å¯ç¨Kotlinå¹¶è¡ç¼è¯
kotlin.parallel.tasks.in.project=true

# ==============================
# ç¼ç è®¾ç½®
# ==============================
# è®¾ç½®æä»¶ç¼ç 
systemProp.file.encoding=UTF-8
systemProp.sun.jnu.encoding=UTF-8

# ==============================
# å®å¨è®¾ç½®
# ==============================
# åè®¸ä¸å®å¨çHTTPä»åºï¼ä»å¼åç¯å¢ï¼
systemProp.org.gradle.internal.publish.checksums.insecure=true
