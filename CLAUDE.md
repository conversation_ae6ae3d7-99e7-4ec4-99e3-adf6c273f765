<project-rule>

# 项目总体

## 项目简介

懃务通是一款智慧后勤小程序，集成便捷登录、物业报修、巡检记录、会议预约、派车管理等核心功能，覆盖后勤管理全场景；支持菜谱管理、面食预订、理发预约、通勤班车、挪车等生活服务线上化，实现固定资产、日用品智能管控，同步搭载安全管理、新闻公告、配置管理、人事管理等基础模块；以数字化推动后勤管理中心信息与智能一体化，通过一站式服务体系简化职工办事流程，提升后勤服务效率与体验，助力构建现代化后勤管理新模式。

## 技术栈要求

| 类别       | 技术选型              | 版本     |
| ---------- | --------------------- | -------- |
| 编程语言   | Kotlin                | 1.2.41   |
| 构建工具   | Gradle                | 4.10.1   |
| 框架       | Spring Boot           | 2.0.4    |
| 安全框架   | Spring Security       | 2.0.4    |
| 数据库     | MariaDB               | 10.5.8   |
| ORM        | MyBatis (注解动态SQL) | 3.5.1    |
| HTTP客户端 | OkHttp                | 3.12.13  |

## 项目架构

```markdown
- `src/main/kotlin/com/shenlan/smartlogixmini/`
  - `auto/` - 自动生成的业务模块代码
    - `Base.kt` - 基础类(BaseModel, BaseMapper, BaseService等)和Result响应类
    - `[各业务模块].kt` - 每个文件包含模型类、Mapper接口、Service类、Controller类
- `sql/` - SQL脚本目录
  - `database.sql` - 数据库初始化脚本，包含建表SQL
  - `migrations/` - 数据库迁移脚本目录
    - `V[序号]__[日期]_[变更描述].sql` - 数据库结构变更迁移文件
- `webapp/` - 前端根目录
- `webapp-vue/` - 前端根目录
- `www/` - 前端打包目录
```

</project-rule>

<development-guideline-rule>

# 开发准则

## 升级规则
**犯错识别**：当用户指出我违反了某个规范时，该规范犯错次数+1
**级别升级**：
- L0 → L1：第1次犯错
- L1 → L2：第2次犯错  
- L2 → L3：第3次犯错
- L3 → L4+：第4次及以上犯错

**操作步骤**：
1. 在对应级别标题的犯错次数+1
2. 将该规范从当前级别组移动到更高级别组
3. **完全删除**旧级别组中的该规范内容
4. 保持规范在新级别组内的分类归属
5. 更新规范内容以强化重要性（可选）

## 🔥💥 L4+ 级别规范 (已犯错4次以上)
[目前暂无]

## 💥 L3 级别规范 (已犯错3次)  

### 代码风格规范
- **完全限定名称禁用**：在代码中必须使用`import`导入所有类型，如`import com.shenlan.smartlogixmini.mybatis.PaginationInfo`后直接使用`PaginationInfo`，**严禁**使用`com.shenlan.smartlogixmini.mybatis.PaginationInfo`形式。**这个规则无例外，必须始终遵守！已经犯错3次，绝对不能再犯！** 
  - 正确做法：使用`import`语句导入类型，然后直接使用类型名
  - 错误做法：在代码中使用完整包名路径
  - **特别注意**：当有类型名冲突时，使用`import ... as ...`语法创建别名，如`import com.shenlan.smartlogixmini.util.MediaType as WechatMediaType`

## 🚨 L2 级别规范 (已犯错2次)
[目前暂无]

## ⚠️ L1 级别规范 (已犯错2次)

### 代码风格规范
- **日志使用具体规范**：必须根据代码所在位置正确选择日志调用方式。**在类内部（包括类方法、companion object等）必须使用`log.info()`**，**在类外部（如顶层函数、扩展函数等）必须使用`"".log.info()`**。严禁在类内部使用`"".log.info()`的冗余写法！例如：
  - 类内部正确：`log.info("message")`
  - 类外部正确：`"".log.info("message")`
  - 类内部错误：`"".log.info("message")`（不必要的冗余写法）
- **日志格式规范**：日志语句中变量名应放在字符串中，变量值作为参数置于末尾。**这是强制性规范，必须严格遵守！** 格式为`log.info("Action description. param1={}, param2={}", value1, value2)`。**严禁使用字符串插值或字符串拼接**。例如：
  - 正确：`log.info("User login successful. username={}, source_ip={}", username, ipAddress)`
  - 正确：`log.info("Testing wechat media check. imageUrl={}, openid={}", imageUrl, openid)`
  - 错误：`log.info("Testing wechat media check for image: $imageUrl, openid: $openid")`
  - 错误：`log.info("User login successful for user: ${username}")`
  - **重要提醒**：任何包含变量的日志语句都必须使用参数化格式，这确保了日志的安全性和性能

### 架构与继承规范
- **Mapper.getList调用限制**：除了`Service.getEntityList`方法自身，**正常情况下其他所有地方都不要调用`Mapper.getList`**。包括其他Service方法、Controller层等都应该调用`Service.getEntityList`而不是直接调用`Mapper.getList`。这确保了分层架构的完整性，业务逻辑集中在Service层，避免绕过Service层的业务处理逻辑！

### 异常处理规范
- **事务自调用避免**：Spring AOP代理机制导致同一类中的`@Transactional`方法内部调用不会触发事务。解决方案：**确保调用者方法也有`@Transactional`注解**，这样所有操作都在同一个事务中执行。例如：在有`@Transactional`注解的方法中直接调用`deleteEntity()`等方法即可，无需使用`super.methodName()`

## L0 级别规范 (基础规范)

### 代码风格规范
- **工具函数导入规范**：使用工具函数时必须明确导入，不能直接使用未导入的函数。例如使用`getUser()`函数时必须添加`import com.shenlan.smartlogixmini.util.getUser`。编写代码时要检查所有使用的函数和类型是否已正确导入
- **日志语言规范**：使用英文编写日志文本，如`log.info("English message")`，以避免日志文件出现编码问题
- **注释语言规范**：代码注释应使用中文，提高中文阅读者的理解速度
- **属性分组规范**：将类属性定义分组，组内属性之间不加空行，组与组之间用一个空行隔开，并在每组开头添加明确的分组注释
- **方法逻辑分组**：方法体内部必须按逻辑分组，每组前加注释，组间空行，提升可读性和维护性
- **实体类属性分组**：实体类属性应分为三组：`数据库表字段`、`关联字段`和`其他字段`，各组间用空行分隔
- **类型转换注解**：转换对象为特定类型列表时（如`as List<XXX>`），添加`@Suppress("UNCHECKED_CAST")`注解避免编译警告
- **前端信息语言**：向前端返回结果时使用中文信息，如`Result.getSuccessInfo("中文信息")`、`Result.getError("中文信息")`
- **Result类方法使用规范**：
  - `Result.getSuccess(data)` - 返回成功结果并包含数据
  - `Result.getSuccessInfo(info)` - 仅返回成功结果和消息，不包含数据
  - `Result.getError(info)` - 返回错误结果和错误消息
  - `Result.getError(info, data)` - 返回错误结果、错误消息和额外数据
  - **注意**：`getSuccessInfo`方法只接受一个字符串参数，不接受数据参数，如需返回数据请使用`getSuccess(data)`
- **异常日志记录**：使用全局函数`fun errorlog(e: Throwable)`打印异常堆栈信息，确保异常被完整记录
- **字符串模板规范**：Kotlin字符串模板中，单独变量后接非标识符字符时省略花括号（如`"Hello $name"`），但属性访问（如`${user.name}`）、表达式（如`${a+b}`）或存在歧义（如`${name}123`）必须保留花括号
- **时区处理规范**：在Java/Kotlin中将`Date`转换为`Instant`进行时间处理时，需注意`toInstant()`方法默认使用UTC时区，在中国（UTC+8）环境下会导致时间偏差8小时；应使用`date.toInstant().atZone(ZoneId.systemDefault()).toInstant()`确保正确应用本地时区
- **标准库优先原则**：优先使用Kotlin标准库方法：在处理集合操作、文件遍历、字符串处理等常见任务时，应首先考虑使用Kotlin标准库提供的扩展函数（如`walkTopDown()`、`filter()`、`map()`、`forEach()`等）和高阶函数，而非手动实现循环或递归，以提高代码简洁性、可读性和性能。
- **尾随逗号禁用**：在`Kotlin 1.2.41`项目中，数据类构造函数、函数参数列表和对象构造调用的最后一个参数后不能有尾随逗号。务必检查并移除所有尾随逗号以保持兼容性。
- **单行文档注释**：单行内容的文档注释使用 `/** 文档注释 */` 格式，不要使用多行格式
- **多行文档注释**：多行内容的文档注释使用标准的 `/** ... */` 多行格式
- **HTTP映射注解使用**：方法级别优先使用具体的HTTP方法注解（`@GetMapping`、`@PostMapping`）而不是通用的`@RequestMapping`
- **HTTP方法选择规范**：根据是否需要请求体来选择HTTP方法：
  - `@GetMapping`：不需要请求体的操作（通过URL参数传递数据）
    - 示例：`@GetMapping("/getInfo/{id}")`、`@GetMapping("/sendCode/{phone}")`、`@GetMapping("/verify/{phone}/{code}")`
    - 所有参数通过 `@PathVariable` 或 `@RequestParam` 传递
  - `@PostMapping`：需要请求体的操作（通过@RequestBody传递复杂数据）
    - 示例：`@PostMapping("/getList")`、`@PostMapping("/save")`
    - 复杂对象通过 `@RequestBody` 传递
  - **严禁使用**：`@PutMapping`、`@DeleteMapping`、`@PatchMapping`等其他HTTP方法注解
  - **重要提醒**：即使是修改操作，如果所有参数都通过URL路径传递（如验证码验证），也应使用`@GetMapping`而非`@PostMapping`
  - **API修改重新评估规范**：修改API方法参数时，必须重新评估HTTP方法选择。去掉`@RequestBody`参数后应改为`@GetMapping`，添加`@RequestBody`参数后应改为`@PostMapping`。不能仅仅修改参数而保持原有的HTTP方法注解
- **Kotlin空安全规范**：可空类型变量即使前面有`isNullOrEmpty()`等空检查，仍必须使用安全调用操作符`?.`或非空断言`!!.`。例如：`if (!str.isNullOrEmpty()) { str?.split("|") }`或`if (!str.isNullOrEmpty()) { str!!.split("|") }`，不能写成`str.split("|")`，因为编译器无法识别逻辑上的非空保证。当逻辑上确保非空时，优先使用`!!.`断言
- **低版本Kotlin空安全断言规范**：在Kotlin 1.2.41中，即使有`isNullOrEmpty()`等空检查逻辑保证，可空类型变量仍需要使用非空断言`!!`才能通过编译。此时必须添加`@Suppress("UNNECESSARY_NOT_NULL_ASSERTION")`注解来消除编译器的"不必要的非空断言"警告。例如：在空检查后使用`@Suppress("UNNECESSARY_NOT_NULL_ASSERTION") return response.openlink!!`
- **方法重用优先原则**：新增功能时应优先考虑重用现有方法，避免重复实现相同逻辑。例如批量处理功能应通过调用单个处理方法实现（如`fileList.map { uploadFile(it) }`），而不是重复编写相同的处理逻辑
- **Data Class复制规范**：修改`data class`对象的部分属性时，优先使用`copy()`方法而非手动构造。例如：使用`paginationInfo.copy(result = newList)`而不是`PaginationInfo(...)`或`PaginationInfo.of(...)`。`copy()`方法更安全、简洁，能避免参数错误和遗漏
- **PaginationInfo结果替换规范**：`PaginationInfo`不是data class，没有`copy()`方法。需要替换结果数据时，必须使用`withResult()`方法，如：`paginationInfo.withResult(newList)`。严禁使用`paginationInfo.copy()`，这会导致编译错误
- **Data Class命名与JSON映射规范**：数据类属性必须使用驼峰命名法，即使对应的JSON字段使用下划线命名。通过`@JsonProperty`注解进行字段映射。例如：`@JsonProperty("access_token") val accessToken: String`而不是`val access_token: String`。这确保了代码风格的一致性和可读性
- **Search类字段顺序规范**：Search类中的字段顺序应与对应实体类的字段顺序保持一致。在Search类中添加新字段时，必须参考实体类的字段顺序进行插入，而不是随意添加到最后。例如：如果实体类字段顺序为`ownerWorkgroupId`、`organizationId`、`name`，则Search类也应按此顺序排列对应的查询字段，这样便于代码维护和字段对应关系的理解
- **枚举类设计规范**：枚举类应保持简洁，避免添加不必要的属性。枚举常量名称本身就具有自我描述性，通常不需要额外的`description`或`name`属性。例如：使用`enum class LoginMethod { WECHAT_PHONE, PHONE_CODE, USERNAME_PASSWORD }`而不是`enum class LoginMethod(val description: String) { WECHAT_PHONE("微信手机号"), ... }`
- **避免不必要的方法创建**：对于简单的属性赋值操作（如`entity.property = value`），不要创建专门的方法。只有当操作涉及复杂业务逻辑、数据验证、事务处理或多步骤操作时，才需要封装为方法。例如：`personnel.currentLoginMethod = loginMethod` 无需封装方法
- **companion object位置规范**：`companion object`必须放在类的尾部，位于所有其他成员（属性、方法、内部类等）之后。这是Kotlin的标准实践，有助于保持代码结构清晰和一致性
- **变量命名冲突避免规范**：在方法中创建新变量时，必须检查是否与现有参数名或局部变量名冲突。如果存在冲突，应使用更具体的变量名。特别是在处理不同类型的Search对象时，应使用具体的类型前缀来区分。例如：在已有`@RequestBody search: MenuSearch`参数的方法中，如需创建PersonnelSearch对象，应命名为`personnelSearch`而非`search`，避免变量名重复导致编译错误或逻辑混乱
- **代码空行格式规范**：删除代码（方法、属性、注释等）后必须检查并修正空行格式。类成员之间应保持1个空行，严禁出现连续的多个空行。删除代码时要确保前后元素之间的空行数量符合规范，避免留下3行、4行等多余空行，影响代码整洁性和可读性
- **无用代码清理规范**：修改代码逻辑后必须检查并删除不再使用的变量、导入、方法调用等无用代码。特别注意：
  - **无用变量清理**：删除条件判断、异常处理等代码后，检查相关变量是否仍被使用，如`val result = someMethod()`后续不再使用result变量则应删除
  - **无用导入清理**：删除代码后检查import语句是否仍然需要，及时清理未使用的导入
  - **无用方法调用清理**：简化逻辑后检查是否有不再需要的方法调用
  - **无用参数清理**：修改方法后检查参数是否都被使用，删除不再需要的参数
  - 保持代码简洁，避免留下"僵尸代码"影响可读性和维护性
- **API路径命名规范**：API路径名称必须与方法名保持完全一致。例如：方法名为`getCurrentSolarTermImage`时，路径必须为`@GetMapping("/getCurrentSolarTermImage")`；方法名为`uploadBatch`时，路径必须为`@PostMapping("/uploadBatch")`。严禁路径名与方法名不匹配，如禁止使用`@GetMapping("/solarTerm/current")`对应`getCurrentSolarTermImage`方法
- **@JsonProperty注解完整性规范**：当数据类中有任何属性使用`@JsonProperty`注解进行JSON字段映射时，必须为该类的所有属性都添加`@JsonProperty`注解，以保持注解使用的一致性。例如：如果类中有`@JsonProperty("env_version") val envVersion: String`，则其他属性也必须添加相应的注解如`@JsonProperty("path") val path: String`、`@JsonProperty("query") val query: String`
- **Companion Object属性访问规范**：在类内部访问companion object的属性时，直接使用属性名即可，无需添加类名前缀。在类外部访问时才需要使用完整的类名前缀。例如：
  - 类内部正确：`loginCodeMap[key] = value`（在SecurityConfiguration类内）
  - 类外部正确：`SecurityConfiguration.loginCodeMap[key] = value`（在其他类中）
  - 类内部错误：`SecurityConfiguration.loginCodeMap[key] = value`（不必要的冗余前缀）
- **循环写法优化规范**：使用固定次数循环时，优先使用`for (i in 0 until n)`而不是`while`循环配合计数器变量。例如：使用`for (i in 0 until 20)`而不是`var loopCount = 0; while (loopCount < 20) { ... loopCount++ }`。这种写法更简洁、可读性更好，减少了手动管理计数器变量的复杂性

## 命名规范

- **模型类命名**：数据库表对应的模型类命名采用首字母大写其余小写形式，如`Userinfo`、`Loginlog`
- **API路径命名**：API路径中的实体类名应与实体类类名保持一致，首字母大写
- **数据库命名规范**：数据库表名使用全小写无分隔符形式，仅添加`tbl_`前缀，而字段名则使用驼峰命名法
- **方法命名规范**：假设实体名为`Aaa`，其相关类（`AaaMapper`、`AaaService`、`AaaResource`）的方法应命名为`getListByBbb`，而非`getAaaByBbb`
- **接口命名规范**：Kotlin接口命名不使用`I`前缀，应直接使用`interface Aaa`而非`interface IAaa`
- **集合字段命名**：集合类型字段命名应使用对应的类型后缀，如：List字段使用`List`后缀（如`userList`）、Set字段使用`Set`后缀（如`userSet`）、Map字段使用`Map`后缀（如`userMap`），以提高代码一致性和可读性
- **布尔字段命名规范**：布尔类型字段命名应使用`if`前缀而非`is`前缀，避免与Spring Boot等框架的getter/setter命名约定产生冲突。例如：使用`ifWorkgroupDefault`而不是`isWorkgroupDefault`，使用`ifActive`而不是`isActive`
- **布尔字段类型选择规范**：Kotlin实体类中的布尔字段应使用`Boolean`类型而非`Int`类型，默认值设为`false`。例如：`var ifAllowManualResubscribe: Boolean = false`而不是`var ifAllowManualResubscribe: Int = 0`。这样可以利用Kotlin的类型安全和布尔值语义
- **布尔字段注释规范**：布尔字段的注释应避免使用数字描述（如`(0-否,1-是)`），而应使用简洁的布尔语义描述。例如：使用`/** 是否启用 */`而不是`/** 是否启用(0-否,1-是) */`，使用`/** 是否允许手动重复订阅 */`而不是`/** 是否允许手动重复订阅(0-否,1-是) */`。这样可以避免与数据库层的整型表示产生混淆，保持代码层面的布尔语义清晰

## 方法参数规范

- **BaseService参数名**：`BaseService`子类重写方法时，参数名应使用`page`而非`search`，如：`override fun getList(page: BaseSearch): Result`

## 数据库规范

- **表名规范**：表名除`tbl_`前缀外应全部小写且不含分隔符
- **字段默认值**：字符串类型的表字段与类属性默认应为非空，默认值设为空字符串
- **日期字段规范**：Search类中的日期查询字段（如startDate、endDate）应使用String类型，格式为`yyyy-MM-dd`，便于前端传参和SQL查询
- **计数查询规范**：计数查询使用`COUNT(*)`而非`COUNT(1)`
- **事务管理**：数据库事务管理通过`@Transactional`注解实现
- **自动更新时间**：MariaDB已设置`ON UPDATE CURRENT_TIMESTAMP`，因此Mybatis SQL不应使用`sysUpdated = NOW()`
- **动态SQL标签**：仅在使用动态SQL的Mybatis查询中添加`<script>`标签，静态SQL无需添加
- **删除策略**：默认采用物理删除策略而非逻辑删除
- **外键字段顺序**：数据库建表SQL中的外键字段应紧跟在`id`字段之后定义，保持字段顺序的一致性和可读性
- **where标签使用**：MyBatis动态SQL中使用`<where>`标签时，无需添加`WHERE 1=1`条件，`<where>`标签会自动处理条件拼接和`WHERE`关键字的添加
- **MyBatis集合参数绑定**：当Mapper方法参数是Collection、List或Array类型时，必须使用`@Param`注解明确指定参数名，否则MyBatis会使用内部参数名`collection`/`list`/`array`导致动态SQL中的参数无法找到。例如：`fun getByIds(@Param("ids") ids: List<String>)`
- **database.sql文件处理规范**：`sql/database.sql`是数据库初始化脚本，不是增量脚本。修改数据库表结构时，应直接在对应的`CREATE TABLE`语句中添加、修改或删除字段定义，**严禁使用`ALTER TABLE`语句**。所有数据库结构变更都应通过修改初始化脚本完成
- **数据库迁移文件规范**：所有数据库结构变更必须同时创建迁移文件记录在`sql/migrations/`目录下。迁移文件命名格式为`V[序号]__[日期]_[变更描述].sql`（如`V3__20250707_tbl_content_add_name_field.sql`）。迁移文件使用`ALTER TABLE`等DDL语句记录增量变更，用于跟踪数据库结构演进历史
- **布尔字段类型规范**：数据库表中的布尔字段必须使用`TINYINT`类型而非`INT`类型，格式为`fieldName TINYINT NOT NULL DEFAULT 0 COMMENT '字段说明(0-否,1-是)'`。这样可以节省存储空间且符合布尔值的语义
- **字段位置一致性规范**：在`database.sql`初始化脚本和迁移文件中添加字段时，必须确保字段位置完全一致。在迁移文件中使用`ALTER TABLE ADD COLUMN`时，必须使用`AFTER existing_field`语法指定正确的字段位置，确保与初始化脚本中的字段顺序保持一致。例如：`ALTER TABLE tbl_organization ADD COLUMN newField TINYINT NOT NULL DEFAULT 0 COMMENT '说明' AFTER existingField;`
- **关联ID字段位置规范**：实体类中添加关联ID字段（如branchOrganizationId、organizationId等）时，必须确保在实体类和数据库表中都放在正确的位置。关联ID字段应紧跟在id字段之后，与数据库表的字段顺序保持一致。严禁在实体类中随意将关联ID字段放在其他位置。例如：添加branchOrganizationId时，在数据库表中应放在`id, pid`之后，在实体类中也必须相应地放在pid字段之后

## 异常处理规范

- **全局异常处理**：`@RestController`/`@Controller`/`@Service`类的方法有全局异常处理器，应打印必要日志后直接抛出异常，避免手动返回错误结果和重复打印堆栈信息
- **事务注解保留**：`@Transactional`注解的作用是保证数据一致性和异常时回滚，与全局异常处理器职责不同。即使有全局异常处理器，仍需保留`@Transactional`注解来保护数据库操作的完整性
- **@Transactional注解强制使用规范**：以下情况的方法必须添加`@Transactional`注解，确保数据一致性：
  - **多个数据库操作的方法**：方法内包含多次数据库写操作（如先查询判断是否存在，不存在则创建，然后再更新）
  - **条件性数据创建方法**：方法内根据条件判断是否需要创建新记录的逻辑
  - **循环中的数据库操作**：方法内在循环中执行数据库写操作
  - **跨表操作方法**：方法内操作多个不同的数据表
  - **数据库操作与业务逻辑混合**：方法内既有数据库操作又有业务逻辑处理，且需要保证原子性
  - 如果任何一个数据库操作失败，整个方法的所有数据库操作都应该回滚，则必须使用`@Transactional`

## 架构与继承规范

- **基类方法重写**：`BaseMapper`/`BaseService`/`BaseResource`已定义基础方法，子类应重写这些方法而非创建新方法
- **HTTP映射注解保持**：重写`BaseResource`中的方法时，必须保持与基类相同的HTTP映射注解。例如重写`getList`方法时必须添加`@PostMapping("/getList")`注解，重写`getInfo`方法时必须添加`@GetMapping("/getInfo/{id}")`注解
- **方法签名完全保持**：重写`BaseResource`中的方法时，必须保持与基类完全相同的方法签名，包括参数注解。例如重写`getList`方法时必须保持`@Valid @RequestBody`参数注解，重写`save`方法时必须保持`@Valid @RequestBody`参数注解，重写`getInfo`方法时必须保持`@PathVariable`参数注解
- **@Valid注解使用规范**：仅在需要进行Bean Validation校验时才使用`@Valid`注解，对于简单的数据传输或不需要校验的场景应省略此注解。具体规则：
  - **需要使用@Valid的场景**：复杂对象有validation注解（如@NotNull、@NotBlank、@Size等）且需要校验
  - **不需要使用@Valid的场景**：简单集合（如`List<String>`）、基本类型、不需要校验的对象
  - 避免为了保持一致性而添加不必要的`@Valid`注解，应根据实际校验需求决定是否使用
- **构造函数优先级**：优先使用主构造函数，避免次要构造函数
- **继承调用规范**：Kotlin继承类时，通过主构造函数调用父类构造函数，如`class Meetingroom : BaseModel()`
- **关联对象加载层级**：获取数据模型关联对象的操作必须在Service层完成而非Mapper层，因为Mapper层无法正确加载嵌套的关联对象（关联对象的关联对象）
- **Service层调用规范**：Service层补充关联对象数据时，必须调用其他实体的Service层方法，不应直接使用Mapper层，确保关联对象的完整性和一致性
- **关联查询方法调用**：Service层的`getInfo`和`getList`方法查询关联对象时，必须使用其他实体Service层的`getInfo`和`getList`方法，而非Mapper层的同名方法，以确保关联数据的完整加载
- **依赖注入方式**：Spring依赖注入优先使用构造器注入方式
- **依赖注入清理规范**：修改代码后要检查并清理不再使用的依赖注入。如果某个注入的依赖在修改后不再被使用，应及时从构造函数中删除，保持依赖关系的简洁性。例如：修改Controller方法改为调用Service而不是直接调用Mapper后，应删除不再使用的Mapper依赖注入
- **BaseMapper方法限制**：注意`BaseMapper`接口中不存在`update`方法，请使用正确的替代方法
- **批量插入操作**：需要批量插入数据时，应使用`BaseMapper`接口提供的`insertList`方法，而非自定义批量插入方法。该方法接受`List<BaseModel>`参数，可高效完成批量数据插入操作
- **分层职责规范**：Controller层负责格式校验、基础约束校验和Result包装，Service层负责业务逻辑校验
- **Service方法返回值**：BaseService子类方法的返回值不要为Result，而是在BaseResource子类方法中包装Result
- **Service自定义方法规范**：BaseService子类如果添加自定义查询方法（如getInfoByDate），返回值应直接返回数据对象或列表，不要包装为Result
- **Service简单查询方法限制**：BaseService子类不应添加仅仅是对Mapper层方法的简单封装的查询方法。如果某个查询方法在Service层没有额外的业务逻辑处理，应该让调用者直接使用Mapper层方法，避免不必要的方法包装
- **层次调用优化**：如果Service层只是单纯调Mapper层方法而无额外业务逻辑，就没必要单独写Service层方法，而是在Controller层直接调Mapper层方法
- **Service间Mapper访问禁止**：严禁在Service中通过其他Service的mapper属性访问Mapper，如禁止`otherService.mapper.method()`写法。必须通过构造器注入所需的Mapper依赖，确保依赖关系清晰和Spring管理的一致性
- **BaseService新方法优先使用**：BaseService提供了新旧两套方法，优先使用新方法（`getEntityPage()`、`getEntityList()`、`getEntity()`、`saveEntity()`、`deleteEntity()`、`deleteEntityLogic()`），这些方法返回直接的数据对象。传统方法（`getList()`、`getInfo()`、`save()`、`delete()`、`deleteLogic()`）返回Result包装对象，仅为兼容性保留。子类不能同时重写新方法和传统方法
- **删除实体时关联数据处理**：重写`deleteEntity`方法时，必须考虑并处理所有相关联的实体数据。特别是当实体拥有默认账号、特殊关联关系或依赖关系时，应在删除主实体前先删除这些关联数据。例如删除工作组时必须先删除其默认账号人员，删除部门时需处理所属人员。删除关联数据时应使用对应Service的deleteEntity方法而非直接操作Mapper，确保删除逻辑的完整性和一致性
- **当前用户获取规范**：获取当前登录用户信息必须使用`getUser()`方法，该方法返回`Personnel?`类型。需要用户ID时使用`getUser()?.id`，避免在Search对象中预设currentUserId字段，而应在Service层动态获取。例如：`val currentUserId = getUser()?.id ?: return`
- **BaseSearch查询全部数据规范**：当需要查询某个实体的全部数据（不分页）时，必须设置`search.ifPage = false`，而不是设置很大的`pageSize`值。这是正确的禁用分页方式，确保查询返回所有匹配的记录
- **BaseService方法重写限制**：严禁重写`getEntityList()`方法，该方法内部会自动调用`getEntityPage()`方法。如需自定义查询逻辑，应重写`getEntityPage()`方法，`getEntityList()`会自动获得相同的功能。重写错误的方法会导致逻辑混乱和重复处理
- **Service层实体更新规范**：在Service层中对实体对象进行更新时，必须使用Service层的`saveEntity()`方法，严禁使用Mapper层的`delete()+insert()`组合操作。例如：使用`personnelService.saveEntity(personnel)`而不是`personnelMapper.delete(id); personnelMapper.insert(personnel)`。这确保了业务逻辑完整性、事务一致性，并触发相关的关联处理逻辑
- **Service层实体创建规范**：在Service层中创建新的实体对象时，必须使用Service层的`saveEntity()`方法，严禁直接使用Mapper层的`insert()`方法。例如：使用`saveEntity(newRecord)`而不是`mapper.insert(newRecord)`。这确保了创建逻辑的完整性，并触发可能存在的业务逻辑处理（如ID生成、关联数据处理等）
- **批量删除查询优化规范**：在Service层实现批量删除功能时，如果只需要获取实体ID进行删除操作，必须设置`search.onlyId = true`以优化查询性能。例如：在`deleteEntityListBySiteId`等批量删除方法中，创建查询条件后应添加`search.onlyId = true`，这样数据库只返回ID字段而不是全部字段，显著提升查询效率

## 技术实现规范

- **Kotlin版本语法限制**：本项目使用 Kotlin 1.2.41，严禁使用高版本语法！具体限制包括：
  - **严禁在when表达式中声明变量**：绝对禁止 `when(val data = result.datas)` 语法，这是高版本语法在1.2.41中不支持，必须先声明变量再使用when：
    ```kotlin
    // 错误写法（高版本语法，1.2.41不支持）
    when(val data = result.datas) { ... }
    
    // 正确写法（1.2.41兼容）
    val data = result.datas
    when(data) { ... }
    ```
  - **禁止尾随逗号**：数据类构造函数、函数参数列表和对象构造调用的最后一个参数后不能有尾随逗号
  - **严格版本兼容性**：任何代码编写前必须确认语法特性在Kotlin 1.2.41中可用，遇到编译错误时优先检查是否使用了高版本语法
  - 必须严格遵守 Kotlin 1.2.41 的所有语法规范
- **JSON解析规范**：默认使用Spring Boot自带的Jackson进行JSON解析，通过`ObjectMapper`处理JSON数据，禁止使用FastJSON等其他JSON库
- **HTTP客户端规范**：默认使用OkHttp作为HTTP客户端，通过`OkHttpClient`发送HTTP请求，禁止使用其他HTTP客户端库
- **定时任务规范**：Scheduler类应该只负责调度触发，所有业务逻辑必须写在Service层中。Scheduler通过依赖注入调用Service的方法，保持职责分离
- **多线程实现选择**：多线程实现使用Java线程而非Kotlin协程
- **查询方法实现**：查询方法`getList`应使用MyBatis动态SQL并传入`Search`对象，避免为每个条件创建单独方法

## 开发流程规范

- **思考与工具使用规范**：思考完毕后**禁止立即调用工具**，必须先生成一段文本说明接下来要执行的步骤和意图，然后再调用相应的工具。这样确保后续回答能够理解之前步骤的意图和上下文，避免失去思考过程的连续性
- **问题分析优先原则**：修改代码前必须彻底分析问题根本原因，严禁进行无意义的修改（如仅仅重命名变量、调整格式等表面操作）。必须基于异常堆栈、日志分析、代码逻辑审查等方式找到真正的问题根源，然后进行针对性修复
- **STAGES.md修改限制**：在未经用户明确确认前修改`STAGES.md`文件。**必须获得用户明确许可**后才能进行任何更改。
- **Git提交信息规范**：采用Conventional Commits风格，使用中文消息内容，一句话概括提交内容。格式为`<type>: <description>`，如：`feat: 添加用户登录功能`、`fix: 修复数据库连接问题`、`docs: 更新API文档`
- **Git提交描述限制**：Git提交时禁止使用详细子项描述，只需要一句话简洁概括即可。避免在提交信息中添加多行详细说明或子项列表

## 构造器依赖注入正确示例

```kotlin
@Service
class AaaService(
    mapper: AaaMapper,
    private val bbbMapper: BbbMapper
) : BaseService<Aaa, AaaMapper>(mapper)
```

</development-guideline-rule>

<mybatis-dynamic-sql-rule>

# Mybatis动态SQL

## 规范

- 如果字段为Kotlin非空字符串类型，不需要`test="!= null"`
- `<`符号需要转义为`&lt;`

## 查询示例

```kotlin
@Select("""
<script>
  <!-- 创建模糊查询模式 -->
  <bind name="pattern" value="'%' + keyword + '%'" />

  SELECT * FROM product
  <where>
    <!-- 多数据库支持 -->
    <if test="_databaseId == 'mysql'">/* MySQL优化提示 */</if>
    <if test="_databaseId == 'oracle'">/* Oracle优化提示 */</if>

    <!-- 条件选择 -->
    <choose>
      <when test="id != null">id = #{id}</when>
      <when test="keyword != null">name LIKE #{pattern}</when>
      <otherwise>is_featured = 1</otherwise>
    </choose>

    <!-- 基本if条件 -->
    <if test="categoryId != null">AND category_id = #{categoryId}</if>

    <!-- 集合处理 -->
    <if test="tagIds.size > 0">
      AND tag_id IN
      <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
        #{tagId}
      </foreach>
    </if>

    <!-- 自定义前缀/后缀处理 -->
    <trim prefix="AND (" suffix=")" prefixOverrides="AND|OR">
      <if test="minPrice != null">price >= #{minPrice}</if>
      <if test="maxPrice != null">AND price &lt;= #{maxPrice}</if>
    </trim>
  </where>
  ORDER BY ${orderBy == null ? 'id DESC' : orderBy}
</script>
""")
fun findProducts(search: ProductsSearch): List<Product>
```

## 更新示例

```kotlin
@Update("""
<script>
  UPDATE product
  <set>
    <if test="name != null">name = #{name},</if>
    <if test="price != null">price = #{price},</if>
    <if test="description != null">description = #{description},</if>
    <if test="stock != null">stock = #{stock},</if>
    <if test="categoryId != null">category_id = #{categoryId},</if>
    <if test="status != null">status = #{status}</if>
  </set>
  WHERE id = #{id}
</script>
""")
fun updateProduct(product: Product): Int
```

## 插入示例

```kotlin
@Insert("""
<script>
  INSERT INTO product (name, price, category_id, created_at)
  VALUES (#{name}, #{price}, #{categoryId}, NOW())

  <selectKey keyProperty="id" resultType="long" order="AFTER">
    SELECT LAST_INSERT_ID()
  </selectKey>

  <!-- 根据数据库类型选择不同的批量插入语法 -->
  <if test="tags.size > 0">
    <choose>
      <when test="_databaseId == 'mysql'">
        <!-- MySQL批量插入 -->
        INSERT INTO product_tag (product_id, tag_id) VALUES
        <foreach collection="tags" item="tag" separator=",">
          (#{id}, #{tag.id})
        </foreach>
      </when>
      <when test="_databaseId == 'oracle'">
        <!-- Oracle批量插入 -->
        <foreach collection="tags" item="tag" separator=" UNION ALL ">
          SELECT #{id}, #{tag.id} FROM dual
        </foreach>
      </when>
    </choose>
  </if>
</script>
""")
fun insertProductWithTags(product: ProductDTO): Int
```

</mybatis-dynamic-sql-rule>

<meeting-module-rule>

# 会议模块

## 模块概述

会议模块后端服务，实现会议预约创建、参会人员管理、会议通知等核心功能。该模块需提供完整的API支持前端实现会议预约管理流程。

## 详细功能规格

### 1. 会议管理

#### 会议预约创建

- 存储会议基本信息：主题、内容、预定时间、发起人信息
- 支持附件上传及存储（无格式和大小限制）
- 记录会议状态（待开始、进行中、已取消、已结束）

#### 会议服务管理

- 支持配置以下会议服务：
    - **用餐**：人数、用餐时间
    - **桌牌**：数量、桌牌内容
    - **纸笔**：数量
    - **茶水**：数量
    - **果盘**：数量

#### 会议提醒配置

- 通知方式选择：短信通知、语音电话

### 2. 会议室管理

#### 会议室信息维护

- 存储会议室属性：名称、可用设备（投影仪、麦克风、白板、笔记本）、人员容量
- 支持通过楼层进行筛选查询

#### 会议室预约时间冲突检测

- 同一时间段会议室只能被一个会议预约
- 提供API检查指定时间段会议室是否可用
- 支持查询会议室预约情况

### 3. 参会人员管理

#### 人员类型支持

- 分为内部成员和外部成员
- 需要信息：姓名、手机号码、单位、职务

#### 参会人员选择与展示

- 根据所属单位分组展示可选人员
- 设置特殊分组"外部成员"统一展示所有外部成员

#### 参会反馈处理

- 记录参会人员反馈：参加、建议延迟（原因）、不参加（原因）
- 提供反馈统计查询接口

### 4. 通知服务

- 实现参会邀请通知发送功能
- 根据配置的提醒时间发送会议提醒
- 支持短信和语音电话两种通知方式

### 5. 查询服务

- 提供按多种条件查询会议信息的API
- 支持会议室查询和搜索功能
- 提供会议状态查询接口

## 核心数据实体及关系

```mermaid
erDiagram
    "Meeting(会议)" ||--o{ "Meetingfacility(会议服务)" : "提供服务"
    "Meeting(会议)" ||--o{ "Meetingnotification(会议通知)" : "发送通知"
    "Meeting(会议)" }o--|| "Meetingroom(会议室)" : "使用会议室"
    "Meeting(会议)" ||--o{ "Content(附件)" : "上传附件"
    "Meeting(会议)" }o--o{ "Personnel(人员)" : "包含参会人员"
    "Personnel(人员)" ||--o{ "Meetingnotification" : "接收通知"
```

</meeting-module-rule>

<personnel-module-rule>

# 人事模块

## 模块概述

人事管理模块后端服务，实现身份认证、权限管理、组织架构管理等核心功能。该模块为系统提供统一的用户身份验证和权限控制基础，确保系统安全性和功能访问控制。

## 详细功能规格

### 身份认证管理

#### 多方式登录支持

- **微信手机号一键登录**：集成微信授权，获取用户手机号进行快速登录
- **手机号验证码登录**：发送短信验证码，通过手机号+验证码方式登录
- **账号密码登录**：传统的用户名密码登录方式

#### 用户准入控制

- 系统不支持用户自主注册
- 只有管理员预先添加的人员才具备登录资格
- 提供用户登录状态验证和会话管理

### 人员信息管理

#### 人员档案维护

- 存储人员基本信息：姓名、手机号、账号、密码、职务名称等
- 支持人员在不同部门间调动

#### 组织关系管理

- 每个人员必须归属于一个部门
- 维护人员与部门的关联关系

#### 工作组关系管理

- 支持人员加入多个工作组
- 提供工作组成员的增删改查功能
- 支持按工作组查询人员列表

### 权限管理体系

#### 权限类型定义

系统支持以下权限类型：

- 职工权限
- 理发管理
- 菜谱管理
- 外卖管理
- 报修管理
- 班车发车
- 人事记录
- 配置管理
- 巡检管理
- 物业巡检
- 白班巡检
- 夜班巡检
- 保洁清洁

#### 权限分配机制

- **个人权限**：直接分配给人员的权限
- **工作组权限**：分配给工作组的权限，组内成员共享
- **权限合并规则**：用户最终权限 = 个人权限 ∪ 所属工作组权限

#### 权限验证服务

- 提供权限校验API，验证用户是否具备特定功能权限
- 提供用户权限清单查询接口

### 组织架构管理

#### 部门管理

- 支持部门信息的增删改查
- 当前为平级部门结构，预留层级扩展能力
- 提供部门人员统计功能

#### 工作组管理

- 支持工作组的创建、修改、删除
- 管理工作组成员关系
- 支持工作组权限批量分配

#### 职务管理

- 维护系统职务字典，为人员职务录入提供选项参考
- 支持职务信息的增删改查

### 查询与统计服务

#### 人员查询服务

- 支持按部门、工作组、职务等维度查询人员
- 提供人员模糊搜索功能（姓名、手机号）

#### 权限统计分析

- 提供各权限的人员分布统计
- 支持工作组权限覆盖情况查询
- 提供权限使用情况分析报告

## 核心数据实体及关系

```mermaid
erDiagram
    "Personnel(人员)" }o--|| "Organization(部门)" : "归属部门"
    "Personnel(人员)" }o--o{ "Workgroup(工作组)" : "参与工作组"
    "Permission(权限)" }o--o{ "Personnel(人员)" : "个人权限"
    "Permission(权限)" }o--o{ "Workgroup(工作组)" : "工作组权限"
    "Position(职务)"
```

</personnel-module-rule>
