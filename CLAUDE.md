<project-rule>

# 项目总体

## 项目简介

懃务通是一款智慧后勤小程序，集成便捷登录、物业报修、巡检记录、会议预约、派车管理等核心功能，覆盖后勤管理全场景；支持菜谱管理、面食预订、理发预约、通勤班车、挪车等生活服务线上化，实现固定资产、日用品智能管控，同步搭载安全管理、新闻公告、配置管理、人事管理等基础模块；以数字化推动后勤管理中心信息与智能一体化，通过一站式服务体系简化职工办事流程，提升后勤服务效率与体验，助力构建现代化后勤管理新模式。

## 技术栈要求

| 类别     | 技术选型              | 版本   |
| -------- | --------------------- | ------ |
| 编程语言 | Kotlin                | 1.2.41 |
| 构建工具 | Gradle                | 4.10.1 |
| 框架     | Spring Boot           | 2.0.4  |
| 安全框架 | Spring Security       | 2.0.4  |
| 数据库   | MariaDB               | 10.5.8 |
| ORM      | MyBatis (注解动态SQL) | 3.5.1  |

## 项目架构

```markdown
- `src/main/kotlin/com/shenlan/smartlogixmini/`
  - `auto/` - 自动生成的业务模块代码
    - `Base.kt` - 基础类(BaseModel, BaseMapper, BaseService等)和Result响应类
    - `[各业务模块].kt` - 每个文件包含模型类、Mapper接口、Service类、Controller类
- `sql/` - SQL脚本目录
    - `database.sql` - 数据库初始化脚本，包含建表SQL
- `webapp/` - 前端根目录
- `webapp-vue/` - 前端根目录
- `www/` - 前端打包目录
```

</project-rule>

<meeting-module-rule>

# 会议模块

## 模块概述

会议模块后端服务，实现会议预约创建、参会人员管理、会议通知等核心功能。该模块需提供完整的API支持前端实现会议预约管理流程。

## 详细功能规格

### 1. 会议管理

#### 会议预约创建

- 存储会议基本信息：主题、内容、预定时间、发起人信息
- 支持附件上传及存储（无格式和大小限制）
- 记录会议状态（待开始、进行中、已取消、已结束）

#### 会议服务管理

- 支持配置以下会议服务：
    - **用餐**：人数、用餐时间
    - **桌牌**：数量、桌牌内容
    - **纸笔**：数量
    - **茶水**：数量
    - **果盘**：数量

#### 会议提醒配置

- 通知方式选择：短信通知、语音电话

### 2. 会议室管理

#### 会议室信息维护

- 存储会议室属性：名称、可用设备（投影仪、麦克风、白板、笔记本）、人员容量
- 支持通过楼层进行筛选查询

#### 会议室预约时间冲突检测

- 同一时间段会议室只能被一个会议预约
- 提供API检查指定时间段会议室是否可用
- 支持查询会议室预约情况

### 3. 参会人员管理

#### 人员类型支持

- 分为内部成员和外部成员
- 需要信息：姓名、手机号码、单位、职务

#### 参会人员选择与展示

- 根据所属单位分组展示可选人员
- 设置特殊分组"外部成员"统一展示所有外部成员

#### 参会反馈处理

- 记录参会人员反馈：参加、建议延迟（原因）、不参加（原因）
- 提供反馈统计查询接口

### 4. 通知服务

- 实现参会邀请通知发送功能
- 根据配置的提醒时间发送会议提醒
- 支持短信和语音电话两种通知方式

### 5. 查询服务

- 提供按多种条件查询会议信息的API
- 支持会议室查询和搜索功能
- 提供会议状态查询接口

## 核心数据实体及关系

```mermaid
erDiagram
    "Meeting(会议)" ||--o{ "Meetingfacility(会议服务)" : "提供服务"
    "Meeting(会议)" ||--o{ "Meetingnotification(会议通知)" : "发送通知"
    "Meeting(会议)" }o--|| "Meetingroom(会议室)" : "使用会议室"
    "Meeting(会议)" ||--o{ "Content(附件)" : "上传附件"
    "Meeting(会议)" }o--o{ "Personnel(人员)" : "包含参会人员"
    "Personnel(人员)" ||--o{ "Meetingnotification" : "接收通知"
```

</meeting-module-rule>

<personnel-module-rule>

# 人事模块

## 模块概述

人事管理模块后端服务，实现身份认证、权限管理、组织架构管理等核心功能。该模块为系统提供统一的用户身份验证和权限控制基础，确保系统安全性和功能访问控制。

## 详细功能规格

### 身份认证管理

#### 多方式登录支持

- **微信手机号一键登录**：集成微信授权，获取用户手机号进行快速登录
- **手机号验证码登录**：发送短信验证码，通过手机号+验证码方式登录
- **账号密码登录**：传统的用户名密码登录方式

#### 用户准入控制

- 系统不支持用户自主注册
- 只有管理员预先添加的人员才具备登录资格
- 提供用户登录状态验证和会话管理

### 人员信息管理

#### 人员档案维护

- 存储人员基本信息：姓名、手机号、账号、密码、职务名称等
- 支持人员在不同部门间调动

#### 组织关系管理

- 每个人员必须归属于一个部门
- 维护人员与部门的关联关系

#### 工作组关系管理

- 支持人员加入多个工作组
- 提供工作组成员的增删改查功能
- 支持按工作组查询人员列表

### 权限管理体系

#### 权限类型定义

系统支持以下权限类型：

- 职工权限
- 理发管理
- 菜谱管理
- 外卖管理
- 报修管理
- 班车发车
- 人事记录
- 配置管理
- 巡检管理
- 物业巡检
- 白班巡检
- 晚班巡检
- 保洁清洁

#### 权限分配机制

- **个人权限**：直接分配给人员的权限
- **工作组权限**：分配给工作组的权限，组内成员共享
- **权限合并规则**：用户最终权限 = 个人权限 ∪ 所属工作组权限

#### 权限验证服务

- 提供权限校验API，验证用户是否具备特定功能权限
- 提供用户权限清单查询接口

### 组织架构管理

#### 部门管理

- 支持部门信息的增删改查
- 当前为平级部门结构，预留层级扩展能力
- 提供部门人员统计功能

#### 工作组管理

- 支持工作组的创建、修改、删除
- 管理工作组成员关系
- 支持工作组权限批量分配

#### 职务管理

- 维护系统职务字典，为人员职务录入提供选项参考
- 支持职务信息的增删改查

### 查询与统计服务

#### 人员查询服务

- 支持按部门、工作组、职务等维度查询人员
- 提供人员模糊搜索功能（姓名、手机号）

#### 权限统计分析

- 提供各权限的人员分布统计
- 支持工作组权限覆盖情况查询
- 提供权限使用情况分析报告

## 核心数据实体及关系

```mermaid
erDiagram
    "Personnel(人员)" }o--|| "Organization(部门)" : "归属部门"
    "Personnel(人员)" }o--o{ "Workgroup(工作组)" : "参与工作组"
    "Permission(权限)" }o--o{ "Personnel(人员)" : "个人权限"
    "Permission(权限)" }o--o{ "Workgroup(工作组)" : "工作组权限"
    "Position(职务)"
```

</personnel-module-rule>

<mybatis-dynamic-sql-rule>

# Mybatis动态SQL

## 规范

- 如果字段为Kotlin非空字符串类型，不需要`test="!= null"`

## 查询示例

```kotlin
@Select("""
<script>
  <!-- 创建模糊查询模式 -->
  <bind name="pattern" value="'%' + keyword + '%'" />

  SELECT * FROM product
  <where>
    <!-- 多数据库支持 -->
    <if test="_databaseId == 'mysql'">/* MySQL优化提示 */</if>
    <if test="_databaseId == 'oracle'">/* Oracle优化提示 */</if>

    <!-- 条件选择 -->
    <choose>
      <when test="id != null">id = #{id}</when>
      <when test="keyword != null">name LIKE #{pattern}</when>
      <otherwise>is_featured = 1</otherwise>
    </choose>

    <!-- 基本if条件 -->
    <if test="categoryId != null">AND category_id = #{categoryId}</if>

    <!-- 集合处理 -->
    <if test="tagIds.size > 0">
      AND tag_id IN
      <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
        #{tagId}
      </foreach>
    </if>

    <!-- 自定义前缀/后缀处理 -->
    <trim prefix="AND (" suffix=")" prefixOverrides="AND|OR">
      <if test="minPrice != null">price >= #{minPrice}</if>
      <if test="maxPrice != null">AND price <= #{maxPrice}</if>
    </trim>
  </where>
  ORDER BY ${orderBy == null ? 'id DESC' : orderBy}
</script>
""")
fun findProducts(search: ProductsSearch): List<Product>
```

## 更新示例

```kotlin
@Update("""
<script>
  UPDATE product
  <set>
    <if test="name != null">name = #{name},</if>
    <if test="price != null">price = #{price},</if>
    <if test="description != null">description = #{description},</if>
    <if test="stock != null">stock = #{stock},</if>
    <if test="categoryId != null">category_id = #{categoryId},</if>
    <if test="status != null">status = #{status}</if>
  </set>
  WHERE id = #{id}
</script>
""")
fun updateProduct(product: Product): Int
```

## 插入示例

```kotlin
@Insert("""
<script>
  INSERT INTO product (name, price, category_id, created_at)
  VALUES (#{name}, #{price}, #{categoryId}, NOW())

  <selectKey keyProperty="id" resultType="long" order="AFTER">
    SELECT LAST_INSERT_ID()
  </selectKey>

  <!-- 根据数据库类型选择不同的批量插入语法 -->
  <if test="tags.size > 0">
    <choose>
      <when test="_databaseId == 'mysql'">
        <!-- MySQL批量插入 -->
        INSERT INTO product_tag (product_id, tag_id) VALUES
        <foreach collection="tags" item="tag" separator=",">
          (#{id}, #{tag.id})
        </foreach>
      </when>
      <when test="_databaseId == 'oracle'">
        <!-- Oracle批量插入 -->
        <foreach collection="tags" item="tag" separator=" UNION ALL ">
          SELECT #{id}, #{tag.id} FROM dual
        </foreach>
      </when>
    </choose>
  </if>
</script>
""")
fun insertProductWithTags(product: ProductDTO): Int
```

</mybatis-dynamic-sql-rule>

<development-guideline-rule>

# 开发准则

## 代码风格规范

- **完全限定名称禁用**：在代码中必须使用`import`导入所有类型，如`import com.shenlan.smartlogixmini.mybatis.PaginationInfo`后直接使用`PaginationInfo`，**严禁**使用`com.shenlan.smartlogixmini.mybatis.PaginationInfo`形式。这个规则无例外，必须始终遵守！
- **日志语言规范**：使用英文编写日志文本，如`log.info("English message")`，以避免日志文件出现编码问题
- **注释语言规范**：代码注释应使用中文，提高中文阅读者的理解速度
- **属性分组规范**：将类属性定义分组，组内属性之间不加空行，组与组之间用一个空行隔开，并在每组开头添加明确的分组注释
- **方法逻辑分组**：方法体内部必须按逻辑分组，每组前加注释，组间空行，提升可读性和维护性
- **实体类属性分组**：实体类属性应分为三组：`数据库表字段`、`关联字段`和`其他字段`，各组间用空行分隔
- **类型转换注解**：转换对象为特定类型列表时（如`as List<XXX>`），添加`@Suppress("UNCHECKED_CAST")`注解避免编译警告
- **日志使用规范**：日志打印应通过`import com.shenlan.smartlogixmini.util.log`，在类内使用`log.info("Log message")`等方法，而类外使用`"".log.info("Log message")`，因为`log`是扩展属性。
- **前端信息语言**：向前端返回结果时使用中文信息，如`Result.getSuccessInfo("中文信息")`、`Result.getError("中文信息")`
- **异常日志记录**：使用全局函数`fun errorlog(e: Throwable)`打印异常堆栈信息，确保异常被完整记录
- **字符串模板规范**：Kotlin字符串模板中，单独变量后接非标识符字符时省略花括号（如`"Hello $name"`），但属性访问（如`${user.name}`）、表达式（如`${a+b}`）或存在歧义（如`${name}123`）必须保留花括号
- **时区处理规范**：在Java/Kotlin中将`Date`转换为`Instant`进行时间处理时，需注意`toInstant()`方法默认使用UTC时区，在中国（UTC+8）环境下会导致时间偏差8小时；应使用`date.toInstant().atZone(ZoneId.systemDefault()).toInstant()`确保正确应用本地时区
- **标准库优先原则**：优先使用Kotlin标准库方法：在处理集合操作、文件遍历、字符串处理等常见任务时，应首先考虑使用Kotlin标准库提供的扩展函数（如`walkTopDown()`、`filter()`、`map()`、`forEach()`等）和高阶函数，而非手动实现循环或递归，以提高代码简洁性、可读性和性能。
- **尾随逗号禁用**：在`Kotlin 1.2.41`项目中，数据类构造函数、函数参数列表和对象构造调用的最后一个参数后不能有尾随逗号。务必检查并移除所有尾随逗号以保持兼容性。
- **单行文档注释**：单行内容的文档注释使用 `/** 文档注释 */` 格式，不要使用多行格式
- **多行文档注释**：多行内容的文档注释使用标准的 `/** ... */` 多行格式
- **HTTP映射注解使用**：方法级别优先使用具体的HTTP方法注解（`@GetMapping`、`@PostMapping`）而不是通用的`@RequestMapping`

## 命名规范

- **模型类命名**：数据库表对应的模型类命名采用首字母大写其余小写形式，如`Userinfo`、`Loginlog`
- **API路径命名**：API路径中的实体类名应与实体类类名保持一致，首字母大写
- **数据库命名规范**：数据库表名使用全小写无分隔符形式，仅添加`tbl_`前缀，而字段名则使用驼峰命名法
- **方法命名规范**：假设实体名为`Aaa`，其相关类（`AaaMapper`、`AaaService`、`AaaResource`）的方法应命名为`getListByBbb`，而非`getAaaByBbb`
- **接口命名规范**：Kotlin接口命名不使用`I`前缀，应直接使用`interface Aaa`而非`interface IAaa`
- **集合字段命名**：集合类型字段命名应使用对应的类型后缀，如：List字段使用`List`后缀（如`userList`）、Set字段使用`Set`后缀（如`userSet`）、Map字段使用`Map`后缀（如`userMap`），以提高代码一致性和可读性

## 方法参数规范

- **BaseService参数名**：`BaseService`子类重写方法时，参数名应使用`page`而非`search`，如：`override fun getList(page: BaseSearch): Result`

## 数据库规范

- **表名规范**：表名除`tbl_`前缀外应全部小写且不含分隔符
- **字段默认值**：字符串类型的表字段与类属性默认应为非空，默认值设为空字符串
- **计数查询规范**：计数查询使用`COUNT(*)`而非`COUNT(1)`
- **事务管理**：数据库事务管理通过`@Transactional`注解实现
- **自动更新时间**：MariaDB已设置`ON UPDATE CURRENT_TIMESTAMP`，因此Mybatis SQL不应使用`sysUpdated = NOW()`
- **动态SQL标签**：仅在使用动态SQL的Mybatis查询中添加`<script>`标签，静态SQL无需添加
- **删除策略**：默认采用物理删除策略而非逻辑删除
- **外键字段顺序**：数据库建表SQL中的外键字段应紧跟在`id`字段之后定义，保持字段顺序的一致性和可读性
- **where标签使用**：MyBatis动态SQL中使用`<where>`标签时，无需添加`WHERE 1=1`条件，`<where>`标签会自动处理条件拼接和`WHERE`关键字的添加
- **Flyway迁移文件保护**：严禁修改`sql/flyway/migration/`目录下的已有迁移文件（V*__*.sql），这些文件代表历史数据库变更记录。如需删除字段或修改表结构，应该：1）修改`sql/database.sql`中的当前表结构；2）修改对应的实体类；3）如需在生产环境执行变更，应创建新的迁移文件。绝不能修改已有的迁移文件！

## 异常处理规范

- **全局异常处理**：`@RestController`/`@Controller`/`@Service`类的方法有全局异常处理器，应打印必要日志后直接抛出异常，避免手动返回错误结果和重复打印堆栈信息

## 架构与继承规范

- **基类方法重写**：`BaseMapper`/`BaseService`/`BaseResource`已定义基础方法，子类应重写这些方法而非创建新方法
- **构造函数优先级**：优先使用主构造函数，避免次要构造函数
- **继承调用规范**：Kotlin继承类时，通过主构造函数调用父类构造函数，如`class Meetingroom : BaseModel()`
- **关联对象加载层级**：获取数据模型关联对象的操作必须在Service层完成而非Mapper层，因为Mapper层无法正确加载嵌套的关联对象（关联对象的关联对象）
- **Service层调用规范**：Service层补充关联对象数据时，必须调用其他实体的Service层方法，不应直接使用Mapper层，确保关联对象的完整性和一致性
- **关联查询方法调用**：Service层的`getInfo`和`getList`方法查询关联对象时，必须使用其他实体Service层的`getInfo`和`getList`方法，而非Mapper层的同名方法，以确保关联数据的完整加载
- **依赖注入方式**：Spring依赖注入优先使用构造器注入方式
- **BaseMapper方法限制**：注意`BaseMapper`接口中不存在`update`方法，请使用正确的替代方法
- **批量插入操作**：需要批量插入数据时，应使用`BaseMapper`接口提供的`insertList`方法，而非自定义批量插入方法。该方法接受`List<BaseModel>`参数，可高效完成批量数据插入操作
- **分层职责规范**：Controller层负责格式校验、基础约束校验和Result包装，Service层负责业务逻辑校验
- **Service方法返回值**：BaseService子类方法的返回值不要为Result，而是在BaseResource子类方法中包装Result
- **层次调用优化**：如果Service层只是单纯调Mapper层方法而无额外业务逻辑，就没必要单独写Service层方法，而是在Controller层直接调Mapper层方法

## 技术实现规范

- **Kotlin版本语法限制**：本项目使用 Kotlin 1.2.41，严禁使用高版本语法！具体限制包括：
  - **禁止在when中声明变量**：严禁 `when(val data = result.datas)` 语法，必须先声明：`val data = result.datas; when(data) {...}`
  - **禁止尾随逗号**：数据类构造函数、函数参数列表和对象构造调用的最后一个参数后不能有尾随逗号
  - 必须严格遵守 Kotlin 1.2.41 的所有语法规范
- **多线程实现选择**：多线程实现使用Java线程而非Kotlin协程
- **查询方法实现**：查询方法`getList`应使用MyBatis动态SQL并传入`Search`对象，避免为每个条件创建单独方法

## 开发流程规范

- **STAGES.md修改限制**：在未经用户明确确认前修改`STAGES.md`文件。**必须获得用户明确许可**后才能进行任何更改。
- **Git提交信息规范**：采用Conventional Commits风格，使用中文消息内容，一句话概括提交内容。格式为`<type>: <description>`，如：`feat: 添加用户登录功能`、`fix: 修复数据库连接问题`、`docs: 更新API文档`
- **Git提交描述限制**：Git提交时禁止使用详细子项描述，只需要一句话简洁概括即可。避免在提交信息中添加多行详细说明或子项列表

## 构造器依赖注入正确示例

```kotlin
@Service
class AaaService(
    mapper: AaaMapper,
    private val bbbMapper: BbbMapper
) : BaseService<Aaa, AaaMapper>(mapper)
```

</development-guideline-rule>
