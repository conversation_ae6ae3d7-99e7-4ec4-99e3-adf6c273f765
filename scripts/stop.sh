#!/bin/bash
echo "Stopping @projectName@ application on port @serverPort@..."

# 查找占用指定端口的进程
PID=$(lsof -ti:@serverPort@)

if [ -z "$PID" ]; then
    echo "No process found listening on port @serverPort@"
    exit 0
fi

echo "Found process with PID: $PID"

# 尝试优雅停止
kill $PID
sleep 3

# 检查进程是否还在运行
if kill -0 $PID 2>/dev/null; then
    echo "Process still running, force killing..."
    kill -9 $PID
    sleep 1
    
    # 再次检查
    if kill -0 $PID 2>/dev/null; then
        echo "Failed to stop process with PID: $PID"
        exit 1
    else
        echo "Successfully force stopped @projectName@ application."
    fi
else
    echo "Successfully stopped @projectName@ application."
fi

echo "Port @serverPort@ is now free." 