#!/bin/bash

# ===============================================
# 智慧后勤小程序 - 自动化部署脚本 (Linux/Unix)
# ===============================================

# 设置严格模式
set -eu

# 项目配置
PROJECT_NAME="@projectName@"
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
BACKUP_DIR="${SCRIPT_DIR}/backup"

# 获取当前时间戳（格式：YYYY-MM-DD_HH-MM-SS）
TIMESTAMP=$(date +"%Y-%m-%d_%H-%M-%S")

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检测是否支持颜色输出
if [ -t 1 ] && command -v tput > /dev/null 2>&1 && tput colors > /dev/null 2>&1; then
    USE_COLOR=1
else
    USE_COLOR=0
fi

# 日志函数
log_info() {
    if [ "$USE_COLOR" = "1" ]; then
        printf "${BLUE}[INFO]${NC} %s\n" "$1"
    else
        printf "[INFO] %s\n" "$1"
    fi
}

log_warn() {
    if [ "$USE_COLOR" = "1" ]; then
        printf "${YELLOW}[WARN]${NC} %s\n" "$1"
    else
        printf "[WARN] %s\n" "$1"
    fi
}

log_error() {
    if [ "$USE_COLOR" = "1" ]; then
        printf "${RED}[ERROR]${NC} %s\n" "$1"
    else
        printf "[ERROR] %s\n" "$1"
    fi
}

log_success() {
    if [ "$USE_COLOR" = "1" ]; then
        printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
    else
        printf "[SUCCESS] %s\n" "$1"
    fi
}

# 错误处理函数
handle_error() {
    log_error "Deployment failed!"
    echo
    echo "==============================================="
    log_error "Error occurred during deployment process, please check above information"
    echo "==============================================="
    exit 1
}

echo
echo "==============================================="
log_info "SmartLogix Mini - Deployment Started"
echo "==============================================="
log_info "Project Name: ${PROJECT_NAME}"
log_info "Script Directory: ${SCRIPT_DIR}"
log_info "Timestamp: ${TIMESTAMP}"
echo "==============================================="

# 1. 检查新 war 文件是否存在
echo
log_info "[Step 1/5] Checking new version file..."
if [ ! -f "${SCRIPT_DIR}/${PROJECT_NAME}_new.war" ]; then
    log_error "New version file ${PROJECT_NAME}_new.war not found"
    log_error "Please place the new version file as ${PROJECT_NAME}_new.war in the script directory"
    exit 1
fi
log_success "New version file check passed"

# 2. 执行停止脚本
echo
log_info "[Step 2/5] Stopping application..."
if [ -f "${SCRIPT_DIR}/stop.sh" ]; then
    if sh "${SCRIPT_DIR}/stop.sh"; then
        log_success "Application stopped successfully"
    else
        log_warn "Stop script execution failed, but continuing deployment process"
    fi
else
    log_warn "Stop script stop.sh not found, skipping stop step"
fi

# 3. 创建备份目录和备份文件
echo
log_info "[Step 3/5] Creating backup..."
if [ ! -d "${BACKUP_DIR}" ]; then
    mkdir -p "${BACKUP_DIR}"
    log_info "Created backup directory: ${BACKUP_DIR}"
fi

# 备份当前 war 文件
if [ -f "${SCRIPT_DIR}/${PROJECT_NAME}.war" ]; then
    BACKUP_FILE="${BACKUP_DIR}/${PROJECT_NAME}_${TIMESTAMP}.war"
    if cp "${SCRIPT_DIR}/${PROJECT_NAME}.war" "${BACKUP_FILE}"; then
        log_success "Backup successful: ${BACKUP_FILE}"
    else
        log_error "Backup failed"
        handle_error
    fi
else
    log_warn "Current war file not found, skipping backup"
fi

# 4. 替换 war 文件
echo
log_info "[Step 4/5] Replacing application file..."
if [ -f "${SCRIPT_DIR}/${PROJECT_NAME}.war" ]; then
    if rm "${SCRIPT_DIR}/${PROJECT_NAME}.war"; then
        log_info "Old version file deleted successfully"
    else
        log_error "Failed to delete old version file"
        handle_error
    fi
fi

if mv "${SCRIPT_DIR}/${PROJECT_NAME}_new.war" "${SCRIPT_DIR}/${PROJECT_NAME}.war"; then
    log_success "File replacement successful"
else
    log_error "File replacement failed"
    handle_error
fi

# 5. 启动应用程序
echo
log_info "[Step 5/5] Starting application..."
if [ -f "${SCRIPT_DIR}/start.sh" ]; then
    if sh "${SCRIPT_DIR}/start.sh"; then
        log_success "Application started successfully"
    else
        log_error "Start script execution failed"
        handle_error
    fi
else
    log_warn "Start script start.sh not found, please start application manually"
fi

echo
echo "==============================================="
log_success "Deployment Completed Successfully!"
echo "==============================================="
log_info "Backup Location: ${BACKUP_DIR}"
log_info "Deployment Time: ${TIMESTAMP}"
echo "==============================================="
echo 