@echo off
setlocal enabledelayedexpansion

REM ===============================================
REM 智慧后勤小程序 - 自动化部署脚本 (Windows)
REM ===============================================

set PROJECT_NAME=@projectName@
set SCRIPT_DIR=%~dp0
set BACKUP_DIR=%SCRIPT_DIR%backup

REM 获取当前时间戳（格式：YYYY-MM-DD_HH-MM-SS）
for /f "tokens=2 delims==" %%i in ('wmic OS Get localdatetime /value') do set datetime=%%i
set YEAR=%datetime:~0,4%
set MONTH=%datetime:~4,2%
set DAY=%datetime:~6,2%
set HOUR=%datetime:~8,2%
set MINUTE=%datetime:~10,2%
set SECOND=%datetime:~12,2%
set TIMESTAMP=%YEAR%-%MONTH%-%DAY%_%HOUR%-%MINUTE%-%SECOND%

echo.
echo ===============================================
echo SmartLogix Mini - Deployment Started
echo ===============================================
echo Project Name: %PROJECT_NAME%
echo Script Directory: %SCRIPT_DIR%
echo Timestamp: %TIMESTAMP%
echo ===============================================

REM 1. 检查新 war 文件是否存在
echo.
echo [Step 1/5] Checking new version file...
if not exist "%SCRIPT_DIR%%PROJECT_NAME%_new.war" (
    echo Error: New version file %PROJECT_NAME%_new.war not found
    goto :error_exit
)
echo New version file check passed

REM 2. 执行停止脚本
echo.
echo [Step 2/5] Stopping application...
if exist "%SCRIPT_DIR%stop.bat" (
    call "%SCRIPT_DIR%stop.bat"
    if !errorlevel! neq 0 (
        echo Warning: Stop script execution failed, but continuing deployment process
    ) else (
        echo Application stopped successfully
    )
) else (
    echo Warning: Stop script stop.bat not found, skipping stop step
)

REM 3. 创建备份目录
echo.
echo [Step 3/5] Creating backup...
if not exist "%BACKUP_DIR%" (
    mkdir "%BACKUP_DIR%"
    echo Created backup directory: %BACKUP_DIR%
)

REM 备份当前 war 文件
if exist "%SCRIPT_DIR%%PROJECT_NAME%.war" (
    set BACKUP_FILE=%BACKUP_DIR%\%PROJECT_NAME%_%TIMESTAMP%.war
    copy "%SCRIPT_DIR%%PROJECT_NAME%.war" "!BACKUP_FILE!" >nul
    if !errorlevel! equ 0 (
        echo Backup successful: !BACKUP_FILE!
    ) else (
        echo Error: Backup failed
        goto :error_exit
    )
) else (
    echo Warning: Current war file not found, skipping backup
)

REM 4. 替换 war 文件
echo.
echo [Step 4/5] Replacing application file...
if exist "%SCRIPT_DIR%%PROJECT_NAME%.war" (
    del "%SCRIPT_DIR%%PROJECT_NAME%.war" /q
    if !errorlevel! neq 0 (
        echo Error: Failed to delete old version file
        goto :error_exit
    )
)

move "%SCRIPT_DIR%%PROJECT_NAME%_new.war" "%SCRIPT_DIR%%PROJECT_NAME%.war" >nul
if !errorlevel! equ 0 (
    echo File replacement successful
) else (
    echo Error: File replacement failed
    goto :error_exit
)

REM 5. 启动应用程序
echo.
echo [Step 5/5] Starting application...
if exist "%SCRIPT_DIR%start.bat" (
    call "%SCRIPT_DIR%start.bat"
    if !errorlevel! neq 0 (
        echo Error: Start script execution failed
        goto :error_exit
    ) else (
        echo Application started successfully
    )
) else (
    echo Warning: Start script start.bat not found, please start application manually
)

echo.
echo ===============================================
echo Deployment Completed Successfully!
echo ===============================================
echo Backup Location: %BACKUP_DIR%
echo Deployment Time: %TIMESTAMP%
echo ===============================================
goto :end

:error_exit
echo.
echo ===============================================
echo Deployment Failed!
echo ===============================================
echo Please check error messages above and retry
echo ===============================================
exit /b 1

:end
endlocal
echo.
pause 