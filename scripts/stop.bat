@echo off
setlocal enabledelayedexpansion
echo Stopping @projectName@ application on port @serverPort@...

REM 查找占用指定端口的进程
set "target_pid="
for /f "tokens=5" %%a in ('netstat -aon ^| find ":@serverPort@" ^| find "LISTENING"') do (
    if "!target_pid!"=="" (
        set "target_pid=%%a"
        echo Found process with PID: %%a
    )
)

REM 如果没有找到进程
if "%target_pid%"=="" (
    echo No process found listening on port @serverPort@
    goto :end
)

REM 直接强制关闭进程
echo Force-stopping process with PID: %target_pid%
taskkill /f /pid %target_pid% >nul 2>&1
if errorlevel 0 (
    echo Successfully force-stopped process with PID: %target_pid%
) else (
    echo Failed to force-stop process with PID: %target_pid%
)

REM 等待进程完全释放资源
timeout /t 2 /nobreak >nul

:success
REM 最终检查端口状态
echo Checking if port @serverPort@ is free...
netstat -aon | find ":@serverPort@" | find "LISTENING" >nul 2>&1
if errorlevel 1 (
    echo Port @serverPort@ is now free.
    echo Application stopped successfully.
) else (
    echo Warning: Port @serverPort@ is still in use.
    echo Showing remaining processes:
    netstat -aon | find ":@serverPort@" | find "LISTENING"
)

:end
echo Done.
exit /b 0 