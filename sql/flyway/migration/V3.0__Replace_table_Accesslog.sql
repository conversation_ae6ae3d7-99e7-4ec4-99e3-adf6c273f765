drop table tbl_accesslog;

CREATE TABLE IF NOT EXISTS tbl_accesslog
(
    id            VARCHAR(36) PRIMARY KEY,
    userId        VARCHAR(36)  NOT NULL DEFAULT '',
    userName      VARCHAR(100) NOT NULL DEFAULT '',
    orgName       VARCHAR(100) NOT NULL DEFAULT '',
    className     VARCHAR(255) NOT NULL DEFAULT '',
    methodName    VARCHAR(100) NOT NULL DEFAULT '',
    executeTime   INT          NOT NULL DEFAULT 0,
    parameters    TEXT         NOT NULL DEFAULT '',
    result        TEXT         NOT NULL DEFAULT '',
    ifError       INT          NOT NULL DEFAULT 0,
    errorInfo     TEXT         NOT NULL DEFAULT '',
    sysCreated    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted    TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_accesslog_userId (userId),
    INDEX idx_accesslog_sysCreated (sysCreated)
);
