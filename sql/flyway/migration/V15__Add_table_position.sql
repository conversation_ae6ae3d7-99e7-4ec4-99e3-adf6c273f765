-- 职务表
CREATE TABLE IF NOT EXISTS tbl_position
(
    id         VARCHAR(36) PRIMARY KEY,
    name       VARCHAR(100) NOT NULL DEFAULT '' COMMENT '职务名称',
    sortOrder  INT          NOT NULL DEFAULT 0 COMMENT '排序序号',
    sysCreated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_position_name (name),
    INDEX idx_position_sortOrder (sortOrder),
    UNIQUE KEY uk_position_name (name)
);

INSERT IGNORE INTO tbl_position (id, name, sortOrder) VALUE
('7c6c5d2dbcd8407f81f8c9ffa498c534', '局长', 10),
('286acaf4ca9e44ed8bcc452f7af41341', '副局长', 20),
('7b5256bea7944acc99a0a6b441b86476', '处长', 30),
('5d0dedf134d3417e9b9c43572e322e65', '副处长', 40),
('635822e0568b43fda352fa52ad350252', '主任', 50),
('8700de605c6b47c7846111cf3bb6c417', '副主任', 60),
('732da5b0b7a340b0b7ae007ba0cebe4d', '科员', 70);
