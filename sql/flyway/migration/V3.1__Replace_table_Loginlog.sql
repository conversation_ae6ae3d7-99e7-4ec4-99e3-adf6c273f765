drop table tbl_loginlog;

CREATE TABLE IF NOT EXISTS tbl_loginlog
(
    id          VARCHAR(36) PRIMARY KEY,
    userId      VARCHAR(36)  NOT NULL DEFAULT '',
    userName    VARCHAR(100) NOT NULL DEFAULT '',
    orgName     VARCHAR(100) NOT NULL DEFAULT '',
    ipAddress   VARCHAR(50)  NOT NULL DEFAULT '',
    browserName VARCHAR(100) NOT NULL DEFAULT '',
    sysCreated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted  TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_loginlog_userId (userId),
    INDEX idx_loginlog_sysCreated (sysCreated)
);
