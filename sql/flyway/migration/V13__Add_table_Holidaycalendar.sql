-- 节假日历表
CREATE TABLE IF NOT EXISTS tbl_holidaycalendar
(
    id           VARCHAR(36) PRIMARY KEY,
    date         DATE        NOT NULL COMMENT '日期',
    officialType INT         NOT NULL DEFAULT 0 COMMENT '官方类型(0-未知,1-节假日,2-补班日)',
    manualType   INT         NOT NULL DEFAULT 0 COMMENT '手动类型(0-未知,1-节假日,2-补班日,3-工作日,4-非工作日)',
    sysCreated   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted   TINYINT     NOT NULL DEFAULT 0,
    INDEX idx_holidaycalendar_date (date),
    INDEX idx_holidaycalendar_officialType (officialType),
    INDEX idx_holidaycalendar_manualType (manualType),
    UNIQUE KEY uk_holidaycalendar_date (date)
);
