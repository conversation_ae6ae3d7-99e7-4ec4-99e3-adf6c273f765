CREATE INDEX idx_meetingnotification_personnelId ON tbl_meetingnotification (personnelId);
CREATE INDEX idx_meetingnotification_method ON tbl_meetingnotification (method);
ALTER TABLE tbl_meetingnotification RENAME INDEX idx_notification_status TO idx_meetingnotification_status;
ALTER TABLE tbl_meetingnotification RENAME INDEX idx_notification_receiptId TO idx_meetingnotification_receiptId;
ALTER TABLE tbl_meetingnotification RENAME INDEX idx_notification_time TO idx_meetingnotification_time;
ALTER TABLE tbl_meetingnotification RENAME INDEX idx_notification_type TO idx_meetingnotification_type;
