-- 权限表
CREATE TABLE IF NOT EXISTS tbl_permission
(
    id         VARCHAR(36) PRIMARY KEY,
    name       VARCHAR(100) NOT NULL DEFAULT '' COMMENT '权限名称',
    sortOrder  INT          NOT NULL DEFAULT 0 COMMENT '排序值',
    sysCreated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_permission_name (name),
    INDEX idx_permission_sortOrder (sortOrder),
    UNIQUE KEY uk_permission_name (name)
    );

-- 人员权限关联表
CREATE TABLE IF NOT EXISTS tbl_personnelpermission
(
    id           VARCHAR(36) PRIMARY KEY,
    personnelId  VARCHAR(36) NOT NULL DEFAULT '' COMMENT '人员ID',
    permissionId VARCHAR(36) NOT NULL DEFAULT '' COMMENT '权限ID',
    sysCreated   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted   TINYINT     NOT NULL DEFAULT 0,
    INDEX idx_personnelpermission_personnelId (personnelId),
    INDEX idx_personnelpermission_permissionId (permissionId),
    UNIQUE KEY uk_personnel_permission (personnelId, permissionId)
    );

-- 工作组权限关联表
CREATE TABLE IF NOT EXISTS tbl_workgrouppermission
(
    id           VARCHAR(36) PRIMARY KEY,
    workgroupId  VARCHAR(36) NOT NULL DEFAULT '' COMMENT '工作组ID',
    permissionId VARCHAR(36) NOT NULL DEFAULT '' COMMENT '权限ID',
    sysCreated   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted   TINYINT     NOT NULL DEFAULT 0,
    INDEX idx_workgrouppermission_workgroupId (workgroupId),
    INDEX idx_workgrouppermission_permissionId (permissionId),
    UNIQUE KEY uk_workgroup_permission (workgroupId, permissionId)
    );

-- 权限表基础数据
INSERT IGNORE INTO tbl_permission (id, name, sortOrder) VALUES
('staff_permission', '职工权限', 10),
('haircut_management', '理发管理', 20),
('menu_management', '菜谱管理', 30),
('delivery_management', '外卖管理', 40),
('repair_management', '报修管理', 50),
('bus_dispatch', '班车发车', 60),
('personnel_record', '人事记录', 70),
('config_management', '配置管理', 80),
('inspection_management', '巡检管理', 90),
('property_inspection', '物业巡检', 100),
('day_shift_inspection', '白班巡检', 110),
('night_shift_inspection', '晚班巡检', 120),
('cleaning_service', '保洁清洁', 130);
