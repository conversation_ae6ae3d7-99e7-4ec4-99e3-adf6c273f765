DROP TABLE tbl_dictdetail;

CREATE TABLE IF NOT EXISTS tbl_dictdetail
(
    id         VARCHAR(36) PRIMARY KEY,
    parentId   VARCHAR(36)  NOT NULL DEFAULT '' COMMENT '父级ID',
    dictCode   VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '字典编码',
    dictName   VARCHAR(100) NOT NULL DEFAULT '' COMMENT '字典名称',
    itemCode   VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '选项编码',
    itemName   VARCHAR(100) NOT NULL DEFAULT '' COMMENT '选项名称',
    itemNameEn VARCHAR(100) NOT NULL DEFAULT '' COMMENT '英文名称',
    sortOrder  INT          NOT NULL DEFAULT 0 COMMENT '排序顺序',
    sysCreated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_dictdetail_dictCode (dictCode),
    INDEX idx_dictdetail_itemCode (itemCode),
    INDEX idx_dictdetail_parentId (parentId),
    INDEX idx_dictdetail_sortOrder (sortOrder)
);
