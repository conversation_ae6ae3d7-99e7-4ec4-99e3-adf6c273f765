DROP TABLE tbl_dictionary;

CREATE TABLE IF NOT EXISTS tbl_dictionary
(
    id          VARCHAR(36) PRIMARY KEY,
    dictCode    VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '字典编码',
    dictName    VARCHAR(100) NOT NULL DEFAULT '' COMMENT '字典名称',
    description TEXT         NOT NULL DEFAULT '' COMMENT '描述信息',
    sortOrder   INT          NOT NULL DEFAULT 0 COMMENT '排序顺序',
    sysCreated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted  TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_dictionary_dictCode (dictCode),
    INDEX idx_dictionary_sortOrder (sortOrder)
);
