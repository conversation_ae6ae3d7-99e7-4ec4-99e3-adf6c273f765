CREATE TABLE IF NOT EXISTS tbl_accesslog
(
    id            VARCHAR(36) PRIMARY KEY,
    userId        VARCHAR(36)  NOT NULL DEFAULT '',
    userName      VARCHAR(100) NOT NULL DEFAULT '',
    orgName       VARCHAR(100) NOT NULL DEFAULT '',
    className     VARCHAR(255) NOT NULL DEFAULT '',
    methodName    VARCHAR(100) NOT NULL DEFAULT '',
    executeTime   INT          NOT NULL DEFAULT 0,
    parameters    TEXT         NOT NULL DEFAULT '',
    result        TEXT         NOT NULL DEFAULT '',
    ifError       INT          NOT NULL DEFAULT 0,
    errorInfo     TEXT         NOT NULL DEFAULT '',
    sysCreated    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted    TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_accesslog_userId (userId),
    INDEX idx_accesslog_sysCreated (sysCreated)
);

-- Content表
CREATE TABLE IF NOT EXISTS tbl_content
(
    id         VARCHAR(36) PRIMARY KEY COMMENT '文件唯一标识',
    meetingId  VARCHAR(36)  NOT NULL DEFAULT '' COMMENT '关联的会议ID',
    fullName   VARCHAR(255) NOT NULL DEFAULT '' COMMENT '文件原始名称（不含扩展名）',
    storeName  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '文件在服务器上的存储名称（包含扩展名）',
    filePath   VARCHAR(255) NOT NULL DEFAULT '' COMMENT '文件存储的相对路径（按日期归档）',
    fileType   VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '文件类型（扩展名）',
    fileSize   INT COMMENT '文件大小（字节）',
    sysCreated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    sysUpdated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    sysDeleted TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标志(0-未删除,1-已删除)',
    INDEX idx_content_fileType (fileType),
    INDEX idx_content_meetingId (meetingId)
) COMMENT '文件内容表，用于存储上传的文件信息';

-- Loginlog表
CREATE TABLE IF NOT EXISTS tbl_loginlog
(
    id          VARCHAR(36) PRIMARY KEY,
    userId      VARCHAR(36)  NOT NULL DEFAULT '',
    userName    VARCHAR(100) NOT NULL DEFAULT '',
    orgName     VARCHAR(100) NOT NULL DEFAULT '',
    ipAddress   VARCHAR(50)  NOT NULL DEFAULT '',
    browserName VARCHAR(255) NOT NULL DEFAULT '',
    sysCreated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted  TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_loginlog_userId (userId),
    INDEX idx_loginlog_sysCreated (sysCreated)
);

-- Dictionary表
CREATE TABLE IF NOT EXISTS tbl_dictionary
(
    id          VARCHAR(36) PRIMARY KEY,
    dictCode    VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '字典编码',
    dictName    VARCHAR(100) NOT NULL DEFAULT '' COMMENT '字典名称',
    description TEXT         NOT NULL DEFAULT '' COMMENT '描述信息',
    sortOrder   INT          NOT NULL DEFAULT 0 COMMENT '排序顺序',
    sysCreated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted  TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_dictionary_dictCode (dictCode),
    INDEX idx_dictionary_sortOrder (sortOrder)
);

-- Dictdetail表
CREATE TABLE IF NOT EXISTS tbl_dictdetail
(
    id         VARCHAR(36) PRIMARY KEY,
    parentId   VARCHAR(36)  NOT NULL DEFAULT '' COMMENT '父级ID',
    dictCode   VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '字典编码',
    dictName   VARCHAR(100) NOT NULL DEFAULT '' COMMENT '字典名称',
    itemCode   VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '选项编码',
    itemName   VARCHAR(100) NOT NULL DEFAULT '' COMMENT '选项名称',
    itemNameEn VARCHAR(100) NOT NULL DEFAULT '' COMMENT '英文名称',
    sortOrder  INT          NOT NULL DEFAULT 0 COMMENT '排序顺序',
    sysCreated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_dictdetail_dictCode (dictCode),
    INDEX idx_dictdetail_itemCode (itemCode),
    INDEX idx_dictdetail_parentId (parentId),
    INDEX idx_dictdetail_sortOrder (sortOrder)
);

-- Config表
CREATE TABLE IF NOT EXISTS tbl_config
(
    id          VARCHAR(36) PRIMARY KEY,
    configCode  VARCHAR(50)  NOT NULL DEFAULT '',
    configName  VARCHAR(100) NOT NULL DEFAULT '',
    jsonValue   TEXT         NOT NULL DEFAULT '',
    description TEXT         NOT NULL DEFAULT '',
    sysCreated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted  TINYINT      NOT NULL DEFAULT 0,
    UNIQUE KEY uk_config_configCode (configCode)
);

-- 会议室表
CREATE TABLE IF NOT EXISTS tbl_meetingroom
(
    id                VARCHAR(36) PRIMARY KEY,
    name              VARCHAR(100) NOT NULL DEFAULT '' COMMENT '会议室名称',
    floor             INT          NULL COMMENT '所在楼层',
    capacity          INT          NULL COMMENT '容纳人数',
    hasProjector      INT          NOT NULL DEFAULT 0 COMMENT '是否有投影仪(0-无,1-有)',
    hasMicrophone     INT          NOT NULL DEFAULT 0 COMMENT '是否有麦克风(0-无,1-有)',
    hasWhiteboard     INT          NOT NULL DEFAULT 0 COMMENT '是否有白板(0-无,1-有)',
    hasNotebook       INT          NOT NULL DEFAULT 0 COMMENT '是否有笔记本(0-无,1-有)',
    hasWaterDispenser INT          NOT NULL DEFAULT 0 COMMENT '是否有饮水机(0-无,1-有)',
    description       TEXT         NOT NULL DEFAULT '' COMMENT '会议室描述',
    status            INT          NOT NULL DEFAULT 1 COMMENT '状态(0-禁用,1-启用)',
    sysCreated        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted        TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_meetingroom_name (name),
    INDEX idx_meetingroom_status (status)
);

-- 组织机构表
CREATE TABLE IF NOT EXISTS tbl_organization
(
    id          VARCHAR(36) PRIMARY KEY,
    name        VARCHAR(100) NOT NULL DEFAULT '' COMMENT '组织机构名称',
    description TEXT         NOT NULL DEFAULT '' COMMENT '组织机构描述',
    sortOrder   INT          NOT NULL DEFAULT 0 COMMENT '排序序号',
    status      INT          NOT NULL DEFAULT 1 COMMENT '状态(0-禁用,1-启用)',
    sysCreated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted  TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_organization_name (name),
    INDEX idx_organization_status (status),
    INDEX idx_organization_sortOrder (sortOrder)
);

-- 人员表
CREATE TABLE IF NOT EXISTS tbl_personnel
(
    id                 VARCHAR(36) PRIMARY KEY,
    name               VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '姓名',
    nameInitials       VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '姓名首字母',
    phone              VARCHAR(20)  NOT NULL DEFAULT '' COMMENT '手机号码',
    organizationId     VARCHAR(36)  NOT NULL DEFAULT '' COMMENT '组织机构ID',
    position           VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '职务',
    email              VARCHAR(100) NOT NULL DEFAULT '' COMMENT '邮箱',
    type               INT          NOT NULL DEFAULT 0 COMMENT '人员类型(0-内部成员,1-外部成员)',
    username           VARCHAR(50)  NULL     DEFAULT NULL COMMENT '用户名',
    password           VARCHAR(255) NOT NULL DEFAULT '' COMMENT '密码',
    ifWorkgroupDefault INT          NOT NULL DEFAULT 0 COMMENT '是否是工作组创建的默认账号(0-否,1-是)',
    sysCreated         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted         TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_personnel_name (name),
    INDEX idx_personnel_nameInitials (nameInitials),
    INDEX idx_personnel_phone (phone),
    INDEX idx_personnel_organizationId (organizationId),
    INDEX idx_personnel_type (type),
    UNIQUE KEY uk_personnel_username (username)
);

-- 车辆信息表
CREATE TABLE tbl_vehicleinfo
(
    id                 VARCHAR(36) PRIMARY KEY COMMENT '车辆唯一标识',
    personnelId        VARCHAR(36) NOT NULL DEFAULT '' COMMENT '关联车主id',
    licensePlateNumber VARCHAR(20) NOT NULL DEFAULT '' COMMENT '车牌号',
    sysCreated         TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    sysUpdated         TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    sysDeleted         TINYINT     NOT NULL DEFAULT 0 COMMENT '删除标志(0-未删除,1-已删除)',
    INDEX idx_licensePlateNumber (licensePlateNumber) COMMENT '车牌号普通索引'
);

-- 车辆通知表
CREATE TABLE tbl_vehiclenotification
(
    id                  VARCHAR(36) PRIMARY KEY COMMENT '车辆通知表唯一标识',
    personnelId         VARCHAR(36) NOT NULL DEFAULT '' COMMENT '关联车主id',
    vehicleInfoId       VARCHAR(36) NOT NULL DEFAULT '' COMMENT '关联车辆id',
    noticeMethod        VARCHAR(50) NOT NULL DEFAULT '' COMMENT '通知方式',
    noticeTime          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '通知时间（默认当前时间）',
    feedbackContent     VARCHAR(50) NOT NULL DEFAULT '无反馈' COMMENT '反馈内容',
    adminDisplay        Int         NOT NULL DEFAULT 0 COMMENT '管理员显示数据0表示需要展示的数据，1表示不需要显示的数据',
    vehicleOwnerDisplay Int         NOT NULL DEFAULT 0 COMMENT '车主显示数据0表示车主未处理的通知反馈，1表示已经处理的通知反馈',
    sysCreated          TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated          TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    sysDeleted          TINYINT     NOT NULL DEFAULT 0 COMMENT '删除标志(0-未删除,1-已删除)'
);

-- 会议表
CREATE TABLE IF NOT EXISTS tbl_meeting
(
    id                      VARCHAR(36) PRIMARY KEY,
    title                   VARCHAR(100) NOT NULL DEFAULT '' COMMENT '会议主题',
    content                 TEXT         NOT NULL DEFAULT '' COMMENT '会议内容',
    meetingroomId           VARCHAR(36)  NOT NULL DEFAULT '' COMMENT '会议室ID',
    startTime               DATETIME     NOT NULL COMMENT '开始时间',
    endTime                 DATETIME     NOT NULL COMMENT '结束时间',
    creatorId               VARCHAR(36)  NOT NULL DEFAULT '' COMMENT '创建人ID',
    creatorName             VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '创建人姓名',
    status                  INT          NOT NULL DEFAULT 0 COMMENT '会议状态(0-待开始,1-进行中,2-已取消,3-已结束)',
    sendInstantNotification INT          NOT NULL DEFAULT 0 COMMENT '是否发送即时通知(创建/修改会议时)(0-否,1-是)',
    notifyMinutesBefore     INT          NULL     DEFAULT NULL COMMENT '会议开始前多少分钟发送通知，null表示不发送通知',
    notifyBySms             INT          NOT NULL DEFAULT 0 COMMENT '是否短信通知(0-否,1-是)',
    notifyByVoiceCall       INT          NOT NULL DEFAULT 0 COMMENT '是否机器人语音电话通知(0-否,1-是)',
    roomSelectionType       INT          NOT NULL DEFAULT 0 COMMENT '会议室选择方式(0-手动选择,1-智能推荐)',
    sysCreated              TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated              TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted              TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_meeting_meetingroomId (meetingroomId),
    INDEX idx_meeting_startTime (startTime),
    INDEX idx_meeting_endTime (endTime),
    INDEX idx_meeting_creatorId (creatorId),
    INDEX idx_meeting_status (status),
    INDEX idx_meeting_notifyMinutesBefore (notifyMinutesBefore),
    INDEX idx_meeting_roomSelectionType (roomSelectionType)
);

-- 参会人员关系表
CREATE TABLE IF NOT EXISTS tbl_meetingpersonnel
(
    id          VARCHAR(36) PRIMARY KEY,
    meetingId   VARCHAR(36) NOT NULL DEFAULT '' COMMENT '会议ID',
    personnelId VARCHAR(36) NOT NULL DEFAULT '' COMMENT '人员ID',
    role        INT         NOT NULL DEFAULT 0 COMMENT '参会角色(0-普通参会人,1-发起人)',
    feedback    INT         NOT NULL DEFAULT 0 COMMENT '参会反馈(0-未反馈,1-参加,2-建议延期,3-不参加)',
    reason      TEXT        NOT NULL DEFAULT '' COMMENT '反馈原因',
    sysCreated  TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated  TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted  TINYINT     NOT NULL DEFAULT 0,
    INDEX idx_meetingpersonnel_meetingId (meetingId),
    INDEX idx_meetingpersonnel_personnelId (personnelId),
    INDEX idx_meetingpersonnel_role (role),
    INDEX idx_meetingpersonnel_feedback (feedback)
);

-- 会议服务表
CREATE TABLE IF NOT EXISTS tbl_meetingfacility
(
    id         VARCHAR(36) PRIMARY KEY,
    meetingId  VARCHAR(36) NOT NULL DEFAULT '' COMMENT '会议ID',
    type       INT         NULL COMMENT '服务类型(0-用餐,1-桌牌,2-纸笔,3-茶水,4-果盘)',
    quantity   INT         NOT NULL DEFAULT 0 COMMENT '数量',
    content    TEXT        NOT NULL DEFAULT '' COMMENT '服务内容(如桌牌内容、用餐时间等)',
    sysCreated TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted TINYINT     NOT NULL DEFAULT 0,
    INDEX idx_meetingfacility_meetingId (meetingId),
    INDEX idx_meetingfacility_type (type)
);

-- 会议通知表
CREATE TABLE IF NOT EXISTS tbl_meetingnotification
(
    id            VARCHAR(36) PRIMARY KEY,
    meetingId     VARCHAR(36)  NOT NULL DEFAULT '' COMMENT '会议ID',
    personnelId   VARCHAR(36)  NOT NULL DEFAULT '' COMMENT '人员ID',
    method        INT          NOT NULL DEFAULT 0 COMMENT '通知方式(0-短信通知,1-机器人语音电话通知)',
    status        INT          NOT NULL DEFAULT 0 COMMENT '通知状态(0-未发送,1-已发送,2-已取消)',
    time          DATETIME     NULL COMMENT '通知时间',
    result        VARCHAR(255) NOT NULL DEFAULT '' COMMENT '通知结果记录',
    receiptId     VARCHAR(100) NOT NULL DEFAULT '' COMMENT '云平台回执ID',
    type          INT          NOT NULL DEFAULT 1 COMMENT '通知类型(0-会议创建通知,1-会议开始通知,2-会议取消通知,3-会议调整通知)',
    sysCreated    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted    TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_meetingnotification_meetingId (meetingId),
    INDEX idx_meetingnotification_personnelId (personnelId),
    INDEX idx_meetingnotification_method (method),
    INDEX idx_meetingnotification_status (status),
    INDEX idx_meetingnotification_receiptId (receiptId),
    INDEX idx_meetingnotification_time (time),
    INDEX idx_meetingnotification_type (type)
);

-- 工作组表
CREATE TABLE IF NOT EXISTS tbl_workgroup
(
    id          VARCHAR(36) PRIMARY KEY,
    name        VARCHAR(100) NOT NULL DEFAULT '' COMMENT '工作组名称',
    sortOrder   INT          NOT NULL DEFAULT 0 COMMENT '排序号',
    sysCreated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted  TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_workgroup_name (name),
    INDEX idx_workgroup_sortOrder (sortOrder)
);

-- 工作组人员关系表
CREATE TABLE IF NOT EXISTS tbl_workgrouppersonnel
(
    id          VARCHAR(36) PRIMARY KEY,
    workgroupId VARCHAR(36) NOT NULL DEFAULT '' COMMENT '工作组ID',
    personnelId VARCHAR(36) NOT NULL DEFAULT '' COMMENT '人员ID',
    sysCreated  TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated  TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted  TINYINT     NOT NULL DEFAULT 0,
    INDEX idx_workgrouppersonnel_workgroupId (workgroupId),
    INDEX idx_workgrouppersonnel_personnelId (personnelId),
    UNIQUE KEY uk_workgroup_personnel (workgroupId, personnelId)
);

-- 权限表
CREATE TABLE IF NOT EXISTS tbl_permission
(
    id         VARCHAR(36) PRIMARY KEY,
    name       VARCHAR(100) NOT NULL DEFAULT '' COMMENT '权限名称',
    sortOrder  INT          NOT NULL DEFAULT 0 COMMENT '排序值',
    sysCreated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_permission_name (name),
    INDEX idx_permission_sortOrder (sortOrder),
    UNIQUE KEY uk_permission_name (name)
);

-- 人员权限关联表
CREATE TABLE IF NOT EXISTS tbl_personnelpermission
(
    id           VARCHAR(36) PRIMARY KEY,
    personnelId  VARCHAR(36) NOT NULL DEFAULT '' COMMENT '人员ID',
    permissionId VARCHAR(36) NOT NULL DEFAULT '' COMMENT '权限ID',
    sysCreated   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted   TINYINT     NOT NULL DEFAULT 0,
    INDEX idx_personnelpermission_personnelId (personnelId),
    INDEX idx_personnelpermission_permissionId (permissionId),
    UNIQUE KEY uk_personnel_permission (personnelId, permissionId)
);

-- 工作组权限关联表
CREATE TABLE IF NOT EXISTS tbl_workgrouppermission
(
    id           VARCHAR(36) PRIMARY KEY,
    workgroupId  VARCHAR(36) NOT NULL DEFAULT '' COMMENT '工作组ID',
    permissionId VARCHAR(36) NOT NULL DEFAULT '' COMMENT '权限ID',
    sysCreated   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted   TINYINT     NOT NULL DEFAULT 0,
    INDEX idx_workgrouppermission_workgroupId (workgroupId),
    INDEX idx_workgrouppermission_permissionId (permissionId),
    UNIQUE KEY uk_workgroup_permission (workgroupId, permissionId)
);

-- 节假日历表
CREATE TABLE IF NOT EXISTS tbl_holidaycalendar
(
    id           VARCHAR(36) PRIMARY KEY,
    date         DATE        NOT NULL COMMENT '日期',
    officialType INT         NOT NULL DEFAULT 0 COMMENT '官方类型(0-未知,1-节假日,2-补班日)',
    officialName VARCHAR(100) NOT NULL DEFAULT '' COMMENT '官方名称',
    manualType   INT         NOT NULL DEFAULT 0 COMMENT '手动类型(0-未知,1-节假日,2-补班日,3-工作日,4-非工作日)',
    sysCreated   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted   TINYINT     NOT NULL DEFAULT 0,
    INDEX idx_holidaycalendar_date (date),
    INDEX idx_holidaycalendar_officialType (officialType),
    INDEX idx_holidaycalendar_manualType (manualType),
    UNIQUE KEY uk_holidaycalendar_date (date)
);

-- 职务表
CREATE TABLE IF NOT EXISTS tbl_position
(
    id         VARCHAR(36) PRIMARY KEY,
    name       VARCHAR(100) NOT NULL DEFAULT '' COMMENT '职务名称',
    sortOrder  INT          NOT NULL DEFAULT 0 COMMENT '排序序号',
    sysCreated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sysUpdated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sysDeleted TINYINT      NOT NULL DEFAULT 0,
    INDEX idx_position_name (name),
    INDEX idx_position_sortOrder (sortOrder),
    UNIQUE KEY uk_position_name (name)
);

-- 权限表基础数据
INSERT IGNORE INTO tbl_permission (id, name, sortOrder) VALUES
('89b485c604634091ba08a02af67617fa', '职工权限', 10),
('44c2b1c532d643be9a4bc765be344c28', '理发管理', 20),
('7de4bc55e3134a798649b5c63170b6d8', '菜谱管理', 30),
('cf2c35f166164b4a8a665404fc318c3c', '外卖管理', 40),
('3e6f95cc439f412a84572fc091fd6d1f', '报修管理', 50),
('10f9a0edb8944567b7d9cb8f4ad32e6b', '班车发车', 60),
('ed1614e952f4459d872bf19b13463f82', '人事记录', 70),
('744627f71cb140dfbcf38f5421311421', '配置管理', 80),
('a6490ff288f141eb9adc7fb538c4e9e6', '巡检管理', 90),
('ced7227b2ca84f4e8b2c20e0fb3d85d7', '物业巡检', 100),
('e308731a108d42dfb45c4d53f855d2b2', '白班巡检', 110),
('3c23460617214e88bd64a988a71360fa', '晚班巡检', 120),
('db53896645d7445c9f83a8a92b72db20', '保洁清洁', 130);

INSERT IGNORE INTO tbl_position (id, name, sortOrder) VALUE
('7c6c5d2dbcd8407f81f8c9ffa498c534', '局长', 10),
('286acaf4ca9e44ed8bcc452f7af41341', '副局长', 20),
('7b5256bea7944acc99a0a6b441b86476', '处长', 30),
('5d0dedf134d3417e9b9c43572e322e65', '副处长', 40),
('635822e0568b43fda352fa52ad350252', '主任', 50),
('8700de605c6b47c7846111cf3bb6c417', '副主任', 60),
('732da5b0b7a340b0b7ae007ba0cebe4d', '科员', 70);
