-- V16: 工作组表添加分支组织ID字段
-- 日期: 2025-07-23
-- 描述: 在tbl_workgroup表中添加branchOrganizationId字段，用于标识工作组所属的分支组织

-- 添加分支组织ID字段
ALTER TABLE tbl_workgroup ADD COLUMN branchOrganizationId VARCHAR(36) NOT NULL DEFAULT '' COMMENT '分支组织ID' AFTER id;

-- 添加分支组织ID字段索引
ALTER TABLE tbl_workgroup ADD INDEX idx_workgroup_branchOrganizationId (branchOrganizationId);

UPDATE tbl_workgroup SET branchOrganizationId = (
    SELECT id FROM tbl_organization WHERE name = '总局'
);

UPDATE tbl_personnel AS p
INNER JOIN tbl_workgroup AS wg ON p.ownerWorkgroupId = wg.id
SET p.branchOrganizationId = wg.branchOrganizationId;
