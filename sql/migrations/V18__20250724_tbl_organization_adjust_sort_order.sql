-- 调整组织排序序号
-- 根据mainBranchDepartmentOrSubBranch值设置优先级排序：
-- 1. mainBranchDepartmentOrSubBranch == 1 (总中心下的部门) - 优先级最高
-- 2. mainBranchDepartmentOrSubBranch == 2 (分中心) - 优先级中等
-- 3. mainBranchDepartmentOrSubBranch == 0 (其他) - 优先级最低
-- 同类型内按原先的sortOrder排序

UPDATE tbl_organization 
SET sortOrder = (
    SELECT new_sort_order FROM (
        SELECT 
            id,
            ROW_NUMBER() OVER (
                ORDER BY 
                    CASE mainBranchDepartmentOrSubBranch
                        WHEN 1 THEN 1  -- 总中心下的部门优先
                        WHEN 2 THEN 2  -- 分中心次之
                        ELSE 3         -- 其他最后
                    END,
                    sortOrder
            ) * 10 AS new_sort_order
        FROM tbl_organization 
        WHERE sysDeleted = 0
    ) AS sorted_orgs 
    WHERE sorted_orgs.id = tbl_organization.id
)
WHERE sysDeleted = 0;