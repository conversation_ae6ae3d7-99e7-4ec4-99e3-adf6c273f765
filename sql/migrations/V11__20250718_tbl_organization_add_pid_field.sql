-- 组织机构表添加父组织ID字段
-- V11 - 2025-07-18
-- 说明：为tbl_organization表添加pid字段以支持层级组织结构

-- 添加父组织ID字段
ALTER TABLE tbl_organization
ADD COLUMN pid VARCHAR(36) NOT NULL DEFAULT '' COMMENT '父组织ID' AFTER id;

-- 添加父组织ID索引
CREATE INDEX idx_organization_pid ON tbl_organization (pid);

INSERT INTO tbl_organization (id, pid, name, sortOrder) VALUES
('646cc4b3bc1042eaadf4cc54a0fe4caa', '', '天津海事局', '1'),
('4626f9ffc7e1475b8f64421ec6910ae2', '646cc4b3bc1042eaadf4cc54a0fe4caa', '总局', '2');

UPDATE tbl_organization
SET pid = '4626f9ffc7e1475b8f64421ec6910ae2'
WHERE name IN ('中心领导', '业务科', '财务科', '综合办公室', '测试部');

UPDATE tbl_organization
SET pid = '646cc4b3bc1042eaadf4cc54a0fe4caa'
WHERE name IN ('北疆分中心', '大沽口分中心', '南疆分中心', '东疆分中心', '新港分中心', '海河分中心', '交管分中心');
